.class public Lgroovy/inspect/Inspector;
.super Ljava/lang/Object;
.source "Inspector.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lgroovy/inspect/Inspector$MemberComparator;,
        Lgroovy/inspect/Inspector$MemberComparatorWithValue;
    }
.end annotation


# static fields
.field public static final CLASS_CLASS_IDX:I = 0x1

.field public static final CLASS_INTERFACE_IDX:I = 0x2

.field public static final CLASS_OTHER_IDX:I = 0x4

.field public static final CLASS_PACKAGE_IDX:I = 0x0

.field public static final CLASS_SUPERCLASS_IDX:I = 0x3

.field public static final GROOVY:Ljava/lang/String; = "GROOVY"

.field public static final JAVA:Ljava/lang/String; = "JAVA"

.field public static final MEMBER_DECLARER_IDX:I = 0x2

.field public static final MEMBER_EXCEPTIONS_IDX:I = 0x6

.field public static final MEMBER_MODIFIER_IDX:I = 0x1

.field public static final MEMBER_NAME_IDX:I = 0x4

.field public static final MEMBER_ORIGIN_IDX:I = 0x0

.field public static final MEMBER_PARAMS_IDX:I = 0x5

.field public static final MEMBER_TYPE_IDX:I = 0x3

.field public static final MEMBER_VALUE_IDX:I = 0x5

.field public static final NOT_APPLICABLE:Ljava/lang/String; = "n/a"


# instance fields
.field protected objectUnderInspection:Ljava/lang/Object;


# direct methods
.method public constructor <init>(Ljava/lang/Object;)V
    .locals 1

    .line 71
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    if-eqz p1, :cond_0

    .line 75
    iput-object p1, p0, Lgroovy/inspect/Inspector;->objectUnderInspection:Ljava/lang/Object;

    return-void

    .line 73
    :cond_0
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string v0, "argument must not be null"

    invoke-direct {p1, v0}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method private static makeExceptionInfo([Ljava/lang/Class;)Ljava/lang/String;
    .locals 0

    .line 299
    invoke-static {p0}, Lgroovy/inspect/Inspector;->makeTypesInfo([Ljava/lang/Class;)Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method private static makeParamsInfo([Ljava/lang/Class;)Ljava/lang/String;
    .locals 0

    .line 295
    invoke-static {p0}, Lgroovy/inspect/Inspector;->makeTypesInfo([Ljava/lang/Class;)Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method private static makeTypesInfo([Ljava/lang/Class;)Ljava/lang/String;
    .locals 3

    .line 285
    new-instance v0, Ljava/lang/StringBuilder;

    const/16 v1, 0x20

    invoke-direct {v0, v1}, Ljava/lang/StringBuilder;-><init>(I)V

    const/4 v1, 0x0

    .line 286
    :goto_0
    array-length v2, p0

    if-ge v1, v2, :cond_1

    .line 287
    aget-object v2, p0, v1

    invoke-static {v2}, Lgroovy/inspect/Inspector;->shortName(Ljava/lang/Class;)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 288
    array-length v2, p0

    add-int/lit8 v2, v2, -0x1

    if-ge v1, v2, :cond_0

    const-string v2, ", "

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    :cond_0
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 291
    :cond_1
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method static print(Ljava/io/PrintStream;[Ljava/lang/Object;)V
    .locals 7

    const/4 v0, 0x0

    move v1, v0

    .line 354
    :goto_0
    array-length v2, p1

    if-ge v1, v2, :cond_1

    .line 355
    aget-object v2, p1, v1

    check-cast v2, [Ljava/lang/String;

    .line 356
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v3

    const-string v4, ":\t"

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {p0, v3}, Ljava/io/PrintStream;->print(Ljava/lang/String;)V

    .line 357
    array-length v3, v2

    move v4, v0

    :goto_1
    if-ge v4, v3, :cond_0

    aget-object v5, v2, v4

    .line 358
    new-instance v6, Ljava/lang/StringBuilder;

    invoke-direct {v6}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v6, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    const-string v6, " "

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {p0, v5}, Ljava/io/PrintStream;->print(Ljava/lang/String;)V

    add-int/lit8 v4, v4, 0x1

    goto :goto_1

    .line 360
    :cond_0
    invoke-virtual {p0}, Ljava/io/PrintStream;->println()V

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_1
    return-void
.end method

.method public static print([Ljava/lang/Object;)V
    .locals 1

    .line 350
    sget-object v0, Ljava/lang/System;->out:Ljava/io/PrintStream;

    invoke-static {v0, p0}, Lgroovy/inspect/Inspector;->print(Ljava/io/PrintStream;[Ljava/lang/Object;)V

    return-void
.end method

.method public static shortName(Ljava/lang/Class;)Ljava/lang/String;
    .locals 2

    if-nez p0, :cond_0

    const-string p0, "n/a"

    return-object p0

    .line 275
    :cond_0
    invoke-virtual {p0}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v0

    .line 276
    invoke-virtual {p0}, Ljava/lang/Class;->getPackage()Ljava/lang/Package;

    move-result-object v1

    if-nez v1, :cond_1

    return-object v0

    .line 277
    :cond_1
    invoke-virtual {p0}, Ljava/lang/Class;->getPackage()Ljava/lang/Package;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/Package;->getName()Ljava/lang/String;

    move-result-object p0

    .line 278
    invoke-virtual {p0}, Ljava/lang/String;->length()I

    move-result p0

    if-lez p0, :cond_2

    add-int/lit8 p0, p0, 0x1

    .line 280
    :cond_2
    invoke-virtual {v0, p0}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method public static sort(Ljava/util/List;)Ljava/util/Collection;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Ljava/lang/Object;",
            ">;)",
            "Ljava/util/Collection;"
        }
    .end annotation

    .line 365
    new-instance v0, Lgroovy/inspect/Inspector$MemberComparator;

    invoke-direct {v0}, Lgroovy/inspect/Inspector$MemberComparator;-><init>()V

    invoke-static {p0, v0}, Lgroovy/inspect/Inspector;->sort(Ljava/util/List;Ljava/util/Comparator;)Ljava/util/Collection;

    move-result-object p0

    return-object p0
.end method

.method public static sort(Ljava/util/List;Ljava/util/Comparator;)Ljava/util/Collection;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Ljava/lang/Object;",
            ">;",
            "Ljava/util/Comparator<",
            "Ljava/lang/Object;",
            ">;)",
            "Ljava/util/Collection;"
        }
    .end annotation

    .line 369
    invoke-interface {p0, p1}, Ljava/util/List;->sort(Ljava/util/Comparator;)V

    return-object p0
.end method


# virtual methods
.method protected fieldInfo(Lgroovy/lang/PropertyValue;)[Ljava/lang/String;
    .locals 4

    const/4 v0, 0x6

    new-array v0, v0, [Ljava/lang/String;

    const/4 v1, 0x0

    const-string v2, "GROOVY"

    aput-object v2, v0, v1

    const/4 v1, 0x1

    const-string v2, "public"

    aput-object v2, v0, v1

    const/4 v1, 0x2

    const-string v2, "n/a"

    aput-object v2, v0, v1

    .line 231
    invoke-virtual {p1}, Lgroovy/lang/PropertyValue;->getType()Ljava/lang/Class;

    move-result-object v1

    invoke-static {v1}, Lgroovy/inspect/Inspector;->shortName(Ljava/lang/Class;)Ljava/lang/String;

    move-result-object v1

    const/4 v3, 0x3

    aput-object v1, v0, v3

    .line 232
    invoke-virtual {p1}, Lgroovy/lang/PropertyValue;->getName()Ljava/lang/String;

    move-result-object v1

    const/4 v3, 0x4

    aput-object v1, v0, v3

    const/4 v1, 0x5

    .line 234
    :try_start_0
    invoke-virtual {p1}, Lgroovy/lang/PropertyValue;->getValue()Ljava/lang/Object;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/runtime/InvokerHelper;->inspect(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    aput-object p1, v0, v1
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    aput-object v2, v0, v1

    .line 238
    :goto_0
    invoke-virtual {p0, v0}, Lgroovy/inspect/Inspector;->withoutNulls([Ljava/lang/String;)[Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method protected fieldInfo(Ljava/lang/reflect/Field;)[Ljava/lang/String;
    .locals 3

    const/4 v0, 0x6

    new-array v0, v0, [Ljava/lang/String;

    const/4 v1, 0x0

    const-string v2, "JAVA"

    aput-object v2, v0, v1

    .line 196
    invoke-virtual {p1}, Ljava/lang/reflect/Field;->getModifiers()I

    move-result v1

    invoke-static {v1}, Ljava/lang/reflect/Modifier;->toString(I)Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x1

    aput-object v1, v0, v2

    .line 197
    invoke-virtual {p1}, Ljava/lang/reflect/Field;->getDeclaringClass()Ljava/lang/Class;

    move-result-object v1

    invoke-static {v1}, Lgroovy/inspect/Inspector;->shortName(Ljava/lang/Class;)Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x2

    aput-object v1, v0, v2

    .line 198
    invoke-virtual {p1}, Ljava/lang/reflect/Field;->getType()Ljava/lang/Class;

    move-result-object v1

    invoke-static {v1}, Lgroovy/inspect/Inspector;->shortName(Ljava/lang/Class;)Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x3

    aput-object v1, v0, v2

    .line 199
    invoke-virtual {p1}, Ljava/lang/reflect/Field;->getName()Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x4

    aput-object v1, v0, v2

    const/4 v1, 0x5

    .line 201
    :try_start_0
    iget-object v2, p0, Lgroovy/inspect/Inspector;->objectUnderInspection:Ljava/lang/Object;

    invoke-virtual {p1, v2}, Ljava/lang/reflect/Field;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/runtime/InvokerHelper;->inspect(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    aput-object p1, v0, v1
    :try_end_0
    .catch Ljava/lang/IllegalAccessException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    const-string p1, "n/a"

    aput-object p1, v0, v1

    .line 205
    :goto_0
    invoke-virtual {p0, v0}, Lgroovy/inspect/Inspector;->withoutNulls([Ljava/lang/String;)[Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method protected fieldWithInfo(Lgroovy/lang/PropertyValue;)Lgroovy/lang/Tuple2;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovy/lang/PropertyValue;",
            ")",
            "Lgroovy/lang/Tuple2<",
            "Ljava/lang/Object;",
            "[",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    const/4 v0, 0x6

    new-array v0, v0, [Ljava/lang/String;

    const/4 v1, 0x0

    const-string v2, "GROOVY"

    aput-object v2, v0, v1

    const/4 v1, 0x1

    const-string v2, "public"

    aput-object v2, v0, v1

    const/4 v1, 0x2

    const-string v2, "n/a"

    aput-object v2, v0, v1

    .line 246
    invoke-virtual {p1}, Lgroovy/lang/PropertyValue;->getType()Ljava/lang/Class;

    move-result-object v1

    invoke-static {v1}, Lgroovy/inspect/Inspector;->shortName(Ljava/lang/Class;)Ljava/lang/String;

    move-result-object v1

    const/4 v3, 0x3

    aput-object v1, v0, v3

    .line 247
    invoke-virtual {p1}, Lgroovy/lang/PropertyValue;->getName()Ljava/lang/String;

    move-result-object v1

    const/4 v3, 0x4

    aput-object v1, v0, v3

    const/4 v1, 0x5

    .line 250
    :try_start_0
    invoke-virtual {p1}, Lgroovy/lang/PropertyValue;->getValue()Ljava/lang/Object;

    move-result-object p1
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    .line 251
    :try_start_1
    invoke-static {p1}, Lorg/codehaus/groovy/runtime/InvokerHelper;->inspect(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v3

    aput-object v3, v0, v1
    :try_end_1
    .catch Ljava/lang/Exception; {:try_start_1 .. :try_end_1} :catch_1

    goto :goto_0

    :catch_0
    const/4 p1, 0x0

    :catch_1
    aput-object v2, v0, v1

    .line 255
    :goto_0
    invoke-virtual {p0, v0}, Lgroovy/inspect/Inspector;->withoutNulls([Ljava/lang/String;)[Ljava/lang/String;

    move-result-object v0

    .line 256
    new-instance v1, Lgroovy/lang/Tuple2;

    invoke-direct {v1, p1, v0}, Lgroovy/lang/Tuple2;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    return-object v1
.end method

.method protected fieldWithInfo(Ljava/lang/reflect/Field;)Lgroovy/lang/Tuple2;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/reflect/Field;",
            ")",
            "Lgroovy/lang/Tuple2<",
            "Ljava/lang/Object;",
            "[",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    const/4 v0, 0x6

    new-array v0, v0, [Ljava/lang/String;

    const/4 v1, 0x0

    const-string v2, "JAVA"

    aput-object v2, v0, v1

    .line 211
    invoke-virtual {p1}, Ljava/lang/reflect/Field;->getModifiers()I

    move-result v1

    invoke-static {v1}, Ljava/lang/reflect/Modifier;->toString(I)Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x1

    aput-object v1, v0, v2

    .line 212
    invoke-virtual {p1}, Ljava/lang/reflect/Field;->getDeclaringClass()Ljava/lang/Class;

    move-result-object v1

    invoke-static {v1}, Lgroovy/inspect/Inspector;->shortName(Ljava/lang/Class;)Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x2

    aput-object v1, v0, v2

    .line 213
    invoke-virtual {p1}, Ljava/lang/reflect/Field;->getType()Ljava/lang/Class;

    move-result-object v1

    invoke-static {v1}, Lgroovy/inspect/Inspector;->shortName(Ljava/lang/Class;)Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x3

    aput-object v1, v0, v2

    .line 214
    invoke-virtual {p1}, Ljava/lang/reflect/Field;->getName()Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x4

    aput-object v1, v0, v2

    .line 215
    invoke-virtual {p0, v0}, Lgroovy/inspect/Inspector;->withoutNulls([Ljava/lang/String;)[Ljava/lang/String;

    move-result-object v0

    const/4 v1, 0x5

    const/4 v2, 0x0

    .line 218
    :try_start_0
    iget-object v3, p0, Lgroovy/inspect/Inspector;->objectUnderInspection:Ljava/lang/Object;

    invoke-virtual {p1, v3}, Ljava/lang/reflect/Field;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    .line 219
    invoke-static {v2}, Lorg/codehaus/groovy/runtime/InvokerHelper;->inspect(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    aput-object p1, v0, v1
    :try_end_0
    .catch Ljava/lang/IllegalAccessException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    const-string p1, "n/a"

    .line 221
    aput-object p1, v0, v1

    .line 223
    :goto_0
    new-instance p1, Lgroovy/lang/Tuple2;

    invoke-direct {p1, v2, v0}, Lgroovy/lang/Tuple2;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    return-object p1
.end method

.method public getClassProps()[Ljava/lang/String;
    .locals 8

    const/4 v0, 0x5

    new-array v0, v0, [Ljava/lang/String;

    .line 85
    invoke-virtual {p0}, Lgroovy/inspect/Inspector;->getClassUnderInspection()Ljava/lang/Class;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/Class;->getPackage()Ljava/lang/Package;

    move-result-object v1

    .line 86
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "package "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    if-nez v1, :cond_0

    const-string v1, "n/a"

    goto :goto_0

    :cond_0
    invoke-virtual {v1}, Ljava/lang/Package;->getName()Ljava/lang/String;

    move-result-object v1

    :goto_0
    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x0

    aput-object v1, v0, v2

    .line 87
    invoke-virtual {p0}, Lgroovy/inspect/Inspector;->getClassUnderInspection()Ljava/lang/Class;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/Class;->getModifiers()I

    move-result v1

    invoke-static {v1}, Ljava/lang/reflect/Modifier;->toString(I)Ljava/lang/String;

    move-result-object v1

    .line 88
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v3, " class "

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {p0}, Lgroovy/inspect/Inspector;->getClassUnderInspection()Ljava/lang/Class;

    move-result-object v3

    invoke-static {v3}, Lgroovy/inspect/Inspector;->shortName(Ljava/lang/Class;)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    const/4 v3, 0x1

    aput-object v1, v0, v3

    const-string v1, "implements "

    const/4 v3, 0x2

    aput-object v1, v0, v3

    .line 90
    invoke-virtual {p0}, Lgroovy/inspect/Inspector;->getClassUnderInspection()Ljava/lang/Class;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/Class;->getInterfaces()[Ljava/lang/Class;

    move-result-object v1

    .line 91
    array-length v4, v1

    :goto_1
    if-ge v2, v4, :cond_1

    aget-object v5, v1, v2

    .line 92
    new-instance v6, Ljava/lang/StringBuilder;

    invoke-direct {v6}, Ljava/lang/StringBuilder;-><init>()V

    aget-object v7, v0, v3

    invoke-virtual {v6, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v6

    invoke-static {v5}, Lgroovy/inspect/Inspector;->shortName(Ljava/lang/Class;)Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v6, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    const-string v6, " "

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v5

    aput-object v5, v0, v3

    add-int/lit8 v2, v2, 0x1

    goto :goto_1

    :cond_1
    const/4 v1, 0x3

    .line 94
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "extends "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {p0}, Lgroovy/inspect/Inspector;->getClassUnderInspection()Ljava/lang/Class;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/Class;->getSuperclass()Ljava/lang/Class;

    move-result-object v3

    invoke-static {v3}, Lgroovy/inspect/Inspector;->shortName(Ljava/lang/Class;)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    aput-object v2, v0, v1

    const/4 v1, 0x4

    .line 95
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "is Primitive: "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {p0}, Lgroovy/inspect/Inspector;->getClassUnderInspection()Ljava/lang/Class;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/Class;->isPrimitive()Z

    move-result v3

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object v2

    const-string v3, ", is Array: "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    .line 96
    invoke-virtual {p0}, Lgroovy/inspect/Inspector;->getClassUnderInspection()Ljava/lang/Class;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/Class;->isArray()Z

    move-result v3

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object v2

    const-string v3, ", is Groovy: "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    .line 97
    invoke-virtual {p0}, Lgroovy/inspect/Inspector;->isGroovy()Z

    move-result v3

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    aput-object v2, v0, v1

    return-object v0
.end method

.method protected getClassUnderInspection()Ljava/lang/Class;
    .locals 1

    .line 270
    iget-object v0, p0, Lgroovy/inspect/Inspector;->objectUnderInspection:Ljava/lang/Object;

    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    return-object v0
.end method

.method public getMetaMethods()[Ljava/lang/Object;
    .locals 4

    .line 141
    iget-object v0, p0, Lgroovy/inspect/Inspector;->objectUnderInspection:Ljava/lang/Object;

    invoke-static {v0}, Lorg/codehaus/groovy/runtime/InvokerHelper;->getMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    .line 142
    invoke-interface {v0}, Lgroovy/lang/MetaClass;->getMetaMethods()Ljava/util/List;

    move-result-object v0

    .line 143
    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v1

    new-array v1, v1, [Ljava/lang/Object;

    .line 145
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    const/4 v2, 0x0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_0

    .line 146
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lgroovy/lang/MetaMethod;

    .line 147
    invoke-virtual {p0, v3}, Lgroovy/inspect/Inspector;->methodInfo(Lgroovy/lang/MetaMethod;)[Ljava/lang/String;

    move-result-object v3

    aput-object v3, v1, v2

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_0
    return-object v1
.end method

.method public getMethods()[Ljava/lang/Object;
    .locals 6

    .line 120
    invoke-virtual {p0}, Lgroovy/inspect/Inspector;->getClassUnderInspection()Ljava/lang/Class;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/Class;->getMethods()[Ljava/lang/reflect/Method;

    move-result-object v0

    .line 121
    invoke-virtual {p0}, Lgroovy/inspect/Inspector;->getClassUnderInspection()Ljava/lang/Class;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/Class;->getConstructors()[Ljava/lang/reflect/Constructor;

    move-result-object v1

    .line 122
    array-length v2, v0

    array-length v3, v1

    add-int/2addr v2, v3

    new-array v2, v2, [Ljava/lang/Object;

    const/4 v3, 0x0

    move v4, v3

    .line 124
    :goto_0
    array-length v5, v0

    if-ge v4, v5, :cond_0

    .line 125
    aget-object v5, v0, v4

    .line 126
    invoke-virtual {p0, v5}, Lgroovy/inspect/Inspector;->methodInfo(Ljava/lang/reflect/Method;)[Ljava/lang/String;

    move-result-object v5

    aput-object v5, v2, v4

    add-int/lit8 v4, v4, 0x1

    goto :goto_0

    .line 128
    :cond_0
    :goto_1
    array-length v0, v1

    if-ge v3, v0, :cond_1

    .line 129
    aget-object v0, v1, v3

    .line 130
    invoke-virtual {p0, v0}, Lgroovy/inspect/Inspector;->methodInfo(Ljava/lang/reflect/Constructor;)[Ljava/lang/String;

    move-result-object v0

    aput-object v0, v2, v4

    add-int/lit8 v3, v3, 0x1

    add-int/lit8 v4, v4, 0x1

    goto :goto_1

    :cond_1
    return-object v2
.end method

.method public getObject()Ljava/lang/Object;
    .locals 1

    .line 111
    iget-object v0, p0, Lgroovy/inspect/Inspector;->objectUnderInspection:Ljava/lang/Object;

    return-object v0
.end method

.method public getPropertiesWithInfo()[Ljava/lang/Object;
    .locals 4

    .line 260
    iget-object v0, p0, Lgroovy/inspect/Inspector;->objectUnderInspection:Ljava/lang/Object;

    invoke-static {v0}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->getMetaPropertyValues(Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    .line 261
    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v1

    new-array v1, v1, [Ljava/lang/Object;

    .line 263
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    const/4 v2, 0x0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_0

    .line 264
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lgroovy/lang/PropertyValue;

    invoke-virtual {p0, v3}, Lgroovy/inspect/Inspector;->fieldWithInfo(Lgroovy/lang/PropertyValue;)Lgroovy/lang/Tuple2;

    move-result-object v3

    aput-object v3, v1, v2

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_0
    return-object v1
.end method

.method public getPropertyInfo()[Ljava/lang/Object;
    .locals 4

    .line 183
    iget-object v0, p0, Lgroovy/inspect/Inspector;->objectUnderInspection:Ljava/lang/Object;

    invoke-static {v0}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->getMetaPropertyValues(Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    .line 184
    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v1

    new-array v1, v1, [Ljava/lang/Object;

    .line 186
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    const/4 v2, 0x0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_0

    .line 187
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lgroovy/lang/PropertyValue;

    .line 188
    invoke-virtual {p0, v3}, Lgroovy/inspect/Inspector;->fieldInfo(Lgroovy/lang/PropertyValue;)[Ljava/lang/String;

    move-result-object v3

    aput-object v3, v1, v2

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_0
    return-object v1
.end method

.method public getPublicFields()[Ljava/lang/Object;
    .locals 4

    .line 158
    invoke-virtual {p0}, Lgroovy/inspect/Inspector;->getClassUnderInspection()Ljava/lang/Class;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/Class;->getFields()[Ljava/lang/reflect/Field;

    move-result-object v0

    .line 159
    array-length v1, v0

    new-array v1, v1, [Ljava/lang/Object;

    const/4 v2, 0x0

    .line 160
    :goto_0
    array-length v3, v0

    if-ge v2, v3, :cond_0

    .line 161
    aget-object v3, v0, v2

    .line 162
    invoke-virtual {p0, v3}, Lgroovy/inspect/Inspector;->fieldInfo(Ljava/lang/reflect/Field;)[Ljava/lang/String;

    move-result-object v3

    aput-object v3, v1, v2

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_0
    return-object v1
.end method

.method public getPublicFieldsWithInfo()[Ljava/lang/Object;
    .locals 4

    .line 168
    invoke-virtual {p0}, Lgroovy/inspect/Inspector;->getClassUnderInspection()Ljava/lang/Class;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/Class;->getFields()[Ljava/lang/reflect/Field;

    move-result-object v0

    .line 169
    array-length v1, v0

    new-array v1, v1, [Ljava/lang/Object;

    const/4 v2, 0x0

    .line 170
    :goto_0
    array-length v3, v0

    if-ge v2, v3, :cond_0

    .line 171
    aget-object v3, v0, v2

    .line 172
    invoke-virtual {p0, v3}, Lgroovy/inspect/Inspector;->fieldWithInfo(Ljava/lang/reflect/Field;)Lgroovy/lang/Tuple2;

    move-result-object v3

    aput-object v3, v1, v2

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_0
    return-object v1
.end method

.method public isGroovy()Z
    .locals 2

    .line 102
    const-class v0, Lgroovy/lang/GroovyObject;

    invoke-virtual {p0}, Lgroovy/inspect/Inspector;->getClassUnderInspection()Ljava/lang/Class;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    move-result v0

    return v0
.end method

.method protected methodInfo(Lgroovy/lang/MetaMethod;)[Ljava/lang/String;
    .locals 4

    const/4 v0, 0x7

    new-array v0, v0, [Ljava/lang/String;

    .line 330
    invoke-virtual {p1}, Lgroovy/lang/MetaMethod;->getModifiers()I

    move-result v1

    const/4 v2, 0x0

    const-string v3, "GROOVY"

    aput-object v3, v0, v2

    .line 332
    invoke-static {v1}, Ljava/lang/reflect/Modifier;->toString(I)Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x1

    aput-object v1, v0, v2

    .line 333
    invoke-virtual {p1}, Lgroovy/lang/MetaMethod;->getDeclaringClass()Lorg/codehaus/groovy/reflection/CachedClass;

    move-result-object v1

    invoke-virtual {v1}, Lorg/codehaus/groovy/reflection/CachedClass;->getTheClass()Ljava/lang/Class;

    move-result-object v1

    invoke-static {v1}, Lgroovy/inspect/Inspector;->shortName(Ljava/lang/Class;)Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x2

    aput-object v1, v0, v2

    .line 334
    invoke-virtual {p1}, Lgroovy/lang/MetaMethod;->getReturnType()Ljava/lang/Class;

    move-result-object v1

    invoke-static {v1}, Lgroovy/inspect/Inspector;->shortName(Ljava/lang/Class;)Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x3

    aput-object v1, v0, v2

    .line 335
    invoke-virtual {p1}, Lgroovy/lang/MetaMethod;->getName()Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x4

    aput-object v1, v0, v2

    .line 336
    invoke-virtual {p1}, Lgroovy/lang/MetaMethod;->getNativeParameterTypes()[Ljava/lang/Class;

    move-result-object p1

    invoke-static {p1}, Lgroovy/inspect/Inspector;->makeParamsInfo([Ljava/lang/Class;)Ljava/lang/String;

    move-result-object p1

    const/4 v1, 0x5

    aput-object p1, v0, v1

    const/4 p1, 0x6

    const-string v1, "n/a"

    aput-object v1, v0, p1

    .line 338
    invoke-virtual {p0, v0}, Lgroovy/inspect/Inspector;->withoutNulls([Ljava/lang/String;)[Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method protected methodInfo(Ljava/lang/reflect/Constructor;)[Ljava/lang/String;
    .locals 3

    const/4 v0, 0x7

    new-array v0, v0, [Ljava/lang/String;

    const/4 v1, 0x0

    const-string v2, "JAVA"

    aput-object v2, v0, v1

    .line 318
    invoke-virtual {p1}, Ljava/lang/reflect/Constructor;->getModifiers()I

    move-result v1

    invoke-static {v1}, Ljava/lang/reflect/Modifier;->toString(I)Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x1

    aput-object v1, v0, v2

    .line 319
    invoke-virtual {p1}, Ljava/lang/reflect/Constructor;->getDeclaringClass()Ljava/lang/Class;

    move-result-object v1

    invoke-static {v1}, Lgroovy/inspect/Inspector;->shortName(Ljava/lang/Class;)Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x2

    aput-object v1, v0, v2

    .line 320
    invoke-virtual {p1}, Ljava/lang/reflect/Constructor;->getDeclaringClass()Ljava/lang/Class;

    move-result-object v1

    invoke-static {v1}, Lgroovy/inspect/Inspector;->shortName(Ljava/lang/Class;)Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x3

    aput-object v1, v0, v2

    .line 321
    invoke-virtual {p1}, Ljava/lang/reflect/Constructor;->getName()Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x4

    aput-object v1, v0, v2

    .line 322
    invoke-virtual {p1}, Ljava/lang/reflect/Constructor;->getParameterTypes()[Ljava/lang/Class;

    move-result-object v1

    invoke-static {v1}, Lgroovy/inspect/Inspector;->makeParamsInfo([Ljava/lang/Class;)Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x5

    aput-object v1, v0, v2

    .line 323
    invoke-virtual {p1}, Ljava/lang/reflect/Constructor;->getExceptionTypes()[Ljava/lang/Class;

    move-result-object p1

    invoke-static {p1}, Lgroovy/inspect/Inspector;->makeExceptionInfo([Ljava/lang/Class;)Ljava/lang/String;

    move-result-object p1

    const/4 v1, 0x6

    aput-object p1, v0, v1

    .line 325
    invoke-virtual {p0, v0}, Lgroovy/inspect/Inspector;->withoutNulls([Ljava/lang/String;)[Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method protected methodInfo(Ljava/lang/reflect/Method;)[Ljava/lang/String;
    .locals 3

    const/4 v0, 0x7

    new-array v0, v0, [Ljava/lang/String;

    const/4 v1, 0x0

    const-string v2, "JAVA"

    aput-object v2, v0, v1

    .line 305
    invoke-virtual {p1}, Ljava/lang/reflect/Method;->getDeclaringClass()Ljava/lang/Class;

    move-result-object v1

    invoke-static {v1}, Lgroovy/inspect/Inspector;->shortName(Ljava/lang/Class;)Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x2

    aput-object v1, v0, v2

    .line 306
    invoke-virtual {p1}, Ljava/lang/reflect/Method;->getModifiers()I

    move-result v1

    invoke-static {v1}, Ljava/lang/reflect/Modifier;->toString(I)Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x1

    aput-object v1, v0, v2

    .line 307
    invoke-virtual {p1}, Ljava/lang/reflect/Method;->getName()Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x4

    aput-object v1, v0, v2

    .line 308
    invoke-virtual {p1}, Ljava/lang/reflect/Method;->getReturnType()Ljava/lang/Class;

    move-result-object v1

    invoke-static {v1}, Lgroovy/inspect/Inspector;->shortName(Ljava/lang/Class;)Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x3

    aput-object v1, v0, v2

    .line 309
    invoke-virtual {p1}, Ljava/lang/reflect/Method;->getParameterTypes()[Ljava/lang/Class;

    move-result-object v1

    invoke-static {v1}, Lgroovy/inspect/Inspector;->makeParamsInfo([Ljava/lang/Class;)Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x5

    aput-object v1, v0, v2

    .line 310
    invoke-virtual {p1}, Ljava/lang/reflect/Method;->getExceptionTypes()[Ljava/lang/Class;

    move-result-object p1

    invoke-static {p1}, Lgroovy/inspect/Inspector;->makeExceptionInfo([Ljava/lang/Class;)Ljava/lang/String;

    move-result-object p1

    const/4 v1, 0x6

    aput-object p1, v0, v1

    .line 312
    invoke-virtual {p0, v0}, Lgroovy/inspect/Inspector;->withoutNulls([Ljava/lang/String;)[Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method protected withoutNulls([Ljava/lang/String;)[Ljava/lang/String;
    .locals 2

    const/4 v0, 0x0

    .line 342
    :goto_0
    array-length v1, p1

    if-ge v0, v1, :cond_1

    .line 343
    aget-object v1, p1, v0

    if-nez v1, :cond_0

    const-string v1, "n/a"

    .line 344
    aput-object v1, p1, v0

    :cond_0
    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_1
    return-object p1
.end method
