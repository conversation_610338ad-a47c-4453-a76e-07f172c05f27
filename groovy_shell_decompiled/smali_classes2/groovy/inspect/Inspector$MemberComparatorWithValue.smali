.class public Lgroovy/inspect/Inspector$MemberComparatorWithValue;
.super Ljava/lang/Object;
.source "Inspector.java"

# interfaces
.implements Ljava/util/Comparator;
.implements Ljava/io/Serializable;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovy/inspect/Inspector;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "MemberComparatorWithValue"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ljava/util/Comparator<",
        "Ljava/lang/Object;",
        ">;",
        "Ljava/io/Serializable;"
    }
.end annotation


# static fields
.field private static final delegate:Lgroovy/inspect/Inspector$MemberComparator;

.field private static final serialVersionUID:J = 0x4158f07beed325dL


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 396
    new-instance v0, Lgroovy/inspect/Inspector$MemberComparator;

    invoke-direct {v0}, Lgroovy/inspect/Inspector$MemberComparator;-><init>()V

    sput-object v0, Lgroovy/inspect/Inspector$MemberComparatorWithValue;->delegate:Lgroovy/inspect/Inspector$MemberComparator;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 394
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public compare(Ljava/lang/Object;Ljava/lang/Object;)I
    .locals 1

    .line 400
    check-cast p1, Lgroovy/lang/Tuple2;

    .line 401
    check-cast p2, Lgroovy/lang/Tuple2;

    .line 402
    sget-object v0, Lgroovy/inspect/Inspector$MemberComparatorWithValue;->delegate:Lgroovy/inspect/Inspector$MemberComparator;

    invoke-virtual {p1}, Lgroovy/lang/Tuple2;->getV2()Ljava/lang/Object;

    move-result-object p1

    invoke-virtual {p2}, Lgroovy/lang/Tuple2;->getV2()Ljava/lang/Object;

    move-result-object p2

    invoke-virtual {v0, p1, p2}, Lgroovy/inspect/Inspector$MemberComparator;->compare(Ljava/lang/Object;Ljava/lang/Object;)I

    move-result p1

    return p1
.end method
