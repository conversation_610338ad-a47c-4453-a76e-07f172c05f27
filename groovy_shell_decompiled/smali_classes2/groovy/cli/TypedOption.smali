.class public Lgroovy/cli/TypedOption;
.super Ljava/util/HashMap;
.source "TypedOption.java"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/util/HashMap<",
        "Ljava/lang/String;",
        "TT;>;"
    }
.end annotation


# static fields
.field private static final serialVersionUID:J = 0x7bf380c7da93953eL


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 23
    invoke-direct {p0}, Ljava/util/HashMap;-><init>()V

    return-void
.end method


# virtual methods
.method public defaultValue()Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()TT;"
        }
    .end annotation

    const-string v0, "defaultValue"

    .line 27
    invoke-super {p0, v0}, <PERSON>java/util/HashMap;->get(Lja<PERSON>/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    return-object v0
.end method
