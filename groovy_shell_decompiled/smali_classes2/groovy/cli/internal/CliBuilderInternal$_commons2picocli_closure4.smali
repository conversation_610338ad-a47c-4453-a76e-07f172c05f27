.class public final Lgroovy/cli/internal/CliBuilderInternal$_commons2picocli_closure4;
.super Lgroovy/lang/Closure;
.source "CliBuilderInternal.groovy"

# interfaces
.implements Lorg/codehaus/groovy/runtime/GeneratedClosure;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lgroovy/cli/internal/CliBuilderInternal;->commons2picocli(Ljava/lang/Object;Ljava/util/Map;)Ljava/util/Map;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x11
    name = "_commons2picocli_closure4"
.end annotation


# static fields
.field private static synthetic $callSiteArray:Ljava/lang/ref/SoftReference;

.field private static synthetic $staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

.field public static transient synthetic __$stMC:Z


# instance fields
.field private synthetic shortname:Lgroovy/lang/Reference;


# direct methods
.method private static synthetic $createCallSiteArray()Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;
    .locals 3

    const/4 v0, 0x3

    new-array v0, v0, [Ljava/lang/String;

    invoke-static {v0}, Lgroovy/cli/internal/CliBuilderInternal$_commons2picocli_closure4;->$createCallSiteArray_1([Ljava/lang/String;)V

    new-instance v1, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;

    const-class v2, Lgroovy/cli/internal/CliBuilderInternal$_commons2picocli_closure4;

    invoke-direct {v1, v2, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;-><init>(Ljava/lang/Class;[Ljava/lang/String;)V

    return-object v1
.end method

.method private static synthetic $createCallSiteArray_1([Ljava/lang/String;)V
    .locals 2

    const/4 v0, 0x0

    const-string v1, "acceptLongOptionsWithSingleHyphen"

    aput-object v1, p0, v0

    const/4 v0, 0x1

    aput-object v1, p0, v0

    const/4 v0, 0x2

    const-string v1, "doCall"

    aput-object v1, p0, v0

    return-void
.end method

.method private static synthetic $getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;
    .locals 2

    sget-object v0, Lgroovy/cli/internal/CliBuilderInternal$_commons2picocli_closure4;->$callSiteArray:Ljava/lang/ref/SoftReference;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Ljava/lang/ref/SoftReference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;

    if-nez v0, :cond_1

    :cond_0
    invoke-static {}, Lgroovy/cli/internal/CliBuilderInternal$_commons2picocli_closure4;->$createCallSiteArray()Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;

    move-result-object v0

    new-instance v1, Ljava/lang/ref/SoftReference;

    invoke-direct {v1, v0}, Ljava/lang/ref/SoftReference;-><init>(Ljava/lang/Object;)V

    sput-object v1, Lgroovy/cli/internal/CliBuilderInternal$_commons2picocli_closure4;->$callSiteArray:Ljava/lang/ref/SoftReference;

    :cond_1
    iget-object v0, v0, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;->array:[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    return-object v0
.end method

.method public constructor <init>(Ljava/lang/Object;Ljava/lang/Object;Lgroovy/lang/Reference;)V
    .locals 0

    invoke-static {}, Lgroovy/cli/internal/CliBuilderInternal$_commons2picocli_closure4;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    invoke-direct {p0, p1, p2}, Lgroovy/lang/Closure;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    iput-object p3, p0, Lgroovy/cli/internal/CliBuilderInternal$_commons2picocli_closure4;->shortname:Lgroovy/lang/Reference;

    return-void
.end method


# virtual methods
.method protected synthetic $getStaticMetaClass()Lgroovy/lang/MetaClass;
    .locals 2

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    const-class v1, Lgroovy/cli/internal/CliBuilderInternal$_commons2picocli_closure4;

    if-eq v0, v1, :cond_0

    invoke-static {p0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->initMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0

    :cond_0
    sget-object v0, Lgroovy/cli/internal/CliBuilderInternal$_commons2picocli_closure4;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    if-nez v0, :cond_1

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getClassInfo(Ljava/lang/Class;)Lorg/codehaus/groovy/reflection/ClassInfo;

    move-result-object v0

    sput-object v0, Lgroovy/cli/internal/CliBuilderInternal$_commons2picocli_closure4;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    :cond_1
    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0
.end method

.method public call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    invoke-static {}, Lgroovy/cli/internal/CliBuilderInternal$_commons2picocli_closure4;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v0

    const/4 v1, 0x2

    aget-object v0, v0, v1

    invoke-interface {v0, p0, p1, p2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callCurrent(Lgroovy/lang/GroovyObject;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public doCall(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 27

    move-object/from16 v0, p0

    move-object/from16 v1, p1

    move-object/from16 v2, p2

    const-class v3, Lgroovy/cli/internal/CliBuilderInternal$_commons2picocli_closure4;

    invoke-static {}, Lgroovy/cli/internal/CliBuilderInternal$_commons2picocli_closure4;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v4

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->isOrigInt()Z

    move-result v5

    const-string v6, "1"

    const-string v7, ">"

    const-string v8, "<"

    const-string v9, "paramLabel"

    const-string v10, "longOpt"

    const-string v11, "*"

    const-string v12, "argName"

    const-string v13, "COMMONS_CLI_UNLIMITED_VALUES"

    const-string v14, "optionalArg"

    const-string v15, "0"

    const-string v16, "1..*"

    const-string v0, "+"

    move-object/from16 v17, v4

    const-string v4, "--"

    const-string v18, "names"

    move-object/from16 v19, v4

    const-string v4, "-"

    move-object/from16 v20, v4

    const-string v4, "args"

    const-string v21, "arity"

    move-object/from16 v22, v10

    const-string v10, ""

    move-object/from16 v23, v7

    const/16 v24, 0x0

    .line 398
    invoke-static/range {v24 .. v24}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v7

    if-eqz v5, :cond_d

    .line 0
    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->isOrigZ()Z

    move-result v5

    if-eqz v5, :cond_d

    sget-boolean v5, Lgroovy/cli/internal/CliBuilderInternal$_commons2picocli_closure4;->__$stMC:Z

    if-nez v5, :cond_d

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->disabledStandardMetaClass()Z

    move-result v5

    if-nez v5, :cond_d

    .line 396
    invoke-static {v1, v4}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v5

    if-eqz v5, :cond_0

    invoke-static {v2, v0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    move/from16 v0, v24

    :goto_0
    if-eqz v0, :cond_1

    const/4 v0, 0x1

    new-array v1, v0, [Ljava/lang/Object;

    const/4 v2, 0x2

    new-array v2, v2, [Ljava/lang/Object;

    aput-object v21, v2, v24

    aput-object v16, v2, v0

    .line 397
    invoke-static {v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createMap([Ljava/lang/Object;)Ljava/util/Map;

    move-result-object v0

    aput-object v0, v1, v24

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    return-object v0

    .line 398
    :cond_1
    invoke-static {v1, v4}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_2

    invoke-static {v2, v7}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_2

    const/4 v0, 0x1

    goto :goto_1

    :cond_2
    move/from16 v0, v24

    :goto_1
    if-eqz v0, :cond_3

    const/4 v0, 0x1

    new-array v1, v0, [Ljava/lang/Object;

    const/4 v2, 0x2

    new-array v2, v2, [Ljava/lang/Object;

    aput-object v21, v2, v24

    aput-object v15, v2, v0

    .line 399
    invoke-static {v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createMap([Ljava/lang/Object;)Ljava/util/Map;

    move-result-object v0

    aput-object v0, v1, v24

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    return-object v0

    .line 400
    :cond_3
    invoke-static {v1, v4}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_5

    .line 401
    const-class v0, Lgroovy/cli/internal/CliBuilderInternal;

    move-object v1, v13

    check-cast v1, Ljava/lang/String;

    invoke-static {v3, v0, v13}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->getField(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v0

    invoke-static {v2, v0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_4

    const/4 v0, 0x1

    new-array v1, v0, [Ljava/lang/Object;

    const/4 v3, 0x2

    new-array v2, v3, [Ljava/lang/Object;

    aput-object v21, v2, v24

    aput-object v11, v2, v0

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createMap([Ljava/lang/Object;)Ljava/util/Map;

    move-result-object v0

    aput-object v0, v1, v24

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    goto :goto_2

    :cond_4
    const/4 v0, 0x1

    const/4 v3, 0x2

    new-array v1, v0, [Ljava/lang/Object;

    new-array v3, v3, [Ljava/lang/Object;

    aput-object v21, v3, v24

    new-instance v4, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v5, v0, [Ljava/lang/Object;

    aput-object v2, v5, v24

    filled-new-array {v10, v10}, [Ljava/lang/String;

    move-result-object v2

    invoke-direct {v4, v5, v2}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    aput-object v4, v3, v0

    invoke-static {v3}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createMap([Ljava/lang/Object;)Ljava/util/Map;

    move-result-object v0

    aput-object v0, v1, v24

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    :goto_2
    return-object v0

    :cond_5
    const/4 v0, 0x1

    .line 402
    invoke-static {v1, v14}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_7

    .line 403
    invoke-static/range {p2 .. p2}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_6

    new-array v1, v0, [Ljava/lang/Object;

    const/4 v3, 0x2

    new-array v2, v3, [Ljava/lang/Object;

    aput-object v21, v2, v24

    const-string v3, "0..1"

    aput-object v3, v2, v0

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createMap([Ljava/lang/Object;)Ljava/util/Map;

    move-result-object v0

    aput-object v0, v1, v24

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    goto :goto_3

    :cond_6
    const/4 v3, 0x2

    new-array v1, v0, [Ljava/lang/Object;

    new-array v2, v3, [Ljava/lang/Object;

    aput-object v21, v2, v24

    aput-object v6, v2, v0

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createMap([Ljava/lang/Object;)Ljava/util/Map;

    move-result-object v0

    aput-object v0, v1, v24

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    :goto_3
    return-object v0

    :cond_7
    const/4 v3, 0x2

    .line 404
    invoke-static {v1, v12}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v4

    if-eqz v4, :cond_8

    new-array v1, v0, [Ljava/lang/Object;

    new-array v3, v3, [Ljava/lang/Object;

    aput-object v9, v3, v24

    .line 405
    new-instance v4, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v5, v0, [Ljava/lang/Object;

    aput-object v2, v5, v24

    move-object/from16 v2, v23

    filled-new-array {v8, v2}, [Ljava/lang/String;

    move-result-object v2

    invoke-direct {v4, v5, v2}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    aput-object v4, v3, v0

    invoke-static {v3}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createMap([Ljava/lang/Object;)Ljava/util/Map;

    move-result-object v0

    aput-object v0, v1, v24

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    return-object v0

    :cond_8
    move-object/from16 v5, v22

    .line 406
    invoke-static {v1, v5}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_a

    .line 407
    aget-object v1, v17, v0

    move-object/from16 v3, p0

    invoke-interface {v1, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGroovyObjectGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_9

    new-array v1, v0, [Ljava/lang/Object;

    const/4 v4, 0x2

    new-array v5, v4, [Ljava/lang/Object;

    aput-object v18, v5, v24

    const/4 v4, 0x3

    new-array v4, v4, [Ljava/lang/Object;

    .line 408
    new-instance v6, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v7, v0, [Ljava/lang/Object;

    iget-object v8, v3, Lgroovy/cli/internal/CliBuilderInternal$_commons2picocli_closure4;->shortname:Lgroovy/lang/Reference;

    invoke-virtual {v8}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v8

    aput-object v8, v7, v24

    move-object/from16 v8, v20

    filled-new-array {v8, v10}, [Ljava/lang/String;

    move-result-object v9

    invoke-direct {v6, v7, v9}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    aput-object v6, v4, v24

    new-instance v6, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v7, v0, [Ljava/lang/Object;

    aput-object v2, v7, v24

    filled-new-array {v8, v10}, [Ljava/lang/String;

    move-result-object v8

    invoke-direct {v6, v7, v8}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    aput-object v6, v4, v0

    new-instance v6, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v7, v0, [Ljava/lang/Object;

    aput-object v2, v7, v24

    move-object/from16 v9, v19

    filled-new-array {v9, v10}, [Ljava/lang/String;

    move-result-object v2

    invoke-direct {v6, v7, v2}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    const/4 v7, 0x2

    aput-object v6, v4, v7

    invoke-static {v4}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v2

    const-class v4, [Ljava/lang/String;

    invoke-static {v2, v4}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->asType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, [Ljava/lang/String;

    aput-object v2, v5, v0

    invoke-static {v5}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createMap([Ljava/lang/Object;)Ljava/util/Map;

    move-result-object v0

    aput-object v0, v1, v24

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    goto :goto_4

    :cond_9
    move-object/from16 v9, v19

    move-object/from16 v8, v20

    const/4 v7, 0x2

    new-array v1, v0, [Ljava/lang/Object;

    new-array v4, v7, [Ljava/lang/Object;

    aput-object v18, v4, v24

    new-array v5, v7, [Ljava/lang/Object;

    .line 409
    new-instance v6, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v7, v0, [Ljava/lang/Object;

    iget-object v11, v3, Lgroovy/cli/internal/CliBuilderInternal$_commons2picocli_closure4;->shortname:Lgroovy/lang/Reference;

    invoke-virtual {v11}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v11

    aput-object v11, v7, v24

    filled-new-array {v8, v10}, [Ljava/lang/String;

    move-result-object v8

    invoke-direct {v6, v7, v8}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    aput-object v6, v5, v24

    new-instance v6, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v7, v0, [Ljava/lang/Object;

    aput-object v2, v7, v24

    filled-new-array {v9, v10}, [Ljava/lang/String;

    move-result-object v2

    invoke-direct {v6, v7, v2}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    aput-object v6, v5, v0

    invoke-static {v5}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v2

    const-class v5, [Ljava/lang/String;

    invoke-static {v2, v5}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->asType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, [Ljava/lang/String;

    aput-object v2, v4, v0

    invoke-static {v4}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createMap([Ljava/lang/Object;)Ljava/util/Map;

    move-result-object v0

    aput-object v0, v1, v24

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    :goto_4
    return-object v0

    :cond_a
    move-object/from16 v3, p0

    const-string v4, "valueSeparator"

    .line 410
    invoke-static {v1, v4}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v4

    if-eqz v4, :cond_b

    new-array v1, v0, [Ljava/lang/Object;

    const/4 v4, 0x2

    new-array v4, v4, [Ljava/lang/Object;

    const-string v5, "splitRegex"

    aput-object v5, v4, v24

    .line 411
    new-instance v5, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v6, v0, [Ljava/lang/Object;

    aput-object v2, v6, v24

    filled-new-array {v10, v10}, [Ljava/lang/String;

    move-result-object v2

    invoke-direct {v5, v6, v2}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    aput-object v5, v4, v0

    invoke-static {v4}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createMap([Ljava/lang/Object;)Ljava/util/Map;

    move-result-object v0

    aput-object v0, v1, v24

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    return-object v0

    :cond_b
    const-string v4, "convert"

    .line 412
    invoke-static {v1, v4}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v4

    if-eqz v4, :cond_c

    new-array v1, v0, [Ljava/lang/Object;

    const/4 v4, 0x2

    new-array v4, v4, [Ljava/lang/Object;

    const-string v5, "converters"

    aput-object v5, v4, v24

    new-array v5, v0, [Ljava/lang/Object;

    aput-object v2, v5, v24

    .line 413
    invoke-static {v5}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v2

    const-class v5, [Lgroovyjarjarpicocli/CommandLine$ITypeConverter;

    invoke-static {v2, v5}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->asType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, [Lgroovyjarjarpicocli/CommandLine$ITypeConverter;

    aput-object v2, v4, v0

    invoke-static {v4}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createMap([Ljava/lang/Object;)Ljava/util/Map;

    move-result-object v0

    aput-object v0, v1, v24

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    return-object v0

    :cond_c
    new-array v4, v0, [Ljava/lang/Object;

    const/4 v5, 0x2

    new-array v5, v5, [Ljava/lang/Object;

    aput-object v1, v5, v24

    aput-object v2, v5, v0

    .line 415
    invoke-static {v5}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createMap([Ljava/lang/Object;)Ljava/util/Map;

    move-result-object v0

    aput-object v0, v4, v24

    invoke-static {v4}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    return-object v0

    :cond_d
    move-object v5, v0

    move-object/from16 v25, v19

    move-object/from16 v26, v20

    move-object/from16 v0, v23

    .line 396
    invoke-static {v1, v4}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v19

    if-eqz v19, :cond_e

    invoke-static {v2, v5}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v5

    if-eqz v5, :cond_e

    const/4 v5, 0x1

    goto :goto_5

    :cond_e
    move/from16 v5, v24

    :goto_5
    if-eqz v5, :cond_f

    const/4 v5, 0x1

    new-array v0, v5, [Ljava/lang/Object;

    const/4 v1, 0x2

    new-array v1, v1, [Ljava/lang/Object;

    aput-object v21, v1, v24

    aput-object v16, v1, v5

    .line 397
    invoke-static {v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createMap([Ljava/lang/Object;)Ljava/util/Map;

    move-result-object v1

    aput-object v1, v0, v24

    invoke-static {v0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    return-object v0

    .line 398
    :cond_f
    invoke-static {v1, v4}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v5

    if-eqz v5, :cond_10

    invoke-static {v2, v7}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v5

    if-eqz v5, :cond_10

    const/4 v5, 0x1

    goto :goto_6

    :cond_10
    move/from16 v5, v24

    :goto_6
    if-eqz v5, :cond_11

    const/4 v5, 0x1

    new-array v0, v5, [Ljava/lang/Object;

    const/4 v1, 0x2

    new-array v1, v1, [Ljava/lang/Object;

    aput-object v21, v1, v24

    aput-object v15, v1, v5

    .line 399
    invoke-static {v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createMap([Ljava/lang/Object;)Ljava/util/Map;

    move-result-object v1

    aput-object v1, v0, v24

    invoke-static {v0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    return-object v0

    .line 400
    :cond_11
    invoke-static {v1, v4}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v4

    if-eqz v4, :cond_13

    .line 401
    const-class v0, Lgroovy/cli/internal/CliBuilderInternal;

    move-object v1, v13

    check-cast v1, Ljava/lang/String;

    invoke-static {v3, v0, v13}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->getField(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v0

    invoke-static {v2, v0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_12

    const/4 v3, 0x1

    new-array v0, v3, [Ljava/lang/Object;

    const/4 v1, 0x2

    new-array v1, v1, [Ljava/lang/Object;

    aput-object v21, v1, v24

    aput-object v11, v1, v3

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createMap([Ljava/lang/Object;)Ljava/util/Map;

    move-result-object v1

    aput-object v1, v0, v24

    invoke-static {v0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    goto :goto_7

    :cond_12
    const/4 v1, 0x2

    const/4 v3, 0x1

    new-array v0, v3, [Ljava/lang/Object;

    new-array v1, v1, [Ljava/lang/Object;

    aput-object v21, v1, v24

    new-instance v4, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v5, v3, [Ljava/lang/Object;

    aput-object v2, v5, v24

    filled-new-array {v10, v10}, [Ljava/lang/String;

    move-result-object v2

    invoke-direct {v4, v5, v2}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    aput-object v4, v1, v3

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createMap([Ljava/lang/Object;)Ljava/util/Map;

    move-result-object v1

    aput-object v1, v0, v24

    invoke-static {v0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    :goto_7
    return-object v0

    :cond_13
    const/4 v3, 0x1

    .line 402
    invoke-static {v1, v14}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v4

    if-eqz v4, :cond_15

    .line 403
    invoke-static/range {p2 .. p2}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_14

    new-array v0, v3, [Ljava/lang/Object;

    const/4 v4, 0x2

    new-array v1, v4, [Ljava/lang/Object;

    aput-object v21, v1, v24

    const-string v2, "0..1"

    aput-object v2, v1, v3

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createMap([Ljava/lang/Object;)Ljava/util/Map;

    move-result-object v1

    aput-object v1, v0, v24

    invoke-static {v0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    goto :goto_8

    :cond_14
    const/4 v4, 0x2

    new-array v0, v3, [Ljava/lang/Object;

    new-array v1, v4, [Ljava/lang/Object;

    aput-object v21, v1, v24

    aput-object v6, v1, v3

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createMap([Ljava/lang/Object;)Ljava/util/Map;

    move-result-object v1

    aput-object v1, v0, v24

    invoke-static {v0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    :goto_8
    return-object v0

    :cond_15
    const/4 v4, 0x2

    .line 404
    invoke-static {v1, v12}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v5

    if-eqz v5, :cond_16

    new-array v1, v3, [Ljava/lang/Object;

    new-array v4, v4, [Ljava/lang/Object;

    aput-object v9, v4, v24

    .line 405
    new-instance v5, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v6, v3, [Ljava/lang/Object;

    aput-object v2, v6, v24

    filled-new-array {v8, v0}, [Ljava/lang/String;

    move-result-object v0

    invoke-direct {v5, v6, v0}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    aput-object v5, v4, v3

    invoke-static {v4}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createMap([Ljava/lang/Object;)Ljava/util/Map;

    move-result-object v0

    aput-object v0, v1, v24

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    return-object v0

    :cond_16
    move-object/from16 v0, v22

    .line 406
    invoke-static {v1, v0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_18

    .line 407
    aget-object v0, v17, v24

    move-object/from16 v4, p0

    invoke-interface {v0, v4}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGroovyObjectGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_17

    new-array v0, v3, [Ljava/lang/Object;

    const/4 v1, 0x2

    new-array v5, v1, [Ljava/lang/Object;

    aput-object v18, v5, v24

    const/4 v1, 0x3

    new-array v1, v1, [Ljava/lang/Object;

    .line 408
    new-instance v6, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v7, v3, [Ljava/lang/Object;

    iget-object v8, v4, Lgroovy/cli/internal/CliBuilderInternal$_commons2picocli_closure4;->shortname:Lgroovy/lang/Reference;

    invoke-virtual {v8}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v8

    aput-object v8, v7, v24

    move-object/from16 v8, v26

    filled-new-array {v8, v10}, [Ljava/lang/String;

    move-result-object v9

    invoke-direct {v6, v7, v9}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    aput-object v6, v1, v24

    new-instance v6, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v7, v3, [Ljava/lang/Object;

    aput-object v2, v7, v24

    filled-new-array {v8, v10}, [Ljava/lang/String;

    move-result-object v8

    invoke-direct {v6, v7, v8}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    aput-object v6, v1, v3

    new-instance v6, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v7, v3, [Ljava/lang/Object;

    aput-object v2, v7, v24

    move-object/from16 v9, v25

    filled-new-array {v9, v10}, [Ljava/lang/String;

    move-result-object v2

    invoke-direct {v6, v7, v2}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    const/4 v7, 0x2

    aput-object v6, v1, v7

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v1

    const-class v2, [Ljava/lang/String;

    invoke-static {v1, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->asType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, [Ljava/lang/String;

    aput-object v1, v5, v3

    invoke-static {v5}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createMap([Ljava/lang/Object;)Ljava/util/Map;

    move-result-object v1

    aput-object v1, v0, v24

    invoke-static {v0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    goto :goto_9

    :cond_17
    move-object/from16 v9, v25

    move-object/from16 v8, v26

    const/4 v7, 0x2

    new-array v0, v3, [Ljava/lang/Object;

    new-array v1, v7, [Ljava/lang/Object;

    aput-object v18, v1, v24

    new-array v5, v7, [Ljava/lang/Object;

    .line 409
    new-instance v6, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v7, v3, [Ljava/lang/Object;

    iget-object v11, v4, Lgroovy/cli/internal/CliBuilderInternal$_commons2picocli_closure4;->shortname:Lgroovy/lang/Reference;

    invoke-virtual {v11}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v11

    aput-object v11, v7, v24

    filled-new-array {v8, v10}, [Ljava/lang/String;

    move-result-object v8

    invoke-direct {v6, v7, v8}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    aput-object v6, v5, v24

    new-instance v6, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v7, v3, [Ljava/lang/Object;

    aput-object v2, v7, v24

    filled-new-array {v9, v10}, [Ljava/lang/String;

    move-result-object v2

    invoke-direct {v6, v7, v2}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    aput-object v6, v5, v3

    invoke-static {v5}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v2

    const-class v5, [Ljava/lang/String;

    invoke-static {v2, v5}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->asType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, [Ljava/lang/String;

    aput-object v2, v1, v3

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createMap([Ljava/lang/Object;)Ljava/util/Map;

    move-result-object v1

    aput-object v1, v0, v24

    invoke-static {v0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    :goto_9
    return-object v0

    :cond_18
    move-object/from16 v4, p0

    const-string v0, "valueSeparator"

    .line 410
    invoke-static {v1, v0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_19

    new-array v0, v3, [Ljava/lang/Object;

    const/4 v1, 0x2

    new-array v1, v1, [Ljava/lang/Object;

    const-string v5, "splitRegex"

    aput-object v5, v1, v24

    .line 411
    new-instance v5, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v6, v3, [Ljava/lang/Object;

    aput-object v2, v6, v24

    filled-new-array {v10, v10}, [Ljava/lang/String;

    move-result-object v2

    invoke-direct {v5, v6, v2}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    aput-object v5, v1, v3

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createMap([Ljava/lang/Object;)Ljava/util/Map;

    move-result-object v1

    aput-object v1, v0, v24

    invoke-static {v0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    return-object v0

    :cond_19
    const-string v0, "convert"

    .line 412
    invoke-static {v1, v0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_1a

    new-array v0, v3, [Ljava/lang/Object;

    const/4 v1, 0x2

    new-array v1, v1, [Ljava/lang/Object;

    const-string v5, "converters"

    aput-object v5, v1, v24

    new-array v5, v3, [Ljava/lang/Object;

    aput-object v2, v5, v24

    .line 413
    invoke-static {v5}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v2

    const-class v5, [Lgroovyjarjarpicocli/CommandLine$ITypeConverter;

    invoke-static {v2, v5}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->asType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, [Lgroovyjarjarpicocli/CommandLine$ITypeConverter;

    aput-object v2, v1, v3

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createMap([Ljava/lang/Object;)Ljava/util/Map;

    move-result-object v1

    aput-object v1, v0, v24

    invoke-static {v0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    return-object v0

    :cond_1a
    new-array v0, v3, [Ljava/lang/Object;

    const/4 v5, 0x2

    new-array v5, v5, [Ljava/lang/Object;

    aput-object v1, v5, v24

    aput-object v2, v5, v3

    .line 415
    invoke-static {v5}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createMap([Ljava/lang/Object;)Ljava/util/Map;

    move-result-object v1

    aput-object v1, v0, v24

    invoke-static {v0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    return-object v0
.end method

.method public getShortname()Ljava/lang/Object;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    invoke-static {}, Lgroovy/cli/internal/CliBuilderInternal$_commons2picocli_closure4;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    iget-object v0, p0, Lgroovy/cli/internal/CliBuilderInternal$_commons2picocli_closure4;->shortname:Lgroovy/lang/Reference;

    invoke-virtual {v0}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    return-object v0
.end method
