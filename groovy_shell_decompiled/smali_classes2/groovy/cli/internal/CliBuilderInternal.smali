.class public Lgroovy/cli/internal/CliBuilderInternal;
.super Ljava/lang/Object;
.source "CliBuilderInternal.groovy"

# interfaces
.implements Lgroovy/lang/GroovyObject;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lgroovy/cli/internal/CliBuilderInternal$ArgSpecAttributes;
    }
.end annotation


# static fields
.field private static synthetic $callSiteArray:Ljava/lang/ref/SoftReference; = null

.field private static synthetic $staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo; = null

.field private static synthetic $staticClassInfo$:Lorg/codehaus/groovy/reflection/ClassInfo; = null

.field private static final COMMONS_CLI_UNLIMITED_VALUES:I = -0x2

.field public static transient synthetic __$stMC:Z


# instance fields
.field private acceptLongOptionsWithSingleHyphen:Z

.field private commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

.field private errorWriter:Ljava/io/PrintWriter;

.field private expandArgumentFiles:Z

.field private footer:Ljava/lang/String;

.field private header:Ljava/lang/String;

.field private transient synthetic metaClass:Lgroovy/lang/MetaClass;

.field private name:Ljava/lang/String;

.field private final parser:Lgroovyjarjarpicocli/CommandLine$Model$ParserSpec;

.field private posix:Ljava/lang/Boolean;

.field private savedTypeOptions:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Lgroovy/cli/TypedOption;",
            ">;"
        }
    .end annotation
.end field

.field private stopAtNonOption:Z

.field private usage:Ljava/lang/String;

.field private final usageMessage:Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;

.field private width:I

.field private writer:Ljava/io/PrintWriter;


# direct methods
.method private static synthetic $createCallSiteArray()Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;
    .locals 3

    const/16 v0, 0x97

    new-array v0, v0, [Ljava/lang/String;

    invoke-static {v0}, Lgroovy/cli/internal/CliBuilderInternal;->$createCallSiteArray_1([Ljava/lang/String;)V

    new-instance v1, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;

    const-class v2, Lgroovy/cli/internal/CliBuilderInternal;

    invoke-direct {v1, v2, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;-><init>(Ljava/lang/Class;[Ljava/lang/String;)V

    return-object v1
.end method

.method private static synthetic $createCallSiteArray_1([Ljava/lang/String;)V
    .locals 14

    const/4 v0, 0x0

    const-string v1, "<$constructor$>"

    aput-object v1, p0, v0

    const/4 v0, 0x1

    const-string v2, "out"

    aput-object v2, p0, v0

    const/4 v0, 0x2

    aput-object v1, p0, v0

    const/4 v0, 0x3

    const-string v2, "err"

    aput-object v2, p0, v0

    const/4 v0, 0x4

    const-string v2, "DEFAULT_USAGE_WIDTH"

    aput-object v2, p0, v0

    const/4 v0, 0x5

    const-string v2, "toggleBooleanFlags"

    aput-object v2, p0, v0

    const/4 v0, 0x6

    const-string v2, "overwrittenOptionsAllowed"

    aput-object v2, p0, v0

    const/4 v0, 0x7

    const-string v2, "limitSplit"

    aput-object v2, p0, v0

    const/16 v0, 0x8

    const-string v2, "aritySatisfiedByAttachedOptionParam"

    aput-object v2, p0, v0

    const/16 v0, 0x9

    const-string v2, "unmatchedOptionsArePositionalParams"

    aput-object v2, p0, v0

    const/16 v0, 0xa

    const-string v3, "stopAtPositional"

    aput-object v3, p0, v0

    const/16 v0, 0xb

    aput-object v1, p0, v0

    const/16 v0, 0xc

    aput-object v1, p0, v0

    const/16 v0, 0xd

    aput-object v1, p0, v0

    const/16 v0, 0xe

    const-string v4, "create"

    aput-object v4, p0, v0

    const/16 v0, 0xf

    const-string v5, "customSynopsis"

    aput-object v5, p0, v0

    const/16 v0, 0x10

    const-string v5, "footer"

    aput-object v5, p0, v0

    const/16 v0, 0x11

    const-string v5, "description"

    aput-object v5, p0, v0

    const/16 v0, 0x12

    const-string v6, "width"

    aput-object v6, p0, v0

    const/16 v0, 0x13

    const-string v6, "expandAtFiles"

    aput-object v6, p0, v0

    const/16 v0, 0x14

    const-string v6, "posixClusteredShortOptionsAllowed"

    aput-object v6, p0, v0

    const/16 v0, 0x15

    aput-object v3, p0, v0

    const/16 v0, 0x16

    aput-object v2, p0, v0

    const/16 v0, 0x17

    const-string v2, "opt"

    aput-object v2, p0, v0

    const/16 v0, 0x18

    const-string v2, "remove"

    aput-object v2, p0, v0

    const/16 v0, 0x19

    const-string v3, "size"

    aput-object v3, p0, v0

    const/16 v0, 0x1a

    const-string v6, "getAt"

    aput-object v6, p0, v0

    const/16 v0, 0x1b

    aput-object v6, p0, v0

    const/16 v0, 0x1c

    const-string v7, "option"

    aput-object v7, p0, v0

    const/16 v0, 0x1d

    aput-object v6, p0, v0

    const/16 v0, 0x1e

    const-string v8, "addOption"

    aput-object v8, p0, v0

    const/16 v0, 0x1f

    aput-object v4, p0, v0

    const/16 v0, 0x20

    aput-object v3, p0, v0

    const/16 v0, 0x21

    aput-object v6, p0, v0

    const/16 v0, 0x22

    aput-object v6, p0, v0

    const/16 v0, 0x23

    aput-object v7, p0, v0

    const/16 v0, 0x24

    aput-object v6, p0, v0

    const/16 v0, 0x25

    aput-object v8, p0, v0

    const/16 v0, 0x26

    aput-object v4, p0, v0

    const/16 v0, 0x27

    aput-object v3, p0, v0

    const/16 v0, 0x28

    aput-object v6, p0, v0

    const/16 v0, 0x29

    aput-object v6, p0, v0

    const/16 v0, 0x2a

    aput-object v8, p0, v0

    const/16 v0, 0x2b

    aput-object v4, p0, v0

    const/16 v0, 0x2c

    aput-object v3, p0, v0

    const/16 v0, 0x2d

    aput-object v6, p0, v0

    const/16 v0, 0x2e

    aput-object v6, p0, v0

    const/16 v0, 0x2f

    aput-object v8, p0, v0

    const/16 v0, 0x30

    aput-object v4, p0, v0

    const/16 v0, 0x31

    aput-object v3, p0, v0

    const/16 v0, 0x32

    aput-object v6, p0, v0

    const/16 v0, 0x33

    aput-object v6, p0, v0

    const/16 v0, 0x34

    const-string v3, "type"

    aput-object v3, p0, v0

    const/16 v0, 0x35

    aput-object v3, p0, v0

    const/16 v0, 0x36

    aput-object v1, p0, v0

    const/16 v0, 0x37

    aput-object v7, p0, v0

    const/16 v0, 0x38

    aput-object v6, p0, v0

    const/16 v0, 0x39

    aput-object v8, p0, v0

    const/16 v0, 0x3a

    aput-object v4, p0, v0

    const/16 v0, 0x3b

    aput-object v3, p0, v0

    const/16 v0, 0x3c

    const-string v4, "defaultValue"

    aput-object v4, p0, v0

    const/16 v0, 0x3d

    const-string v7, "converters"

    aput-object v7, p0, v0

    const/16 v0, 0x3e

    const-string v8, "invokeMethod"

    aput-object v8, p0, v0

    const/16 v0, 0x3f

    const-string v8, "getMetaClass"

    aput-object v8, p0, v0

    const/16 v0, 0x40

    const-string v8, "first"

    aput-object v8, p0, v0

    const/16 v0, 0x41

    const-string v9, "sort"

    aput-object v9, p0, v0

    const/16 v0, 0x42

    const-string v9, "names"

    aput-object v9, p0, v0

    const/16 v0, 0x43

    const-string v10, "length"

    aput-object v10, p0, v0

    const/16 v0, 0x44

    const-string v11, "substring"

    aput-object v11, p0, v0

    const/16 v0, 0x45

    aput-object v8, p0, v0

    const/16 v0, 0x46

    const-string v8, "sort"

    aput-object v8, p0, v0

    const/16 v0, 0x47

    aput-object v9, p0, v0

    const/16 v0, 0x48

    const-string v8, "startsWith"

    aput-object v8, p0, v0

    const/16 v0, 0x49

    const-string v8, "substring"

    aput-object v8, p0, v0

    const/16 v0, 0x4a

    aput-object v1, p0, v0

    const/16 v0, 0x4b

    const-string v8, "put"

    aput-object v8, p0, v0

    const/16 v0, 0x4c

    aput-object v8, p0, v0

    const/16 v0, 0x4d

    aput-object v8, p0, v0

    const/16 v0, 0x4e

    aput-object v8, p0, v0

    const/16 v0, 0x4f

    aput-object v1, p0, v0

    const/16 v0, 0x50

    aput-object v8, p0, v0

    const/16 v0, 0x51

    aput-object v8, p0, v0

    const/16 v0, 0x52

    const-string v11, "getClass"

    aput-object v11, p0, v0

    const/16 v0, 0x53

    aput-object v8, p0, v0

    const/16 v0, 0x54

    const-string v8, "putAt"

    aput-object v8, p0, v0

    const/16 v0, 0x55

    const-string v8, "createCommandLine"

    aput-object v8, p0, v0

    const/16 v0, 0x56

    aput-object v1, p0, v0

    const/16 v0, 0x57

    const-string v8, "parseArgs"

    aput-object v8, p0, v0

    const/16 v0, 0x58

    const-string v8, "println"

    aput-object v8, p0, v0

    const/16 v0, 0x59

    const-string v8, "plus"

    aput-object v8, p0, v0

    const/16 v0, 0x5a

    const-string v8, "message"

    aput-object v8, p0, v0

    const/16 v0, 0x5b

    const-string v8, "printUsage"

    aput-object v8, p0, v0

    const/16 v0, 0x5c

    const-string v11, "commandLine"

    aput-object v11, p0, v0

    const/16 v0, 0x5d

    const-string v12, "parser"

    aput-object v12, p0, v0

    const/16 v0, 0x5e

    const-string v12, "usageMessage"

    aput-object v12, p0, v0

    const/16 v0, 0x5f

    const-string v12, "name"

    aput-object v12, p0, v0

    const/16 v0, 0x60

    const-string v12, "empty"

    aput-object v12, p0, v0

    const/16 v0, 0x61

    const-string v12, "positionalParameters"

    aput-object v12, p0, v0

    const/16 v0, 0x62

    const-string v12, "addPositional"

    aput-object v12, p0, v0

    const/16 v0, 0x63

    const-string v12, "build"

    aput-object v12, p0, v0

    const/16 v0, 0x64

    const-string v12, "hidden"

    aput-object v12, p0, v0

    const/16 v0, 0x65

    const-string v12, "paramLabel"

    aput-object v12, p0, v0

    const/16 v0, 0x66

    const-string v12, "arity"

    aput-object v12, p0, v0

    const/16 v0, 0x67

    aput-object v3, p0, v0

    const/16 v0, 0x68

    const-string v13, "builder"

    aput-object v13, p0, v0

    const/16 v0, 0x69

    aput-object v1, p0, v0

    const/16 v0, 0x6a

    aput-object v8, p0, v0

    const/16 v0, 0x6b

    aput-object v11, p0, v0

    const/16 v0, 0x6c

    const-string v1, "createCommandLine"

    aput-object v1, p0, v0

    const/16 v0, 0x6d

    aput-object v8, p0, v0

    const/16 v0, 0x6e

    aput-object v11, p0, v0

    const/16 v0, 0x6f

    const-string v1, "usage"

    aput-object v1, p0, v0

    const/16 v0, 0x70

    const-string v1, "flush"

    aput-object v1, p0, v0

    const/16 v0, 0x71

    aput-object v5, p0, v0

    const/16 v0, 0x72

    aput-object v13, p0, v0

    const/16 v0, 0x73

    const-string v1, "longOpt"

    aput-object v1, p0, v0

    const/16 v0, 0x74

    aput-object v9, p0, v0

    const/16 v0, 0x75

    aput-object v1, p0, v0

    const/16 v0, 0x76

    aput-object v1, p0, v0

    const/16 v0, 0x77

    aput-object v2, p0, v0

    const/16 v0, 0x78

    aput-object v5, p0, v0

    const/16 v0, 0x79

    aput-object v13, p0, v0

    const/16 v0, 0x7a

    const-string v1, "each"

    aput-object v1, p0, v0

    const/16 v0, 0x7b

    const-string v1, "commons2picocli"

    aput-object v1, p0, v0

    const/16 v0, 0x7c

    aput-object v3, p0, v0

    const/16 v0, 0x7d

    aput-object v12, p0, v0

    const/16 v0, 0x7e

    aput-object v10, p0, v0

    const/16 v0, 0x7f

    aput-object v7, p0, v0

    const/16 v0, 0x80

    aput-object v3, p0, v0

    const/16 v0, 0x81

    aput-object v12, p0, v0

    const/16 v0, 0x82

    const-string v1, "convert"

    aput-object v1, p0, v0

    const/16 v0, 0x83

    aput-object v3, p0, v0

    const/16 v0, 0x84

    aput-object v12, p0, v0

    const/16 v0, 0x85

    aput-object v10, p0, v0

    const/16 v0, 0x86

    aput-object v7, p0, v0

    const/16 v0, 0x87

    aput-object v3, p0, v0

    const/16 v0, 0x88

    aput-object v12, p0, v0

    const/16 v0, 0x89

    const-string v1, "convert"

    aput-object v1, p0, v0

    const/16 v0, 0x8a

    const-string v1, "build"

    aput-object v1, p0, v0

    const/16 v0, 0x8b

    const-string v1, "args"

    aput-object v1, p0, v0

    const/16 v0, 0x8c

    const-string v1, "optionalArg"

    aput-object v1, p0, v0

    const/16 v0, 0x8d

    const-string v1, "args"

    aput-object v1, p0, v0

    const/16 v0, 0x8e

    aput-object v2, p0, v0

    const/16 v0, 0x8f

    aput-object v2, p0, v0

    const/16 v0, 0x90

    aput-object v4, p0, v0

    const/16 v0, 0x91

    aput-object v2, p0, v0

    const/16 v0, 0x92

    const-string v1, "sum"

    aput-object v1, p0, v0

    const/16 v0, 0x93

    const-string v1, "collectMany"

    aput-object v1, p0, v0

    const/16 v0, 0x94

    aput-object v10, p0, v0

    const/16 v0, 0x95

    aput-object v6, p0, v0

    const/16 v0, 0x96

    aput-object v10, p0, v0

    return-void
.end method

.method private static synthetic $getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;
    .locals 2

    sget-object v0, Lgroovy/cli/internal/CliBuilderInternal;->$callSiteArray:Ljava/lang/ref/SoftReference;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Ljava/lang/ref/SoftReference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;

    if-nez v0, :cond_1

    :cond_0
    invoke-static {}, Lgroovy/cli/internal/CliBuilderInternal;->$createCallSiteArray()Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;

    move-result-object v0

    new-instance v1, Ljava/lang/ref/SoftReference;

    invoke-direct {v1, v0}, Ljava/lang/ref/SoftReference;-><init>(Ljava/lang/Object;)V

    sput-object v1, Lgroovy/cli/internal/CliBuilderInternal;->$callSiteArray:Ljava/lang/ref/SoftReference;

    :cond_1
    iget-object v0, v0, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;->array:[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    return-object v0
.end method

.method public constructor <init>()V
    .locals 11
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    invoke-static {}, Lgroovy/cli/internal/CliBuilderInternal;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const-string v1, "groovy"

    iput-object v1, p0, Lgroovy/cli/internal/CliBuilderInternal;->usage:Ljava/lang/String;

    iput-object v1, p0, Lgroovy/cli/internal/CliBuilderInternal;->name:Ljava/lang/String;

    const/4 v1, 0x1

    invoke-static {v1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v2

    iput-object v2, p0, Lgroovy/cli/internal/CliBuilderInternal;->posix:Ljava/lang/Boolean;

    iput-boolean v1, p0, Lgroovy/cli/internal/CliBuilderInternal;->expandArgumentFiles:Z

    iput-boolean v1, p0, Lgroovy/cli/internal/CliBuilderInternal;->stopAtNonOption:Z

    const/4 v3, 0x0

    iput-boolean v3, p0, Lgroovy/cli/internal/CliBuilderInternal;->acceptLongOptionsWithSingleHyphen:Z

    .line 79
    aget-object v4, v0, v3

    const-class v5, Ljava/io/PrintWriter;

    aget-object v1, v0, v1

    const-class v6, Ljava/lang/System;

    invoke-interface {v1, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-interface {v4, v5, v1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    const-class v4, Ljava/io/PrintWriter;

    invoke-static {v1, v4}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/io/PrintWriter;

    iput-object v1, p0, Lgroovy/cli/internal/CliBuilderInternal;->writer:Ljava/io/PrintWriter;

    const/4 v1, 0x2

    .line 87
    aget-object v1, v0, v1

    const-class v4, Ljava/io/PrintWriter;

    const/4 v5, 0x3

    aget-object v5, v0, v5

    const-class v6, Ljava/lang/System;

    invoke-interface {v5, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    invoke-interface {v1, v4, v5}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    const-class v4, Ljava/io/PrintWriter;

    invoke-static {v1, v4}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/io/PrintWriter;

    iput-object v1, p0, Lgroovy/cli/internal/CliBuilderInternal;->errorWriter:Ljava/io/PrintWriter;

    const/4 v1, 0x0

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v4

    move-object v5, v4

    check-cast v5, Ljava/lang/String;

    iput-object v4, p0, Lgroovy/cli/internal/CliBuilderInternal;->header:Ljava/lang/String;

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v1

    move-object v4, v1

    check-cast v4, Ljava/lang/String;

    iput-object v1, p0, Lgroovy/cli/internal/CliBuilderInternal;->footer:Ljava/lang/String;

    const/4 v1, 0x4

    aget-object v1, v0, v1

    const-class v4, Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;

    invoke-interface {v1, v4}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->intUnbox(Ljava/lang/Object;)I

    move-result v1

    iput v1, p0, Lgroovy/cli/internal/CliBuilderInternal;->width:I

    const/4 v1, 0x5

    .line 112
    aget-object v1, v0, v1

    const/4 v4, 0x6

    .line 117
    aget-object v4, v0, v4

    const/4 v5, 0x7

    .line 116
    aget-object v5, v0, v5

    const/16 v6, 0x8

    .line 115
    aget-object v6, v0, v6

    const/16 v7, 0x9

    .line 114
    aget-object v7, v0, v7

    const/16 v8, 0xa

    .line 113
    aget-object v8, v0, v8

    const/16 v9, 0xb

    .line 112
    aget-object v9, v0, v9

    const-class v10, Lgroovyjarjarpicocli/CommandLine$Model$ParserSpec;

    invoke-interface {v9, v10}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v9

    invoke-interface {v8, v9, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v8

    invoke-interface {v7, v8, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v7

    invoke-interface {v6, v7, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    invoke-interface {v5, v6, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    invoke-interface {v4, v5, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-static {v3}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v3

    invoke-interface {v1, v2, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    const-class v2, Lgroovyjarjarpicocli/CommandLine$Model$ParserSpec;

    invoke-static {v1, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarpicocli/CommandLine$Model$ParserSpec;

    iput-object v1, p0, Lgroovy/cli/internal/CliBuilderInternal;->parser:Lgroovyjarjarpicocli/CommandLine$Model$ParserSpec;

    const/16 v1, 0xc

    .line 127
    aget-object v1, v0, v1

    const-class v2, Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;

    invoke-interface {v1, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    const-class v2, Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;

    invoke-static {v1, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;

    iput-object v1, p0, Lgroovy/cli/internal/CliBuilderInternal;->usageMessage:Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;

    const/16 v1, 0xd

    .line 132
    aget-object v1, v0, v1

    const-class v2, Ljava/util/HashMap;

    invoke-interface {v1, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    const-class v2, Ljava/util/Map;

    invoke-static {v1, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/util/Map;

    iput-object v1, p0, Lgroovy/cli/internal/CliBuilderInternal;->savedTypeOptions:Ljava/util/Map;

    const/16 v1, 0xe

    .line 145
    aget-object v0, v0, v1

    const-class v1, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-interface {v0, v1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    const-class v1, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-static {v0, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    iput-object v0, p0, Lgroovy/cli/internal/CliBuilderInternal;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-virtual {p0}, Lgroovy/cli/internal/CliBuilderInternal;->$getStaticMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    iput-object v0, p0, Lgroovy/cli/internal/CliBuilderInternal;->metaClass:Lgroovy/lang/MetaClass;

    return-void
.end method

.method private commons2picocli(Ljava/lang/Object;Ljava/util/Map;)Ljava/util/Map;
    .locals 6

    new-instance v0, Lgroovy/lang/Reference;

    invoke-direct {v0, p1}, Lgroovy/lang/Reference;-><init>(Ljava/lang/Object;)V

    invoke-static {}, Lgroovy/cli/internal/CliBuilderInternal;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object p1

    const/16 v1, 0x8b

    .line 387
    aget-object v1, p1, v1

    invoke-interface {v1, p2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v1

    const/4 v2, 0x0

    const/4 v3, 0x1

    if-eqz v1, :cond_0

    const/16 v1, 0x8c

    aget-object v1, p1, v1

    invoke-interface {v1, p2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_0

    move v1, v3

    goto :goto_0

    :cond_0
    move v1, v2

    :goto_0
    if-eqz v1, :cond_1

    .line 388
    new-instance v1, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v4, v3, [Ljava/lang/Object;

    const/16 v5, 0x8d

    aget-object v5, p1, v5

    invoke-interface {v5, p2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    aput-object v5, v4, v2

    const-string v2, "0.."

    const-string v5, ""

    filled-new-array {v2, v5}, [Ljava/lang/String;

    move-result-object v2

    invoke-direct {v1, v4, v2}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    const/4 v2, 0x0

    const-string v4, "arity"

    move-object v5, v4

    check-cast v5, Ljava/lang/String;

    invoke-static {v1, v2, p2, v4}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->setProperty(Ljava/lang/Object;Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;)V

    const/16 v1, 0x8e

    .line 389
    aget-object v1, p1, v1

    const-string v2, "args"

    invoke-interface {v1, p2, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v1, 0x8f

    .line 390
    aget-object v1, p1, v1

    const-string v2, "optionalArg"

    invoke-interface {v1, p2, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_1
    const/16 v1, 0x90

    .line 392
    aget-object v1, p1, v1

    invoke-interface {v1, p2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v1

    xor-int/2addr v1, v3

    if-eqz v1, :cond_2

    const/16 v1, 0x91

    .line 393
    aget-object v1, p1, v1

    const-string v2, "defaultValue"

    invoke-interface {v1, p2, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_2
    const/16 v1, 0x92

    .line 395
    aget-object v1, p1, v1

    const/16 v2, 0x93

    aget-object p1, p1, v2

    new-instance v2, Lgroovy/cli/internal/CliBuilderInternal$_commons2picocli_closure4;

    invoke-direct {v2, p0, p0, v0}, Lgroovy/cli/internal/CliBuilderInternal$_commons2picocli_closure4;-><init>(Ljava/lang/Object;Ljava/lang/Object;Lgroovy/lang/Reference;)V

    invoke-interface {p1, p2, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    invoke-interface {v1, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    const-class p2, Ljava/util/Map;

    invoke-static {p1, p2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->asType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/util/Map;

    .line 418
    const-class p2, Ljava/util/Map;

    invoke-static {p1, p2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/util/Map;

    return-object p1
.end method

.method private create(Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;)Lgroovy/cli/TypedOption;
    .locals 8

    invoke-static {}, Lgroovy/cli/internal/CliBuilderInternal;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v0

    const/16 v1, 0x40

    .line 271
    aget-object v1, v0, v1

    const/16 v2, 0x41

    aget-object v2, v0, v2

    const/16 v3, 0x42

    aget-object v3, v0, v3

    invoke-interface {v3, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    new-instance v4, Lgroovy/cli/internal/CliBuilderInternal$_create_closure1;

    invoke-direct {v4, p0, p0}, Lgroovy/cli/internal/CliBuilderInternal$_create_closure1;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    invoke-interface {v2, v3, v4}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-interface {v1, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v1

    move-object v2, v1

    check-cast v2, Ljava/lang/String;

    const/16 v2, 0x43

    .line 272
    aget-object v2, v0, v2

    invoke-interface {v2, v1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callSafe(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    const/4 v3, 0x2

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v3

    invoke-static {v2, v3}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    const/4 v4, 0x0

    if-eqz v2, :cond_0

    const/16 v2, 0x44

    aget-object v2, v0, v2

    const/4 v5, 0x1

    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v5

    invoke-interface {v2, v1, v5}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    goto :goto_0

    :cond_0
    move-object v1, v4

    :goto_0
    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v1

    move-object v2, v1

    check-cast v2, Ljava/lang/String;

    const/16 v2, 0x45

    .line 274
    aget-object v2, v0, v2

    const/16 v5, 0x46

    aget-object v5, v0, v5

    const/16 v6, 0x47

    aget-object v6, v0, v6

    invoke-interface {v6, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    new-instance v7, Lgroovy/cli/internal/CliBuilderInternal$_create_closure2;

    invoke-direct {v7, p0, p0}, Lgroovy/cli/internal/CliBuilderInternal$_create_closure2;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    invoke-interface {v5, v6, v7}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    invoke-interface {v2, v5}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v2

    move-object v5, v2

    check-cast v5, Ljava/lang/String;

    const/16 v5, 0x48

    .line 275
    aget-object v5, v0, v5

    const-string v6, "--"

    invoke-interface {v5, v2, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callSafe(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    invoke-static {v5}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v5

    if-eqz v5, :cond_1

    const/16 v5, 0x49

    aget-object v5, v0, v5

    invoke-interface {v5, v2, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    goto :goto_1

    :cond_1
    move-object v2, v4

    :goto_1
    invoke-static {v2}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v2

    move-object v3, v2

    check-cast v3, Ljava/lang/String;

    const/16 v3, 0x4a

    .line 277
    aget-object v3, v0, v3

    const-class v5, Lgroovy/cli/TypedOption;

    invoke-interface {v3, v5}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    const-class v5, Ljava/util/Map;

    invoke-static {v3, v5}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/util/Map;

    .line 278
    invoke-static {v1, v4}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareNotEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v4

    if-eqz v4, :cond_2

    const/16 v4, 0x4b

    aget-object v4, v0, v4

    const-string v5, "opt"

    invoke-interface {v4, v3, v5, v1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_2
    const/16 v4, 0x4c

    .line 279
    aget-object v4, v0, v4

    const-string v5, "longOpt"

    invoke-interface {v4, v3, v5, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v4, 0x4d

    .line 280
    aget-object v4, v0, v4

    const-string v5, "cliOption"

    invoke-interface {v4, v3, v5, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 281
    invoke-static {p3}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_3

    const/16 p1, 0x4e

    .line 282
    aget-object p1, v0, p1

    const-string v4, "defaultValue"

    invoke-interface {p1, v3, v4, p3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 284
    :cond_3
    invoke-static {p4}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result p1

    const-string p3, "type"

    if-eqz p1, :cond_6

    .line 285
    invoke-static {p2}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result p1

    if-nez p1, :cond_5

    const/16 p1, 0x50

    .line 288
    aget-object p1, v0, p1

    const-string p2, "convert"

    invoke-interface {p1, v3, p2, p4}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 p1, 0x51

    .line 289
    aget-object p1, v0, p1

    instance-of p2, p4, Ljava/lang/Class;

    if-eqz p2, :cond_4

    goto :goto_2

    :cond_4
    const/16 p2, 0x52

    aget-object p2, v0, p2

    invoke-interface {p2, p4}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p4

    :goto_2
    invoke-interface {p1, v3, p3, p4}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_3

    :cond_5
    const/16 p1, 0x4f

    .line 286
    aget-object p1, v0, p1

    const-class p2, Lgroovy/cli/CliBuilderException;

    const-string p3, "You can\'t specify \'type\' when using \'convert\'"

    invoke-interface {p1, p2, p3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/Throwable;

    throw p1

    :cond_6
    const/16 p1, 0x53

    .line 291
    aget-object p1, v0, p1

    invoke-interface {p1, v3, p3, p2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :goto_3
    const/16 p1, 0x54

    .line 293
    aget-object p1, v0, p1

    iget-object p2, p0, Lgroovy/cli/internal/CliBuilderInternal;->savedTypeOptions:Ljava/util/Map;

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result p3

    if-eqz p3, :cond_7

    move-object v1, v2

    :cond_7
    invoke-interface {p1, p2, v1, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 294
    const-class p1, Lgroovy/cli/TypedOption;

    invoke-static {v3, p1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovy/cli/TypedOption;

    return-object p1
.end method

.method private createCommandLine()Lgroovyjarjarpicocli/CommandLine;
    .locals 10

    invoke-static {}, Lgroovy/cli/internal/CliBuilderInternal;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v0

    const/16 v1, 0x5d

    .line 315
    aget-object v1, v0, v1

    iget-object v2, p0, Lgroovy/cli/internal/CliBuilderInternal;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    iget-object v3, p0, Lgroovy/cli/internal/CliBuilderInternal;->parser:Lgroovyjarjarpicocli/CommandLine$Model$ParserSpec;

    invoke-interface {v1, v2, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v1, 0x5e

    .line 316
    aget-object v1, v0, v1

    const/16 v2, 0x5f

    aget-object v2, v0, v2

    iget-object v3, p0, Lgroovy/cli/internal/CliBuilderInternal;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    iget-object v4, p0, Lgroovy/cli/internal/CliBuilderInternal;->name:Ljava/lang/String;

    invoke-interface {v2, v3, v4}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    iget-object v3, p0, Lgroovy/cli/internal/CliBuilderInternal;->usageMessage:Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;

    invoke-interface {v1, v2, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v1, 0x60

    .line 317
    aget-object v1, v0, v1

    const/16 v2, 0x61

    aget-object v2, v0, v2

    iget-object v3, p0, Lgroovy/cli/internal/CliBuilderInternal;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-interface {v2, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-interface {v1, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_0

    const/16 v1, 0x62

    .line 318
    aget-object v1, v0, v1

    iget-object v2, p0, Lgroovy/cli/internal/CliBuilderInternal;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    const/16 v3, 0x63

    aget-object v3, v0, v3

    const/16 v4, 0x64

    aget-object v4, v0, v4

    const/16 v5, 0x65

    aget-object v5, v0, v5

    const/16 v6, 0x66

    aget-object v6, v0, v6

    const/16 v7, 0x67

    aget-object v7, v0, v7

    const/16 v8, 0x68

    aget-object v8, v0, v8

    const-class v9, Lgroovyjarjarpicocli/CommandLine$Model$PositionalParamSpec;

    invoke-interface {v8, v9}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v8

    const-class v9, [Ljava/lang/String;

    invoke-interface {v7, v8, v9}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v7

    const-string v8, "*"

    invoke-interface {v6, v7, v8}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    const-string v7, "P"

    invoke-interface {v5, v6, v7}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    const/4 v6, 0x1

    invoke-static {v6}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v6

    invoke-interface {v4, v5, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    invoke-interface {v3, v4}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    invoke-interface {v1, v2, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_0
    const/16 v1, 0x69

    .line 320
    aget-object v0, v0, v1

    const-class v1, Lgroovyjarjarpicocli/CommandLine;

    iget-object v2, p0, Lgroovy/cli/internal/CliBuilderInternal;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-interface {v0, v1, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    const-class v1, Lgroovyjarjarpicocli/CommandLine;

    invoke-static {v0, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarpicocli/CommandLine;

    return-object v0
.end method

.method private printUsage(Lgroovyjarjarpicocli/CommandLine;Ljava/io/PrintWriter;)V
    .locals 2

    invoke-static {}, Lgroovy/cli/internal/CliBuilderInternal;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v0

    const/16 v1, 0x6f

    .line 332
    aget-object v1, v0, v1

    invoke-interface {v1, p1, p2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 p1, 0x70

    .line 333
    aget-object p1, v0, p1

    invoke-interface {p1, p2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method


# virtual methods
.method protected synthetic $getStaticMetaClass()Lgroovy/lang/MetaClass;
    .locals 2

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    const-class v1, Lgroovy/cli/internal/CliBuilderInternal;

    if-eq v0, v1, :cond_0

    invoke-static {p0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->initMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0

    :cond_0
    sget-object v0, Lgroovy/cli/internal/CliBuilderInternal;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    if-nez v0, :cond_1

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getClassInfo(Ljava/lang/Class;)Lorg/codehaus/groovy/reflection/ClassInfo;

    move-result-object v0

    sput-object v0, Lgroovy/cli/internal/CliBuilderInternal;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    :cond_1
    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0
.end method

.method public getAcceptLongOptionsWithSingleHyphen()Z
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-boolean v0, p0, Lgroovy/cli/internal/CliBuilderInternal;->acceptLongOptionsWithSingleHyphen:Z

    return v0
.end method

.method public getErrorWriter()Ljava/io/PrintWriter;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-object v0, p0, Lgroovy/cli/internal/CliBuilderInternal;->errorWriter:Ljava/io/PrintWriter;

    return-object v0
.end method

.method public getExpandArgumentFiles()Z
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-boolean v0, p0, Lgroovy/cli/internal/CliBuilderInternal;->expandArgumentFiles:Z

    return v0
.end method

.method public getFooter()Ljava/lang/String;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-object v0, p0, Lgroovy/cli/internal/CliBuilderInternal;->footer:Ljava/lang/String;

    return-object v0
.end method

.method public getHeader()Ljava/lang/String;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-object v0, p0, Lgroovy/cli/internal/CliBuilderInternal;->header:Ljava/lang/String;

    return-object v0
.end method

.method public getMetaClass()Lgroovy/lang/MetaClass;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    .annotation runtime Lgroovy/transform/Internal;
    .end annotation

    .annotation runtime Lgroovyjarjaropenbeans/Transient;
    .end annotation

    iget-object v0, p0, Lgroovy/cli/internal/CliBuilderInternal;->metaClass:Lgroovy/lang/MetaClass;

    if-eqz v0, :cond_0

    return-object v0

    :cond_0
    invoke-virtual {p0}, Lgroovy/cli/internal/CliBuilderInternal;->$getStaticMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    iput-object v0, p0, Lgroovy/cli/internal/CliBuilderInternal;->metaClass:Lgroovy/lang/MetaClass;

    return-object v0
.end method

.method public getName()Ljava/lang/String;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-object v0, p0, Lgroovy/cli/internal/CliBuilderInternal;->name:Ljava/lang/String;

    return-object v0
.end method

.method public final getParser()Lgroovyjarjarpicocli/CommandLine$Model$ParserSpec;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-object v0, p0, Lgroovy/cli/internal/CliBuilderInternal;->parser:Lgroovyjarjarpicocli/CommandLine$Model$ParserSpec;

    return-object v0
.end method

.method public getPosix()Ljava/lang/Boolean;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-object v0, p0, Lgroovy/cli/internal/CliBuilderInternal;->posix:Ljava/lang/Boolean;

    return-object v0
.end method

.method public getSavedTypeOptions()Ljava/util/Map;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Lgroovy/cli/TypedOption;",
            ">;"
        }
    .end annotation

    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-object v0, p0, Lgroovy/cli/internal/CliBuilderInternal;->savedTypeOptions:Ljava/util/Map;

    return-object v0
.end method

.method public getStopAtNonOption()Z
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-boolean v0, p0, Lgroovy/cli/internal/CliBuilderInternal;->stopAtNonOption:Z

    return v0
.end method

.method public getUsage()Ljava/lang/String;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-object v0, p0, Lgroovy/cli/internal/CliBuilderInternal;->usage:Ljava/lang/String;

    return-object v0
.end method

.method public final getUsageMessage()Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-object v0, p0, Lgroovy/cli/internal/CliBuilderInternal;->usageMessage:Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;

    return-object v0
.end method

.method public getWidth()I
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget v0, p0, Lgroovy/cli/internal/CliBuilderInternal;->width:I

    return v0
.end method

.method public getWriter()Ljava/io/PrintWriter;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-object v0, p0, Lgroovy/cli/internal/CliBuilderInternal;->writer:Ljava/io/PrintWriter;

    return-object v0
.end method

.method public invokeMethod(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 10

    invoke-static {}, Lgroovy/cli/internal/CliBuilderInternal;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v0

    .line 246
    instance-of v1, p2, [Ljava/lang/Object;

    if-eqz v1, :cond_11

    .line 247
    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->isOrigInt()Z

    move-result v1

    const/4 v2, 0x1

    const/4 v3, 0x0

    if-eqz v1, :cond_3

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->isOrigZ()Z

    move-result v1

    if-eqz v1, :cond_3

    sget-boolean v1, Lgroovy/cli/internal/CliBuilderInternal;->__$stMC:Z

    if-nez v1, :cond_3

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->disabledStandardMetaClass()Z

    move-result v1

    if-nez v1, :cond_3

    const/16 v1, 0x20

    aget-object v1, v0, v1

    invoke-interface {v1, p2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v4

    invoke-static {v1, v4}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_2

    const/16 v1, 0x21

    aget-object v1, v0, v1

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v4

    invoke-interface {v1, p2, v4}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    instance-of v1, v1, Ljava/lang/String;

    if-nez v1, :cond_1

    const/16 v1, 0x22

    aget-object v1, v0, v1

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v4

    invoke-interface {v1, p2, v4}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    instance-of v1, v1, Lgroovy/lang/GString;

    if-eqz v1, :cond_0

    goto :goto_0

    :cond_0
    move v1, v3

    goto :goto_1

    :cond_1
    :goto_0
    move v1, v2

    :goto_1
    if-eqz v1, :cond_2

    move v1, v2

    goto :goto_2

    :cond_2
    move v1, v3

    :goto_2
    if-eqz v1, :cond_7

    const/16 v1, 0x23

    .line 248
    aget-object v1, v0, v1

    new-array v2, v3, [Ljava/lang/Object;

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createMap([Ljava/lang/Object;)Ljava/util/Map;

    move-result-object v2

    const/16 v4, 0x24

    aget-object v4, v0, v4

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v3

    invoke-interface {v4, p2, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p2

    invoke-interface {v1, p0, p1, v2, p2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callCurrent(Lgroovy/lang/GroovyObject;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    const/16 p1, 0x25

    .line 249
    aget-object p1, v0, p1

    iget-object p2, p0, Lgroovy/cli/internal/CliBuilderInternal;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-interface {p1, p2, v5}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 p1, 0x26

    .line 250
    aget-object v3, v0, p1

    const/4 v6, 0x0

    const/4 v7, 0x0

    const/4 v8, 0x0

    move-object v4, p0

    invoke-interface/range {v3 .. v8}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callCurrent(Lgroovy/lang/GroovyObject;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1

    :cond_3
    const/16 v1, 0x19

    .line 247
    aget-object v1, v0, v1

    invoke-interface {v1, p2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v4

    invoke-static {v1, v4}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_6

    const/16 v1, 0x1a

    aget-object v1, v0, v1

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v4

    invoke-interface {v1, p2, v4}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    instance-of v1, v1, Ljava/lang/String;

    if-nez v1, :cond_5

    const/16 v1, 0x1b

    aget-object v1, v0, v1

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v4

    invoke-interface {v1, p2, v4}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    instance-of v1, v1, Lgroovy/lang/GString;

    if-eqz v1, :cond_4

    goto :goto_3

    :cond_4
    move v1, v3

    goto :goto_4

    :cond_5
    :goto_3
    move v1, v2

    :goto_4
    if-eqz v1, :cond_6

    move v1, v2

    goto :goto_5

    :cond_6
    move v1, v3

    :goto_5
    if-eqz v1, :cond_7

    const/16 v1, 0x1c

    .line 248
    aget-object v1, v0, v1

    new-array v2, v3, [Ljava/lang/Object;

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createMap([Ljava/lang/Object;)Ljava/util/Map;

    move-result-object v2

    const/16 v4, 0x1d

    aget-object v4, v0, v4

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v3

    invoke-interface {v4, p2, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p2

    invoke-interface {v1, p0, p1, v2, p2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callCurrent(Lgroovy/lang/GroovyObject;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    const/16 p1, 0x1e

    .line 249
    aget-object p1, v0, p1

    iget-object p2, p0, Lgroovy/cli/internal/CliBuilderInternal;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-interface {p1, p2, v5}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 p1, 0x1f

    .line 250
    aget-object v3, v0, p1

    const/4 v6, 0x0

    const/4 v7, 0x0

    const/4 v8, 0x0

    move-object v4, p0

    invoke-interface/range {v3 .. v8}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callCurrent(Lgroovy/lang/GroovyObject;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1

    .line 252
    :cond_7
    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->isOrigInt()Z

    move-result v1

    const-string v4, "leftShift"

    if-eqz v1, :cond_a

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->isOrigZ()Z

    move-result v1

    if-eqz v1, :cond_a

    sget-boolean v1, Lgroovy/cli/internal/CliBuilderInternal;->__$stMC:Z

    if-nez v1, :cond_a

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->disabledStandardMetaClass()Z

    move-result v1

    if-nez v1, :cond_a

    const/16 v1, 0x2c

    aget-object v1, v0, v1

    invoke-interface {v1, p2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v5

    invoke-static {v1, v5}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_8

    const/16 v1, 0x2d

    aget-object v1, v0, v1

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v5

    invoke-interface {v1, p2, v5}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    instance-of v1, v1, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;

    if-eqz v1, :cond_8

    move v1, v2

    goto :goto_6

    :cond_8
    move v1, v3

    :goto_6
    if-eqz v1, :cond_9

    invoke-static {p1, v4}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_9

    move v1, v2

    goto :goto_7

    :cond_9
    move v1, v3

    :goto_7
    if-eqz v1, :cond_d

    const/16 p1, 0x2e

    .line 253
    aget-object p1, v0, p1

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    invoke-interface {p1, p2, v1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    const-class p2, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;

    invoke-static {p1, p2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->asType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    move-object v3, p1

    check-cast v3, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;

    const/16 p1, 0x2f

    .line 254
    aget-object p1, v0, p1

    iget-object p2, p0, Lgroovy/cli/internal/CliBuilderInternal;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-interface {p1, p2, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 p1, 0x30

    .line 255
    aget-object v1, v0, p1

    const/4 v4, 0x0

    const/4 v5, 0x0

    const/4 v6, 0x0

    move-object v2, p0

    invoke-interface/range {v1 .. v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callCurrent(Lgroovy/lang/GroovyObject;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1

    :cond_a
    const/16 v1, 0x27

    .line 252
    aget-object v1, v0, v1

    invoke-interface {v1, p2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v5

    invoke-static {v1, v5}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_b

    const/16 v1, 0x28

    aget-object v1, v0, v1

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v5

    invoke-interface {v1, p2, v5}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    instance-of v1, v1, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;

    if-eqz v1, :cond_b

    move v1, v2

    goto :goto_8

    :cond_b
    move v1, v3

    :goto_8
    if-eqz v1, :cond_c

    invoke-static {p1, v4}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_c

    move v1, v2

    goto :goto_9

    :cond_c
    move v1, v3

    :goto_9
    if-eqz v1, :cond_d

    const/16 p1, 0x29

    .line 253
    aget-object p1, v0, p1

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    invoke-interface {p1, p2, v1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    const-class p2, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;

    invoke-static {p1, p2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->asType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    move-object v3, p1

    check-cast v3, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;

    const/16 p1, 0x2a

    .line 254
    aget-object p1, v0, p1

    iget-object p2, p0, Lgroovy/cli/internal/CliBuilderInternal;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-interface {p1, p2, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 p1, 0x2b

    .line 255
    aget-object v1, v0, p1

    const/4 v4, 0x0

    const/4 v5, 0x0

    const/4 v6, 0x0

    move-object v2, p0

    invoke-interface/range {v1 .. v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callCurrent(Lgroovy/lang/GroovyObject;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1

    :cond_d
    const/16 v1, 0x31

    .line 257
    aget-object v1, v0, v1

    invoke-interface {v1, p2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    const/4 v4, 0x2

    invoke-static {v4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v4

    invoke-static {v1, v4}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_e

    const/16 v1, 0x32

    aget-object v1, v0, v1

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v4

    invoke-interface {v1, p2, v4}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    instance-of v1, v1, Ljava/util/Map;

    if-eqz v1, :cond_e

    move v1, v2

    goto :goto_a

    :cond_e
    move v1, v3

    :goto_a
    if-eqz v1, :cond_11

    const/16 v1, 0x33

    .line 258
    aget-object v1, v0, v1

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v4

    invoke-interface {v1, p2, v4}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    const-class v4, Ljava/util/Map;

    invoke-static {v1, v4}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->asType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/util/Map;

    const/16 v4, 0x34

    .line 259
    aget-object v4, v0, v4

    invoke-interface {v4, v1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    invoke-static {v4}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v4

    if-eqz v4, :cond_f

    const/16 v4, 0x35

    aget-object v4, v0, v4

    invoke-interface {v4, v1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    instance-of v4, v4, Ljava/lang/Class;

    xor-int/2addr v4, v2

    if-eqz v4, :cond_f

    move v3, v2

    :cond_f
    if-nez v3, :cond_10

    const/16 v3, 0x37

    .line 262
    aget-object v3, v0, v3

    const/16 v4, 0x38

    aget-object v4, v0, v4

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    invoke-interface {v4, p2, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p2

    invoke-interface {v3, p0, p1, v1, p2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callCurrent(Lgroovy/lang/GroovyObject;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    const/16 p1, 0x39

    .line 263
    aget-object p1, v0, p1

    iget-object p2, p0, Lgroovy/cli/internal/CliBuilderInternal;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-interface {p1, p2, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 p1, 0x3a

    .line 264
    aget-object v4, v0, p1

    const/16 p1, 0x3b

    aget-object p1, v0, p1

    invoke-interface {p1, v1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v7

    const/16 p1, 0x3c

    aget-object p1, v0, p1

    invoke-interface {p1, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v8

    const/16 p1, 0x3d

    aget-object p1, v0, p1

    invoke-interface {p1, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v9

    move-object v5, p0

    invoke-interface/range {v4 .. v9}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callCurrent(Lgroovy/lang/GroovyObject;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1

    :cond_10
    const/16 p1, 0x36

    .line 260
    aget-object p1, v0, p1

    const-class p2, Lgroovy/cli/CliBuilderException;

    const-string v0, "\'type\' must be a Class"

    invoke-interface {p1, p2, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/Throwable;

    throw p1

    :cond_11
    const/16 v1, 0x3e

    .line 267
    aget-object v1, v0, v1

    const/16 v2, 0x3f

    aget-object v0, v0, v2

    const-class v2, Lorg/codehaus/groovy/runtime/InvokerHelper;

    invoke-interface {v0, v2, p0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    invoke-interface {v1, v0, p0, p1, p2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public isAcceptLongOptionsWithSingleHyphen()Z
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-boolean v0, p0, Lgroovy/cli/internal/CliBuilderInternal;->acceptLongOptionsWithSingleHyphen:Z

    return v0
.end method

.method public isExpandArgumentFiles()Z
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-boolean v0, p0, Lgroovy/cli/internal/CliBuilderInternal;->expandArgumentFiles:Z

    return v0
.end method

.method public isPosix()Ljava/lang/Boolean;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-object v0, p0, Lgroovy/cli/internal/CliBuilderInternal;->posix:Ljava/lang/Boolean;

    return-object v0
.end method

.method public isStopAtNonOption()Z
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-boolean v0, p0, Lgroovy/cli/internal/CliBuilderInternal;->stopAtNonOption:Z

    return v0
.end method

.method public option(Ljava/util/Map;Ljava/lang/Class;Ljava/lang/String;)Lgroovy/cli/TypedOption;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Ljava/util/Map;",
            "Ljava/lang/Class<",
            "TT;>;",
            "Ljava/lang/String;",
            ")",
            "Lgroovy/cli/TypedOption<",
            "TT;>;"
        }
    .end annotation

    invoke-static {}, Lgroovy/cli/internal/CliBuilderInternal;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v0

    const/16 v1, 0x17

    .line 236
    aget-object v1, v0, v1

    invoke-interface {v1, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_0

    goto :goto_0

    :cond_0
    const-string v1, "_"

    :goto_0
    const/4 v2, 0x0

    const-string v3, "type"

    .line 237
    move-object v4, v3

    check-cast v4, Ljava/lang/String;

    invoke-static {p2, v2, p1, v3}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->setProperty(Ljava/lang/Object;Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;)V

    const/16 p2, 0x18

    .line 238
    aget-object p2, v0, p2

    const-string v0, "opt"

    invoke-interface {p2, p1, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 239
    const-class p2, Lgroovy/cli/internal/CliBuilderInternal;

    new-instance v0, Lorg/codehaus/groovy/runtime/GStringImpl;

    const/4 v2, 0x1

    new-array v3, v2, [Ljava/lang/Object;

    const/4 v4, 0x0

    aput-object v1, v3, v4

    const-string v1, ""

    filled-new-array {v1, v1}, [Ljava/lang/String;

    move-result-object v1

    invoke-direct {v0, v3, v1}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v0}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v0

    move-object v1, v0

    check-cast v1, Ljava/lang/String;

    const/4 v1, 0x2

    new-array v1, v1, [Ljava/lang/Object;

    aput-object p1, v1, v4

    aput-object p3, v1, v2

    invoke-static {p2, p0, v0, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodOnCurrentN(Ljava/lang/Class;Lgroovy/lang/GroovyObject;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    const-class p2, Lgroovy/cli/TypedOption;

    invoke-static {p1, p2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovy/cli/TypedOption;

    return-object p1
.end method

.method public option(Ljava/lang/Object;Ljava/util/Map;Ljava/lang/Object;)Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;
    .locals 17

    move-object/from16 v0, p0

    move-object/from16 v1, p1

    move-object/from16 v2, p2

    move-object/from16 v3, p3

    invoke-static {}, Lgroovy/cli/internal/CliBuilderInternal;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v4

    .line 351
    new-instance v5, Lgroovy/lang/Reference;

    const/4 v6, 0x0

    invoke-direct {v5, v6}, Lgroovy/lang/Reference;-><init>(Ljava/lang/Object;)V

    invoke-virtual {v5}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec$Builder;

    const-string v6, "_"

    .line 352
    invoke-static {v1, v6}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v6

    const-string v7, "-"

    const-string v8, ""

    const/4 v9, 0x0

    .line 368
    invoke-static {v9}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v10

    const/4 v11, 0x1

    if-eqz v6, :cond_1

    const/16 v6, 0x71

    .line 353
    aget-object v6, v4, v6

    const/16 v12, 0x72

    aget-object v12, v4, v12

    const-class v13, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;

    new-instance v14, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v15, v11, [Ljava/lang/Object;

    const/16 v16, 0x73

    aget-object v11, v4, v16

    invoke-interface {v11, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v11

    aput-object v11, v15, v9

    const-string v11, "--"

    filled-new-array {v11, v8}, [Ljava/lang/String;

    move-result-object v9

    invoke-direct {v14, v15, v9}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-interface {v12, v13, v14}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v9

    invoke-interface {v6, v9, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    const-class v6, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec$Builder;

    invoke-static {v3, v6}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec$Builder;

    move-object v6, v5

    check-cast v6, Lgroovy/lang/Reference;

    invoke-virtual {v5, v3}, Lgroovy/lang/Reference;->set(Ljava/lang/Object;)V

    .line 354
    iget-boolean v3, v0, Lgroovy/cli/internal/CliBuilderInternal;->acceptLongOptionsWithSingleHyphen:Z

    if-eqz v3, :cond_0

    const/16 v3, 0x74

    .line 355
    aget-object v3, v4, v3

    invoke-virtual {v5}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec$Builder;

    new-instance v9, Lorg/codehaus/groovy/runtime/GStringImpl;

    const/4 v12, 0x1

    new-array v13, v12, [Ljava/lang/Object;

    const/16 v14, 0x75

    aget-object v14, v4, v14

    invoke-interface {v14, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v14

    const/4 v15, 0x0

    aput-object v14, v13, v15

    filled-new-array {v7, v8}, [Ljava/lang/String;

    move-result-object v7

    invoke-direct {v9, v13, v7}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    new-instance v7, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v13, v12, [Ljava/lang/Object;

    const/16 v12, 0x76

    aget-object v12, v4, v12

    invoke-interface {v12, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v12

    aput-object v12, v13, v15

    filled-new-array {v11, v8}, [Ljava/lang/String;

    move-result-object v8

    invoke-direct {v7, v13, v8}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-interface {v3, v6, v9, v7}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_0
    const/16 v3, 0x77

    .line 357
    aget-object v3, v4, v3

    const-string v6, "longOpt"

    invoke-interface {v3, v2, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/4 v13, 0x0

    goto :goto_0

    :cond_1
    const/16 v6, 0x78

    .line 359
    aget-object v6, v4, v6

    const/16 v9, 0x79

    aget-object v9, v4, v9

    const-class v11, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;

    new-instance v12, Lorg/codehaus/groovy/runtime/GStringImpl;

    const/4 v13, 0x1

    new-array v14, v13, [Ljava/lang/Object;

    const/4 v13, 0x0

    aput-object v1, v14, v13

    filled-new-array {v7, v8}, [Ljava/lang/String;

    move-result-object v7

    invoke-direct {v12, v14, v7}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-interface {v9, v11, v12}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v7

    invoke-interface {v6, v7, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    const-class v6, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec$Builder;

    invoke-static {v3, v6}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec$Builder;

    move-object v6, v5

    check-cast v6, Lgroovy/lang/Reference;

    invoke-virtual {v5, v3}, Lgroovy/lang/Reference;->set(Ljava/lang/Object;)V

    :goto_0
    const/16 v3, 0x7a

    .line 361
    aget-object v3, v4, v3

    const/16 v6, 0x7b

    aget-object v6, v4, v6

    invoke-interface {v6, v0, v1, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callCurrent(Lgroovy/lang/GroovyObject;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    new-instance v6, Lgroovy/cli/internal/CliBuilderInternal$_option_closure3;

    invoke-direct {v6, v0, v0, v5}, Lgroovy/cli/internal/CliBuilderInternal$_option_closure3;-><init>(Ljava/lang/Object;Ljava/lang/Object;Lgroovy/lang/Reference;)V

    invoke-interface {v3, v1, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 368
    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->isOrigInt()Z

    move-result v1

    const-string v3, "1"

    if-eqz v1, :cond_5

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->isOrigZ()Z

    move-result v1

    if-eqz v1, :cond_5

    sget-boolean v1, Lgroovy/cli/internal/CliBuilderInternal;->__$stMC:Z

    if-nez v1, :cond_5

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->disabledStandardMetaClass()Z

    move-result v1

    if-nez v1, :cond_5

    const/16 v1, 0x83

    aget-object v1, v4, v1

    invoke-virtual {v5}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec$Builder;

    invoke-interface {v1, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v1

    const/4 v6, 0x1

    xor-int/2addr v1, v6

    if-eqz v1, :cond_2

    const/16 v1, 0x84

    aget-object v1, v4, v1

    invoke-virtual {v5}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v7

    check-cast v7, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec$Builder;

    invoke-interface {v1, v7}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v1

    xor-int/2addr v1, v6

    if-eqz v1, :cond_2

    const/4 v12, 0x1

    goto :goto_1

    :cond_2
    move v12, v13

    :goto_1
    if-eqz v12, :cond_3

    const/16 v1, 0x85

    aget-object v1, v4, v1

    const/16 v6, 0x86

    aget-object v6, v4, v6

    invoke-virtual {v5}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v7

    check-cast v7, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec$Builder;

    invoke-interface {v6, v7}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    invoke-interface {v1, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetPropertySafe(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-static {v1, v10}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareGreaterThan(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_3

    const/4 v9, 0x1

    goto :goto_2

    :cond_3
    move v9, v13

    :goto_2
    if-eqz v9, :cond_8

    const/16 v1, 0x87

    .line 369
    aget-object v1, v4, v1

    const/16 v6, 0x88

    aget-object v6, v4, v6

    invoke-virtual {v5}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v7

    check-cast v7, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec$Builder;

    invoke-interface {v6, v7, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    const/16 v6, 0x89

    aget-object v6, v4, v6

    invoke-interface {v6, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_4

    :goto_3
    const-class v2, Ljava/lang/Object;

    goto :goto_4

    :cond_4
    const-class v2, [Ljava/lang/String;

    :goto_4
    invoke-interface {v1, v3, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto/16 :goto_7

    :cond_5
    const/16 v1, 0x7c

    .line 368
    aget-object v1, v4, v1

    invoke-virtual {v5}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec$Builder;

    invoke-interface {v1, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v1

    const/4 v12, 0x1

    xor-int/2addr v1, v12

    if-eqz v1, :cond_6

    const/16 v1, 0x7d

    aget-object v1, v4, v1

    invoke-virtual {v5}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec$Builder;

    invoke-interface {v1, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v1

    xor-int/2addr v1, v12

    if-eqz v1, :cond_6

    move v1, v12

    goto :goto_5

    :cond_6
    move v1, v13

    :goto_5
    if-eqz v1, :cond_7

    const/16 v1, 0x7e

    aget-object v1, v4, v1

    const/16 v6, 0x7f

    aget-object v6, v4, v6

    invoke-virtual {v5}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v7

    check-cast v7, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec$Builder;

    invoke-interface {v6, v7}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    invoke-interface {v1, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetPropertySafe(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-static {v1, v10}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareGreaterThan(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_7

    move v9, v12

    goto :goto_6

    :cond_7
    move v9, v13

    :goto_6
    if-eqz v9, :cond_8

    const/16 v1, 0x80

    .line 369
    aget-object v1, v4, v1

    const/16 v6, 0x81

    aget-object v6, v4, v6

    invoke-virtual {v5}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v7

    check-cast v7, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec$Builder;

    invoke-interface {v6, v7, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    const/16 v6, 0x82

    aget-object v6, v4, v6

    invoke-interface {v6, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_4

    goto :goto_3

    :cond_8
    :goto_7
    const/16 v1, 0x8a

    .line 371
    aget-object v1, v4, v1

    invoke-virtual {v5}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec$Builder;

    invoke-interface {v1, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    const-class v2, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;

    invoke-static {v1, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;

    return-object v1
.end method

.method public parse(Ljava/lang/Object;)Lgroovy/cli/internal/OptionAccessor;
    .locals 7

    invoke-static {}, Lgroovy/cli/internal/CliBuilderInternal;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v0

    .line 302
    sget-boolean v1, Lgroovy/cli/internal/CliBuilderInternal;->__$stMC:Z

    if-nez v1, :cond_0

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->disabledStandardMetaClass()Z

    move-result v1

    if-nez v1, :cond_0

    invoke-direct {p0}, Lgroovy/cli/internal/CliBuilderInternal;->createCommandLine()Lgroovyjarjarpicocli/CommandLine;

    move-result-object v1

    goto :goto_0

    :cond_0
    const/16 v1, 0x55

    aget-object v1, v0, v1

    invoke-interface {v1, p0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callCurrent(Lgroovy/lang/GroovyObject;)Ljava/lang/Object;

    move-result-object v1

    const-class v2, Lgroovyjarjarpicocli/CommandLine;

    invoke-static {v1, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarpicocli/CommandLine;

    :goto_0
    const/16 v2, 0x56

    const/4 v3, 0x0

    .line 304
    :try_start_0
    aget-object v2, v0, v2

    const-class v4, Lgroovy/cli/internal/OptionAccessor;

    const/16 v5, 0x57

    aget-object v5, v0, v5

    const-class v6, [Ljava/lang/String;

    invoke-static {p1, v6}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->asType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, [Ljava/lang/String;

    const-class v6, [Ljava/lang/String;

    invoke-static {p1, v6}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createPojoWrapper(Ljava/lang/Object;Ljava/lang/Class;)Lorg/codehaus/groovy/runtime/wrappers/Wrapper;

    move-result-object p1

    invoke-interface {v5, v1, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    invoke-interface {v2, v4, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    .line 305
    iget-object v1, p0, Lgroovy/cli/internal/CliBuilderInternal;->savedTypeOptions:Ljava/util/Map;

    const-string v2, "savedTypeOptions"

    move-object v4, v2

    check-cast v4, Ljava/lang/String;

    invoke-static {v1, v3, p1, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->setProperty(Ljava/lang/Object;Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;)V

    .line 306
    const-class v1, Lgroovy/cli/internal/OptionAccessor;

    invoke-static {p1, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovy/cli/internal/OptionAccessor;
    :try_end_0
    .catch Lgroovyjarjarpicocli/CommandLine$ParameterException; {:try_start_0 .. :try_end_0} :catch_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    return-object p1

    :catchall_0
    move-exception p1

    goto :goto_1

    :catch_0
    move-exception p1

    const/16 v1, 0x58

    .line 308
    :try_start_1
    aget-object v1, v0, v1

    iget-object v2, p0, Lgroovy/cli/internal/CliBuilderInternal;->errorWriter:Ljava/io/PrintWriter;

    const/16 v4, 0x59

    aget-object v4, v0, v4

    const-string v5, "error: "

    const/16 v6, 0x5a

    aget-object v6, v0, v6

    invoke-interface {v6, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    invoke-interface {v4, v5, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    invoke-interface {v1, v2, v4}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v1, 0x5b

    .line 309
    aget-object v1, v0, v1

    const/16 v2, 0x5c

    aget-object v0, v0, v2

    invoke-interface {v0, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    iget-object v0, p0, Lgroovy/cli/internal/CliBuilderInternal;->errorWriter:Ljava/io/PrintWriter;

    invoke-interface {v1, p0, p1, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callCurrent(Lgroovy/lang/GroovyObject;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 310
    const-class p1, Lgroovy/cli/internal/OptionAccessor;

    invoke-static {v3, p1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovy/cli/internal/OptionAccessor;
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    return-object p1

    .line 311
    :goto_1
    throw p1
.end method

.method public setAcceptLongOptionsWithSingleHyphen(Z)V
    .locals 0
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iput-boolean p1, p0, Lgroovy/cli/internal/CliBuilderInternal;->acceptLongOptionsWithSingleHyphen:Z

    return-void
.end method

.method public setErrorWriter(Ljava/io/PrintWriter;)V
    .locals 0
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iput-object p1, p0, Lgroovy/cli/internal/CliBuilderInternal;->errorWriter:Ljava/io/PrintWriter;

    return-void
.end method

.method public setExpandArgumentFiles(Z)V
    .locals 2

    invoke-static {}, Lgroovy/cli/internal/CliBuilderInternal;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v0

    .line 195
    iput-boolean p1, p0, Lgroovy/cli/internal/CliBuilderInternal;->expandArgumentFiles:Z

    const/16 v1, 0x13

    .line 196
    aget-object v0, v0, v1

    iget-object v1, p0, Lgroovy/cli/internal/CliBuilderInternal;->parser:Lgroovyjarjarpicocli/CommandLine$Model$ParserSpec;

    invoke-static {p1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object p1

    invoke-interface {v0, v1, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method public setFooter(Ljava/lang/String;)V
    .locals 2

    invoke-static {}, Lgroovy/cli/internal/CliBuilderInternal;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v0

    .line 163
    iput-object p1, p0, Lgroovy/cli/internal/CliBuilderInternal;->footer:Ljava/lang/String;

    const/16 v1, 0x10

    .line 164
    aget-object v0, v0, v1

    iget-object v1, p0, Lgroovy/cli/internal/CliBuilderInternal;->usageMessage:Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;

    invoke-interface {v0, v1, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method public setHeader(Ljava/lang/String;)V
    .locals 2

    invoke-static {}, Lgroovy/cli/internal/CliBuilderInternal;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v0

    .line 173
    iput-object p1, p0, Lgroovy/cli/internal/CliBuilderInternal;->header:Ljava/lang/String;

    const/16 v1, 0x11

    .line 176
    aget-object v0, v0, v1

    iget-object v1, p0, Lgroovy/cli/internal/CliBuilderInternal;->usageMessage:Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;

    invoke-interface {v0, v1, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method public setMetaClass(Lgroovy/lang/MetaClass;)V
    .locals 0
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    .annotation runtime Lgroovy/transform/Internal;
    .end annotation

    iput-object p1, p0, Lgroovy/cli/internal/CliBuilderInternal;->metaClass:Lgroovy/lang/MetaClass;

    return-void
.end method

.method public setName(Ljava/lang/String;)V
    .locals 0
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iput-object p1, p0, Lgroovy/cli/internal/CliBuilderInternal;->name:Ljava/lang/String;

    return-void
.end method

.method public setPosix(Ljava/lang/Boolean;)V
    .locals 3

    invoke-static {}, Lgroovy/cli/internal/CliBuilderInternal;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v0

    .line 206
    iput-object p1, p0, Lgroovy/cli/internal/CliBuilderInternal;->posix:Ljava/lang/Boolean;

    const/16 v1, 0x14

    .line 207
    aget-object v0, v0, v1

    iget-object v1, p0, Lgroovy/cli/internal/CliBuilderInternal;->parser:Lgroovyjarjarpicocli/CommandLine$Model$ParserSpec;

    invoke-static {p1}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_0

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    invoke-static {p1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object p1

    :goto_0
    invoke-interface {v0, v1, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method public setSavedTypeOptions(Ljava/util/Map;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Lgroovy/cli/TypedOption;",
            ">;)V"
        }
    .end annotation

    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iput-object p1, p0, Lgroovy/cli/internal/CliBuilderInternal;->savedTypeOptions:Ljava/util/Map;

    return-void
.end method

.method public setStopAtNonOption(Z)V
    .locals 4

    invoke-static {}, Lgroovy/cli/internal/CliBuilderInternal;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v0

    .line 220
    iput-boolean p1, p0, Lgroovy/cli/internal/CliBuilderInternal;->stopAtNonOption:Z

    const/16 v1, 0x15

    .line 221
    aget-object v1, v0, v1

    iget-object v2, p0, Lgroovy/cli/internal/CliBuilderInternal;->parser:Lgroovyjarjarpicocli/CommandLine$Model$ParserSpec;

    invoke-static {p1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v3

    invoke-interface {v1, v2, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v1, 0x16

    .line 222
    aget-object v0, v0, v1

    iget-object v1, p0, Lgroovy/cli/internal/CliBuilderInternal;->parser:Lgroovyjarjarpicocli/CommandLine$Model$ParserSpec;

    invoke-static {p1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object p1

    invoke-interface {v0, v1, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method public setUsage(Ljava/lang/String;)V
    .locals 2

    invoke-static {}, Lgroovy/cli/internal/CliBuilderInternal;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v0

    .line 153
    iput-object p1, p0, Lgroovy/cli/internal/CliBuilderInternal;->usage:Ljava/lang/String;

    const/16 v1, 0xf

    .line 154
    aget-object v0, v0, v1

    iget-object v1, p0, Lgroovy/cli/internal/CliBuilderInternal;->usageMessage:Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;

    invoke-interface {v0, v1, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method public setWidth(I)V
    .locals 2

    invoke-static {}, Lgroovy/cli/internal/CliBuilderInternal;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v0

    .line 185
    iput p1, p0, Lgroovy/cli/internal/CliBuilderInternal;->width:I

    const/16 v1, 0x12

    .line 186
    aget-object v0, v0, v1

    iget-object v1, p0, Lgroovy/cli/internal/CliBuilderInternal;->usageMessage:Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    invoke-interface {v0, v1, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method public setWriter(Ljava/io/PrintWriter;)V
    .locals 0

    invoke-static {}, Lgroovy/cli/internal/CliBuilderInternal;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    .line 231
    iput-object p1, p0, Lgroovy/cli/internal/CliBuilderInternal;->writer:Ljava/io/PrintWriter;

    .line 232
    iput-object p1, p0, Lgroovy/cli/internal/CliBuilderInternal;->errorWriter:Ljava/io/PrintWriter;

    return-void
.end method

.method public synthetic this$dist$get$1(Ljava/lang/String;)Ljava/lang/Object;
    .locals 4

    invoke-static {}, Lgroovy/cli/internal/CliBuilderInternal;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    const-class v0, Lgroovy/cli/internal/CliBuilderInternal;

    new-instance v1, Lorg/codehaus/groovy/runtime/GStringImpl;

    const/4 v2, 0x1

    new-array v2, v2, [Ljava/lang/Object;

    const/4 v3, 0x0

    aput-object p1, v2, v3

    const-string p1, ""

    filled-new-array {p1, p1}, [Ljava/lang/String;

    move-result-object p1

    invoke-direct {v1, v2, p1}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    move-object v1, p1

    check-cast v1, Ljava/lang/String;

    invoke-static {v0, p0, p1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->getGroovyObjectProperty(Ljava/lang/Class;Lgroovy/lang/GroovyObject;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public synthetic this$dist$invoke$1(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 7

    const-class v0, Lgroovy/cli/internal/CliBuilderInternal;

    invoke-static {}, Lgroovy/cli/internal/CliBuilderInternal;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v1

    instance-of v2, p2, [Ljava/lang/Object;

    const/4 v3, 0x1

    xor-int/2addr v2, v3

    const-string v4, ""

    const/4 v5, 0x0

    if-eqz v2, :cond_0

    new-instance v1, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v2, v3, [Ljava/lang/Object;

    aput-object p1, v2, v5

    filled-new-array {v4, v4}, [Ljava/lang/String;

    move-result-object p1

    invoke-direct {v1, v2, p1}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    move-object v1, p1

    check-cast v1, Ljava/lang/String;

    new-array v1, v3, [Ljava/lang/Object;

    aput-object p2, v1, v5

    invoke-static {v0, p0, p1, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodOnCurrentN(Ljava/lang/Class;Lgroovy/lang/GroovyObject;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1

    :cond_0
    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->isOrigInt()Z

    move-result v2

    if-eqz v2, :cond_1

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->isOrigZ()Z

    move-result v2

    if-eqz v2, :cond_1

    sget-boolean v2, Lgroovy/cli/internal/CliBuilderInternal;->__$stMC:Z

    if-nez v2, :cond_1

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->disabledStandardMetaClass()Z

    move-result v2

    if-nez v2, :cond_1

    const/16 v2, 0x96

    aget-object v1, v1, v2

    const-class v2, [Ljava/lang/Object;

    invoke-static {p2, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, [Ljava/lang/Object;

    invoke-interface {v1, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    invoke-static {v1, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_2

    new-instance v1, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v2, v3, [Ljava/lang/Object;

    aput-object p1, v2, v5

    filled-new-array {v4, v4}, [Ljava/lang/String;

    move-result-object p1

    invoke-direct {v1, v2, p1}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    move-object v1, p1

    check-cast v1, Ljava/lang/String;

    new-array v1, v3, [Ljava/lang/Object;

    const-class v2, [Ljava/lang/Object;

    invoke-static {p2, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, [Ljava/lang/Object;

    invoke-static {p2, v5}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->objectArrayGet([Ljava/lang/Object;I)Ljava/lang/Object;

    move-result-object p2

    aput-object p2, v1, v5

    invoke-static {v0, p0, p1, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodOnCurrentN(Ljava/lang/Class;Lgroovy/lang/GroovyObject;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1

    :cond_1
    const/16 v2, 0x94

    aget-object v2, v1, v2

    const-class v6, [Ljava/lang/Object;

    invoke-static {p2, v6}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v6

    check-cast v6, [Ljava/lang/Object;

    invoke-interface {v2, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v6

    invoke-static {v2, v6}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_2

    new-instance v2, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v6, v3, [Ljava/lang/Object;

    aput-object p1, v6, v5

    filled-new-array {v4, v4}, [Ljava/lang/String;

    move-result-object p1

    invoke-direct {v2, v6, p1}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    move-object v2, p1

    check-cast v2, Ljava/lang/String;

    new-array v2, v3, [Ljava/lang/Object;

    const/16 v3, 0x95

    aget-object v1, v1, v3

    const-class v3, [Ljava/lang/Object;

    invoke-static {p2, v3}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, [Ljava/lang/Object;

    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v3

    invoke-interface {v1, p2, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p2

    aput-object p2, v2, v5

    invoke-static {v0, p0, p1, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodOnCurrentN(Ljava/lang/Class;Lgroovy/lang/GroovyObject;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1

    :cond_2
    new-instance v1, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v2, v3, [Ljava/lang/Object;

    aput-object p1, v2, v5

    filled-new-array {v4, v4}, [Ljava/lang/String;

    move-result-object p1

    invoke-direct {v1, v2, p1}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    move-object v1, p1

    check-cast v1, Ljava/lang/String;

    new-array v1, v5, [Ljava/lang/Object;

    new-array v2, v3, [Ljava/lang/Object;

    aput-object p2, v2, v5

    new-array p2, v3, [I

    aput v5, p2, v5

    invoke-static {v1, v2, p2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->despreadList([Ljava/lang/Object;[Ljava/lang/Object;[I)[Ljava/lang/Object;

    move-result-object p2

    invoke-static {v0, p0, p1, p2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodOnCurrentN(Ljava/lang/Class;Lgroovy/lang/GroovyObject;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public synthetic this$dist$set$1(Ljava/lang/String;Ljava/lang/Object;)V
    .locals 4

    invoke-static {}, Lgroovy/cli/internal/CliBuilderInternal;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    const-class v0, Lgroovy/cli/internal/CliBuilderInternal;

    new-instance v1, Lorg/codehaus/groovy/runtime/GStringImpl;

    const/4 v2, 0x1

    new-array v2, v2, [Ljava/lang/Object;

    const/4 v3, 0x0

    aput-object p1, v2, v3

    const-string p1, ""

    filled-new-array {p1, p1}, [Ljava/lang/String;

    move-result-object p1

    invoke-direct {v1, v2, p1}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    move-object v1, p1

    check-cast v1, Ljava/lang/String;

    invoke-static {p2, v0, p0, p1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->setGroovyObjectProperty(Ljava/lang/Object;Ljava/lang/Class;Lgroovy/lang/GroovyObject;Ljava/lang/String;)V

    return-void
.end method

.method public usage()V
    .locals 4

    invoke-static {}, Lgroovy/cli/internal/CliBuilderInternal;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v0

    sget-boolean v1, Lgroovy/cli/internal/CliBuilderInternal;->__$stMC:Z

    if-nez v1, :cond_1

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->disabledStandardMetaClass()Z

    move-result v1

    if-nez v1, :cond_1

    const/16 v1, 0x6d

    .line 328
    aget-object v1, v0, v1

    const/16 v2, 0x6e

    aget-object v0, v0, v2

    iget-object v2, p0, Lgroovy/cli/internal/CliBuilderInternal;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-interface {v0, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_0

    goto :goto_0

    :cond_0
    invoke-direct {p0}, Lgroovy/cli/internal/CliBuilderInternal;->createCommandLine()Lgroovyjarjarpicocli/CommandLine;

    move-result-object v0

    :goto_0
    iget-object v2, p0, Lgroovy/cli/internal/CliBuilderInternal;->writer:Ljava/io/PrintWriter;

    invoke-interface {v1, p0, v0, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callCurrent(Lgroovy/lang/GroovyObject;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_2

    :cond_1
    const/16 v1, 0x6a

    aget-object v1, v0, v1

    const/16 v2, 0x6b

    aget-object v2, v0, v2

    iget-object v3, p0, Lgroovy/cli/internal/CliBuilderInternal;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-interface {v2, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_2

    goto :goto_1

    :cond_2
    const/16 v2, 0x6c

    aget-object v0, v0, v2

    invoke-interface {v0, p0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callCurrent(Lgroovy/lang/GroovyObject;)Ljava/lang/Object;

    move-result-object v2

    :goto_1
    iget-object v0, p0, Lgroovy/cli/internal/CliBuilderInternal;->writer:Ljava/io/PrintWriter;

    invoke-interface {v1, p0, v2, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callCurrent(Lgroovy/lang/GroovyObject;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :goto_2
    return-void
.end method
