.class Lgroovy/cli/internal/CliBuilderInternal$ArgSpecAttributes;
.super Ljava/lang/Object;
.source "CliBuilderInternal.groovy"

# interfaces
.implements Lgroovy/lang/GroovyObject;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovy/cli/internal/CliBuilderInternal;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0xa
    name = "ArgSpecAttributes"
.end annotation


# static fields
.field private static synthetic $callSiteArray:Ljava/lang/ref/SoftReference;

.field private static synthetic $staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

.field private static synthetic $staticClassInfo$:Lorg/codehaus/groovy/reflection/ClassInfo;

.field public static transient synthetic __$stMC:Z


# instance fields
.field private auxiliaryTypes:[Ljava/lang/Class;

.field private getter:Lgroovyjarjarpicocli/CommandLine$Model$IGetter;

.field private hasInitialValue:Z

.field private initialValue:Ljava/lang/Object;

.field private label:Ljava/lang/String;

.field private transient synthetic metaClass:Lgroovy/lang/MetaClass;

.field private setter:Lgroovyjarjarpicocli/CommandLine$Model$ISetter;

.field private type:Ljava/lang/Class;


# direct methods
.method private static synthetic $createCallSiteArray()Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;
    .locals 3

    const/4 v0, 0x6

    new-array v0, v0, [Ljava/lang/String;

    invoke-static {v0}, Lgroovy/cli/internal/CliBuilderInternal$ArgSpecAttributes;->$createCallSiteArray_1([Ljava/lang/String;)V

    new-instance v1, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;

    const-class v2, Lgroovy/cli/internal/CliBuilderInternal$ArgSpecAttributes;

    invoke-direct {v1, v2, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;-><init>(Ljava/lang/Class;[Ljava/lang/String;)V

    return-object v1
.end method

.method private static synthetic $createCallSiteArray_1([Ljava/lang/String;)V
    .locals 3

    const/4 v0, 0x0

    const-string v1, "length"

    aput-object v1, p0, v0

    const/4 v0, 0x1

    const-string v2, "getAt"

    aput-object v2, p0, v0

    const/4 v0, 0x2

    aput-object v1, p0, v0

    const/4 v0, 0x3

    aput-object v1, p0, v0

    const/4 v0, 0x4

    aput-object v2, p0, v0

    const/4 v0, 0x5

    aput-object v1, p0, v0

    return-void
.end method

.method private static synthetic $getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;
    .locals 2

    sget-object v0, Lgroovy/cli/internal/CliBuilderInternal$ArgSpecAttributes;->$callSiteArray:Ljava/lang/ref/SoftReference;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Ljava/lang/ref/SoftReference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;

    if-nez v0, :cond_1

    :cond_0
    invoke-static {}, Lgroovy/cli/internal/CliBuilderInternal$ArgSpecAttributes;->$createCallSiteArray()Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;

    move-result-object v0

    new-instance v1, Ljava/lang/ref/SoftReference;

    invoke-direct {v1, v0}, Ljava/lang/ref/SoftReference;-><init>(Ljava/lang/Object;)V

    sput-object v1, Lgroovy/cli/internal/CliBuilderInternal$ArgSpecAttributes;->$callSiteArray:Ljava/lang/ref/SoftReference;

    :cond_1
    iget-object v0, v0, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;->array:[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    return-object v0
.end method

.method public static synthetic $static_methodMissing(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 8

    const-class v0, Lgroovy/cli/internal/CliBuilderInternal$ArgSpecAttributes;

    invoke-static {}, Lgroovy/cli/internal/CliBuilderInternal$ArgSpecAttributes;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v1

    instance-of v2, p1, [Ljava/lang/Object;

    const/4 v3, 0x1

    xor-int/2addr v2, v3

    const-string v4, ""

    const/4 v5, 0x0

    if-eqz v2, :cond_0

    const-class v1, Lgroovy/cli/internal/CliBuilderInternal;

    new-instance v2, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v6, v3, [Ljava/lang/Object;

    aput-object p0, v6, v5

    filled-new-array {v4, v4}, [Ljava/lang/String;

    move-result-object p0

    invoke-direct {v2, v6, p0}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p0

    move-object v2, p0

    check-cast v2, Ljava/lang/String;

    new-array v2, v3, [Ljava/lang/Object;

    aput-object p1, v2, v5

    invoke-static {v0, v1, p0, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodN(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    return-object p0

    :cond_0
    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->isOrigInt()Z

    move-result v2

    if-eqz v2, :cond_1

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->isOrigZ()Z

    move-result v2

    if-eqz v2, :cond_1

    sget-boolean v2, Lgroovy/cli/internal/CliBuilderInternal$ArgSpecAttributes;->__$stMC:Z

    if-nez v2, :cond_1

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->disabledStandardMetaClass()Z

    move-result v2

    if-nez v2, :cond_1

    const/4 v2, 0x5

    aget-object v1, v1, v2

    const-class v2, [Ljava/lang/Object;

    invoke-static {p1, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, [Ljava/lang/Object;

    invoke-interface {v1, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    invoke-static {v1, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_2

    const-class v1, Lgroovy/cli/internal/CliBuilderInternal;

    new-instance v2, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v6, v3, [Ljava/lang/Object;

    aput-object p0, v6, v5

    filled-new-array {v4, v4}, [Ljava/lang/String;

    move-result-object p0

    invoke-direct {v2, v6, p0}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p0

    move-object v2, p0

    check-cast v2, Ljava/lang/String;

    new-array v2, v3, [Ljava/lang/Object;

    const-class v3, [Ljava/lang/Object;

    invoke-static {p1, v3}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, [Ljava/lang/Object;

    invoke-static {p1, v5}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->objectArrayGet([Ljava/lang/Object;I)Ljava/lang/Object;

    move-result-object p1

    aput-object p1, v2, v5

    invoke-static {v0, v1, p0, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodN(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    return-object p0

    :cond_1
    const/4 v2, 0x3

    aget-object v2, v1, v2

    const-class v6, [Ljava/lang/Object;

    invoke-static {p1, v6}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v6

    check-cast v6, [Ljava/lang/Object;

    invoke-interface {v2, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v6

    invoke-static {v2, v6}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_2

    const-class v2, Lgroovy/cli/internal/CliBuilderInternal;

    new-instance v6, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v7, v3, [Ljava/lang/Object;

    aput-object p0, v7, v5

    filled-new-array {v4, v4}, [Ljava/lang/String;

    move-result-object p0

    invoke-direct {v6, v7, p0}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v6}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p0

    move-object v4, p0

    check-cast v4, Ljava/lang/String;

    new-array v3, v3, [Ljava/lang/Object;

    const/4 v4, 0x4

    aget-object v1, v1, v4

    const-class v4, [Ljava/lang/Object;

    invoke-static {p1, v4}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, [Ljava/lang/Object;

    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v4

    invoke-interface {v1, p1, v4}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    aput-object p1, v3, v5

    invoke-static {v0, v2, p0, v3}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodN(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    return-object p0

    :cond_2
    const-class v1, Lgroovy/cli/internal/CliBuilderInternal;

    new-instance v2, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v6, v3, [Ljava/lang/Object;

    aput-object p0, v6, v5

    filled-new-array {v4, v4}, [Ljava/lang/String;

    move-result-object p0

    invoke-direct {v2, v6, p0}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p0

    move-object v2, p0

    check-cast v2, Ljava/lang/String;

    new-array v2, v5, [Ljava/lang/Object;

    new-array v4, v3, [Ljava/lang/Object;

    aput-object p1, v4, v5

    new-array p1, v3, [I

    aput v5, p1, v5

    invoke-static {v2, v4, p1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->despreadList([Ljava/lang/Object;[Ljava/lang/Object;[I)[Ljava/lang/Object;

    move-result-object p1

    invoke-static {v0, v1, p0, p1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodN(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic $static_propertyMissing(Ljava/lang/String;)Ljava/lang/Object;
    .locals 5

    invoke-static {}, Lgroovy/cli/internal/CliBuilderInternal$ArgSpecAttributes;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    const-class v0, Lgroovy/cli/internal/CliBuilderInternal$ArgSpecAttributes;

    const-class v1, Lgroovy/cli/internal/CliBuilderInternal;

    new-instance v2, Lorg/codehaus/groovy/runtime/GStringImpl;

    const/4 v3, 0x1

    new-array v3, v3, [Ljava/lang/Object;

    const/4 v4, 0x0

    aput-object p0, v3, v4

    const-string p0, ""

    filled-new-array {p0, p0}, [Ljava/lang/String;

    move-result-object p0

    invoke-direct {v2, v3, p0}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p0

    move-object v2, p0

    check-cast v2, Ljava/lang/String;

    invoke-static {v0, v1, p0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->getProperty(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic $static_propertyMissing(Ljava/lang/String;Ljava/lang/Object;)V
    .locals 4

    invoke-static {}, Lgroovy/cli/internal/CliBuilderInternal$ArgSpecAttributes;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    const-class v0, Lgroovy/cli/internal/CliBuilderInternal;

    new-instance v1, Lorg/codehaus/groovy/runtime/GStringImpl;

    const/4 v2, 0x1

    new-array v2, v2, [Ljava/lang/Object;

    const/4 v3, 0x0

    aput-object p0, v2, v3

    const-string p0, ""

    filled-new-array {p0, p0}, [Ljava/lang/String;

    move-result-object p0

    invoke-direct {v1, v2, p0}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p0

    move-object v1, p0

    check-cast v1, Ljava/lang/String;

    const/4 v1, 0x0

    invoke-static {p1, v1, v0, p0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->setProperty(Ljava/lang/Object;Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;)V

    return-void
.end method

.method public constructor <init>()V
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    invoke-static {}, Lgroovy/cli/internal/CliBuilderInternal$ArgSpecAttributes;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    invoke-virtual {p0}, Lgroovy/cli/internal/CliBuilderInternal$ArgSpecAttributes;->$getStaticMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    iput-object v0, p0, Lgroovy/cli/internal/CliBuilderInternal$ArgSpecAttributes;->metaClass:Lgroovy/lang/MetaClass;

    return-void
.end method


# virtual methods
.method protected synthetic $getStaticMetaClass()Lgroovy/lang/MetaClass;
    .locals 2

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    const-class v1, Lgroovy/cli/internal/CliBuilderInternal$ArgSpecAttributes;

    if-eq v0, v1, :cond_0

    invoke-static {p0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->initMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0

    :cond_0
    sget-object v0, Lgroovy/cli/internal/CliBuilderInternal$ArgSpecAttributes;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    if-nez v0, :cond_1

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getClassInfo(Ljava/lang/Class;)Lorg/codehaus/groovy/reflection/ClassInfo;

    move-result-object v0

    sput-object v0, Lgroovy/cli/internal/CliBuilderInternal$ArgSpecAttributes;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    :cond_1
    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0
.end method

.method public getAuxiliaryTypes()[Ljava/lang/Class;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-object v0, p0, Lgroovy/cli/internal/CliBuilderInternal$ArgSpecAttributes;->auxiliaryTypes:[Ljava/lang/Class;

    return-object v0
.end method

.method public getGetter()Lgroovyjarjarpicocli/CommandLine$Model$IGetter;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-object v0, p0, Lgroovy/cli/internal/CliBuilderInternal$ArgSpecAttributes;->getter:Lgroovyjarjarpicocli/CommandLine$Model$IGetter;

    return-object v0
.end method

.method public getHasInitialValue()Z
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-boolean v0, p0, Lgroovy/cli/internal/CliBuilderInternal$ArgSpecAttributes;->hasInitialValue:Z

    return v0
.end method

.method public getInitialValue()Ljava/lang/Object;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-object v0, p0, Lgroovy/cli/internal/CliBuilderInternal$ArgSpecAttributes;->initialValue:Ljava/lang/Object;

    return-object v0
.end method

.method public getLabel()Ljava/lang/String;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-object v0, p0, Lgroovy/cli/internal/CliBuilderInternal$ArgSpecAttributes;->label:Ljava/lang/String;

    return-object v0
.end method

.method public getMetaClass()Lgroovy/lang/MetaClass;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    .annotation runtime Lgroovy/transform/Internal;
    .end annotation

    .annotation runtime Lgroovyjarjaropenbeans/Transient;
    .end annotation

    iget-object v0, p0, Lgroovy/cli/internal/CliBuilderInternal$ArgSpecAttributes;->metaClass:Lgroovy/lang/MetaClass;

    if-eqz v0, :cond_0

    return-object v0

    :cond_0
    invoke-virtual {p0}, Lgroovy/cli/internal/CliBuilderInternal$ArgSpecAttributes;->$getStaticMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    iput-object v0, p0, Lgroovy/cli/internal/CliBuilderInternal$ArgSpecAttributes;->metaClass:Lgroovy/lang/MetaClass;

    return-object v0
.end method

.method public getSetter()Lgroovyjarjarpicocli/CommandLine$Model$ISetter;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-object v0, p0, Lgroovy/cli/internal/CliBuilderInternal$ArgSpecAttributes;->setter:Lgroovyjarjarpicocli/CommandLine$Model$ISetter;

    return-object v0
.end method

.method public getType()Ljava/lang/Class;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-object v0, p0, Lgroovy/cli/internal/CliBuilderInternal$ArgSpecAttributes;->type:Ljava/lang/Class;

    return-object v0
.end method

.method public isHasInitialValue()Z
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-boolean v0, p0, Lgroovy/cli/internal/CliBuilderInternal$ArgSpecAttributes;->hasInitialValue:Z

    return v0
.end method

.method public synthetic methodMissing(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 8

    const-class v0, Lgroovy/cli/internal/CliBuilderInternal$ArgSpecAttributes;

    invoke-static {}, Lgroovy/cli/internal/CliBuilderInternal$ArgSpecAttributes;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v1

    instance-of v2, p2, [Ljava/lang/Object;

    const/4 v3, 0x1

    xor-int/2addr v2, v3

    const-string v4, ""

    const/4 v5, 0x0

    if-eqz v2, :cond_0

    const-class v1, Lgroovy/cli/internal/CliBuilderInternal;

    new-instance v2, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v6, v3, [Ljava/lang/Object;

    aput-object p1, v6, v5

    filled-new-array {v4, v4}, [Ljava/lang/String;

    move-result-object p1

    invoke-direct {v2, v6, p1}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    move-object v2, p1

    check-cast v2, Ljava/lang/String;

    new-array v2, v3, [Ljava/lang/Object;

    aput-object p2, v2, v5

    invoke-static {v0, v1, p1, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodN(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1

    :cond_0
    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->isOrigInt()Z

    move-result v2

    if-eqz v2, :cond_1

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->isOrigZ()Z

    move-result v2

    if-eqz v2, :cond_1

    sget-boolean v2, Lgroovy/cli/internal/CliBuilderInternal$ArgSpecAttributes;->__$stMC:Z

    if-nez v2, :cond_1

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->disabledStandardMetaClass()Z

    move-result v2

    if-nez v2, :cond_1

    const/4 v2, 0x2

    aget-object v1, v1, v2

    const-class v2, [Ljava/lang/Object;

    invoke-static {p2, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, [Ljava/lang/Object;

    invoke-interface {v1, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    invoke-static {v1, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_2

    const-class v1, Lgroovy/cli/internal/CliBuilderInternal;

    new-instance v2, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v6, v3, [Ljava/lang/Object;

    aput-object p1, v6, v5

    filled-new-array {v4, v4}, [Ljava/lang/String;

    move-result-object p1

    invoke-direct {v2, v6, p1}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    move-object v2, p1

    check-cast v2, Ljava/lang/String;

    new-array v2, v3, [Ljava/lang/Object;

    const-class v3, [Ljava/lang/Object;

    invoke-static {p2, v3}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, [Ljava/lang/Object;

    invoke-static {p2, v5}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->objectArrayGet([Ljava/lang/Object;I)Ljava/lang/Object;

    move-result-object p2

    aput-object p2, v2, v5

    invoke-static {v0, v1, p1, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodN(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1

    :cond_1
    aget-object v2, v1, v5

    const-class v6, [Ljava/lang/Object;

    invoke-static {p2, v6}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v6

    check-cast v6, [Ljava/lang/Object;

    invoke-interface {v2, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v6

    invoke-static {v2, v6}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_2

    const-class v2, Lgroovy/cli/internal/CliBuilderInternal;

    new-instance v6, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v7, v3, [Ljava/lang/Object;

    aput-object p1, v7, v5

    filled-new-array {v4, v4}, [Ljava/lang/String;

    move-result-object p1

    invoke-direct {v6, v7, p1}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v6}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    move-object v4, p1

    check-cast v4, Ljava/lang/String;

    new-array v4, v3, [Ljava/lang/Object;

    aget-object v1, v1, v3

    const-class v3, [Ljava/lang/Object;

    invoke-static {p2, v3}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, [Ljava/lang/Object;

    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v3

    invoke-interface {v1, p2, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p2

    aput-object p2, v4, v5

    invoke-static {v0, v2, p1, v4}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodN(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1

    :cond_2
    const-class v1, Lgroovy/cli/internal/CliBuilderInternal;

    new-instance v2, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v6, v3, [Ljava/lang/Object;

    aput-object p1, v6, v5

    filled-new-array {v4, v4}, [Ljava/lang/String;

    move-result-object p1

    invoke-direct {v2, v6, p1}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    move-object v2, p1

    check-cast v2, Ljava/lang/String;

    new-array v2, v5, [Ljava/lang/Object;

    new-array v4, v3, [Ljava/lang/Object;

    aput-object p2, v4, v5

    new-array p2, v3, [I

    aput v5, p2, v5

    invoke-static {v2, v4, p2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->despreadList([Ljava/lang/Object;[Ljava/lang/Object;[I)[Ljava/lang/Object;

    move-result-object p2

    invoke-static {v0, v1, p1, p2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodN(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public synthetic propertyMissing(Ljava/lang/String;)Ljava/lang/Object;
    .locals 5

    invoke-static {}, Lgroovy/cli/internal/CliBuilderInternal$ArgSpecAttributes;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    const-class v0, Lgroovy/cli/internal/CliBuilderInternal$ArgSpecAttributes;

    const-class v1, Lgroovy/cli/internal/CliBuilderInternal;

    new-instance v2, Lorg/codehaus/groovy/runtime/GStringImpl;

    const/4 v3, 0x1

    new-array v3, v3, [Ljava/lang/Object;

    const/4 v4, 0x0

    aput-object p1, v3, v4

    const-string p1, ""

    filled-new-array {p1, p1}, [Ljava/lang/String;

    move-result-object p1

    invoke-direct {v2, v3, p1}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    move-object v2, p1

    check-cast v2, Ljava/lang/String;

    invoke-static {v0, v1, p1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->getProperty(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public synthetic propertyMissing(Ljava/lang/String;Ljava/lang/Object;)V
    .locals 4

    invoke-static {}, Lgroovy/cli/internal/CliBuilderInternal$ArgSpecAttributes;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    const-class v0, Lgroovy/cli/internal/CliBuilderInternal;

    new-instance v1, Lorg/codehaus/groovy/runtime/GStringImpl;

    const/4 v2, 0x1

    new-array v2, v2, [Ljava/lang/Object;

    const/4 v3, 0x0

    aput-object p1, v2, v3

    const-string p1, ""

    filled-new-array {p1, p1}, [Ljava/lang/String;

    move-result-object p1

    invoke-direct {v1, v2, p1}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    move-object v1, p1

    check-cast v1, Ljava/lang/String;

    const/4 v1, 0x0

    invoke-static {p2, v1, v0, p1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->setProperty(Ljava/lang/Object;Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;)V

    return-void
.end method

.method public varargs setAuxiliaryTypes([Ljava/lang/Class;)V
    .locals 0
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iput-object p1, p0, Lgroovy/cli/internal/CliBuilderInternal$ArgSpecAttributes;->auxiliaryTypes:[Ljava/lang/Class;

    return-void
.end method

.method public setGetter(Lgroovyjarjarpicocli/CommandLine$Model$IGetter;)V
    .locals 0
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iput-object p1, p0, Lgroovy/cli/internal/CliBuilderInternal$ArgSpecAttributes;->getter:Lgroovyjarjarpicocli/CommandLine$Model$IGetter;

    return-void
.end method

.method public setHasInitialValue(Z)V
    .locals 0
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iput-boolean p1, p0, Lgroovy/cli/internal/CliBuilderInternal$ArgSpecAttributes;->hasInitialValue:Z

    return-void
.end method

.method public setInitialValue(Ljava/lang/Object;)V
    .locals 0
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iput-object p1, p0, Lgroovy/cli/internal/CliBuilderInternal$ArgSpecAttributes;->initialValue:Ljava/lang/Object;

    return-void
.end method

.method public setLabel(Ljava/lang/String;)V
    .locals 0
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iput-object p1, p0, Lgroovy/cli/internal/CliBuilderInternal$ArgSpecAttributes;->label:Ljava/lang/String;

    return-void
.end method

.method public setMetaClass(Lgroovy/lang/MetaClass;)V
    .locals 0
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    .annotation runtime Lgroovy/transform/Internal;
    .end annotation

    iput-object p1, p0, Lgroovy/cli/internal/CliBuilderInternal$ArgSpecAttributes;->metaClass:Lgroovy/lang/MetaClass;

    return-void
.end method

.method public setSetter(Lgroovyjarjarpicocli/CommandLine$Model$ISetter;)V
    .locals 0
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iput-object p1, p0, Lgroovy/cli/internal/CliBuilderInternal$ArgSpecAttributes;->setter:Lgroovyjarjarpicocli/CommandLine$Model$ISetter;

    return-void
.end method

.method public setType(Ljava/lang/Class;)V
    .locals 0
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iput-object p1, p0, Lgroovy/cli/internal/CliBuilderInternal$ArgSpecAttributes;->type:Ljava/lang/Class;

    return-void
.end method
