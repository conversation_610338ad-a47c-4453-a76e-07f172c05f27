.class public final Lgroovy/security/GroovyCodeSourcePermission;
.super Ljava/security/BasicPermission;
.source "GroovyCodeSourcePermission.java"


# static fields
.field private static final serialVersionUID:J = 0x6f387afe891a4a3bL


# direct methods
.method public constructor <init>(Ljava/lang/String;)V
    .locals 0

    .line 34
    invoke-direct {p0, p1}, Ljava/security/BasicPermission;-><init>(Ljava/lang/String;)V

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;Ljava/lang/String;)V
    .locals 0

    .line 38
    invoke-direct {p0, p1, p2}, Ljava/security/BasicPermission;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method
