.class public abstract Lgroovy/time/BaseDuration;
.super Ljava/lang/Object;
.source "BaseDuration.java"

# interfaces
.implements Ljava/lang/Comparable;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lgroovy/time/BaseDuration$From;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ljava/lang/Comparable<",
        "Lgroovy/time/BaseDuration;",
        ">;"
    }
.end annotation


# instance fields
.field protected final days:I

.field protected final hours:I

.field protected final millis:I

.field protected final minutes:I

.field protected final months:I

.field protected final seconds:I

.field protected final years:I


# direct methods
.method protected constructor <init>(IIIII)V
    .locals 8

    const/4 v1, 0x0

    const/4 v2, 0x0

    move-object v0, p0

    move v3, p1

    move v4, p2

    move v5, p3

    move v6, p4

    move v7, p5

    .line 54
    invoke-direct/range {v0 .. v7}, Lgroovy/time/BaseDuration;-><init>(IIIIIII)V

    return-void
.end method

.method protected constructor <init>(IIIIIII)V
    .locals 0

    .line 43
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 44
    iput p1, p0, Lgroovy/time/BaseDuration;->years:I

    .line 45
    iput p2, p0, Lgroovy/time/BaseDuration;->months:I

    .line 46
    iput p3, p0, Lgroovy/time/BaseDuration;->days:I

    .line 47
    iput p4, p0, Lgroovy/time/BaseDuration;->hours:I

    .line 48
    iput p5, p0, Lgroovy/time/BaseDuration;->minutes:I

    .line 49
    iput p6, p0, Lgroovy/time/BaseDuration;->seconds:I

    .line 50
    iput p7, p0, Lgroovy/time/BaseDuration;->millis:I

    return-void
.end method


# virtual methods
.method public compareTo(Lgroovy/time/BaseDuration;)I
    .locals 4

    .line 130
    invoke-virtual {p0}, Lgroovy/time/BaseDuration;->toMilliseconds()J

    move-result-wide v0

    invoke-virtual {p1}, Lgroovy/time/BaseDuration;->toMilliseconds()J

    move-result-wide v2

    sub-long/2addr v0, v2

    invoke-static {v0, v1}, Ljava/lang/Long;->signum(J)I

    move-result p1

    return p1
.end method

.method public bridge synthetic compareTo(Ljava/lang/Object;)I
    .locals 0

    .line 34
    check-cast p1, Lgroovy/time/BaseDuration;

    invoke-virtual {p0, p1}, Lgroovy/time/BaseDuration;->compareTo(Lgroovy/time/BaseDuration;)I

    move-result p1

    return p1
.end method

.method public abstract getAgo()Ljava/util/Date;
.end method

.method public getDays()I
    .locals 1

    .line 66
    iget v0, p0, Lgroovy/time/BaseDuration;->days:I

    return v0
.end method

.method public abstract getFrom()Lgroovy/time/BaseDuration$From;
.end method

.method public getHours()I
    .locals 1

    .line 70
    iget v0, p0, Lgroovy/time/BaseDuration;->hours:I

    return v0
.end method

.method public getMillis()I
    .locals 1

    .line 82
    iget v0, p0, Lgroovy/time/BaseDuration;->millis:I

    return v0
.end method

.method public getMinutes()I
    .locals 1

    .line 74
    iget v0, p0, Lgroovy/time/BaseDuration;->minutes:I

    return v0
.end method

.method public getMonths()I
    .locals 1

    .line 62
    iget v0, p0, Lgroovy/time/BaseDuration;->months:I

    return v0
.end method

.method public getSeconds()I
    .locals 1

    .line 78
    iget v0, p0, Lgroovy/time/BaseDuration;->seconds:I

    return v0
.end method

.method public getYears()I
    .locals 1

    .line 58
    iget v0, p0, Lgroovy/time/BaseDuration;->years:I

    return v0
.end method

.method public plus(Ljava/util/Date;)Ljava/util/Date;
    .locals 2

    .line 86
    invoke-static {}, Ljava/util/Calendar;->getInstance()Ljava/util/Calendar;

    move-result-object v0

    .line 88
    invoke-virtual {v0, p1}, Ljava/util/Calendar;->setTime(Ljava/util/Date;)V

    .line 89
    iget p1, p0, Lgroovy/time/BaseDuration;->years:I

    const/4 v1, 0x1

    invoke-virtual {v0, v1, p1}, Ljava/util/Calendar;->add(II)V

    .line 90
    iget p1, p0, Lgroovy/time/BaseDuration;->months:I

    const/4 v1, 0x2

    invoke-virtual {v0, v1, p1}, Ljava/util/Calendar;->add(II)V

    .line 91
    iget p1, p0, Lgroovy/time/BaseDuration;->days:I

    const/4 v1, 0x6

    invoke-virtual {v0, v1, p1}, Ljava/util/Calendar;->add(II)V

    .line 92
    iget p1, p0, Lgroovy/time/BaseDuration;->hours:I

    const/16 v1, 0xb

    invoke-virtual {v0, v1, p1}, Ljava/util/Calendar;->add(II)V

    .line 93
    iget p1, p0, Lgroovy/time/BaseDuration;->minutes:I

    const/16 v1, 0xc

    invoke-virtual {v0, v1, p1}, Ljava/util/Calendar;->add(II)V

    .line 94
    iget p1, p0, Lgroovy/time/BaseDuration;->seconds:I

    const/16 v1, 0xd

    invoke-virtual {v0, v1, p1}, Ljava/util/Calendar;->add(II)V

    .line 95
    iget p1, p0, Lgroovy/time/BaseDuration;->millis:I

    const/16 v1, 0xe

    invoke-virtual {v0, v1, p1}, Ljava/util/Calendar;->add(II)V

    .line 97
    invoke-virtual {v0}, Ljava/util/Calendar;->getTime()Ljava/util/Date;

    move-result-object p1

    return-object p1
.end method

.method public abstract toMilliseconds()J
.end method

.method public toString()Ljava/lang/String;
    .locals 6

    .line 101
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 103
    iget v1, p0, Lgroovy/time/BaseDuration;->years:I

    if-eqz v1, :cond_0

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    iget v2, p0, Lgroovy/time/BaseDuration;->years:I

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v2, " years"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 104
    :cond_0
    iget v1, p0, Lgroovy/time/BaseDuration;->months:I

    if-eqz v1, :cond_1

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    iget v2, p0, Lgroovy/time/BaseDuration;->months:I

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v2, " months"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 105
    :cond_1
    iget v1, p0, Lgroovy/time/BaseDuration;->days:I

    if-eqz v1, :cond_2

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    iget v2, p0, Lgroovy/time/BaseDuration;->days:I

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v2, " days"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 106
    :cond_2
    iget v1, p0, Lgroovy/time/BaseDuration;->hours:I

    if-eqz v1, :cond_3

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    iget v2, p0, Lgroovy/time/BaseDuration;->hours:I

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v2, " hours"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 107
    :cond_3
    iget v1, p0, Lgroovy/time/BaseDuration;->minutes:I

    if-eqz v1, :cond_4

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    iget v2, p0, Lgroovy/time/BaseDuration;->minutes:I

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v2, " minutes"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 109
    :cond_4
    iget v1, p0, Lgroovy/time/BaseDuration;->seconds:I

    const-string v2, "0"

    if-nez v1, :cond_5

    iget v3, p0, Lgroovy/time/BaseDuration;->millis:I

    if-eqz v3, :cond_8

    .line 110
    :cond_5
    iget v3, p0, Lgroovy/time/BaseDuration;->millis:I

    rem-int/lit16 v4, v3, 0x3e8

    sub-int/2addr v3, v4

    .line 111
    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v3

    const/16 v5, 0x3e8

    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v5

    invoke-static {v3, v5}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->intdiv(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/Number;->intValue()I

    move-result v3

    add-int/2addr v1, v3

    .line 112
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v5, ""

    invoke-virtual {v3, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-static {v4}, Ljava/lang/Math;->abs(I)I

    move-result v5

    invoke-virtual {v3, v5}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    .line 113
    new-instance v5, Ljava/lang/StringBuilder;

    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    if-nez v1, :cond_7

    if-gez v4, :cond_6

    const-string v1, "-0"

    goto :goto_0

    :cond_6
    move-object v1, v2

    goto :goto_0

    :cond_7
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    :goto_0
    invoke-virtual {v5, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v4, "."

    invoke-virtual {v1, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    const/4 v4, 0x3

    invoke-static {v4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v4

    invoke-static {v3, v4, v2}, Lorg/codehaus/groovy/runtime/StringGroovyMethods;->padLeft(Ljava/lang/CharSequence;Ljava/lang/Number;Ljava/lang/CharSequence;)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v3, " seconds"

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 116
    :cond_8
    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v1

    if-nez v1, :cond_9

    .line 117
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    const-string v1, ", "

    invoke-static {v0, v1}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->join(Ljava/util/Iterator;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    return-object v0

    :cond_9
    return-object v2
.end method
