.class public Lgroovy/time/TimeCategory;
.super Ljava/lang/Object;
.source "TimeCategory.java"


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 41
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static getDay(Ljava/lang/Integer;)Lgroovy/time/Duration;
    .locals 0

    .line 169
    invoke-static {p0}, Lgroovy/time/TimeCategory;->getDays(Ljava/lang/Integer;)Lgroovy/time/Duration;

    move-result-object p0

    return-object p0
.end method

.method public static getDaylightSavingsOffset(Lgroovy/time/BaseDuration;)Lgroovy/time/Duration;
    .locals 5

    .line 93
    new-instance v0, Ljava/util/Date;

    invoke-virtual {p0}, Lgroovy/time/BaseDuration;->toMilliseconds()J

    move-result-wide v1

    const-wide/16 v3, 0x1

    add-long/2addr v1, v3

    invoke-direct {v0, v1, v2}, Ljava/util/Date;-><init>(J)V

    invoke-static {v0}, Lgroovy/time/TimeCategory;->getDaylightSavingsOffset(Ljava/util/Date;)Lgroovy/time/Duration;

    move-result-object p0

    return-object p0
.end method

.method public static getDaylightSavingsOffset(Ljava/util/Date;)Lgroovy/time/Duration;
    .locals 3

    .line 86
    invoke-static {p0}, Lgroovy/time/TimeCategory;->getTimeZone(Ljava/util/Date;)Ljava/util/TimeZone;

    move-result-object v0

    .line 87
    invoke-virtual {v0}, Ljava/util/TimeZone;->useDaylightTime()Z

    move-result v1

    const/4 v2, 0x0

    if-eqz v1, :cond_0

    invoke-virtual {v0, p0}, Ljava/util/TimeZone;->inDaylightTime(Ljava/util/Date;)Z

    move-result p0

    if-eqz p0, :cond_0

    .line 88
    invoke-virtual {v0}, Ljava/util/TimeZone;->getDSTSavings()I

    move-result p0

    goto :goto_0

    :cond_0
    move p0, v2

    .line 89
    :goto_0
    new-instance v0, Lgroovy/time/TimeDuration;

    invoke-direct {v0, v2, v2, v2, p0}, Lgroovy/time/TimeDuration;-><init>(IIII)V

    return-object v0
.end method

.method public static getDays(Ljava/lang/Integer;)Lgroovy/time/Duration;
    .locals 7

    .line 165
    new-instance v6, Lgroovy/time/Duration;

    invoke-virtual {p0}, Ljava/lang/Integer;->intValue()I

    move-result v1

    const/4 v2, 0x0

    const/4 v3, 0x0

    const/4 v4, 0x0

    const/4 v5, 0x0

    move-object v0, v6

    invoke-direct/range {v0 .. v5}, Lgroovy/time/Duration;-><init>(IIIII)V

    return-object v6
.end method

.method public static getHour(Ljava/lang/Integer;)Lgroovy/time/TimeDuration;
    .locals 0

    .line 177
    invoke-static {p0}, Lgroovy/time/TimeCategory;->getHours(Ljava/lang/Integer;)Lgroovy/time/TimeDuration;

    move-result-object p0

    return-object p0
.end method

.method public static getHours(Ljava/lang/Integer;)Lgroovy/time/TimeDuration;
    .locals 7

    .line 173
    new-instance v6, Lgroovy/time/TimeDuration;

    invoke-virtual {p0}, Ljava/lang/Integer;->intValue()I

    move-result v2

    const/4 v1, 0x0

    const/4 v3, 0x0

    const/4 v4, 0x0

    const/4 v5, 0x0

    move-object v0, v6

    invoke-direct/range {v0 .. v5}, Lgroovy/time/TimeDuration;-><init>(IIIII)V

    return-object v6
.end method

.method public static getMillisecond(Ljava/lang/Integer;)Lgroovy/time/TimeDuration;
    .locals 0

    .line 201
    invoke-static {p0}, Lgroovy/time/TimeCategory;->getMilliseconds(Ljava/lang/Integer;)Lgroovy/time/TimeDuration;

    move-result-object p0

    return-object p0
.end method

.method public static getMilliseconds(Ljava/lang/Integer;)Lgroovy/time/TimeDuration;
    .locals 7

    .line 197
    new-instance v6, Lgroovy/time/TimeDuration;

    invoke-virtual {p0}, Ljava/lang/Integer;->intValue()I

    move-result v5

    const/4 v1, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    const/4 v4, 0x0

    move-object v0, v6

    invoke-direct/range {v0 .. v5}, Lgroovy/time/TimeDuration;-><init>(IIIII)V

    return-object v6
.end method

.method public static getMinute(Ljava/lang/Integer;)Lgroovy/time/TimeDuration;
    .locals 0

    .line 185
    invoke-static {p0}, Lgroovy/time/TimeCategory;->getMinutes(Ljava/lang/Integer;)Lgroovy/time/TimeDuration;

    move-result-object p0

    return-object p0
.end method

.method public static getMinutes(Ljava/lang/Integer;)Lgroovy/time/TimeDuration;
    .locals 7

    .line 181
    new-instance v6, Lgroovy/time/TimeDuration;

    invoke-virtual {p0}, Ljava/lang/Integer;->intValue()I

    move-result v3

    const/4 v1, 0x0

    const/4 v2, 0x0

    const/4 v4, 0x0

    const/4 v5, 0x0

    move-object v0, v6

    invoke-direct/range {v0 .. v5}, Lgroovy/time/TimeDuration;-><init>(IIIII)V

    return-object v6
.end method

.method public static getMonth(Ljava/lang/Integer;)Lgroovy/time/DatumDependentDuration;
    .locals 0

    .line 141
    invoke-static {p0}, Lgroovy/time/TimeCategory;->getMonths(Ljava/lang/Integer;)Lgroovy/time/DatumDependentDuration;

    move-result-object p0

    return-object p0
.end method

.method public static getMonths(Ljava/lang/Integer;)Lgroovy/time/DatumDependentDuration;
    .locals 9

    .line 137
    new-instance v8, Lgroovy/time/DatumDependentDuration;

    invoke-virtual {p0}, Ljava/lang/Integer;->intValue()I

    move-result v2

    const/4 v1, 0x0

    const/4 v3, 0x0

    const/4 v4, 0x0

    const/4 v5, 0x0

    const/4 v6, 0x0

    const/4 v7, 0x0

    move-object v0, v8

    invoke-direct/range {v0 .. v7}, Lgroovy/time/DatumDependentDuration;-><init>(IIIIIII)V

    return-object v8
.end method

.method public static getRelativeDaylightSavingsOffset(Ljava/util/Date;Ljava/util/Date;)Lgroovy/time/Duration;
    .locals 3

    .line 106
    invoke-static {p0}, Lgroovy/time/TimeCategory;->getDaylightSavingsOffset(Ljava/util/Date;)Lgroovy/time/Duration;

    move-result-object p0

    .line 107
    invoke-static {p1}, Lgroovy/time/TimeCategory;->getDaylightSavingsOffset(Ljava/util/Date;)Lgroovy/time/Duration;

    move-result-object p1

    .line 108
    new-instance v0, Lgroovy/time/TimeDuration;

    invoke-virtual {p1}, Lgroovy/time/Duration;->toMilliseconds()J

    move-result-wide v1

    invoke-virtual {p0}, Lgroovy/time/Duration;->toMilliseconds()J

    move-result-wide p0

    sub-long/2addr v1, p0

    long-to-int p0, v1

    const/4 p1, 0x0

    invoke-direct {v0, p1, p1, p1, p0}, Lgroovy/time/TimeDuration;-><init>(IIII)V

    return-object v0
.end method

.method public static getSecond(Ljava/lang/Integer;)Lgroovy/time/TimeDuration;
    .locals 0

    .line 193
    invoke-static {p0}, Lgroovy/time/TimeCategory;->getSeconds(Ljava/lang/Integer;)Lgroovy/time/TimeDuration;

    move-result-object p0

    return-object p0
.end method

.method public static getSeconds(Ljava/lang/Integer;)Lgroovy/time/TimeDuration;
    .locals 7

    .line 189
    new-instance v6, Lgroovy/time/TimeDuration;

    invoke-virtual {p0}, Ljava/lang/Integer;->intValue()I

    move-result v4

    const/4 v1, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    const/4 v5, 0x0

    move-object v0, v6

    invoke-direct/range {v0 .. v5}, Lgroovy/time/TimeDuration;-><init>(IIIII)V

    return-object v6
.end method

.method public static getTimeZone(Ljava/util/Date;)Ljava/util/TimeZone;
    .locals 1
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    .line 74
    invoke-static {}, Ljava/util/Calendar;->getInstance()Ljava/util/Calendar;

    move-result-object v0

    .line 75
    invoke-virtual {v0, p0}, Ljava/util/Calendar;->setTime(Ljava/util/Date;)V

    .line 76
    invoke-virtual {v0}, Ljava/util/Calendar;->getTimeZone()Ljava/util/TimeZone;

    move-result-object p0

    return-object p0
.end method

.method public static getWeek(Ljava/lang/Integer;)Lgroovy/time/Duration;
    .locals 0

    .line 161
    invoke-static {p0}, Lgroovy/time/TimeCategory;->getWeeks(Ljava/lang/Integer;)Lgroovy/time/Duration;

    move-result-object p0

    return-object p0
.end method

.method public static getWeeks(Ljava/lang/Integer;)Lgroovy/time/Duration;
    .locals 7

    .line 157
    new-instance v6, Lgroovy/time/Duration;

    invoke-virtual {p0}, Ljava/lang/Integer;->intValue()I

    move-result p0

    mul-int/lit8 v1, p0, 0x7

    const/4 v2, 0x0

    const/4 v3, 0x0

    const/4 v4, 0x0

    const/4 v5, 0x0

    move-object v0, v6

    invoke-direct/range {v0 .. v5}, Lgroovy/time/Duration;-><init>(IIIII)V

    return-object v6
.end method

.method public static getYear(Ljava/lang/Integer;)Lgroovy/time/DatumDependentDuration;
    .locals 0

    .line 149
    invoke-static {p0}, Lgroovy/time/TimeCategory;->getYears(Ljava/lang/Integer;)Lgroovy/time/DatumDependentDuration;

    move-result-object p0

    return-object p0
.end method

.method public static getYears(Ljava/lang/Integer;)Lgroovy/time/DatumDependentDuration;
    .locals 9

    .line 145
    new-instance v8, Lgroovy/time/DatumDependentDuration;

    invoke-virtual {p0}, Ljava/lang/Integer;->intValue()I

    move-result v1

    const/4 v2, 0x0

    const/4 v3, 0x0

    const/4 v4, 0x0

    const/4 v5, 0x0

    const/4 v6, 0x0

    const/4 v7, 0x0

    move-object v0, v8

    invoke-direct/range {v0 .. v7}, Lgroovy/time/DatumDependentDuration;-><init>(IIIIIII)V

    return-object v8
.end method

.method public static minus(Ljava/util/Date;Ljava/util/Date;)Lgroovy/time/TimeDuration;
    .locals 12

    .line 119
    invoke-virtual {p0}, Ljava/util/Date;->getTime()J

    move-result-wide v0

    invoke-virtual {p1}, Ljava/util/Date;->getTime()J

    move-result-wide p0

    sub-long/2addr v0, p0

    const-wide/32 p0, 0x5265c00

    .line 120
    div-long p0, v0, p0

    const-wide/16 v2, 0x18

    mul-long/2addr v2, p0

    const-wide/16 v4, 0x3c

    mul-long/2addr v2, v4

    mul-long/2addr v2, v4

    const-wide/16 v4, 0x3e8

    mul-long/2addr v2, v4

    sub-long/2addr v0, v2

    const-wide/32 v2, 0x36ee80

    .line 122
    div-long v2, v0, v2

    long-to-int v8, v2

    mul-int/lit8 v2, v8, 0x3c

    mul-int/lit8 v2, v2, 0x3c

    mul-int/lit16 v2, v2, 0x3e8

    int-to-long v2, v2

    sub-long/2addr v0, v2

    const-wide/32 v2, 0xea60

    .line 124
    div-long v2, v0, v2

    long-to-int v9, v2

    mul-int/lit8 v2, v9, 0x3c

    mul-int/lit16 v2, v2, 0x3e8

    int-to-long v2, v2

    sub-long/2addr v0, v2

    .line 126
    div-long v2, v0, v4

    long-to-int v10, v2

    mul-int/lit16 v2, v10, 0x3e8

    int-to-long v2, v2

    sub-long/2addr v0, v2

    .line 129
    new-instance v2, Lgroovy/time/TimeDuration;

    long-to-int v7, p0

    long-to-int v11, v0

    move-object v6, v2

    invoke-direct/range {v6 .. v11}, Lgroovy/time/TimeDuration;-><init>(IIIII)V

    return-object v2
.end method

.method public static minus(Ljava/util/Date;Lgroovy/time/BaseDuration;)Ljava/util/Date;
    .locals 2

    .line 51
    invoke-static {}, Ljava/util/Calendar;->getInstance()Ljava/util/Calendar;

    move-result-object v0

    .line 53
    invoke-virtual {v0, p0}, Ljava/util/Calendar;->setTime(Ljava/util/Date;)V

    .line 54
    invoke-virtual {p1}, Lgroovy/time/BaseDuration;->getYears()I

    move-result p0

    neg-int p0, p0

    const/4 v1, 0x1

    invoke-virtual {v0, v1, p0}, Ljava/util/Calendar;->add(II)V

    .line 55
    invoke-virtual {p1}, Lgroovy/time/BaseDuration;->getMonths()I

    move-result p0

    neg-int p0, p0

    const/4 v1, 0x2

    invoke-virtual {v0, v1, p0}, Ljava/util/Calendar;->add(II)V

    .line 56
    invoke-virtual {p1}, Lgroovy/time/BaseDuration;->getDays()I

    move-result p0

    neg-int p0, p0

    const/4 v1, 0x6

    invoke-virtual {v0, v1, p0}, Ljava/util/Calendar;->add(II)V

    .line 57
    invoke-virtual {p1}, Lgroovy/time/BaseDuration;->getHours()I

    move-result p0

    neg-int p0, p0

    const/16 v1, 0xb

    invoke-virtual {v0, v1, p0}, Ljava/util/Calendar;->add(II)V

    .line 58
    invoke-virtual {p1}, Lgroovy/time/BaseDuration;->getMinutes()I

    move-result p0

    neg-int p0, p0

    const/16 v1, 0xc

    invoke-virtual {v0, v1, p0}, Ljava/util/Calendar;->add(II)V

    .line 59
    invoke-virtual {p1}, Lgroovy/time/BaseDuration;->getSeconds()I

    move-result p0

    neg-int p0, p0

    const/16 v1, 0xd

    invoke-virtual {v0, v1, p0}, Ljava/util/Calendar;->add(II)V

    .line 60
    invoke-virtual {p1}, Lgroovy/time/BaseDuration;->getMillis()I

    move-result p0

    neg-int p0, p0

    const/16 p1, 0xe

    invoke-virtual {v0, p1, p0}, Ljava/util/Calendar;->add(II)V

    .line 62
    invoke-virtual {v0}, Ljava/util/Calendar;->getTime()Ljava/util/Date;

    move-result-object p0

    return-object p0
.end method

.method public static plus(Ljava/util/Date;Lgroovy/time/BaseDuration;)Ljava/util/Date;
    .locals 0

    .line 47
    invoke-virtual {p1, p0}, Lgroovy/time/BaseDuration;->plus(Ljava/util/Date;)Ljava/util/Date;

    move-result-object p0

    return-object p0
.end method
