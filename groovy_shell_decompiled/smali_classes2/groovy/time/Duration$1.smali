.class Lgroovy/time/Duration$1;
.super Lgroovy/time/BaseDuration$From;
.source "Duration.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lgroovy/time/Duration;->getFrom()Lgroovy/time/BaseDuration$From;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lgroovy/time/Duration;


# direct methods
.method constructor <init>(Lgroovy/time/Duration;)V
    .locals 0

    .line 102
    iput-object p1, p0, Lgroovy/time/Duration$1;->this$0:Lgroovy/time/Duration;

    invoke-direct {p0}, Lgroovy/time/BaseDuration$From;-><init>()V

    return-void
.end method


# virtual methods
.method public getNow()Ljava/util/Date;
    .locals 4

    .line 104
    invoke-static {}, Ljava/util/Calendar;->getInstance()Ljava/util/Calendar;

    move-result-object v0

    .line 106
    iget-object v1, p0, Lgroovy/time/Duration$1;->this$0:Lgroovy/time/Duration;

    invoke-virtual {v1}, Lgroovy/time/Duration;->getDays()I

    move-result v1

    const/4 v2, 0x6

    invoke-virtual {v0, v2, v1}, Ljava/util/Calendar;->add(II)V

    const/16 v1, 0xb

    const/4 v2, 0x0

    .line 108
    invoke-virtual {v0, v1, v2}, Ljava/util/Calendar;->set(II)V

    const/16 v1, 0xc

    .line 109
    invoke-virtual {v0, v1, v2}, Ljava/util/Calendar;->set(II)V

    const/16 v1, 0xd

    .line 110
    invoke-virtual {v0, v1, v2}, Ljava/util/Calendar;->set(II)V

    const/16 v1, 0xe

    .line 111
    invoke-virtual {v0, v1, v2}, Ljava/util/Calendar;->set(II)V

    .line 113
    new-instance v1, Ljava/util/Date;

    invoke-virtual {v0}, Ljava/util/Calendar;->getTimeInMillis()J

    move-result-wide v2

    invoke-direct {v1, v2, v3}, Ljava/util/Date;-><init>(J)V

    return-object v1
.end method
