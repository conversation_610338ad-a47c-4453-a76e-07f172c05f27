.class Lgroovy/time/DatumDependentDuration$1;
.super Lgroovy/time/BaseDuration$From;
.source "DatumDependentDuration.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lgroovy/time/DatumDependentDuration;->getFrom()Lgroovy/time/BaseDuration$From;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lgroovy/time/DatumDependentDuration;


# direct methods
.method constructor <init>(Lgroovy/time/DatumDependentDuration;)V
    .locals 0

    .line 118
    iput-object p1, p0, Lgroovy/time/DatumDependentDuration$1;->this$0:Lgroovy/time/DatumDependentDuration;

    invoke-direct {p0}, Lgroovy/time/BaseDuration$From;-><init>()V

    return-void
.end method


# virtual methods
.method public getNow()Ljava/util/Date;
    .locals 6

    .line 120
    invoke-static {}, Ljava/util/Calendar;->getInstance()Ljava/util/Calendar;

    move-result-object v0

    .line 122
    iget-object v1, p0, Lgroovy/time/DatumDependentDuration$1;->this$0:Lgroovy/time/DatumDependentDuration;

    invoke-virtual {v1}, Lgroovy/time/DatumDependentDuration;->getYears()I

    move-result v1

    const/4 v2, 0x1

    invoke-virtual {v0, v2, v1}, Ljava/util/Calendar;->add(II)V

    .line 123
    iget-object v1, p0, Lgroovy/time/DatumDependentDuration$1;->this$0:Lgroovy/time/DatumDependentDuration;

    invoke-virtual {v1}, Lgroovy/time/DatumDependentDuration;->getMonths()I

    move-result v1

    const/4 v2, 0x2

    invoke-virtual {v0, v2, v1}, Ljava/util/Calendar;->add(II)V

    .line 124
    iget-object v1, p0, Lgroovy/time/DatumDependentDuration$1;->this$0:Lgroovy/time/DatumDependentDuration;

    invoke-virtual {v1}, Lgroovy/time/DatumDependentDuration;->getDays()I

    move-result v1

    const/4 v2, 0x6

    invoke-virtual {v0, v2, v1}, Ljava/util/Calendar;->add(II)V

    .line 125
    iget-object v1, p0, Lgroovy/time/DatumDependentDuration$1;->this$0:Lgroovy/time/DatumDependentDuration;

    invoke-virtual {v1}, Lgroovy/time/DatumDependentDuration;->getHours()I

    move-result v1

    const/16 v2, 0xb

    invoke-virtual {v0, v2, v1}, Ljava/util/Calendar;->add(II)V

    .line 126
    iget-object v1, p0, Lgroovy/time/DatumDependentDuration$1;->this$0:Lgroovy/time/DatumDependentDuration;

    invoke-virtual {v1}, Lgroovy/time/DatumDependentDuration;->getMinutes()I

    move-result v1

    const/16 v3, 0xc

    invoke-virtual {v0, v3, v1}, Ljava/util/Calendar;->add(II)V

    .line 127
    iget-object v1, p0, Lgroovy/time/DatumDependentDuration$1;->this$0:Lgroovy/time/DatumDependentDuration;

    invoke-virtual {v1}, Lgroovy/time/DatumDependentDuration;->getSeconds()I

    move-result v1

    const/16 v4, 0xd

    invoke-virtual {v0, v4, v1}, Ljava/util/Calendar;->add(II)V

    .line 128
    iget-object v1, p0, Lgroovy/time/DatumDependentDuration$1;->this$0:Lgroovy/time/DatumDependentDuration;

    invoke-virtual {v1}, Lgroovy/time/DatumDependentDuration;->getMillis()I

    move-result v1

    const/16 v5, 0xe

    invoke-virtual {v0, v5, v1}, Ljava/util/Calendar;->add(II)V

    const/4 v1, 0x0

    .line 130
    invoke-virtual {v0, v2, v1}, Ljava/util/Calendar;->set(II)V

    .line 131
    invoke-virtual {v0, v3, v1}, Ljava/util/Calendar;->set(II)V

    .line 132
    invoke-virtual {v0, v4, v1}, Ljava/util/Calendar;->set(II)V

    .line 133
    invoke-virtual {v0, v5, v1}, Ljava/util/Calendar;->set(II)V

    .line 135
    new-instance v1, Ljava/util/Date;

    invoke-virtual {v0}, Ljava/util/Calendar;->getTimeInMillis()J

    move-result-wide v2

    invoke-direct {v1, v2, v3}, Ljava/util/Date;-><init>(J)V

    return-object v1
.end method
