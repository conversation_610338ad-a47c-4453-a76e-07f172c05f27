.class public Lgroovy/beans/ListenerListASTTransformation;
.super Ljava/lang/Object;
.source "ListenerListASTTransformation.groovy"

# interfaces
.implements Lorg/codehaus/groovy/transform/ASTTransformation;
.implements Lgroovyjarjarasm/asm/Opcodes;
.implements Lgroovy/lang/GroovyObject;


# annotations
.annotation runtime Lorg/codehaus/groovy/transform/GroovyASTTransformation;
    phase = .enum Lorg/codehaus/groovy/control/CompilePhase;->CANONICALIZATION:Lorg/codehaus/groovy/control/CompilePhase;
.end annotation


# static fields
.field private static synthetic $callSiteArray:Ljava/lang/ref/SoftReference;

.field private static synthetic $staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

.field private static synthetic $staticClassInfo$:Lorg/codehaus/groovy/reflection/ClassInfo;

.field private static final COLLECTION_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

.field private static final MY_CLASS:Ljava/lang/Class;

.field public static transient synthetic __$stMC:Z


# instance fields
.field private transient synthetic metaClass:Lgroovy/lang/MetaClass;


# direct methods
.method private static synthetic $createCallSiteArray()Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;
    .locals 3

    const/16 v0, 0xde

    new-array v0, v0, [Ljava/lang/String;

    invoke-static {v0}, Lgroovy/beans/ListenerListASTTransformation;->$createCallSiteArray_1([Ljava/lang/String;)V

    new-instance v1, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;

    const-class v2, Lgroovy/beans/ListenerListASTTransformation;

    invoke-direct {v1, v2, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;-><init>(Ljava/lang/Class;[Ljava/lang/String;)V

    return-object v1
.end method

.method private static synthetic $createCallSiteArray_1([Ljava/lang/String;)V
    .locals 20

    const/4 v0, 0x0

    const-string v1, "getAt"

    aput-object v1, p0, v0

    const/4 v0, 0x1

    aput-object v1, p0, v0

    const/4 v0, 0x2

    const-string v2, "<$constructor$>"

    aput-object v2, p0, v0

    const/4 v0, 0x3

    const-string v3, "class"

    aput-object v3, p0, v0

    const/4 v0, 0x4

    const-string v4, "node"

    aput-object v4, p0, v0

    const/4 v0, 0x5

    aput-object v3, p0, v0

    const/4 v0, 0x6

    const-string v4, "parent"

    aput-object v4, p0, v0

    const/4 v0, 0x7

    aput-object v2, p0, v0

    const/16 v0, 0x8

    aput-object v3, p0, v0

    const/16 v0, 0x9

    const-string v4, "node"

    aput-object v4, p0, v0

    const/16 v0, 0xa

    aput-object v3, p0, v0

    const/16 v0, 0xb

    const-string v3, "parent"

    aput-object v3, p0, v0

    const/16 v0, 0xc

    aput-object v1, p0, v0

    const/16 v0, 0xd

    aput-object v1, p0, v0

    const/16 v0, 0xe

    const-string v3, "declaringClass"

    aput-object v3, p0, v0

    const/16 v0, 0xf

    aput-object v1, p0, v0

    const/16 v0, 0x10

    const-string v3, "declaringClass"

    aput-object v3, p0, v0

    const/16 v0, 0x11

    const-string v3, "type"

    aput-object v3, p0, v0

    const/16 v0, 0x12

    const-string v4, "isDerivedFrom"

    aput-object v4, p0, v0

    const/16 v0, 0x13

    const-string v4, "implementsInterface"

    aput-object v4, p0, v0

    const/16 v0, 0x14

    const-string v4, "isDerivedFrom"

    aput-object v4, p0, v0

    const/16 v0, 0x15

    const-string v4, "implementsInterface"

    aput-object v4, p0, v0

    const/16 v0, 0x16

    const-string v4, "addError"

    aput-object v4, p0, v0

    const/16 v0, 0x17

    const-string v5, "plus"

    aput-object v5, p0, v0

    const/16 v0, 0x18

    aput-object v5, p0, v0

    const/16 v0, 0x19

    const-string v6, "name"

    aput-object v6, p0, v0

    const/16 v0, 0x1a

    const-string v7, "genericsTypes"

    aput-object v7, p0, v0

    const/16 v0, 0x1b

    aput-object v3, p0, v0

    const/16 v0, 0x1c

    aput-object v4, p0, v0

    const/16 v0, 0x1d

    aput-object v5, p0, v0

    const/16 v0, 0x1e

    aput-object v5, p0, v0

    const/16 v0, 0x1f

    aput-object v6, p0, v0

    const/16 v0, 0x20

    const-string v7, "wildcard"

    aput-object v7, p0, v0

    const/16 v0, 0x21

    aput-object v1, p0, v0

    const/16 v0, 0x22

    aput-object v4, p0, v0

    const/16 v0, 0x23

    aput-object v5, p0, v0

    const/16 v0, 0x24

    aput-object v5, p0, v0

    const/16 v0, 0x25

    aput-object v6, p0, v0

    const/16 v0, 0x26

    aput-object v3, p0, v0

    const/16 v0, 0x27

    aput-object v1, p0, v0

    const/16 v0, 0x28

    const-string v1, "initialValueExpression"

    aput-object v1, p0, v0

    const/16 v0, 0x29

    aput-object v2, p0, v0

    const/16 v0, 0x2a

    const-string v1, "value"

    aput-object v1, p0, v0

    const/16 v0, 0x2b

    const-string v1, "getMember"

    aput-object v1, p0, v0

    const/16 v0, 0x2c

    const-string v1, "nameWithoutPackage"

    aput-object v1, p0, v0

    const/16 v0, 0x2d

    const-string v1, "findAll"

    aput-object v1, p0, v0

    const/16 v0, 0x2e

    const-string v1, "methods"

    aput-object v1, p0, v0

    const/16 v0, 0x2f

    const-string v1, "value"

    aput-object v1, p0, v0

    const/16 v0, 0x30

    const-string v1, "getMember"

    aput-object v1, p0, v0

    const/16 v0, 0x31

    const-string v1, "addAddListener"

    aput-object v1, p0, v0

    const/16 v0, 0x32

    const-string v1, "addRemoveListener"

    aput-object v1, p0, v0

    const/16 v0, 0x33

    const-string v1, "addGetListeners"

    aput-object v1, p0, v0

    const/16 v0, 0x34

    const-string v1, "each"

    aput-object v1, p0, v0

    const/16 v0, 0x35

    aput-object v4, p0, v0

    const/16 v0, 0x36

    const-string v1, "errorCollector"

    aput-object v1, p0, v0

    const/16 v0, 0x37

    aput-object v2, p0, v0

    const/16 v0, 0x38

    aput-object v2, p0, v0

    const/16 v0, 0x39

    const-string v1, "lineNumber"

    aput-object v1, p0, v0

    const/16 v0, 0x3a

    const-string v1, "columnNumber"

    aput-object v1, p0, v0

    const/16 v0, 0x3b

    const-string v1, "or"

    aput-object v1, p0, v0

    const/16 v0, 0x3c

    const-string v3, "ACC_PUBLIC"

    aput-object v3, p0, v0

    const/16 v0, 0x3d

    const-string v5, "ACC_SYNCHRONIZED"

    aput-object v5, p0, v0

    const/16 v0, 0x3e

    aput-object v3, p0, v0

    const/16 v0, 0x3f

    aput-object v3, p0, v0

    const/16 v0, 0x40

    aput-object v5, p0, v0

    const/16 v0, 0x41

    aput-object v3, p0, v0

    const/16 v0, 0x42

    const-string v7, "make"

    aput-object v7, p0, v0

    const/16 v0, 0x43

    const-string v8, "TYPE"

    aput-object v8, p0, v0

    const/16 v0, 0x44

    const-string v9, "capitalize"

    aput-object v9, p0, v0

    const/16 v0, 0x45

    const-string v10, "makeWithoutCaching"

    aput-object v10, p0, v0

    const/16 v0, 0x46

    aput-object v6, p0, v0

    const/16 v0, 0x47

    const-string v10, "param"

    aput-object v10, p0, v0

    const/16 v0, 0x48

    const-string v10, "hasMethod"

    aput-object v10, p0, v0

    const/16 v0, 0x49

    aput-object v4, p0, v0

    const/16 v0, 0x4a

    aput-object v6, p0, v0

    const/16 v0, 0x4b

    aput-object v6, p0, v0

    const/16 v0, 0x4c

    aput-object v2, p0, v0

    const/16 v0, 0x4d

    const-string v11, "addStatements"

    aput-object v11, p0, v0

    const/16 v0, 0x4e

    const-string v12, "ifS"

    aput-object v12, p0, v0

    const/16 v0, 0x4f

    const-string v13, "equalsNullX"

    aput-object v13, p0, v0

    const/16 v0, 0x50

    const-string v14, "varX"

    aput-object v14, p0, v0

    const/16 v0, 0x51

    const-string v15, "RETURN_NULL_OR_VOID"

    aput-object v15, p0, v0

    const/16 v0, 0x52

    aput-object v12, p0, v0

    const/16 v0, 0x53

    aput-object v13, p0, v0

    const/16 v0, 0x54

    aput-object v14, p0, v0

    const/16 v0, 0x55

    aput-object v6, p0, v0

    const/16 v0, 0x56

    const-string v15, "assignS"

    aput-object v15, p0, v0

    const/16 v0, 0x57

    aput-object v14, p0, v0

    const/16 v0, 0x58

    aput-object v6, p0, v0

    const/16 v0, 0x59

    aput-object v2, p0, v0

    const/16 v0, 0x5a

    const-string v15, "stmt"

    aput-object v15, p0, v0

    const/16 v0, 0x5b

    const-string v16, "callX"

    aput-object v16, p0, v0

    const/16 v0, 0x5c

    aput-object v14, p0, v0

    const/16 v0, 0x5d

    aput-object v6, p0, v0

    const/16 v0, 0x5e

    const-string v17, "constX"

    aput-object v17, p0, v0

    const/16 v0, 0x5f

    const-string v17, "args"

    aput-object v17, p0, v0

    const/16 v0, 0x60

    aput-object v14, p0, v0

    const/16 v0, 0x61

    const-string v18, "addMethod"

    aput-object v18, p0, v0

    const/16 v0, 0x62

    aput-object v2, p0, v0

    const/16 v0, 0x63

    aput-object v1, p0, v0

    const/16 v0, 0x64

    aput-object v3, p0, v0

    const/16 v0, 0x65

    aput-object v5, p0, v0

    const/16 v0, 0x66

    aput-object v3, p0, v0

    const/16 v0, 0x67

    aput-object v3, p0, v0

    const/16 v0, 0x68

    aput-object v5, p0, v0

    const/16 v0, 0x69

    aput-object v3, p0, v0

    const/16 v0, 0x6a

    aput-object v7, p0, v0

    const/16 v0, 0x6b

    aput-object v8, p0, v0

    const/16 v0, 0x6c

    aput-object v9, p0, v0

    const/16 v0, 0x6d

    const-string v19, "makeWithoutCaching"

    aput-object v19, p0, v0

    const/16 v0, 0x6e

    aput-object v6, p0, v0

    const/16 v0, 0x6f

    const-string v19, "param"

    aput-object v19, p0, v0

    const/16 v0, 0x70

    aput-object v10, p0, v0

    const/16 v0, 0x71

    aput-object v4, p0, v0

    const/16 v0, 0x72

    aput-object v6, p0, v0

    const/16 v0, 0x73

    aput-object v6, p0, v0

    const/16 v0, 0x74

    aput-object v2, p0, v0

    const/16 v0, 0x75

    aput-object v11, p0, v0

    const/16 v0, 0x76

    aput-object v12, p0, v0

    const/16 v0, 0x77

    aput-object v13, p0, v0

    const/16 v0, 0x78

    aput-object v14, p0, v0

    const/16 v0, 0x79

    const-string v19, "RETURN_NULL_OR_VOID"

    aput-object v19, p0, v0

    const/16 v0, 0x7a

    aput-object v12, p0, v0

    const/16 v0, 0x7b

    aput-object v13, p0, v0

    const/16 v0, 0x7c

    aput-object v14, p0, v0

    const/16 v0, 0x7d

    aput-object v6, p0, v0

    const/16 v0, 0x7e

    const-string v13, "assignS"

    aput-object v13, p0, v0

    const/16 v0, 0x7f

    aput-object v14, p0, v0

    const/16 v0, 0x80

    aput-object v6, p0, v0

    const/16 v0, 0x81

    aput-object v2, p0, v0

    const/16 v0, 0x82

    aput-object v15, p0, v0

    const/16 v0, 0x83

    aput-object v16, p0, v0

    const/16 v0, 0x84

    aput-object v14, p0, v0

    const/16 v0, 0x85

    aput-object v6, p0, v0

    const/16 v0, 0x86

    const-string v13, "constX"

    aput-object v13, p0, v0

    const/16 v0, 0x87

    aput-object v17, p0, v0

    const/16 v0, 0x88

    aput-object v14, p0, v0

    const/16 v0, 0x89

    aput-object v18, p0, v0

    const/16 v0, 0x8a

    aput-object v2, p0, v0

    const/16 v0, 0x8b

    aput-object v1, p0, v0

    const/16 v0, 0x8c

    aput-object v3, p0, v0

    const/16 v0, 0x8d

    aput-object v5, p0, v0

    const/16 v0, 0x8e

    aput-object v3, p0, v0

    const/16 v0, 0x8f

    aput-object v3, p0, v0

    const/16 v0, 0x90

    aput-object v5, p0, v0

    const/16 v0, 0x91

    aput-object v3, p0, v0

    const/16 v0, 0x92

    const-string v13, "makeArray"

    aput-object v13, p0, v0

    const/16 v0, 0x93

    aput-object v9, p0, v0

    const/16 v0, 0x94

    aput-object v10, p0, v0

    const/16 v0, 0x95

    aput-object v4, p0, v0

    const/16 v0, 0x96

    aput-object v6, p0, v0

    const/16 v0, 0x97

    aput-object v6, p0, v0

    const/16 v0, 0x98

    aput-object v2, p0, v0

    const/16 v0, 0x99

    aput-object v11, p0, v0

    const/16 v0, 0x9a

    const-string v13, "declS"

    aput-object v13, p0, v0

    const/16 v0, 0x9b

    const-string v13, "localVarX"

    aput-object v13, p0, v0

    const/16 v0, 0x9c

    const-string v13, "DYNAMIC_TYPE"

    aput-object v13, p0, v0

    const/16 v0, 0x9d

    aput-object v2, p0, v0

    const/16 v0, 0x9e

    aput-object v12, p0, v0

    const/16 v0, 0x9f

    const-string v13, "notNullX"

    aput-object v13, p0, v0

    const/16 v0, 0xa0

    aput-object v14, p0, v0

    const/16 v0, 0xa1

    aput-object v6, p0, v0

    const/16 v0, 0xa2

    aput-object v15, p0, v0

    const/16 v0, 0xa3

    aput-object v16, p0, v0

    const/16 v0, 0xa4

    aput-object v14, p0, v0

    const/16 v0, 0xa5

    const-string v13, "constX"

    aput-object v13, p0, v0

    const/16 v0, 0xa6

    aput-object v17, p0, v0

    const/16 v0, 0xa7

    aput-object v14, p0, v0

    const/16 v0, 0xa8

    aput-object v6, p0, v0

    const/16 v0, 0xa9

    const-string v13, "returnS"

    aput-object v13, p0, v0

    const/16 v0, 0xaa

    const-string v13, "castX"

    aput-object v13, p0, v0

    const/16 v0, 0xab

    aput-object v14, p0, v0

    const/16 v0, 0xac

    aput-object v18, p0, v0

    const/16 v0, 0xad

    aput-object v2, p0, v0

    const/16 v0, 0xae

    aput-object v7, p0, v0

    const/16 v0, 0xaf

    aput-object v8, p0, v0

    const/16 v0, 0xb0

    aput-object v9, p0, v0

    const/16 v0, 0xb1

    aput-object v6, p0, v0

    const/16 v0, 0xb2

    aput-object v1, p0, v0

    const/16 v0, 0xb3

    aput-object v3, p0, v0

    const/16 v0, 0xb4

    aput-object v5, p0, v0

    const/16 v0, 0xb5

    aput-object v3, p0, v0

    const/16 v0, 0xb6

    aput-object v3, p0, v0

    const/16 v0, 0xb7

    aput-object v5, p0, v0

    const/16 v0, 0xb8

    aput-object v3, p0, v0

    const/16 v0, 0xb9

    aput-object v10, p0, v0

    const/16 v0, 0xba

    const-string v1, "parameters"

    aput-object v1, p0, v0

    const/16 v0, 0xbb

    aput-object v4, p0, v0

    const/16 v0, 0xbc

    aput-object v6, p0, v0

    const/16 v0, 0xbd

    aput-object v6, p0, v0

    const/16 v0, 0xbe

    aput-object v17, p0, v0

    const/16 v0, 0xbf

    const-string v1, "parameters"

    aput-object v1, p0, v0

    const/16 v0, 0xc0

    aput-object v2, p0, v0

    const/16 v0, 0xc1

    const-string v1, "plainNodeReference"

    aput-object v1, p0, v0

    const/16 v0, 0xc2

    aput-object v7, p0, v0

    const/16 v0, 0xc3

    aput-object v11, p0, v0

    const/16 v0, 0xc4

    aput-object v12, p0, v0

    const/16 v0, 0xc5

    const-string v1, "notNullX"

    aput-object v1, p0, v0

    const/16 v0, 0xc6

    aput-object v14, p0, v0

    const/16 v0, 0xc7

    aput-object v6, p0, v0

    const/16 v0, 0xc8

    aput-object v2, p0, v0

    const/16 v0, 0xc9

    const-string v1, "declS"

    aput-object v1, p0, v0

    const/16 v0, 0xca

    const-string v1, "localVarX"

    aput-object v1, p0, v0

    const/16 v0, 0xcb

    const-string v1, "ctorX"

    aput-object v1, p0, v0

    const/16 v0, 0xcc

    aput-object v17, p0, v0

    const/16 v0, 0xcd

    aput-object v14, p0, v0

    const/16 v0, 0xce

    aput-object v6, p0, v0

    const/16 v0, 0xcf

    aput-object v2, p0, v0

    const/16 v0, 0xd0

    const-string v1, "param"

    aput-object v1, p0, v0

    const/16 v0, 0xd1

    const-string v1, "DYNAMIC_TYPE"

    aput-object v1, p0, v0

    const/16 v0, 0xd2

    aput-object v14, p0, v0

    const/16 v0, 0xd3

    aput-object v2, p0, v0

    const/16 v0, 0xd4

    aput-object v15, p0, v0

    const/16 v0, 0xd5

    aput-object v16, p0, v0

    const/16 v0, 0xd6

    aput-object v14, p0, v0

    const/16 v0, 0xd7

    aput-object v6, p0, v0

    const/16 v0, 0xd8

    aput-object v2, p0, v0

    const/16 v0, 0xd9

    aput-object v2, p0, v0

    const/16 v0, 0xda

    const-string v1, "collect"

    aput-object v1, p0, v0

    const/16 v0, 0xdb

    const-string v1, "parameters"

    aput-object v1, p0, v0

    const/16 v0, 0xdc

    aput-object v18, p0, v0

    const/16 v0, 0xdd

    aput-object v7, p0, v0

    return-void
.end method

.method private static synthetic $getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;
    .locals 2

    sget-object v0, Lgroovy/beans/ListenerListASTTransformation;->$callSiteArray:Ljava/lang/ref/SoftReference;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Ljava/lang/ref/SoftReference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;

    if-nez v0, :cond_1

    :cond_0
    invoke-static {}, Lgroovy/beans/ListenerListASTTransformation;->$createCallSiteArray()Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;

    move-result-object v0

    new-instance v1, Ljava/lang/ref/SoftReference;

    invoke-direct {v1, v0}, Ljava/lang/ref/SoftReference;-><init>(Ljava/lang/Object;)V

    sput-object v1, Lgroovy/beans/ListenerListASTTransformation;->$callSiteArray:Ljava/lang/ref/SoftReference;

    :cond_1
    iget-object v0, v0, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;->array:[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    return-object v0
.end method

.method static constructor <clinit>()V
    .locals 3

    const-class v0, Lgroovy/beans/ListenerList;

    sput-object v0, Lgroovy/beans/ListenerListASTTransformation;->MY_CLASS:Ljava/lang/Class;

    .line 72
    invoke-static {}, Lgroovy/beans/ListenerListASTTransformation;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v0

    const/16 v1, 0xdd

    aget-object v0, v0, v1

    const-class v1, Lorg/codehaus/groovy/ast/ClassHelper;

    const-class v2, Ljava/util/Collection;

    invoke-interface {v0, v1, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    const-class v1, Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {v0, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/ast/ClassNode;

    sput-object v0, Lgroovy/beans/ListenerListASTTransformation;->COLLECTION_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    return-void
.end method

.method public constructor <init>()V
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    invoke-static {}, Lgroovy/beans/ListenerListASTTransformation;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    invoke-virtual {p0}, Lgroovy/beans/ListenerListASTTransformation;->$getStaticMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    iput-object v0, p0, Lgroovy/beans/ListenerListASTTransformation;->metaClass:Lgroovy/lang/MetaClass;

    return-void
.end method

.method private static addError(Lorg/codehaus/groovy/ast/AnnotationNode;Lorg/codehaus/groovy/control/SourceUnit;Ljava/lang/String;)Ljava/lang/Object;
    .locals 9

    invoke-static {}, Lgroovy/beans/ListenerListASTTransformation;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v0

    const/16 v1, 0x35

    .line 125
    aget-object v1, v0, v1

    const/16 v2, 0x36

    aget-object v2, v0, v2

    invoke-interface {v2, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    const/16 v3, 0x37

    .line 126
    aget-object v3, v0, v3

    const-class v4, Lorg/codehaus/groovy/control/messages/SyntaxErrorMessage;

    const/16 v5, 0x38

    .line 127
    aget-object v5, v0, v5

    const-class v6, Lorg/codehaus/groovy/syntax/SyntaxException;

    const/16 v7, 0x39

    aget-object v7, v0, v7

    invoke-interface {v7, p0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v7

    const/16 v8, 0x3a

    aget-object v0, v0, v8

    invoke-interface {v0, p0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    invoke-interface {v5, v6, p2, v7, p0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    invoke-interface {v3, v4, p0, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    invoke-interface {v1, v2, p0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method protected synthetic $getStaticMetaClass()Lgroovy/lang/MetaClass;
    .locals 2

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    const-class v1, Lgroovy/beans/ListenerListASTTransformation;

    if-eq v0, v1, :cond_0

    invoke-static {p0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->initMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0

    :cond_0
    sget-object v0, Lgroovy/beans/ListenerListASTTransformation;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    if-nez v0, :cond_1

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getClassInfo(Ljava/lang/Class;)Lorg/codehaus/groovy/reflection/ClassInfo;

    move-result-object v0

    sput-object v0, Lgroovy/beans/ListenerListASTTransformation;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    :cond_1
    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0
.end method

.method public addAddListener(Lorg/codehaus/groovy/control/SourceUnit;Lorg/codehaus/groovy/ast/AnnotationNode;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/FieldNode;Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Ljava/lang/Object;)V
    .locals 20

    move-object/from16 v0, p0

    move-object/from16 v1, p3

    move-object/from16 v2, p4

    move-object/from16 v3, p5

    invoke-static {}, Lgroovy/beans/ListenerListASTTransformation;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v4

    .line 144
    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->isOrigInt()Z

    move-result v5

    if-eqz v5, :cond_1

    sget-boolean v5, Lgroovy/beans/ListenerListASTTransformation;->__$stMC:Z

    if-nez v5, :cond_1

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->disabledStandardMetaClass()Z

    move-result v5

    if-nez v5, :cond_1

    invoke-static/range {p7 .. p7}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v5

    if-eqz v5, :cond_0

    const/16 v5, 0x3f

    aget-object v5, v4, v5

    invoke-interface {v5, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGroovyObjectGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    invoke-static {v5}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->intUnbox(Ljava/lang/Object;)I

    move-result v5

    const/16 v6, 0x40

    aget-object v6, v4, v6

    invoke-interface {v6, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGroovyObjectGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    invoke-static {v6}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->intUnbox(Ljava/lang/Object;)I

    move-result v6

    or-int/2addr v5, v6

    goto :goto_1

    :cond_0
    const/16 v5, 0x41

    aget-object v5, v4, v5

    invoke-interface {v5, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGroovyObjectGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    goto :goto_0

    :cond_1
    invoke-static/range {p7 .. p7}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v5

    if-eqz v5, :cond_2

    const/16 v5, 0x3b

    aget-object v5, v4, v5

    const/16 v6, 0x3c

    aget-object v6, v4, v6

    invoke-interface {v6, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGroovyObjectGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    const/16 v7, 0x3d

    aget-object v7, v4, v7

    invoke-interface {v7, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGroovyObjectGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v7

    invoke-interface {v5, v6, v7}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    goto :goto_0

    :cond_2
    const/16 v5, 0x3e

    aget-object v5, v4, v5

    invoke-interface {v5, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGroovyObjectGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    :goto_0
    invoke-static {v5}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->intUnbox(Ljava/lang/Object;)I

    move-result v5

    :goto_1
    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v5

    move-object v7, v5

    const/16 v5, 0x42

    .line 145
    aget-object v5, v4, v5

    const-class v6, Lorg/codehaus/groovy/ast/ClassHelper;

    const/16 v8, 0x43

    aget-object v8, v4, v8

    const-class v9, Ljava/lang/Void;

    invoke-interface {v8, v9}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v8

    invoke-interface {v5, v6, v8}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v8

    .line 146
    new-instance v6, Lorg/codehaus/groovy/runtime/GStringImpl;

    const/4 v5, 0x1

    new-array v9, v5, [Ljava/lang/Object;

    const/16 v10, 0x44

    aget-object v10, v4, v10

    move-object/from16 v11, p6

    invoke-interface {v10, v11}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v10

    const/4 v11, 0x0

    aput-object v10, v9, v11

    const-string v10, "add"

    const-string v12, ""

    filled-new-array {v10, v12}, [Ljava/lang/String;

    move-result-object v13

    invoke-direct {v6, v9, v13}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    const/16 v9, 0x45

    .line 147
    aget-object v9, v4, v9

    const-class v13, Lorg/codehaus/groovy/ast/ClassHelper;

    const/16 v14, 0x46

    aget-object v14, v4, v14

    invoke-interface {v14, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v14

    invoke-interface {v9, v13, v14}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v9

    const/4 v13, 0x0

    const-string v14, "redirect"

    .line 148
    move-object v15, v14

    check-cast v15, Ljava/lang/String;

    invoke-static {v3, v13, v9, v14}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->setProperty(Ljava/lang/Object;Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;)V

    new-array v3, v5, [Ljava/lang/Object;

    const/16 v13, 0x47

    .line 149
    aget-object v13, v4, v13

    const-class v14, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const-string v15, "listener"

    invoke-interface {v13, v14, v9, v15}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v9

    aput-object v9, v3, v11

    invoke-static {v3}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v3

    const-class v9, [Lorg/codehaus/groovy/ast/Parameter;

    invoke-static {v3, v9}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->asType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v3

    move-object v9, v3

    check-cast v9, [Lorg/codehaus/groovy/ast/Parameter;

    const/16 v3, 0x48

    .line 151
    aget-object v3, v4, v3

    invoke-interface {v3, v1, v6, v9}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    invoke-static {v3}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v3

    const/4 v13, 0x2

    const/4 v14, 0x3

    if-eqz v3, :cond_3

    const/16 v2, 0x49

    .line 152
    aget-object v2, v4, v2

    const-class v3, Lgroovy/beans/ListenerListASTTransformation;

    new-instance v7, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v8, v14, [Ljava/lang/Object;

    const/16 v9, 0x4a

    aget-object v9, v4, v9

    sget-object v10, Lgroovy/beans/ListenerListASTTransformation;->MY_CLASS:Ljava/lang/Class;

    invoke-interface {v9, v10}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v9

    aput-object v9, v8, v11

    const/16 v9, 0x4b

    aget-object v4, v4, v9

    invoke-interface {v4, v1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    aput-object v1, v8, v5

    aput-object v6, v8, v13

    const-string v1, "Conflict using @"

    const-string v4, ". Class "

    const-string v5, " already has method "

    filled-new-array {v1, v4, v5, v12}, [Ljava/lang/String;

    move-result-object v1

    invoke-direct {v7, v8, v1}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    move-object/from16 v1, p1

    move-object/from16 v4, p2

    invoke-interface {v2, v3, v4, v1, v7}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-void

    :cond_3
    const/16 v3, 0x4c

    .line 156
    aget-object v3, v4, v3

    const-class v12, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    invoke-interface {v3, v12}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    const-class v12, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    invoke-static {v3, v12}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    const/16 v12, 0x4d

    .line 157
    aget-object v12, v4, v12

    new-array v14, v14, [Ljava/lang/Object;

    const/16 v16, 0x4e

    .line 158
    aget-object v13, v4, v16

    const-class v5, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v16, 0x4f

    .line 159
    aget-object v11, v4, v16

    const-class v0, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v16, 0x50

    aget-object v1, v4, v16

    move-object/from16 v16, v9

    const-class v9, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    invoke-interface {v1, v9, v15}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-interface {v11, v0, v1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    const/16 v1, 0x51

    aget-object v1, v4, v1

    const-class v9, Lorg/codehaus/groovy/ast/stmt/ReturnStatement;

    invoke-interface {v1, v9}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-interface {v13, v5, v0, v1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    const/4 v1, 0x0

    aput-object v0, v14, v1

    const/16 v0, 0x52

    .line 162
    aget-object v0, v4, v0

    const-class v1, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v5, 0x53

    .line 163
    aget-object v5, v4, v5

    const-class v9, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v11, 0x54

    aget-object v11, v4, v11

    const-class v13, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v17, 0x55

    move-object/from16 v18, v8

    aget-object v8, v4, v17

    invoke-interface {v8, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v8

    invoke-interface {v11, v13, v8}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v8

    invoke-interface {v5, v9, v8}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    const/16 v8, 0x56

    .line 164
    aget-object v8, v4, v8

    const-class v9, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v11, 0x57

    aget-object v11, v4, v11

    const-class v13, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v17, 0x58

    move-object/from16 v19, v7

    aget-object v7, v4, v17

    invoke-interface {v7, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v7

    invoke-interface {v11, v13, v7}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v7

    const/16 v11, 0x59

    aget-object v11, v4, v11

    const-class v13, Lorg/codehaus/groovy/ast/expr/ListExpression;

    invoke-interface {v11, v13}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v11

    invoke-interface {v8, v9, v7, v11}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v7

    invoke-interface {v0, v1, v5, v7}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    const/4 v1, 0x1

    aput-object v0, v14, v1

    const/16 v0, 0x5a

    .line 166
    aget-object v0, v4, v0

    const-class v1, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v5, 0x5b

    aget-object v5, v4, v5

    const-class v7, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v8, 0x5c

    aget-object v8, v4, v8

    const-class v9, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v11, 0x5d

    aget-object v11, v4, v11

    invoke-interface {v11, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-interface {v8, v9, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    const/16 v8, 0x5e

    aget-object v8, v4, v8

    const-class v9, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    invoke-interface {v8, v9, v10}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v8

    const/16 v9, 0x5f

    aget-object v9, v4, v9

    const-class v10, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v11, 0x60

    aget-object v11, v4, v11

    const-class v13, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    invoke-interface {v11, v13, v15}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v11

    invoke-interface {v9, v10, v11}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v9

    invoke-interface {v5, v7, v2, v8, v9}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-interface {v0, v1, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    const/4 v1, 0x2

    aput-object v0, v14, v1

    invoke-static {v14}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    invoke-interface {v12, v3, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v0, 0x61

    .line 168
    aget-object v0, v4, v0

    const/16 v1, 0x62

    aget-object v1, v4, v1

    const-class v2, Lorg/codehaus/groovy/ast/MethodNode;

    const/4 v4, 0x0

    new-array v4, v4, [Ljava/lang/Object;

    invoke-static {v4}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v4

    const-class v5, [Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {v4, v5}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->asType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, [Lorg/codehaus/groovy/ast/ClassNode;

    const-class v5, [Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {v4, v5}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createPojoWrapper(Ljava/lang/Object;Ljava/lang/Class;)Lorg/codehaus/groovy/runtime/wrappers/Wrapper;

    move-result-object v10

    move-object/from16 v7, v19

    move-object/from16 v8, v18

    move-object/from16 v9, v16

    move-object v11, v3

    invoke-static/range {v6 .. v11}, Lorg/codehaus/groovy/runtime/ArrayUtil;->createArray(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object v3

    invoke-interface {v1, v2, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v2, p3

    invoke-interface {v0, v2, v1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method public addFireMethods(Lorg/codehaus/groovy/control/SourceUnit;Lorg/codehaus/groovy/ast/AnnotationNode;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/FieldNode;[Lorg/codehaus/groovy/ast/GenericsType;ZLorg/codehaus/groovy/ast/MethodNode;)V
    .locals 25

    move-object/from16 v0, p0

    move-object/from16 v1, p3

    move-object/from16 v2, p4

    move-object/from16 v3, p7

    invoke-static {}, Lgroovy/beans/ListenerListASTTransformation;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v4

    const/16 v5, 0xae

    .line 260
    aget-object v5, v4, v5

    const-class v6, Lorg/codehaus/groovy/ast/ClassHelper;

    const/16 v7, 0xaf

    aget-object v7, v4, v7

    const-class v8, Ljava/lang/Void;

    invoke-interface {v7, v8}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v7

    invoke-interface {v5, v6, v7}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v10

    .line 261
    new-instance v8, Lorg/codehaus/groovy/runtime/GStringImpl;

    const/4 v5, 0x1

    new-array v6, v5, [Ljava/lang/Object;

    const/16 v7, 0xb0

    aget-object v7, v4, v7

    const/16 v9, 0xb1

    aget-object v9, v4, v9

    invoke-interface {v9, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v9

    invoke-interface {v7, v9}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v7

    const/4 v9, 0x0

    aput-object v7, v6, v9

    const-string v7, "fire"

    const-string v11, ""

    filled-new-array {v7, v11}, [Ljava/lang/String;

    move-result-object v7

    invoke-direct {v8, v6, v7}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    .line 262
    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->isOrigInt()Z

    move-result v6

    if-eqz v6, :cond_1

    sget-boolean v6, Lgroovy/beans/ListenerListASTTransformation;->__$stMC:Z

    if-nez v6, :cond_1

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->disabledStandardMetaClass()Z

    move-result v6

    if-nez v6, :cond_1

    if-eqz p6, :cond_0

    const/16 v6, 0xb6

    aget-object v6, v4, v6

    invoke-interface {v6, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGroovyObjectGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    invoke-static {v6}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->intUnbox(Ljava/lang/Object;)I

    move-result v6

    const/16 v7, 0xb7

    aget-object v7, v4, v7

    invoke-interface {v7, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGroovyObjectGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v7

    invoke-static {v7}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->intUnbox(Ljava/lang/Object;)I

    move-result v7

    or-int/2addr v6, v7

    goto :goto_1

    :cond_0
    const/16 v6, 0xb8

    aget-object v6, v4, v6

    invoke-interface {v6, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGroovyObjectGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    goto :goto_0

    :cond_1
    if-eqz p6, :cond_2

    const/16 v6, 0xb2

    aget-object v6, v4, v6

    const/16 v7, 0xb3

    aget-object v7, v4, v7

    invoke-interface {v7, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGroovyObjectGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v7

    const/16 v12, 0xb4

    aget-object v12, v4, v12

    invoke-interface {v12, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGroovyObjectGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v12

    invoke-interface {v6, v7, v12}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    goto :goto_0

    :cond_2
    const/16 v6, 0xb5

    aget-object v6, v4, v6

    invoke-interface {v6, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGroovyObjectGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    :goto_0
    invoke-static {v6}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->intUnbox(Ljava/lang/Object;)I

    move-result v6

    :goto_1
    invoke-static {v6}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v6

    const/16 v7, 0xb9

    .line 264
    aget-object v7, v4, v7

    const/16 v12, 0xba

    aget-object v12, v4, v12

    invoke-interface {v12, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v12

    invoke-interface {v7, v1, v8, v12}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v7

    invoke-static {v7}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v7

    const/4 v12, 0x2

    if-eqz v7, :cond_3

    const/16 v2, 0xbb

    .line 265
    aget-object v2, v4, v2

    const-class v3, Lgroovy/beans/ListenerListASTTransformation;

    new-instance v6, Lorg/codehaus/groovy/runtime/GStringImpl;

    const/4 v7, 0x3

    new-array v7, v7, [Ljava/lang/Object;

    const/16 v10, 0xbc

    aget-object v10, v4, v10

    sget-object v13, Lgroovy/beans/ListenerListASTTransformation;->MY_CLASS:Ljava/lang/Class;

    invoke-interface {v10, v13}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v10

    aput-object v10, v7, v9

    const/16 v9, 0xbd

    aget-object v4, v4, v9

    invoke-interface {v4, v1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    aput-object v1, v7, v5

    aput-object v8, v7, v12

    const-string v1, "Conflict using @"

    const-string v4, ". Class "

    const-string v5, " already has method "

    filled-new-array {v1, v4, v5, v11}, [Ljava/lang/String;

    move-result-object v1

    invoke-direct {v6, v7, v1}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    move-object/from16 v1, p1

    move-object/from16 v4, p2

    invoke-interface {v2, v3, v4, v1, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-void

    :cond_3
    const/16 v7, 0xbe

    .line 269
    aget-object v7, v4, v7

    const-class v11, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v13, 0xbf

    aget-object v13, v4, v13

    invoke-interface {v13, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v13

    invoke-interface {v7, v11, v13}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v7

    const/16 v11, 0xc0

    .line 271
    aget-object v11, v4, v11

    const-class v13, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    invoke-interface {v11, v13}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v11

    const-class v13, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    invoke-static {v11, v13}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v11

    move-object v13, v11

    check-cast v13, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    const/16 v11, 0xc1

    .line 272
    aget-object v11, v4, v11

    const/16 v14, 0xc2

    aget-object v14, v4, v14

    const-class v15, Lorg/codehaus/groovy/ast/ClassHelper;

    const-class v9, Ljava/util/ArrayList;

    invoke-interface {v14, v15, v9}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v9

    invoke-interface {v11, v9}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v9

    const/4 v11, 0x0

    const-string v14, "genericsTypes"

    .line 273
    move-object v15, v14

    check-cast v15, Ljava/lang/String;

    move-object/from16 v15, p5

    invoke-static {v15, v11, v9, v14}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->setProperty(Ljava/lang/Object;Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;)V

    const/16 v11, 0xc3

    .line 274
    aget-object v11, v4, v11

    new-array v14, v5, [Ljava/lang/Object;

    const/16 v15, 0xc4

    .line 275
    aget-object v15, v4, v15

    const-class v5, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v16, 0xc5

    .line 276
    aget-object v12, v4, v16

    const-class v1, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v16, 0xc6

    move-object/from16 v17, v10

    aget-object v10, v4, v16

    move-object/from16 v16, v6

    const-class v6, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v18, 0xc7

    move-object/from16 v19, v8

    aget-object v8, v4, v18

    invoke-interface {v8, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v8

    invoke-interface {v10, v6, v8}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    invoke-interface {v12, v1, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    const/16 v6, 0xc8

    .line 277
    aget-object v6, v4, v6

    const-class v8, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    const/4 v10, 0x2

    new-array v10, v10, [Ljava/lang/Object;

    const/16 v12, 0xc9

    .line 278
    aget-object v12, v4, v12

    const-class v0, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v18, 0xca

    move-object/from16 p1, v11

    .line 279
    aget-object v11, v4, v18

    move-object/from16 p2, v13

    const-class v13, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    move-object/from16 v18, v14

    const-string v14, "__list"

    invoke-interface {v11, v13, v14, v9}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v11

    const/16 v13, 0xcb

    .line 280
    aget-object v13, v4, v13

    move-object/from16 p5, v1

    const-class v1, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v20, 0xcc

    move-object/from16 p6, v5

    aget-object v5, v4, v20

    move-object/from16 v20, v15

    const-class v15, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v21, 0xcd

    move-object/from16 v22, v6

    aget-object v6, v4, v21

    move-object/from16 v21, v8

    const-class v8, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v23, 0xce

    move-object/from16 v24, v7

    aget-object v7, v4, v23

    invoke-interface {v7, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-interface {v6, v8, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-interface {v5, v15, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-interface {v13, v1, v9, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-interface {v12, v0, v11, v1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    const/4 v1, 0x0

    aput-object v0, v10, v1

    const/16 v0, 0xcf

    .line 282
    aget-object v0, v4, v0

    const-class v1, Lorg/codehaus/groovy/ast/stmt/ForStatement;

    const/16 v2, 0xd0

    .line 283
    aget-object v2, v4, v2

    const-class v5, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v6, 0xd1

    aget-object v6, v4, v6

    const-class v7, Lorg/codehaus/groovy/ast/ClassHelper;

    invoke-interface {v6, v7}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    const-string v7, "listener"

    invoke-interface {v2, v5, v6, v7}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    const/16 v5, 0xd2

    .line 284
    aget-object v5, v4, v5

    const-class v6, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    invoke-interface {v5, v6, v14}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    const/16 v6, 0xd3

    .line 285
    aget-object v6, v4, v6

    const-class v8, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    const/4 v9, 0x1

    new-array v11, v9, [Ljava/lang/Object;

    const/16 v9, 0xd4

    .line 286
    aget-object v9, v4, v9

    const-class v12, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v13, 0xd5

    aget-object v13, v4, v13

    const-class v14, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v15, 0xd6

    aget-object v15, v4, v15

    move-object/from16 v23, v10

    const-class v10, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    invoke-interface {v15, v10, v7}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v7

    const/16 v10, 0xd7

    aget-object v10, v4, v10

    invoke-interface {v10, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v10

    move-object/from16 v15, v24

    invoke-interface {v13, v14, v7, v10, v15}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v7

    invoke-interface {v9, v12, v7}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v7

    const/4 v9, 0x0

    aput-object v7, v11, v9

    invoke-static {v11}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v7

    const/16 v9, 0xd8

    .line 287
    aget-object v9, v4, v9

    const-class v10, Lorg/codehaus/groovy/ast/VariableScope;

    invoke-interface {v9, v10}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v9

    invoke-interface {v6, v8, v7, v9}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    invoke-interface {v0, v1, v2, v5, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    const/4 v1, 0x1

    aput-object v0, v23, v1

    invoke-static/range {v23 .. v23}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    const/16 v1, 0xd9

    .line 289
    aget-object v1, v4, v1

    const-class v2, Lorg/codehaus/groovy/ast/VariableScope;

    invoke-interface {v1, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v5, v21

    move-object/from16 v2, v22

    invoke-interface {v2, v5, v0, v1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    move-object/from16 v5, p5

    move-object/from16 v2, p6

    move-object/from16 v1, v20

    invoke-interface {v1, v2, v5, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    const/4 v1, 0x0

    aput-object v0, v18, v1

    invoke-static/range {v18 .. v18}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    move-object/from16 v2, p1

    move-object/from16 v1, p2

    invoke-interface {v2, v1, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v0, 0xda

    .line 293
    aget-object v0, v4, v0

    const/16 v2, 0xdb

    aget-object v2, v4, v2

    invoke-interface {v2, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    new-instance v3, Lgroovy/beans/ListenerListASTTransformation$_addFireMethods_closure3;

    move-object/from16 v5, p0

    invoke-direct {v3, v5, v5}, Lgroovy/beans/ListenerListASTTransformation$_addFireMethods_closure3;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    invoke-interface {v0, v2, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    const/16 v2, 0xdc

    .line 299
    aget-object v2, v4, v2

    const-class v3, [Lorg/codehaus/groovy/ast/Parameter;

    invoke-static {v0, v3}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->asType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lorg/codehaus/groovy/ast/Parameter;

    const-class v3, [Lorg/codehaus/groovy/ast/Parameter;

    invoke-static {v0, v3}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createPojoWrapper(Ljava/lang/Object;Ljava/lang/Class;)Lorg/codehaus/groovy/runtime/wrappers/Wrapper;

    move-result-object v11

    const/4 v0, 0x0

    new-array v0, v0, [Ljava/lang/Object;

    invoke-static {v0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    const-class v3, [Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {v0, v3}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->asType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lorg/codehaus/groovy/ast/ClassNode;

    const-class v3, [Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {v0, v3}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createPojoWrapper(Ljava/lang/Object;Ljava/lang/Class;)Lorg/codehaus/groovy/runtime/wrappers/Wrapper;

    move-result-object v12

    move-object/from16 v8, v19

    move-object/from16 v9, v16

    move-object/from16 v10, v17

    move-object v13, v1

    invoke-static/range {v8 .. v13}, Lorg/codehaus/groovy/runtime/ArrayUtil;->createArray(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object v0

    move-object/from16 v1, p3

    invoke-interface {v2, v1, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method public addGetListeners(Lorg/codehaus/groovy/control/SourceUnit;Lorg/codehaus/groovy/ast/AnnotationNode;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/FieldNode;Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Ljava/lang/Object;)V
    .locals 23

    move-object/from16 v0, p0

    move-object/from16 v1, p3

    move-object/from16 v2, p4

    invoke-static {}, Lgroovy/beans/ListenerListASTTransformation;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v3

    .line 223
    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->isOrigInt()Z

    move-result v4

    if-eqz v4, :cond_1

    sget-boolean v4, Lgroovy/beans/ListenerListASTTransformation;->__$stMC:Z

    if-nez v4, :cond_1

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->disabledStandardMetaClass()Z

    move-result v4

    if-nez v4, :cond_1

    invoke-static/range {p7 .. p7}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v4

    if-eqz v4, :cond_0

    const/16 v4, 0x8f

    aget-object v4, v3, v4

    invoke-interface {v4, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGroovyObjectGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    invoke-static {v4}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->intUnbox(Ljava/lang/Object;)I

    move-result v4

    const/16 v5, 0x90

    aget-object v5, v3, v5

    invoke-interface {v5, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGroovyObjectGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    invoke-static {v5}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->intUnbox(Ljava/lang/Object;)I

    move-result v5

    or-int/2addr v4, v5

    goto :goto_1

    :cond_0
    const/16 v4, 0x91

    aget-object v4, v3, v4

    invoke-interface {v4, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGroovyObjectGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    goto :goto_0

    :cond_1
    invoke-static/range {p7 .. p7}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v4

    if-eqz v4, :cond_2

    const/16 v4, 0x8b

    aget-object v4, v3, v4

    const/16 v5, 0x8c

    aget-object v5, v3, v5

    invoke-interface {v5, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGroovyObjectGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    const/16 v6, 0x8d

    aget-object v6, v3, v6

    invoke-interface {v6, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGroovyObjectGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    invoke-interface {v4, v5, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    goto :goto_0

    :cond_2
    const/16 v4, 0x8e

    aget-object v4, v3, v4

    invoke-interface {v4, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGroovyObjectGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    :goto_0
    invoke-static {v4}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->intUnbox(Ljava/lang/Object;)I

    move-result v4

    :goto_1
    invoke-static {v4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v4

    move-object v6, v4

    const/16 v4, 0x92

    .line 224
    aget-object v4, v3, v4

    move-object/from16 v5, p5

    invoke-interface {v4, v5}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v7

    .line 225
    new-instance v5, Lorg/codehaus/groovy/runtime/GStringImpl;

    const/4 v4, 0x1

    new-array v8, v4, [Ljava/lang/Object;

    const/16 v9, 0x93

    aget-object v9, v3, v9

    move-object/from16 v10, p6

    invoke-interface {v9, v10}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v9

    const/4 v10, 0x0

    aput-object v9, v8, v10

    const-string v9, "get"

    const-string v11, "s"

    filled-new-array {v9, v11}, [Ljava/lang/String;

    move-result-object v9

    invoke-direct {v5, v8, v9}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    new-array v8, v10, [Ljava/lang/Object;

    .line 226
    invoke-static {v8}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v8

    const-class v9, [Lorg/codehaus/groovy/ast/Parameter;

    invoke-static {v8, v9}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->asType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v8

    check-cast v8, [Lorg/codehaus/groovy/ast/Parameter;

    const/16 v9, 0x94

    .line 228
    aget-object v9, v3, v9

    invoke-interface {v9, v1, v5, v8}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v9

    invoke-static {v9}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v9

    const/4 v11, 0x2

    const/4 v12, 0x3

    if-eqz v9, :cond_3

    const/16 v2, 0x95

    .line 229
    aget-object v2, v3, v2

    const-class v6, Lgroovy/beans/ListenerListASTTransformation;

    new-instance v7, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v8, v12, [Ljava/lang/Object;

    const/16 v9, 0x96

    aget-object v9, v3, v9

    sget-object v12, Lgroovy/beans/ListenerListASTTransformation;->MY_CLASS:Ljava/lang/Class;

    invoke-interface {v9, v12}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v9

    aput-object v9, v8, v10

    const/16 v9, 0x97

    aget-object v3, v3, v9

    invoke-interface {v3, v1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    aput-object v1, v8, v4

    aput-object v5, v8, v11

    const-string v1, "Conflict using @"

    const-string v3, ". Class "

    const-string v4, " already has method "

    const-string v5, ""

    filled-new-array {v1, v3, v4, v5}, [Ljava/lang/String;

    move-result-object v1

    invoke-direct {v7, v8, v1}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    move-object/from16 v1, p1

    move-object/from16 v3, p2

    invoke-interface {v2, v6, v3, v1, v7}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-void

    :cond_3
    const/16 v9, 0x98

    .line 233
    aget-object v9, v3, v9

    const-class v13, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    invoke-interface {v9, v13}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v9

    const-class v13, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    invoke-static {v9, v13}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v9

    move-object v13, v9

    check-cast v13, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    const/16 v9, 0x99

    .line 234
    aget-object v9, v3, v9

    new-array v12, v12, [Ljava/lang/Object;

    const/16 v14, 0x9a

    .line 235
    aget-object v14, v3, v14

    const-class v15, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v16, 0x9b

    aget-object v11, v3, v16

    const-class v4, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v16, 0x9c

    aget-object v10, v3, v16

    const-class v0, Lorg/codehaus/groovy/ast/ClassHelper;

    invoke-interface {v10, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    const-string v10, "__result"

    invoke-interface {v11, v4, v10, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    const/16 v4, 0x9d

    aget-object v4, v3, v4

    const-class v11, Lorg/codehaus/groovy/ast/expr/ListExpression;

    invoke-interface {v4, v11}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    invoke-interface {v14, v15, v0, v4}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    const/4 v4, 0x0

    aput-object v0, v12, v4

    const/16 v0, 0x9e

    .line 236
    aget-object v0, v3, v0

    const-class v4, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v11, 0x9f

    .line 237
    aget-object v11, v3, v11

    const-class v14, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v15, 0xa0

    aget-object v15, v3, v15

    const-class v1, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v16, 0xa1

    move-object/from16 v17, v8

    aget-object v8, v3, v16

    invoke-interface {v8, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v8

    invoke-interface {v15, v1, v8}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-interface {v11, v14, v1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    const/16 v8, 0xa2

    .line 238
    aget-object v8, v3, v8

    const-class v11, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v14, 0xa3

    aget-object v14, v3, v14

    const-class v15, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v16, 0xa4

    move-object/from16 v18, v6

    aget-object v6, v3, v16

    move-object/from16 v16, v5

    const-class v5, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    invoke-interface {v6, v5, v10}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    const/16 v6, 0xa5

    aget-object v6, v3, v6

    move-object/from16 p1, v9

    const-class v9, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    move-object/from16 p2, v13

    const-string v13, "addAll"

    invoke-interface {v6, v9, v13}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    const/16 v9, 0xa6

    aget-object v9, v3, v9

    const-class v13, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v19, 0xa7

    move-object/from16 v20, v7

    aget-object v7, v3, v19

    move-object/from16 v19, v10

    const-class v10, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v21, 0xa8

    move-object/from16 v22, v12

    aget-object v12, v3, v21

    invoke-interface {v12, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-interface {v7, v10, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-interface {v9, v13, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-interface {v14, v15, v5, v6, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-interface {v8, v11, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-interface {v0, v4, v1, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    const/4 v1, 0x1

    aput-object v0, v22, v1

    const/16 v0, 0xa9

    .line 240
    aget-object v0, v3, v0

    const-class v1, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v2, 0xaa

    aget-object v2, v3, v2

    const-class v4, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v5, 0xab

    aget-object v5, v3, v5

    const-class v6, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    move-object/from16 v7, v19

    invoke-interface {v5, v6, v7}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    move-object/from16 v7, v20

    invoke-interface {v2, v4, v7, v5}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-interface {v0, v1, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    const/4 v1, 0x2

    aput-object v0, v22, v1

    invoke-static/range {v22 .. v22}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    move-object/from16 v2, p1

    move-object/from16 v1, p2

    invoke-interface {v2, v1, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v0, 0xac

    .line 242
    aget-object v0, v3, v0

    const/16 v2, 0xad

    aget-object v2, v3, v2

    const-class v3, Lorg/codehaus/groovy/ast/MethodNode;

    const/4 v4, 0x0

    new-array v4, v4, [Ljava/lang/Object;

    invoke-static {v4}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v4

    const-class v5, [Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {v4, v5}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->asType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, [Lorg/codehaus/groovy/ast/ClassNode;

    const-class v5, [Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {v4, v5}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createPojoWrapper(Ljava/lang/Object;Ljava/lang/Class;)Lorg/codehaus/groovy/runtime/wrappers/Wrapper;

    move-result-object v9

    move-object/from16 v5, v16

    move-object/from16 v6, v18

    move-object/from16 v8, v17

    move-object v10, v1

    invoke-static/range {v5 .. v10}, Lorg/codehaus/groovy/runtime/ArrayUtil;->createArray(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object v1

    invoke-interface {v2, v3, v1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v2, p3

    invoke-interface {v0, v2, v1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method public addRemoveListener(Lorg/codehaus/groovy/control/SourceUnit;Lorg/codehaus/groovy/ast/AnnotationNode;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/FieldNode;Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Ljava/lang/Object;)V
    .locals 20

    move-object/from16 v0, p0

    move-object/from16 v1, p3

    move-object/from16 v2, p4

    move-object/from16 v3, p5

    invoke-static {}, Lgroovy/beans/ListenerListASTTransformation;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v4

    .line 184
    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->isOrigInt()Z

    move-result v5

    if-eqz v5, :cond_1

    sget-boolean v5, Lgroovy/beans/ListenerListASTTransformation;->__$stMC:Z

    if-nez v5, :cond_1

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->disabledStandardMetaClass()Z

    move-result v5

    if-nez v5, :cond_1

    invoke-static/range {p7 .. p7}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v5

    if-eqz v5, :cond_0

    const/16 v5, 0x67

    aget-object v5, v4, v5

    invoke-interface {v5, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGroovyObjectGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    invoke-static {v5}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->intUnbox(Ljava/lang/Object;)I

    move-result v5

    const/16 v6, 0x68

    aget-object v6, v4, v6

    invoke-interface {v6, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGroovyObjectGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    invoke-static {v6}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->intUnbox(Ljava/lang/Object;)I

    move-result v6

    or-int/2addr v5, v6

    goto :goto_1

    :cond_0
    const/16 v5, 0x69

    aget-object v5, v4, v5

    invoke-interface {v5, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGroovyObjectGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    goto :goto_0

    :cond_1
    invoke-static/range {p7 .. p7}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v5

    if-eqz v5, :cond_2

    const/16 v5, 0x63

    aget-object v5, v4, v5

    const/16 v6, 0x64

    aget-object v6, v4, v6

    invoke-interface {v6, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGroovyObjectGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    const/16 v7, 0x65

    aget-object v7, v4, v7

    invoke-interface {v7, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGroovyObjectGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v7

    invoke-interface {v5, v6, v7}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    goto :goto_0

    :cond_2
    const/16 v5, 0x66

    aget-object v5, v4, v5

    invoke-interface {v5, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGroovyObjectGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    :goto_0
    invoke-static {v5}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->intUnbox(Ljava/lang/Object;)I

    move-result v5

    :goto_1
    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v5

    move-object v7, v5

    const/16 v5, 0x6a

    .line 185
    aget-object v5, v4, v5

    const-class v6, Lorg/codehaus/groovy/ast/ClassHelper;

    const/16 v8, 0x6b

    aget-object v8, v4, v8

    const-class v9, Ljava/lang/Void;

    invoke-interface {v8, v9}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v8

    invoke-interface {v5, v6, v8}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v8

    .line 186
    new-instance v6, Lorg/codehaus/groovy/runtime/GStringImpl;

    const/4 v5, 0x1

    new-array v9, v5, [Ljava/lang/Object;

    const/16 v10, 0x6c

    aget-object v10, v4, v10

    move-object/from16 v11, p6

    invoke-interface {v10, v11}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v10

    const/4 v11, 0x0

    aput-object v10, v9, v11

    const-string v10, "remove"

    const-string v12, ""

    filled-new-array {v10, v12}, [Ljava/lang/String;

    move-result-object v13

    invoke-direct {v6, v9, v13}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    const/16 v9, 0x6d

    .line 187
    aget-object v9, v4, v9

    const-class v13, Lorg/codehaus/groovy/ast/ClassHelper;

    const/16 v14, 0x6e

    aget-object v14, v4, v14

    invoke-interface {v14, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v14

    invoke-interface {v9, v13, v14}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v9

    const/4 v13, 0x0

    const-string v14, "redirect"

    .line 188
    move-object v15, v14

    check-cast v15, Ljava/lang/String;

    invoke-static {v3, v13, v9, v14}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->setProperty(Ljava/lang/Object;Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;)V

    new-array v3, v5, [Ljava/lang/Object;

    const/16 v13, 0x6f

    .line 189
    aget-object v13, v4, v13

    const-class v14, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const-string v15, "listener"

    invoke-interface {v13, v14, v9, v15}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v9

    aput-object v9, v3, v11

    invoke-static {v3}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v3

    const-class v9, [Lorg/codehaus/groovy/ast/Parameter;

    invoke-static {v3, v9}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->asType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v3

    move-object v9, v3

    check-cast v9, [Lorg/codehaus/groovy/ast/Parameter;

    const/16 v3, 0x70

    .line 191
    aget-object v3, v4, v3

    invoke-interface {v3, v1, v6, v9}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    invoke-static {v3}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v3

    const/4 v13, 0x2

    const/4 v14, 0x3

    if-eqz v3, :cond_3

    const/16 v2, 0x71

    .line 192
    aget-object v2, v4, v2

    const-class v3, Lgroovy/beans/ListenerListASTTransformation;

    new-instance v7, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v8, v14, [Ljava/lang/Object;

    const/16 v9, 0x72

    aget-object v9, v4, v9

    sget-object v10, Lgroovy/beans/ListenerListASTTransformation;->MY_CLASS:Ljava/lang/Class;

    invoke-interface {v9, v10}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v9

    aput-object v9, v8, v11

    const/16 v9, 0x73

    aget-object v4, v4, v9

    invoke-interface {v4, v1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    aput-object v1, v8, v5

    aput-object v6, v8, v13

    const-string v1, "Conflict using @"

    const-string v4, ". Class "

    const-string v5, " already has method "

    filled-new-array {v1, v4, v5, v12}, [Ljava/lang/String;

    move-result-object v1

    invoke-direct {v7, v8, v1}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    move-object/from16 v1, p1

    move-object/from16 v4, p2

    invoke-interface {v2, v3, v4, v1, v7}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-void

    :cond_3
    const/16 v3, 0x74

    .line 196
    aget-object v3, v4, v3

    const-class v12, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    invoke-interface {v3, v12}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    const-class v12, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    invoke-static {v3, v12}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    const/16 v12, 0x75

    .line 197
    aget-object v12, v4, v12

    new-array v14, v14, [Ljava/lang/Object;

    const/16 v16, 0x76

    .line 198
    aget-object v13, v4, v16

    const-class v5, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v16, 0x77

    .line 199
    aget-object v11, v4, v16

    const-class v0, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v16, 0x78

    aget-object v1, v4, v16

    move-object/from16 v16, v9

    const-class v9, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    invoke-interface {v1, v9, v15}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-interface {v11, v0, v1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    const/16 v1, 0x79

    aget-object v1, v4, v1

    const-class v9, Lorg/codehaus/groovy/ast/stmt/ReturnStatement;

    invoke-interface {v1, v9}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-interface {v13, v5, v0, v1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    const/4 v1, 0x0

    aput-object v0, v14, v1

    const/16 v0, 0x7a

    .line 202
    aget-object v0, v4, v0

    const-class v1, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v5, 0x7b

    .line 203
    aget-object v5, v4, v5

    const-class v9, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v11, 0x7c

    aget-object v11, v4, v11

    const-class v13, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v17, 0x7d

    move-object/from16 v18, v8

    aget-object v8, v4, v17

    invoke-interface {v8, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v8

    invoke-interface {v11, v13, v8}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v8

    invoke-interface {v5, v9, v8}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    const/16 v8, 0x7e

    .line 204
    aget-object v8, v4, v8

    const-class v9, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v11, 0x7f

    aget-object v11, v4, v11

    const-class v13, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v17, 0x80

    move-object/from16 v19, v7

    aget-object v7, v4, v17

    invoke-interface {v7, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v7

    invoke-interface {v11, v13, v7}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v7

    const/16 v11, 0x81

    aget-object v11, v4, v11

    const-class v13, Lorg/codehaus/groovy/ast/expr/ListExpression;

    invoke-interface {v11, v13}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v11

    invoke-interface {v8, v9, v7, v11}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v7

    invoke-interface {v0, v1, v5, v7}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    const/4 v1, 0x1

    aput-object v0, v14, v1

    const/16 v0, 0x82

    .line 206
    aget-object v0, v4, v0

    const-class v1, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v5, 0x83

    aget-object v5, v4, v5

    const-class v7, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v8, 0x84

    aget-object v8, v4, v8

    const-class v9, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v11, 0x85

    aget-object v11, v4, v11

    invoke-interface {v11, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-interface {v8, v9, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    const/16 v8, 0x86

    aget-object v8, v4, v8

    const-class v9, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    invoke-interface {v8, v9, v10}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v8

    const/16 v9, 0x87

    aget-object v9, v4, v9

    const-class v10, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v11, 0x88

    aget-object v11, v4, v11

    const-class v13, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    invoke-interface {v11, v13, v15}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v11

    invoke-interface {v9, v10, v11}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v9

    invoke-interface {v5, v7, v2, v8, v9}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-interface {v0, v1, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    const/4 v1, 0x2

    aput-object v0, v14, v1

    invoke-static {v14}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    invoke-interface {v12, v3, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v0, 0x89

    .line 208
    aget-object v0, v4, v0

    const/16 v1, 0x8a

    aget-object v1, v4, v1

    const-class v2, Lorg/codehaus/groovy/ast/MethodNode;

    const/4 v4, 0x0

    new-array v4, v4, [Ljava/lang/Object;

    invoke-static {v4}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v4

    const-class v5, [Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {v4, v5}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->asType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, [Lorg/codehaus/groovy/ast/ClassNode;

    const-class v5, [Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {v4, v5}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createPojoWrapper(Ljava/lang/Object;Ljava/lang/Class;)Lorg/codehaus/groovy/runtime/wrappers/Wrapper;

    move-result-object v10

    move-object/from16 v7, v19

    move-object/from16 v8, v18

    move-object/from16 v9, v16

    move-object v11, v3

    invoke-static/range {v6 .. v11}, Lorg/codehaus/groovy/runtime/ArrayUtil;->createArray(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object v3

    invoke-interface {v1, v2, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v2, p3

    invoke-interface {v0, v2, v1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method public getMetaClass()Lgroovy/lang/MetaClass;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    .annotation runtime Lgroovy/transform/Internal;
    .end annotation

    .annotation runtime Lgroovyjarjaropenbeans/Transient;
    .end annotation

    iget-object v0, p0, Lgroovy/beans/ListenerListASTTransformation;->metaClass:Lgroovy/lang/MetaClass;

    if-eqz v0, :cond_0

    return-object v0

    :cond_0
    invoke-virtual {p0}, Lgroovy/beans/ListenerListASTTransformation;->$getStaticMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    iput-object v0, p0, Lgroovy/beans/ListenerListASTTransformation;->metaClass:Lgroovy/lang/MetaClass;

    return-object v0
.end method

.method public setMetaClass(Lgroovy/lang/MetaClass;)V
    .locals 0
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    .annotation runtime Lgroovy/transform/Internal;
    .end annotation

    iput-object p1, p0, Lgroovy/beans/ListenerListASTTransformation;->metaClass:Lgroovy/lang/MetaClass;

    return-void
.end method

.method public visit([Lorg/codehaus/groovy/ast/ASTNode;Lorg/codehaus/groovy/control/SourceUnit;)V
    .locals 21

    move-object/from16 v9, p0

    move-object/from16 v0, p1

    const-class v1, Lgroovy/beans/ListenerListASTTransformation;

    new-instance v3, Lgroovy/lang/Reference;

    move-object/from16 v2, p2

    invoke-direct {v3, v2}, Lgroovy/lang/Reference;-><init>(Ljava/lang/Object;)V

    invoke-static {}, Lgroovy/beans/ListenerListASTTransformation;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v2

    .line 76
    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->isOrigInt()Z

    move-result v4

    const-string v5, ""

    const-string v6, " / "

    const-string v7, "Internal error: wrong types: "

    const/4 v8, 0x2

    const/4 v10, 0x0

    invoke-static {v10}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v11

    const/4 v12, 0x1

    invoke-static {v12}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v13

    if-eqz v4, :cond_3

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->isOrigZ()Z

    move-result v4

    if-eqz v4, :cond_3

    sget-boolean v4, Lgroovy/beans/ListenerListASTTransformation;->__$stMC:Z

    if-nez v4, :cond_3

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->disabledStandardMetaClass()Z

    move-result v4

    if-nez v4, :cond_3

    invoke-static {v0, v10}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->objectArrayGet([Ljava/lang/Object;I)Ljava/lang/Object;

    move-result-object v4

    instance-of v4, v4, Lorg/codehaus/groovy/ast/AnnotationNode;

    xor-int/2addr v4, v12

    if-nez v4, :cond_1

    invoke-static {v0, v12}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->objectArrayGet([Ljava/lang/Object;I)Ljava/lang/Object;

    move-result-object v4

    instance-of v4, v4, Lorg/codehaus/groovy/ast/AnnotatedNode;

    xor-int/2addr v4, v12

    if-eqz v4, :cond_0

    goto :goto_0

    :cond_0
    move v4, v10

    goto :goto_1

    :cond_1
    :goto_0
    move v4, v12

    :goto_1
    if-nez v4, :cond_2

    goto :goto_4

    :cond_2
    const/4 v0, 0x7

    .line 77
    aget-object v0, v2, v0

    const-class v1, Ljava/lang/RuntimeException;

    new-instance v3, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v4, v8, [Ljava/lang/Object;

    const/16 v8, 0x8

    aget-object v8, v2, v8

    const/16 v11, 0x9

    aget-object v11, v2, v11

    invoke-interface {v11, v9}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGroovyObjectGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v11

    invoke-interface {v8, v11}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v8

    aput-object v8, v4, v10

    const/16 v8, 0xa

    aget-object v8, v2, v8

    const/16 v10, 0xb

    aget-object v2, v2, v10

    invoke-interface {v2, v9}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGroovyObjectGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-interface {v8, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    aput-object v2, v4, v12

    filled-new-array {v7, v6, v5}, [Ljava/lang/String;

    move-result-object v2

    invoke-direct {v3, v4, v2}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-interface {v0, v1, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/Throwable;

    throw v0

    .line 76
    :cond_3
    aget-object v4, v2, v10

    invoke-interface {v4, v0, v11}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    instance-of v4, v4, Lorg/codehaus/groovy/ast/AnnotationNode;

    xor-int/2addr v4, v12

    if-nez v4, :cond_5

    aget-object v4, v2, v12

    invoke-interface {v4, v0, v13}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    instance-of v4, v4, Lorg/codehaus/groovy/ast/AnnotatedNode;

    xor-int/2addr v4, v12

    if-eqz v4, :cond_4

    goto :goto_2

    :cond_4
    move v4, v10

    goto :goto_3

    :cond_5
    :goto_2
    move v4, v12

    :goto_3
    if-nez v4, :cond_12

    .line 78
    :goto_4
    new-instance v4, Lgroovy/lang/Reference;

    const/4 v5, 0x0

    invoke-direct {v4, v5}, Lgroovy/lang/Reference;-><init>(Ljava/lang/Object;)V

    .line 79
    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->isOrigInt()Z

    move-result v6

    if-eqz v6, :cond_6

    sget-boolean v6, Lgroovy/beans/ListenerListASTTransformation;->__$stMC:Z

    if-nez v6, :cond_6

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->disabledStandardMetaClass()Z

    move-result v6

    if-nez v6, :cond_6

    invoke-static {v0, v10}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->objectArrayGet([Ljava/lang/Object;I)Ljava/lang/Object;

    move-result-object v6

    goto :goto_5

    :cond_6
    const/16 v6, 0xc

    aget-object v6, v2, v6

    invoke-interface {v6, v0, v11}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    :goto_5
    const-class v7, Lorg/codehaus/groovy/ast/AnnotationNode;

    invoke-static {v6, v7}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Lorg/codehaus/groovy/ast/AnnotationNode;

    move-object v7, v4

    check-cast v7, Lgroovy/lang/Reference;

    invoke-virtual {v4, v6}, Lgroovy/lang/Reference;->set(Ljava/lang/Object;)V

    new-instance v6, Lgroovy/lang/Reference;

    invoke-direct {v6, v5}, Lgroovy/lang/Reference;-><init>(Ljava/lang/Object;)V

    .line 80
    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->isOrigInt()Z

    move-result v7

    if-eqz v7, :cond_7

    sget-boolean v7, Lgroovy/beans/ListenerListASTTransformation;->__$stMC:Z

    if-nez v7, :cond_7

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->disabledStandardMetaClass()Z

    move-result v7

    if-nez v7, :cond_7

    invoke-static {v0, v12}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->objectArrayGet([Ljava/lang/Object;I)Ljava/lang/Object;

    move-result-object v7

    goto :goto_6

    :cond_7
    const/16 v7, 0xd

    aget-object v7, v2, v7

    invoke-interface {v7, v0, v13}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v7

    :goto_6
    const-class v8, Lorg/codehaus/groovy/ast/FieldNode;

    invoke-static {v7, v8}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v7

    check-cast v7, Lorg/codehaus/groovy/ast/FieldNode;

    move-object v8, v6

    check-cast v8, Lgroovy/lang/Reference;

    invoke-virtual {v6, v7}, Lgroovy/lang/Reference;->set(Ljava/lang/Object;)V

    new-instance v7, Lgroovy/lang/Reference;

    invoke-direct {v7, v5}, Lgroovy/lang/Reference;-><init>(Ljava/lang/Object;)V

    .line 81
    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->isOrigInt()Z

    move-result v8

    if-eqz v8, :cond_8

    sget-boolean v8, Lgroovy/beans/ListenerListASTTransformation;->__$stMC:Z

    if-nez v8, :cond_8

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->disabledStandardMetaClass()Z

    move-result v8

    if-nez v8, :cond_8

    const/16 v8, 0x10

    aget-object v8, v2, v8

    invoke-static {v0, v12}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->objectArrayGet([Ljava/lang/Object;I)Ljava/lang/Object;

    move-result-object v0

    goto :goto_7

    :cond_8
    const/16 v8, 0xe

    aget-object v8, v2, v8

    const/16 v14, 0xf

    aget-object v14, v2, v14

    invoke-interface {v14, v0, v13}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    :goto_7
    invoke-interface {v8, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    const-class v8, Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {v0, v8}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/ast/ClassNode;

    move-object v8, v7

    check-cast v8, Lgroovy/lang/Reference;

    invoke-virtual {v7, v0}, Lgroovy/lang/Reference;->set(Ljava/lang/Object;)V

    const/16 v0, 0x11

    .line 82
    aget-object v0, v2, v0

    invoke-virtual {v6}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v8

    check-cast v8, Lorg/codehaus/groovy/ast/FieldNode;

    invoke-interface {v0, v8}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    const-class v8, Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {v0, v8}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/ast/ClassNode;

    .line 84
    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->isOrigZ()Z

    move-result v8

    if-eqz v8, :cond_b

    sget-boolean v8, Lgroovy/beans/ListenerListASTTransformation;->__$stMC:Z

    if-nez v8, :cond_b

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->disabledStandardMetaClass()Z

    move-result v8

    if-nez v8, :cond_b

    const/16 v8, 0x14

    aget-object v8, v2, v8

    sget-object v13, Lgroovy/beans/ListenerListASTTransformation;->COLLECTION_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-interface {v8, v0, v13}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v8

    invoke-static {v8}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v8

    if-nez v8, :cond_a

    const/16 v8, 0x15

    aget-object v8, v2, v8

    invoke-interface {v8, v0, v13}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_9

    goto :goto_8

    :cond_9
    move v0, v10

    goto :goto_9

    :cond_a
    :goto_8
    move v0, v12

    goto :goto_9

    :cond_b
    const/16 v8, 0x12

    aget-object v8, v2, v8

    sget-object v13, Lgroovy/beans/ListenerListASTTransformation;->COLLECTION_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-interface {v8, v0, v13}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v8

    invoke-static {v8}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v8

    if-nez v8, :cond_a

    const/16 v8, 0x13

    aget-object v8, v2, v8

    invoke-interface {v8, v0, v13}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_9

    goto :goto_8

    :goto_9
    xor-int/2addr v0, v12

    const-string v8, "@"

    if-eqz v0, :cond_c

    const/16 v0, 0x16

    .line 87
    aget-object v0, v2, v0

    invoke-virtual {v4}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lorg/codehaus/groovy/ast/AnnotationNode;

    invoke-virtual {v3}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lorg/codehaus/groovy/control/SourceUnit;

    const/16 v5, 0x17

    aget-object v5, v2, v5

    const/16 v6, 0x18

    aget-object v6, v2, v6

    const/16 v7, 0x19

    aget-object v2, v2, v7

    sget-object v7, Lgroovy/beans/ListenerListASTTransformation;->MY_CLASS:Ljava/lang/Class;

    invoke-interface {v2, v7}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-interface {v6, v8, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    const-string v6, " can only annotate collection properties."

    invoke-interface {v5, v2, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-interface {v0, v1, v4, v3, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-void

    :cond_c
    const/16 v0, 0x1a

    .line 91
    aget-object v0, v2, v0

    const/16 v13, 0x1b

    aget-object v13, v2, v13

    invoke-virtual {v6}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v14

    check-cast v14, Lorg/codehaus/groovy/ast/FieldNode;

    invoke-interface {v13, v14}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v13

    invoke-interface {v0, v13}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    new-instance v13, Lgroovy/lang/Reference;

    invoke-direct {v13, v0}, Lgroovy/lang/Reference;-><init>(Ljava/lang/Object;)V

    .line 92
    invoke-virtual {v13}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v0

    xor-int/2addr v0, v12

    if-eqz v0, :cond_d

    const/16 v0, 0x1c

    .line 93
    aget-object v0, v2, v0

    invoke-virtual {v4}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lorg/codehaus/groovy/ast/AnnotationNode;

    invoke-virtual {v3}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lorg/codehaus/groovy/control/SourceUnit;

    const/16 v5, 0x1d

    aget-object v5, v2, v5

    const/16 v6, 0x1e

    aget-object v6, v2, v6

    const/16 v7, 0x1f

    aget-object v2, v2, v7

    sget-object v7, Lgroovy/beans/ListenerListASTTransformation;->MY_CLASS:Ljava/lang/Class;

    invoke-interface {v2, v7}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-interface {v6, v8, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    const-string v6, " fields must have a generic type."

    invoke-interface {v5, v2, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-interface {v0, v1, v4, v3, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-void

    :cond_d
    const/16 v0, 0x20

    .line 97
    aget-object v0, v2, v0

    const/16 v14, 0x21

    aget-object v14, v2, v14

    invoke-virtual {v13}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v15

    invoke-interface {v14, v15, v11}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v14

    invoke-interface {v0, v14}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_e

    const/16 v0, 0x22

    .line 98
    aget-object v0, v2, v0

    invoke-virtual {v4}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lorg/codehaus/groovy/ast/AnnotationNode;

    invoke-virtual {v3}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lorg/codehaus/groovy/control/SourceUnit;

    const/16 v5, 0x23

    aget-object v5, v2, v5

    const/16 v6, 0x24

    aget-object v6, v2, v6

    const/16 v7, 0x25

    aget-object v2, v2, v7

    sget-object v7, Lgroovy/beans/ListenerListASTTransformation;->MY_CLASS:Ljava/lang/Class;

    invoke-interface {v2, v7}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-interface {v6, v8, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    const-string v6, " fields with generic wildcards not yet supported."

    invoke-interface {v5, v2, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-interface {v0, v1, v4, v3, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-void

    :cond_e
    const/16 v0, 0x26

    .line 102
    aget-object v0, v2, v0

    const/16 v1, 0x27

    aget-object v1, v2, v1

    invoke-virtual {v13}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v8

    invoke-interface {v1, v8, v11}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-interface {v0, v1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    const/16 v1, 0x28

    .line 104
    aget-object v1, v2, v1

    invoke-virtual {v6}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v8

    check-cast v8, Lorg/codehaus/groovy/ast/FieldNode;

    invoke-interface {v1, v8}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v1

    xor-int/2addr v1, v12

    if-eqz v1, :cond_f

    const/16 v1, 0x29

    .line 105
    aget-object v1, v2, v1

    const-class v8, Lorg/codehaus/groovy/ast/expr/ListExpression;

    invoke-interface {v1, v8}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-virtual {v6}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v8

    check-cast v8, Lorg/codehaus/groovy/ast/FieldNode;

    const-string v11, "initialValueExpression"

    move-object v12, v11

    check-cast v12, Ljava/lang/String;

    invoke-static {v1, v5, v8, v11}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->setProperty(Ljava/lang/Object;Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;)V

    :cond_f
    const/16 v1, 0x2a

    .line 108
    aget-object v1, v2, v1

    const/16 v5, 0x2b

    aget-object v5, v2, v5

    invoke-virtual {v4}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v8

    check-cast v8, Lorg/codehaus/groovy/ast/AnnotationNode;

    const-string v11, "name"

    invoke-interface {v5, v8, v11}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    invoke-interface {v1, v5}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetPropertySafe(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v5

    if-eqz v5, :cond_10

    goto :goto_a

    :cond_10
    const/16 v1, 0x2c

    aget-object v1, v2, v1

    invoke-interface {v1, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    :goto_a
    const/16 v5, 0x2d

    .line 110
    aget-object v5, v2, v5

    const/16 v8, 0x2e

    aget-object v8, v2, v8

    invoke-interface {v8, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v8

    new-instance v11, Lgroovy/beans/ListenerListASTTransformation$_visit_closure1;

    invoke-direct {v11, v9, v9}, Lgroovy/beans/ListenerListASTTransformation$_visit_closure1;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    invoke-interface {v5, v8, v11}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v11

    const/16 v5, 0x2f

    .line 114
    aget-object v5, v2, v5

    const/16 v8, 0x30

    aget-object v8, v2, v8

    invoke-virtual {v4}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v12

    check-cast v12, Lorg/codehaus/groovy/ast/AnnotationNode;

    const-string v14, "synchronize"

    invoke-interface {v8, v12, v14}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v8

    invoke-interface {v5, v8}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetPropertySafe(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    invoke-static {v5}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v8

    if-eqz v8, :cond_11

    goto :goto_b

    :cond_11
    invoke-static {v10}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v5

    :goto_b
    new-instance v8, Lgroovy/lang/Reference;

    invoke-direct {v8, v5}, Lgroovy/lang/Reference;-><init>(Ljava/lang/Object;)V

    const/16 v5, 0x31

    .line 115
    aget-object v5, v2, v5

    invoke-virtual {v3}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v10

    move-object v14, v10

    check-cast v14, Lorg/codehaus/groovy/control/SourceUnit;

    invoke-virtual {v4}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v10

    move-object v15, v10

    check-cast v15, Lorg/codehaus/groovy/ast/AnnotationNode;

    invoke-virtual {v7}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v10

    move-object/from16 v16, v10

    check-cast v16, Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v6}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v10

    move-object/from16 v17, v10

    check-cast v17, Lorg/codehaus/groovy/ast/FieldNode;

    invoke-virtual {v8}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v20

    move-object/from16 v18, v0

    move-object/from16 v19, v1

    invoke-static/range {v14 .. v20}, Lorg/codehaus/groovy/runtime/ArrayUtil;->createArray(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object v10

    invoke-interface {v5, v9, v10}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callCurrent(Lgroovy/lang/GroovyObject;[Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v5, 0x32

    .line 116
    aget-object v5, v2, v5

    invoke-virtual {v3}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v10

    move-object v14, v10

    check-cast v14, Lorg/codehaus/groovy/control/SourceUnit;

    invoke-virtual {v4}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v10

    move-object v15, v10

    check-cast v15, Lorg/codehaus/groovy/ast/AnnotationNode;

    invoke-virtual {v7}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v10

    move-object/from16 v16, v10

    check-cast v16, Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v6}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v10

    move-object/from16 v17, v10

    check-cast v17, Lorg/codehaus/groovy/ast/FieldNode;

    invoke-virtual {v8}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v20

    invoke-static/range {v14 .. v20}, Lorg/codehaus/groovy/runtime/ArrayUtil;->createArray(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object v10

    invoke-interface {v5, v9, v10}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callCurrent(Lgroovy/lang/GroovyObject;[Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v5, 0x33

    .line 117
    aget-object v5, v2, v5

    invoke-virtual {v3}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v10

    move-object v14, v10

    check-cast v14, Lorg/codehaus/groovy/control/SourceUnit;

    invoke-virtual {v4}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v10

    move-object v15, v10

    check-cast v15, Lorg/codehaus/groovy/ast/AnnotationNode;

    invoke-virtual {v7}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v10

    move-object/from16 v16, v10

    check-cast v16, Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v6}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v10

    move-object/from16 v17, v10

    check-cast v17, Lorg/codehaus/groovy/ast/FieldNode;

    invoke-virtual {v8}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v20

    invoke-static/range {v14 .. v20}, Lorg/codehaus/groovy/runtime/ArrayUtil;->createArray(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object v0

    invoke-interface {v5, v9, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callCurrent(Lgroovy/lang/GroovyObject;[Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v0, 0x34

    .line 119
    aget-object v10, v2, v0

    new-instance v12, Lgroovy/beans/ListenerListASTTransformation$_visit_closure2;

    move-object v0, v12

    move-object/from16 v1, p0

    move-object/from16 v2, p0

    move-object v5, v7

    move-object v7, v13

    invoke-direct/range {v0 .. v8}, Lgroovy/beans/ListenerListASTTransformation$_visit_closure2;-><init>(Ljava/lang/Object;Ljava/lang/Object;Lgroovy/lang/Reference;Lgroovy/lang/Reference;Lgroovy/lang/Reference;Lgroovy/lang/Reference;Lgroovy/lang/Reference;Lgroovy/lang/Reference;)V

    invoke-interface {v10, v11, v12}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-void

    .line 77
    :cond_12
    aget-object v0, v2, v8

    const-class v1, Ljava/lang/RuntimeException;

    new-instance v3, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v4, v8, [Ljava/lang/Object;

    const/4 v8, 0x3

    aget-object v8, v2, v8

    const/4 v11, 0x4

    aget-object v11, v2, v11

    invoke-interface {v11, v9}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGroovyObjectGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v11

    invoke-interface {v8, v11}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v8

    aput-object v8, v4, v10

    const/4 v8, 0x5

    aget-object v8, v2, v8

    const/4 v10, 0x6

    aget-object v2, v2, v10

    invoke-interface {v2, v9}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGroovyObjectGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-interface {v8, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    aput-object v2, v4, v12

    filled-new-array {v7, v6, v5}, [Ljava/lang/String;

    move-result-object v2

    invoke-direct {v3, v4, v2}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-interface {v0, v1, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/Throwable;

    throw v0
.end method
