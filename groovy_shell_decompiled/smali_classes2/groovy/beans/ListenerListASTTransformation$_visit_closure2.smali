.class public final Lgroovy/beans/ListenerListASTTransformation$_visit_closure2;
.super Lgroovy/lang/Closure;
.source "ListenerListASTTransformation.groovy"

# interfaces
.implements Lorg/codehaus/groovy/runtime/GeneratedClosure;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lgroovy/beans/ListenerListASTTransformation;->visit([Lorg/codehaus/groovy/ast/ASTNode;Lorg/codehaus/groovy/control/SourceUnit;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x11
    name = "_visit_closure2"
.end annotation


# static fields
.field private static synthetic $callSiteArray:Ljava/lang/ref/SoftReference;

.field private static synthetic $staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

.field public static transient synthetic __$stMC:Z


# instance fields
.field private synthetic declaringClass:Lgroovy/lang/Reference;

.field private synthetic field:Lgroovy/lang/Reference;

.field private synthetic node:Lgroovy/lang/Reference;

.field private synthetic source:Lgroovy/lang/Reference;

.field private synthetic synchronize:Lgroovy/lang/Reference;

.field private synthetic types:Lgroovy/lang/Reference;


# direct methods
.method private static synthetic $createCallSiteArray()Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;
    .locals 3

    const/4 v0, 0x2

    new-array v0, v0, [Ljava/lang/String;

    invoke-static {v0}, Lgroovy/beans/ListenerListASTTransformation$_visit_closure2;->$createCallSiteArray_1([Ljava/lang/String;)V

    new-instance v1, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;

    const-class v2, Lgroovy/beans/ListenerListASTTransformation$_visit_closure2;

    invoke-direct {v1, v2, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;-><init>(Ljava/lang/Class;[Ljava/lang/String;)V

    return-object v1
.end method

.method private static synthetic $createCallSiteArray_1([Ljava/lang/String;)V
    .locals 2

    const/4 v0, 0x0

    const-string v1, "addFireMethods"

    aput-object v1, p0, v0

    const/4 v0, 0x1

    const-string v1, "doCall"

    aput-object v1, p0, v0

    return-void
.end method

.method private static synthetic $getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;
    .locals 2

    sget-object v0, Lgroovy/beans/ListenerListASTTransformation$_visit_closure2;->$callSiteArray:Ljava/lang/ref/SoftReference;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Ljava/lang/ref/SoftReference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;

    if-nez v0, :cond_1

    :cond_0
    invoke-static {}, Lgroovy/beans/ListenerListASTTransformation$_visit_closure2;->$createCallSiteArray()Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;

    move-result-object v0

    new-instance v1, Ljava/lang/ref/SoftReference;

    invoke-direct {v1, v0}, Ljava/lang/ref/SoftReference;-><init>(Ljava/lang/Object;)V

    sput-object v1, Lgroovy/beans/ListenerListASTTransformation$_visit_closure2;->$callSiteArray:Ljava/lang/ref/SoftReference;

    :cond_1
    iget-object v0, v0, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;->array:[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    return-object v0
.end method

.method public constructor <init>(Ljava/lang/Object;Ljava/lang/Object;Lgroovy/lang/Reference;Lgroovy/lang/Reference;Lgroovy/lang/Reference;Lgroovy/lang/Reference;Lgroovy/lang/Reference;Lgroovy/lang/Reference;)V
    .locals 0

    invoke-static {}, Lgroovy/beans/ListenerListASTTransformation$_visit_closure2;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    invoke-direct {p0, p1, p2}, Lgroovy/lang/Closure;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    iput-object p3, p0, Lgroovy/beans/ListenerListASTTransformation$_visit_closure2;->source:Lgroovy/lang/Reference;

    iput-object p4, p0, Lgroovy/beans/ListenerListASTTransformation$_visit_closure2;->node:Lgroovy/lang/Reference;

    iput-object p5, p0, Lgroovy/beans/ListenerListASTTransformation$_visit_closure2;->declaringClass:Lgroovy/lang/Reference;

    iput-object p6, p0, Lgroovy/beans/ListenerListASTTransformation$_visit_closure2;->field:Lgroovy/lang/Reference;

    iput-object p7, p0, Lgroovy/beans/ListenerListASTTransformation$_visit_closure2;->types:Lgroovy/lang/Reference;

    iput-object p8, p0, Lgroovy/beans/ListenerListASTTransformation$_visit_closure2;->synchronize:Lgroovy/lang/Reference;

    return-void
.end method


# virtual methods
.method protected synthetic $getStaticMetaClass()Lgroovy/lang/MetaClass;
    .locals 2

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    const-class v1, Lgroovy/beans/ListenerListASTTransformation$_visit_closure2;

    if-eq v0, v1, :cond_0

    invoke-static {p0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->initMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0

    :cond_0
    sget-object v0, Lgroovy/beans/ListenerListASTTransformation$_visit_closure2;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    if-nez v0, :cond_1

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getClassInfo(Ljava/lang/Class;)Lorg/codehaus/groovy/reflection/ClassInfo;

    move-result-object v0

    sput-object v0, Lgroovy/beans/ListenerListASTTransformation$_visit_closure2;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    :cond_1
    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0
.end method

.method public call(Lorg/codehaus/groovy/ast/MethodNode;)Ljava/lang/Object;
    .locals 2
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    invoke-static {}, Lgroovy/beans/ListenerListASTTransformation$_visit_closure2;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v0

    const/4 v1, 0x1

    aget-object v0, v0, v1

    invoke-interface {v0, p0, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callCurrent(Lgroovy/lang/GroovyObject;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public doCall(Lorg/codehaus/groovy/ast/MethodNode;)Ljava/lang/Object;
    .locals 9

    invoke-static {}, Lgroovy/beans/ListenerListASTTransformation$_visit_closure2;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v0

    const/4 v1, 0x0

    .line 120
    aget-object v0, v0, v1

    iget-object v1, p0, Lgroovy/beans/ListenerListASTTransformation$_visit_closure2;->source:Lgroovy/lang/Reference;

    invoke-virtual {v1}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v2

    iget-object v1, p0, Lgroovy/beans/ListenerListASTTransformation$_visit_closure2;->node:Lgroovy/lang/Reference;

    invoke-virtual {v1}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v3

    iget-object v1, p0, Lgroovy/beans/ListenerListASTTransformation$_visit_closure2;->declaringClass:Lgroovy/lang/Reference;

    invoke-virtual {v1}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v4

    iget-object v1, p0, Lgroovy/beans/ListenerListASTTransformation$_visit_closure2;->field:Lgroovy/lang/Reference;

    invoke-virtual {v1}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v5

    iget-object v1, p0, Lgroovy/beans/ListenerListASTTransformation$_visit_closure2;->types:Lgroovy/lang/Reference;

    invoke-virtual {v1}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v6

    iget-object v1, p0, Lgroovy/beans/ListenerListASTTransformation$_visit_closure2;->synchronize:Lgroovy/lang/Reference;

    invoke-virtual {v1}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v7

    move-object v8, p1

    invoke-static/range {v2 .. v8}, Lorg/codehaus/groovy/runtime/ArrayUtil;->createArray(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object p1

    invoke-interface {v0, p0, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callCurrent(Lgroovy/lang/GroovyObject;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public getDeclaringClass()Lorg/codehaus/groovy/ast/ClassNode;
    .locals 2
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    invoke-static {}, Lgroovy/beans/ListenerListASTTransformation$_visit_closure2;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    iget-object v0, p0, Lgroovy/beans/ListenerListASTTransformation$_visit_closure2;->declaringClass:Lgroovy/lang/Reference;

    invoke-virtual {v0}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    const-class v1, Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {v0, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/ast/ClassNode;

    return-object v0
.end method

.method public getField()Lorg/codehaus/groovy/ast/FieldNode;
    .locals 2
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    invoke-static {}, Lgroovy/beans/ListenerListASTTransformation$_visit_closure2;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    iget-object v0, p0, Lgroovy/beans/ListenerListASTTransformation$_visit_closure2;->field:Lgroovy/lang/Reference;

    invoke-virtual {v0}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    const-class v1, Lorg/codehaus/groovy/ast/FieldNode;

    invoke-static {v0, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/ast/FieldNode;

    return-object v0
.end method

.method public getNode()Lorg/codehaus/groovy/ast/AnnotationNode;
    .locals 2
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    invoke-static {}, Lgroovy/beans/ListenerListASTTransformation$_visit_closure2;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    iget-object v0, p0, Lgroovy/beans/ListenerListASTTransformation$_visit_closure2;->node:Lgroovy/lang/Reference;

    invoke-virtual {v0}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    const-class v1, Lorg/codehaus/groovy/ast/AnnotationNode;

    invoke-static {v0, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/ast/AnnotationNode;

    return-object v0
.end method

.method public getSource()Lorg/codehaus/groovy/control/SourceUnit;
    .locals 2
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    invoke-static {}, Lgroovy/beans/ListenerListASTTransformation$_visit_closure2;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    iget-object v0, p0, Lgroovy/beans/ListenerListASTTransformation$_visit_closure2;->source:Lgroovy/lang/Reference;

    invoke-virtual {v0}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    const-class v1, Lorg/codehaus/groovy/control/SourceUnit;

    invoke-static {v0, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/control/SourceUnit;

    return-object v0
.end method

.method public getSynchronize()Ljava/lang/Object;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    invoke-static {}, Lgroovy/beans/ListenerListASTTransformation$_visit_closure2;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    iget-object v0, p0, Lgroovy/beans/ListenerListASTTransformation$_visit_closure2;->synchronize:Lgroovy/lang/Reference;

    invoke-virtual {v0}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    return-object v0
.end method

.method public getTypes()Ljava/lang/Object;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    invoke-static {}, Lgroovy/beans/ListenerListASTTransformation$_visit_closure2;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    iget-object v0, p0, Lgroovy/beans/ListenerListASTTransformation$_visit_closure2;->types:Lgroovy/lang/Reference;

    invoke-virtual {v0}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    return-object v0
.end method
