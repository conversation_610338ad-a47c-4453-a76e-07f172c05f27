.class public Lgroovy/beans/DefaultPropertyAccessor;
.super Ljava/lang/Object;
.source "DefaultPropertyAccessor.java"

# interfaces
.implements Lgroovy/beans/PropertyAccessor;


# static fields
.field public static final INSTANCE:Lgroovy/beans/PropertyAccessor;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 22
    new-instance v0, Lgroovy/beans/DefaultPropertyAccessor;

    invoke-direct {v0}, Lgroovy/beans/DefaultPropertyAccessor;-><init>()V

    sput-object v0, Lgroovy/beans/DefaultPropertyAccessor;->INSTANCE:Lgroovy/beans/PropertyAccessor;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 21
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public read(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;
    .locals 1

    .line 25
    sget-object v0, Lgroovy/beans/DefaultPropertyReader;->INSTANCE:Lgroovy/beans/PropertyReader;

    invoke-interface {v0, p1, p2}, Lgroovy/beans/PropertyReader;->read(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public write(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;)V
    .locals 1

    .line 29
    sget-object v0, Lgroovy/beans/DefaultPropertyWriter;->INSTANCE:Lgroovy/beans/PropertyWriter;

    invoke-interface {v0, p1, p2, p3}, Lgroovy/beans/PropertyWriter;->write(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;)V

    return-void
.end method
