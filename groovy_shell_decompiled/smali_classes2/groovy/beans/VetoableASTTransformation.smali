.class public Lgroovy/beans/VetoableASTTransformation;
.super Lgroovy/beans/BindableASTTransformation;
.source "VetoableASTTransformation.java"


# annotations
.annotation runtime Lorg/codehaus/groovy/transform/GroovyASTTransformation;
    phase = .enum Lorg/codehaus/groovy/control/CompilePhase;->CANONICALIZATION:Lorg/codehaus/groovy/control/CompilePhase;
.end annotation


# static fields
.field protected static final constrainedClassNode:Lorg/codehaus/groovy/ast/ClassNode;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 79
    const-class v0, Lgroovy/beans/Vetoable;

    invoke-static {v0}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    sput-object v0, Lgroovy/beans/VetoableASTTransformation;->constrainedClassNode:Lorg/codehaus/groovy/ast/ClassNode;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 77
    invoke-direct {p0}, Lgroovy/beans/BindableASTTransformation;-><init>()V

    return-void
.end method

.method private addListenerToClass(Lorg/codehaus/groovy/control/SourceUnit;Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 4

    .line 143
    invoke-static {p2}, Lgroovy/beans/BindableASTTransformation;->hasBindableAnnotation(Lorg/codehaus/groovy/ast/AnnotatedNode;)Z

    move-result v0

    .line 144
    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/ClassNode;->getProperties()Ljava/util/List;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :cond_0
    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_3

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lorg/codehaus/groovy/ast/PropertyNode;

    .line 145
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/PropertyNode;->getField()Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object v3

    invoke-static {v3}, Lgroovy/beans/VetoableASTTransformation;->hasVetoableAnnotation(Lorg/codehaus/groovy/ast/AnnotatedNode;)Z

    move-result v3

    if-nez v3, :cond_0

    .line 146
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/PropertyNode;->getField()Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object v3

    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/FieldNode;->isFinal()Z

    move-result v3

    if-nez v3, :cond_0

    .line 147
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/PropertyNode;->getField()Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object v3

    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/FieldNode;->isStatic()Z

    move-result v3

    if-nez v3, :cond_0

    if-nez v0, :cond_2

    .line 150
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/PropertyNode;->getField()Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object v3

    invoke-static {v3}, Lgroovy/beans/BindableASTTransformation;->hasBindableAnnotation(Lorg/codehaus/groovy/ast/AnnotatedNode;)Z

    move-result v3

    if-eqz v3, :cond_1

    goto :goto_1

    :cond_1
    const/4 v3, 0x0

    goto :goto_2

    :cond_2
    :goto_1
    const/4 v3, 0x1

    .line 149
    :goto_2
    invoke-direct {p0, p1, v3, p2, v2}, Lgroovy/beans/VetoableASTTransformation;->createListenerSetter(Lorg/codehaus/groovy/control/SourceUnit;ZLorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/PropertyNode;)V

    goto :goto_0

    :cond_3
    return-void
.end method

.method private addListenerToProperty(Lorg/codehaus/groovy/control/SourceUnit;Lorg/codehaus/groovy/ast/AnnotationNode;Lorg/codehaus/groovy/ast/AnnotatedNode;)V
    .locals 7

    .line 120
    invoke-virtual {p3}, Lorg/codehaus/groovy/ast/AnnotatedNode;->getDeclaringClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    .line 121
    move-object v1, p3

    check-cast v1, Lorg/codehaus/groovy/ast/FieldNode;

    .line 122
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object v2

    .line 123
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->getProperties()Ljava/util/List;

    move-result-object v3

    invoke-interface {v3}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v3

    :cond_0
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    move-result v4

    if-eqz v4, :cond_4

    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lorg/codehaus/groovy/ast/PropertyNode;

    .line 124
    invoke-static {p3}, Lgroovy/beans/BindableASTTransformation;->hasBindableAnnotation(Lorg/codehaus/groovy/ast/AnnotatedNode;)Z

    move-result v5

    if-nez v5, :cond_2

    .line 125
    invoke-virtual {p3}, Lorg/codehaus/groovy/ast/AnnotatedNode;->getDeclaringClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v5

    invoke-static {v5}, Lgroovy/beans/BindableASTTransformation;->hasBindableAnnotation(Lorg/codehaus/groovy/ast/AnnotatedNode;)Z

    move-result v5

    if-eqz v5, :cond_1

    goto :goto_0

    :cond_1
    const/4 v5, 0x0

    goto :goto_1

    :cond_2
    :goto_0
    const/4 v5, 0x1

    .line 127
    :goto_1
    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/PropertyNode;->getName()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v6, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v6

    if-eqz v6, :cond_0

    .line 128
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/FieldNode;->isStatic()Z

    move-result p3

    if-eqz p3, :cond_3

    .line 130
    invoke-virtual {p1}, Lorg/codehaus/groovy/control/SourceUnit;->getErrorCollector()Lorg/codehaus/groovy/control/ErrorCollector;

    move-result-object p3

    const-string v0, "@groovy.beans.Vetoable cannot annotate a static property."

    invoke-virtual {p3, v0, p2, p1}, Lorg/codehaus/groovy/control/ErrorCollector;->addErrorAndContinue(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;Lorg/codehaus/groovy/control/SourceUnit;)V

    goto :goto_2

    .line 132
    :cond_3
    invoke-direct {p0, p1, v5, v0, v4}, Lgroovy/beans/VetoableASTTransformation;->createListenerSetter(Lorg/codehaus/groovy/control/SourceUnit;ZLorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/PropertyNode;)V

    :goto_2
    return-void

    .line 138
    :cond_4
    invoke-virtual {p1}, Lorg/codehaus/groovy/control/SourceUnit;->getErrorCollector()Lorg/codehaus/groovy/control/ErrorCollector;

    move-result-object p3

    const-string v0, "@groovy.beans.Vetoable must be on a property, not a field.  Try removing the private, protected, or public modifier."

    invoke-virtual {p3, v0, p2, p1}, Lorg/codehaus/groovy/control/ErrorCollector;->addErrorAndContinue(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;Lorg/codehaus/groovy/control/SourceUnit;)V

    return-void
.end method

.method private createListenerSetter(Lorg/codehaus/groovy/control/SourceUnit;ZLorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/PropertyNode;)V
    .locals 3

    if-eqz p2, :cond_0

    .line 196
    invoke-virtual {p0, p3, p1}, Lgroovy/beans/VetoableASTTransformation;->needsPropertyChangeSupport(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/control/SourceUnit;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 197
    invoke-virtual {p0, p3}, Lgroovy/beans/VetoableASTTransformation;->addPropertyChangeSupport(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 199
    :cond_0
    invoke-virtual {p0, p3, p1}, Lgroovy/beans/VetoableASTTransformation;->needsVetoableChangeSupport(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/control/SourceUnit;)Z

    move-result p1

    if-eqz p1, :cond_1

    .line 200
    invoke-virtual {p0, p3}, Lgroovy/beans/VetoableASTTransformation;->addVetoableChangeSupport(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 202
    :cond_1
    invoke-virtual {p4}, Lorg/codehaus/groovy/ast/PropertyNode;->getName()Ljava/lang/String;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->getSetterName(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    .line 203
    invoke-virtual {p3, p1}, Lorg/codehaus/groovy/ast/ClassNode;->getMethods(Ljava/lang/String;)Ljava/util/List;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_3

    .line 204
    invoke-virtual {p4}, Lorg/codehaus/groovy/ast/PropertyNode;->getField()Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->fieldX(Lorg/codehaus/groovy/ast/FieldNode;)Lorg/codehaus/groovy/ast/expr/FieldExpression;

    move-result-object v0

    .line 205
    new-instance v1, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    invoke-direct {v1}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;-><init>()V

    .line 206
    invoke-virtual {p0, p4, v0}, Lgroovy/beans/VetoableASTTransformation;->createConstrainedStatement(Lorg/codehaus/groovy/ast/PropertyNode;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v2

    invoke-virtual {v1, v2}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    if-eqz p2, :cond_2

    .line 208
    invoke-virtual {p0, p4, v0}, Lgroovy/beans/VetoableASTTransformation;->createBindableStatement(Lorg/codehaus/groovy/ast/PropertyNode;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p2

    invoke-virtual {v1, p2}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    goto :goto_0

    .line 210
    :cond_2
    invoke-virtual {p0, v0}, Lgroovy/beans/VetoableASTTransformation;->createSetStatement(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p2

    invoke-virtual {v1, p2}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 214
    :goto_0
    invoke-virtual {p0, p3, p4, p1, v1}, Lgroovy/beans/VetoableASTTransformation;->createSetterMethod(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/PropertyNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/stmt/Statement;)V

    goto :goto_1

    .line 216
    :cond_3
    invoke-virtual {p4}, Lorg/codehaus/groovy/ast/PropertyNode;->getName()Ljava/lang/String;

    move-result-object p1

    invoke-static {p3, p2, p1}, Lgroovy/beans/VetoableASTTransformation;->wrapSetterMethod(Lorg/codehaus/groovy/ast/ClassNode;ZLjava/lang/String;)V

    :goto_1
    return-void
.end method

.method public static hasVetoableAnnotation(Lorg/codehaus/groovy/ast/AnnotatedNode;)Z
    .locals 2

    .line 88
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/AnnotatedNode;->getAnnotations()Ljava/util/List;

    move-result-object p0

    invoke-interface {p0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p0

    :cond_0
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_1

    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/ast/AnnotationNode;

    .line 89
    sget-object v1, Lgroovy/beans/VetoableASTTransformation;->constrainedClassNode:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/AnnotationNode;->getClassNode()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    invoke-virtual {v1, v0}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 p0, 0x1

    return p0

    :cond_1
    const/4 p0, 0x0

    return p0
.end method

.method private static wrapSetterMethod(Lorg/codehaus/groovy/ast/ClassNode;ZLjava/lang/String;)V
    .locals 11

    .line 160
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "get"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-static {p2}, Lorg/apache/groovy/util/BeanUtils;->capitalize(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    .line 161
    invoke-static {p2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->getSetterName(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0, v1}, Lorg/codehaus/groovy/ast/ClassNode;->getSetterMethod(Ljava/lang/String;)Lorg/codehaus/groovy/ast/MethodNode;

    move-result-object p0

    if-eqz p0, :cond_1

    .line 165
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/MethodNode;->getCode()Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v1

    const-string v2, "$oldValue"

    .line 167
    invoke-static {v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->localVarX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v2

    const-string v3, "$newValue"

    .line 168
    invoke-static {v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->localVarX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v3

    .line 169
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/MethodNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v4

    const/4 v5, 0x0

    aget-object v4, v4, v5

    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/Parameter;->getName()Ljava/lang/String;

    move-result-object v4

    invoke-static {v4}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v4

    .line 170
    new-instance v6, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    invoke-direct {v6}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;-><init>()V

    .line 173
    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callThisX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object v7

    invoke-static {v2, v7}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->declS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v7

    invoke-virtual {v6, v7}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    const/4 v7, 0x3

    new-array v8, v7, [Lorg/codehaus/groovy/ast/expr/Expression;

    .line 177
    invoke-static {p2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->constX(Ljava/lang/Object;)Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    move-result-object v9

    aput-object v9, v8, v5

    const/4 v9, 0x1

    aput-object v2, v8, v9

    const/4 v10, 0x2

    aput-object v4, v8, v10

    .line 176
    invoke-static {v8}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->args([Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    move-result-object v4

    const-string v8, "fireVetoableChange"

    invoke-static {v8, v4}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callThisX(Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object v4

    invoke-static {v4}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->stmt(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v4

    invoke-virtual {v6, v4}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 180
    invoke-virtual {v6, v1}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    if-eqz p1, :cond_0

    .line 184
    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callThisX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object p1

    invoke-static {v3, p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->declS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    invoke-virtual {v6, p1}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    new-array p1, v7, [Lorg/codehaus/groovy/ast/expr/Expression;

    .line 187
    invoke-static {p2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->constX(Ljava/lang/Object;)Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    move-result-object p2

    aput-object p2, p1, v5

    aput-object v2, p1, v9

    aput-object v3, p1, v10

    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->args([Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    move-result-object p1

    const-string p2, "firePropertyChange"

    invoke-static {p2, p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callThisX(Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->stmt(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    invoke-virtual {v6, p1}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 191
    :cond_0
    invoke-virtual {p0, v6}, Lorg/codehaus/groovy/ast/MethodNode;->setCode(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    :cond_1
    return-void
.end method


# virtual methods
.method protected addVetoableChangeSupport(Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 30

    move-object/from16 v0, p1

    .line 331
    const-class v1, Lgroovyjarjaropenbeans/VetoableChangeSupport;

    invoke-static {v1}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    .line 332
    const-class v2, Lgroovyjarjaropenbeans/VetoableChangeListener;

    invoke-static {v2}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v2

    const/4 v3, 0x1

    new-array v4, v3, [Lorg/codehaus/groovy/ast/expr/Expression;

    const-string v5, "this"

    .line 340
    invoke-static {v5}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v5

    const/4 v6, 0x0

    aput-object v5, v4, v6

    invoke-static {v4}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->args([Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    move-result-object v4

    invoke-static {v1, v4}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ctorX(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;

    move-result-object v4

    const-string v5, "this$vetoableChangeSupport"

    const/16 v7, 0x1012

    .line 336
    invoke-virtual {v0, v5, v7, v1, v4}, Lorg/codehaus/groovy/ast/ClassNode;->addField(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object v1

    .line 346
    new-instance v4, Lorg/codehaus/groovy/ast/MethodNode;

    sget-object v10, Lorg/codehaus/groovy/ast/ClassHelper;->VOID_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    new-array v5, v3, [Lorg/codehaus/groovy/ast/Parameter;

    const-string v14, "listener"

    .line 351
    invoke-static {v2, v14}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->param(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v7

    aput-object v7, v5, v6

    invoke-static {v5}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->params([Lorg/codehaus/groovy/ast/Parameter;)[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v11

    sget-object v12, Lorg/codehaus/groovy/ast/ClassNode;->EMPTY_ARRAY:[Lorg/codehaus/groovy/ast/ClassNode;

    .line 353
    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->fieldX(Lorg/codehaus/groovy/ast/FieldNode;)Lorg/codehaus/groovy/ast/expr/FieldExpression;

    move-result-object v5

    new-array v7, v3, [Lorg/codehaus/groovy/ast/expr/Expression;

    invoke-static {v14, v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v8

    aput-object v8, v7, v6

    invoke-static {v7}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->args([Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    move-result-object v7

    const-string v15, "addVetoableChangeListener"

    invoke-static {v5, v15, v7}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object v5

    invoke-static {v5}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->stmt(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v13

    const-string v8, "addVetoableChangeListener"

    const/4 v9, 0x1

    move-object v7, v4

    invoke-direct/range {v7 .. v13}, Lorg/codehaus/groovy/ast/MethodNode;-><init>(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 346
    invoke-static {v0, v4}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->addGeneratedMethod(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/MethodNode;)V

    .line 359
    new-instance v4, Lorg/codehaus/groovy/ast/MethodNode;

    sget-object v19, Lorg/codehaus/groovy/ast/ClassHelper;->VOID_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    const/4 v5, 0x2

    new-array v7, v5, [Lorg/codehaus/groovy/ast/Parameter;

    sget-object v8, Lorg/codehaus/groovy/ast/ClassHelper;->STRING_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    const-string v9, "name"

    .line 364
    invoke-static {v8, v9}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->param(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v8

    aput-object v8, v7, v6

    invoke-static {v2, v14}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->param(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v8

    aput-object v8, v7, v3

    invoke-static {v7}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->params([Lorg/codehaus/groovy/ast/Parameter;)[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v20

    sget-object v21, Lorg/codehaus/groovy/ast/ClassNode;->EMPTY_ARRAY:[Lorg/codehaus/groovy/ast/ClassNode;

    .line 366
    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->fieldX(Lorg/codehaus/groovy/ast/FieldNode;)Lorg/codehaus/groovy/ast/expr/FieldExpression;

    move-result-object v7

    new-array v8, v5, [Lorg/codehaus/groovy/ast/expr/Expression;

    sget-object v10, Lorg/codehaus/groovy/ast/ClassHelper;->STRING_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {v9, v10}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v10

    aput-object v10, v8, v6

    invoke-static {v14, v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v10

    aput-object v10, v8, v3

    invoke-static {v8}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->args([Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    move-result-object v8

    invoke-static {v7, v15, v8}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object v7

    invoke-static {v7}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->stmt(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v22

    const-string v17, "addVetoableChangeListener"

    const/16 v18, 0x1

    move-object/from16 v16, v4

    invoke-direct/range {v16 .. v22}, Lorg/codehaus/groovy/ast/MethodNode;-><init>(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 359
    invoke-static {v0, v4}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->addGeneratedMethod(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/MethodNode;)V

    .line 372
    new-instance v4, Lorg/codehaus/groovy/ast/MethodNode;

    sget-object v26, Lorg/codehaus/groovy/ast/ClassHelper;->VOID_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    new-array v7, v3, [Lorg/codehaus/groovy/ast/Parameter;

    .line 377
    invoke-static {v2, v14}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->param(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v8

    aput-object v8, v7, v6

    invoke-static {v7}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->params([Lorg/codehaus/groovy/ast/Parameter;)[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v27

    sget-object v28, Lorg/codehaus/groovy/ast/ClassNode;->EMPTY_ARRAY:[Lorg/codehaus/groovy/ast/ClassNode;

    .line 379
    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->fieldX(Lorg/codehaus/groovy/ast/FieldNode;)Lorg/codehaus/groovy/ast/expr/FieldExpression;

    move-result-object v7

    new-array v8, v3, [Lorg/codehaus/groovy/ast/expr/Expression;

    invoke-static {v14, v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v10

    aput-object v10, v8, v6

    invoke-static {v8}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->args([Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    move-result-object v8

    const-string v10, "removeVetoableChangeListener"

    invoke-static {v7, v10, v8}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object v7

    invoke-static {v7}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->stmt(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v29

    const-string v24, "removeVetoableChangeListener"

    const/16 v25, 0x1

    move-object/from16 v23, v4

    invoke-direct/range {v23 .. v29}, Lorg/codehaus/groovy/ast/MethodNode;-><init>(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 372
    invoke-static {v0, v4}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->addGeneratedMethod(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/MethodNode;)V

    .line 382
    new-instance v4, Lorg/codehaus/groovy/ast/MethodNode;

    sget-object v18, Lorg/codehaus/groovy/ast/ClassHelper;->VOID_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    new-array v7, v5, [Lorg/codehaus/groovy/ast/Parameter;

    sget-object v8, Lorg/codehaus/groovy/ast/ClassHelper;->STRING_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    .line 387
    invoke-static {v8, v9}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->param(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v8

    aput-object v8, v7, v6

    invoke-static {v2, v14}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->param(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v8

    aput-object v8, v7, v3

    invoke-static {v7}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->params([Lorg/codehaus/groovy/ast/Parameter;)[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v19

    sget-object v20, Lorg/codehaus/groovy/ast/ClassNode;->EMPTY_ARRAY:[Lorg/codehaus/groovy/ast/ClassNode;

    .line 389
    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->fieldX(Lorg/codehaus/groovy/ast/FieldNode;)Lorg/codehaus/groovy/ast/expr/FieldExpression;

    move-result-object v7

    new-array v8, v5, [Lorg/codehaus/groovy/ast/expr/Expression;

    sget-object v11, Lorg/codehaus/groovy/ast/ClassHelper;->STRING_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {v9, v11}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v11

    aput-object v11, v8, v6

    invoke-static {v14, v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v11

    aput-object v11, v8, v3

    invoke-static {v8}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->args([Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    move-result-object v8

    invoke-static {v7, v10, v8}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object v7

    invoke-static {v7}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->stmt(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v21

    const-string v16, "removeVetoableChangeListener"

    const/16 v17, 0x1

    move-object v15, v4

    invoke-direct/range {v15 .. v21}, Lorg/codehaus/groovy/ast/MethodNode;-><init>(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 382
    invoke-static {v0, v4}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->addGeneratedMethod(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/MethodNode;)V

    .line 397
    new-instance v4, Lorg/codehaus/groovy/ast/MethodNode;

    sget-object v25, Lorg/codehaus/groovy/ast/ClassHelper;->VOID_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    const/4 v7, 0x3

    new-array v8, v7, [Lorg/codehaus/groovy/ast/Parameter;

    sget-object v10, Lorg/codehaus/groovy/ast/ClassHelper;->STRING_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    .line 402
    invoke-static {v10, v9}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->param(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v10

    aput-object v10, v8, v6

    sget-object v10, Lorg/codehaus/groovy/ast/ClassHelper;->OBJECT_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    const-string v11, "oldValue"

    invoke-static {v10, v11}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->param(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v10

    aput-object v10, v8, v3

    sget-object v10, Lorg/codehaus/groovy/ast/ClassHelper;->OBJECT_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    const-string v12, "newValue"

    invoke-static {v10, v12}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->param(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v10

    aput-object v10, v8, v5

    invoke-static {v8}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->params([Lorg/codehaus/groovy/ast/Parameter;)[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v26

    new-array v8, v3, [Lorg/codehaus/groovy/ast/ClassNode;

    const-class v10, Lgroovyjarjaropenbeans/PropertyVetoException;

    .line 403
    invoke-static {v10}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v10

    aput-object v10, v8, v6

    .line 404
    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->fieldX(Lorg/codehaus/groovy/ast/FieldNode;)Lorg/codehaus/groovy/ast/expr/FieldExpression;

    move-result-object v10

    new-array v7, v7, [Lorg/codehaus/groovy/ast/expr/Expression;

    sget-object v13, Lorg/codehaus/groovy/ast/ClassHelper;->STRING_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {v9, v13}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v13

    aput-object v13, v7, v6

    invoke-static {v11}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v11

    aput-object v11, v7, v3

    invoke-static {v12}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v11

    aput-object v11, v7, v5

    invoke-static {v7}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->args([Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    move-result-object v5

    const-string v7, "fireVetoableChange"

    invoke-static {v10, v7, v5}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object v5

    invoke-static {v5}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->stmt(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v28

    const-string v23, "fireVetoableChange"

    const/16 v24, 0x1

    move-object/from16 v22, v4

    move-object/from16 v27, v8

    invoke-direct/range {v22 .. v28}, Lorg/codehaus/groovy/ast/MethodNode;-><init>(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 397
    invoke-static {v0, v4}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->addGeneratedMethod(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/MethodNode;)V

    .line 410
    new-instance v4, Lorg/codehaus/groovy/ast/MethodNode;

    .line 414
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/ClassNode;->makeArray()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v13

    sget-object v14, Lorg/codehaus/groovy/ast/Parameter;->EMPTY_ARRAY:[Lorg/codehaus/groovy/ast/Parameter;

    sget-object v15, Lorg/codehaus/groovy/ast/ClassNode;->EMPTY_ARRAY:[Lorg/codehaus/groovy/ast/ClassNode;

    .line 417
    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->fieldX(Lorg/codehaus/groovy/ast/FieldNode;)Lorg/codehaus/groovy/ast/expr/FieldExpression;

    move-result-object v5

    const-string v7, "getVetoableChangeListeners"

    invoke-static {v5, v7}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object v5

    invoke-static {v5}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->returnS(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v16

    const-string v11, "getVetoableChangeListeners"

    const/4 v12, 0x1

    move-object v10, v4

    invoke-direct/range {v10 .. v16}, Lorg/codehaus/groovy/ast/MethodNode;-><init>(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 410
    invoke-static {v0, v4}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->addGeneratedMethod(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/MethodNode;)V

    .line 423
    new-instance v4, Lorg/codehaus/groovy/ast/MethodNode;

    .line 427
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/ClassNode;->makeArray()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v20

    new-array v2, v3, [Lorg/codehaus/groovy/ast/Parameter;

    sget-object v5, Lorg/codehaus/groovy/ast/ClassHelper;->STRING_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    .line 428
    invoke-static {v5, v9}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->param(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v5

    aput-object v5, v2, v6

    invoke-static {v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->params([Lorg/codehaus/groovy/ast/Parameter;)[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v21

    sget-object v22, Lorg/codehaus/groovy/ast/ClassNode;->EMPTY_ARRAY:[Lorg/codehaus/groovy/ast/ClassNode;

    .line 430
    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->fieldX(Lorg/codehaus/groovy/ast/FieldNode;)Lorg/codehaus/groovy/ast/expr/FieldExpression;

    move-result-object v1

    new-array v2, v3, [Lorg/codehaus/groovy/ast/expr/Expression;

    sget-object v3, Lorg/codehaus/groovy/ast/ClassHelper;->STRING_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {v9, v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v3

    aput-object v3, v2, v6

    invoke-static {v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->args([Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    move-result-object v2

    invoke-static {v1, v7, v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object v1

    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->returnS(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v23

    const-string v18, "getVetoableChangeListeners"

    const/16 v19, 0x1

    move-object/from16 v17, v4

    invoke-direct/range {v17 .. v23}, Lorg/codehaus/groovy/ast/MethodNode;-><init>(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 423
    invoke-static {v0, v4}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->addGeneratedMethod(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/MethodNode;)V

    return-void
.end method

.method protected createConstrainedStatement(Lorg/codehaus/groovy/ast/PropertyNode;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;
    .locals 2

    const/4 v0, 0x3

    new-array v0, v0, [Lorg/codehaus/groovy/ast/expr/Expression;

    .line 229
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/PropertyNode;->getName()Ljava/lang/String;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->constX(Ljava/lang/Object;)Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    move-result-object p1

    const/4 v1, 0x0

    aput-object p1, v0, v1

    const/4 p1, 0x1

    aput-object p2, v0, p1

    const-string p1, "value"

    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object p1

    const/4 p2, 0x2

    aput-object p1, v0, p2

    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->args([Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    move-result-object p1

    const-string p2, "fireVetoableChange"

    invoke-static {p2, p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callThisX(Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->stmt(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    return-object p1
.end method

.method protected createSetStatement(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;
    .locals 1

    const-string v0, "value"

    .line 242
    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v0

    invoke-static {p1, v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    return-object p1
.end method

.method protected createSetterMethod(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/PropertyNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/stmt/Statement;)V
    .locals 9

    const/4 v0, 0x1

    new-array v6, v0, [Lorg/codehaus/groovy/ast/ClassNode;

    .line 302
    const-class v1, Lgroovyjarjaropenbeans/PropertyVetoException;

    invoke-static {v1}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    const/4 v2, 0x0

    aput-object v1, v6, v2

    .line 303
    new-instance v8, Lorg/codehaus/groovy/ast/MethodNode;

    .line 305
    invoke-static {p2}, Lorg/codehaus/groovy/ast/tools/PropertyNodeUtils;->adjustPropertyModifiersForMethod(Lorg/codehaus/groovy/ast/PropertyNode;)I

    move-result v3

    sget-object v4, Lorg/codehaus/groovy/ast/ClassHelper;->VOID_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    new-array v1, v0, [Lorg/codehaus/groovy/ast/Parameter;

    .line 307
    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/PropertyNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p2

    const-string v5, "value"

    invoke-static {p2, v5}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->param(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/Parameter;

    move-result-object p2

    aput-object p2, v1, v2

    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->params([Lorg/codehaus/groovy/ast/Parameter;)[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v5

    move-object v1, v8

    move-object v2, p3

    move-object v7, p4

    invoke-direct/range {v1 .. v7}, Lorg/codehaus/groovy/ast/MethodNode;-><init>(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 310
    invoke-virtual {v8, v0}, Lorg/codehaus/groovy/ast/MethodNode;->setSynthetic(Z)V

    .line 312
    invoke-static {p1, v8}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->addGeneratedMethod(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/MethodNode;)V

    return-void
.end method

.method protected needsVetoableChangeSupport(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/control/SourceUnit;)Z
    .locals 9

    const/4 v0, 0x0

    move-object v1, p1

    move v2, v0

    move v3, v2

    move v4, v3

    :goto_0
    const/4 v5, 0x1

    if-eqz v1, :cond_8

    .line 258
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/ClassNode;->getMethods()Ljava/util/List;

    move-result-object v6

    invoke-interface {v6}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v6

    :cond_0
    invoke-interface {v6}, Ljava/util/Iterator;->hasNext()Z

    move-result v7

    if-eqz v7, :cond_7

    invoke-interface {v6}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v7

    check-cast v7, Lorg/codehaus/groovy/ast/MethodNode;

    if-nez v2, :cond_2

    .line 260
    invoke-virtual {v7}, Lorg/codehaus/groovy/ast/MethodNode;->getName()Ljava/lang/String;

    move-result-object v2

    const-string v8, "addVetoableChangeListener"

    invoke-virtual {v2, v8}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_1

    invoke-virtual {v7}, Lorg/codehaus/groovy/ast/MethodNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v2

    array-length v2, v2

    if-ne v2, v5, :cond_1

    goto :goto_1

    :cond_1
    move v2, v0

    goto :goto_2

    :cond_2
    :goto_1
    move v2, v5

    :goto_2
    if-nez v3, :cond_4

    .line 261
    invoke-virtual {v7}, Lorg/codehaus/groovy/ast/MethodNode;->getName()Ljava/lang/String;

    move-result-object v3

    const-string v8, "removeVetoableChangeListener"

    invoke-virtual {v3, v8}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_3

    invoke-virtual {v7}, Lorg/codehaus/groovy/ast/MethodNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v3

    array-length v3, v3

    if-ne v3, v5, :cond_3

    goto :goto_3

    :cond_3
    move v3, v0

    goto :goto_4

    :cond_4
    :goto_3
    move v3, v5

    :goto_4
    if-nez v4, :cond_6

    .line 262
    invoke-virtual {v7}, Lorg/codehaus/groovy/ast/MethodNode;->getName()Ljava/lang/String;

    move-result-object v4

    const-string v8, "fireVetoableChange"

    invoke-virtual {v4, v8}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v4

    if-eqz v4, :cond_5

    invoke-virtual {v7}, Lorg/codehaus/groovy/ast/MethodNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v4

    array-length v4, v4

    const/4 v7, 0x3

    if-ne v4, v7, :cond_5

    goto :goto_5

    :cond_5
    move v4, v0

    goto :goto_6

    :cond_6
    :goto_5
    move v4, v5

    :goto_6
    if-eqz v2, :cond_0

    if-eqz v3, :cond_0

    if-eqz v4, :cond_0

    return v0

    .line 267
    :cond_7
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/ClassNode;->getSuperClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    goto :goto_0

    .line 270
    :cond_8
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getSuperClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    :goto_7
    if-eqz v1, :cond_c

    .line 272
    invoke-static {v1}, Lgroovy/beans/VetoableASTTransformation;->hasVetoableAnnotation(Lorg/codehaus/groovy/ast/AnnotatedNode;)Z

    move-result v6

    if-eqz v6, :cond_9

    return v0

    .line 273
    :cond_9
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/ClassNode;->getFields()Ljava/util/List;

    move-result-object v6

    invoke-interface {v6}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v6

    :cond_a
    invoke-interface {v6}, Ljava/util/Iterator;->hasNext()Z

    move-result v7

    if-eqz v7, :cond_b

    invoke-interface {v6}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v7

    check-cast v7, Lorg/codehaus/groovy/ast/FieldNode;

    .line 274
    invoke-static {v7}, Lgroovy/beans/VetoableASTTransformation;->hasVetoableAnnotation(Lorg/codehaus/groovy/ast/AnnotatedNode;)Z

    move-result v7

    if-eqz v7, :cond_a

    return v0

    .line 276
    :cond_b
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/ClassNode;->getSuperClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    goto :goto_7

    :cond_c
    if-nez v2, :cond_e

    if-nez v3, :cond_e

    if-eqz v4, :cond_d

    goto :goto_8

    :cond_d
    return v5

    .line 279
    :cond_e
    :goto_8
    invoke-virtual {p2}, Lorg/codehaus/groovy/control/SourceUnit;->getErrorCollector()Lorg/codehaus/groovy/control/ErrorCollector;

    move-result-object v1

    new-instance v2, Lorg/codehaus/groovy/control/messages/SimpleMessage;

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "@Vetoable cannot be processed on "

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    .line 281
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v3, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v3, " because some but not all of addVetoableChangeListener, removeVetoableChange, and fireVetoableChange were declared in the current or super classes."

    invoke-virtual {p1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {v2, p1, p2}, Lorg/codehaus/groovy/control/messages/SimpleMessage;-><init>(Ljava/lang/String;Lorg/codehaus/groovy/control/ProcessingUnit;)V

    .line 279
    invoke-virtual {v1, v2}, Lorg/codehaus/groovy/control/ErrorCollector;->addErrorAndContinue(Lorg/codehaus/groovy/control/messages/Message;)V

    return v0
.end method

.method public visit([Lorg/codehaus/groovy/ast/ASTNode;Lorg/codehaus/groovy/control/SourceUnit;)V
    .locals 4

    const/4 v0, 0x0

    .line 103
    aget-object v1, p1, v0

    instance-of v1, v1, Lorg/codehaus/groovy/ast/AnnotationNode;

    if-eqz v1, :cond_2

    const/4 v1, 0x1

    aget-object v2, p1, v1

    instance-of v2, v2, Lorg/codehaus/groovy/ast/AnnotatedNode;

    if-eqz v2, :cond_2

    .line 106
    aget-object v0, p1, v0

    check-cast v0, Lorg/codehaus/groovy/ast/AnnotationNode;

    .line 108
    aget-object v2, p1, v1

    instance-of v2, v2, Lorg/codehaus/groovy/ast/ClassNode;

    if-eqz v2, :cond_0

    .line 109
    aget-object p1, p1, v1

    check-cast p1, Lorg/codehaus/groovy/ast/ClassNode;

    invoke-direct {p0, p2, p1}, Lgroovy/beans/VetoableASTTransformation;->addListenerToClass(Lorg/codehaus/groovy/control/SourceUnit;Lorg/codehaus/groovy/ast/ClassNode;)V

    goto :goto_0

    .line 111
    :cond_0
    aget-object v2, p1, v1

    check-cast v2, Lorg/codehaus/groovy/ast/FieldNode;

    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/FieldNode;->getModifiers()I

    move-result v2

    and-int/lit8 v2, v2, 0x10

    if-eqz v2, :cond_1

    .line 112
    invoke-virtual {p2}, Lorg/codehaus/groovy/control/SourceUnit;->getErrorCollector()Lorg/codehaus/groovy/control/ErrorCollector;

    move-result-object v2

    const-string v3, "@groovy.beans.Vetoable cannot annotate a final property."

    invoke-virtual {v2, v3, v0, p2}, Lorg/codehaus/groovy/control/ErrorCollector;->addErrorAndContinue(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;Lorg/codehaus/groovy/control/SourceUnit;)V

    .line 115
    :cond_1
    aget-object p1, p1, v1

    check-cast p1, Lorg/codehaus/groovy/ast/AnnotatedNode;

    invoke-direct {p0, p2, v0, p1}, Lgroovy/beans/VetoableASTTransformation;->addListenerToProperty(Lorg/codehaus/groovy/control/SourceUnit;Lorg/codehaus/groovy/ast/AnnotationNode;Lorg/codehaus/groovy/ast/AnnotatedNode;)V

    :goto_0
    return-void

    .line 104
    :cond_2
    new-instance p1, Ljava/lang/RuntimeException;

    const-string p2, "Internal error: wrong types: $node.class / $parent.class"

    invoke-direct {p1, p2}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/String;)V

    throw p1
.end method
