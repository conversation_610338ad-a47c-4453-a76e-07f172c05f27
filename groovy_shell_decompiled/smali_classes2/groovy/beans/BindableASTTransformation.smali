.class public Lgroovy/beans/BindableASTTransformation;
.super Ljava/lang/Object;
.source "BindableASTTransformation.java"

# interfaces
.implements Lorg/codehaus/groovy/transform/ASTTransformation;
.implements Lgroovyjarjarasm/asm/Opcodes;


# annotations
.annotation runtime Lorg/codehaus/groovy/transform/GroovyASTTransformation;
    phase = .enum Lorg/codehaus/groovy/control/CompilePhase;->CANONICALIZATION:Lorg/codehaus/groovy/control/CompilePhase;
.end annotation


# static fields
.field protected static final boundClassNode:Lorg/codehaus/groovy/ast/ClassNode;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 79
    const-class v0, Lgroovy/beans/Bindable;

    invoke-static {v0}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    sput-object v0, Lgroovy/beans/BindableASTTransformation;->boundClassNode:Lorg/codehaus/groovy/ast/ClassNode;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 77
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method private addListenerToClass(Lorg/codehaus/groovy/control/SourceUnit;Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 3

    .line 151
    invoke-virtual {p0, p2, p1}, Lgroovy/beans/BindableASTTransformation;->needsPropertyChangeSupport(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/control/SourceUnit;)Z

    move-result p1

    if-eqz p1, :cond_0

    .line 152
    invoke-virtual {p0, p2}, Lgroovy/beans/BindableASTTransformation;->addPropertyChangeSupport(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 154
    :cond_0
    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/ClassNode;->getProperties()Ljava/util/List;

    move-result-object p1

    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_1
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_3

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/ast/PropertyNode;

    .line 155
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/PropertyNode;->getField()Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object v1

    .line 157
    invoke-static {v1}, Lgroovy/beans/BindableASTTransformation;->hasBindableAnnotation(Lorg/codehaus/groovy/ast/AnnotatedNode;)Z

    move-result v2

    if-nez v2, :cond_1

    .line 158
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/FieldNode;->getModifiers()I

    move-result v2

    and-int/lit8 v2, v2, 0x10

    if-nez v2, :cond_1

    .line 159
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/FieldNode;->isStatic()Z

    move-result v2

    if-nez v2, :cond_1

    .line 160
    invoke-static {v1}, Lgroovy/beans/VetoableASTTransformation;->hasVetoableAnnotation(Lorg/codehaus/groovy/ast/AnnotatedNode;)Z

    move-result v1

    if-eqz v1, :cond_2

    goto :goto_0

    .line 168
    :cond_2
    invoke-direct {p0, p2, v0}, Lgroovy/beans/BindableASTTransformation;->createListenerSetter(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/PropertyNode;)V

    goto :goto_0

    :cond_3
    return-void
.end method

.method private addListenerToProperty(Lorg/codehaus/groovy/control/SourceUnit;Lorg/codehaus/groovy/ast/AnnotationNode;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/FieldNode;)V
    .locals 4

    .line 131
    invoke-virtual {p4}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object v0

    .line 132
    invoke-virtual {p3}, Lorg/codehaus/groovy/ast/ClassNode;->getProperties()Ljava/util/List;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :cond_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_3

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lorg/codehaus/groovy/ast/PropertyNode;

    .line 133
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/PropertyNode;->getName()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v3, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_0

    .line 134
    invoke-virtual {p4}, Lorg/codehaus/groovy/ast/FieldNode;->isStatic()Z

    move-result p4

    if-eqz p4, :cond_1

    .line 136
    invoke-virtual {p1}, Lorg/codehaus/groovy/control/SourceUnit;->getErrorCollector()Lorg/codehaus/groovy/control/ErrorCollector;

    move-result-object p3

    const-string p4, "@groovy.beans.Bindable cannot annotate a static property."

    invoke-virtual {p3, p4, p2, p1}, Lorg/codehaus/groovy/control/ErrorCollector;->addErrorAndContinue(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;Lorg/codehaus/groovy/control/SourceUnit;)V

    goto :goto_0

    .line 138
    :cond_1
    invoke-virtual {p0, p3, p1}, Lgroovy/beans/BindableASTTransformation;->needsPropertyChangeSupport(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/control/SourceUnit;)Z

    move-result p1

    if-eqz p1, :cond_2

    .line 139
    invoke-virtual {p0, p3}, Lgroovy/beans/BindableASTTransformation;->addPropertyChangeSupport(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 141
    :cond_2
    invoke-direct {p0, p3, v2}, Lgroovy/beans/BindableASTTransformation;->createListenerSetter(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/PropertyNode;)V

    :goto_0
    return-void

    .line 147
    :cond_3
    invoke-virtual {p1}, Lorg/codehaus/groovy/control/SourceUnit;->getErrorCollector()Lorg/codehaus/groovy/control/ErrorCollector;

    move-result-object p3

    const-string p4, "@groovy.beans.Bindable must be on a property, not a field.  Try removing the private, protected, or public modifier."

    invoke-virtual {p3, p4, p2, p1}, Lorg/codehaus/groovy/control/ErrorCollector;->addErrorAndContinue(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;Lorg/codehaus/groovy/control/SourceUnit;)V

    return-void
.end method

.method private createListenerSetter(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/PropertyNode;)V
    .locals 2

    .line 205
    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/PropertyNode;->getName()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->getSetterName(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    .line 206
    invoke-virtual {p1, v0}, Lorg/codehaus/groovy/ast/ClassNode;->getMethods(Ljava/lang/String;)Ljava/util/List;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/List;->isEmpty()Z

    move-result v1

    if-eqz v1, :cond_0

    .line 207
    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/PropertyNode;->getField()Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object v1

    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->fieldX(Lorg/codehaus/groovy/ast/FieldNode;)Lorg/codehaus/groovy/ast/expr/FieldExpression;

    move-result-object v1

    invoke-virtual {p0, p2, v1}, Lgroovy/beans/BindableASTTransformation;->createBindableStatement(Lorg/codehaus/groovy/ast/PropertyNode;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v1

    .line 210
    invoke-virtual {p0, p1, p2, v0, v1}, Lgroovy/beans/BindableASTTransformation;->createSetterMethod(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/PropertyNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/stmt/Statement;)V

    goto :goto_0

    .line 212
    :cond_0
    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/PropertyNode;->getName()Ljava/lang/String;

    move-result-object p2

    invoke-static {p1, p2}, Lgroovy/beans/BindableASTTransformation;->wrapSetterMethod(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)V

    :goto_0
    return-void
.end method

.method public static hasBindableAnnotation(Lorg/codehaus/groovy/ast/AnnotatedNode;)Z
    .locals 2

    .line 88
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/AnnotatedNode;->getAnnotations()Ljava/util/List;

    move-result-object p0

    invoke-interface {p0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p0

    :cond_0
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_1

    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/ast/AnnotationNode;

    .line 89
    sget-object v1, Lgroovy/beans/BindableASTTransformation;->boundClassNode:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/AnnotationNode;->getClassNode()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    invoke-virtual {v1, v0}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 p0, 0x1

    return p0

    :cond_1
    const/4 p0, 0x0

    return p0
.end method

.method private static wrapSetterMethod(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)V
    .locals 6

    .line 176
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "get"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-static {p1}, Lorg/apache/groovy/util/BeanUtils;->capitalize(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    .line 177
    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->getSetterName(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0, v1}, Lorg/codehaus/groovy/ast/ClassNode;->getSetterMethod(Ljava/lang/String;)Lorg/codehaus/groovy/ast/MethodNode;

    move-result-object p0

    if-eqz p0, :cond_0

    .line 181
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/MethodNode;->getCode()Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v1

    const-string v2, "$oldValue"

    .line 183
    invoke-static {v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->localVarX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v2

    const-string v3, "$newValue"

    .line 184
    invoke-static {v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->localVarX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v3

    .line 185
    new-instance v4, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    invoke-direct {v4}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;-><init>()V

    .line 188
    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callThisX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object v5

    invoke-static {v2, v5}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->declS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v5

    invoke-virtual {v4, v5}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 191
    invoke-virtual {v4, v1}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 194
    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callThisX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object v0

    invoke-static {v3, v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->declS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v0

    invoke-virtual {v4, v0}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    const/4 v0, 0x3

    new-array v0, v0, [Lorg/codehaus/groovy/ast/expr/Expression;

    const/4 v1, 0x0

    .line 197
    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->constX(Ljava/lang/Object;)Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    move-result-object p1

    aput-object p1, v0, v1

    const/4 p1, 0x1

    aput-object v2, v0, p1

    const/4 p1, 0x2

    aput-object v3, v0, p1

    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->args([Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    move-result-object p1

    const-string v0, "firePropertyChange"

    invoke-static {v0, p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callThisX(Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->stmt(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    invoke-virtual {v4, p1}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 200
    invoke-virtual {p0, v4}, Lorg/codehaus/groovy/ast/MethodNode;->setCode(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    :cond_0
    return-void
.end method


# virtual methods
.method protected addPropertyChangeSupport(Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 30

    move-object/from16 v0, p1

    .line 317
    const-class v1, Lgroovyjarjaropenbeans/PropertyChangeSupport;

    invoke-static {v1}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    .line 318
    const-class v2, Lgroovyjarjaropenbeans/PropertyChangeListener;

    invoke-static {v2}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v2

    const/4 v3, 0x1

    new-array v4, v3, [Lorg/codehaus/groovy/ast/expr/Expression;

    const-string v5, "this"

    .line 327
    invoke-static {v5}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v5

    const/4 v6, 0x0

    aput-object v5, v4, v6

    invoke-static {v4}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->args([Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    move-result-object v4

    invoke-static {v1, v4}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ctorX(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;

    move-result-object v4

    const-string v5, "this$propertyChangeSupport"

    const/16 v7, 0x1012

    .line 323
    invoke-virtual {v0, v5, v7, v1, v4}, Lorg/codehaus/groovy/ast/ClassNode;->addField(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object v1

    .line 333
    new-instance v4, Lorg/codehaus/groovy/ast/MethodNode;

    sget-object v10, Lorg/codehaus/groovy/ast/ClassHelper;->VOID_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    new-array v5, v3, [Lorg/codehaus/groovy/ast/Parameter;

    const-string v14, "listener"

    .line 338
    invoke-static {v2, v14}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->param(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v7

    aput-object v7, v5, v6

    invoke-static {v5}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->params([Lorg/codehaus/groovy/ast/Parameter;)[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v11

    sget-object v12, Lorg/codehaus/groovy/ast/ClassNode;->EMPTY_ARRAY:[Lorg/codehaus/groovy/ast/ClassNode;

    .line 340
    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->fieldX(Lorg/codehaus/groovy/ast/FieldNode;)Lorg/codehaus/groovy/ast/expr/FieldExpression;

    move-result-object v5

    new-array v7, v3, [Lorg/codehaus/groovy/ast/expr/Expression;

    invoke-static {v14, v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v8

    aput-object v8, v7, v6

    invoke-static {v7}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->args([Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    move-result-object v7

    const-string v15, "addPropertyChangeListener"

    invoke-static {v5, v15, v7}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object v5

    invoke-static {v5}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->stmt(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v13

    const-string v8, "addPropertyChangeListener"

    const/4 v9, 0x1

    move-object v7, v4

    invoke-direct/range {v7 .. v13}, Lorg/codehaus/groovy/ast/MethodNode;-><init>(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 333
    invoke-static {v0, v4}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->addGeneratedMethod(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/MethodNode;)V

    .line 346
    new-instance v4, Lorg/codehaus/groovy/ast/MethodNode;

    sget-object v19, Lorg/codehaus/groovy/ast/ClassHelper;->VOID_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    const/4 v5, 0x2

    new-array v7, v5, [Lorg/codehaus/groovy/ast/Parameter;

    sget-object v8, Lorg/codehaus/groovy/ast/ClassHelper;->STRING_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    const-string v9, "name"

    .line 351
    invoke-static {v8, v9}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->param(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v8

    aput-object v8, v7, v6

    invoke-static {v2, v14}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->param(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v8

    aput-object v8, v7, v3

    invoke-static {v7}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->params([Lorg/codehaus/groovy/ast/Parameter;)[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v20

    sget-object v21, Lorg/codehaus/groovy/ast/ClassNode;->EMPTY_ARRAY:[Lorg/codehaus/groovy/ast/ClassNode;

    .line 353
    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->fieldX(Lorg/codehaus/groovy/ast/FieldNode;)Lorg/codehaus/groovy/ast/expr/FieldExpression;

    move-result-object v7

    new-array v8, v5, [Lorg/codehaus/groovy/ast/expr/Expression;

    sget-object v10, Lorg/codehaus/groovy/ast/ClassHelper;->STRING_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {v9, v10}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v10

    aput-object v10, v8, v6

    invoke-static {v14, v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v10

    aput-object v10, v8, v3

    invoke-static {v8}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->args([Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    move-result-object v8

    invoke-static {v7, v15, v8}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object v7

    invoke-static {v7}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->stmt(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v22

    const-string v17, "addPropertyChangeListener"

    const/16 v18, 0x1

    move-object/from16 v16, v4

    invoke-direct/range {v16 .. v22}, Lorg/codehaus/groovy/ast/MethodNode;-><init>(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 346
    invoke-static {v0, v4}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->addGeneratedMethod(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/MethodNode;)V

    .line 359
    new-instance v4, Lorg/codehaus/groovy/ast/MethodNode;

    sget-object v26, Lorg/codehaus/groovy/ast/ClassHelper;->VOID_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    new-array v7, v3, [Lorg/codehaus/groovy/ast/Parameter;

    .line 364
    invoke-static {v2, v14}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->param(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v8

    aput-object v8, v7, v6

    invoke-static {v7}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->params([Lorg/codehaus/groovy/ast/Parameter;)[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v27

    sget-object v28, Lorg/codehaus/groovy/ast/ClassNode;->EMPTY_ARRAY:[Lorg/codehaus/groovy/ast/ClassNode;

    .line 366
    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->fieldX(Lorg/codehaus/groovy/ast/FieldNode;)Lorg/codehaus/groovy/ast/expr/FieldExpression;

    move-result-object v7

    new-array v8, v3, [Lorg/codehaus/groovy/ast/expr/Expression;

    invoke-static {v14, v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v10

    aput-object v10, v8, v6

    invoke-static {v8}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->args([Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    move-result-object v8

    const-string v10, "removePropertyChangeListener"

    invoke-static {v7, v10, v8}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object v7

    invoke-static {v7}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->stmt(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v29

    const-string v24, "removePropertyChangeListener"

    const/16 v25, 0x1

    move-object/from16 v23, v4

    invoke-direct/range {v23 .. v29}, Lorg/codehaus/groovy/ast/MethodNode;-><init>(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 359
    invoke-static {v0, v4}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->addGeneratedMethod(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/MethodNode;)V

    .line 369
    new-instance v4, Lorg/codehaus/groovy/ast/MethodNode;

    sget-object v18, Lorg/codehaus/groovy/ast/ClassHelper;->VOID_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    new-array v7, v5, [Lorg/codehaus/groovy/ast/Parameter;

    sget-object v8, Lorg/codehaus/groovy/ast/ClassHelper;->STRING_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    .line 374
    invoke-static {v8, v9}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->param(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v8

    aput-object v8, v7, v6

    invoke-static {v2, v14}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->param(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v8

    aput-object v8, v7, v3

    invoke-static {v7}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->params([Lorg/codehaus/groovy/ast/Parameter;)[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v19

    sget-object v20, Lorg/codehaus/groovy/ast/ClassNode;->EMPTY_ARRAY:[Lorg/codehaus/groovy/ast/ClassNode;

    .line 376
    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->fieldX(Lorg/codehaus/groovy/ast/FieldNode;)Lorg/codehaus/groovy/ast/expr/FieldExpression;

    move-result-object v7

    new-array v8, v5, [Lorg/codehaus/groovy/ast/expr/Expression;

    sget-object v11, Lorg/codehaus/groovy/ast/ClassHelper;->STRING_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {v9, v11}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v11

    aput-object v11, v8, v6

    invoke-static {v14, v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v11

    aput-object v11, v8, v3

    invoke-static {v8}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->args([Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    move-result-object v8

    invoke-static {v7, v10, v8}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object v7

    invoke-static {v7}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->stmt(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v21

    const-string v16, "removePropertyChangeListener"

    const/16 v17, 0x1

    move-object v15, v4

    invoke-direct/range {v15 .. v21}, Lorg/codehaus/groovy/ast/MethodNode;-><init>(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 369
    invoke-static {v0, v4}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->addGeneratedMethod(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/MethodNode;)V

    .line 382
    new-instance v4, Lorg/codehaus/groovy/ast/MethodNode;

    sget-object v25, Lorg/codehaus/groovy/ast/ClassHelper;->VOID_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    const/4 v7, 0x3

    new-array v8, v7, [Lorg/codehaus/groovy/ast/Parameter;

    sget-object v10, Lorg/codehaus/groovy/ast/ClassHelper;->STRING_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    .line 387
    invoke-static {v10, v9}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->param(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v10

    aput-object v10, v8, v6

    sget-object v10, Lorg/codehaus/groovy/ast/ClassHelper;->OBJECT_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    const-string v11, "oldValue"

    invoke-static {v10, v11}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->param(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v10

    aput-object v10, v8, v3

    sget-object v10, Lorg/codehaus/groovy/ast/ClassHelper;->OBJECT_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    const-string v12, "newValue"

    invoke-static {v10, v12}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->param(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v10

    aput-object v10, v8, v5

    invoke-static {v8}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->params([Lorg/codehaus/groovy/ast/Parameter;)[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v26

    sget-object v27, Lorg/codehaus/groovy/ast/ClassNode;->EMPTY_ARRAY:[Lorg/codehaus/groovy/ast/ClassNode;

    .line 389
    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->fieldX(Lorg/codehaus/groovy/ast/FieldNode;)Lorg/codehaus/groovy/ast/expr/FieldExpression;

    move-result-object v8

    new-array v7, v7, [Lorg/codehaus/groovy/ast/expr/Expression;

    sget-object v10, Lorg/codehaus/groovy/ast/ClassHelper;->STRING_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {v9, v10}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v10

    aput-object v10, v7, v6

    invoke-static {v11}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v10

    aput-object v10, v7, v3

    invoke-static {v12}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v10

    aput-object v10, v7, v5

    invoke-static {v7}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->args([Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    move-result-object v5

    const-string v7, "firePropertyChange"

    invoke-static {v8, v7, v5}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object v5

    invoke-static {v5}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->stmt(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v28

    const-string v23, "firePropertyChange"

    const/16 v24, 0x1

    move-object/from16 v22, v4

    invoke-direct/range {v22 .. v28}, Lorg/codehaus/groovy/ast/MethodNode;-><init>(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 382
    invoke-static {v0, v4}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->addGeneratedMethod(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/MethodNode;)V

    .line 395
    new-instance v4, Lorg/codehaus/groovy/ast/MethodNode;

    .line 399
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/ClassNode;->makeArray()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v13

    sget-object v14, Lorg/codehaus/groovy/ast/Parameter;->EMPTY_ARRAY:[Lorg/codehaus/groovy/ast/Parameter;

    sget-object v15, Lorg/codehaus/groovy/ast/ClassNode;->EMPTY_ARRAY:[Lorg/codehaus/groovy/ast/ClassNode;

    .line 402
    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->fieldX(Lorg/codehaus/groovy/ast/FieldNode;)Lorg/codehaus/groovy/ast/expr/FieldExpression;

    move-result-object v5

    const-string v7, "getPropertyChangeListeners"

    invoke-static {v5, v7}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object v5

    invoke-static {v5}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->returnS(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v16

    const-string v11, "getPropertyChangeListeners"

    const/4 v12, 0x1

    move-object v10, v4

    invoke-direct/range {v10 .. v16}, Lorg/codehaus/groovy/ast/MethodNode;-><init>(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 395
    invoke-static {v0, v4}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->addGeneratedMethod(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/MethodNode;)V

    .line 408
    new-instance v4, Lorg/codehaus/groovy/ast/MethodNode;

    .line 412
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/ClassNode;->makeArray()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v20

    new-array v2, v3, [Lorg/codehaus/groovy/ast/Parameter;

    sget-object v5, Lorg/codehaus/groovy/ast/ClassHelper;->STRING_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    .line 413
    invoke-static {v5, v9}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->param(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v5

    aput-object v5, v2, v6

    invoke-static {v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->params([Lorg/codehaus/groovy/ast/Parameter;)[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v21

    sget-object v22, Lorg/codehaus/groovy/ast/ClassNode;->EMPTY_ARRAY:[Lorg/codehaus/groovy/ast/ClassNode;

    .line 415
    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->fieldX(Lorg/codehaus/groovy/ast/FieldNode;)Lorg/codehaus/groovy/ast/expr/FieldExpression;

    move-result-object v1

    new-array v2, v3, [Lorg/codehaus/groovy/ast/expr/Expression;

    sget-object v3, Lorg/codehaus/groovy/ast/ClassHelper;->STRING_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {v9, v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v3

    aput-object v3, v2, v6

    invoke-static {v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->args([Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    move-result-object v2

    invoke-static {v1, v7, v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object v1

    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->returnS(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v23

    const-string v18, "getPropertyChangeListeners"

    const/16 v19, 0x1

    move-object/from16 v17, v4

    invoke-direct/range {v17 .. v23}, Lorg/codehaus/groovy/ast/MethodNode;-><init>(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 408
    invoke-static {v0, v4}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->addGeneratedMethod(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/MethodNode;)V

    return-void
.end method

.method protected createBindableStatement(Lorg/codehaus/groovy/ast/PropertyNode;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;
    .locals 2

    const/4 v0, 0x3

    new-array v0, v0, [Lorg/codehaus/groovy/ast/expr/Expression;

    .line 226
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/PropertyNode;->getName()Ljava/lang/String;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->constX(Ljava/lang/Object;)Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    move-result-object p1

    const/4 v1, 0x0

    aput-object p1, v0, v1

    const/4 p1, 0x1

    aput-object p2, v0, p1

    const-string p1, "value"

    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object p1

    invoke-static {p2, p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignX(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    const/4 p2, 0x2

    aput-object p1, v0, p2

    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->args([Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    move-result-object p1

    const-string p2, "firePropertyChange"

    invoke-static {p2, p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callThisX(Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->stmt(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    return-object p1
.end method

.method protected createSetterMethod(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/PropertyNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/stmt/Statement;)V
    .locals 9

    .line 238
    new-instance v7, Lorg/codehaus/groovy/ast/MethodNode;

    .line 240
    invoke-static {p2}, Lorg/codehaus/groovy/ast/tools/PropertyNodeUtils;->adjustPropertyModifiersForMethod(Lorg/codehaus/groovy/ast/PropertyNode;)I

    move-result v2

    sget-object v3, Lorg/codehaus/groovy/ast/ClassHelper;->VOID_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    const/4 v8, 0x1

    new-array v0, v8, [Lorg/codehaus/groovy/ast/Parameter;

    .line 242
    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/PropertyNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p2

    const-string v1, "value"

    invoke-static {p2, v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->param(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/Parameter;

    move-result-object p2

    const/4 v1, 0x0

    aput-object p2, v0, v1

    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->params([Lorg/codehaus/groovy/ast/Parameter;)[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v4

    sget-object v5, Lorg/codehaus/groovy/ast/ClassNode;->EMPTY_ARRAY:[Lorg/codehaus/groovy/ast/ClassNode;

    move-object v0, v7

    move-object v1, p3

    move-object v6, p4

    invoke-direct/range {v0 .. v6}, Lorg/codehaus/groovy/ast/MethodNode;-><init>(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 245
    invoke-virtual {v7, v8}, Lorg/codehaus/groovy/ast/MethodNode;->setSynthetic(Z)V

    .line 247
    invoke-static {p1, v7}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->addGeneratedMethod(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/MethodNode;)V

    return-void
.end method

.method protected needsPropertyChangeSupport(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/control/SourceUnit;)Z
    .locals 9

    const/4 v0, 0x0

    move-object v1, p1

    move v2, v0

    move v3, v2

    move v4, v3

    :goto_0
    const/4 v5, 0x1

    if-eqz v1, :cond_8

    .line 265
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/ClassNode;->getMethods()Ljava/util/List;

    move-result-object v6

    invoke-interface {v6}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v6

    :cond_0
    invoke-interface {v6}, Ljava/util/Iterator;->hasNext()Z

    move-result v7

    if-eqz v7, :cond_7

    invoke-interface {v6}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v7

    check-cast v7, Lorg/codehaus/groovy/ast/MethodNode;

    if-nez v2, :cond_2

    .line 267
    invoke-virtual {v7}, Lorg/codehaus/groovy/ast/MethodNode;->getName()Ljava/lang/String;

    move-result-object v2

    const-string v8, "addPropertyChangeListener"

    invoke-virtual {v2, v8}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_1

    invoke-virtual {v7}, Lorg/codehaus/groovy/ast/MethodNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v2

    array-length v2, v2

    if-ne v2, v5, :cond_1

    goto :goto_1

    :cond_1
    move v2, v0

    goto :goto_2

    :cond_2
    :goto_1
    move v2, v5

    :goto_2
    if-nez v3, :cond_4

    .line 268
    invoke-virtual {v7}, Lorg/codehaus/groovy/ast/MethodNode;->getName()Ljava/lang/String;

    move-result-object v3

    const-string v8, "removePropertyChangeListener"

    invoke-virtual {v3, v8}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_3

    invoke-virtual {v7}, Lorg/codehaus/groovy/ast/MethodNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v3

    array-length v3, v3

    if-ne v3, v5, :cond_3

    goto :goto_3

    :cond_3
    move v3, v0

    goto :goto_4

    :cond_4
    :goto_3
    move v3, v5

    :goto_4
    if-nez v4, :cond_6

    .line 269
    invoke-virtual {v7}, Lorg/codehaus/groovy/ast/MethodNode;->getName()Ljava/lang/String;

    move-result-object v4

    const-string v8, "firePropertyChange"

    invoke-virtual {v4, v8}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v4

    if-eqz v4, :cond_5

    invoke-virtual {v7}, Lorg/codehaus/groovy/ast/MethodNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v4

    array-length v4, v4

    const/4 v7, 0x3

    if-ne v4, v7, :cond_5

    goto :goto_5

    :cond_5
    move v4, v0

    goto :goto_6

    :cond_6
    :goto_5
    move v4, v5

    :goto_6
    if-eqz v2, :cond_0

    if-eqz v3, :cond_0

    if-eqz v4, :cond_0

    return v0

    .line 274
    :cond_7
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/ClassNode;->getSuperClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    goto :goto_0

    .line 277
    :cond_8
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getSuperClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    :goto_7
    if-eqz v1, :cond_c

    .line 279
    invoke-static {v1}, Lgroovy/beans/BindableASTTransformation;->hasBindableAnnotation(Lorg/codehaus/groovy/ast/AnnotatedNode;)Z

    move-result v6

    if-eqz v6, :cond_9

    return v0

    .line 280
    :cond_9
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/ClassNode;->getFields()Ljava/util/List;

    move-result-object v6

    invoke-interface {v6}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v6

    :cond_a
    invoke-interface {v6}, Ljava/util/Iterator;->hasNext()Z

    move-result v7

    if-eqz v7, :cond_b

    invoke-interface {v6}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v7

    check-cast v7, Lorg/codehaus/groovy/ast/FieldNode;

    .line 281
    invoke-static {v7}, Lgroovy/beans/BindableASTTransformation;->hasBindableAnnotation(Lorg/codehaus/groovy/ast/AnnotatedNode;)Z

    move-result v7

    if-eqz v7, :cond_a

    return v0

    .line 283
    :cond_b
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/ClassNode;->getSuperClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    goto :goto_7

    :cond_c
    if-nez v2, :cond_e

    if-nez v3, :cond_e

    if-eqz v4, :cond_d

    goto :goto_8

    :cond_d
    return v5

    .line 286
    :cond_e
    :goto_8
    invoke-virtual {p2}, Lorg/codehaus/groovy/control/SourceUnit;->getErrorCollector()Lorg/codehaus/groovy/control/ErrorCollector;

    move-result-object v1

    new-instance v2, Lorg/codehaus/groovy/control/messages/SimpleMessage;

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "@Bindable cannot be processed on "

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    .line 288
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v3, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v3, " because some but not all of addPropertyChangeListener, removePropertyChange, and firePropertyChange were declared in the current or super classes."

    invoke-virtual {p1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {v2, p1, p2}, Lorg/codehaus/groovy/control/messages/SimpleMessage;-><init>(Ljava/lang/String;Lorg/codehaus/groovy/control/ProcessingUnit;)V

    .line 286
    invoke-virtual {v1, v2}, Lorg/codehaus/groovy/control/ErrorCollector;->addErrorAndContinue(Lorg/codehaus/groovy/control/messages/Message;)V

    return v0
.end method

.method public visit([Lorg/codehaus/groovy/ast/ASTNode;Lorg/codehaus/groovy/control/SourceUnit;)V
    .locals 5

    const/4 v0, 0x0

    .line 103
    aget-object v1, p1, v0

    instance-of v1, v1, Lorg/codehaus/groovy/ast/AnnotationNode;

    if-eqz v1, :cond_5

    const/4 v1, 0x1

    aget-object v2, p1, v1

    instance-of v2, v2, Lorg/codehaus/groovy/ast/AnnotatedNode;

    if-eqz v2, :cond_5

    .line 106
    aget-object v0, p1, v0

    check-cast v0, Lorg/codehaus/groovy/ast/AnnotationNode;

    .line 107
    aget-object p1, p1, v1

    check-cast p1, Lorg/codehaus/groovy/ast/AnnotatedNode;

    .line 109
    invoke-static {p1}, Lgroovy/beans/VetoableASTTransformation;->hasVetoableAnnotation(Lorg/codehaus/groovy/ast/AnnotatedNode;)Z

    move-result v1

    if-eqz v1, :cond_0

    return-void

    .line 114
    :cond_0
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/AnnotatedNode;->getDeclaringClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    .line 115
    instance-of v2, p1, Lorg/codehaus/groovy/ast/FieldNode;

    if-eqz v2, :cond_3

    .line 116
    move-object v2, p1

    check-cast v2, Lorg/codehaus/groovy/ast/FieldNode;

    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/FieldNode;->getModifiers()I

    move-result v3

    and-int/lit8 v3, v3, 0x10

    if-eqz v3, :cond_1

    .line 117
    invoke-virtual {p2}, Lorg/codehaus/groovy/control/SourceUnit;->getErrorCollector()Lorg/codehaus/groovy/control/ErrorCollector;

    move-result-object v3

    const-string v4, "@groovy.beans.Bindable cannot annotate a final property."

    invoke-virtual {v3, v4, v0, p2}, Lorg/codehaus/groovy/control/ErrorCollector;->addErrorAndContinue(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;Lorg/codehaus/groovy/control/SourceUnit;)V

    .line 120
    :cond_1
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/AnnotatedNode;->getDeclaringClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    invoke-static {p1}, Lgroovy/beans/VetoableASTTransformation;->hasVetoableAnnotation(Lorg/codehaus/groovy/ast/AnnotatedNode;)Z

    move-result p1

    if-eqz p1, :cond_2

    return-void

    .line 124
    :cond_2
    invoke-direct {p0, p2, v0, v1, v2}, Lgroovy/beans/BindableASTTransformation;->addListenerToProperty(Lorg/codehaus/groovy/control/SourceUnit;Lorg/codehaus/groovy/ast/AnnotationNode;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/FieldNode;)V

    goto :goto_0

    .line 125
    :cond_3
    instance-of v0, p1, Lorg/codehaus/groovy/ast/ClassNode;

    if-eqz v0, :cond_4

    .line 126
    check-cast p1, Lorg/codehaus/groovy/ast/ClassNode;

    invoke-direct {p0, p2, p1}, Lgroovy/beans/BindableASTTransformation;->addListenerToClass(Lorg/codehaus/groovy/control/SourceUnit;Lorg/codehaus/groovy/ast/ClassNode;)V

    :cond_4
    :goto_0
    return-void

    .line 104
    :cond_5
    new-instance p1, Ljava/lang/RuntimeException;

    const-string p2, "Internal error: wrong types: $node.class / $parent.class"

    invoke-direct {p1, p2}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/String;)V

    throw p1
.end method
