.class public Lgroovy/beans/DefaultPropertyReader;
.super Ljava/lang/Object;
.source "DefaultPropertyReader.java"

# interfaces
.implements Lgroovy/beans/PropertyReader;


# static fields
.field public static final INSTANCE:Lgroovy/beans/PropertyReader;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 24
    new-instance v0, Lgroovy/beans/DefaultPropertyReader;

    invoke-direct {v0}, Lgroovy/beans/DefaultPropertyReader;-><init>()V

    sput-object v0, Lgroovy/beans/DefaultPropertyReader;->INSTANCE:Lgroovy/beans/PropertyReader;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 23
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public read(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;
    .locals 0

    .line 27
    invoke-static {p1, p2}, Lorg/codehaus/groovy/runtime/InvokerHelper;->getPropertySafe(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method
