.class public Lgroovy/beans/DefaultPropertyWriter;
.super Ljava/lang/Object;
.source "DefaultPropertyWriter.java"

# interfaces
.implements Lgroovy/beans/PropertyWriter;


# static fields
.field public static final INSTANCE:Lgroovy/beans/PropertyWriter;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 24
    new-instance v0, Lgroovy/beans/DefaultPropertyWriter;

    invoke-direct {v0}, Lgroovy/beans/DefaultPropertyWriter;-><init>()V

    sput-object v0, Lgroovy/beans/DefaultPropertyWriter;->INSTANCE:Lgroovy/beans/PropertyWriter;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 23
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public write(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;)V
    .locals 0

    .line 27
    invoke-static {p1, p2, p3}, Lorg/codehaus/groovy/runtime/InvokerHelper;->setProperty(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;)V

    return-void
.end method
