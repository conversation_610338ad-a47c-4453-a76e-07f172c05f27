.class public final synthetic Lorg/apache/groovy/parser/antlr4/AstBuilder$$ExternalSyntheticLambda72;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Predicate;


# static fields
.field public static final synthetic INSTANCE:Lorg/apache/groovy/parser/antlr4/AstBuilder$$ExternalSyntheticLambda72;


# direct methods
.method static synthetic constructor <clinit>()V
    .locals 1

    new-instance v0, Lorg/apache/groovy/parser/antlr4/AstBuilder$$ExternalSyntheticLambda72;

    invoke-direct {v0}, Lorg/apache/groovy/parser/antlr4/AstBuilder$$ExternalSyntheticLambda72;-><init>()V

    sput-object v0, Lorg/apache/groovy/parser/antlr4/AstBuilder$$ExternalSyntheticLambda72;->INSTANCE:Lorg/apache/groovy/parser/antlr4/AstBuilder$$ExternalSyntheticLambda72;

    return-void
.end method

.method private synthetic constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final test(Ljava/lang/Object;)Z
    .locals 0

    invoke-static {p1}, Lorg/apache/groovy/parser/antlr4/AstBuilder;->$r8$lambda$hWpmawqjQhjJotBrvOxYRVnG6dI(Ljava/lang/Object;)Z

    move-result p1

    return p1
.end method
