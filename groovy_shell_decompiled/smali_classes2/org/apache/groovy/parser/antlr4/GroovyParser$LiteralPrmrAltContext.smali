.class public Lorg/apache/groovy/parser/antlr4/GroovyParser$LiteralPrmrAltContext;
.super Lorg/apache/groovy/parser/antlr4/GroovyParser$PrimaryContext;
.source "GroovyParser.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/apache/groovy/parser/antlr4/GroovyParser;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "LiteralPrmrAltContext"
.end annotation


# direct methods
.method public constructor <init>(Lorg/apache/groovy/parser/antlr4/GroovyParser$PrimaryContext;)V
    .locals 0

    .line 10117
    invoke-direct {p0}, Lorg/apache/groovy/parser/antlr4/GroovyParser$PrimaryContext;-><init>()V

    invoke-virtual {p0, p1}, Lorg/apache/groovy/parser/antlr4/GroovyParser$LiteralPrmrAltContext;->copyFrom(Lorg/apache/groovy/parser/antlr4/GroovyParser$PrimaryContext;)V

    return-void
.end method


# virtual methods
.method public accept(Lgroovyjarjarantlr4/v4/runtime/tree/ParseTreeVisitor;)Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<Result:",
            "Ljava/lang/Object;",
            ">(",
            "Lgroovyjarjarantlr4/v4/runtime/tree/ParseTreeVisitor<",
            "+TResult;>;)TResult;"
        }
    .end annotation

    .line 10120
    instance-of v0, p1, Lorg/apache/groovy/parser/antlr4/GroovyParserVisitor;

    if-eqz v0, :cond_0

    check-cast p1, Lorg/apache/groovy/parser/antlr4/GroovyParserVisitor;

    invoke-interface {p1, p0}, Lorg/apache/groovy/parser/antlr4/GroovyParserVisitor;->visitLiteralPrmrAlt(Lorg/apache/groovy/parser/antlr4/GroovyParser$LiteralPrmrAltContext;)Ljava/lang/Object;

    move-result-object p1

    return-object p1

    .line 10121
    :cond_0
    invoke-interface {p1, p0}, Lgroovyjarjarantlr4/v4/runtime/tree/ParseTreeVisitor;->visitChildren(Lgroovyjarjarantlr4/v4/runtime/tree/RuleNode;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public literal()Lorg/apache/groovy/parser/antlr4/GroovyParser$LiteralContext;
    .locals 2

    .line 10115
    const-class v0, Lorg/apache/groovy/parser/antlr4/GroovyParser$LiteralContext;

    const/4 v1, 0x0

    invoke-virtual {p0, v0, v1}, Lorg/apache/groovy/parser/antlr4/GroovyParser$LiteralPrmrAltContext;->getRuleContext(Ljava/lang/Class;I)Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    move-result-object v0

    check-cast v0, Lorg/apache/groovy/parser/antlr4/GroovyParser$LiteralContext;

    return-object v0
.end method
