.class public final synthetic Lorg/codehaus/groovy/reflection/CachedClass$5$$ExternalSyntheticLambda0;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/security/PrivilegedAction;


# instance fields
.field public final synthetic f$0:Lorg/codehaus/groovy/reflection/CachedClass$5;


# direct methods
.method public synthetic constructor <init>(Lorg/codehaus/groovy/reflection/CachedClass$5;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/codehaus/groovy/reflection/CachedClass$5$$ExternalSyntheticLambda0;->f$0:Lorg/codehaus/groovy/reflection/CachedClass$5;

    return-void
.end method


# virtual methods
.method public final run()Ljava/lang/Object;
    .locals 1

    iget-object v0, p0, Lorg/codehaus/groovy/reflection/CachedClass$5$$ExternalSyntheticLambda0;->f$0:Lorg/codehaus/groovy/reflection/CachedClass$5;

    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/CachedClass$5;->lambda$initValue$0$org-codehaus-groovy-reflection-CachedClass$5()Lorg/codehaus/groovy/runtime/callsite/CallSiteClassLoader;

    move-result-object v0

    return-object v0
.end method
