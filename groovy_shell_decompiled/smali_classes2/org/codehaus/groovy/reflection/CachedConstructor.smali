.class public Lorg/codehaus/groovy/reflection/CachedConstructor;
.super Lorg/codehaus/groovy/reflection/ParameterTypes;
.source "CachedConstructor.java"


# instance fields
.field private final cachedConstructor:Ljava/lang/reflect/Constructor;

.field private final clazz:Lorg/codehaus/groovy/reflection/CachedClass;

.field private makeAccessibleDone:Z


# direct methods
.method public constructor <init>(Ljava/lang/reflect/Constructor;)V
    .locals 1

    .line 41
    invoke-virtual {p1}, Ljava/lang/reflect/Constructor;->getDeclaringClass()Ljava/lang/Class;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/reflection/ReflectionCache;->getCachedClass(Ljava/lang/Class;)Lorg/codehaus/groovy/reflection/CachedClass;

    move-result-object v0

    invoke-direct {p0, v0, p1}, Lorg/codehaus/groovy/reflection/CachedConstructor;-><init>(Lorg/codehaus/groovy/reflection/CachedClass;Ljava/lang/reflect/Constructor;)V

    return-void
.end method

.method public constructor <init>(Lorg/codehaus/groovy/reflection/CachedClass;Ljava/lang/reflect/Constructor;)V
    .locals 1

    .line 35
    invoke-direct {p0}, Lorg/codehaus/groovy/reflection/ParameterTypes;-><init>()V

    const/4 v0, 0x0

    .line 121
    iput-boolean v0, p0, Lorg/codehaus/groovy/reflection/CachedConstructor;->makeAccessibleDone:Z

    .line 36
    iput-object p2, p0, Lorg/codehaus/groovy/reflection/CachedConstructor;->cachedConstructor:Ljava/lang/reflect/Constructor;

    .line 37
    iput-object p1, p0, Lorg/codehaus/groovy/reflection/CachedConstructor;->clazz:Lorg/codehaus/groovy/reflection/CachedClass;

    return-void
.end method

.method private static createException(Ljava/lang/String;Ljava/lang/reflect/Constructor;[Ljava/lang/Object;Ljava/lang/Throwable;Z)Lgroovy/lang/GroovyRuntimeException;
    .locals 2

    .line 88
    new-instance v0, Lgroovy/lang/GroovyRuntimeException;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p0

    const-string p1, " with arguments: "

    invoke-virtual {p0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    .line 92
    invoke-static {p2}, Lorg/codehaus/groovy/runtime/InvokerHelper;->toString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    const-string p1, " reason: "

    invoke-virtual {p0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    if-eqz p4, :cond_0

    goto :goto_0

    :cond_0
    const/4 p3, 0x0

    .line 95
    :goto_0
    invoke-direct {v0, p0, p3}, Lgroovy/lang/GroovyRuntimeException;-><init>(Ljava/lang/String;Ljava/lang/Throwable;)V

    return-object v0
.end method

.method public static find(Ljava/lang/reflect/Constructor;)Lorg/codehaus/groovy/reflection/CachedConstructor;
    .locals 5

    .line 49
    invoke-virtual {p0}, Ljava/lang/reflect/Constructor;->getDeclaringClass()Ljava/lang/Class;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/reflection/ReflectionCache;->getCachedClass(Ljava/lang/Class;)Lorg/codehaus/groovy/reflection/CachedClass;

    move-result-object v0

    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/CachedClass;->getConstructors()[Lorg/codehaus/groovy/reflection/CachedConstructor;

    move-result-object v0

    .line 50
    array-length v1, v0

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v1, :cond_1

    aget-object v3, v0, v2

    .line 51
    iget-object v4, v3, Lorg/codehaus/groovy/reflection/CachedConstructor;->cachedConstructor:Ljava/lang/reflect/Constructor;

    invoke-virtual {v4, p0}, Ljava/lang/reflect/Constructor;->equals(Ljava/lang/Object;)Z

    move-result v4

    if-eqz v4, :cond_0

    return-object v3

    :cond_0
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 54
    :cond_1
    new-instance v0, Ljava/lang/RuntimeException;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "Couldn\'t find method: "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    invoke-direct {v0, p0}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method private isConstructorOfAbstractClass()Z
    .locals 1

    .line 130
    iget-object v0, p0, Lorg/codehaus/groovy/reflection/CachedConstructor;->cachedConstructor:Ljava/lang/reflect/Constructor;

    invoke-virtual {v0}, Ljava/lang/reflect/Constructor;->getDeclaringClass()Ljava/lang/Class;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/Class;->getModifiers()I

    move-result v0

    invoke-static {v0}, Ljava/lang/reflect/Modifier;->isAbstract(I)Z

    move-result v0

    return v0
.end method

.method private makeAccessibleIfNecessary()V
    .locals 1

    .line 123
    iget-boolean v0, p0, Lorg/codehaus/groovy/reflection/CachedConstructor;->makeAccessibleDone:Z

    if-nez v0, :cond_0

    .line 124
    iget-object v0, p0, Lorg/codehaus/groovy/reflection/CachedConstructor;->cachedConstructor:Ljava/lang/reflect/Constructor;

    invoke-static {v0}, Lorg/codehaus/groovy/reflection/ReflectionUtils;->makeAccessibleInPrivilegedAction(Ljava/lang/reflect/AccessibleObject;)Ljava/util/Optional;

    const/4 v0, 0x1

    .line 125
    iput-boolean v0, p0, Lorg/codehaus/groovy/reflection/CachedConstructor;->makeAccessibleDone:Z

    :cond_0
    return-void
.end method


# virtual methods
.method public doConstructorInvoke([Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 58
    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/reflection/CachedConstructor;->coerceArgumentsToClasses([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object p1

    .line 59
    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/reflection/CachedConstructor;->invoke([Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public getCachedClass()Lorg/codehaus/groovy/reflection/CachedClass;
    .locals 1

    .line 108
    iget-object v0, p0, Lorg/codehaus/groovy/reflection/CachedConstructor;->clazz:Lorg/codehaus/groovy/reflection/CachedClass;

    return-object v0
.end method

.method public getCachedConstructor()Ljava/lang/reflect/Constructor;
    .locals 1

    .line 116
    invoke-direct {p0}, Lorg/codehaus/groovy/reflection/CachedConstructor;->makeAccessibleIfNecessary()V

    .line 117
    iget-object v0, p0, Lorg/codehaus/groovy/reflection/CachedConstructor;->cachedConstructor:Ljava/lang/reflect/Constructor;

    invoke-static {v0}, Lorg/codehaus/groovy/reflection/AccessPermissionChecker;->checkAccessPermission(Ljava/lang/reflect/Constructor;)V

    .line 118
    iget-object v0, p0, Lorg/codehaus/groovy/reflection/CachedConstructor;->cachedConstructor:Ljava/lang/reflect/Constructor;

    return-object v0
.end method

.method public getDeclaringClass()Ljava/lang/Class;
    .locals 1

    .line 112
    iget-object v0, p0, Lorg/codehaus/groovy/reflection/CachedConstructor;->cachedConstructor:Ljava/lang/reflect/Constructor;

    invoke-virtual {v0}, Ljava/lang/reflect/Constructor;->getDeclaringClass()Ljava/lang/Class;

    move-result-object v0

    return-object v0
.end method

.method public getModifiers()I
    .locals 1

    .line 104
    iget-object v0, p0, Lorg/codehaus/groovy/reflection/CachedConstructor;->cachedConstructor:Ljava/lang/reflect/Constructor;

    invoke-virtual {v0}, Ljava/lang/reflect/Constructor;->getModifiers()I

    move-result v0

    return v0
.end method

.method protected getPT()[Ljava/lang/Class;
    .locals 1

    .line 45
    iget-object v0, p0, Lorg/codehaus/groovy/reflection/CachedConstructor;->cachedConstructor:Ljava/lang/reflect/Constructor;

    invoke-virtual {v0}, Ljava/lang/reflect/Constructor;->getParameterTypes()[Ljava/lang/Class;

    move-result-object v0

    return-object v0
.end method

.method public invoke([Ljava/lang/Object;)Ljava/lang/Object;
    .locals 5

    .line 63
    iget-object v0, p0, Lorg/codehaus/groovy/reflection/CachedConstructor;->cachedConstructor:Ljava/lang/reflect/Constructor;

    .line 65
    invoke-direct {p0}, Lorg/codehaus/groovy/reflection/CachedConstructor;->isConstructorOfAbstractClass()Z

    move-result v1

    const/4 v2, 0x1

    const-string v3, "failed to invoke constructor: "

    if-nez v1, :cond_2

    .line 69
    invoke-direct {p0}, Lorg/codehaus/groovy/reflection/CachedConstructor;->makeAccessibleIfNecessary()V

    const/4 v1, 0x0

    .line 72
    :try_start_0
    invoke-virtual {v0, p1}, Ljava/lang/reflect/Constructor;->newInstance([Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1
    :try_end_0
    .catch Ljava/lang/reflect/InvocationTargetException; {:try_start_0 .. :try_end_0} :catch_3
    .catch Ljava/lang/IllegalArgumentException; {:try_start_0 .. :try_end_0} :catch_2
    .catch Ljava/lang/IllegalAccessException; {:try_start_0 .. :try_end_0} :catch_1
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return-object p1

    :catch_0
    move-exception v1

    .line 80
    instance-of v4, v1, Ljava/lang/RuntimeException;

    if-eqz v4, :cond_0

    .line 81
    check-cast v1, Ljava/lang/RuntimeException;

    throw v1

    .line 83
    :cond_0
    invoke-static {v3, v0, p1, v1, v2}, Lorg/codehaus/groovy/reflection/CachedConstructor;->createException(Ljava/lang/String;Ljava/lang/reflect/Constructor;[Ljava/lang/Object;Ljava/lang/Throwable;Z)Lgroovy/lang/GroovyRuntimeException;

    move-result-object p1

    throw p1

    :catch_1
    move-exception v2

    const-string v3, "could not access constructor: "

    .line 78
    invoke-static {v3, v0, p1, v2, v1}, Lorg/codehaus/groovy/reflection/CachedConstructor;->createException(Ljava/lang/String;Ljava/lang/reflect/Constructor;[Ljava/lang/Object;Ljava/lang/Throwable;Z)Lgroovy/lang/GroovyRuntimeException;

    move-result-object p1

    throw p1

    :catch_2
    move-exception v2

    .line 76
    invoke-static {v3, v0, p1, v2, v1}, Lorg/codehaus/groovy/reflection/CachedConstructor;->createException(Ljava/lang/String;Ljava/lang/reflect/Constructor;[Ljava/lang/Object;Ljava/lang/Throwable;Z)Lgroovy/lang/GroovyRuntimeException;

    move-result-object p1

    throw p1

    :catch_3
    move-exception p1

    .line 74
    invoke-virtual {p1}, Ljava/lang/reflect/InvocationTargetException;->getCause()Ljava/lang/Throwable;

    move-result-object v0

    instance-of v0, v0, Ljava/lang/RuntimeException;

    if-eqz v0, :cond_1

    invoke-virtual {p1}, Ljava/lang/reflect/InvocationTargetException;->getCause()Ljava/lang/Throwable;

    move-result-object p1

    check-cast p1, Ljava/lang/RuntimeException;

    goto :goto_0

    :cond_1
    new-instance v0, Lorg/codehaus/groovy/runtime/InvokerInvocationException;

    invoke-direct {v0, p1}, Lorg/codehaus/groovy/runtime/InvokerInvocationException;-><init>(Ljava/lang/reflect/InvocationTargetException;)V

    move-object p1, v0

    :goto_0
    throw p1

    .line 66
    :cond_2
    new-instance v1, Ljava/lang/InstantiationException;

    invoke-direct {v1}, Ljava/lang/InstantiationException;-><init>()V

    invoke-static {v3, v0, p1, v1, v2}, Lorg/codehaus/groovy/reflection/CachedConstructor;->createException(Ljava/lang/String;Ljava/lang/reflect/Constructor;[Ljava/lang/Object;Ljava/lang/Throwable;Z)Lgroovy/lang/GroovyRuntimeException;

    move-result-object p1

    throw p1
.end method

.method public toString()Ljava/lang/String;
    .locals 1

    .line 100
    iget-object v0, p0, Lorg/codehaus/groovy/reflection/CachedConstructor;->cachedConstructor:Ljava/lang/reflect/Constructor;

    invoke-virtual {v0}, Ljava/lang/reflect/Constructor;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
