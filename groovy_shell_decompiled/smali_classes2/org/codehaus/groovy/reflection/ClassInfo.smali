.class public Lorg/codehaus/groovy/reflection/ClassInfo;
.super Ljava/lang/Object;
.source "ClassInfo.java"

# interfaces
.implements Lorg/codehaus/groovy/util/Finalizable;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/codehaus/groovy/reflection/ClassInfo$GlobalClassSet;,
        Lorg/codehaus/groovy/reflection/ClassInfo$LazyCachedClassRef;,
        Lorg/codehaus/groovy/reflection/ClassInfo$LazyClassLoaderRef;,
        Lorg/codehaus/groovy/reflection/ClassInfo$ClassInfoAction;
    }
.end annotation


# static fields
.field private static final globalClassSet:Lorg/codehaus/groovy/reflection/ClassInfo$GlobalClassSet;

.field private static final globalClassValue:Lorg/codehaus/groovy/reflection/GroovyClassValue;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lorg/codehaus/groovy/reflection/GroovyClassValue<",
            "Lorg/codehaus/groovy/reflection/ClassInfo;",
            ">;"
        }
    .end annotation
.end field

.field private static final modifiedExpandos:Lorg/codehaus/groovy/util/ManagedConcurrentLinkedQueue;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lorg/codehaus/groovy/util/ManagedConcurrentLinkedQueue<",
            "Lorg/codehaus/groovy/reflection/ClassInfo;",
            ">;"
        }
    .end annotation
.end field

.field private static final softBundle:Lorg/codehaus/groovy/util/ReferenceBundle;

.field private static final weakBundle:Lorg/codehaus/groovy/util/ReferenceBundle;


# instance fields
.field private final artifactClassLoader:Lorg/codehaus/groovy/reflection/ClassInfo$LazyClassLoaderRef;

.field private final cachedClassRef:Lorg/codehaus/groovy/reflection/ClassInfo$LazyCachedClassRef;

.field private final classRef:Ljava/lang/ref/WeakReference;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/lang/ref/WeakReference<",
            "Ljava/lang/Class<",
            "*>;>;"
        }
    .end annotation
.end field

.field dgmMetaMethods:[Lgroovy/lang/MetaMethod;

.field public final hash:I

.field private final lock:Lorg/codehaus/groovy/util/LockableObject;

.field newMetaMethods:[Lgroovy/lang/MetaMethod;

.field private perInstanceMetaClassMap:Lorg/codehaus/groovy/util/ManagedConcurrentMap;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lorg/codehaus/groovy/util/ManagedConcurrentMap<",
            "Ljava/lang/Object;",
            "Lgroovy/lang/MetaClass;",
            ">;"
        }
    .end annotation
.end field

.field private strongMetaClass:Lgroovy/lang/MetaClass;

.field private final version:Ljava/util/concurrent/atomic/AtomicInteger;

.field private weakMetaClass:Lorg/codehaus/groovy/util/ManagedReference;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lorg/codehaus/groovy/util/ManagedReference<",
            "Lgroovy/lang/MetaClass;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 87
    invoke-static {}, Lorg/codehaus/groovy/util/ReferenceBundle;->getSoftBundle()Lorg/codehaus/groovy/util/ReferenceBundle;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/reflection/ClassInfo;->softBundle:Lorg/codehaus/groovy/util/ReferenceBundle;

    .line 88
    invoke-static {}, Lorg/codehaus/groovy/util/ReferenceBundle;->getWeakBundle()Lorg/codehaus/groovy/util/ReferenceBundle;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/reflection/ClassInfo;->weakBundle:Lorg/codehaus/groovy/util/ReferenceBundle;

    .line 90
    new-instance v1, Lorg/codehaus/groovy/util/ManagedConcurrentLinkedQueue;

    invoke-direct {v1, v0}, Lorg/codehaus/groovy/util/ManagedConcurrentLinkedQueue;-><init>(Lorg/codehaus/groovy/util/ReferenceBundle;)V

    sput-object v1, Lorg/codehaus/groovy/reflection/ClassInfo;->modifiedExpandos:Lorg/codehaus/groovy/util/ManagedConcurrentLinkedQueue;

    .line 93
    new-instance v0, Lorg/codehaus/groovy/reflection/ClassInfo$1;

    invoke-direct {v0}, Lorg/codehaus/groovy/reflection/ClassInfo$1;-><init>()V

    invoke-static {v0}, Lorg/codehaus/groovy/reflection/GroovyClassValueFactory;->createGroovyClassValue(Lorg/codehaus/groovy/reflection/GroovyClassValue$ComputeValue;)Lorg/codehaus/groovy/reflection/GroovyClassValue;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/reflection/ClassInfo;->globalClassValue:Lorg/codehaus/groovy/reflection/GroovyClassValue;

    .line 102
    new-instance v0, Lorg/codehaus/groovy/reflection/ClassInfo$GlobalClassSet;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/codehaus/groovy/reflection/ClassInfo$GlobalClassSet;-><init>(Lorg/codehaus/groovy/reflection/ClassInfo$1;)V

    sput-object v0, Lorg/codehaus/groovy/reflection/ClassInfo;->globalClassSet:Lorg/codehaus/groovy/reflection/ClassInfo$GlobalClassSet;

    return-void
.end method

.method constructor <init>(Ljava/lang/Class;)V
    .locals 1

    .line 104
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 77
    new-instance v0, Lorg/codehaus/groovy/util/LockableObject;

    invoke-direct {v0}, Lorg/codehaus/groovy/util/LockableObject;-><init>()V

    iput-object v0, p0, Lorg/codehaus/groovy/reflection/ClassInfo;->lock:Lorg/codehaus/groovy/util/LockableObject;

    const/4 v0, -0x1

    .line 78
    iput v0, p0, Lorg/codehaus/groovy/reflection/ClassInfo;->hash:I

    .line 80
    new-instance v0, Ljava/util/concurrent/atomic/AtomicInteger;

    invoke-direct {v0}, Ljava/util/concurrent/atomic/AtomicInteger;-><init>()V

    iput-object v0, p0, Lorg/codehaus/groovy/reflection/ClassInfo;->version:Ljava/util/concurrent/atomic/AtomicInteger;

    .line 83
    sget-object v0, Lgroovy/lang/MetaMethod;->EMPTY_ARRAY:[Lgroovy/lang/MetaMethod;

    iput-object v0, p0, Lorg/codehaus/groovy/reflection/ClassInfo;->dgmMetaMethods:[Lgroovy/lang/MetaMethod;

    .line 84
    sget-object v0, Lgroovy/lang/MetaMethod;->EMPTY_ARRAY:[Lgroovy/lang/MetaMethod;

    iput-object v0, p0, Lorg/codehaus/groovy/reflection/ClassInfo;->newMetaMethods:[Lgroovy/lang/MetaMethod;

    .line 105
    new-instance v0, Ljava/lang/ref/WeakReference;

    invoke-direct {v0, p1}, Ljava/lang/ref/WeakReference;-><init>(Ljava/lang/Object;)V

    iput-object v0, p0, Lorg/codehaus/groovy/reflection/ClassInfo;->classRef:Ljava/lang/ref/WeakReference;

    .line 106
    new-instance p1, Lorg/codehaus/groovy/reflection/ClassInfo$LazyCachedClassRef;

    sget-object v0, Lorg/codehaus/groovy/reflection/ClassInfo;->softBundle:Lorg/codehaus/groovy/util/ReferenceBundle;

    invoke-direct {p1, v0, p0}, Lorg/codehaus/groovy/reflection/ClassInfo$LazyCachedClassRef;-><init>(Lorg/codehaus/groovy/util/ReferenceBundle;Lorg/codehaus/groovy/reflection/ClassInfo;)V

    iput-object p1, p0, Lorg/codehaus/groovy/reflection/ClassInfo;->cachedClassRef:Lorg/codehaus/groovy/reflection/ClassInfo$LazyCachedClassRef;

    .line 107
    new-instance p1, Lorg/codehaus/groovy/reflection/ClassInfo$LazyClassLoaderRef;

    invoke-direct {p1, v0, p0}, Lorg/codehaus/groovy/reflection/ClassInfo$LazyClassLoaderRef;-><init>(Lorg/codehaus/groovy/util/ReferenceBundle;Lorg/codehaus/groovy/reflection/ClassInfo;)V

    iput-object p1, p0, Lorg/codehaus/groovy/reflection/ClassInfo;->artifactClassLoader:Lorg/codehaus/groovy/reflection/ClassInfo$LazyClassLoaderRef;

    return-void
.end method

.method static synthetic access$000()Lorg/codehaus/groovy/reflection/ClassInfo$GlobalClassSet;
    .locals 1

    .line 73
    sget-object v0, Lorg/codehaus/groovy/reflection/ClassInfo;->globalClassSet:Lorg/codehaus/groovy/reflection/ClassInfo$GlobalClassSet;

    return-object v0
.end method

.method static synthetic access$200(Lorg/codehaus/groovy/reflection/ClassInfo;)Ljava/lang/ref/WeakReference;
    .locals 0

    .line 73
    iget-object p0, p0, Lorg/codehaus/groovy/reflection/ClassInfo;->classRef:Ljava/lang/ref/WeakReference;

    return-object p0
.end method

.method static synthetic access$300(Ljava/lang/Class;Lorg/codehaus/groovy/reflection/ClassInfo;)Lorg/codehaus/groovy/reflection/CachedClass;
    .locals 0

    .line 73
    invoke-static {p0, p1}, Lorg/codehaus/groovy/reflection/ClassInfo;->createCachedClass(Ljava/lang/Class;Lorg/codehaus/groovy/reflection/ClassInfo;)Lorg/codehaus/groovy/reflection/CachedClass;

    move-result-object p0

    return-object p0
.end method

.method static synthetic access$400()Lorg/codehaus/groovy/util/ReferenceBundle;
    .locals 1

    .line 73
    sget-object v0, Lorg/codehaus/groovy/reflection/ClassInfo;->weakBundle:Lorg/codehaus/groovy/util/ReferenceBundle;

    return-object v0
.end method

.method public static clearModifiedExpandos()V
    .locals 3

    .line 127
    sget-object v0, Lorg/codehaus/groovy/reflection/ClassInfo;->modifiedExpandos:Lorg/codehaus/groovy/util/ManagedConcurrentLinkedQueue;

    invoke-virtual {v0}, Lorg/codehaus/groovy/util/ManagedConcurrentLinkedQueue;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    .line 128
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/reflection/ClassInfo;

    .line 129
    invoke-interface {v0}, Ljava/util/Iterator;->remove()V

    const/4 v2, 0x0

    .line 130
    invoke-virtual {v1, v2}, Lorg/codehaus/groovy/reflection/ClassInfo;->setStrongMetaClass(Lgroovy/lang/MetaClass;)V

    goto :goto_0

    :cond_0
    return-void
.end method

.method private static createCachedClass(Ljava/lang/Class;Lorg/codehaus/groovy/reflection/ClassInfo;)Lorg/codehaus/groovy/reflection/CachedClass;
    .locals 4

    .line 337
    const-class v0, Ljava/lang/Object;

    if-ne p0, v0, :cond_0

    .line 338
    new-instance p0, Lorg/codehaus/groovy/reflection/stdclasses/ObjectCachedClass;

    invoke-direct {p0, p1}, Lorg/codehaus/groovy/reflection/stdclasses/ObjectCachedClass;-><init>(Lorg/codehaus/groovy/reflection/ClassInfo;)V

    return-object p0

    .line 340
    :cond_0
    const-class v0, Ljava/lang/String;

    if-ne p0, v0, :cond_1

    .line 341
    new-instance p0, Lorg/codehaus/groovy/reflection/stdclasses/StringCachedClass;

    invoke-direct {p0, p1}, Lorg/codehaus/groovy/reflection/stdclasses/StringCachedClass;-><init>(Lorg/codehaus/groovy/reflection/ClassInfo;)V

    return-object p0

    .line 344
    :cond_1
    const-class v0, Ljava/lang/Number;

    invoke-virtual {v0, p0}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    move-result v0

    const/4 v1, 0x1

    if-nez v0, :cond_8

    invoke-virtual {p0}, Ljava/lang/Class;->isPrimitive()Z

    move-result v0

    if-eqz v0, :cond_2

    goto :goto_0

    .line 371
    :cond_2
    invoke-virtual {p0}, Ljava/lang/Class;->isArray()Z

    move-result v0

    if-eqz v0, :cond_3

    .line 372
    new-instance v0, Lorg/codehaus/groovy/reflection/stdclasses/ArrayCachedClass;

    invoke-direct {v0, p0, p1}, Lorg/codehaus/groovy/reflection/stdclasses/ArrayCachedClass;-><init>(Ljava/lang/Class;Lorg/codehaus/groovy/reflection/ClassInfo;)V

    goto/16 :goto_d

    .line 373
    :cond_3
    const-class v0, Ljava/lang/Boolean;

    if-ne p0, v0, :cond_4

    .line 374
    new-instance v0, Lorg/codehaus/groovy/reflection/stdclasses/BooleanCachedClass;

    invoke-direct {v0, p0, p1, v1}, Lorg/codehaus/groovy/reflection/stdclasses/BooleanCachedClass;-><init>(Ljava/lang/Class;Lorg/codehaus/groovy/reflection/ClassInfo;Z)V

    goto/16 :goto_d

    .line 375
    :cond_4
    const-class v0, Ljava/lang/Character;

    if-ne p0, v0, :cond_5

    .line 376
    new-instance v0, Lorg/codehaus/groovy/reflection/stdclasses/CharacterCachedClass;

    invoke-direct {v0, p0, p1, v1}, Lorg/codehaus/groovy/reflection/stdclasses/CharacterCachedClass;-><init>(Ljava/lang/Class;Lorg/codehaus/groovy/reflection/ClassInfo;Z)V

    goto/16 :goto_d

    .line 377
    :cond_5
    const-class v0, Lgroovy/lang/Closure;

    invoke-virtual {v0, p0}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    move-result v0

    if-eqz v0, :cond_6

    .line 378
    new-instance v0, Lorg/codehaus/groovy/reflection/stdclasses/CachedClosureClass;

    invoke-direct {v0, p0, p1}, Lorg/codehaus/groovy/reflection/stdclasses/CachedClosureClass;-><init>(Ljava/lang/Class;Lorg/codehaus/groovy/reflection/ClassInfo;)V

    goto/16 :goto_d

    .line 379
    :cond_6
    invoke-static {p0}, Lorg/codehaus/groovy/reflection/ClassInfo;->isSAM(Ljava/lang/Class;)Z

    move-result v0

    if-eqz v0, :cond_7

    .line 380
    new-instance v0, Lorg/codehaus/groovy/reflection/stdclasses/CachedSAMClass;

    invoke-direct {v0, p0, p1}, Lorg/codehaus/groovy/reflection/stdclasses/CachedSAMClass;-><init>(Ljava/lang/Class;Lorg/codehaus/groovy/reflection/ClassInfo;)V

    goto/16 :goto_d

    .line 382
    :cond_7
    new-instance v0, Lorg/codehaus/groovy/reflection/CachedClass;

    invoke-direct {v0, p0, p1}, Lorg/codehaus/groovy/reflection/CachedClass;-><init>(Ljava/lang/Class;Lorg/codehaus/groovy/reflection/ClassInfo;)V

    goto/16 :goto_d

    .line 345
    :cond_8
    :goto_0
    const-class v0, Ljava/lang/Number;

    if-ne p0, v0, :cond_9

    .line 346
    new-instance v0, Lorg/codehaus/groovy/reflection/stdclasses/NumberCachedClass;

    invoke-direct {v0, p0, p1}, Lorg/codehaus/groovy/reflection/stdclasses/NumberCachedClass;-><init>(Ljava/lang/Class;Lorg/codehaus/groovy/reflection/ClassInfo;)V

    goto/16 :goto_d

    .line 347
    :cond_9
    const-class v0, Ljava/lang/Integer;

    const/4 v2, 0x0

    if-eq p0, v0, :cond_1e

    sget-object v0, Ljava/lang/Integer;->TYPE:Ljava/lang/Class;

    if-ne p0, v0, :cond_a

    goto/16 :goto_b

    .line 349
    :cond_a
    const-class v0, Ljava/lang/Double;

    if-eq p0, v0, :cond_1c

    sget-object v0, Ljava/lang/Double;->TYPE:Ljava/lang/Class;

    if-ne p0, v0, :cond_b

    goto/16 :goto_9

    .line 351
    :cond_b
    const-class v0, Ljava/math/BigDecimal;

    if-ne p0, v0, :cond_c

    .line 352
    new-instance v0, Lorg/codehaus/groovy/reflection/stdclasses/BigDecimalCachedClass;

    invoke-direct {v0, p0, p1}, Lorg/codehaus/groovy/reflection/stdclasses/BigDecimalCachedClass;-><init>(Ljava/lang/Class;Lorg/codehaus/groovy/reflection/ClassInfo;)V

    goto/16 :goto_d

    .line 353
    :cond_c
    const-class v0, Ljava/lang/Long;

    if-eq p0, v0, :cond_1a

    sget-object v0, Ljava/lang/Long;->TYPE:Ljava/lang/Class;

    if-ne p0, v0, :cond_d

    goto/16 :goto_7

    .line 355
    :cond_d
    const-class v0, Ljava/lang/Float;

    if-eq p0, v0, :cond_18

    sget-object v0, Ljava/lang/Float;->TYPE:Ljava/lang/Class;

    if-ne p0, v0, :cond_e

    goto :goto_5

    .line 357
    :cond_e
    const-class v0, Ljava/lang/Short;

    if-eq p0, v0, :cond_16

    sget-object v0, Ljava/lang/Short;->TYPE:Ljava/lang/Class;

    if-ne p0, v0, :cond_f

    goto :goto_3

    .line 359
    :cond_f
    sget-object v0, Ljava/lang/Boolean;->TYPE:Ljava/lang/Class;

    if-ne p0, v0, :cond_10

    .line 360
    new-instance v0, Lorg/codehaus/groovy/reflection/stdclasses/BooleanCachedClass;

    invoke-direct {v0, p0, p1, v2}, Lorg/codehaus/groovy/reflection/stdclasses/BooleanCachedClass;-><init>(Ljava/lang/Class;Lorg/codehaus/groovy/reflection/ClassInfo;Z)V

    goto/16 :goto_d

    .line 361
    :cond_10
    sget-object v0, Ljava/lang/Character;->TYPE:Ljava/lang/Class;

    if-ne p0, v0, :cond_11

    .line 362
    new-instance v0, Lorg/codehaus/groovy/reflection/stdclasses/CharacterCachedClass;

    invoke-direct {v0, p0, p1, v2}, Lorg/codehaus/groovy/reflection/stdclasses/CharacterCachedClass;-><init>(Ljava/lang/Class;Lorg/codehaus/groovy/reflection/ClassInfo;Z)V

    goto/16 :goto_d

    .line 363
    :cond_11
    const-class v0, Ljava/math/BigInteger;

    if-ne p0, v0, :cond_12

    .line 364
    new-instance v0, Lorg/codehaus/groovy/reflection/stdclasses/BigIntegerCachedClass;

    invoke-direct {v0, p0, p1}, Lorg/codehaus/groovy/reflection/stdclasses/BigIntegerCachedClass;-><init>(Ljava/lang/Class;Lorg/codehaus/groovy/reflection/ClassInfo;)V

    goto/16 :goto_d

    .line 365
    :cond_12
    const-class v0, Ljava/lang/Byte;

    if-eq p0, v0, :cond_14

    sget-object v0, Ljava/lang/Byte;->TYPE:Ljava/lang/Class;

    if-ne p0, v0, :cond_13

    goto :goto_1

    .line 368
    :cond_13
    new-instance v0, Lorg/codehaus/groovy/reflection/CachedClass;

    invoke-direct {v0, p0, p1}, Lorg/codehaus/groovy/reflection/CachedClass;-><init>(Ljava/lang/Class;Lorg/codehaus/groovy/reflection/ClassInfo;)V

    goto :goto_d

    .line 366
    :cond_14
    :goto_1
    new-instance v0, Lorg/codehaus/groovy/reflection/stdclasses/ByteCachedClass;

    const-class v3, Ljava/lang/Byte;

    if-ne p0, v3, :cond_15

    goto :goto_2

    :cond_15
    move v1, v2

    :goto_2
    invoke-direct {v0, p0, p1, v1}, Lorg/codehaus/groovy/reflection/stdclasses/ByteCachedClass;-><init>(Ljava/lang/Class;Lorg/codehaus/groovy/reflection/ClassInfo;Z)V

    goto :goto_d

    .line 358
    :cond_16
    :goto_3
    new-instance v0, Lorg/codehaus/groovy/reflection/stdclasses/ShortCachedClass;

    const-class v3, Ljava/lang/Short;

    if-ne p0, v3, :cond_17

    goto :goto_4

    :cond_17
    move v1, v2

    :goto_4
    invoke-direct {v0, p0, p1, v1}, Lorg/codehaus/groovy/reflection/stdclasses/ShortCachedClass;-><init>(Ljava/lang/Class;Lorg/codehaus/groovy/reflection/ClassInfo;Z)V

    goto :goto_d

    .line 356
    :cond_18
    :goto_5
    new-instance v0, Lorg/codehaus/groovy/reflection/stdclasses/FloatCachedClass;

    const-class v3, Ljava/lang/Float;

    if-ne p0, v3, :cond_19

    goto :goto_6

    :cond_19
    move v1, v2

    :goto_6
    invoke-direct {v0, p0, p1, v1}, Lorg/codehaus/groovy/reflection/stdclasses/FloatCachedClass;-><init>(Ljava/lang/Class;Lorg/codehaus/groovy/reflection/ClassInfo;Z)V

    goto :goto_d

    .line 354
    :cond_1a
    :goto_7
    new-instance v0, Lorg/codehaus/groovy/reflection/stdclasses/LongCachedClass;

    const-class v3, Ljava/lang/Long;

    if-ne p0, v3, :cond_1b

    goto :goto_8

    :cond_1b
    move v1, v2

    :goto_8
    invoke-direct {v0, p0, p1, v1}, Lorg/codehaus/groovy/reflection/stdclasses/LongCachedClass;-><init>(Ljava/lang/Class;Lorg/codehaus/groovy/reflection/ClassInfo;Z)V

    goto :goto_d

    .line 350
    :cond_1c
    :goto_9
    new-instance v0, Lorg/codehaus/groovy/reflection/stdclasses/DoubleCachedClass;

    const-class v3, Ljava/lang/Double;

    if-ne p0, v3, :cond_1d

    goto :goto_a

    :cond_1d
    move v1, v2

    :goto_a
    invoke-direct {v0, p0, p1, v1}, Lorg/codehaus/groovy/reflection/stdclasses/DoubleCachedClass;-><init>(Ljava/lang/Class;Lorg/codehaus/groovy/reflection/ClassInfo;Z)V

    goto :goto_d

    .line 348
    :cond_1e
    :goto_b
    new-instance v0, Lorg/codehaus/groovy/reflection/stdclasses/IntegerCachedClass;

    const-class v3, Ljava/lang/Integer;

    if-ne p0, v3, :cond_1f

    goto :goto_c

    :cond_1f
    move v1, v2

    :goto_c
    invoke-direct {v0, p0, p1, v1}, Lorg/codehaus/groovy/reflection/stdclasses/IntegerCachedClass;-><init>(Ljava/lang/Class;Lorg/codehaus/groovy/reflection/ClassInfo;Z)V

    :goto_d
    return-object v0
.end method

.method public static fullSize()I
    .locals 1

    .line 333
    sget-object v0, Lorg/codehaus/groovy/reflection/ClassInfo;->globalClassSet:Lorg/codehaus/groovy/reflection/ClassInfo$GlobalClassSet;

    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/ClassInfo$GlobalClassSet;->fullSize()I

    move-result v0

    return v0
.end method

.method public static getAllClassInfo()Ljava/util/Collection;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Collection<",
            "Lorg/codehaus/groovy/reflection/ClassInfo;",
            ">;"
        }
    .end annotation

    .line 176
    invoke-static {}, Lorg/codehaus/groovy/reflection/ClassInfo;->getAllGlobalClassInfo()Ljava/util/Collection;

    move-result-object v0

    return-object v0
.end method

.method private static getAllGlobalClassInfo()Ljava/util/Collection;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Collection<",
            "Lorg/codehaus/groovy/reflection/ClassInfo;",
            ">;"
        }
    .end annotation

    .line 186
    sget-object v0, Lorg/codehaus/groovy/reflection/ClassInfo;->globalClassSet:Lorg/codehaus/groovy/reflection/ClassInfo$GlobalClassSet;

    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/ClassInfo$GlobalClassSet;->values()Ljava/util/Collection;

    move-result-object v0

    return-object v0
.end method

.method public static getClassInfo(Ljava/lang/Class;)Lorg/codehaus/groovy/reflection/ClassInfo;
    .locals 1

    .line 156
    sget-object v0, Lorg/codehaus/groovy/reflection/ClassInfo;->globalClassValue:Lorg/codehaus/groovy/reflection/GroovyClassValue;

    invoke-interface {v0, p0}, Lorg/codehaus/groovy/reflection/GroovyClassValue;->get(Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Lorg/codehaus/groovy/reflection/ClassInfo;

    return-object p0
.end method

.method private getMetaClassUnderLock()Lgroovy/lang/MetaClass;
    .locals 4

    .line 261
    invoke-virtual {p0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getStrongMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    if-eqz v0, :cond_0

    return-object v0

    .line 264
    :cond_0
    invoke-virtual {p0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getWeakMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    .line 265
    invoke-static {}, Lgroovy/lang/GroovySystem;->getMetaClassRegistry()Lgroovy/lang/MetaClassRegistry;

    move-result-object v1

    .line 266
    invoke-interface {v1}, Lgroovy/lang/MetaClassRegistry;->getMetaClassCreationHandler()Lgroovy/lang/MetaClassRegistry$MetaClassCreationHandle;

    move-result-object v2

    .line 268
    invoke-static {v0, v2}, Lorg/codehaus/groovy/reflection/ClassInfo;->isValidWeakMetaClass(Lgroovy/lang/MetaClass;Lgroovy/lang/MetaClassRegistry$MetaClassCreationHandle;)Z

    move-result v3

    if-eqz v3, :cond_1

    return-object v0

    .line 272
    :cond_1
    iget-object v0, p0, Lorg/codehaus/groovy/reflection/ClassInfo;->classRef:Ljava/lang/ref/WeakReference;

    invoke-virtual {v0}, Ljava/lang/ref/WeakReference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/Class;

    invoke-virtual {v2, v0, v1}, Lgroovy/lang/MetaClassRegistry$MetaClassCreationHandle;->create(Ljava/lang/Class;Lgroovy/lang/MetaClassRegistry;)Lgroovy/lang/MetaClass;

    move-result-object v0

    .line 273
    invoke-interface {v0}, Lgroovy/lang/MetaClass;->initialize()V

    .line 275
    invoke-static {}, Lgroovy/lang/GroovySystem;->isKeepJavaMetaClasses()Z

    move-result v1

    if-eqz v1, :cond_2

    .line 276
    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->setStrongMetaClass(Lgroovy/lang/MetaClass;)V

    goto :goto_0

    .line 278
    :cond_2
    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->setWeakMetaClass(Lgroovy/lang/MetaClass;)V

    :goto_0
    return-object v0
.end method

.method private static isSAM(Ljava/lang/Class;)Z
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Class<",
            "*>;)Z"
        }
    .end annotation

    .line 389
    invoke-static {p0}, Lorg/codehaus/groovy/reflection/stdclasses/CachedSAMClass;->getSAMMethod(Ljava/lang/Class;)Ljava/lang/reflect/Method;

    move-result-object p0

    if-eqz p0, :cond_0

    const/4 p0, 0x1

    goto :goto_0

    :cond_0
    const/4 p0, 0x0

    :goto_0
    return p0
.end method

.method private static isValidWeakMetaClass(Lgroovy/lang/MetaClass;)Z
    .locals 1

    .line 284
    invoke-static {}, Lgroovy/lang/GroovySystem;->getMetaClassRegistry()Lgroovy/lang/MetaClassRegistry;

    move-result-object v0

    invoke-interface {v0}, Lgroovy/lang/MetaClassRegistry;->getMetaClassCreationHandler()Lgroovy/lang/MetaClassRegistry$MetaClassCreationHandle;

    move-result-object v0

    invoke-static {p0, v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->isValidWeakMetaClass(Lgroovy/lang/MetaClass;Lgroovy/lang/MetaClassRegistry$MetaClassCreationHandle;)Z

    move-result p0

    return p0
.end method

.method private static isValidWeakMetaClass(Lgroovy/lang/MetaClass;Lgroovy/lang/MetaClassRegistry$MetaClassCreationHandle;)Z
    .locals 1

    const/4 v0, 0x0

    if-nez p0, :cond_0

    return v0

    .line 293
    :cond_0
    instance-of p1, p1, Lgroovy/lang/ExpandoMetaClassCreationHandle;

    .line 294
    instance-of p0, p0, Lgroovy/lang/ExpandoMetaClass;

    if-eqz p1, :cond_1

    if-eqz p0, :cond_2

    :cond_1
    const/4 v0, 0x1

    :cond_2
    return v0
.end method

.method public static onAllClassInfo(Lorg/codehaus/groovy/reflection/ClassInfo$ClassInfoAction;)V
    .locals 2

    .line 180
    invoke-static {}, Lorg/codehaus/groovy/reflection/ClassInfo;->getAllGlobalClassInfo()Ljava/util/Collection;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/reflection/ClassInfo;

    .line 181
    invoke-interface {p0, v1}, Lorg/codehaus/groovy/reflection/ClassInfo$ClassInfoAction;->onClassInfo(Lorg/codehaus/groovy/reflection/ClassInfo;)V

    goto :goto_0

    :cond_0
    return-void
.end method

.method public static remove(Ljava/lang/Class;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Class<",
            "*>;)V"
        }
    .end annotation

    .line 172
    sget-object v0, Lorg/codehaus/groovy/reflection/ClassInfo;->globalClassValue:Lorg/codehaus/groovy/reflection/GroovyClassValue;

    invoke-interface {v0, p0}, Lorg/codehaus/groovy/reflection/GroovyClassValue;->remove(Ljava/lang/Class;)V

    return-void
.end method

.method private replaceWeakMetaClassRef(Lorg/codehaus/groovy/util/ManagedReference;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/util/ManagedReference<",
            "Lgroovy/lang/MetaClass;",
            ">;)V"
        }
    .end annotation

    .line 241
    iget-object v0, p0, Lorg/codehaus/groovy/reflection/ClassInfo;->weakMetaClass:Lorg/codehaus/groovy/util/ManagedReference;

    if-eqz v0, :cond_0

    .line 243
    invoke-virtual {v0}, Lorg/codehaus/groovy/util/ManagedReference;->clear()V

    .line 245
    :cond_0
    iput-object p1, p0, Lorg/codehaus/groovy/reflection/ClassInfo;->weakMetaClass:Lorg/codehaus/groovy/util/ManagedReference;

    return-void
.end method

.method public static size()I
    .locals 1

    .line 329
    sget-object v0, Lorg/codehaus/groovy/reflection/ClassInfo;->globalClassSet:Lorg/codehaus/groovy/reflection/ClassInfo$GlobalClassSet;

    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/ClassInfo$GlobalClassSet;->size()I

    move-result v0

    return v0
.end method


# virtual methods
.method public finalizeReference()V
    .locals 1

    const/4 v0, 0x0

    .line 457
    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->setStrongMetaClass(Lgroovy/lang/MetaClass;)V

    .line 458
    iget-object v0, p0, Lorg/codehaus/groovy/reflection/ClassInfo;->cachedClassRef:Lorg/codehaus/groovy/reflection/ClassInfo$LazyCachedClassRef;

    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/ClassInfo$LazyCachedClassRef;->clear()V

    .line 459
    iget-object v0, p0, Lorg/codehaus/groovy/reflection/ClassInfo;->artifactClassLoader:Lorg/codehaus/groovy/reflection/ClassInfo$LazyClassLoaderRef;

    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/ClassInfo$LazyClassLoaderRef;->clear()V

    return-void
.end method

.method public getArtifactClassLoader()Lorg/codehaus/groovy/reflection/ClassLoaderForClassArtifacts;
    .locals 1

    .line 152
    iget-object v0, p0, Lorg/codehaus/groovy/reflection/ClassInfo;->artifactClassLoader:Lorg/codehaus/groovy/reflection/ClassInfo$LazyClassLoaderRef;

    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/ClassInfo$LazyClassLoaderRef;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/reflection/ClassLoaderForClassArtifacts;

    return-object v0
.end method

.method public getCachedClass()Lorg/codehaus/groovy/reflection/CachedClass;
    .locals 1

    .line 148
    iget-object v0, p0, Lorg/codehaus/groovy/reflection/ClassInfo;->cachedClassRef:Lorg/codehaus/groovy/reflection/ClassInfo$LazyCachedClassRef;

    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/ClassInfo$LazyCachedClassRef;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/reflection/CachedClass;

    return-object v0
.end method

.method public final getMetaClass()Lgroovy/lang/MetaClass;
    .locals 1

    .line 310
    invoke-virtual {p0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getMetaClassForClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    if-eqz v0, :cond_0

    return-object v0

    .line 313
    :cond_0
    invoke-virtual {p0}, Lorg/codehaus/groovy/reflection/ClassInfo;->lock()V

    .line 315
    :try_start_0
    invoke-direct {p0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getMetaClassUnderLock()Lgroovy/lang/MetaClass;

    move-result-object v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 317
    invoke-virtual {p0}, Lorg/codehaus/groovy/reflection/ClassInfo;->unlock()V

    return-object v0

    :catchall_0
    move-exception v0

    invoke-virtual {p0}, Lorg/codehaus/groovy/reflection/ClassInfo;->unlock()V

    .line 318
    throw v0
.end method

.method public getMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;
    .locals 0

    .line 322
    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/reflection/ClassInfo;->getPerInstanceMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object p1

    if-eqz p1, :cond_0

    return-object p1

    .line 325
    :cond_0
    invoke-virtual {p0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getMetaClass()Lgroovy/lang/MetaClass;

    move-result-object p1

    return-object p1
.end method

.method public getMetaClassForClass()Lgroovy/lang/MetaClass;
    .locals 2

    .line 251
    iget-object v0, p0, Lorg/codehaus/groovy/reflection/ClassInfo;->strongMetaClass:Lgroovy/lang/MetaClass;

    if-eqz v0, :cond_0

    return-object v0

    .line 253
    :cond_0
    invoke-virtual {p0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getWeakMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    .line 254
    invoke-static {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->isValidWeakMetaClass(Lgroovy/lang/MetaClass;)Z

    move-result v1

    if-eqz v1, :cond_1

    return-object v0

    :cond_1
    const/4 v0, 0x0

    return-object v0
.end method

.method public getModifiedExpando()Lgroovy/lang/ExpandoMetaClass;
    .locals 3

    .line 122
    iget-object v0, p0, Lorg/codehaus/groovy/reflection/ClassInfo;->strongMetaClass:Lgroovy/lang/MetaClass;

    const/4 v1, 0x0

    if-nez v0, :cond_0

    goto :goto_0

    .line 123
    :cond_0
    instance-of v2, v0, Lgroovy/lang/ExpandoMetaClass;

    if-eqz v2, :cond_1

    move-object v1, v0

    check-cast v1, Lgroovy/lang/ExpandoMetaClass;

    :cond_1
    :goto_0
    return-object v1
.end method

.method public getPerInstanceMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;
    .locals 1

    .line 401
    iget-object v0, p0, Lorg/codehaus/groovy/reflection/ClassInfo;->perInstanceMetaClassMap:Lorg/codehaus/groovy/util/ManagedConcurrentMap;

    if-nez v0, :cond_0

    const/4 p1, 0x0

    return-object p1

    .line 404
    :cond_0
    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/util/ManagedConcurrentMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovy/lang/MetaClass;

    return-object p1
.end method

.method public getStrongMetaClass()Lgroovy/lang/MetaClass;
    .locals 1

    .line 190
    iget-object v0, p0, Lorg/codehaus/groovy/reflection/ClassInfo;->strongMetaClass:Lgroovy/lang/MetaClass;

    return-object v0
.end method

.method public final getTheClass()Ljava/lang/Class;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/lang/Class<",
            "*>;"
        }
    .end annotation

    .line 144
    iget-object v0, p0, Lorg/codehaus/groovy/reflection/ClassInfo;->classRef:Ljava/lang/ref/WeakReference;

    invoke-virtual {v0}, Ljava/lang/ref/WeakReference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/Class;

    return-object v0
.end method

.method public getVersion()I
    .locals 1

    .line 111
    iget-object v0, p0, Lorg/codehaus/groovy/reflection/ClassInfo;->version:Ljava/util/concurrent/atomic/AtomicInteger;

    invoke-virtual {v0}, Ljava/util/concurrent/atomic/AtomicInteger;->get()I

    move-result v0

    return v0
.end method

.method public getWeakMetaClass()Lgroovy/lang/MetaClass;
    .locals 1

    .line 223
    iget-object v0, p0, Lorg/codehaus/groovy/reflection/ClassInfo;->weakMetaClass:Lorg/codehaus/groovy/util/ManagedReference;

    if-nez v0, :cond_0

    const/4 v0, 0x0

    goto :goto_0

    .line 224
    :cond_0
    invoke-virtual {v0}, Lorg/codehaus/groovy/util/ManagedReference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovy/lang/MetaClass;

    :goto_0
    return-object v0
.end method

.method public hasPerInstanceMetaClasses()Z
    .locals 1

    .line 424
    iget-object v0, p0, Lorg/codehaus/groovy/reflection/ClassInfo;->perInstanceMetaClassMap:Lorg/codehaus/groovy/util/ManagedConcurrentMap;

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public incVersion()V
    .locals 1

    .line 115
    iget-object v0, p0, Lorg/codehaus/groovy/reflection/ClassInfo;->version:Ljava/util/concurrent/atomic/AtomicInteger;

    invoke-virtual {v0}, Ljava/util/concurrent/atomic/AtomicInteger;->incrementAndGet()I

    .line 116
    invoke-static {}, Lorg/codehaus/groovy/vmplugin/VMPluginFactory;->getPlugin()Lorg/codehaus/groovy/vmplugin/VMPlugin;

    move-result-object v0

    invoke-interface {v0}, Lorg/codehaus/groovy/vmplugin/VMPlugin;->invalidateCallSites()V

    return-void
.end method

.method public lock()V
    .locals 1

    .line 393
    iget-object v0, p0, Lorg/codehaus/groovy/reflection/ClassInfo;->lock:Lorg/codehaus/groovy/util/LockableObject;

    invoke-virtual {v0}, Lorg/codehaus/groovy/util/LockableObject;->lock()V

    return-void
.end method

.method public setPerInstanceMetaClass(Ljava/lang/Object;Lgroovy/lang/MetaClass;)V
    .locals 2

    .line 408
    iget-object v0, p0, Lorg/codehaus/groovy/reflection/ClassInfo;->version:Ljava/util/concurrent/atomic/AtomicInteger;

    invoke-virtual {v0}, Ljava/util/concurrent/atomic/AtomicInteger;->incrementAndGet()I

    if-eqz p2, :cond_1

    .line 411
    iget-object v0, p0, Lorg/codehaus/groovy/reflection/ClassInfo;->perInstanceMetaClassMap:Lorg/codehaus/groovy/util/ManagedConcurrentMap;

    if-nez v0, :cond_0

    .line 412
    new-instance v0, Lorg/codehaus/groovy/util/ManagedConcurrentMap;

    invoke-static {}, Lorg/codehaus/groovy/util/ReferenceBundle;->getWeakBundle()Lorg/codehaus/groovy/util/ReferenceBundle;

    move-result-object v1

    invoke-direct {v0, v1}, Lorg/codehaus/groovy/util/ManagedConcurrentMap;-><init>(Lorg/codehaus/groovy/util/ReferenceBundle;)V

    iput-object v0, p0, Lorg/codehaus/groovy/reflection/ClassInfo;->perInstanceMetaClassMap:Lorg/codehaus/groovy/util/ManagedConcurrentMap;

    .line 414
    :cond_0
    iget-object v0, p0, Lorg/codehaus/groovy/reflection/ClassInfo;->perInstanceMetaClassMap:Lorg/codehaus/groovy/util/ManagedConcurrentMap;

    invoke-virtual {v0, p1, p2}, Lorg/codehaus/groovy/util/ManagedConcurrentMap;->put(Ljava/lang/Object;Ljava/lang/Object;)V

    goto :goto_0

    .line 417
    :cond_1
    iget-object p2, p0, Lorg/codehaus/groovy/reflection/ClassInfo;->perInstanceMetaClassMap:Lorg/codehaus/groovy/util/ManagedConcurrentMap;

    if-eqz p2, :cond_2

    .line 418
    invoke-virtual {p2, p1}, Lorg/codehaus/groovy/util/ManagedConcurrentMap;->remove(Ljava/lang/Object;)V

    :cond_2
    :goto_0
    return-void
.end method

.method public setStrongMetaClass(Lgroovy/lang/MetaClass;)V
    .locals 2

    .line 194
    iget-object v0, p0, Lorg/codehaus/groovy/reflection/ClassInfo;->version:Ljava/util/concurrent/atomic/AtomicInteger;

    invoke-virtual {v0}, Ljava/util/concurrent/atomic/AtomicInteger;->incrementAndGet()I

    .line 198
    iget-object v0, p0, Lorg/codehaus/groovy/reflection/ClassInfo;->strongMetaClass:Lgroovy/lang/MetaClass;

    .line 200
    instance-of v1, v0, Lgroovy/lang/ExpandoMetaClass;

    if-eqz v1, :cond_1

    .line 201
    check-cast v0, Lgroovy/lang/ExpandoMetaClass;

    const/4 v1, 0x0

    iput-boolean v1, v0, Lgroovy/lang/ExpandoMetaClass;->inRegistry:Z

    .line 202
    sget-object v0, Lorg/codehaus/groovy/reflection/ClassInfo;->modifiedExpandos:Lorg/codehaus/groovy/util/ManagedConcurrentLinkedQueue;

    invoke-virtual {v0}, Lorg/codehaus/groovy/util/ManagedConcurrentLinkedQueue;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_0
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    .line 203
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/reflection/ClassInfo;

    if-ne v1, p0, :cond_0

    .line 205
    invoke-interface {v0}, Ljava/util/Iterator;->remove()V

    goto :goto_0

    .line 210
    :cond_1
    iput-object p1, p0, Lorg/codehaus/groovy/reflection/ClassInfo;->strongMetaClass:Lgroovy/lang/MetaClass;

    .line 212
    instance-of v0, p1, Lgroovy/lang/ExpandoMetaClass;

    if-eqz v0, :cond_2

    .line 213
    check-cast p1, Lgroovy/lang/ExpandoMetaClass;

    const/4 v0, 0x1

    iput-boolean v0, p1, Lgroovy/lang/ExpandoMetaClass;->inRegistry:Z

    .line 214
    sget-object p1, Lorg/codehaus/groovy/reflection/ClassInfo;->modifiedExpandos:Lorg/codehaus/groovy/util/ManagedConcurrentLinkedQueue;

    invoke-virtual {p1, p0}, Lorg/codehaus/groovy/util/ManagedConcurrentLinkedQueue;->add(Ljava/lang/Object;)V

    :cond_2
    const/4 p1, 0x0

    .line 217
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/reflection/ClassInfo;->replaceWeakMetaClassRef(Lorg/codehaus/groovy/util/ManagedReference;)V

    return-void
.end method

.method public setWeakMetaClass(Lgroovy/lang/MetaClass;)V
    .locals 2

    .line 228
    iget-object v0, p0, Lorg/codehaus/groovy/reflection/ClassInfo;->version:Ljava/util/concurrent/atomic/AtomicInteger;

    invoke-virtual {v0}, Ljava/util/concurrent/atomic/AtomicInteger;->incrementAndGet()I

    const/4 v0, 0x0

    .line 230
    iput-object v0, p0, Lorg/codehaus/groovy/reflection/ClassInfo;->strongMetaClass:Lgroovy/lang/MetaClass;

    if-eqz p1, :cond_0

    .line 233
    new-instance v0, Lorg/codehaus/groovy/util/ManagedReference;

    sget-object v1, Lorg/codehaus/groovy/reflection/ClassInfo;->softBundle:Lorg/codehaus/groovy/util/ReferenceBundle;

    invoke-direct {v0, v1, p1}, Lorg/codehaus/groovy/util/ManagedReference;-><init>(Lorg/codehaus/groovy/util/ReferenceBundle;Ljava/lang/Object;)V

    .line 235
    :cond_0
    invoke-direct {p0, v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->replaceWeakMetaClassRef(Lorg/codehaus/groovy/util/ManagedReference;)V

    return-void
.end method

.method public unlock()V
    .locals 1

    .line 397
    iget-object v0, p0, Lorg/codehaus/groovy/reflection/ClassInfo;->lock:Lorg/codehaus/groovy/util/LockableObject;

    invoke-virtual {v0}, Lorg/codehaus/groovy/util/LockableObject;->unlock()V

    return-void
.end method
