.class Lorg/codehaus/groovy/reflection/ClassInfo$LazyCachedClassRef;
.super Lorg/codehaus/groovy/util/LazyReference;
.source "ClassInfo.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/codehaus/groovy/reflection/ClassInfo;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0xa
    name = "LazyCachedClassRef"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lorg/codehaus/groovy/util/LazyReference<",
        "Lorg/codehaus/groovy/reflection/CachedClass;",
        ">;"
    }
.end annotation


# static fields
.field private static final serialVersionUID:J = -0x136ec5eb5e5300e8L


# instance fields
.field private final info:Lorg/codehaus/groovy/reflection/ClassInfo;


# direct methods
.method constructor <init>(Lorg/codehaus/groovy/util/ReferenceBundle;Lorg/codehaus/groovy/reflection/ClassInfo;)V
    .locals 0

    .line 432
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/util/LazyReference;-><init>(Lorg/codehaus/groovy/util/ReferenceBundle;)V

    .line 433
    iput-object p2, p0, Lorg/codehaus/groovy/reflection/ClassInfo$LazyCachedClassRef;->info:Lorg/codehaus/groovy/reflection/ClassInfo;

    return-void
.end method


# virtual methods
.method public bridge synthetic initValue()Ljava/lang/Object;
    .locals 1

    .line 427
    invoke-virtual {p0}, Lorg/codehaus/groovy/reflection/ClassInfo$LazyCachedClassRef;->initValue()Lorg/codehaus/groovy/reflection/CachedClass;

    move-result-object v0

    return-object v0
.end method

.method public initValue()Lorg/codehaus/groovy/reflection/CachedClass;
    .locals 2

    .line 437
    iget-object v0, p0, Lorg/codehaus/groovy/reflection/ClassInfo$LazyCachedClassRef;->info:Lorg/codehaus/groovy/reflection/ClassInfo;

    invoke-static {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->access$200(Lorg/codehaus/groovy/reflection/ClassInfo;)Ljava/lang/ref/WeakReference;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/ref/WeakReference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/Class;

    iget-object v1, p0, Lorg/codehaus/groovy/reflection/ClassInfo$LazyCachedClassRef;->info:Lorg/codehaus/groovy/reflection/ClassInfo;

    invoke-static {v0, v1}, Lorg/codehaus/groovy/reflection/ClassInfo;->access$300(Ljava/lang/Class;Lorg/codehaus/groovy/reflection/ClassInfo;)Lorg/codehaus/groovy/reflection/CachedClass;

    move-result-object v0

    return-object v0
.end method
