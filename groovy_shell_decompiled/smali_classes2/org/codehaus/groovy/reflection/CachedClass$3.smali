.class Lorg/codehaus/groovy/reflection/CachedClass$3;
.super Lorg/codehaus/groovy/util/LazyReference;
.source "CachedClass.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/codehaus/groovy/reflection/CachedClass;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lorg/codehaus/groovy/util/LazyReference<",
        "[",
        "Lorg/codehaus/groovy/reflection/CachedMethod;",
        ">;"
    }
.end annotation


# static fields
.field private static final serialVersionUID:J = 0x581727e588c21544L


# instance fields
.field final synthetic this$0:Lorg/codehaus/groovy/reflection/CachedClass;


# direct methods
.method constructor <init>(Lorg/codehaus/groovy/reflection/CachedClass;Lorg/codehaus/groovy/util/ReferenceBundle;)V
    .locals 0

    .line 80
    iput-object p1, p0, Lorg/codehaus/groovy/reflection/CachedClass$3;->this$0:Lorg/codehaus/groovy/reflection/CachedClass;

    invoke-direct {p0, p2}, Lorg/codehaus/groovy/util/LazyReference;-><init>(Lorg/codehaus/groovy/util/ReferenceBundle;)V

    return-void
.end method

.method static synthetic lambda$initValue$0(Ljava/lang/reflect/Method;)Z
    .locals 1

    .line 88
    invoke-virtual {p0}, Ljava/lang/reflect/Method;->getName()Ljava/lang/String;

    move-result-object p0

    const/16 v0, 0x2b

    invoke-virtual {p0, v0}, Ljava/lang/String;->indexOf(I)I

    move-result p0

    if-gez p0, :cond_0

    const/4 p0, 0x1

    goto :goto_0

    :cond_0
    const/4 p0, 0x0

    :goto_0
    return p0
.end method

.method static synthetic lambda$initValue$1(Ljava/lang/reflect/Method;)Z
    .locals 1

    .line 89
    const-class v0, Lorg/codehaus/groovy/reflection/CachedClass;

    invoke-static {p0, v0}, Lorg/codehaus/groovy/reflection/ReflectionUtils;->checkCanSetAccessible(Ljava/lang/reflect/AccessibleObject;Ljava/lang/Class;)Z

    move-result v0

    if-nez v0, :cond_1

    invoke-virtual {p0}, Ljava/lang/reflect/Method;->getModifiers()I

    move-result p0

    invoke-static {p0}, Ljava/lang/reflect/Modifier;->isProtected(I)Z

    move-result p0

    if-eqz p0, :cond_0

    goto :goto_0

    :cond_0
    const/4 p0, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 p0, 0x1

    :goto_1
    return p0
.end method

.method static synthetic lambda$initValue$3(I)[Lorg/codehaus/groovy/reflection/CachedMethod;
    .locals 0

    .line 91
    new-array p0, p0, [Lorg/codehaus/groovy/reflection/CachedMethod;

    return-object p0
.end method


# virtual methods
.method public bridge synthetic initValue()Ljava/lang/Object;
    .locals 1

    .line 80
    invoke-virtual {p0}, Lorg/codehaus/groovy/reflection/CachedClass$3;->initValue()[Lorg/codehaus/groovy/reflection/CachedMethod;

    move-result-object v0

    return-object v0
.end method

.method public initValue()[Lorg/codehaus/groovy/reflection/CachedMethod;
    .locals 8

    .line 85
    new-instance v0, Lorg/codehaus/groovy/reflection/CachedClass$3$$ExternalSyntheticLambda0;

    invoke-direct {v0, p0}, Lorg/codehaus/groovy/reflection/CachedClass$3$$ExternalSyntheticLambda0;-><init>(Lorg/codehaus/groovy/reflection/CachedClass$3;)V

    .line 96
    invoke-static {v0}, Ljava/security/AccessController;->doPrivileged(Ljava/security/PrivilegedAction;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lorg/codehaus/groovy/reflection/CachedMethod;

    .line 98
    new-instance v1, Ljava/util/ArrayList;

    array-length v2, v0

    invoke-direct {v1, v2}, Ljava/util/ArrayList;-><init>(I)V

    .line 99
    new-instance v2, Ljava/util/ArrayList;

    array-length v3, v0

    invoke-direct {v2, v3}, Ljava/util/ArrayList;-><init>(I)V

    .line 100
    array-length v3, v0

    const/4 v4, 0x0

    :goto_0
    if-ge v4, v3, :cond_2

    aget-object v5, v0, v4

    .line 101
    invoke-virtual {v5}, Lorg/codehaus/groovy/reflection/CachedMethod;->getName()Ljava/lang/String;

    move-result-object v6

    const-string v7, "this$"

    .line 102
    invoke-virtual {v6, v7}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v7

    if-nez v7, :cond_1

    const-string v7, "super$"

    invoke-virtual {v6, v7}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v6

    if-eqz v6, :cond_0

    goto :goto_1

    .line 105
    :cond_0
    invoke-interface {v1, v5}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_2

    .line 103
    :cond_1
    :goto_1
    invoke-interface {v2, v5}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :goto_2
    add-int/lit8 v4, v4, 0x1

    goto :goto_0

    .line 108
    :cond_2
    invoke-static {v1}, Ljava/util/Collections;->sort(Ljava/util/List;)V

    .line 110
    iget-object v0, p0, Lorg/codehaus/groovy/reflection/CachedClass$3;->this$0:Lorg/codehaus/groovy/reflection/CachedClass;

    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/CachedClass;->getCachedSuperClass()Lorg/codehaus/groovy/reflection/CachedClass;

    move-result-object v0

    if-eqz v0, :cond_3

    .line 112
    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/CachedClass;->getMethods()[Lorg/codehaus/groovy/reflection/CachedMethod;

    .line 113
    iget-object v0, v0, Lorg/codehaus/groovy/reflection/CachedClass;->mopMethods:[Lorg/codehaus/groovy/reflection/CachedMethod;

    invoke-static {v2, v0}, Ljava/util/Collections;->addAll(Ljava/util/Collection;[Ljava/lang/Object;)Z

    .line 115
    :cond_3
    invoke-interface {v2}, Ljava/util/List;->size()I

    move-result v0

    const/4 v3, 0x1

    if-le v0, v3, :cond_4

    .line 116
    sget-object v0, Lorg/codehaus/groovy/reflection/CachedClass$CachedMethodComparatorByName;->INSTANCE:Ljava/util/Comparator;

    invoke-interface {v2, v0}, Ljava/util/List;->sort(Ljava/util/Comparator;)V

    .line 118
    :cond_4
    iget-object v0, p0, Lorg/codehaus/groovy/reflection/CachedClass$3;->this$0:Lorg/codehaus/groovy/reflection/CachedClass;

    sget-object v3, Lorg/codehaus/groovy/reflection/CachedMethod;->EMPTY_ARRAY:[Lorg/codehaus/groovy/reflection/CachedMethod;

    invoke-interface {v2, v3}, Ljava/util/List;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object v2

    check-cast v2, [Lorg/codehaus/groovy/reflection/CachedMethod;

    iput-object v2, v0, Lorg/codehaus/groovy/reflection/CachedClass;->mopMethods:[Lorg/codehaus/groovy/reflection/CachedMethod;

    .line 120
    sget-object v0, Lorg/codehaus/groovy/reflection/CachedMethod;->EMPTY_ARRAY:[Lorg/codehaus/groovy/reflection/CachedMethod;

    invoke-interface {v1, v0}, Ljava/util/List;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lorg/codehaus/groovy/reflection/CachedMethod;

    return-object v0
.end method

.method public synthetic lambda$initValue$2$org-codehaus-groovy-reflection-CachedClass$3(Ljava/lang/reflect/Method;)Lorg/codehaus/groovy/reflection/CachedMethod;
    .locals 2

    .line 90
    new-instance v0, Lorg/codehaus/groovy/reflection/CachedMethod;

    iget-object v1, p0, Lorg/codehaus/groovy/reflection/CachedClass$3;->this$0:Lorg/codehaus/groovy/reflection/CachedClass;

    invoke-direct {v0, v1, p1}, Lorg/codehaus/groovy/reflection/CachedMethod;-><init>(Lorg/codehaus/groovy/reflection/CachedClass;Ljava/lang/reflect/Method;)V

    return-object v0
.end method

.method public synthetic lambda$initValue$4$org-codehaus-groovy-reflection-CachedClass$3()[Lorg/codehaus/groovy/reflection/CachedMethod;
    .locals 2

    .line 87
    :try_start_0
    iget-object v0, p0, Lorg/codehaus/groovy/reflection/CachedClass$3;->this$0:Lorg/codehaus/groovy/reflection/CachedClass;

    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/CachedClass;->getTheClass()Ljava/lang/Class;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/Class;->getDeclaredMethods()[Ljava/lang/reflect/Method;

    move-result-object v0

    invoke-static {v0}, Ljava/util/Arrays;->stream([Ljava/lang/Object;)Ljava/util/stream/Stream;

    move-result-object v0

    sget-object v1, Lorg/codehaus/groovy/reflection/CachedClass$3$$ExternalSyntheticLambda3;->INSTANCE:Lorg/codehaus/groovy/reflection/CachedClass$3$$ExternalSyntheticLambda3;

    .line 88
    invoke-interface {v0, v1}, Ljava/util/stream/Stream;->filter(Ljava/util/function/Predicate;)Ljava/util/stream/Stream;

    move-result-object v0

    sget-object v1, Lorg/codehaus/groovy/reflection/CachedClass$3$$ExternalSyntheticLambda4;->INSTANCE:Lorg/codehaus/groovy/reflection/CachedClass$3$$ExternalSyntheticLambda4;

    .line 89
    invoke-interface {v0, v1}, Ljava/util/stream/Stream;->filter(Ljava/util/function/Predicate;)Ljava/util/stream/Stream;

    move-result-object v0

    new-instance v1, Lorg/codehaus/groovy/reflection/CachedClass$3$$ExternalSyntheticLambda1;

    invoke-direct {v1, p0}, Lorg/codehaus/groovy/reflection/CachedClass$3$$ExternalSyntheticLambda1;-><init>(Lorg/codehaus/groovy/reflection/CachedClass$3;)V

    .line 90
    invoke-interface {v0, v1}, Ljava/util/stream/Stream;->map(Ljava/util/function/Function;)Ljava/util/stream/Stream;

    move-result-object v0

    sget-object v1, Lorg/codehaus/groovy/reflection/CachedClass$3$$ExternalSyntheticLambda2;->INSTANCE:Lorg/codehaus/groovy/reflection/CachedClass$3$$ExternalSyntheticLambda2;

    .line 91
    invoke-interface {v0, v1}, Ljava/util/stream/Stream;->toArray(Ljava/util/function/IntFunction;)[Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lorg/codehaus/groovy/reflection/CachedMethod;
    :try_end_0
    .catch Ljava/lang/LinkageError; {:try_start_0 .. :try_end_0} :catch_0

    return-object v0

    .line 93
    :catch_0
    sget-object v0, Lorg/codehaus/groovy/reflection/CachedMethod;->EMPTY_ARRAY:[Lorg/codehaus/groovy/reflection/CachedMethod;

    return-object v0
.end method
