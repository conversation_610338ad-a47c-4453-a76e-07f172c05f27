.class Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7$GroovyClassValuePreJava7Segment;
.super Lorg/codehaus/groovy/util/ManagedConcurrentMap$Segment;
.source "GroovyClassValuePreJava7.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x2
    name = "GroovyClassValuePreJava7Segment"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lorg/codehaus/groovy/util/ManagedConcurrentMap$Segment<",
        "Ljava/lang/Class<",
        "*>;TT;>;"
    }
.end annotation


# static fields
.field private static final serialVersionUID:J = 0x11e62068d72dfeb0L


# instance fields
.field final synthetic this$0:Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7;


# direct methods
.method constructor <init>(Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7;Lorg/codehaus/groovy/util/ReferenceBundle;I)V
    .locals 0

    .line 59
    iput-object p1, p0, Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7$GroovyClassValuePreJava7Segment;->this$0:Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7;

    .line 60
    invoke-direct {p0, p2, p3}, Lorg/codehaus/groovy/util/ManagedConcurrentMap$Segment;-><init>(Lorg/codehaus/groovy/util/ReferenceBundle;I)V

    return-void
.end method


# virtual methods
.method protected createEntry(Ljava/lang/Class;ILjava/lang/Object;)Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7$EntryWithValue;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Class<",
            "*>;ITT;)",
            "Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7<",
            "TT;>.EntryWithValue;"
        }
    .end annotation

    .line 66
    new-instance p3, Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7$EntryWithValue;

    iget-object v0, p0, Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7$GroovyClassValuePreJava7Segment;->this$0:Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7;

    invoke-direct {p3, v0, p0, p1, p2}, Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7$EntryWithValue;-><init>(Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7;Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7$GroovyClassValuePreJava7Segment;Ljava/lang/Class;I)V

    return-object p3
.end method

.method protected bridge synthetic createEntry(Ljava/lang/Object;ILjava/lang/Object;)Lorg/codehaus/groovy/util/AbstractConcurrentMap$Entry;
    .locals 0

    .line 55
    check-cast p1, Ljava/lang/Class;

    invoke-virtual {p0, p1, p2, p3}, Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7$GroovyClassValuePreJava7Segment;->createEntry(Ljava/lang/Class;ILjava/lang/Object;)Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7$EntryWithValue;

    move-result-object p1

    return-object p1
.end method
