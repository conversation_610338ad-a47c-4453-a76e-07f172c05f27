.class Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7;
.super Ljava/lang/Object;
.source "GroovyClassValuePreJava7.java"

# interfaces
.implements Lorg/codehaus/groovy/reflection/GroovyClassValue;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7$GroovyClassValuePreJava7Map;,
        Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7$EntryWithValue;,
        Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7$GroovyClassValuePreJava7Segment;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;",
        "Lorg/codehaus/groovy/reflection/GroovyClassValue<",
        "TT;>;"
    }
.end annotation


# static fields
.field private static final weakBundle:Lorg/codehaus/groovy/util/ReferenceBundle;


# instance fields
.field private final computeValue:Lorg/codehaus/groovy/reflection/GroovyClassValue$ComputeValue;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lorg/codehaus/groovy/reflection/GroovyClassValue$ComputeValue<",
            "TT;>;"
        }
    .end annotation
.end field

.field private final map:Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7$GroovyClassValuePreJava7Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7<",
            "TT;>.GroovyClassValuePreJava7Map;"
        }
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 32
    invoke-static {}, Lorg/codehaus/groovy/util/ReferenceBundle;->getWeakBundle()Lorg/codehaus/groovy/util/ReferenceBundle;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7;->weakBundle:Lorg/codehaus/groovy/util/ReferenceBundle;

    return-void
.end method

.method public constructor <init>(Lorg/codehaus/groovy/reflection/GroovyClassValue$ComputeValue;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/reflection/GroovyClassValue$ComputeValue<",
            "TT;>;)V"
        }
    .end annotation

    .line 89
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 87
    new-instance v0, Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7$GroovyClassValuePreJava7Map;

    invoke-direct {v0, p0}, Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7$GroovyClassValuePreJava7Map;-><init>(Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7;)V

    iput-object v0, p0, Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7;->map:Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7$GroovyClassValuePreJava7Map;

    .line 90
    iput-object p1, p0, Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7;->computeValue:Lorg/codehaus/groovy/reflection/GroovyClassValue$ComputeValue;

    return-void
.end method

.method static synthetic access$000()Lorg/codehaus/groovy/util/ReferenceBundle;
    .locals 1

    .line 31
    sget-object v0, Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7;->weakBundle:Lorg/codehaus/groovy/util/ReferenceBundle;

    return-object v0
.end method

.method static synthetic access$100(Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7;)Lorg/codehaus/groovy/reflection/GroovyClassValue$ComputeValue;
    .locals 0

    .line 31
    iget-object p0, p0, Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7;->computeValue:Lorg/codehaus/groovy/reflection/GroovyClassValue$ComputeValue;

    return-object p0
.end method


# virtual methods
.method public get(Ljava/lang/Class;)Ljava/lang/Object;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Class<",
            "*>;)TT;"
        }
    .end annotation

    .line 96
    iget-object v0, p0, Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7;->map:Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7$GroovyClassValuePreJava7Map;

    const/4 v1, 0x0

    invoke-virtual {v0, p1, v1}, Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7$GroovyClassValuePreJava7Map;->getOrPut(Ljava/lang/Object;Ljava/lang/Object;)Lorg/codehaus/groovy/util/AbstractConcurrentMap$Entry;

    move-result-object p1

    check-cast p1, Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7$EntryWithValue;

    invoke-virtual {p1}, Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7$EntryWithValue;->getValue()Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public remove(Ljava/lang/Class;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Class<",
            "*>;)V"
        }
    .end annotation

    .line 103
    iget-object v0, p0, Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7;->map:Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7$GroovyClassValuePreJava7Map;

    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7$GroovyClassValuePreJava7Map;->remove(Ljava/lang/Object;)V

    return-void
.end method
