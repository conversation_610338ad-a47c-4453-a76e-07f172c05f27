.class Lorg/codehaus/groovy/reflection/GroovyClassValueFactory;
.super Ljava/lang/Object;
.source "GroovyClassValueFactory.java"


# static fields
.field private static final USE_CLASSVALUE:Z


# direct methods
.method static constructor <clinit>()V
    .locals 2

    const-string v0, "groovy.use.classvalue"

    const-string v1, "true"

    .line 32
    invoke-static {v0, v1}, Lorg/apache/groovy/util/SystemUtil;->getSystemPropertySafe(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Ljava/lang/Boolean;->parseBoolean(Ljava/lang/String;)Z

    move-result v0

    sput-boolean v0, Lorg/codehaus/groovy/reflection/GroovyClassValueFactory;->USE_CLASSVALUE:Z

    return-void
.end method

.method constructor <init>()V
    .locals 0

    .line 25
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static createGroovyClassValue(Lorg/codehaus/groovy/reflection/GroovyClassValue$ComputeValue;)Lorg/codehaus/groovy/reflection/GroovyClassValue;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Lorg/codehaus/groovy/reflection/GroovyClassValue$ComputeValue<",
            "TT;>;)",
            "Lorg/codehaus/groovy/reflection/GroovyClassValue<",
            "TT;>;"
        }
    .end annotation

    .line 35
    sget-boolean v0, Lorg/codehaus/groovy/reflection/GroovyClassValueFactory;->USE_CLASSVALUE:Z

    if-eqz v0, :cond_0

    .line 36
    new-instance v0, Lorg/codehaus/groovy/reflection/v7/GroovyClassValueJava7;

    invoke-direct {v0, p0}, Lorg/codehaus/groovy/reflection/v7/GroovyClassValueJava7;-><init>(Lorg/codehaus/groovy/reflection/GroovyClassValue$ComputeValue;)V

    goto :goto_0

    .line 37
    :cond_0
    new-instance v0, Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7;

    invoke-direct {v0, p0}, Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7;-><init>(Lorg/codehaus/groovy/reflection/GroovyClassValue$ComputeValue;)V

    :goto_0
    return-object v0
.end method
