.class Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7$EntryWithValue;
.super Lorg/codehaus/groovy/util/ManagedConcurrentMap$EntryWithValue;
.source "GroovyClassValuePreJava7.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x2
    name = "EntryWithValue"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lorg/codehaus/groovy/util/ManagedConcurrentMap$EntryWithValue<",
        "Ljava/lang/Class<",
        "*>;TT;>;"
    }
.end annotation


# instance fields
.field final synthetic this$0:Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7;


# direct methods
.method public constructor <init>(Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7;Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7$GroovyClassValuePreJava7Segment;Ljava/lang/Class;I)V
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7<",
            "TT;>.GroovyClassValuePreJava7Segment;",
            "Ljava/lang/Class<",
            "*>;I)V"
        }
    .end annotation

    .line 36
    iput-object p1, p0, Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7$EntryWithValue;->this$0:Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7;

    .line 37
    invoke-static {}, Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7;->access$000()Lorg/codehaus/groovy/util/ReferenceBundle;

    move-result-object v1

    invoke-static {p1}, Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7;->access$100(Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7;)Lorg/codehaus/groovy/reflection/GroovyClassValue$ComputeValue;

    move-result-object p1

    invoke-interface {p1, p3}, Lorg/codehaus/groovy/reflection/GroovyClassValue$ComputeValue;->computeValue(Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v5

    move-object v0, p0

    move-object v2, p2

    move-object v3, p3

    move v4, p4

    invoke-direct/range {v0 .. v5}, Lorg/codehaus/groovy/util/ManagedConcurrentMap$EntryWithValue;-><init>(Lorg/codehaus/groovy/util/ReferenceBundle;Lorg/codehaus/groovy/util/ManagedConcurrentMap$Segment;Ljava/lang/Object;ILjava/lang/Object;)V

    return-void
.end method


# virtual methods
.method public finalizeReference()V
    .locals 2

    .line 47
    invoke-virtual {p0}, Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7$EntryWithValue;->getValue()Ljava/lang/Object;

    move-result-object v0

    .line 48
    instance-of v1, v0, Lorg/codehaus/groovy/util/Finalizable;

    if-eqz v1, :cond_0

    .line 49
    check-cast v0, Lorg/codehaus/groovy/util/Finalizable;

    invoke-interface {v0}, Lorg/codehaus/groovy/util/Finalizable;->finalizeReference()V

    .line 51
    :cond_0
    invoke-super {p0}, Lorg/codehaus/groovy/util/ManagedConcurrentMap$EntryWithValue;->finalizeReference()V

    return-void
.end method

.method public setValue(Ljava/lang/Object;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TT;)V"
        }
    .end annotation

    if-eqz p1, :cond_0

    .line 42
    invoke-super {p0, p1}, Lorg/codehaus/groovy/util/ManagedConcurrentMap$EntryWithValue;->setValue(Ljava/lang/Object;)V

    :cond_0
    return-void
.end method
