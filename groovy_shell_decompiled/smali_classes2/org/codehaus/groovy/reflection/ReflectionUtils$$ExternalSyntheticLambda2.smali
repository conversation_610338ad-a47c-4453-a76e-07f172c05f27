.class public final synthetic Lorg/codehaus/groovy/reflection/ReflectionUtils$$ExternalSyntheticLambda2;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Function;


# static fields
.field public static final synthetic INSTANCE:Lorg/codehaus/groovy/reflection/ReflectionUtils$$ExternalSyntheticLambda2;


# direct methods
.method static synthetic constructor <clinit>()V
    .locals 1

    new-instance v0, Lorg/codehaus/groovy/reflection/ReflectionUtils$$ExternalSyntheticLambda2;

    invoke-direct {v0}, Lorg/codehaus/groovy/reflection/ReflectionUtils$$ExternalSyntheticLambda2;-><init>()V

    sput-object v0, Lorg/codehaus/groovy/reflection/ReflectionUtils$$ExternalSyntheticLambda2;->INSTANCE:Lorg/codehaus/groovy/reflection/ReflectionUtils$$ExternalSyntheticLambda2;

    return-void
.end method

.method private synthetic constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final apply(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    check-cast p1, Ljava/lang/Class;

    invoke-static {p1}, Lorg/codehaus/groovy/reflection/ReflectionUtils;->$r8$lambda$shF8saQsWFDsy9IE4443JR1Bxpk(Ljava/lang/Class;)[Ljava/lang/reflect/Method;

    move-result-object p1

    return-object p1
.end method
