.class public Lorg/codehaus/groovy/reflection/CachedClass$CachedMethodComparatorByName;
.super Ljava/lang/Object;
.source "CachedClass.java"

# interfaces
.implements Ljava/util/Comparator;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/codehaus/groovy/reflection/CachedClass;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "CachedMethodComparatorByName"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ljava/util/Comparator<",
        "Lorg/codehaus/groovy/reflection/CachedMethod;",
        ">;"
    }
.end annotation


# static fields
.field public static final INSTANCE:Ljava/util/Comparator;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 480
    new-instance v0, Lorg/codehaus/groovy/reflection/CachedClass$CachedMethodComparatorByName;

    invoke-direct {v0}, Lorg/codehaus/groovy/reflection/CachedClass$CachedMethodComparatorByName;-><init>()V

    sput-object v0, Lorg/codehaus/groovy/reflection/CachedClass$CachedMethodComparatorByName;->INSTANCE:Ljava/util/Comparator;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 479
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public bridge synthetic compare(Ljava/lang/Object;Ljava/lang/Object;)I
    .locals 0

    .line 479
    check-cast p1, Lorg/codehaus/groovy/reflection/CachedMethod;

    check-cast p2, Lorg/codehaus/groovy/reflection/CachedMethod;

    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/reflection/CachedClass$CachedMethodComparatorByName;->compare(Lorg/codehaus/groovy/reflection/CachedMethod;Lorg/codehaus/groovy/reflection/CachedMethod;)I

    move-result p1

    return p1
.end method

.method public compare(Lorg/codehaus/groovy/reflection/CachedMethod;Lorg/codehaus/groovy/reflection/CachedMethod;)I
    .locals 0

    .line 484
    invoke-virtual {p1}, Lorg/codehaus/groovy/reflection/CachedMethod;->getName()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p2}, Lorg/codehaus/groovy/reflection/CachedMethod;->getName()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p1, p2}, Ljava/lang/String;->compareTo(Ljava/lang/String;)I

    move-result p1

    return p1
.end method
