.class public final synthetic Lorg/codehaus/groovy/reflection/CachedClass$1$$ExternalSyntheticLambda1;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Function;


# static fields
.field public static final synthetic INSTANCE:Lorg/codehaus/groovy/reflection/CachedClass$1$$ExternalSyntheticLambda1;


# direct methods
.method static synthetic constructor <clinit>()V
    .locals 1

    new-instance v0, Lorg/codehaus/groovy/reflection/CachedClass$1$$ExternalSyntheticLambda1;

    invoke-direct {v0}, Lorg/codehaus/groovy/reflection/CachedClass$1$$ExternalSyntheticLambda1;-><init>()V

    sput-object v0, Lorg/codehaus/groovy/reflection/CachedClass$1$$ExternalSyntheticLambda1;->INSTANCE:Lorg/codehaus/groovy/reflection/CachedClass$1$$ExternalSyntheticLambda1;

    return-void
.end method

.method private synthetic constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final apply(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    new-instance v0, Lorg/codehaus/groovy/reflection/CachedField;

    check-cast p1, Ljava/lang/reflect/Field;

    invoke-direct {v0, p1}, Lorg/codehaus/groovy/reflection/CachedField;-><init>(Ljava/lang/reflect/Field;)V

    return-object v0
.end method
