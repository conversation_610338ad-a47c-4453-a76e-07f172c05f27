.class public Lorg/codehaus/groovy/reflection/ClassLoaderForClassArtifacts;
.super Ljava/lang/ClassLoader;
.source "ClassLoaderForClassArtifacts.java"


# instance fields
.field private final classNamesCounter:Ljava/util/concurrent/atomic/AtomicInteger;

.field public final klazz:Ljava/lang/ref/SoftReference;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/lang/ref/SoftReference<",
            "Ljava/lang/Class;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Ljava/lang/Class;)V
    .locals 2

    .line 38
    invoke-virtual {p1}, Ljava/lang/Class;->getClassLoader()Ljava/lang/ClassLoader;

    move-result-object v0

    invoke-direct {p0, v0}, Ljava/lang/ClassLoader;-><init>(Ljava/lang/ClassLoader;)V

    .line 35
    new-instance v0, Ljava/util/concurrent/atomic/AtomicInteger;

    const/4 v1, -0x1

    invoke-direct {v0, v1}, Ljava/util/concurrent/atomic/AtomicInteger;-><init>(I)V

    iput-object v0, p0, Lorg/codehaus/groovy/reflection/ClassLoaderForClassArtifacts;->classNamesCounter:Ljava/util/concurrent/atomic/AtomicInteger;

    .line 39
    new-instance v0, Ljava/lang/ref/SoftReference;

    invoke-direct {v0, p1}, Ljava/lang/ref/SoftReference;-><init>(Ljava/lang/Object;)V

    iput-object v0, p0, Lorg/codehaus/groovy/reflection/ClassLoaderForClassArtifacts;->klazz:Ljava/lang/ref/SoftReference;

    return-void
.end method


# virtual methods
.method public createClassName(Ljava/lang/String;)Ljava/lang/String;
    .locals 5

    .line 68
    iget-object v0, p0, Lorg/codehaus/groovy/reflection/ClassLoaderForClassArtifacts;->klazz:Ljava/lang/ref/SoftReference;

    invoke-virtual {v0}, Ljava/lang/ref/SoftReference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/Class;

    invoke-virtual {v0}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v0

    const-string v1, "java."

    .line 69
    invoke-virtual {v0, v1}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v1

    const-string v2, "$"

    if-eqz v1, :cond_0

    .line 70
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const/16 v3, 0x2e

    const/16 v4, 0x5f

    invoke-virtual {v0, v3, v4}, Ljava/lang/String;->replace(CC)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    goto :goto_0

    .line 72
    :cond_0
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    .line 73
    :goto_0
    iget-object v0, p0, Lorg/codehaus/groovy/reflection/ClassLoaderForClassArtifacts;->classNamesCounter:Ljava/util/concurrent/atomic/AtomicInteger;

    invoke-virtual {v0}, Ljava/util/concurrent/atomic/AtomicInteger;->getAndIncrement()I

    move-result v0

    const/4 v1, -0x1

    if-ne v0, v1, :cond_1

    goto :goto_1

    .line 74
    :cond_1
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    :goto_1
    return-object p1
.end method

.method public createClassName(Ljava/lang/reflect/Method;)Ljava/lang/String;
    .locals 0

    .line 63
    invoke-virtual {p1}, Ljava/lang/reflect/Method;->getName()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/reflection/ClassLoaderForClassArtifacts;->createClassName(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public define(Ljava/lang/String;[B)Ljava/lang/Class;
    .locals 6

    .line 43
    array-length v4, p2

    iget-object v0, p0, Lorg/codehaus/groovy/reflection/ClassLoaderForClassArtifacts;->klazz:Ljava/lang/ref/SoftReference;

    invoke-virtual {v0}, Ljava/lang/ref/SoftReference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/Class;

    invoke-virtual {v0}, Ljava/lang/Class;->getProtectionDomain()Ljava/security/ProtectionDomain;

    move-result-object v5

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-virtual/range {v0 .. v5}, Lorg/codehaus/groovy/reflection/ClassLoaderForClassArtifacts;->defineClass(Ljava/lang/String;[BIILjava/security/ProtectionDomain;)Ljava/lang/Class;

    move-result-object p1

    .line 44
    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/reflection/ClassLoaderForClassArtifacts;->resolveClass(Ljava/lang/Class;)V

    return-object p1
.end method

.method public defineClassAndGetConstructor(Ljava/lang/String;[B)Ljava/lang/reflect/Constructor;
    .locals 2

    .line 78
    new-instance v0, Lorg/codehaus/groovy/reflection/ClassLoaderForClassArtifacts$$ExternalSyntheticLambda0;

    invoke-direct {v0, p0, p1, p2}, Lorg/codehaus/groovy/reflection/ClassLoaderForClassArtifacts$$ExternalSyntheticLambda0;-><init>(Lorg/codehaus/groovy/reflection/ClassLoaderForClassArtifacts;Ljava/lang/String;[B)V

    invoke-static {v0}, Ljava/security/AccessController;->doPrivileged(Ljava/security/PrivilegedAction;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/Class;

    if-eqz p1, :cond_0

    const/4 p2, 0x5

    :try_start_0
    new-array p2, p2, [Ljava/lang/Class;

    const/4 v0, 0x0

    .line 82
    const-class v1, Lorg/codehaus/groovy/runtime/callsite/CallSite;

    aput-object v1, p2, v0

    const/4 v0, 0x1

    const-class v1, Lgroovy/lang/MetaClassImpl;

    aput-object v1, p2, v0

    const/4 v0, 0x2

    const-class v1, Lgroovy/lang/MetaMethod;

    aput-object v1, p2, v0

    const/4 v0, 0x3

    const-class v1, [Ljava/lang/Class;

    aput-object v1, p2, v0

    const/4 v0, 0x4

    const-class v1, Ljava/lang/reflect/Constructor;

    aput-object v1, p2, v0

    invoke-virtual {p1, p2}, Ljava/lang/Class;->getConstructor([Ljava/lang/Class;)Ljava/lang/reflect/Constructor;

    move-result-object p1
    :try_end_0
    .catch Ljava/lang/NoSuchMethodException; {:try_start_0 .. :try_end_0} :catch_0

    return-object p1

    :catch_0
    :cond_0
    const/4 p1, 0x0

    return-object p1
.end method

.method public synthetic lambda$defineClassAndGetConstructor$0$org-codehaus-groovy-reflection-ClassLoaderForClassArtifacts(Ljava/lang/String;[B)Ljava/lang/Class;
    .locals 0

    .line 78
    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/reflection/ClassLoaderForClassArtifacts;->define(Ljava/lang/String;[B)Ljava/lang/Class;

    move-result-object p1

    return-object p1
.end method

.method public loadClass(Ljava/lang/String;)Ljava/lang/Class;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/ClassNotFoundException;
        }
    .end annotation

    .line 49
    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/reflection/ClassLoaderForClassArtifacts;->findLoadedClass(Ljava/lang/String;)Ljava/lang/Class;

    move-result-object v0

    if-eqz v0, :cond_0

    return-object v0

    .line 53
    :cond_0
    sget-object v0, Lorg/codehaus/groovy/runtime/callsite/GroovySunClassLoader;->sunVM:Lorg/codehaus/groovy/reflection/SunClassLoader;

    if-eqz v0, :cond_1

    .line 54
    sget-object v0, Lorg/codehaus/groovy/runtime/callsite/GroovySunClassLoader;->sunVM:Lorg/codehaus/groovy/reflection/SunClassLoader;

    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/reflection/SunClassLoader;->doesKnow(Ljava/lang/String;)Ljava/lang/Class;

    move-result-object v0

    if-eqz v0, :cond_1

    return-object v0

    .line 59
    :cond_1
    invoke-super {p0, p1}, Ljava/lang/ClassLoader;->loadClass(Ljava/lang/String;)Ljava/lang/Class;

    move-result-object p1

    return-object p1
.end method
