.class Lorg/codehaus/groovy/reflection/MixinInMetaClass$1;
.super Lorg/codehaus/groovy/runtime/metaclass/NewInstanceMetaMethod;
.source "MixinInMetaClass.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/codehaus/groovy/reflection/MixinInMetaClass;->staticMethod(Lgroovy/lang/MetaClass;Ljava/util/List;Lorg/codehaus/groovy/reflection/CachedMethod;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic val$self:Lgroovy/lang/MetaClass;


# direct methods
.method constructor <init>(Lorg/codehaus/groovy/reflection/CachedMethod;Lgroovy/lang/MetaClass;)V
    .locals 0

    .line 182
    iput-object p2, p0, Lorg/codehaus/groovy/reflection/MixinInMetaClass$1;->val$self:Lgroovy/lang/MetaClass;

    invoke-direct {p0, p1}, Lorg/codehaus/groovy/runtime/metaclass/NewInstanceMetaMethod;-><init>(Lorg/codehaus/groovy/reflection/CachedMethod;)V

    return-void
.end method


# virtual methods
.method public getDeclaringClass()Lorg/codehaus/groovy/reflection/CachedClass;
    .locals 1

    .line 184
    iget-object v0, p0, Lorg/codehaus/groovy/reflection/MixinInMetaClass$1;->val$self:Lgroovy/lang/MetaClass;

    invoke-interface {v0}, Lgroovy/lang/MetaClass;->getTheClass()Ljava/lang/Class;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/reflection/ReflectionCache;->getCachedClass(Ljava/lang/Class;)Lorg/codehaus/groovy/reflection/CachedClass;

    move-result-object v0

    return-object v0
.end method
