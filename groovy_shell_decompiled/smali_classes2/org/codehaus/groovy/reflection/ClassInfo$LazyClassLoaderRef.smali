.class Lorg/codehaus/groovy/reflection/ClassInfo$LazyClassLoaderRef;
.super Lorg/codehaus/groovy/util/LazyReference;
.source "ClassInfo.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/codehaus/groovy/reflection/ClassInfo;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0xa
    name = "LazyClassLoaderRef"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lorg/codehaus/groovy/util/LazyReference<",
        "Lorg/codehaus/groovy/reflection/ClassLoaderForClassArtifacts;",
        ">;"
    }
.end annotation


# static fields
.field private static final serialVersionUID:J = 0x16bf9832bdc01c41L


# instance fields
.field private final info:Lorg/codehaus/groovy/reflection/ClassInfo;


# direct methods
.method constructor <init>(Lorg/codehaus/groovy/util/ReferenceBundle;Lorg/codehaus/groovy/reflection/ClassInfo;)V
    .locals 0

    .line 446
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/util/LazyReference;-><init>(Lorg/codehaus/groovy/util/ReferenceBundle;)V

    .line 447
    iput-object p2, p0, Lorg/codehaus/groovy/reflection/ClassInfo$LazyClassLoaderRef;->info:Lorg/codehaus/groovy/reflection/ClassInfo;

    return-void
.end method


# virtual methods
.method public bridge synthetic initValue()Ljava/lang/Object;
    .locals 1

    .line 441
    invoke-virtual {p0}, Lorg/codehaus/groovy/reflection/ClassInfo$LazyClassLoaderRef;->initValue()Lorg/codehaus/groovy/reflection/ClassLoaderForClassArtifacts;

    move-result-object v0

    return-object v0
.end method

.method public initValue()Lorg/codehaus/groovy/reflection/ClassLoaderForClassArtifacts;
    .locals 1

    .line 451
    new-instance v0, Lorg/codehaus/groovy/reflection/ClassInfo$LazyClassLoaderRef$$ExternalSyntheticLambda0;

    invoke-direct {v0, p0}, Lorg/codehaus/groovy/reflection/ClassInfo$LazyClassLoaderRef$$ExternalSyntheticLambda0;-><init>(Lorg/codehaus/groovy/reflection/ClassInfo$LazyClassLoaderRef;)V

    invoke-static {v0}, Ljava/security/AccessController;->doPrivileged(Ljava/security/PrivilegedAction;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/reflection/ClassLoaderForClassArtifacts;

    return-object v0
.end method

.method public synthetic lambda$initValue$0$org-codehaus-groovy-reflection-ClassInfo$LazyClassLoaderRef()Lorg/codehaus/groovy/reflection/ClassLoaderForClassArtifacts;
    .locals 2

    .line 451
    new-instance v0, Lorg/codehaus/groovy/reflection/ClassLoaderForClassArtifacts;

    iget-object v1, p0, Lorg/codehaus/groovy/reflection/ClassInfo$LazyClassLoaderRef;->info:Lorg/codehaus/groovy/reflection/ClassInfo;

    invoke-static {v1}, Lorg/codehaus/groovy/reflection/ClassInfo;->access$200(Lorg/codehaus/groovy/reflection/ClassInfo;)Ljava/lang/ref/WeakReference;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/ref/WeakReference;->get()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/Class;

    invoke-direct {v0, v1}, Lorg/codehaus/groovy/reflection/ClassLoaderForClassArtifacts;-><init>(Ljava/lang/Class;)V

    return-object v0
.end method
