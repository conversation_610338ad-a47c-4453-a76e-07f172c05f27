.class public final synthetic Lorg/codehaus/groovy/reflection/ClassLoaderForClassArtifacts$$ExternalSyntheticLambda0;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/security/PrivilegedAction;


# instance fields
.field public final synthetic f$0:Lorg/codehaus/groovy/reflection/ClassLoaderForClassArtifacts;

.field public final synthetic f$1:Ljava/lang/String;

.field public final synthetic f$2:[B


# direct methods
.method public synthetic constructor <init>(Lorg/codehaus/groovy/reflection/ClassLoaderForClassArtifacts;Ljava/lang/String;[B)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/codehaus/groovy/reflection/ClassLoaderForClassArtifacts$$ExternalSyntheticLambda0;->f$0:Lorg/codehaus/groovy/reflection/ClassLoaderForClassArtifacts;

    iput-object p2, p0, Lorg/codehaus/groovy/reflection/ClassLoaderForClassArtifacts$$ExternalSyntheticLambda0;->f$1:Ljava/lang/String;

    iput-object p3, p0, Lorg/codehaus/groovy/reflection/ClassLoaderForClassArtifacts$$ExternalSyntheticLambda0;->f$2:[B

    return-void
.end method


# virtual methods
.method public final run()Ljava/lang/Object;
    .locals 3

    iget-object v0, p0, Lorg/codehaus/groovy/reflection/ClassLoaderForClassArtifacts$$ExternalSyntheticLambda0;->f$0:Lorg/codehaus/groovy/reflection/ClassLoaderForClassArtifacts;

    iget-object v1, p0, Lorg/codehaus/groovy/reflection/ClassLoaderForClassArtifacts$$ExternalSyntheticLambda0;->f$1:Ljava/lang/String;

    iget-object v2, p0, Lorg/codehaus/groovy/reflection/ClassLoaderForClassArtifacts$$ExternalSyntheticLambda0;->f$2:[B

    invoke-virtual {v0, v1, v2}, Lorg/codehaus/groovy/reflection/ClassLoaderForClassArtifacts;->lambda$defineClassAndGetConstructor$0$org-codehaus-groovy-reflection-ClassLoaderForClassArtifacts(Ljava/lang/String;[B)Ljava/lang/Class;

    move-result-object v0

    return-object v0
.end method
