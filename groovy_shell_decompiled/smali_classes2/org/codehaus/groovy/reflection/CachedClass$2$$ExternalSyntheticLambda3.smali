.class public final synthetic Lorg/codehaus/groovy/reflection/CachedClass$2$$ExternalSyntheticLambda3;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Predicate;


# static fields
.field public static final synthetic INSTANCE:Lorg/codehaus/groovy/reflection/CachedClass$2$$ExternalSyntheticLambda3;


# direct methods
.method static synthetic constructor <clinit>()V
    .locals 1

    new-instance v0, Lorg/codehaus/groovy/reflection/CachedClass$2$$ExternalSyntheticLambda3;

    invoke-direct {v0}, Lorg/codehaus/groovy/reflection/CachedClass$2$$ExternalSyntheticLambda3;-><init>()V

    sput-object v0, Lorg/codehaus/groovy/reflection/CachedClass$2$$ExternalSyntheticLambda3;->INSTANCE:Lorg/codehaus/groovy/reflection/CachedClass$2$$ExternalSyntheticLambda3;

    return-void
.end method

.method private synthetic constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final test(Ljava/lang/Object;)Z
    .locals 0

    check-cast p1, Ljava/lang/reflect/Constructor;

    invoke-static {p1}, Lorg/codehaus/groovy/reflection/CachedClass$2;->lambda$initValue$0(Ljava/lang/reflect/Constructor;)Z

    move-result p1

    return p1
.end method
