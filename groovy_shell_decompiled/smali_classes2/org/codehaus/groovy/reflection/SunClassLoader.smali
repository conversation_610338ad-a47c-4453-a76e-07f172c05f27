.class public Lorg/codehaus/groovy/reflection/SunClassLoader;
.super Ljava/lang/ClassLoader;
.source "SunClassLoader.java"

# interfaces
.implements Lgroovyjarjarasm/asm/Opcodes;


# static fields
.field protected static final sunVM:Lorg/codehaus/groovy/reflection/SunClassLoader;


# instance fields
.field protected final knownClasses:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/Class;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 44
    :try_start_0
    sget-object v0, Lorg/codehaus/groovy/reflection/SunClassLoader$$ExternalSyntheticLambda0;->INSTANCE:Lorg/codehaus/groovy/reflection/SunClassLoader$$ExternalSyntheticLambda0;

    invoke-static {v0}, Ljava/security/AccessController;->doPrivileged(Ljava/security/PrivilegedAction;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/reflection/SunClassLoader;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_0

    :catchall_0
    const/4 v0, 0x0

    .line 54
    :goto_0
    sput-object v0, Lorg/codehaus/groovy/reflection/SunClassLoader;->sunVM:Lorg/codehaus/groovy/reflection/SunClassLoader;

    return-void
.end method

.method protected constructor <init>()V
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Throwable;
        }
    .end annotation

    .line 58
    const-class v0, Lorg/codehaus/groovy/reflection/SunClassLoader;

    invoke-virtual {v0}, Ljava/lang/Class;->getClassLoader()Ljava/lang/ClassLoader;

    move-result-object v0

    invoke-direct {p0, v0}, Ljava/lang/ClassLoader;-><init>(Ljava/lang/ClassLoader;)V

    .line 37
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    iput-object v0, p0, Lorg/codehaus/groovy/reflection/SunClassLoader;->knownClasses:Ljava/util/Map;

    .line 60
    invoke-static {}, Ljava/lang/ClassLoader;->getSystemClassLoader()Ljava/lang/ClassLoader;

    move-result-object v1

    const-string v2, "sun.reflect.MagicAccessorImpl"

    invoke-virtual {v1, v2}, Ljava/lang/ClassLoader;->loadClass(Ljava/lang/String;)Ljava/lang/Class;

    move-result-object v1

    .line 61
    invoke-interface {v0, v2, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 62
    invoke-direct {p0}, Lorg/codehaus/groovy/reflection/SunClassLoader;->loadMagic()V

    return-void
.end method

.method static synthetic lambda$static$0()Lorg/codehaus/groovy/reflection/SunClassLoader;
    .locals 1

    .line 46
    :try_start_0
    new-instance v0, Lorg/codehaus/groovy/reflection/SunClassLoader;

    invoke-direct {v0}, Lorg/codehaus/groovy/reflection/SunClassLoader;-><init>()V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    return-object v0

    :catchall_0
    const/4 v0, 0x0

    return-object v0
.end method

.method private loadMagic()V
    .locals 14

    .line 66
    new-instance v7, Lgroovyjarjarasm/asm/ClassWriter;

    const/4 v0, 0x1

    invoke-direct {v7, v0}, Lgroovyjarjarasm/asm/ClassWriter;-><init>(I)V

    const/16 v1, 0x30

    const/4 v2, 0x1

    const-string v3, "sun/reflect/GroovyMagic"

    const/4 v4, 0x0

    const-string v5, "sun/reflect/MagicAccessorImpl"

    const/4 v6, 0x0

    move-object v0, v7

    .line 67
    invoke-virtual/range {v0 .. v6}, Lgroovyjarjarasm/asm/ClassWriter;->visit(IILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)V

    const/4 v1, 0x1

    const-string v2, "<init>"

    const-string v3, "()V"

    const/4 v5, 0x0

    .line 68
    invoke-virtual/range {v0 .. v5}, Lgroovyjarjarasm/asm/ClassWriter;->visitMethod(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lgroovyjarjarasm/asm/MethodVisitor;

    move-result-object v0

    .line 69
    invoke-virtual {v0}, Lgroovyjarjarasm/asm/MethodVisitor;->visitCode()V

    const/16 v1, 0x19

    const/4 v2, 0x0

    .line 70
    invoke-virtual {v0, v1, v2}, Lgroovyjarjarasm/asm/MethodVisitor;->visitVarInsn(II)V

    const/16 v9, 0xb7

    const-string v10, "sun/reflect/MagicAccessorImpl"

    const-string v11, "<init>"

    const-string v12, "()V"

    const/4 v13, 0x0

    move-object v8, v0

    .line 71
    invoke-virtual/range {v8 .. v13}, Lgroovyjarjarasm/asm/MethodVisitor;->visitMethodInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V

    const/16 v1, 0xb1

    .line 72
    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    .line 73
    invoke-virtual {v0, v2, v2}, Lgroovyjarjarasm/asm/MethodVisitor;->visitMaxs(II)V

    .line 74
    invoke-virtual {v0}, Lgroovyjarjarasm/asm/MethodVisitor;->visitEnd()V

    .line 75
    invoke-virtual {v7}, Lgroovyjarjarasm/asm/ClassWriter;->visitEnd()V

    .line 77
    invoke-virtual {v7}, Lgroovyjarjarasm/asm/ClassWriter;->toByteArray()[B

    move-result-object v0

    const-string v1, "sun.reflect.GroovyMagic"

    invoke-virtual {p0, v0, v1}, Lorg/codehaus/groovy/reflection/SunClassLoader;->define([BLjava/lang/String;)V

    return-void
.end method

.method protected static resName(Ljava/lang/String;)Ljava/lang/String;
    .locals 3

    .line 90
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const/16 v1, 0x2e

    const/16 v2, 0x2f

    invoke-virtual {p0, v1, v2}, Ljava/lang/String;->replace(CC)Ljava/lang/String;

    move-result-object p0

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    const-string v0, ".class"

    invoke-virtual {p0, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method protected define([BLjava/lang/String;)V
    .locals 3

    .line 94
    iget-object v0, p0, Lorg/codehaus/groovy/reflection/SunClassLoader;->knownClasses:Ljava/util/Map;

    array-length v1, p1

    const/4 v2, 0x0

    invoke-virtual {p0, p2, p1, v2, v1}, Lorg/codehaus/groovy/reflection/SunClassLoader;->defineClass(Ljava/lang/String;[BII)Ljava/lang/Class;

    move-result-object p1

    invoke-interface {v0, p2, p1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method public doesKnow(Ljava/lang/String;)Ljava/lang/Class;
    .locals 1

    .line 111
    iget-object v0, p0, Lorg/codehaus/groovy/reflection/SunClassLoader;->knownClasses:Ljava/util/Map;

    invoke-interface {v0, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/Class;

    return-object p1
.end method

.method protected declared-synchronized loadClass(Ljava/lang/String;Z)Ljava/lang/Class;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/ClassNotFoundException;
        }
    .end annotation

    monitor-enter p0

    .line 98
    :try_start_0
    iget-object v0, p0, Lorg/codehaus/groovy/reflection/SunClassLoader;->knownClasses:Ljava/util/Map;

    invoke-interface {v0, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/Class;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-eqz v0, :cond_0

    .line 100
    monitor-exit p0

    return-object v0

    .line 103
    :cond_0
    :try_start_1
    invoke-super {p0, p1, p2}, Ljava/lang/ClassLoader;->loadClass(Ljava/lang/String;Z)Ljava/lang/Class;

    move-result-object p1
    :try_end_1
    .catch Ljava/lang/ClassNotFoundException; {:try_start_1 .. :try_end_1} :catch_0
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    monitor-exit p0

    return-object p1

    .line 105
    :catch_0
    :try_start_2
    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/Class;->getClassLoader()Ljava/lang/ClassLoader;

    move-result-object p2

    invoke-virtual {p2, p1}, Ljava/lang/ClassLoader;->loadClass(Ljava/lang/String;)Ljava/lang/Class;

    move-result-object p1
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    monitor-exit p0

    return-object p1

    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method

.method protected loadFromRes(Ljava/lang/String;)V
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 81
    const-class v0, Lorg/codehaus/groovy/reflection/SunClassLoader;

    invoke-virtual {v0}, Ljava/lang/Class;->getClassLoader()Ljava/lang/ClassLoader;

    move-result-object v0

    invoke-static {p1}, Lorg/codehaus/groovy/reflection/SunClassLoader;->resName(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/ClassLoader;->getResourceAsStream(Ljava/lang/String;)Ljava/io/InputStream;

    move-result-object v0

    .line 82
    :try_start_0
    new-instance v1, Lgroovyjarjarasm/asm/ClassReader;

    invoke-direct {v1, v0}, Lgroovyjarjarasm/asm/ClassReader;-><init>(Ljava/io/InputStream;)V

    .line 83
    new-instance v2, Lgroovyjarjarasm/asm/ClassWriter;

    const/4 v3, 0x1

    invoke-direct {v2, v3}, Lgroovyjarjarasm/asm/ClassWriter;-><init>(I)V

    const/4 v3, 0x2

    .line 84
    invoke-virtual {v1, v2, v3}, Lgroovyjarjarasm/asm/ClassReader;->accept(Lgroovyjarjarasm/asm/ClassVisitor;I)V

    .line 85
    invoke-virtual {v2}, Lgroovyjarjarasm/asm/ClassWriter;->toByteArray()[B

    move-result-object v1

    invoke-virtual {p0, v1, p1}, Lorg/codehaus/groovy/reflection/SunClassLoader;->define([BLjava/lang/String;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-eqz v0, :cond_0

    .line 86
    invoke-virtual {v0}, Ljava/io/InputStream;->close()V

    :cond_0
    return-void

    :catchall_0
    move-exception p1

    if-eqz v0, :cond_1

    .line 81
    :try_start_1
    invoke-virtual {v0}, Ljava/io/InputStream;->close()V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_1

    goto :goto_0

    :catchall_1
    move-exception v0

    invoke-virtual {p1, v0}, Ljava/lang/Throwable;->addSuppressed(Ljava/lang/Throwable;)V

    :cond_1
    :goto_0
    throw p1
.end method
