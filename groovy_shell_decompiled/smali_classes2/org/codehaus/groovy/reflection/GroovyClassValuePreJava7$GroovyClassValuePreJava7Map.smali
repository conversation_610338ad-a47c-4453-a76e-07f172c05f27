.class Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7$GroovyClassValuePreJava7Map;
.super Lorg/codehaus/groovy/util/ManagedConcurrentMap;
.source "GroovyClassValuePreJava7.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x2
    name = "GroovyClassValuePreJava7Map"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lorg/codehaus/groovy/util/ManagedConcurrentMap<",
        "Ljava/lang/Class<",
        "*>;TT;>;"
    }
.end annotation


# instance fields
.field final synthetic this$0:Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7;


# direct methods
.method public constructor <init>(Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7;)V
    .locals 0

    .line 72
    iput-object p1, p0, Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7$GroovyClassValuePreJava7Map;->this$0:Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7;

    .line 73
    invoke-static {}, Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7;->access$000()Lorg/codehaus/groovy/util/ReferenceBundle;

    move-result-object p1

    invoke-direct {p0, p1}, Lorg/codehaus/groovy/util/ManagedConcurrentMap;-><init>(Lorg/codehaus/groovy/util/ReferenceBundle;)V

    return-void
.end method


# virtual methods
.method protected createSegment(Ljava/lang/Object;I)Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7$GroovyClassValuePreJava7Segment;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "I)",
            "Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7<",
            "TT;>.GroovyClassValuePreJava7Segment;"
        }
    .end annotation

    .line 78
    check-cast p1, Lorg/codehaus/groovy/util/ReferenceBundle;

    if-eqz p1, :cond_0

    .line 80
    new-instance v0, Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7$GroovyClassValuePreJava7Segment;

    iget-object v1, p0, Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7$GroovyClassValuePreJava7Map;->this$0:Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7;

    invoke-direct {v0, v1, p1, p2}, Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7$GroovyClassValuePreJava7Segment;-><init>(Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7;Lorg/codehaus/groovy/util/ReferenceBundle;I)V

    return-object v0

    .line 79
    :cond_0
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string p2, "bundle must not be null "

    invoke-direct {p1, p2}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method protected bridge synthetic createSegment(Ljava/lang/Object;I)Lorg/codehaus/groovy/util/AbstractConcurrentMapBase$Segment;
    .locals 0

    .line 70
    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7$GroovyClassValuePreJava7Map;->createSegment(Ljava/lang/Object;I)Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7$GroovyClassValuePreJava7Segment;

    move-result-object p1

    return-object p1
.end method

.method protected bridge synthetic createSegment(Ljava/lang/Object;I)Lorg/codehaus/groovy/util/ManagedConcurrentMap$Segment;
    .locals 0

    .line 70
    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7$GroovyClassValuePreJava7Map;->createSegment(Ljava/lang/Object;I)Lorg/codehaus/groovy/reflection/GroovyClassValuePreJava7$GroovyClassValuePreJava7Segment;

    move-result-object p1

    return-object p1
.end method
