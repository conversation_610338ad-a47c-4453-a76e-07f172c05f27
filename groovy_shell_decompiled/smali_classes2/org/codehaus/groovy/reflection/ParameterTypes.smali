.class public Lorg/codehaus/groovy/reflection/ParameterTypes;
.super Ljava/lang/Object;
.source "ParameterTypes.java"


# static fields
.field private static final NO_PARAMETERS:[Ljava/lang/Class;


# instance fields
.field protected isVargsMethod:Z

.field protected volatile nativeParamTypes:[Ljava/lang/Class;

.field protected volatile parameterTypes:[Lorg/codehaus/groovy/reflection/CachedClass;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    const/4 v0, 0x0

    new-array v0, v0, [Ljava/lang/Class;

    .line 29
    sput-object v0, Lorg/codehaus/groovy/reflection/ParameterTypes;->NO_PARAMETERS:[Ljava/lang/Class;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 36
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public constructor <init>([Ljava/lang/Class;)V
    .locals 0

    .line 39
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 40
    iput-object p1, p0, Lorg/codehaus/groovy/reflection/ParameterTypes;->nativeParamTypes:[Ljava/lang/Class;

    return-void
.end method

.method public constructor <init>([Ljava/lang/String;)V
    .locals 3

    .line 43
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 44
    array-length v0, p1

    new-array v0, v0, [Ljava/lang/Class;

    iput-object v0, p0, Lorg/codehaus/groovy/reflection/ParameterTypes;->nativeParamTypes:[Ljava/lang/Class;

    const/4 v0, 0x0

    .line 45
    :goto_0
    array-length v1, p1

    if-eq v0, v1, :cond_0

    .line 47
    :try_start_0
    iget-object v1, p0, Lorg/codehaus/groovy/reflection/ParameterTypes;->nativeParamTypes:[Ljava/lang/Class;

    aget-object v2, p1, v0

    invoke-static {v2}, Ljava/lang/Class;->forName(Ljava/lang/String;)Ljava/lang/Class;

    move-result-object v2

    aput-object v2, v1, v0
    :try_end_0
    .catch Ljava/lang/ClassNotFoundException; {:try_start_0 .. :try_end_0} :catch_0

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :catch_0
    move-exception p1

    .line 49
    new-instance v0, Ljava/lang/NoClassDefFoundError;

    invoke-direct {v0}, Ljava/lang/NoClassDefFoundError;-><init>()V

    .line 50
    invoke-virtual {v0, p1}, Ljava/lang/NoClassDefFoundError;->initCause(Ljava/lang/Throwable;)Ljava/lang/Throwable;

    .line 51
    throw v0

    :cond_0
    return-void
.end method

.method public constructor <init>([Lorg/codehaus/groovy/reflection/CachedClass;)V
    .locals 0

    .line 56
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 57
    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/reflection/ParameterTypes;->setParametersTypes([Lorg/codehaus/groovy/reflection/CachedClass;)V

    return-void
.end method

.method private static fitToVargs([Ljava/lang/Object;[Lorg/codehaus/groovy/reflection/CachedClass;)[Ljava/lang/Object;
    .locals 5

    .line 188
    array-length v0, p1

    add-int/lit8 v0, v0, -0x1

    aget-object v0, p1, v0

    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/CachedClass;->getTheClass()Ljava/lang/Class;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/Class;->getComponentType()Ljava/lang/Class;

    move-result-object v0

    .line 189
    invoke-static {v0}, Lorg/codehaus/groovy/reflection/ReflectionCache;->autoboxType(Ljava/lang/Class;)Ljava/lang/Class;

    move-result-object v0

    .line 190
    invoke-virtual {p0}, [Ljava/lang/Object;->clone()Ljava/lang/Object;

    move-result-object p0

    check-cast p0, [Ljava/lang/Object;

    .line 191
    invoke-static {p0}, Lorg/codehaus/groovy/runtime/MetaClassHelper;->unwrap([Ljava/lang/Object;)V

    .line 193
    array-length v1, p0

    array-length v2, p1

    add-int/lit8 v2, v2, -0x1

    const/4 v3, 0x0

    if-ne v1, v2, :cond_0

    .line 195
    array-length p1, p1

    new-array v1, p1, [Ljava/lang/Object;

    .line 196
    array-length v2, p0

    invoke-static {p0, v3, v1, v3, v2}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    .line 197
    invoke-static {v0, v3}, Ljava/lang/reflect/Array;->newInstance(Ljava/lang/Class;I)Ljava/lang/Object;

    move-result-object p0

    add-int/lit8 p1, p1, -0x1

    .line 198
    aput-object p0, v1, p1

    return-object v1

    .line 200
    :cond_0
    array-length v1, p0

    array-length v2, p1

    if-ne v1, v2, :cond_2

    .line 204
    array-length v1, p0

    add-int/lit8 v1, v1, -0x1

    aget-object v1, p0, v1

    if-eqz v1, :cond_1

    .line 205
    invoke-virtual {v1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/Class;->isArray()Z

    move-result v1

    if-nez v1, :cond_1

    .line 207
    array-length v1, p1

    add-int/lit8 v1, v1, -0x1

    invoke-static {p0, v1, v0}, Lorg/codehaus/groovy/reflection/ParameterTypes;->makeCommonArray([Ljava/lang/Object;ILjava/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    .line 208
    array-length v1, p1

    new-array v2, v1, [Ljava/lang/Object;

    .line 209
    array-length p1, p1

    add-int/lit8 p1, p1, -0x1

    invoke-static {p0, v3, v2, v3, p1}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    add-int/lit8 v1, v1, -0x1

    .line 210
    aput-object v0, v2, v1

    return-object v2

    :cond_1
    return-object p0

    .line 216
    :cond_2
    array-length v1, p0

    array-length v2, p1

    if-le v1, v2, :cond_3

    .line 219
    array-length v1, p1

    new-array v2, v1, [Ljava/lang/Object;

    .line 221
    array-length v4, p1

    add-int/lit8 v4, v4, -0x1

    invoke-static {p0, v3, v2, v3, v4}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    .line 223
    array-length p1, p1

    add-int/lit8 p1, p1, -0x1

    invoke-static {p0, p1, v0}, Lorg/codehaus/groovy/reflection/ParameterTypes;->makeCommonArray([Ljava/lang/Object;ILjava/lang/Class;)Ljava/lang/Object;

    move-result-object p0

    add-int/lit8 v1, v1, -0x1

    .line 224
    aput-object p0, v2, v1

    return-object v2

    .line 227
    :cond_3
    new-instance p0, Lorg/codehaus/groovy/GroovyBugError;

    const-string p1, "trying to call a vargs method without enough arguments"

    invoke-direct {p0, p1}, Lorg/codehaus/groovy/GroovyBugError;-><init>(Ljava/lang/String;)V

    throw p0
.end method

.method private static getArgClass(Ljava/lang/Object;)Ljava/lang/Class;
    .locals 1

    if-nez p0, :cond_0

    const/4 p0, 0x0

    goto :goto_0

    .line 378
    :cond_0
    instance-of v0, p0, Lorg/codehaus/groovy/runtime/wrappers/Wrapper;

    if-eqz v0, :cond_1

    .line 379
    check-cast p0, Lorg/codehaus/groovy/runtime/wrappers/Wrapper;

    invoke-virtual {p0}, Lorg/codehaus/groovy/runtime/wrappers/Wrapper;->getType()Ljava/lang/Class;

    move-result-object p0

    goto :goto_0

    .line 381
    :cond_1
    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object p0

    :goto_0
    return-object p0
.end method

.method private declared-synchronized getNativeParameterTypes0()V
    .locals 3

    monitor-enter p0

    .line 100
    :try_start_0
    iget-object v0, p0, Lorg/codehaus/groovy/reflection/ParameterTypes;->nativeParamTypes:[Ljava/lang/Class;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-eqz v0, :cond_0

    .line 101
    monitor-exit p0

    return-void

    .line 104
    :cond_0
    :try_start_1
    iget-object v0, p0, Lorg/codehaus/groovy/reflection/ParameterTypes;->parameterTypes:[Lorg/codehaus/groovy/reflection/CachedClass;

    if-eqz v0, :cond_1

    .line 105
    iget-object v0, p0, Lorg/codehaus/groovy/reflection/ParameterTypes;->parameterTypes:[Lorg/codehaus/groovy/reflection/CachedClass;

    array-length v0, v0

    new-array v0, v0, [Ljava/lang/Class;

    const/4 v1, 0x0

    .line 106
    :goto_0
    iget-object v2, p0, Lorg/codehaus/groovy/reflection/ParameterTypes;->parameterTypes:[Lorg/codehaus/groovy/reflection/CachedClass;

    array-length v2, v2

    if-eq v1, v2, :cond_2

    .line 107
    iget-object v2, p0, Lorg/codehaus/groovy/reflection/ParameterTypes;->parameterTypes:[Lorg/codehaus/groovy/reflection/CachedClass;

    aget-object v2, v2, v1

    invoke-virtual {v2}, Lorg/codehaus/groovy/reflection/CachedClass;->getTheClass()Ljava/lang/Class;

    move-result-object v2

    aput-object v2, v0, v1

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 110
    :cond_1
    invoke-virtual {p0}, Lorg/codehaus/groovy/reflection/ParameterTypes;->getPT()[Ljava/lang/Class;

    move-result-object v0

    .line 111
    :cond_2
    iput-object v0, p0, Lorg/codehaus/groovy/reflection/ParameterTypes;->nativeParamTypes:[Ljava/lang/Class;
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 112
    monitor-exit p0

    return-void

    :catchall_0
    move-exception v0

    monitor-exit p0

    throw v0
.end method

.method private declared-synchronized getParametersTypes0()V
    .locals 4

    monitor-enter p0

    .line 74
    :try_start_0
    iget-object v0, p0, Lorg/codehaus/groovy/reflection/ParameterTypes;->parameterTypes:[Lorg/codehaus/groovy/reflection/CachedClass;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-eqz v0, :cond_0

    .line 75
    monitor-exit p0

    return-void

    .line 77
    :cond_0
    :try_start_1
    iget-object v0, p0, Lorg/codehaus/groovy/reflection/ParameterTypes;->nativeParamTypes:[Ljava/lang/Class;

    if-nez v0, :cond_1

    invoke-virtual {p0}, Lorg/codehaus/groovy/reflection/ParameterTypes;->getPT()[Ljava/lang/Class;

    move-result-object v0

    goto :goto_0

    :cond_1
    iget-object v0, p0, Lorg/codehaus/groovy/reflection/ParameterTypes;->nativeParamTypes:[Ljava/lang/Class;

    .line 78
    :goto_0
    array-length v1, v0

    if-nez v1, :cond_2

    .line 79
    sget-object v0, Lorg/codehaus/groovy/reflection/ParameterTypes;->NO_PARAMETERS:[Ljava/lang/Class;

    iput-object v0, p0, Lorg/codehaus/groovy/reflection/ParameterTypes;->nativeParamTypes:[Ljava/lang/Class;

    .line 80
    sget-object v0, Lorg/codehaus/groovy/reflection/CachedClass;->EMPTY_ARRAY:[Lorg/codehaus/groovy/reflection/CachedClass;

    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/reflection/ParameterTypes;->setParametersTypes([Lorg/codehaus/groovy/reflection/CachedClass;)V

    goto :goto_2

    .line 83
    :cond_2
    array-length v1, v0

    new-array v1, v1, [Lorg/codehaus/groovy/reflection/CachedClass;

    const/4 v2, 0x0

    .line 84
    :goto_1
    array-length v3, v0

    if-eq v2, v3, :cond_3

    .line 85
    aget-object v3, v0, v2

    invoke-static {v3}, Lorg/codehaus/groovy/reflection/ReflectionCache;->getCachedClass(Ljava/lang/Class;)Lorg/codehaus/groovy/reflection/CachedClass;

    move-result-object v3

    aput-object v3, v1, v2

    add-int/lit8 v2, v2, 0x1

    goto :goto_1

    .line 87
    :cond_3
    iput-object v0, p0, Lorg/codehaus/groovy/reflection/ParameterTypes;->nativeParamTypes:[Ljava/lang/Class;

    .line 88
    invoke-virtual {p0, v1}, Lorg/codehaus/groovy/reflection/ParameterTypes;->setParametersTypes([Lorg/codehaus/groovy/reflection/CachedClass;)V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 90
    :goto_2
    monitor-exit p0

    return-void

    :catchall_0
    move-exception v0

    monitor-exit p0

    throw v0
.end method

.method private static isValidExactMethod([Ljava/lang/Class;[Lorg/codehaus/groovy/reflection/CachedClass;)Z
    .locals 5

    .line 259
    array-length v0, p1

    const/4 v1, 0x0

    move v2, v1

    :goto_0
    if-ge v2, v0, :cond_1

    .line 261
    aget-object v3, p1, v2

    aget-object v4, p0, v2

    invoke-virtual {v3, v4}, Lorg/codehaus/groovy/reflection/CachedClass;->isAssignableFrom(Ljava/lang/Class;)Z

    move-result v3

    if-nez v3, :cond_0

    return v1

    :cond_0
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_1
    const/4 p0, 0x1

    return p0
.end method

.method private static isValidVarargsMethod([Ljava/lang/Class;I[Lorg/codehaus/groovy/reflection/CachedClass;I)Z
    .locals 4

    const/4 v0, 0x0

    move v1, v0

    :goto_0
    if-ge v1, p3, :cond_1

    .line 307
    aget-object v2, p2, v1

    aget-object v3, p0, v1

    invoke-virtual {v2, v3}, Lorg/codehaus/groovy/reflection/CachedClass;->isAssignableFrom(Ljava/lang/Class;)Z

    move-result v2

    if-eqz v2, :cond_0

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    return v0

    .line 312
    :cond_1
    aget-object v1, p2, p3

    .line 313
    invoke-virtual {v1}, Lorg/codehaus/groovy/reflection/CachedClass;->getTheClass()Ljava/lang/Class;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/Class;->getComponentType()Ljava/lang/Class;

    move-result-object v2

    .line 314
    array-length p2, p2

    const/4 v3, 0x1

    if-ne p1, p2, :cond_3

    aget-object p2, p0, p3

    .line 315
    invoke-virtual {v1, p2}, Lorg/codehaus/groovy/reflection/CachedClass;->isAssignableFrom(Ljava/lang/Class;)Z

    move-result p2

    if-nez p2, :cond_2

    aget-object p2, p0, p3

    .line 316
    invoke-static {v2, p2}, Lorg/codehaus/groovy/reflection/ParameterTypes;->testComponentAssignable(Ljava/lang/Class;Ljava/lang/Class;)Z

    move-result p2

    if-eqz p2, :cond_3

    :cond_2
    return v3

    :cond_3
    :goto_1
    if-ge p3, p1, :cond_5

    .line 322
    aget-object p2, p0, p3

    invoke-static {v2, p2}, Lorg/codehaus/groovy/runtime/MetaClassHelper;->isAssignableFrom(Ljava/lang/Class;Ljava/lang/Class;)Z

    move-result p2

    if-eqz p2, :cond_4

    add-int/lit8 p3, p3, 0x1

    goto :goto_1

    :cond_4
    return v0

    :cond_5
    return v3
.end method

.method private static makeCommonArray([Ljava/lang/Object;ILjava/lang/Class;)Ljava/lang/Object;
    .locals 4

    .line 232
    array-length v0, p0

    sub-int/2addr v0, p1

    invoke-static {p2, v0}, Ljava/lang/reflect/Array;->newInstance(Ljava/lang/Class;I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Ljava/lang/Object;

    move v1, p1

    .line 233
    :goto_0
    array-length v2, p0

    if-ge v1, v2, :cond_0

    .line 234
    aget-object v2, p0, v1

    .line 235
    invoke-static {v2, p2}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v2

    sub-int v3, v1, p1

    .line 236
    aput-object v2, v0, v3

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    return-object v0
.end method

.method private static testComponentAssignable(Ljava/lang/Class;Ljava/lang/Class;)Z
    .locals 0

    .line 299
    invoke-virtual {p1}, Ljava/lang/Class;->getComponentType()Ljava/lang/Class;

    move-result-object p1

    if-nez p1, :cond_0

    const/4 p0, 0x0

    return p0

    .line 301
    :cond_0
    invoke-static {p0, p1}, Lorg/codehaus/groovy/runtime/MetaClassHelper;->isAssignableFrom(Ljava/lang/Class;Ljava/lang/Class;)Z

    move-result p0

    return p0
.end method


# virtual methods
.method public final coerceArgumentsToClasses([Ljava/lang/Object;)[Ljava/lang/Object;
    .locals 5

    .line 145
    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/reflection/ParameterTypes;->correctArguments([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object p1

    .line 147
    iget-object v0, p0, Lorg/codehaus/groovy/reflection/ParameterTypes;->parameterTypes:[Lorg/codehaus/groovy/reflection/CachedClass;

    .line 148
    array-length v1, p1

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v1, :cond_1

    .line 150
    aget-object v3, p1, v2

    if-eqz v3, :cond_0

    .line 152
    aget-object v4, v0, v2

    invoke-virtual {v4, v3}, Lorg/codehaus/groovy/reflection/CachedClass;->coerceArgument(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    aput-object v3, p1, v2

    :cond_0
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_1
    return-object p1
.end method

.method public correctArguments([Ljava/lang/Object;)[Ljava/lang/Object;
    .locals 3

    if-nez p1, :cond_0

    .line 161
    sget-object p1, Lorg/codehaus/groovy/runtime/MetaClassHelper;->EMPTY_ARRAY:[Ljava/lang/Object;

    return-object p1

    .line 164
    :cond_0
    invoke-virtual {p0}, Lorg/codehaus/groovy/reflection/ParameterTypes;->getParameterTypes()[Lorg/codehaus/groovy/reflection/CachedClass;

    move-result-object v0

    .line 165
    array-length v1, v0

    const/4 v2, 0x1

    if-ne v1, v2, :cond_2

    array-length v1, p1

    if-nez v1, :cond_2

    .line 166
    iget-boolean p1, p0, Lorg/codehaus/groovy/reflection/ParameterTypes;->isVargsMethod:Z

    if-eqz p1, :cond_1

    new-array p1, v2, [Ljava/lang/Object;

    const/4 v1, 0x0

    .line 167
    aget-object v0, v0, v1

    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/CachedClass;->getTheClass()Ljava/lang/Class;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/Class;->getComponentType()Ljava/lang/Class;

    move-result-object v0

    invoke-static {v0, v1}, Ljava/lang/reflect/Array;->newInstance(Ljava/lang/Class;I)Ljava/lang/Object;

    move-result-object v0

    aput-object v0, p1, v1

    return-object p1

    .line 169
    :cond_1
    sget-object p1, Lorg/codehaus/groovy/runtime/MetaClassHelper;->ARRAY_WITH_NULL:[Ljava/lang/Object;

    return-object p1

    .line 172
    :cond_2
    iget-boolean v1, p0, Lorg/codehaus/groovy/reflection/ParameterTypes;->isVargsMethod:Z

    if-eqz v1, :cond_3

    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/reflection/ParameterTypes;->isVargsMethod([Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_3

    .line 173
    invoke-static {p1, v0}, Lorg/codehaus/groovy/reflection/ParameterTypes;->fitToVargs([Ljava/lang/Object;[Lorg/codehaus/groovy/reflection/CachedClass;)[Ljava/lang/Object;

    move-result-object p1

    :cond_3
    return-object p1
.end method

.method public getNativeParameterTypes()[Ljava/lang/Class;
    .locals 1

    .line 93
    iget-object v0, p0, Lorg/codehaus/groovy/reflection/ParameterTypes;->nativeParamTypes:[Ljava/lang/Class;

    if-nez v0, :cond_0

    .line 94
    invoke-direct {p0}, Lorg/codehaus/groovy/reflection/ParameterTypes;->getNativeParameterTypes0()V

    .line 96
    :cond_0
    iget-object v0, p0, Lorg/codehaus/groovy/reflection/ParameterTypes;->nativeParamTypes:[Ljava/lang/Class;

    return-object v0
.end method

.method protected getPT()[Ljava/lang/Class;
    .locals 2

    .line 115
    new-instance v0, Ljava/lang/UnsupportedOperationException;

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-direct {v0, v1}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public getParameterTypes()[Lorg/codehaus/groovy/reflection/CachedClass;
    .locals 1

    .line 66
    iget-object v0, p0, Lorg/codehaus/groovy/reflection/ParameterTypes;->parameterTypes:[Lorg/codehaus/groovy/reflection/CachedClass;

    if-nez v0, :cond_0

    .line 67
    invoke-direct {p0}, Lorg/codehaus/groovy/reflection/ParameterTypes;->getParametersTypes0()V

    .line 70
    :cond_0
    iget-object v0, p0, Lorg/codehaus/groovy/reflection/ParameterTypes;->parameterTypes:[Lorg/codehaus/groovy/reflection/CachedClass;

    return-object v0
.end method

.method public isValidExactMethod([Ljava/lang/Class;)Z
    .locals 5

    .line 285
    invoke-direct {p0}, Lorg/codehaus/groovy/reflection/ParameterTypes;->getParametersTypes0()V

    .line 286
    array-length v0, p1

    .line 287
    iget-object v1, p0, Lorg/codehaus/groovy/reflection/ParameterTypes;->parameterTypes:[Lorg/codehaus/groovy/reflection/CachedClass;

    array-length v1, v1

    const/4 v2, 0x0

    if-eq v0, v1, :cond_0

    return v2

    :cond_0
    move v1, v2

    :goto_0
    if-ge v1, v0, :cond_2

    .line 291
    aget-object v3, p1, v1

    if-eqz v3, :cond_1

    iget-object v3, p0, Lorg/codehaus/groovy/reflection/ParameterTypes;->parameterTypes:[Lorg/codehaus/groovy/reflection/CachedClass;

    aget-object v3, v3, v1

    aget-object v4, p1, v1

    invoke-virtual {v3, v4}, Lorg/codehaus/groovy/reflection/CachedClass;->isAssignableFrom(Ljava/lang/Class;)Z

    move-result v3

    if-nez v3, :cond_1

    return v2

    :cond_1
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_2
    const/4 p1, 0x1

    return p1
.end method

.method public isValidExactMethod([Ljava/lang/Object;)Z
    .locals 5

    .line 270
    invoke-direct {p0}, Lorg/codehaus/groovy/reflection/ParameterTypes;->getParametersTypes0()V

    .line 271
    array-length v0, p1

    .line 272
    iget-object v1, p0, Lorg/codehaus/groovy/reflection/ParameterTypes;->parameterTypes:[Lorg/codehaus/groovy/reflection/CachedClass;

    array-length v1, v1

    const/4 v2, 0x0

    if-eq v0, v1, :cond_0

    return v2

    :cond_0
    move v1, v2

    :goto_0
    if-ge v1, v0, :cond_2

    .line 276
    aget-object v3, p1, v1

    if-eqz v3, :cond_1

    iget-object v3, p0, Lorg/codehaus/groovy/reflection/ParameterTypes;->parameterTypes:[Lorg/codehaus/groovy/reflection/CachedClass;

    aget-object v3, v3, v1

    aget-object v4, p1, v1

    invoke-virtual {v4}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v4

    invoke-virtual {v3, v4}, Lorg/codehaus/groovy/reflection/CachedClass;->isAssignableFrom(Ljava/lang/Class;)Z

    move-result v3

    if-nez v3, :cond_1

    return v2

    :cond_1
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_2
    const/4 p1, 0x1

    return p1
.end method

.method public isValidMethod([Ljava/lang/Class;)Z
    .locals 5

    const/4 v0, 0x1

    if-nez p1, :cond_0

    return v0

    .line 244
    :cond_0
    array-length v1, p1

    .line 245
    invoke-virtual {p0}, Lorg/codehaus/groovy/reflection/ParameterTypes;->getParameterTypes()[Lorg/codehaus/groovy/reflection/CachedClass;

    move-result-object v2

    .line 246
    array-length v3, v2

    sub-int/2addr v3, v0

    .line 248
    iget-boolean v4, p0, Lorg/codehaus/groovy/reflection/ParameterTypes;->isVargsMethod:Z

    if-eqz v4, :cond_1

    if-lt v1, v3, :cond_1

    .line 249
    invoke-static {p1, v1, v2, v3}, Lorg/codehaus/groovy/reflection/ParameterTypes;->isValidVarargsMethod([Ljava/lang/Class;I[Lorg/codehaus/groovy/reflection/CachedClass;I)Z

    move-result p1

    return p1

    .line 250
    :cond_1
    array-length v3, v2

    if-ne v3, v1, :cond_2

    .line 251
    invoke-static {p1, v2}, Lorg/codehaus/groovy/reflection/ParameterTypes;->isValidExactMethod([Ljava/lang/Class;[Lorg/codehaus/groovy/reflection/CachedClass;)Z

    move-result p1

    return p1

    .line 252
    :cond_2
    array-length p1, v2

    const/4 v3, 0x0

    if-ne p1, v0, :cond_3

    if-nez v1, :cond_3

    aget-object p1, v2, v3

    iget-boolean p1, p1, Lorg/codehaus/groovy/reflection/CachedClass;->isPrimitive:Z

    if-nez p1, :cond_3

    return v0

    :cond_3
    return v3
.end method

.method public isValidMethod([Ljava/lang/Object;)Z
    .locals 8

    const/4 v0, 0x1

    if-nez p1, :cond_0

    return v0

    .line 331
    :cond_0
    array-length v1, p1

    .line 332
    invoke-virtual {p0}, Lorg/codehaus/groovy/reflection/ParameterTypes;->getParameterTypes()[Lorg/codehaus/groovy/reflection/CachedClass;

    move-result-object v2

    .line 333
    array-length v3, v2

    sub-int/2addr v3, v0

    const/4 v4, 0x0

    if-lt v1, v3, :cond_7

    .line 335
    array-length v5, v2

    if-lez v5, :cond_7

    aget-object v5, v2, v3

    iget-boolean v5, v5, Lorg/codehaus/groovy/reflection/CachedClass;->isArray:Z

    if-eqz v5, :cond_7

    move v5, v4

    :goto_0
    if-ge v5, v3, :cond_2

    .line 339
    aget-object v6, v2, v5

    aget-object v7, p1, v5

    invoke-static {v7}, Lorg/codehaus/groovy/reflection/ParameterTypes;->getArgClass(Ljava/lang/Object;)Ljava/lang/Class;

    move-result-object v7

    invoke-virtual {v6, v7}, Lorg/codehaus/groovy/reflection/CachedClass;->isAssignableFrom(Ljava/lang/Class;)Z

    move-result v6

    if-eqz v6, :cond_1

    add-int/lit8 v5, v5, 0x1

    goto :goto_0

    :cond_1
    return v4

    .line 345
    :cond_2
    aget-object v5, v2, v3

    .line 346
    invoke-virtual {v5}, Lorg/codehaus/groovy/reflection/CachedClass;->getTheClass()Ljava/lang/Class;

    move-result-object v6

    invoke-virtual {v6}, Ljava/lang/Class;->getComponentType()Ljava/lang/Class;

    move-result-object v6

    .line 347
    array-length v2, v2

    if-ne v1, v2, :cond_4

    aget-object v2, p1, v3

    .line 348
    invoke-static {v2}, Lorg/codehaus/groovy/reflection/ParameterTypes;->getArgClass(Ljava/lang/Object;)Ljava/lang/Class;

    move-result-object v2

    invoke-virtual {v5, v2}, Lorg/codehaus/groovy/reflection/CachedClass;->isAssignableFrom(Ljava/lang/Class;)Z

    move-result v2

    if-nez v2, :cond_3

    aget-object v2, p1, v3

    .line 349
    invoke-static {v2}, Lorg/codehaus/groovy/reflection/ParameterTypes;->getArgClass(Ljava/lang/Object;)Ljava/lang/Class;

    move-result-object v2

    invoke-static {v6, v2}, Lorg/codehaus/groovy/reflection/ParameterTypes;->testComponentAssignable(Ljava/lang/Class;Ljava/lang/Class;)Z

    move-result v2

    if-eqz v2, :cond_4

    :cond_3
    return v0

    :cond_4
    :goto_1
    if-ge v3, v1, :cond_6

    .line 356
    aget-object v2, p1, v3

    invoke-static {v2}, Lorg/codehaus/groovy/reflection/ParameterTypes;->getArgClass(Ljava/lang/Object;)Ljava/lang/Class;

    move-result-object v2

    invoke-static {v6, v2}, Lorg/codehaus/groovy/runtime/MetaClassHelper;->isAssignableFrom(Ljava/lang/Class;Ljava/lang/Class;)Z

    move-result v2

    if-eqz v2, :cond_5

    add-int/lit8 v3, v3, 0x1

    goto :goto_1

    :cond_5
    return v4

    :cond_6
    return v0

    .line 360
    :cond_7
    array-length v3, v2

    if-ne v3, v1, :cond_a

    move v3, v4

    :goto_2
    if-ge v3, v1, :cond_9

    .line 363
    aget-object v5, v2, v3

    aget-object v6, p1, v3

    invoke-static {v6}, Lorg/codehaus/groovy/reflection/ParameterTypes;->getArgClass(Ljava/lang/Object;)Ljava/lang/Class;

    move-result-object v6

    invoke-virtual {v5, v6}, Lorg/codehaus/groovy/reflection/CachedClass;->isAssignableFrom(Ljava/lang/Class;)Z

    move-result v5

    if-eqz v5, :cond_8

    add-int/lit8 v3, v3, 0x1

    goto :goto_2

    :cond_8
    return v4

    :cond_9
    return v0

    .line 367
    :cond_a
    array-length p1, v2

    if-ne p1, v0, :cond_b

    if-nez v1, :cond_b

    aget-object p1, v2, v4

    iget-boolean p1, p1, Lorg/codehaus/groovy/reflection/CachedClass;->isPrimitive:Z

    if-nez p1, :cond_b

    return v0

    :cond_b
    return v4
.end method

.method public isVargsMethod()Z
    .locals 1

    .line 119
    iget-boolean v0, p0, Lorg/codehaus/groovy/reflection/ParameterTypes;->isVargsMethod:Z

    return v0
.end method

.method public isVargsMethod([Ljava/lang/Object;)Z
    .locals 4

    .line 125
    iget-boolean v0, p0, Lorg/codehaus/groovy/reflection/ParameterTypes;->isVargsMethod:Z

    const/4 v1, 0x0

    if-nez v0, :cond_0

    return v1

    .line 128
    :cond_0
    iget-object v0, p0, Lorg/codehaus/groovy/reflection/ParameterTypes;->parameterTypes:[Lorg/codehaus/groovy/reflection/CachedClass;

    array-length v0, v0

    const/4 v2, 0x1

    sub-int/2addr v0, v2

    .line 130
    array-length v3, p1

    if-ne v0, v3, :cond_1

    return v2

    .line 131
    :cond_1
    array-length v3, p1

    if-le v0, v3, :cond_2

    return v1

    .line 132
    :cond_2
    array-length v1, p1

    iget-object v3, p0, Lorg/codehaus/groovy/reflection/ParameterTypes;->parameterTypes:[Lorg/codehaus/groovy/reflection/CachedClass;

    array-length v3, v3

    if-le v1, v3, :cond_3

    return v2

    .line 135
    :cond_3
    array-length v1, p1

    sub-int/2addr v1, v2

    aget-object p1, p1, v1

    if-nez p1, :cond_4

    return v2

    .line 137
    :cond_4
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object p1

    .line 138
    iget-object v1, p0, Lorg/codehaus/groovy/reflection/ParameterTypes;->parameterTypes:[Lorg/codehaus/groovy/reflection/CachedClass;

    aget-object v0, v1, v0

    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/CachedClass;->getTheClass()Ljava/lang/Class;

    move-result-object v0

    invoke-virtual {p1, v0}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result p1

    xor-int/2addr p1, v2

    return p1
.end method

.method protected final setParametersTypes([Lorg/codehaus/groovy/reflection/CachedClass;)V
    .locals 2

    .line 61
    iput-object p1, p0, Lorg/codehaus/groovy/reflection/ParameterTypes;->parameterTypes:[Lorg/codehaus/groovy/reflection/CachedClass;

    .line 62
    array-length v0, p1

    const/4 v1, 0x1

    if-lez v0, :cond_0

    array-length v0, p1

    sub-int/2addr v0, v1

    aget-object p1, p1, v0

    iget-boolean p1, p1, Lorg/codehaus/groovy/reflection/CachedClass;->isArray:Z

    if-eqz p1, :cond_0

    goto :goto_0

    :cond_0
    const/4 v1, 0x0

    :goto_0
    iput-boolean v1, p0, Lorg/codehaus/groovy/reflection/ParameterTypes;->isVargsMethod:Z

    return-void
.end method
