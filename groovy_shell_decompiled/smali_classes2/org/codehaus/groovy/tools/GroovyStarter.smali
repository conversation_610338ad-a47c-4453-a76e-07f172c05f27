.class public Lorg/codehaus/groovy/tools/GroovyStarter;
.super Ljava/lang/Object;
.source "GroovyStarter.java"


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 30
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method private static exit(Ljava/lang/Exception;)V
    .locals 0

    .line 124
    invoke-virtual {p0}, Ljava/lang/Exception;->printStackTrace()V

    const/4 p0, 0x1

    .line 125
    invoke-static {p0}, Ljava/lang/System;->exit(I)V

    return-void
.end method

.method private static exit(Ljava/lang/String;)V
    .locals 1

    .line 129
    sget-object v0, Ljava/lang/System;->err:Ljava/io/PrintStream;

    invoke-virtual {v0, p0}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    const/4 p0, 0x1

    .line 130
    invoke-static {p0}, Ljava/lang/System;->exit(I)V

    return-void
.end method

.method static synthetic lambda$rootLoader$0(Lorg/codehaus/groovy/tools/LoaderConfiguration;)Lorg/codehaus/groovy/tools/RootLoader;
    .locals 1

    .line 108
    new-instance v0, Lorg/codehaus/groovy/tools/RootLoader;

    invoke-direct {v0, p0}, Lorg/codehaus/groovy/tools/RootLoader;-><init>(Lorg/codehaus/groovy/tools/LoaderConfiguration;)V

    return-object v0
.end method

.method public static main([Ljava/lang/String;)V
    .locals 0

    .line 39
    :try_start_0
    invoke-static {p0}, Lorg/codehaus/groovy/tools/GroovyStarter;->rootLoader([Ljava/lang/String;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_0

    :catchall_0
    move-exception p0

    .line 41
    invoke-virtual {p0}, Ljava/lang/Throwable;->printStackTrace()V

    :goto_0
    return-void
.end method

.method static printUsage()V
    .locals 2

    .line 33
    sget-object v0, Ljava/lang/System;->out:Ljava/io/PrintStream;

    const-string v1, "possible programs are \'groovyc\',\'groovy\',\'console\', and \'groovysh\'"

    invoke-virtual {v0, v1}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    const/4 v0, 0x1

    .line 34
    invoke-static {v0}, Ljava/lang/System;->exit(I)V

    return-void
.end method

.method public static rootLoader([Ljava/lang/String;)V
    .locals 12

    const-string v0, "groovy.starter.conf"

    const/4 v1, 0x0

    .line 46
    invoke-static {v0, v1}, Ljava/lang/System;->getProperty(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    .line 47
    new-instance v2, Lorg/codehaus/groovy/tools/LoaderConfiguration;

    invoke-direct {v2}, Lorg/codehaus/groovy/tools/LoaderConfiguration;-><init>()V

    const/4 v3, 0x0

    move v4, v3

    move v5, v4

    move v6, v5

    move v7, v6

    .line 53
    :goto_0
    array-length v8, p0

    sub-int/2addr v8, v4

    const/4 v9, 0x1

    if-lez v8, :cond_a

    if-eqz v5, :cond_0

    if-eqz v6, :cond_0

    if-nez v7, :cond_a

    .line 54
    :cond_0
    aget-object v8, p0, v4

    invoke-virtual {v8}, Ljava/lang/String;->hashCode()I

    const/4 v10, -0x1

    invoke-virtual {v8}, Ljava/lang/String;->hashCode()I

    move-result v11

    sparse-switch v11, :sswitch_data_0

    goto :goto_1

    :sswitch_0
    const-string v11, "--main"

    invoke-virtual {v8, v11}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v8

    if-nez v8, :cond_1

    goto :goto_1

    :cond_1
    const/4 v10, 0x2

    goto :goto_1

    :sswitch_1
    const-string v11, "--conf"

    invoke-virtual {v8, v11}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v8

    if-nez v8, :cond_2

    goto :goto_1

    :cond_2
    move v10, v9

    goto :goto_1

    :sswitch_2
    const-string v11, "--classpath"

    invoke-virtual {v8, v11}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v8

    if-nez v8, :cond_3

    goto :goto_1

    :cond_3
    move v10, v3

    :goto_1
    packed-switch v10, :pswitch_data_0

    goto :goto_2

    :pswitch_0
    if-eqz v5, :cond_4

    goto :goto_2

    .line 66
    :cond_4
    array-length v5, p0

    add-int/lit8 v8, v4, 0x1

    if-ne v5, v8, :cond_5

    const-string v5, "main parameter needs argument"

    .line 67
    invoke-static {v5}, Lorg/codehaus/groovy/tools/GroovyStarter;->exit(Ljava/lang/String;)V

    .line 69
    :cond_5
    aget-object v5, p0, v8

    invoke-virtual {v2, v5}, Lorg/codehaus/groovy/tools/LoaderConfiguration;->setMainClass(Ljava/lang/String;)V

    add-int/lit8 v4, v4, 0x2

    move v5, v9

    goto :goto_0

    :pswitch_1
    if-eqz v6, :cond_6

    goto :goto_2

    .line 75
    :cond_6
    array-length v0, p0

    add-int/lit8 v6, v4, 0x1

    if-ne v0, v6, :cond_7

    const-string v0, "conf parameter needs argument"

    .line 76
    invoke-static {v0}, Lorg/codehaus/groovy/tools/GroovyStarter;->exit(Ljava/lang/String;)V

    .line 78
    :cond_7
    aget-object v0, p0, v6

    add-int/lit8 v4, v4, 0x2

    move v6, v9

    goto :goto_0

    :pswitch_2
    if-eqz v7, :cond_8

    goto :goto_2

    .line 57
    :cond_8
    array-length v7, p0

    add-int/lit8 v8, v4, 0x1

    if-ne v7, v8, :cond_9

    const-string v7, "classpath parameter needs argument"

    .line 58
    invoke-static {v7}, Lorg/codehaus/groovy/tools/GroovyStarter;->exit(Ljava/lang/String;)V

    .line 60
    :cond_9
    aget-object v7, p0, v8

    invoke-virtual {v2, v7}, Lorg/codehaus/groovy/tools/LoaderConfiguration;->addClassPath(Ljava/lang/String;)V

    add-int/lit8 v4, v4, 0x2

    move v7, v9

    goto :goto_0

    :cond_a
    :goto_2
    const-string v5, "groovy.starter.conf.override"

    .line 88
    invoke-static {v5, v1}, Ljava/lang/System;->getProperty(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v5

    if-eqz v5, :cond_b

    move-object v0, v5

    .line 92
    :cond_b
    invoke-virtual {v2}, Lorg/codehaus/groovy/tools/LoaderConfiguration;->getMainClass()Ljava/lang/String;

    move-result-object v5

    if-nez v5, :cond_c

    if-nez v0, :cond_c

    const-string v5, "no configuration file or main class specified"

    .line 93
    invoke-static {v5}, Lorg/codehaus/groovy/tools/GroovyStarter;->exit(Ljava/lang/String;)V

    .line 97
    :cond_c
    array-length v5, p0

    invoke-static {p0, v4, v5}, Ljava/util/Arrays;->copyOfRange([Ljava/lang/Object;II)[Ljava/lang/Object;

    move-result-object p0

    check-cast p0, [Ljava/lang/String;

    if-eqz v0, :cond_d

    .line 101
    :try_start_0
    new-instance v4, Ljava/io/FileInputStream;

    invoke-direct {v4, v0}, Ljava/io/FileInputStream;-><init>(Ljava/lang/String;)V

    invoke-virtual {v2, v4}, Lorg/codehaus/groovy/tools/LoaderConfiguration;->configure(Ljava/io/InputStream;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_3

    :catch_0
    move-exception v0

    .line 103
    sget-object v4, Ljava/lang/System;->err:Ljava/io/PrintStream;

    const-string v5, "exception while configuring main class loader:"

    invoke-virtual {v4, v5}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    .line 104
    invoke-static {v0}, Lorg/codehaus/groovy/tools/GroovyStarter;->exit(Ljava/lang/Exception;)V

    .line 108
    :cond_d
    :goto_3
    new-instance v0, Lorg/codehaus/groovy/tools/GroovyStarter$$ExternalSyntheticLambda0;

    invoke-direct {v0, v2}, Lorg/codehaus/groovy/tools/GroovyStarter$$ExternalSyntheticLambda0;-><init>(Lorg/codehaus/groovy/tools/LoaderConfiguration;)V

    invoke-static {v0}, Ljava/security/AccessController;->doPrivileged(Ljava/security/PrivilegedAction;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/ClassLoader;

    .line 111
    :try_start_1
    invoke-virtual {v2}, Lorg/codehaus/groovy/tools/LoaderConfiguration;->getMainClass()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Ljava/lang/ClassLoader;->loadClass(Ljava/lang/String;)Ljava/lang/Class;

    move-result-object v0

    const-string v2, "main"

    new-array v4, v9, [Ljava/lang/Class;

    .line 112
    const-class v5, [Ljava/lang/String;

    aput-object v5, v4, v3

    invoke-virtual {v0, v2, v4}, Ljava/lang/Class;->getMethod(Ljava/lang/String;[Ljava/lang/Class;)Ljava/lang/reflect/Method;

    move-result-object v0
    :try_end_1
    .catch Ljava/lang/ReflectiveOperationException; {:try_start_1 .. :try_end_1} :catch_2
    .catch Ljava/lang/SecurityException; {:try_start_1 .. :try_end_1} :catch_1

    goto :goto_5

    :catch_1
    move-exception v0

    goto :goto_4

    :catch_2
    move-exception v0

    .line 114
    :goto_4
    invoke-static {v0}, Lorg/codehaus/groovy/tools/GroovyStarter;->exit(Ljava/lang/Exception;)V

    move-object v0, v1

    :goto_5
    :try_start_2
    new-array v2, v9, [Ljava/lang/Object;

    aput-object p0, v2, v3

    .line 117
    invoke-virtual {v0, v1, v2}, Ljava/lang/reflect/Method;->invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;
    :try_end_2
    .catch Ljava/lang/ReflectiveOperationException; {:try_start_2 .. :try_end_2} :catch_4
    .catch Ljava/lang/IllegalArgumentException; {:try_start_2 .. :try_end_2} :catch_3

    goto :goto_7

    :catch_3
    move-exception p0

    goto :goto_6

    :catch_4
    move-exception p0

    .line 119
    :goto_6
    invoke-static {p0}, Lorg/codehaus/groovy/tools/GroovyStarter;->exit(Ljava/lang/Exception;)V

    :goto_7
    return-void

    :sswitch_data_0
    .sparse-switch
        0x1c94dffd -> :sswitch_2
        0x4f72e4c4 -> :sswitch_1
        0x4f773b59 -> :sswitch_0
    .end sparse-switch

    :pswitch_data_0
    .packed-switch 0x0
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method
