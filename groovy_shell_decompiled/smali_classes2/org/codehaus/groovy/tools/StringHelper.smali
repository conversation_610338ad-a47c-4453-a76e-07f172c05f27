.class public Lorg/codehaus/groovy/tools/StringHelper;
.super Ljava/lang/Object;
.source "StringHelper.java"


# static fields
.field private static final DOUBLE_QUOTE:C = '\"'

.field private static final EMPTY_STRING_ARRAY:[Ljava/lang/String;

.field private static final SINGLE_QUOTE:C = '\''

.field private static final SPACE:C = ' '


# direct methods
.method static constructor <clinit>()V
    .locals 1

    const/4 v0, 0x0

    new-array v0, v0, [Ljava/lang/String;

    .line 27
    sput-object v0, Lorg/codehaus/groovy/tools/StringHelper;->EMPTY_STRING_ARRAY:[Ljava/lang/String;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 24
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method private static scanQuoted(Ljava/lang/String;IC)I
    .locals 1

    .line 70
    :goto_0
    invoke-virtual {p0}, Ljava/lang/String;->length()I

    move-result v0

    if-ge p1, v0, :cond_1

    add-int/lit8 v0, p1, 0x1

    .line 71
    invoke-virtual {p0, p1}, Ljava/lang/String;->charAt(I)C

    move-result p1

    if-ne p2, p1, :cond_0

    move p1, v0

    goto :goto_1

    :cond_0
    move p1, v0

    goto :goto_0

    :cond_1
    :goto_1
    return p1
.end method

.method private static scanToken(Ljava/lang/String;I)I
    .locals 2

    .line 55
    :cond_0
    :goto_0
    invoke-virtual {p0}, Ljava/lang/String;->length()I

    move-result v0

    if-ge p1, v0, :cond_3

    .line 56
    invoke-virtual {p0, p1}, Ljava/lang/String;->charAt(I)C

    move-result v0

    const/16 v1, 0x20

    if-ne v1, v0, :cond_1

    goto :goto_1

    :cond_1
    add-int/lit8 p1, p1, 0x1

    const/16 v1, 0x27

    if-ne v1, v0, :cond_2

    .line 60
    invoke-static {p0, p1, v1}, Lorg/codehaus/groovy/tools/StringHelper;->scanQuoted(Ljava/lang/String;IC)I

    move-result p1

    goto :goto_0

    :cond_2
    const/16 v1, 0x22

    if-ne v1, v0, :cond_0

    .line 62
    invoke-static {p0, p1, v1}, Lorg/codehaus/groovy/tools/StringHelper;->scanQuoted(Ljava/lang/String;IC)I

    move-result p1

    goto :goto_0

    :cond_3
    :goto_1
    return p1
.end method

.method private static skipWhitespace(Ljava/lang/String;I)I
    .locals 2

    .line 79
    :goto_0
    invoke-virtual {p0}, Ljava/lang/String;->length()I

    move-result v0

    if-ge p1, v0, :cond_1

    .line 80
    invoke-virtual {p0, p1}, Ljava/lang/String;->charAt(I)C

    move-result v0

    const/16 v1, 0x20

    if-eq v1, v0, :cond_0

    goto :goto_1

    :cond_0
    add-int/lit8 p1, p1, 0x1

    goto :goto_0

    :cond_1
    :goto_1
    return p1
.end method

.method public static tokenizeUnquoted(Ljava/lang/String;)[Ljava/lang/String;
    .locals 3

    .line 40
    new-instance v0, Ljava/util/LinkedList;

    invoke-direct {v0}, Ljava/util/LinkedList;-><init>()V

    const/4 v1, 0x0

    .line 42
    :goto_0
    invoke-virtual {p0}, Ljava/lang/String;->length()I

    move-result v2

    if-ge v1, v2, :cond_1

    .line 43
    invoke-static {p0, v1}, Lorg/codehaus/groovy/tools/StringHelper;->skipWhitespace(Ljava/lang/String;I)I

    move-result v1

    .line 44
    invoke-static {p0, v1}, Lorg/codehaus/groovy/tools/StringHelper;->scanToken(Ljava/lang/String;I)I

    move-result v2

    if-ge v1, v2, :cond_0

    .line 46
    invoke-virtual {p0, v1, v2}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :cond_0
    move v1, v2

    goto :goto_0

    .line 50
    :cond_1
    sget-object p0, Lorg/codehaus/groovy/tools/StringHelper;->EMPTY_STRING_ARRAY:[Ljava/lang/String;

    invoke-interface {v0, p0}, Ljava/util/List;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object p0

    check-cast p0, [Ljava/lang/String;

    return-object p0
.end method
