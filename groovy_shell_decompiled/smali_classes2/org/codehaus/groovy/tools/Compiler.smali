.class public Lorg/codehaus/groovy/tools/Compiler;
.super Ljava/lang/Object;
.source "Compiler.java"


# static fields
.field public static final DEFAULT:Lorg/codehaus/groovy/tools/Compiler;


# instance fields
.field private final configuration:Lorg/codehaus/groovy/control/CompilerConfiguration;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 34
    new-instance v0, Lorg/codehaus/groovy/tools/Compiler;

    invoke-direct {v0}, Lorg/codehaus/groovy/tools/Compiler;-><init>()V

    sput-object v0, Lorg/codehaus/groovy/tools/Compiler;->DEFAULT:Lorg/codehaus/groovy/tools/Compiler;

    return-void
.end method

.method public constructor <init>()V
    .locals 1

    .line 41
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    .line 42
    iput-object v0, p0, Lorg/codehaus/groovy/tools/Compiler;->configuration:Lorg/codehaus/groovy/control/CompilerConfiguration;

    return-void
.end method

.method public constructor <init>(Lorg/codehaus/groovy/control/CompilerConfiguration;)V
    .locals 0

    .line 49
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 50
    iput-object p1, p0, Lorg/codehaus/groovy/tools/Compiler;->configuration:Lorg/codehaus/groovy/control/CompilerConfiguration;

    return-void
.end method


# virtual methods
.method public compile(Ljava/io/File;)V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lorg/codehaus/groovy/control/CompilationFailedException;
        }
    .end annotation

    .line 58
    new-instance v0, Lorg/codehaus/groovy/control/CompilationUnit;

    iget-object v1, p0, Lorg/codehaus/groovy/tools/Compiler;->configuration:Lorg/codehaus/groovy/control/CompilerConfiguration;

    invoke-direct {v0, v1}, Lorg/codehaus/groovy/control/CompilationUnit;-><init>(Lorg/codehaus/groovy/control/CompilerConfiguration;)V

    .line 59
    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/control/CompilationUnit;->addSource(Ljava/io/File;)Lorg/codehaus/groovy/control/SourceUnit;

    .line 60
    invoke-virtual {v0}, Lorg/codehaus/groovy/control/CompilationUnit;->compile()V

    return-void
.end method

.method public compile(Ljava/lang/String;Ljava/lang/String;)V
    .locals 8
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lorg/codehaus/groovy/control/CompilationFailedException;
        }
    .end annotation

    .line 88
    new-instance v0, Lorg/codehaus/groovy/control/CompilationUnit;

    iget-object v1, p0, Lorg/codehaus/groovy/tools/Compiler;->configuration:Lorg/codehaus/groovy/control/CompilerConfiguration;

    invoke-direct {v0, v1}, Lorg/codehaus/groovy/control/CompilationUnit;-><init>(Lorg/codehaus/groovy/control/CompilerConfiguration;)V

    .line 89
    new-instance v1, Lorg/codehaus/groovy/control/SourceUnit;

    iget-object v5, p0, Lorg/codehaus/groovy/tools/Compiler;->configuration:Lorg/codehaus/groovy/control/CompilerConfiguration;

    invoke-virtual {v0}, Lorg/codehaus/groovy/control/CompilationUnit;->getClassLoader()Lgroovy/lang/GroovyClassLoader;

    move-result-object v6

    invoke-virtual {v0}, Lorg/codehaus/groovy/control/CompilationUnit;->getErrorCollector()Lorg/codehaus/groovy/control/ErrorCollector;

    move-result-object v7

    move-object v2, v1

    move-object v3, p1

    move-object v4, p2

    invoke-direct/range {v2 .. v7}, Lorg/codehaus/groovy/control/SourceUnit;-><init>(Ljava/lang/String;Ljava/lang/String;Lorg/codehaus/groovy/control/CompilerConfiguration;Lgroovy/lang/GroovyClassLoader;Lorg/codehaus/groovy/control/ErrorCollector;)V

    invoke-virtual {v0, v1}, Lorg/codehaus/groovy/control/CompilationUnit;->addSource(Lorg/codehaus/groovy/control/SourceUnit;)Lorg/codehaus/groovy/control/SourceUnit;

    .line 90
    invoke-virtual {v0}, Lorg/codehaus/groovy/control/CompilationUnit;->compile()V

    return-void
.end method

.method public compile([Ljava/io/File;)V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lorg/codehaus/groovy/control/CompilationFailedException;
        }
    .end annotation

    .line 68
    new-instance v0, Lorg/codehaus/groovy/control/CompilationUnit;

    iget-object v1, p0, Lorg/codehaus/groovy/tools/Compiler;->configuration:Lorg/codehaus/groovy/control/CompilerConfiguration;

    invoke-direct {v0, v1}, Lorg/codehaus/groovy/control/CompilationUnit;-><init>(Lorg/codehaus/groovy/control/CompilerConfiguration;)V

    .line 69
    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/control/CompilationUnit;->addSources([Ljava/io/File;)V

    .line 70
    invoke-virtual {v0}, Lorg/codehaus/groovy/control/CompilationUnit;->compile()V

    return-void
.end method

.method public compile([Ljava/lang/String;)V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lorg/codehaus/groovy/control/CompilationFailedException;
        }
    .end annotation

    .line 78
    new-instance v0, Lorg/codehaus/groovy/control/CompilationUnit;

    iget-object v1, p0, Lorg/codehaus/groovy/tools/Compiler;->configuration:Lorg/codehaus/groovy/control/CompilerConfiguration;

    invoke-direct {v0, v1}, Lorg/codehaus/groovy/control/CompilationUnit;-><init>(Lorg/codehaus/groovy/control/CompilerConfiguration;)V

    .line 79
    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/control/CompilationUnit;->addSources([Ljava/lang/String;)V

    .line 80
    invoke-virtual {v0}, Lorg/codehaus/groovy/control/CompilationUnit;->compile()V

    return-void
.end method
