.class public Lorg/codehaus/groovy/tools/GrapeMain;
.super Ljava/lang/Object;
.source "GrapeMain.groovy"

# interfaces
.implements Ljava/lang/Runnable;
.implements Lgroovy/lang/GroovyObject;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/codehaus/groovy/tools/GrapeMain$HelpOptionsMixin;,
        Lorg/codehaus/groovy/tools/GrapeMain$VersionProvider;,
        Lorg/codehaus/groovy/tools/GrapeMain$Install;,
        Lorg/codehaus/groovy/tools/GrapeMain$ListCommand;,
        Lorg/codehaus/groovy/tools/GrapeMain$Resolve;,
        Lorg/codehaus/groovy/tools/GrapeMain$Uninstall;
    }
.end annotation

.annotation runtime Lgroovyjarjarpicocli/CommandLine$Command;
    description = {
        "Allows for the inspection and management of the local grape cache."
    }
    name = "grape"
    subcommands = {
        Lorg/codehaus/groovy/tools/GrapeMain$Install;,
        Lorg/codehaus/groovy/tools/GrapeMain$Uninstall;,
        Lorg/codehaus/groovy/tools/GrapeMain$ListCommand;,
        Lorg/codehaus/groovy/tools/GrapeMain$Resolve;,
        Lgroovyjarjarpicocli/CommandLine$HelpCommand;
    }
.end annotation


# static fields
.field private static synthetic $callSiteArray:Ljava/lang/ref/SoftReference;

.field private static synthetic $staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

.field private static synthetic $staticClassInfo$:Lorg/codehaus/groovy/reflection/ClassInfo;

.field public static transient synthetic __$stMC:Z


# instance fields
.field private debug:Z
    .annotation runtime Lgroovyjarjarpicocli/CommandLine$Option;
        description = {
            "Log level 4 - debug"
        }
        names = {
            "-d",
            "--debug"
        }
    .end annotation
.end field

.field private info:Z
    .annotation runtime Lgroovyjarjarpicocli/CommandLine$Option;
        description = {
            "Log level 2 - info"
        }
        names = {
            "-i",
            "--info"
        }
    .end annotation
.end field

.field private transient synthetic metaClass:Lgroovy/lang/MetaClass;

.field private parser:Lgroovyjarjarpicocli/CommandLine;

.field private final properties:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .annotation runtime Lgroovyjarjarpicocli/CommandLine$Option;
        description = {
            "define a system property"
        }
        names = {
            "-D",
            "--define"
        }
        paramLabel = "<name=value>"
    .end annotation
.end field

.field private quiet:Z
    .annotation runtime Lgroovyjarjarpicocli/CommandLine$Option;
        description = {
            "Log level 0 - only errors"
        }
        names = {
            "-q",
            "--quiet"
        }
    .end annotation
.end field

.field private final resolvers:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .annotation runtime Lgroovyjarjarpicocli/CommandLine$Option;
        description = {
            "define a grab resolver (for install)"
        }
        names = {
            "-r",
            "--resolver"
        }
        paramLabel = "<url>"
    .end annotation
.end field

.field private unmatched:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .annotation runtime Lgroovyjarjarpicocli/CommandLine$Unmatched;
    .end annotation
.end field

.field private verbose:Z
    .annotation runtime Lgroovyjarjarpicocli/CommandLine$Option;
        description = {
            "Log level 3 - verbose"
        }
        names = {
            "-V",
            "--verbose"
        }
    .end annotation
.end field

.field private warn:Z
    .annotation runtime Lgroovyjarjarpicocli/CommandLine$Option;
        description = {
            "Log level 1 - errors and warnings"
        }
        names = {
            "-w",
            "--warn"
        }
    .end annotation
.end field


# direct methods
.method private static synthetic $createCallSiteArray()Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;
    .locals 3

    const/16 v0, 0x20

    new-array v0, v0, [Ljava/lang/String;

    invoke-static {v0}, Lorg/codehaus/groovy/tools/GrapeMain;->$createCallSiteArray_1([Ljava/lang/String;)V

    new-instance v1, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;

    const-class v2, Lorg/codehaus/groovy/tools/GrapeMain;

    invoke-direct {v1, v2, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;-><init>(Ljava/lang/Class;[Ljava/lang/String;)V

    return-object v1
.end method

.method private static synthetic $createCallSiteArray_1([Ljava/lang/String;)V
    .locals 5

    const/4 v0, 0x0

    const-string v1, "<$constructor$>"

    aput-object v1, p0, v0

    const/4 v0, 0x1

    aput-object v1, p0, v0

    const/4 v0, 0x2

    aput-object v1, p0, v0

    const/4 v0, 0x3

    aput-object v1, p0, v0

    const/4 v0, 0x4

    aput-object v1, p0, v0

    const/4 v0, 0x5

    const-string v2, "addMixin"

    aput-object v2, p0, v0

    const/4 v0, 0x6

    aput-object v1, p0, v0

    const/4 v0, 0x7

    const-string v2, "each"

    aput-object v2, p0, v0

    const/16 v0, 0x8

    const-string v3, "findAll"

    aput-object v3, p0, v0

    const/16 v0, 0x9

    const-string v3, "subcommands"

    aput-object v3, p0, v0

    const/16 v0, 0xa

    const-string v3, "parseWithHandler"

    aput-object v3, p0, v0

    const/16 v0, 0xb

    aput-object v1, p0, v0

    const/16 v0, 0xc

    const-string v3, "println"

    aput-object v3, p0, v0

    const/16 v0, 0xd

    const-string v3, "err"

    aput-object v3, p0, v0

    const/16 v0, 0xe

    const-string v3, "getAt"

    aput-object v3, p0, v0

    const/16 v0, 0xf

    const-string v4, "usage"

    aput-object v4, p0, v0

    const/16 v0, 0x10

    const-string v4, "out"

    aput-object v4, p0, v0

    const/16 v0, 0x11

    aput-object v2, p0, v0

    const/16 v0, 0x12

    aput-object v1, p0, v0

    const/16 v0, 0x13

    const-string v2, "MSG_ERR"

    aput-object v2, p0, v0

    const/16 v0, 0x14

    aput-object v1, p0, v0

    const/16 v0, 0x15

    const-string v2, "MSG_WARN"

    aput-object v2, p0, v0

    const/16 v0, 0x16

    aput-object v1, p0, v0

    const/16 v0, 0x17

    const-string v2, "MSG_INFO"

    aput-object v2, p0, v0

    const/16 v0, 0x18

    aput-object v1, p0, v0

    const/16 v0, 0x19

    const-string v2, "MSG_VERBOSE"

    aput-object v2, p0, v0

    const/16 v0, 0x1a

    aput-object v1, p0, v0

    const/16 v0, 0x1b

    const-string v2, "MSG_DEBUG"

    aput-object v2, p0, v0

    const/16 v0, 0x1c

    aput-object v1, p0, v0

    const/16 v0, 0x1d

    const-string v1, "length"

    aput-object v1, p0, v0

    const/16 v0, 0x1e

    aput-object v3, p0, v0

    const/16 v0, 0x1f

    aput-object v1, p0, v0

    return-void
.end method

.method private static synthetic $getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;
    .locals 2

    sget-object v0, Lorg/codehaus/groovy/tools/GrapeMain;->$callSiteArray:Ljava/lang/ref/SoftReference;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Ljava/lang/ref/SoftReference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;

    if-nez v0, :cond_1

    :cond_0
    invoke-static {}, Lorg/codehaus/groovy/tools/GrapeMain;->$createCallSiteArray()Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;

    move-result-object v0

    new-instance v1, Ljava/lang/ref/SoftReference;

    invoke-direct {v1, v0}, Ljava/lang/ref/SoftReference;-><init>(Ljava/lang/Object;)V

    sput-object v1, Lorg/codehaus/groovy/tools/GrapeMain;->$callSiteArray:Ljava/lang/ref/SoftReference;

    :cond_1
    iget-object v0, v0, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;->array:[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    return-object v0
.end method

.method public constructor <init>()V
    .locals 3
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    invoke-static {}, Lorg/codehaus/groovy/tools/GrapeMain;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v1, 0x0

    .line 42
    aget-object v1, v0, v1

    const-class v2, Ljava/util/LinkedHashMap;

    invoke-interface {v1, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    const-class v2, Ljava/util/Map;

    invoke-static {v1, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/util/Map;

    iput-object v1, p0, Lorg/codehaus/groovy/tools/GrapeMain;->properties:Ljava/util/Map;

    const/4 v1, 0x1

    .line 46
    aget-object v1, v0, v1

    const-class v2, Ljava/util/ArrayList;

    invoke-interface {v1, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    const-class v2, Ljava/util/List;

    invoke-static {v1, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/util/List;

    iput-object v1, p0, Lorg/codehaus/groovy/tools/GrapeMain;->resolvers:Ljava/util/List;

    const/4 v1, 0x2

    .line 63
    aget-object v0, v0, v1

    const-class v1, Ljava/util/ArrayList;

    invoke-interface {v0, v1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    const-class v1, Ljava/util/List;

    invoke-static {v0, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/List;

    iput-object v0, p0, Lorg/codehaus/groovy/tools/GrapeMain;->unmatched:Ljava/util/List;

    invoke-virtual {p0}, Lorg/codehaus/groovy/tools/GrapeMain;->$getStaticMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/tools/GrapeMain;->metaClass:Lgroovy/lang/MetaClass;

    return-void
.end method

.method private init()V
    .locals 3

    invoke-static {}, Lorg/codehaus/groovy/tools/GrapeMain;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v0

    const/16 v1, 0x11

    .line 87
    aget-object v0, v0, v1

    iget-object v1, p0, Lorg/codehaus/groovy/tools/GrapeMain;->properties:Ljava/util/Map;

    new-instance v2, Lorg/codehaus/groovy/tools/GrapeMain$_init_closure3;

    invoke-direct {v2, p0, p0}, Lorg/codehaus/groovy/tools/GrapeMain$_init_closure3;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    invoke-interface {v0, v1, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method public static varargs main([Ljava/lang/String;)V
    .locals 8

    const-class v0, Lorg/codehaus/groovy/tools/GrapeMain;

    invoke-static {}, Lorg/codehaus/groovy/tools/GrapeMain;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v1

    const/4 v2, 0x3

    .line 68
    aget-object v2, v1, v2

    invoke-interface {v2, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-static {v2, v0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lorg/codehaus/groovy/tools/GrapeMain;

    const/4 v3, 0x4

    .line 69
    aget-object v3, v1, v3

    const-class v4, Lgroovyjarjarpicocli/CommandLine;

    invoke-interface {v3, v4, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    const/4 v4, 0x5

    .line 70
    aget-object v4, v1, v4

    const/4 v5, 0x6

    aget-object v5, v1, v5

    const-class v6, Lorg/codehaus/groovy/tools/GrapeMain$HelpOptionsMixin;

    invoke-interface {v5, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    const-string v6, "helpOptions"

    invoke-interface {v4, v3, v6, v5}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/4 v4, 0x7

    .line 71
    aget-object v4, v1, v4

    const/16 v5, 0x8

    aget-object v5, v1, v5

    const/16 v6, 0x9

    aget-object v6, v1, v6

    invoke-interface {v6, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    new-instance v7, Lorg/codehaus/groovy/tools/GrapeMain$_main_closure1;

    invoke-direct {v7, v0, v0}, Lorg/codehaus/groovy/tools/GrapeMain$_main_closure1;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    invoke-interface {v5, v6, v7}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    new-instance v6, Lorg/codehaus/groovy/tools/GrapeMain$_main_closure2;

    invoke-direct {v6, v0, v0}, Lorg/codehaus/groovy/tools/GrapeMain$_main_closure2;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    invoke-interface {v4, v5, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const-string v4, "parser"

    .line 73
    move-object v5, v4

    check-cast v5, Ljava/lang/String;

    invoke-static {v3, v0, v2, v4}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->setGroovyObjectProperty(Ljava/lang/Object;Ljava/lang/Class;Lgroovy/lang/GroovyObject;Ljava/lang/String;)V

    const/16 v0, 0xa

    .line 74
    aget-object v0, v1, v0

    const/16 v2, 0xb

    aget-object v1, v1, v2

    const-class v2, Lgroovyjarjarpicocli/CommandLine$RunLast;

    invoke-interface {v1, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-interface {v0, v3, v1, p0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method private setupLogging()V
    .locals 2
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    invoke-static {}, Lorg/codehaus/groovy/tools/GrapeMain;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    sget-boolean v0, Lorg/codehaus/groovy/tools/GrapeMain;->__$stMC:Z

    const/4 v1, 0x2

    if-nez v0, :cond_0

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->disabledStandardMetaClass()Z

    move-result v0

    :cond_0
    invoke-direct {p0, v1}, Lorg/codehaus/groovy/tools/GrapeMain;->setupLogging(I)V

    return-void
.end method

.method private setupLogging(I)V
    .locals 5

    invoke-static {}, Lorg/codehaus/groovy/tools/GrapeMain;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v0

    .line 94
    iget-boolean v1, p0, Lorg/codehaus/groovy/tools/GrapeMain;->quiet:Z

    const-string v2, "defaultLogger"

    const/4 v3, 0x0

    if-eqz v1, :cond_0

    const/16 p1, 0x12

    .line 95
    aget-object p1, v0, p1

    const-class v1, Lorg/apache/ivy/util/DefaultMessageLogger;

    const/16 v4, 0x13

    aget-object v0, v0, v4

    const-class v4, Lorg/apache/ivy/util/Message;

    invoke-interface {v0, v4}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    invoke-interface {p1, v1, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    const-class v0, Lorg/apache/ivy/util/Message;

    move-object v1, v2

    check-cast v1, Ljava/lang/String;

    invoke-static {p1, v3, v0, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->setProperty(Ljava/lang/Object;Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;)V

    goto/16 :goto_0

    .line 96
    :cond_0
    iget-boolean v1, p0, Lorg/codehaus/groovy/tools/GrapeMain;->warn:Z

    if-eqz v1, :cond_1

    const/16 p1, 0x14

    .line 97
    aget-object p1, v0, p1

    const-class v1, Lorg/apache/ivy/util/DefaultMessageLogger;

    const/16 v4, 0x15

    aget-object v0, v0, v4

    const-class v4, Lorg/apache/ivy/util/Message;

    invoke-interface {v0, v4}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    invoke-interface {p1, v1, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    const-class v0, Lorg/apache/ivy/util/Message;

    move-object v1, v2

    check-cast v1, Ljava/lang/String;

    invoke-static {p1, v3, v0, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->setProperty(Ljava/lang/Object;Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;)V

    goto/16 :goto_0

    .line 98
    :cond_1
    iget-boolean v1, p0, Lorg/codehaus/groovy/tools/GrapeMain;->info:Z

    if-eqz v1, :cond_2

    const/16 p1, 0x16

    .line 99
    aget-object p1, v0, p1

    const-class v1, Lorg/apache/ivy/util/DefaultMessageLogger;

    const/16 v4, 0x17

    aget-object v0, v0, v4

    const-class v4, Lorg/apache/ivy/util/Message;

    invoke-interface {v0, v4}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    invoke-interface {p1, v1, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    const-class v0, Lorg/apache/ivy/util/Message;

    move-object v1, v2

    check-cast v1, Ljava/lang/String;

    invoke-static {p1, v3, v0, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->setProperty(Ljava/lang/Object;Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;)V

    goto :goto_0

    .line 100
    :cond_2
    iget-boolean v1, p0, Lorg/codehaus/groovy/tools/GrapeMain;->verbose:Z

    if-eqz v1, :cond_3

    const/16 p1, 0x18

    .line 101
    aget-object p1, v0, p1

    const-class v1, Lorg/apache/ivy/util/DefaultMessageLogger;

    const/16 v4, 0x19

    aget-object v0, v0, v4

    const-class v4, Lorg/apache/ivy/util/Message;

    invoke-interface {v0, v4}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    invoke-interface {p1, v1, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    const-class v0, Lorg/apache/ivy/util/Message;

    move-object v1, v2

    check-cast v1, Ljava/lang/String;

    invoke-static {p1, v3, v0, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->setProperty(Ljava/lang/Object;Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;)V

    goto :goto_0

    .line 102
    :cond_3
    iget-boolean v1, p0, Lorg/codehaus/groovy/tools/GrapeMain;->debug:Z

    if-eqz v1, :cond_4

    const/16 p1, 0x1a

    .line 103
    aget-object p1, v0, p1

    const-class v1, Lorg/apache/ivy/util/DefaultMessageLogger;

    const/16 v4, 0x1b

    aget-object v0, v0, v4

    const-class v4, Lorg/apache/ivy/util/Message;

    invoke-interface {v0, v4}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    invoke-interface {p1, v1, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    const-class v0, Lorg/apache/ivy/util/Message;

    move-object v1, v2

    check-cast v1, Ljava/lang/String;

    invoke-static {p1, v3, v0, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->setProperty(Ljava/lang/Object;Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;)V

    goto :goto_0

    :cond_4
    const/16 v1, 0x1c

    .line 105
    aget-object v0, v0, v1

    const-class v1, Lorg/apache/ivy/util/DefaultMessageLogger;

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    invoke-interface {v0, v1, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    const-class v0, Lorg/apache/ivy/util/Message;

    move-object v1, v2

    check-cast v1, Ljava/lang/String;

    invoke-static {p1, v3, v0, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->setProperty(Ljava/lang/Object;Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;)V

    :goto_0
    return-void
.end method


# virtual methods
.method protected synthetic $getStaticMetaClass()Lgroovy/lang/MetaClass;
    .locals 2

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    const-class v1, Lorg/codehaus/groovy/tools/GrapeMain;

    if-eq v0, v1, :cond_0

    invoke-static {p0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->initMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0

    :cond_0
    sget-object v0, Lorg/codehaus/groovy/tools/GrapeMain;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    if-nez v0, :cond_1

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getClassInfo(Ljava/lang/Class;)Lorg/codehaus/groovy/reflection/ClassInfo;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/tools/GrapeMain;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    :cond_1
    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0
.end method

.method public getMetaClass()Lgroovy/lang/MetaClass;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    .annotation runtime Lgroovy/transform/Internal;
    .end annotation

    .annotation runtime Lgroovyjarjaropenbeans/Transient;
    .end annotation

    iget-object v0, p0, Lorg/codehaus/groovy/tools/GrapeMain;->metaClass:Lgroovy/lang/MetaClass;

    if-eqz v0, :cond_0

    return-object v0

    :cond_0
    invoke-virtual {p0}, Lorg/codehaus/groovy/tools/GrapeMain;->$getStaticMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/tools/GrapeMain;->metaClass:Lgroovy/lang/MetaClass;

    return-object v0
.end method

.method public getUnmatched()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-object v0, p0, Lorg/codehaus/groovy/tools/GrapeMain;->unmatched:Ljava/util/List;

    return-object v0
.end method

.method public run()V
    .locals 8

    invoke-static {}, Lorg/codehaus/groovy/tools/GrapeMain;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v0

    .line 78
    iget-object v1, p0, Lorg/codehaus/groovy/tools/GrapeMain;->unmatched:Ljava/util/List;

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_0

    const/16 v1, 0xc

    .line 79
    aget-object v1, v0, v1

    const/16 v2, 0xd

    aget-object v2, v0, v2

    const-class v3, Ljava/lang/System;

    invoke-interface {v2, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    new-instance v3, Lorg/codehaus/groovy/runtime/GStringImpl;

    const/4 v4, 0x1

    new-array v4, v4, [Ljava/lang/Object;

    const/16 v5, 0xe

    aget-object v0, v0, v5

    iget-object v5, p0, Lorg/codehaus/groovy/tools/GrapeMain;->unmatched:Ljava/util/List;

    const/4 v6, 0x0

    invoke-static {v6}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v7

    invoke-interface {v0, v5, v7}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    aput-object v0, v4, v6

    const-string v0, "grape: \'"

    const-string v5, "\' is not a grape command. See \'grape --help\'"

    filled-new-array {v0, v5}, [Ljava/lang/String;

    move-result-object v0

    invoke-direct {v3, v4, v0}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-interface {v1, v2, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    :cond_0
    const/16 v1, 0xf

    .line 81
    aget-object v1, v0, v1

    iget-object v2, p0, Lorg/codehaus/groovy/tools/GrapeMain;->parser:Lgroovyjarjarpicocli/CommandLine;

    const/16 v3, 0x10

    aget-object v0, v0, v3

    const-class v3, Ljava/lang/System;

    invoke-interface {v0, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    invoke-interface {v1, v2, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :goto_0
    return-void
.end method

.method public setMetaClass(Lgroovy/lang/MetaClass;)V
    .locals 0
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    .annotation runtime Lgroovy/transform/Internal;
    .end annotation

    iput-object p1, p0, Lorg/codehaus/groovy/tools/GrapeMain;->metaClass:Lgroovy/lang/MetaClass;

    return-void
.end method

.method public setUnmatched(Ljava/util/List;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;)V"
        }
    .end annotation

    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iput-object p1, p0, Lorg/codehaus/groovy/tools/GrapeMain;->unmatched:Ljava/util/List;

    return-void
.end method

.method public synthetic this$dist$get$1(Ljava/lang/String;)Ljava/lang/Object;
    .locals 4

    invoke-static {}, Lorg/codehaus/groovy/tools/GrapeMain;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    const-class v0, Lorg/codehaus/groovy/tools/GrapeMain;

    new-instance v1, Lorg/codehaus/groovy/runtime/GStringImpl;

    const/4 v2, 0x1

    new-array v2, v2, [Ljava/lang/Object;

    const/4 v3, 0x0

    aput-object p1, v2, v3

    const-string p1, ""

    filled-new-array {p1, p1}, [Ljava/lang/String;

    move-result-object p1

    invoke-direct {v1, v2, p1}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    move-object v1, p1

    check-cast v1, Ljava/lang/String;

    invoke-static {v0, p0, p1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->getGroovyObjectProperty(Ljava/lang/Class;Lgroovy/lang/GroovyObject;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public synthetic this$dist$invoke$1(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 7

    const-class v0, Lorg/codehaus/groovy/tools/GrapeMain;

    invoke-static {}, Lorg/codehaus/groovy/tools/GrapeMain;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v1

    instance-of v2, p2, [Ljava/lang/Object;

    const/4 v3, 0x1

    xor-int/2addr v2, v3

    const-string v4, ""

    const/4 v5, 0x0

    if-eqz v2, :cond_0

    new-instance v1, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v2, v3, [Ljava/lang/Object;

    aput-object p1, v2, v5

    filled-new-array {v4, v4}, [Ljava/lang/String;

    move-result-object p1

    invoke-direct {v1, v2, p1}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    move-object v1, p1

    check-cast v1, Ljava/lang/String;

    new-array v1, v3, [Ljava/lang/Object;

    aput-object p2, v1, v5

    invoke-static {v0, p0, p1, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodOnCurrentN(Ljava/lang/Class;Lgroovy/lang/GroovyObject;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1

    :cond_0
    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->isOrigInt()Z

    move-result v2

    if-eqz v2, :cond_1

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->isOrigZ()Z

    move-result v2

    if-eqz v2, :cond_1

    sget-boolean v2, Lorg/codehaus/groovy/tools/GrapeMain;->__$stMC:Z

    if-nez v2, :cond_1

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->disabledStandardMetaClass()Z

    move-result v2

    if-nez v2, :cond_1

    const/16 v2, 0x1f

    aget-object v1, v1, v2

    const-class v2, [Ljava/lang/Object;

    invoke-static {p2, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, [Ljava/lang/Object;

    invoke-interface {v1, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    invoke-static {v1, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_2

    new-instance v1, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v2, v3, [Ljava/lang/Object;

    aput-object p1, v2, v5

    filled-new-array {v4, v4}, [Ljava/lang/String;

    move-result-object p1

    invoke-direct {v1, v2, p1}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    move-object v1, p1

    check-cast v1, Ljava/lang/String;

    new-array v1, v3, [Ljava/lang/Object;

    const-class v2, [Ljava/lang/Object;

    invoke-static {p2, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, [Ljava/lang/Object;

    invoke-static {p2, v5}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->objectArrayGet([Ljava/lang/Object;I)Ljava/lang/Object;

    move-result-object p2

    aput-object p2, v1, v5

    invoke-static {v0, p0, p1, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodOnCurrentN(Ljava/lang/Class;Lgroovy/lang/GroovyObject;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1

    :cond_1
    const/16 v2, 0x1d

    aget-object v2, v1, v2

    const-class v6, [Ljava/lang/Object;

    invoke-static {p2, v6}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v6

    check-cast v6, [Ljava/lang/Object;

    invoke-interface {v2, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v6

    invoke-static {v2, v6}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_2

    new-instance v2, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v6, v3, [Ljava/lang/Object;

    aput-object p1, v6, v5

    filled-new-array {v4, v4}, [Ljava/lang/String;

    move-result-object p1

    invoke-direct {v2, v6, p1}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    move-object v2, p1

    check-cast v2, Ljava/lang/String;

    new-array v2, v3, [Ljava/lang/Object;

    const/16 v3, 0x1e

    aget-object v1, v1, v3

    const-class v3, [Ljava/lang/Object;

    invoke-static {p2, v3}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, [Ljava/lang/Object;

    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v3

    invoke-interface {v1, p2, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p2

    aput-object p2, v2, v5

    invoke-static {v0, p0, p1, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodOnCurrentN(Ljava/lang/Class;Lgroovy/lang/GroovyObject;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1

    :cond_2
    new-instance v1, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v2, v3, [Ljava/lang/Object;

    aput-object p1, v2, v5

    filled-new-array {v4, v4}, [Ljava/lang/String;

    move-result-object p1

    invoke-direct {v1, v2, p1}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    move-object v1, p1

    check-cast v1, Ljava/lang/String;

    new-array v1, v5, [Ljava/lang/Object;

    new-array v2, v3, [Ljava/lang/Object;

    aput-object p2, v2, v5

    new-array p2, v3, [I

    aput v5, p2, v5

    invoke-static {v1, v2, p2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->despreadList([Ljava/lang/Object;[Ljava/lang/Object;[I)[Ljava/lang/Object;

    move-result-object p2

    invoke-static {v0, p0, p1, p2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodOnCurrentN(Ljava/lang/Class;Lgroovy/lang/GroovyObject;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public synthetic this$dist$set$1(Ljava/lang/String;Ljava/lang/Object;)V
    .locals 4

    invoke-static {}, Lorg/codehaus/groovy/tools/GrapeMain;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    const-class v0, Lorg/codehaus/groovy/tools/GrapeMain;

    new-instance v1, Lorg/codehaus/groovy/runtime/GStringImpl;

    const/4 v2, 0x1

    new-array v2, v2, [Ljava/lang/Object;

    const/4 v3, 0x0

    aput-object p1, v2, v3

    const-string p1, ""

    filled-new-array {p1, p1}, [Ljava/lang/String;

    move-result-object p1

    invoke-direct {v1, v2, p1}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    move-object v1, p1

    check-cast v1, Ljava/lang/String;

    invoke-static {p2, v0, p0, p1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->setGroovyObjectProperty(Ljava/lang/Object;Ljava/lang/Class;Lgroovy/lang/GroovyObject;Ljava/lang/String;)V

    return-void
.end method
