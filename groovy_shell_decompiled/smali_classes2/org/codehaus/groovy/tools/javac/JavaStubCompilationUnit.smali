.class public Lorg/codehaus/groovy/tools/javac/JavaStubCompilationUnit;
.super Lorg/codehaus/groovy/control/CompilationUnit;
.source "JavaStubCompilationUnit.java"


# instance fields
.field private stubCount:I

.field private final stubGenerator:Lorg/codehaus/groovy/tools/javac/JavaStubGenerator;


# direct methods
.method public constructor <init>(Lorg/codehaus/groovy/control/CompilerConfiguration;Lgroovy/lang/GroovyClassLoader;)V
    .locals 1

    const/4 v0, 0x0

    .line 72
    invoke-direct {p0, p1, p2, v0}, Lorg/codehaus/groovy/tools/javac/JavaStubCompilationUnit;-><init>(Lorg/codehaus/groovy/control/CompilerConfiguration;Lgroovy/lang/GroovyClassLoader;Ljava/io/File;)V

    return-void
.end method

.method public constructor <init>(Lorg/codehaus/groovy/control/CompilerConfiguration;Lgroovy/lang/GroovyClassLoader;Ljava/io/File;)V
    .locals 2

    const/4 v0, 0x0

    .line 46
    invoke-direct {p0, p1, v0, p2}, Lorg/codehaus/groovy/control/CompilationUnit;-><init>(Lorg/codehaus/groovy/control/CompilerConfiguration;Ljava/security/CodeSource;Lgroovy/lang/GroovyClassLoader;)V

    if-nez p3, :cond_0

    .line 49
    iget-object p1, p0, Lorg/codehaus/groovy/tools/javac/JavaStubCompilationUnit;->configuration:Lorg/codehaus/groovy/control/CompilerConfiguration;

    invoke-virtual {p1}, Lorg/codehaus/groovy/control/CompilerConfiguration;->getJointCompilationOptions()Ljava/util/Map;

    move-result-object p1

    const-string p2, "stubDir"

    .line 50
    invoke-interface {p1, p2}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    move-object p3, p1

    check-cast p3, Ljava/io/File;

    .line 52
    :cond_0
    iget-object p1, p0, Lorg/codehaus/groovy/tools/javac/JavaStubCompilationUnit;->configuration:Lorg/codehaus/groovy/control/CompilerConfiguration;

    invoke-virtual {p1}, Lorg/codehaus/groovy/control/CompilerConfiguration;->getTargetBytecode()Ljava/lang/String;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/control/CompilerConfiguration;->isPostJDK5(Ljava/lang/String;)Z

    move-result p1

    .line 53
    iget-object p2, p0, Lorg/codehaus/groovy/tools/javac/JavaStubCompilationUnit;->configuration:Lorg/codehaus/groovy/control/CompilerConfiguration;

    invoke-virtual {p2}, Lorg/codehaus/groovy/control/CompilerConfiguration;->getSourceEncoding()Ljava/lang/String;

    move-result-object p2

    .line 54
    new-instance v0, Lorg/codehaus/groovy/tools/javac/JavaStubGenerator;

    const/4 v1, 0x0

    invoke-direct {v0, p3, v1, p1, p2}, Lorg/codehaus/groovy/tools/javac/JavaStubGenerator;-><init>(Ljava/io/File;ZZLjava/lang/String;)V

    iput-object v0, p0, Lorg/codehaus/groovy/tools/javac/JavaStubCompilationUnit;->stubGenerator:Lorg/codehaus/groovy/tools/javac/JavaStubGenerator;

    .line 56
    new-instance p1, Lorg/codehaus/groovy/tools/javac/JavaStubCompilationUnit$$ExternalSyntheticLambda0;

    invoke-direct {p1, p0}, Lorg/codehaus/groovy/tools/javac/JavaStubCompilationUnit$$ExternalSyntheticLambda0;-><init>(Lorg/codehaus/groovy/tools/javac/JavaStubCompilationUnit;)V

    const/4 p2, 0x3

    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/tools/javac/JavaStubCompilationUnit;->addPhaseOperation(Lorg/codehaus/groovy/control/CompilationUnit$IPrimaryClassNodeOperation;I)V

    .line 61
    new-instance p1, Lorg/codehaus/groovy/tools/javac/JavaStubCompilationUnit$$ExternalSyntheticLambda1;

    invoke-direct {p1, p0}, Lorg/codehaus/groovy/tools/javac/JavaStubCompilationUnit$$ExternalSyntheticLambda1;-><init>(Lorg/codehaus/groovy/tools/javac/JavaStubCompilationUnit;)V

    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/tools/javac/JavaStubCompilationUnit;->addPhaseOperation(Lorg/codehaus/groovy/control/CompilationUnit$IPrimaryClassNodeOperation;I)V

    return-void
.end method

.method private hasAcceptedFileExtension(Ljava/lang/String;)Z
    .locals 1

    .line 113
    invoke-virtual {p1}, Ljava/lang/String;->toLowerCase()Ljava/lang/String;

    move-result-object p1

    const/16 v0, 0x2e

    .line 114
    invoke-virtual {p1, v0}, Ljava/lang/String;->lastIndexOf(I)I

    move-result v0

    add-int/lit8 v0, v0, 0x1

    invoke-virtual {p1, v0}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object p1

    .line 115
    iget-object v0, p0, Lorg/codehaus/groovy/tools/javac/JavaStubCompilationUnit;->configuration:Lorg/codehaus/groovy/control/CompilerConfiguration;

    invoke-virtual {v0}, Lorg/codehaus/groovy/control/CompilerConfiguration;->getScriptExtensions()Ljava/util/Set;

    move-result-object v0

    invoke-interface {v0, p1}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result p1

    return p1
.end method


# virtual methods
.method public addSource(Ljava/io/File;)Lorg/codehaus/groovy/control/SourceUnit;
    .locals 1

    .line 98
    invoke-virtual {p1}, Ljava/io/File;->getName()Ljava/lang/String;

    move-result-object v0

    invoke-direct {p0, v0}, Lorg/codehaus/groovy/tools/javac/JavaStubCompilationUnit;->hasAcceptedFileExtension(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 99
    invoke-super {p0, p1}, Lorg/codehaus/groovy/control/CompilationUnit;->addSource(Ljava/io/File;)Lorg/codehaus/groovy/control/SourceUnit;

    move-result-object p1

    return-object p1

    :cond_0
    const/4 p1, 0x0

    return-object p1
.end method

.method public addSource(Ljava/net/URL;)Lorg/codehaus/groovy/control/SourceUnit;
    .locals 1

    .line 106
    invoke-virtual {p1}, Ljava/net/URL;->getPath()Ljava/lang/String;

    move-result-object v0

    invoke-direct {p0, v0}, Lorg/codehaus/groovy/tools/javac/JavaStubCompilationUnit;->hasAcceptedFileExtension(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 107
    invoke-super {p0, p1}, Lorg/codehaus/groovy/control/CompilationUnit;->addSource(Ljava/net/URL;)Lorg/codehaus/groovy/control/SourceUnit;

    move-result-object p1

    return-object p1

    :cond_0
    const/4 p1, 0x0

    return-object p1
.end method

.method public compile()V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lorg/codehaus/groovy/control/CompilationFailedException;
        }
    .end annotation

    const/4 v0, 0x0

    .line 81
    iput v0, p0, Lorg/codehaus/groovy/tools/javac/JavaStubCompilationUnit;->stubCount:I

    const/4 v0, 0x3

    .line 82
    invoke-super {p0, v0}, Lorg/codehaus/groovy/control/CompilationUnit;->compile(I)V

    return-void
.end method

.method public configure(Lorg/codehaus/groovy/control/CompilerConfiguration;)V
    .locals 1

    .line 87
    invoke-super {p0, p1}, Lorg/codehaus/groovy/control/CompilationUnit;->configure(Lorg/codehaus/groovy/control/CompilerConfiguration;)V

    .line 89
    iget-object p1, p0, Lorg/codehaus/groovy/tools/javac/JavaStubCompilationUnit;->configuration:Lorg/codehaus/groovy/control/CompilerConfiguration;

    invoke-virtual {p1}, Lorg/codehaus/groovy/control/CompilerConfiguration;->getTargetDirectory()Ljava/io/File;

    move-result-object p1

    if-eqz p1, :cond_0

    .line 91
    invoke-virtual {p1}, Ljava/io/File;->getAbsolutePath()Ljava/lang/String;

    move-result-object p1

    .line 92
    invoke-virtual {p0}, Lorg/codehaus/groovy/tools/javac/JavaStubCompilationUnit;->getClassLoader()Lgroovy/lang/GroovyClassLoader;

    move-result-object v0

    invoke-virtual {v0, p1}, Lgroovy/lang/GroovyClassLoader;->addClasspath(Ljava/lang/String;)V

    :cond_0
    return-void
.end method

.method public getStubCount()I
    .locals 1

    .line 76
    iget v0, p0, Lorg/codehaus/groovy/tools/javac/JavaStubCompilationUnit;->stubCount:I

    return v0
.end method

.method public synthetic lambda$new$0$org-codehaus-groovy-tools-javac-JavaStubCompilationUnit(Lorg/codehaus/groovy/control/SourceUnit;Lorg/codehaus/groovy/classgen/GeneratorContext;Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lorg/codehaus/groovy/control/CompilationFailedException;
        }
    .end annotation

    .line 57
    new-instance p2, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;

    invoke-direct {p2, p1}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;-><init>(Lorg/codehaus/groovy/control/SourceUnit;)V

    invoke-virtual {p2, p3}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->visitClass(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 58
    new-instance p2, Lorg/codehaus/groovy/tools/javac/JavaAwareResolveVisitor;

    invoke-direct {p2, p0}, Lorg/codehaus/groovy/tools/javac/JavaAwareResolveVisitor;-><init>(Lorg/codehaus/groovy/control/CompilationUnit;)V

    invoke-virtual {p2, p3, p1}, Lorg/codehaus/groovy/tools/javac/JavaAwareResolveVisitor;->startResolving(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/control/SourceUnit;)V

    return-void
.end method

.method public synthetic lambda$new$1$org-codehaus-groovy-tools-javac-JavaStubCompilationUnit(Lorg/codehaus/groovy/control/SourceUnit;Lorg/codehaus/groovy/classgen/GeneratorContext;Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lorg/codehaus/groovy/control/CompilationFailedException;
        }
    .end annotation

    .line 63
    :try_start_0
    iget-object p2, p0, Lorg/codehaus/groovy/tools/javac/JavaStubCompilationUnit;->stubGenerator:Lorg/codehaus/groovy/tools/javac/JavaStubGenerator;

    invoke-virtual {p2, p3}, Lorg/codehaus/groovy/tools/javac/JavaStubGenerator;->generateClass(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 64
    iget p2, p0, Lorg/codehaus/groovy/tools/javac/JavaStubCompilationUnit;->stubCount:I

    add-int/lit8 p2, p2, 0x1

    iput p2, p0, Lorg/codehaus/groovy/tools/javac/JavaStubCompilationUnit;->stubCount:I
    :try_end_0
    .catch Ljava/io/FileNotFoundException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception p2

    .line 66
    invoke-virtual {p1, p2}, Lorg/codehaus/groovy/control/SourceUnit;->addException(Ljava/lang/Exception;)V

    :goto_0
    return-void
.end method
