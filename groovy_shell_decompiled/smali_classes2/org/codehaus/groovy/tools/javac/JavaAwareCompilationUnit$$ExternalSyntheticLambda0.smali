.class public final synthetic Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit$$ExternalSyntheticLambda0;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Lorg/codehaus/groovy/control/CompilationUnit$IPrimaryClassNodeOperation;


# instance fields
.field public final synthetic f$0:Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit;


# direct methods
.method public synthetic constructor <init>(Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit$$ExternalSyntheticLambda0;->f$0:Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit;

    return-void
.end method


# virtual methods
.method public final call(Lorg/codehaus/groovy/control/SourceUnit;Lorg/codehaus/groovy/classgen/GeneratorContext;Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 1

    iget-object v0, p0, Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit$$ExternalSyntheticLambda0;->f$0:Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit;

    invoke-virtual {v0, p1, p2, p3}, Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit;->lambda$new$0$org-codehaus-groovy-tools-javac-JavaAwareCompilationUnit(Lorg/codehaus/groovy/control/SourceUnit;Lorg/codehaus/groovy/classgen/GeneratorContext;Lorg/codehaus/groovy/ast/ClassNode;)V

    return-void
.end method
