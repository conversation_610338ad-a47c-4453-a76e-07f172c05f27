.class public Lorg/codehaus/groovy/tools/javac/JavaAwareResolveVisitor;
.super Lorg/codehaus/groovy/control/ResolveVisitor;
.source "JavaAwareResolveVisitor.java"


# direct methods
.method public constructor <init>(Lorg/codehaus/groovy/control/CompilationUnit;)V
    .locals 0

    .line 33
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/control/ResolveVisitor;-><init>(Lorg/codehaus/groovy/control/CompilationUnit;)V

    return-void
.end method


# virtual methods
.method public addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V
    .locals 0

    return-void
.end method

.method protected visitClassCodeContainer(Lorg/codehaus/groovy/ast/stmt/Statement;)V
    .locals 0

    return-void
.end method

.method public visitConstructor(Lorg/codehaus/groovy/ast/ConstructorNode;)V
    .locals 0

    .line 38
    invoke-super {p0, p1}, Lorg/codehaus/groovy/control/ResolveVisitor;->visitConstructor(Lorg/codehaus/groovy/ast/ConstructorNode;)V

    .line 39
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ConstructorNode;->getCode()Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    .line 40
    invoke-static {p1}, Lorg/apache/groovy/ast/tools/ConstructorNodeUtils;->getFirstIfSpecialConstructorCall(Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;

    move-result-object p1

    if-nez p1, :cond_0

    return-void

    .line 42
    :cond_0
    invoke-virtual {p1, p0}, Lorg/codehaus/groovy/ast/expr/Expression;->visit(Lorg/codehaus/groovy/ast/GroovyCodeVisitor;)V

    return-void
.end method
