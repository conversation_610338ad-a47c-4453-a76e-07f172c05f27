.class public interface abstract Lorg/codehaus/groovy/tools/javac/JavaCompiler;
.super Ljava/lang/Object;
.source "JavaCompiler.java"


# virtual methods
.method public abstract compile(Ljava/util/List;Lorg/codehaus/groovy/control/CompilationUnit;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;",
            "Lorg/codehaus/groovy/control/CompilationUnit;",
            ")V"
        }
    .end annotation
.end method
