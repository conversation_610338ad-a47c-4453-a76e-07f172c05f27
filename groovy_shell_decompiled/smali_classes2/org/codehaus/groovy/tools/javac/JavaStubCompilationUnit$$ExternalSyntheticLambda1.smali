.class public final synthetic Lorg/codehaus/groovy/tools/javac/JavaStubCompilationUnit$$ExternalSyntheticLambda1;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Lorg/codehaus/groovy/control/CompilationUnit$IPrimaryClassNodeOperation;


# instance fields
.field public final synthetic f$0:Lorg/codehaus/groovy/tools/javac/JavaStubCompilationUnit;


# direct methods
.method public synthetic constructor <init>(Lorg/codehaus/groovy/tools/javac/JavaStubCompilationUnit;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/codehaus/groovy/tools/javac/JavaStubCompilationUnit$$ExternalSyntheticLambda1;->f$0:Lorg/codehaus/groovy/tools/javac/JavaStubCompilationUnit;

    return-void
.end method


# virtual methods
.method public final call(Lorg/codehaus/groovy/control/SourceUnit;Lorg/codehaus/groovy/classgen/GeneratorContext;Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 1

    iget-object v0, p0, Lorg/codehaus/groovy/tools/javac/JavaStubCompilationUnit$$ExternalSyntheticLambda1;->f$0:Lorg/codehaus/groovy/tools/javac/JavaStubCompilationUnit;

    invoke-virtual {v0, p1, p2, p3}, Lorg/codehaus/groovy/tools/javac/JavaStubCompilationUnit;->lambda$new$1$org-codehaus-groovy-tools-javac-JavaStubCompilationUnit(Lorg/codehaus/groovy/control/SourceUnit;Lorg/codehaus/groovy/classgen/GeneratorContext;Lorg/codehaus/groovy/ast/ClassNode;)V

    return-void
.end method
