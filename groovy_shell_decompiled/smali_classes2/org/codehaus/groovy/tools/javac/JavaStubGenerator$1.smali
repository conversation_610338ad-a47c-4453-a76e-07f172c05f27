.class Lorg/codehaus/groovy/tools/javac/JavaStubGenerator$1;
.super Lorg/codehaus/groovy/classgen/Verifier;
.source "JavaStubGenerator.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/codehaus/groovy/tools/javac/JavaStubGenerator;->printClassContents(Ljava/io/PrintWriter;Lorg/codehaus/groovy/ast/ClassNode;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lorg/codehaus/groovy/tools/javac/JavaStubGenerator;


# direct methods
.method constructor <init>(Lorg/codehaus/groovy/tools/javac/JavaStubGenerator;)V
    .locals 0

    .line 207
    iput-object p1, p0, Lorg/codehaus/groovy/tools/javac/JavaStubGenerator$1;->this$0:Lorg/codehaus/groovy/tools/javac/JavaStubGenerator;

    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/Verifier;-><init>()V

    return-void
.end method

.method private doAddMethod(Lorg/codehaus/groovy/ast/MethodNode;)Lorg/codehaus/groovy/ast/MethodNode;
    .locals 2

    .line 278
    iget-object v0, p0, Lorg/codehaus/groovy/tools/javac/JavaStubGenerator$1;->this$0:Lorg/codehaus/groovy/tools/javac/JavaStubGenerator;

    invoke-static {v0}, Lorg/codehaus/groovy/tools/javac/JavaStubGenerator;->access$200(Lorg/codehaus/groovy/tools/javac/JavaStubGenerator;)Ljava/util/Map;

    move-result-object v0

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getTypeDescriptor()Ljava/lang/String;

    move-result-object v1

    invoke-interface {v0, v1, p1}, Ljava/util/Map;->putIfAbsent(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-object p1
.end method


# virtual methods
.method protected addConstructor([Lorg/codehaus/groovy/ast/Parameter;Lorg/codehaus/groovy/ast/ConstructorNode;Lorg/codehaus/groovy/ast/stmt/Statement;Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 2

    .line 253
    instance-of v0, p3, Lorg/codehaus/groovy/ast/stmt/ExpressionStatement;

    if-eqz v0, :cond_0

    .line 255
    new-instance v0, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    invoke-direct {v0}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;-><init>()V

    .line 256
    move-object v1, v0

    check-cast v1, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    invoke-virtual {v0, p3}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    move-object p3, v0

    .line 258
    :cond_0
    new-instance v0, Lorg/codehaus/groovy/ast/ConstructorNode;

    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/ConstructorNode;->getModifiers()I

    move-result v1

    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/ConstructorNode;->getExceptions()[Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p2

    invoke-direct {v0, v1, p1, p2, p3}, Lorg/codehaus/groovy/ast/ConstructorNode;-><init>(I[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 259
    invoke-virtual {v0, p4}, Lorg/codehaus/groovy/ast/ConstructorNode;->setDeclaringClass(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 260
    iget-object p1, p0, Lorg/codehaus/groovy/tools/javac/JavaStubGenerator$1;->this$0:Lorg/codehaus/groovy/tools/javac/JavaStubGenerator;

    invoke-static {p1}, Lorg/codehaus/groovy/tools/javac/JavaStubGenerator;->access$100(Lorg/codehaus/groovy/tools/javac/JavaStubGenerator;)Ljava/util/List;

    move-result-object p1

    invoke-interface {p1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public addCovariantMethods(Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 0

    return-void
.end method

.method protected addDefaultConstructor(Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 0

    return-void
.end method

.method protected addDefaultParameters(Lorg/codehaus/groovy/classgen/Verifier$DefaultArgsAction;Lorg/codehaus/groovy/ast/MethodNode;)V
    .locals 5

    .line 264
    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/MethodNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v0

    .line 265
    array-length v1, v0

    new-array v1, v1, [Lorg/codehaus/groovy/ast/expr/Expression;

    const/4 v2, 0x0

    move v3, v2

    .line 266
    :goto_0
    array-length v4, v0

    if-ge v3, v4, :cond_1

    .line 267
    aget-object v4, v0, v3

    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/Parameter;->hasInitialExpression()Z

    move-result v4

    if-eqz v4, :cond_0

    .line 268
    aget-object v4, v0, v3

    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/Parameter;->getInitialExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v4

    aput-object v4, v1, v3

    :cond_0
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    .line 270
    :cond_1
    invoke-super {p0, p1, p2}, Lorg/codehaus/groovy/classgen/Verifier;->addDefaultParameters(Lorg/codehaus/groovy/classgen/Verifier$DefaultArgsAction;Lorg/codehaus/groovy/ast/MethodNode;)V

    .line 271
    :goto_1
    array-length p1, v0

    if-ge v2, p1, :cond_3

    .line 272
    aget-object p1, v1, v2

    if-eqz p1, :cond_2

    .line 273
    aget-object p1, v0, v2

    aget-object p2, v1, v2

    invoke-virtual {p1, p2}, Lorg/codehaus/groovy/ast/Parameter;->setInitialExpression(Lorg/codehaus/groovy/ast/expr/Expression;)V

    :cond_2
    add-int/lit8 v2, v2, 0x1

    goto :goto_1

    :cond_3
    return-void
.end method

.method protected addInitialization(Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 0

    return-void
.end method

.method protected addMethod(Lorg/codehaus/groovy/ast/ClassNode;ZLjava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/MethodNode;
    .locals 7

    .line 249
    new-instance p1, Lorg/codehaus/groovy/ast/MethodNode;

    move-object v0, p1

    move-object v1, p3

    move v2, p4

    move-object v3, p5

    move-object v4, p6

    move-object v5, p7

    move-object v6, p8

    invoke-direct/range {v0 .. v6}, Lorg/codehaus/groovy/ast/MethodNode;-><init>(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)V

    invoke-direct {p0, p1}, Lorg/codehaus/groovy/tools/javac/JavaStubGenerator$1;->doAddMethod(Lorg/codehaus/groovy/ast/MethodNode;)Lorg/codehaus/groovy/ast/MethodNode;

    move-result-object p1

    return-object p1
.end method

.method protected addPropertyMethod(Lorg/codehaus/groovy/ast/MethodNode;)V
    .locals 0

    .line 245
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/tools/javac/JavaStubGenerator$1;->doAddMethod(Lorg/codehaus/groovy/ast/MethodNode;)Lorg/codehaus/groovy/ast/MethodNode;

    return-void
.end method

.method protected addReturnIfNeeded(Lorg/codehaus/groovy/ast/MethodNode;)V
    .locals 0

    return-void
.end method

.method protected getFinalVariablesCallback()Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableNotFinalCallback;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public visitClass(Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 5

    .line 210
    new-instance v0, Ljava/util/ArrayList;

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getObjectInitializerStatements()Ljava/util/List;

    move-result-object v1

    invoke-direct {v0, v1}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    .line 211
    invoke-super {p0, p1}, Lorg/codehaus/groovy/classgen/Verifier;->visitClass(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 212
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getObjectInitializerStatements()Ljava/util/List;

    move-result-object v1

    invoke-interface {v1, v0}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 214
    invoke-static {p1}, Lorg/codehaus/groovy/tools/javac/JavaStubGenerator;->access$000(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/lang/Iterable;

    move-result-object p1

    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_2

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/ast/ClassNode;

    .line 216
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->isUsingGenerics()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GenericsUtils;->createGenericsSpec(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/util/Map;

    move-result-object v1

    goto :goto_0

    :cond_1
    const/4 v1, 0x0

    .line 217
    :goto_0
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->getProperties()Ljava/util/List;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_1
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_0

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lorg/codehaus/groovy/ast/PropertyNode;

    .line 218
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/PropertyNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v3

    .line 219
    invoke-static {v1, v3}, Lorg/codehaus/groovy/ast/tools/GenericsUtils;->correctToGenericsSpecRecurse(Ljava/util/Map;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v4

    invoke-virtual {v2, v4}, Lorg/codehaus/groovy/ast/PropertyNode;->setType(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 220
    invoke-super {p0, v2}, Lorg/codehaus/groovy/classgen/Verifier;->visitProperty(Lorg/codehaus/groovy/ast/PropertyNode;)V

    .line 221
    invoke-virtual {v2, v3}, Lorg/codehaus/groovy/ast/PropertyNode;->setType(Lorg/codehaus/groovy/ast/ClassNode;)V

    goto :goto_1

    :cond_2
    return-void
.end method

.method public visitConstructor(Lorg/codehaus/groovy/ast/ConstructorNode;)V
    .locals 2

    .line 228
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ConstructorNode;->getCode()Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    if-eqz p1, :cond_0

    .line 230
    new-instance v0, Lorg/codehaus/groovy/classgen/VerifierCodeVisitor;

    invoke-virtual {p0}, Lorg/codehaus/groovy/tools/javac/JavaStubGenerator$1;->getClassNode()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    invoke-direct {v0, v1}, Lorg/codehaus/groovy/classgen/VerifierCodeVisitor;-><init>(Lorg/codehaus/groovy/ast/ClassNode;)V

    invoke-virtual {p1, v0}, Lorg/codehaus/groovy/ast/stmt/Statement;->visit(Lorg/codehaus/groovy/ast/GroovyCodeVisitor;)V

    :cond_0
    return-void
.end method

.method public visitProperty(Lorg/codehaus/groovy/ast/PropertyNode;)V
    .locals 1

    .line 237
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/PropertyNode;->isStatic()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/PropertyNode;->getDeclaringClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/transform/trait/Traits;->isTrait(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v0

    if-nez v0, :cond_1

    .line 238
    :cond_0
    invoke-super {p0, p1}, Lorg/codehaus/groovy/classgen/Verifier;->visitProperty(Lorg/codehaus/groovy/ast/PropertyNode;)V

    :cond_1
    return-void
.end method
