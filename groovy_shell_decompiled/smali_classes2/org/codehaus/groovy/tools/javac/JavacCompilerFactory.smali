.class public Lorg/codehaus/groovy/tools/javac/JavacCompilerFactory;
.super Ljava/lang/Object;
.source "JavacCompilerFactory.java"

# interfaces
.implements Lorg/codehaus/groovy/tools/javac/JavaCompilerFactory;


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 23
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public createCompiler(Lorg/codehaus/groovy/control/CompilerConfiguration;)Lorg/codehaus/groovy/tools/javac/JavaCompiler;
    .locals 1

    .line 25
    new-instance v0, Lorg/codehaus/groovy/tools/javac/JavacJavaCompiler;

    invoke-direct {v0, p1}, Lorg/codehaus/groovy/tools/javac/JavacJavaCompiler;-><init>(Lorg/codehaus/groovy/control/CompilerConfiguration;)V

    return-object v0
.end method
