.class public final synthetic Lorg/codehaus/groovy/tools/javac/JavaStubGenerator$$ExternalSyntheticLambda0;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Consumer;


# static fields
.field public static final synthetic INSTANCE:Lorg/codehaus/groovy/tools/javac/JavaStubGenerator$$ExternalSyntheticLambda0;


# direct methods
.method static synthetic constructor <clinit>()V
    .locals 1

    new-instance v0, Lorg/codehaus/groovy/tools/javac/JavaStubGenerator$$ExternalSyntheticLambda0;

    invoke-direct {v0}, Lorg/codehaus/groovy/tools/javac/JavaStubGenerator$$ExternalSyntheticLambda0;-><init>()V

    sput-object v0, Lorg/codehaus/groovy/tools/javac/JavaStubGenerator$$ExternalSyntheticLambda0;->INSTANCE:Lorg/codehaus/groovy/tools/javac/JavaStubGenerator$$ExternalSyntheticLambda0;

    return-void
.end method

.method private synthetic constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final accept(Ljava/lang/Object;)V
    .locals 0

    check-cast p1, Ljavax/tools/JavaFileObject;

    invoke-interface {p1}, Ljavax/tools/FileObject;->delete()Z

    return-void
.end method
