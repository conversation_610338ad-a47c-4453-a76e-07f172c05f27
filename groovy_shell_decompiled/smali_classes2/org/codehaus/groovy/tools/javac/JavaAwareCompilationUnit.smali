.class public Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit;
.super Lorg/codehaus/groovy/control/CompilationUnit;
.source "JavaAwareCompilationUnit.java"


# instance fields
.field private compilerFactory:Lorg/codehaus/groovy/tools/javac/JavaCompilerFactory;

.field private final generationGoal:Ljava/io/File;

.field private final javaSources:Ljava/util/Set;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Set<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field private final keepStubs:Z

.field private final memStubEnabled:Z

.field private final stubGenerator:Lorg/codehaus/groovy/tools/javac/JavaStubGenerator;


# direct methods
.method public constructor <init>()V
    .locals 1

    const/4 v0, 0x0

    .line 56
    invoke-direct {p0, v0, v0, v0}, Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit;-><init>(Lorg/codehaus/groovy/control/CompilerConfiguration;Lgroovy/lang/GroovyClassLoader;Lgroovy/lang/GroovyClassLoader;)V

    return-void
.end method

.method public constructor <init>(Lorg/codehaus/groovy/control/CompilerConfiguration;)V
    .locals 1

    const/4 v0, 0x0

    .line 60
    invoke-direct {p0, p1, v0, v0}, Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit;-><init>(Lorg/codehaus/groovy/control/CompilerConfiguration;Lgroovy/lang/GroovyClassLoader;Lgroovy/lang/GroovyClassLoader;)V

    return-void
.end method

.method public constructor <init>(Lorg/codehaus/groovy/control/CompilerConfiguration;Lgroovy/lang/GroovyClassLoader;)V
    .locals 1

    const/4 v0, 0x0

    .line 64
    invoke-direct {p0, p1, p2, v0}, Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit;-><init>(Lorg/codehaus/groovy/control/CompilerConfiguration;Lgroovy/lang/GroovyClassLoader;Lgroovy/lang/GroovyClassLoader;)V

    return-void
.end method

.method public constructor <init>(Lorg/codehaus/groovy/control/CompilerConfiguration;Lgroovy/lang/GroovyClassLoader;Lgroovy/lang/GroovyClassLoader;)V
    .locals 5

    const/4 v0, 0x0

    .line 68
    invoke-direct {p0, p1, v0, p2, p3}, Lorg/codehaus/groovy/control/CompilationUnit;-><init>(Lorg/codehaus/groovy/control/CompilerConfiguration;Ljava/security/CodeSource;Lgroovy/lang/GroovyClassLoader;Lgroovy/lang/GroovyClassLoader;)V

    .line 49
    new-instance p1, Ljava/util/HashSet;

    invoke-direct {p1}, Ljava/util/HashSet;-><init>()V

    iput-object p1, p0, Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit;->javaSources:Ljava/util/Set;

    .line 50
    new-instance p1, Lorg/codehaus/groovy/tools/javac/JavacCompilerFactory;

    invoke-direct {p1}, Lorg/codehaus/groovy/tools/javac/JavacCompilerFactory;-><init>()V

    iput-object p1, p0, Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit;->compilerFactory:Lorg/codehaus/groovy/tools/javac/JavaCompilerFactory;

    .line 71
    iget-object p1, p0, Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit;->configuration:Lorg/codehaus/groovy/control/CompilerConfiguration;

    invoke-virtual {p1}, Lorg/codehaus/groovy/control/CompilerConfiguration;->getJointCompilationOptions()Ljava/util/Map;

    move-result-object p1

    .line 73
    iget-object p2, p0, Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit;->configuration:Lorg/codehaus/groovy/control/CompilerConfiguration;

    invoke-virtual {p2}, Lorg/codehaus/groovy/control/CompilerConfiguration;->getTargetBytecode()Ljava/lang/String;

    move-result-object p2

    invoke-static {p2}, Lorg/codehaus/groovy/control/CompilerConfiguration;->isPostJDK5(Ljava/lang/String;)Z

    move-result p2

    .line 74
    iget-object p3, p0, Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit;->configuration:Lorg/codehaus/groovy/control/CompilerConfiguration;

    invoke-virtual {p3}, Lorg/codehaus/groovy/control/CompilerConfiguration;->getSourceEncoding()Ljava/lang/String;

    move-result-object p3

    const-string v1, "memStub"

    .line 75
    invoke-interface {p1, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    const/4 v3, 0x0

    if-nez v2, :cond_2

    const-string v2, "groovy.mem.stub"

    const-string v4, "false"

    .line 77
    invoke-static {v2, v4}, Lorg/apache/groovy/util/SystemUtil;->getSystemPropertySafe(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    invoke-static {v2}, Ljava/lang/Boolean;->parseBoolean(Ljava/lang/String;)Z

    move-result v2

    if-nez v2, :cond_1

    const-string v2, "groovy.generate.stub.in.memory"

    .line 79
    invoke-static {v2, v4}, Lorg/apache/groovy/util/SystemUtil;->getSystemPropertySafe(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    invoke-static {v2}, Ljava/lang/Boolean;->parseBoolean(Ljava/lang/String;)Z

    move-result v2

    if-eqz v2, :cond_0

    goto :goto_0

    :cond_0
    move v2, v3

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v2, 0x1

    .line 77
    :goto_1
    invoke-static {v2}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v2

    .line 80
    invoke-interface {p1, v1, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 83
    :cond_2
    sget-object v1, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    const-string v4, "keepStubs"

    invoke-interface {p1, v4}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    invoke-virtual {v1, v4}, Ljava/lang/Boolean;->equals(Ljava/lang/Object;)Z

    move-result v1

    iput-boolean v1, p0, Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit;->keepStubs:Z

    .line 84
    sget-object v1, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    invoke-virtual {v1, v2}, Ljava/lang/Boolean;->equals(Ljava/lang/Object;)Z

    move-result v1

    iput-boolean v1, p0, Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit;->memStubEnabled:Z

    if-eqz v1, :cond_3

    goto :goto_2

    :cond_3
    const-string v0, "stubDir"

    .line 85
    invoke-interface {p1, v0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    move-object v0, p1

    check-cast v0, Ljava/io/File;

    :goto_2
    iput-object v0, p0, Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit;->generationGoal:Ljava/io/File;

    .line 86
    new-instance p1, Lorg/codehaus/groovy/tools/javac/JavaStubGenerator;

    invoke-direct {p1, v0, v3, p2, p3}, Lorg/codehaus/groovy/tools/javac/JavaStubGenerator;-><init>(Ljava/io/File;ZZLjava/lang/String;)V

    iput-object p1, p0, Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit;->stubGenerator:Lorg/codehaus/groovy/tools/javac/JavaStubGenerator;

    .line 89
    new-instance p1, Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit$$ExternalSyntheticLambda0;

    invoke-direct {p1, p0}, Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit$$ExternalSyntheticLambda0;-><init>(Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit;)V

    const/4 p2, 0x3

    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit;->addPhaseOperation(Lorg/codehaus/groovy/control/CompilationUnit$IPrimaryClassNodeOperation;I)V

    .line 97
    new-instance p1, Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit$$ExternalSyntheticLambda1;

    invoke-direct {p1, p0}, Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit$$ExternalSyntheticLambda1;-><init>(Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit;)V

    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit;->addPhaseOperation(Lorg/codehaus/groovy/control/CompilationUnit$IPrimaryClassNodeOperation;I)V

    .line 102
    new-instance p1, Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit$$ExternalSyntheticLambda2;

    invoke-direct {p1, p0}, Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit$$ExternalSyntheticLambda2;-><init>(Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit;)V

    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit;->addPhaseOperation(Lorg/codehaus/groovy/control/CompilationUnit$IPrimaryClassNodeOperation;I)V

    return-void
.end method

.method private addJavaOrGroovySource(Ljava/io/File;)V
    .locals 2

    .line 160
    invoke-virtual {p1}, Ljava/io/File;->getName()Ljava/lang/String;

    move-result-object v0

    const-string v1, ".java"

    invoke-virtual {v0, v1}, Ljava/lang/String;->endsWith(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 161
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit;->addJavaSource(Ljava/io/File;)V

    goto :goto_0

    .line 163
    :cond_0
    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit;->addSource(Ljava/io/File;)Lorg/codehaus/groovy/control/SourceUnit;

    :goto_0
    return-void
.end method

.method private addJavaSource(Ljava/io/File;)V
    .locals 1

    .line 142
    iget-object v0, p0, Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit;->javaSources:Ljava/util/Set;

    invoke-virtual {p1}, Ljava/io/File;->getAbsolutePath()Ljava/lang/String;

    move-result-object p1

    invoke-interface {v0, p1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    return-void
.end method


# virtual methods
.method public addSources([Ljava/io/File;)V
    .locals 3

    .line 154
    array-length v0, p1

    const/4 v1, 0x0

    :goto_0
    if-ge v1, v0, :cond_0

    aget-object v2, p1, v1

    .line 155
    invoke-direct {p0, v2}, Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit;->addJavaOrGroovySource(Ljava/io/File;)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    return-void
.end method

.method public addSources([Ljava/lang/String;)V
    .locals 4

    .line 147
    array-length v0, p1

    const/4 v1, 0x0

    :goto_0
    if-ge v1, v0, :cond_0

    aget-object v2, p1, v1

    .line 148
    new-instance v3, Ljava/io/File;

    invoke-direct {v3, v2}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    invoke-direct {p0, v3}, Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit;->addJavaOrGroovySource(Ljava/io/File;)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    return-void
.end method

.method public configure(Lorg/codehaus/groovy/control/CompilerConfiguration;)V
    .locals 1

    .line 132
    invoke-super {p0, p1}, Lorg/codehaus/groovy/control/CompilationUnit;->configure(Lorg/codehaus/groovy/control/CompilerConfiguration;)V

    .line 134
    iget-object p1, p0, Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit;->configuration:Lorg/codehaus/groovy/control/CompilerConfiguration;

    invoke-virtual {p1}, Lorg/codehaus/groovy/control/CompilerConfiguration;->getTargetDirectory()Ljava/io/File;

    move-result-object p1

    if-eqz p1, :cond_0

    .line 136
    invoke-virtual {p1}, Ljava/io/File;->getAbsolutePath()Ljava/lang/String;

    move-result-object p1

    .line 137
    invoke-virtual {p0}, Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit;->getClassLoader()Lgroovy/lang/GroovyClassLoader;

    move-result-object v0

    invoke-virtual {v0, p1}, Lgroovy/lang/GroovyClassLoader;->addClasspath(Ljava/lang/String;)V

    :cond_0
    return-void
.end method

.method public getCompilerFactory()Lorg/codehaus/groovy/tools/javac/JavaCompilerFactory;
    .locals 1

    .line 168
    iget-object v0, p0, Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit;->compilerFactory:Lorg/codehaus/groovy/tools/javac/JavaCompilerFactory;

    return-object v0
.end method

.method public gotoPhase(I)V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lorg/codehaus/groovy/control/CompilationFailedException;
        }
    .end annotation

    .line 113
    invoke-super {p0, p1}, Lorg/codehaus/groovy/control/CompilationUnit;->gotoPhase(I)V

    const/4 v0, 0x4

    if-ne p1, v0, :cond_3

    .line 115
    iget-object p1, p0, Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit;->javaSources:Ljava/util/Set;

    invoke-interface {p1}, Ljava/util/Set;->isEmpty()Z

    move-result p1

    if-nez p1, :cond_3

    .line 116
    invoke-virtual {p0}, Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit;->getAST()Lorg/codehaus/groovy/ast/CompileUnit;

    move-result-object p1

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/CompileUnit;->getModules()Ljava/util/List;

    move-result-object p1

    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/ast/ModuleNode;

    const/4 v1, 0x0

    .line 117
    invoke-virtual {v0, v1}, Lorg/codehaus/groovy/ast/ModuleNode;->setImportsResolved(Z)V

    goto :goto_0

    .line 120
    :cond_0
    :try_start_0
    iget-object p1, p0, Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit;->stubGenerator:Lorg/codehaus/groovy/tools/javac/JavaStubGenerator;

    invoke-virtual {p1}, Lorg/codehaus/groovy/tools/javac/JavaStubGenerator;->getJavaStubCompilationUnitSet()Ljava/util/Set;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit;->addJavaCompilationUnits(Ljava/util/Set;)V

    .line 121
    iget-object p1, p0, Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit;->compilerFactory:Lorg/codehaus/groovy/tools/javac/JavaCompilerFactory;

    iget-object v0, p0, Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit;->configuration:Lorg/codehaus/groovy/control/CompilerConfiguration;

    invoke-interface {p1, v0}, Lorg/codehaus/groovy/tools/javac/JavaCompilerFactory;->createCompiler(Lorg/codehaus/groovy/control/CompilerConfiguration;)Lorg/codehaus/groovy/tools/javac/JavaCompiler;

    move-result-object p1

    .line 122
    new-instance v0, Ljava/util/ArrayList;

    iget-object v1, p0, Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit;->javaSources:Ljava/util/Set;

    invoke-direct {v0, v1}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    invoke-interface {p1, v0, p0}, Lorg/codehaus/groovy/tools/javac/JavaCompiler;->compile(Ljava/util/List;Lorg/codehaus/groovy/control/CompilationUnit;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 124
    iget-boolean p1, p0, Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit;->keepStubs:Z

    if-nez p1, :cond_1

    iget-object p1, p0, Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit;->stubGenerator:Lorg/codehaus/groovy/tools/javac/JavaStubGenerator;

    invoke-virtual {p1}, Lorg/codehaus/groovy/tools/javac/JavaStubGenerator;->clean()V

    .line 125
    :cond_1
    iget-object p1, p0, Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit;->javaSources:Ljava/util/Set;

    invoke-interface {p1}, Ljava/util/Set;->clear()V

    goto :goto_1

    :catchall_0
    move-exception p1

    .line 124
    iget-boolean v0, p0, Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit;->keepStubs:Z

    if-nez v0, :cond_2

    iget-object v0, p0, Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit;->stubGenerator:Lorg/codehaus/groovy/tools/javac/JavaStubGenerator;

    invoke-virtual {v0}, Lorg/codehaus/groovy/tools/javac/JavaStubGenerator;->clean()V

    .line 125
    :cond_2
    iget-object v0, p0, Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit;->javaSources:Ljava/util/Set;

    invoke-interface {v0}, Ljava/util/Set;->clear()V

    .line 126
    throw p1

    :cond_3
    :goto_1
    return-void
.end method

.method public synthetic lambda$new$0$org-codehaus-groovy-tools-javac-JavaAwareCompilationUnit(Lorg/codehaus/groovy/control/SourceUnit;Lorg/codehaus/groovy/classgen/GeneratorContext;Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lorg/codehaus/groovy/control/CompilationFailedException;
        }
    .end annotation

    .line 90
    iget-object p2, p0, Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit;->javaSources:Ljava/util/Set;

    invoke-interface {p2}, Ljava/util/Set;->isEmpty()Z

    move-result p2

    if-nez p2, :cond_0

    .line 91
    new-instance p2, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;

    invoke-direct {p2, p1}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;-><init>(Lorg/codehaus/groovy/control/SourceUnit;)V

    invoke-virtual {p2, p3}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->visitClass(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 92
    new-instance p2, Lorg/codehaus/groovy/tools/javac/JavaAwareResolveVisitor;

    invoke-direct {p2, p0}, Lorg/codehaus/groovy/tools/javac/JavaAwareResolveVisitor;-><init>(Lorg/codehaus/groovy/control/CompilationUnit;)V

    invoke-virtual {p2, p3, p1}, Lorg/codehaus/groovy/tools/javac/JavaAwareResolveVisitor;->startResolving(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/control/SourceUnit;)V

    .line 93
    new-instance p2, Lorg/codehaus/groovy/control/AnnotationConstantsVisitor;

    invoke-direct {p2}, Lorg/codehaus/groovy/control/AnnotationConstantsVisitor;-><init>()V

    invoke-virtual {p2, p3, p1}, Lorg/codehaus/groovy/control/AnnotationConstantsVisitor;->visitClass(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/control/SourceUnit;)V

    :cond_0
    return-void
.end method

.method public synthetic lambda$new$1$org-codehaus-groovy-tools-javac-JavaAwareCompilationUnit(Lorg/codehaus/groovy/control/SourceUnit;Lorg/codehaus/groovy/classgen/GeneratorContext;Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lorg/codehaus/groovy/control/CompilationFailedException;
        }
    .end annotation

    .line 98
    new-instance p2, Lorg/codehaus/groovy/transform/ASTTransformationCollectorCodeVisitor;

    invoke-virtual {p0}, Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit;->getTransformLoader()Lgroovy/lang/GroovyClassLoader;

    move-result-object v0

    invoke-direct {p2, p1, v0}, Lorg/codehaus/groovy/transform/ASTTransformationCollectorCodeVisitor;-><init>(Lorg/codehaus/groovy/control/SourceUnit;Lgroovy/lang/GroovyClassLoader;)V

    .line 99
    invoke-interface {p2, p3}, Lorg/codehaus/groovy/ast/GroovyClassVisitor;->visitClass(Lorg/codehaus/groovy/ast/ClassNode;)V

    return-void
.end method

.method public synthetic lambda$new$2$org-codehaus-groovy-tools-javac-JavaAwareCompilationUnit(Lorg/codehaus/groovy/control/SourceUnit;Lorg/codehaus/groovy/classgen/GeneratorContext;Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lorg/codehaus/groovy/control/CompilationFailedException;
        }
    .end annotation

    .line 104
    :try_start_0
    iget-object p2, p0, Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit;->javaSources:Ljava/util/Set;

    invoke-interface {p2}, Ljava/util/Set;->isEmpty()Z

    move-result p2

    if-nez p2, :cond_0

    iget-object p2, p0, Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit;->stubGenerator:Lorg/codehaus/groovy/tools/javac/JavaStubGenerator;

    invoke-virtual {p2, p3}, Lorg/codehaus/groovy/tools/javac/JavaStubGenerator;->generateClass(Lorg/codehaus/groovy/ast/ClassNode;)V
    :try_end_0
    .catch Ljava/io/FileNotFoundException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception p2

    .line 106
    invoke-virtual {p1, p2}, Lorg/codehaus/groovy/control/SourceUnit;->addException(Ljava/lang/Exception;)V

    :cond_0
    :goto_0
    return-void
.end method

.method public setCompilerFactory(Lorg/codehaus/groovy/tools/javac/JavaCompilerFactory;)V
    .locals 0

    .line 172
    iput-object p1, p0, Lorg/codehaus/groovy/tools/javac/JavaAwareCompilationUnit;->compilerFactory:Lorg/codehaus/groovy/tools/javac/JavaCompilerFactory;

    return-void
.end method
