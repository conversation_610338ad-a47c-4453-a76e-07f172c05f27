.class public Lorg/codehaus/groovy/tools/javac/JavacJavaCompiler;
.super Ljava/lang/Object;
.source "JavacJavaCompiler.java"

# interfaces
.implements Lorg/codehaus/groovy/tools/javac/JavaCompiler;


# static fields
.field private static final DEFAULT_LOCALE:Ljava/util/Locale;


# instance fields
.field private final charset:Ljava/nio/charset/Charset;

.field private final config:Lorg/codehaus/groovy/control/CompilerConfiguration;


# direct methods
.method public static synthetic $r8$lambda$9ujlb-IU2rou71voK9NsDp7pLBI(Ljava/lang/String;)Ljava/io/File;
    .locals 1

    new-instance v0, Ljava/io/File;

    invoke-direct {v0, p0}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    return-object v0
.end method

.method static constructor <clinit>()V
    .locals 1

    .line 51
    sget-object v0, Ljava/util/Locale;->ENGLISH:Ljava/util/Locale;

    sput-object v0, Lorg/codehaus/groovy/tools/javac/JavacJavaCompiler;->DEFAULT_LOCALE:Ljava/util/Locale;

    return-void
.end method

.method public constructor <init>(Lorg/codehaus/groovy/control/CompilerConfiguration;)V
    .locals 0

    .line 55
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 56
    iput-object p1, p0, Lorg/codehaus/groovy/tools/javac/JavacJavaCompiler;->config:Lorg/codehaus/groovy/control/CompilerConfiguration;

    .line 57
    invoke-virtual {p1}, Lorg/codehaus/groovy/control/CompilerConfiguration;->getSourceEncoding()Ljava/lang/String;

    move-result-object p1

    invoke-static {p1}, Ljava/nio/charset/Charset;->forName(Ljava/lang/String;)Ljava/nio/charset/Charset;

    move-result-object p1

    iput-object p1, p0, Lorg/codehaus/groovy/tools/javac/JavacJavaCompiler;->charset:Ljava/nio/charset/Charset;

    return-void
.end method

.method private static addJavacError(Ljava/lang/String;Lorg/codehaus/groovy/control/CompilationUnit;Lorg/apache/groovy/io/StringBuilderWriter;)V
    .locals 1

    if-eqz p2, :cond_0

    .line 133
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    const-string v0, "\n"

    invoke-virtual {p0, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p2}, Lorg/apache/groovy/io/StringBuilderWriter;->getBuilder()Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    goto :goto_0

    .line 135
    :cond_0
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p2, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    const-string p2, "\nThis javac version does not support compile(String[],PrintWriter), so no further details of the error are available. The message error text should be found on System.err.\n"

    invoke-virtual {p0, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    .line 140
    :goto_0
    invoke-virtual {p1}, Lorg/codehaus/groovy/control/CompilationUnit;->getErrorCollector()Lorg/codehaus/groovy/control/ErrorCollector;

    move-result-object p2

    new-instance v0, Lorg/codehaus/groovy/control/messages/SimpleMessage;

    invoke-direct {v0, p0, p1}, Lorg/codehaus/groovy/control/messages/SimpleMessage;-><init>(Ljava/lang/String;Lorg/codehaus/groovy/control/ProcessingUnit;)V

    invoke-virtual {p2, v0}, Lorg/codehaus/groovy/control/ErrorCollector;->addFatalError(Lorg/codehaus/groovy/control/messages/Message;)V

    return-void
.end method

.method private doCompileWithSystemJavaCompiler(Lorg/codehaus/groovy/control/CompilationUnit;Ljava/util/List;Ljava/util/List;Lorg/apache/groovy/io/StringBuilderWriter;)Z
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/control/CompilationUnit;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;",
            "Lorg/apache/groovy/io/StringBuilderWriter;",
            ")Z"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 94
    invoke-static {}, Ljavax/tools/ToolProvider;->getSystemJavaCompiler()Ljavax/tools/JavaCompiler;

    move-result-object v0

    .line 95
    sget-object v7, Lorg/codehaus/groovy/tools/javac/JavacJavaCompiler;->DEFAULT_LOCALE:Ljava/util/Locale;

    iget-object v1, p0, Lorg/codehaus/groovy/tools/javac/JavacJavaCompiler;->charset:Ljava/nio/charset/Charset;

    const/4 v2, 0x0

    invoke-interface {v0, v2, v7, v1}, Ljavax/tools/JavaCompiler;->getStandardFileManager(Ljavax/tools/DiagnosticListener;Ljava/util/Locale;Ljava/nio/charset/Charset;)Ljavax/tools/StandardJavaFileManager;

    move-result-object v8

    .line 96
    :try_start_0
    invoke-virtual {p1}, Lorg/codehaus/groovy/control/CompilationUnit;->getJavaCompilationUnitSet()Ljava/util/Set;

    move-result-object p1

    .line 98
    iget-object v1, p0, Lorg/codehaus/groovy/tools/javac/JavacJavaCompiler;->config:Lorg/codehaus/groovy/control/CompilerConfiguration;

    invoke-virtual {v1}, Lorg/codehaus/groovy/control/CompilerConfiguration;->getJointCompilationOptions()Ljava/util/Map;

    move-result-object v1

    const-string v2, "memStub"

    .line 99
    invoke-interface {v1, v2}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-static {v2}, Ljava/lang/Boolean;->parseBoolean(Ljava/lang/String;)Z

    move-result v2

    if-nez v2, :cond_1

    .line 101
    new-instance p1, Ljava/util/HashSet;

    invoke-direct {p1}, Ljava/util/HashSet;-><init>()V

    const-string v2, "-sourcepath"

    .line 104
    invoke-interface {p3, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    const-string v2, "stubDir"

    .line 105
    invoke-interface {v1, v2}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/io/File;

    if-eqz v1, :cond_0

    .line 109
    invoke-virtual {v1}, Ljava/io/File;->getAbsolutePath()Ljava/lang/String;

    move-result-object v1

    invoke-interface {p3, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    .line 107
    :cond_0
    new-instance p1, Lorg/codehaus/groovy/GroovyBugError;

    const-string p2, "stubDir is not specified"

    invoke-direct {p1, p2}, Lorg/codehaus/groovy/GroovyBugError;-><init>(Ljava/lang/String;)V

    throw p1

    :cond_1
    :goto_0
    move-object v6, p1

    .line 114
    invoke-interface {p2}, Ljava/util/List;->stream()Ljava/util/stream/Stream;

    move-result-object p1

    sget-object p2, Lorg/codehaus/groovy/tools/javac/JavacJavaCompiler$$ExternalSyntheticLambda2;->INSTANCE:Lorg/codehaus/groovy/tools/javac/JavacJavaCompiler$$ExternalSyntheticLambda2;

    invoke-interface {p1, p2}, Ljava/util/stream/Stream;->map(Ljava/util/function/Function;)Ljava/util/stream/Stream;

    move-result-object p1

    invoke-static {}, Ljava/util/stream/Collectors;->toList()Ljava/util/stream/Collector;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/stream/Stream;->collect(Ljava/util/stream/Collector;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/Iterable;

    .line 113
    invoke-interface {v8, p1}, Ljavax/tools/StandardJavaFileManager;->getJavaFileObjectsFromFiles(Ljava/lang/Iterable;)Ljava/lang/Iterable;

    move-result-object p1

    .line 115
    invoke-static {v6}, Ljava/util/Objects;->requireNonNull(Ljava/lang/Object;)Ljava/lang/Object;

    new-instance p2, Lorg/codehaus/groovy/tools/javac/JavacJavaCompiler$$ExternalSyntheticLambda1;

    invoke-direct {p2, v6}, Lorg/codehaus/groovy/tools/javac/JavacJavaCompiler$$ExternalSyntheticLambda1;-><init>(Ljava/util/Set;)V

    invoke-interface {p1, p2}, Ljava/lang/Iterable;->forEach(Ljava/util/function/Consumer;)V

    const/4 v3, 0x0

    .line 122
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v5

    move-object v1, p4

    move-object v2, v8

    move-object v4, p3

    .line 117
    invoke-interface/range {v0 .. v6}, Ljavax/tools/JavaCompiler;->getTask(Ljava/io/Writer;Ljavax/tools/JavaFileManager;Ljavax/tools/DiagnosticListener;Ljava/lang/Iterable;Ljava/lang/Iterable;Ljava/lang/Iterable;)Ljavax/tools/JavaCompiler$CompilationTask;

    move-result-object p1

    .line 125
    invoke-interface {p1, v7}, Ljavax/tools/JavaCompiler$CompilationTask;->setLocale(Ljava/util/Locale;)V

    .line 127
    invoke-interface {p1}, Ljavax/tools/JavaCompiler$CompilationTask;->call()Ljava/lang/Boolean;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/Boolean;->booleanValue()Z

    move-result p1
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-eqz v8, :cond_2

    .line 128
    invoke-interface {v8}, Ljavax/tools/StandardJavaFileManager;->close()V

    :cond_2
    return p1

    :catchall_0
    move-exception p1

    if-eqz v8, :cond_3

    .line 95
    :try_start_1
    invoke-interface {v8}, Ljavax/tools/StandardJavaFileManager;->close()V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_1

    goto :goto_1

    :catchall_1
    move-exception p2

    invoke-virtual {p1, p2}, Ljava/lang/Throwable;->addSuppressed(Ljava/lang/Throwable;)V

    :cond_3
    :goto_1
    throw p1
.end method

.method static synthetic lambda$makeParameters$0()Ljava/security/CodeSource;
    .locals 1

    .line 190
    const-class v0, Lgroovy/lang/GroovyObject;

    invoke-virtual {v0}, Ljava/lang/Class;->getProtectionDomain()Ljava/security/ProtectionDomain;

    move-result-object v0

    invoke-virtual {v0}, Ljava/security/ProtectionDomain;->getCodeSource()Ljava/security/CodeSource;

    move-result-object v0

    return-object v0
.end method

.method private makeParameters(Lgroovy/lang/GroovyClassLoader;)Ljava/util/List;
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovy/lang/GroovyClassLoader;",
            ")",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .line 144
    iget-object v0, p0, Lorg/codehaus/groovy/tools/javac/JavacJavaCompiler;->config:Lorg/codehaus/groovy/control/CompilerConfiguration;

    invoke-virtual {v0}, Lorg/codehaus/groovy/control/CompilerConfiguration;->getJointCompilationOptions()Ljava/util/Map;

    move-result-object v0

    .line 145
    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    .line 147
    iget-object v2, p0, Lorg/codehaus/groovy/tools/javac/JavacJavaCompiler;->config:Lorg/codehaus/groovy/control/CompilerConfiguration;

    invoke-virtual {v2}, Lorg/codehaus/groovy/control/CompilerConfiguration;->getTargetDirectory()Ljava/io/File;

    move-result-object v2

    if-nez v2, :cond_0

    .line 148
    new-instance v2, Ljava/io/File;

    const-string v3, "."

    invoke-direct {v2, v3}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    :cond_0
    const-string v3, "-d"

    .line 150
    invoke-interface {v1, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 151
    invoke-virtual {v2}, Ljava/io/File;->getAbsolutePath()Ljava/lang/String;

    move-result-object v2

    invoke-interface {v1, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    const-string v2, "flags"

    .line 153
    invoke-interface {v0, v2}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, [Ljava/lang/String;

    const-string v3, "-"

    const/4 v4, 0x0

    if-eqz v2, :cond_1

    .line 155
    array-length v5, v2

    move v6, v4

    :goto_0
    if-ge v6, v5, :cond_1

    aget-object v7, v2, v6

    .line 156
    new-instance v8, Ljava/lang/StringBuilder;

    invoke-direct {v8}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v8, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v8

    invoke-virtual {v8, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v7

    invoke-virtual {v7}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v7

    invoke-interface {v1, v7}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    add-int/lit8 v6, v6, 0x1

    goto :goto_0

    :cond_1
    const-string v2, "namedValues"

    .line 161
    invoke-interface {v0, v2}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Ljava/lang/String;

    if-eqz v0, :cond_3

    .line 163
    array-length v2, v0

    move v5, v4

    move v6, v5

    :goto_1
    if-ge v5, v2, :cond_4

    .line 164
    aget-object v7, v0, v5

    const-string v8, "classpath"

    .line 165
    invoke-virtual {v7, v8}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v8

    if-eqz v8, :cond_2

    const/4 v6, 0x1

    .line 166
    :cond_2
    new-instance v8, Ljava/lang/StringBuilder;

    invoke-direct {v8}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v8, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v8

    invoke-virtual {v8, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v7

    invoke-virtual {v7}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v7

    invoke-interface {v1, v7}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    add-int/lit8 v7, v5, 0x1

    .line 167
    aget-object v7, v0, v7

    invoke-interface {v1, v7}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    add-int/lit8 v5, v5, 0x2

    goto :goto_1

    :cond_3
    move v6, v4

    :cond_4
    if-nez v6, :cond_8

    .line 174
    new-instance v0, Ljava/util/ArrayList;

    iget-object v2, p0, Lorg/codehaus/groovy/tools/javac/JavacJavaCompiler;->config:Lorg/codehaus/groovy/control/CompilerConfiguration;

    invoke-virtual {v2}, Lorg/codehaus/groovy/control/CompilerConfiguration;->getClasspath()Ljava/util/List;

    move-result-object v2

    invoke-direct {v0, v2}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    :goto_2
    if-eqz p1, :cond_6

    .line 177
    instance-of v2, p1, Ljava/net/URLClassLoader;

    if-eqz v2, :cond_5

    .line 178
    move-object v2, p1

    check-cast v2, Ljava/net/URLClassLoader;

    invoke-virtual {v2}, Ljava/net/URLClassLoader;->getURLs()[Ljava/net/URL;

    move-result-object v2

    array-length v3, v2

    move v5, v4

    :goto_3
    if-ge v5, v3, :cond_5

    aget-object v6, v2, v5

    .line 180
    :try_start_0
    new-instance v7, Ljava/io/File;

    invoke-virtual {v6}, Ljava/net/URL;->toURI()Ljava/net/URI;

    move-result-object v6

    invoke-direct {v7, v6}, Ljava/io/File;-><init>(Ljava/net/URI;)V

    invoke-virtual {v7}, Ljava/io/File;->getPath()Ljava/lang/String;

    move-result-object v6

    invoke-interface {v0, v6}, Ljava/util/List;->add(Ljava/lang/Object;)Z
    :try_end_0
    .catch Ljava/net/URISyntaxException; {:try_start_0 .. :try_end_0} :catch_0

    :catch_0
    add-int/lit8 v5, v5, 0x1

    goto :goto_3

    .line 185
    :cond_5
    invoke-virtual {p1}, Ljava/lang/ClassLoader;->getParent()Ljava/lang/ClassLoader;

    move-result-object p1

    goto :goto_2

    .line 189
    :cond_6
    :try_start_1
    sget-object p1, Lorg/codehaus/groovy/tools/javac/JavacJavaCompiler$$ExternalSyntheticLambda0;->INSTANCE:Lorg/codehaus/groovy/tools/javac/JavacJavaCompiler$$ExternalSyntheticLambda0;

    invoke-static {p1}, Ljava/security/AccessController;->doPrivileged(Ljava/security/PrivilegedAction;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/security/CodeSource;

    if-eqz p1, :cond_7

    .line 192
    new-instance v2, Ljava/io/File;

    invoke-virtual {p1}, Ljava/security/CodeSource;->getLocation()Ljava/net/URL;

    move-result-object p1

    invoke-virtual {p1}, Ljava/net/URL;->toURI()Ljava/net/URI;

    move-result-object p1

    invoke-direct {v2, p1}, Ljava/io/File;-><init>(Ljava/net/URI;)V

    invoke-virtual {v2}, Ljava/io/File;->getPath()Ljava/lang/String;

    move-result-object p1

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z
    :try_end_1
    .catch Ljava/net/URISyntaxException; {:try_start_1 .. :try_end_1} :catch_1

    :catch_1
    :cond_7
    const-string p1, "-classpath"

    .line 197
    invoke-interface {v1, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 198
    sget-object p1, Ljava/io/File;->pathSeparator:Ljava/lang/String;

    invoke-static {v0, p1}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->join(Ljava/lang/Iterable;Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    invoke-interface {v1, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :cond_8
    return-object v1
.end method


# virtual methods
.method public compile(Ljava/util/List;Lorg/codehaus/groovy/control/CompilationUnit;)V
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;",
            "Lorg/codehaus/groovy/control/CompilationUnit;",
            ")V"
        }
    .end annotation

    .line 61
    invoke-virtual {p2}, Lorg/codehaus/groovy/control/CompilationUnit;->getClassLoader()Lgroovy/lang/GroovyClassLoader;

    move-result-object v0

    invoke-direct {p0, v0}, Lorg/codehaus/groovy/tools/javac/JavacJavaCompiler;->makeParameters(Lgroovy/lang/GroovyClassLoader;)Ljava/util/List;

    move-result-object v0

    .line 62
    new-instance v1, Lorg/apache/groovy/io/StringBuilderWriter;

    invoke-direct {v1}, Lorg/apache/groovy/io/StringBuilderWriter;-><init>()V

    const/4 v2, 0x2

    const/4 v3, 0x1

    .line 66
    :try_start_0
    invoke-direct {p0, p2, p1, v0, v1}, Lorg/codehaus/groovy/tools/javac/JavacJavaCompiler;->doCompileWithSystemJavaCompiler(Lorg/codehaus/groovy/control/CompilationUnit;Ljava/util/List;Ljava/util/List;Lorg/apache/groovy/io/StringBuilderWriter;)Z

    move-result p1
    :try_end_0
    .catch Ljava/lang/IllegalArgumentException; {:try_start_0 .. :try_end_0} :catch_3
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_1
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    xor-int/2addr p1, v3

    goto :goto_1

    :catch_0
    move-exception p1

    const/4 v0, 0x0

    goto :goto_0

    :catch_1
    move-exception p1

    .line 75
    :try_start_1
    invoke-virtual {p2}, Lorg/codehaus/groovy/control/CompilationUnit;->getErrorCollector()Lorg/codehaus/groovy/control/ErrorCollector;

    move-result-object v0

    new-instance v4, Lorg/codehaus/groovy/control/messages/ExceptionMessage;

    invoke-direct {v4, p1, v3, p2}, Lorg/codehaus/groovy/control/messages/ExceptionMessage;-><init>(Ljava/lang/Exception;ZLorg/codehaus/groovy/control/ProcessingUnit;)V

    invoke-virtual {v0, v4}, Lorg/codehaus/groovy/control/ErrorCollector;->addFatalError(Lorg/codehaus/groovy/control/messages/Message;)V
    :try_end_1
    .catch Ljava/lang/Exception; {:try_start_1 .. :try_end_1} :catch_2

    move p1, v3

    goto :goto_1

    :catch_2
    move-exception p1

    move v0, v3

    goto :goto_0

    :catch_3
    move-exception p1

    .line 72
    :try_start_2
    invoke-virtual {p2}, Lorg/codehaus/groovy/control/CompilationUnit;->getErrorCollector()Lorg/codehaus/groovy/control/ErrorCollector;

    move-result-object v0

    new-instance v4, Lorg/codehaus/groovy/control/messages/ExceptionMessage;

    invoke-direct {v4, p1, v3, p2}, Lorg/codehaus/groovy/control/messages/ExceptionMessage;-><init>(Ljava/lang/Exception;ZLorg/codehaus/groovy/control/ProcessingUnit;)V

    invoke-virtual {v0, v4}, Lorg/codehaus/groovy/control/ErrorCollector;->addFatalError(Lorg/codehaus/groovy/control/messages/Message;)V
    :try_end_2
    .catch Ljava/lang/Exception; {:try_start_2 .. :try_end_2} :catch_4

    move p1, v2

    goto :goto_1

    :catch_4
    move-exception p1

    move v0, v2

    .line 78
    :goto_0
    invoke-virtual {p2}, Lorg/codehaus/groovy/control/CompilationUnit;->getErrorCollector()Lorg/codehaus/groovy/control/ErrorCollector;

    move-result-object v4

    new-instance v5, Lorg/codehaus/groovy/control/messages/ExceptionMessage;

    invoke-direct {v5, p1, v3, p2}, Lorg/codehaus/groovy/control/messages/ExceptionMessage;-><init>(Ljava/lang/Exception;ZLorg/codehaus/groovy/control/ProcessingUnit;)V

    invoke-virtual {v4, v5}, Lorg/codehaus/groovy/control/ErrorCollector;->addFatalError(Lorg/codehaus/groovy/control/messages/Message;)V

    move p1, v0

    :goto_1
    if-eqz p1, :cond_2

    if-eq p1, v3, :cond_1

    if-eq p1, v2, :cond_0

    const-string p1, "unexpected return value by javac."

    .line 85
    invoke-static {p1, p2, v1}, Lorg/codehaus/groovy/tools/javac/JavacJavaCompiler;->addJavacError(Ljava/lang/String;Lorg/codehaus/groovy/control/CompilationUnit;Lorg/apache/groovy/io/StringBuilderWriter;)V

    goto :goto_2

    :cond_0
    const-string p1, "Invalid commandline usage for javac."

    .line 84
    invoke-static {p1, p2, v1}, Lorg/codehaus/groovy/tools/javac/JavacJavaCompiler;->addJavacError(Ljava/lang/String;Lorg/codehaus/groovy/control/CompilationUnit;Lorg/apache/groovy/io/StringBuilderWriter;)V

    goto :goto_2

    :cond_1
    const-string p1, "Compile error during compilation with javac."

    .line 83
    invoke-static {p1, p2, v1}, Lorg/codehaus/groovy/tools/javac/JavacJavaCompiler;->addJavacError(Ljava/lang/String;Lorg/codehaus/groovy/control/CompilationUnit;Lorg/apache/groovy/io/StringBuilderWriter;)V

    goto :goto_2

    .line 89
    :cond_2
    sget-object p1, Ljava/lang/System;->out:Ljava/io/PrintStream;

    invoke-virtual {p1, v1}, Ljava/io/PrintStream;->print(Ljava/lang/Object;)V

    :goto_2
    return-void
.end method
