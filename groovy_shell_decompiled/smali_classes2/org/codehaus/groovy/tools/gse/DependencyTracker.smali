.class public Lorg/codehaus/groovy/tools/gse/DependencyTracker;
.super Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;
.source "DependencyTracker.java"


# instance fields
.field private final cache:Lorg/codehaus/groovy/tools/gse/StringSetMap;

.field private current:Ljava/util/Set;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Set<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field private final precompiledDependencies:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "*>;"
        }
    .end annotation
.end field

.field private final source:Lorg/codehaus/groovy/control/SourceUnit;


# direct methods
.method public constructor <init>(Lorg/codehaus/groovy/control/SourceUnit;Lorg/codehaus/groovy/tools/gse/StringSetMap;)V
    .locals 1

    .line 47
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    invoke-direct {p0, p1, p2, v0}, Lorg/codehaus/groovy/tools/gse/DependencyTracker;-><init>(Lorg/codehaus/groovy/control/SourceUnit;Lorg/codehaus/groovy/tools/gse/StringSetMap;Ljava/util/Map;)V

    return-void
.end method

.method public constructor <init>(Lorg/codehaus/groovy/control/SourceUnit;Lorg/codehaus/groovy/tools/gse/StringSetMap;Ljava/util/Map;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/control/SourceUnit;",
            "Lorg/codehaus/groovy/tools/gse/StringSetMap;",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "*>;)V"
        }
    .end annotation

    .line 50
    invoke-direct {p0}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;-><init>()V

    .line 51
    iput-object p1, p0, Lorg/codehaus/groovy/tools/gse/DependencyTracker;->source:Lorg/codehaus/groovy/control/SourceUnit;

    .line 52
    iput-object p2, p0, Lorg/codehaus/groovy/tools/gse/DependencyTracker;->cache:Lorg/codehaus/groovy/tools/gse/StringSetMap;

    .line 53
    iput-object p3, p0, Lorg/codehaus/groovy/tools/gse/DependencyTracker;->precompiledDependencies:Ljava/util/Map;

    return-void
.end method

.method private addToCache(Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 2

    if-nez p1, :cond_0

    return-void

    .line 58
    :cond_0
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v0

    .line 59
    iget-object v1, p0, Lorg/codehaus/groovy/tools/gse/DependencyTracker;->precompiledDependencies:Ljava/util/Map;

    invoke-interface {v1, v0}, Ljava/util/Map;->containsKey(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_1

    .line 60
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->isPrimaryClassNode()Z

    move-result v0

    if-nez v0, :cond_1

    return-void

    .line 64
    :cond_1
    iget-object v0, p0, Lorg/codehaus/groovy/tools/gse/DependencyTracker;->current:Ljava/util/Set;

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 65
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getSuperClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    invoke-direct {p0, v0}, Lorg/codehaus/groovy/tools/gse/DependencyTracker;->addToCache(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 66
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getInterfaces()[Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    invoke-direct {p0, p1}, Lorg/codehaus/groovy/tools/gse/DependencyTracker;->addToCache([Lorg/codehaus/groovy/ast/ClassNode;)V

    return-void
.end method

.method private addToCache([Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 3

    if-nez p1, :cond_0

    return-void

    .line 71
    :cond_0
    array-length v0, p1

    const/4 v1, 0x0

    :goto_0
    if-ge v1, v0, :cond_1

    aget-object v2, p1, v1

    invoke-direct {p0, v2}, Lorg/codehaus/groovy/tools/gse/DependencyTracker;->addToCache(Lorg/codehaus/groovy/ast/ClassNode;)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_1
    return-void
.end method


# virtual methods
.method protected getSourceUnit()Lorg/codehaus/groovy/control/SourceUnit;
    .locals 1

    .line 84
    iget-object v0, p0, Lorg/codehaus/groovy/tools/gse/DependencyTracker;->source:Lorg/codehaus/groovy/control/SourceUnit;

    return-object v0
.end method

.method public visitAnnotations(Lorg/codehaus/groovy/ast/AnnotatedNode;)V
    .locals 1

    .line 127
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;->visitAnnotations(Lorg/codehaus/groovy/ast/AnnotatedNode;)V

    .line 128
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/AnnotatedNode;->getAnnotations()Ljava/util/List;

    move-result-object p1

    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/ast/AnnotationNode;

    .line 129
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/AnnotationNode;->getClassNode()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    invoke-direct {p0, v0}, Lorg/codehaus/groovy/tools/gse/DependencyTracker;->addToCache(Lorg/codehaus/groovy/ast/ClassNode;)V

    goto :goto_0

    :cond_0
    return-void
.end method

.method public visitArrayExpression(Lorg/codehaus/groovy/ast/expr/ArrayExpression;)V
    .locals 0

    .line 107
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;->visitArrayExpression(Lorg/codehaus/groovy/ast/expr/ArrayExpression;)V

    .line 108
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/ArrayExpression;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    invoke-direct {p0, p1}, Lorg/codehaus/groovy/tools/gse/DependencyTracker;->addToCache(Lorg/codehaus/groovy/ast/ClassNode;)V

    return-void
.end method

.method public visitCastExpression(Lorg/codehaus/groovy/ast/expr/CastExpression;)V
    .locals 0

    .line 112
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;->visitCastExpression(Lorg/codehaus/groovy/ast/expr/CastExpression;)V

    .line 113
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/CastExpression;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    invoke-direct {p0, p1}, Lorg/codehaus/groovy/tools/gse/DependencyTracker;->addToCache(Lorg/codehaus/groovy/ast/ClassNode;)V

    return-void
.end method

.method public visitCatchStatement(Lorg/codehaus/groovy/ast/stmt/CatchStatement;)V
    .locals 0

    .line 122
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;->visitCatchStatement(Lorg/codehaus/groovy/ast/stmt/CatchStatement;)V

    .line 123
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/stmt/CatchStatement;->getVariable()Lorg/codehaus/groovy/ast/Parameter;

    move-result-object p1

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/Parameter;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    invoke-direct {p0, p1}, Lorg/codehaus/groovy/tools/gse/DependencyTracker;->addToCache(Lorg/codehaus/groovy/ast/ClassNode;)V

    return-void
.end method

.method public visitClass(Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 3

    .line 76
    iget-object v0, p0, Lorg/codehaus/groovy/tools/gse/DependencyTracker;->current:Ljava/util/Set;

    .line 77
    iget-object v1, p0, Lorg/codehaus/groovy/tools/gse/DependencyTracker;->cache:Lorg/codehaus/groovy/tools/gse/StringSetMap;

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Lorg/codehaus/groovy/tools/gse/StringSetMap;->get(Ljava/lang/Object;)Ljava/util/Set;

    move-result-object v1

    iput-object v1, p0, Lorg/codehaus/groovy/tools/gse/DependencyTracker;->current:Ljava/util/Set;

    .line 78
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/tools/gse/DependencyTracker;->addToCache(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 79
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;->visitClass(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 80
    iput-object v0, p0, Lorg/codehaus/groovy/tools/gse/DependencyTracker;->current:Ljava/util/Set;

    return-void
.end method

.method public visitClassExpression(Lorg/codehaus/groovy/ast/expr/ClassExpression;)V
    .locals 0

    .line 88
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;->visitClassExpression(Lorg/codehaus/groovy/ast/expr/ClassExpression;)V

    .line 89
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/ClassExpression;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    invoke-direct {p0, p1}, Lorg/codehaus/groovy/tools/gse/DependencyTracker;->addToCache(Lorg/codehaus/groovy/ast/ClassNode;)V

    return-void
.end method

.method public visitConstructorCallExpression(Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;)V
    .locals 0

    .line 134
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;->visitConstructorCallExpression(Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;)V

    .line 135
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    invoke-direct {p0, p1}, Lorg/codehaus/groovy/tools/gse/DependencyTracker;->addToCache(Lorg/codehaus/groovy/ast/ClassNode;)V

    return-void
.end method

.method public visitField(Lorg/codehaus/groovy/ast/FieldNode;)V
    .locals 0

    .line 93
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;->visitField(Lorg/codehaus/groovy/ast/FieldNode;)V

    .line 94
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/FieldNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    invoke-direct {p0, p1}, Lorg/codehaus/groovy/tools/gse/DependencyTracker;->addToCache(Lorg/codehaus/groovy/ast/ClassNode;)V

    return-void
.end method

.method public visitMethod(Lorg/codehaus/groovy/ast/MethodNode;)V
    .locals 4

    .line 98
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v0

    array-length v1, v0

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v1, :cond_0

    aget-object v3, v0, v2

    .line 99
    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/Parameter;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v3

    invoke-direct {p0, v3}, Lorg/codehaus/groovy/tools/gse/DependencyTracker;->addToCache(Lorg/codehaus/groovy/ast/ClassNode;)V

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 101
    :cond_0
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getReturnType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    invoke-direct {p0, v0}, Lorg/codehaus/groovy/tools/gse/DependencyTracker;->addToCache(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 102
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getExceptions()[Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    invoke-direct {p0, v0}, Lorg/codehaus/groovy/tools/gse/DependencyTracker;->addToCache([Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 103
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;->visitMethod(Lorg/codehaus/groovy/ast/MethodNode;)V

    return-void
.end method

.method public visitVariableExpression(Lorg/codehaus/groovy/ast/expr/VariableExpression;)V
    .locals 0

    .line 117
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;->visitVariableExpression(Lorg/codehaus/groovy/ast/expr/VariableExpression;)V

    .line 118
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    invoke-direct {p0, p1}, Lorg/codehaus/groovy/tools/gse/DependencyTracker;->addToCache(Lorg/codehaus/groovy/ast/ClassNode;)V

    return-void
.end method
