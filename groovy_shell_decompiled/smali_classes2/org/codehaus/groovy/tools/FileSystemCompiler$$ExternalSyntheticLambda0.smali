.class public final synthetic Lorg/codehaus/groovy/tools/FileSystemCompiler$$ExternalSyntheticLambda0;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Lgroovy/lang/GroovyResourceLoader;


# static fields
.field public static final synthetic INSTANCE:Lorg/codehaus/groovy/tools/FileSystemCompiler$$ExternalSyntheticLambda0;


# direct methods
.method static synthetic constructor <clinit>()V
    .locals 1

    new-instance v0, Lorg/codehaus/groovy/tools/FileSystemCompiler$$ExternalSyntheticLambda0;

    invoke-direct {v0}, Lorg/codehaus/groovy/tools/FileSystemCompiler$$ExternalSyntheticLambda0;-><init>()V

    sput-object v0, Lorg/codehaus/groovy/tools/FileSystemCompiler$$ExternalSyntheticLambda0;->INSTANCE:Lorg/codehaus/groovy/tools/FileSystemCompiler$$ExternalSyntheticLambda0;

    return-void
.end method

.method private synthetic constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final loadGroovySource(Ljava/lang/String;)Ljava/net/URL;
    .locals 0

    invoke-static {p1}, Lorg/codehaus/groovy/tools/FileSystemCompiler;->lambda$doCompilation$0(Ljava/lang/String;)Ljava/net/URL;

    move-result-object p1

    return-object p1
.end method
