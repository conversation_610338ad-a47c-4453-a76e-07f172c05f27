.class public Lorg/codehaus/groovy/tools/shell/util/MessageSource;
.super Lgroovy/lang/GroovyObjectSupport;
.source "MessageSource.java"


# static fields
.field static final synthetic $assertionsDisabled:Z


# instance fields
.field private final bundleNames:[Ljava/lang/String;

.field private cachedBundles:[Ljava/util/ResourceBundle;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Ljava/lang/Class;)V
    .locals 2

    const/4 v0, 0x1

    new-array v0, v0, [Ljava/lang/String;

    .line 69
    invoke-virtual {p1}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object p1

    const/4 v1, 0x0

    aput-object p1, v0, v1

    invoke-direct {p0, v0}, Lorg/codehaus/groovy/tools/shell/util/MessageSource;-><init>([Ljava/lang/String;)V

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;)V
    .locals 2

    const/4 v0, 0x1

    new-array v0, v0, [Ljava/lang/String;

    const/4 v1, 0x0

    aput-object p1, v0, v1

    .line 46
    invoke-direct {p0, v0}, Lorg/codehaus/groovy/tools/shell/util/MessageSource;-><init>([Ljava/lang/String;)V

    return-void
.end method

.method public constructor <init>([Ljava/lang/Class;)V
    .locals 0

    .line 65
    invoke-static {p1}, Lorg/codehaus/groovy/tools/shell/util/MessageSource;->classNames([Ljava/lang/Class;)[Ljava/lang/String;

    move-result-object p1

    invoke-direct {p0, p1}, Lorg/codehaus/groovy/tools/shell/util/MessageSource;-><init>([Ljava/lang/String;)V

    return-void
.end method

.method public constructor <init>([Ljava/lang/String;)V
    .locals 0

    .line 38
    invoke-direct {p0}, Lgroovy/lang/GroovyObjectSupport;-><init>()V

    .line 42
    iput-object p1, p0, Lorg/codehaus/groovy/tools/shell/util/MessageSource;->bundleNames:[Ljava/lang/String;

    return-void
.end method

.method private static classNames([Ljava/lang/Class;)[Ljava/lang/String;
    .locals 3

    .line 53
    array-length v0, p0

    new-array v0, v0, [Ljava/lang/String;

    const/4 v1, 0x0

    .line 55
    :goto_0
    array-length v2, p0

    if-ge v1, v2, :cond_0

    .line 58
    aget-object v2, p0, v1

    invoke-virtual {v2}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v2

    aput-object v2, v0, v1

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    return-object v0
.end method

.method private createBundles()[Ljava/util/ResourceBundle;
    .locals 4

    .line 73
    iget-object v0, p0, Lorg/codehaus/groovy/tools/shell/util/MessageSource;->bundleNames:[Ljava/lang/String;

    array-length v0, v0

    new-array v0, v0, [Ljava/util/ResourceBundle;

    const/4 v1, 0x0

    .line 75
    :goto_0
    iget-object v2, p0, Lorg/codehaus/groovy/tools/shell/util/MessageSource;->bundleNames:[Ljava/lang/String;

    array-length v3, v2

    if-ge v1, v3, :cond_0

    .line 78
    aget-object v2, v2, v1

    invoke-static {v2}, Ljava/util/ResourceBundle;->getBundle(Ljava/lang/String;)Ljava/util/ResourceBundle;

    move-result-object v2

    aput-object v2, v0, v1

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    return-object v0
.end method

.method private getBundles()[Ljava/util/ResourceBundle;
    .locals 1

    .line 85
    iget-object v0, p0, Lorg/codehaus/groovy/tools/shell/util/MessageSource;->cachedBundles:[Ljava/util/ResourceBundle;

    if-nez v0, :cond_0

    .line 86
    invoke-direct {p0}, Lorg/codehaus/groovy/tools/shell/util/MessageSource;->createBundles()[Ljava/util/ResourceBundle;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/tools/shell/util/MessageSource;->cachedBundles:[Ljava/util/ResourceBundle;

    .line 88
    :cond_0
    iget-object v0, p0, Lorg/codehaus/groovy/tools/shell/util/MessageSource;->cachedBundles:[Ljava/util/ResourceBundle;

    return-object v0
.end method


# virtual methods
.method public format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;
    .locals 0

    .line 128
    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/tools/shell/util/MessageSource;->getMessage(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    .line 130
    invoke-static {p1, p2}, Ljava/text/MessageFormat;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public getMessage(Ljava/lang/String;)Ljava/lang/String;
    .locals 4

    .line 99
    invoke-direct {p0}, Lorg/codehaus/groovy/tools/shell/util/MessageSource;->getBundles()[Ljava/util/ResourceBundle;

    move-result-object v0

    .line 101
    array-length v1, v0

    const/4 v2, 0x0

    :goto_0
    const/4 v3, 0x0

    if-ge v2, v1, :cond_0

    aget-object v3, v0, v2

    .line 103
    :try_start_0
    invoke-virtual {v3, p1}, Ljava/util/ResourceBundle;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1
    :try_end_0
    .catch Ljava/util/MissingResourceException; {:try_start_0 .. :try_end_0} :catch_0

    return-object p1

    :catch_0
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 117
    :cond_0
    throw v3
.end method

.method public getProperty(Ljava/lang/String;)Ljava/lang/Object;
    .locals 0

    .line 137
    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/tools/shell/util/MessageSource;->getMessage(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method
