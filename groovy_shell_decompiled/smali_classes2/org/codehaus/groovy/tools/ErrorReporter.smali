.class public Lorg/codehaus/groovy/tools/ErrorReporter;
.super Ljava/lang/Object;
.source "ErrorReporter.java"


# instance fields
.field private base:Ljava/lang/Throwable;

.field private debug:Z

.field private output:Ljava/lang/Object;


# direct methods
.method public constructor <init>(Ljava/lang/Throwable;)V
    .locals 2

    .line 47
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    .line 35
    iput-object v0, p0, Lorg/codehaus/groovy/tools/ErrorReporter;->base:Ljava/lang/Throwable;

    const/4 v1, 0x0

    .line 36
    iput-boolean v1, p0, Lorg/codehaus/groovy/tools/ErrorReporter;->debug:Z

    .line 38
    iput-object v0, p0, Lorg/codehaus/groovy/tools/ErrorReporter;->output:Ljava/lang/Object;

    .line 48
    iput-object p1, p0, Lorg/codehaus/groovy/tools/ErrorReporter;->base:Ljava/lang/Throwable;

    return-void
.end method

.method public constructor <init>(Ljava/lang/Throwable;Z)V
    .locals 2

    .line 58
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    .line 35
    iput-object v0, p0, Lorg/codehaus/groovy/tools/ErrorReporter;->base:Ljava/lang/Throwable;

    const/4 v1, 0x0

    .line 36
    iput-boolean v1, p0, Lorg/codehaus/groovy/tools/ErrorReporter;->debug:Z

    .line 38
    iput-object v0, p0, Lorg/codehaus/groovy/tools/ErrorReporter;->output:Ljava/lang/Object;

    .line 59
    iput-object p1, p0, Lorg/codehaus/groovy/tools/ErrorReporter;->base:Ljava/lang/Throwable;

    .line 60
    iput-boolean p2, p0, Lorg/codehaus/groovy/tools/ErrorReporter;->debug:Z

    return-void
.end method


# virtual methods
.method protected dispatch(Ljava/lang/Throwable;Z)V
    .locals 1

    .line 88
    instance-of v0, p1, Lorg/codehaus/groovy/control/CompilationFailedException;

    if-eqz v0, :cond_0

    .line 89
    check-cast p1, Lorg/codehaus/groovy/control/CompilationFailedException;

    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/tools/ErrorReporter;->report(Lorg/codehaus/groovy/control/CompilationFailedException;Z)V

    goto :goto_0

    .line 90
    :cond_0
    instance-of v0, p1, Lorg/codehaus/groovy/GroovyExceptionInterface;

    if-eqz v0, :cond_1

    .line 91
    check-cast p1, Lorg/codehaus/groovy/GroovyExceptionInterface;

    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/tools/ErrorReporter;->report(Lorg/codehaus/groovy/GroovyExceptionInterface;Z)V

    goto :goto_0

    .line 92
    :cond_1
    instance-of v0, p1, Lgroovy/lang/GroovyRuntimeException;

    if-eqz v0, :cond_2

    .line 93
    check-cast p1, Lgroovy/lang/GroovyRuntimeException;

    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/tools/ErrorReporter;->report(Ljava/lang/Exception;Z)V

    goto :goto_0

    .line 94
    :cond_2
    instance-of v0, p1, Ljava/lang/Exception;

    if-eqz v0, :cond_3

    .line 95
    check-cast p1, Ljava/lang/Exception;

    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/tools/ErrorReporter;->report(Ljava/lang/Exception;Z)V

    goto :goto_0

    .line 97
    :cond_3
    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/tools/ErrorReporter;->report(Ljava/lang/Throwable;Z)V

    :goto_0
    return-void
.end method

.method protected println(Ljava/lang/String;)V
    .locals 2

    .line 151
    iget-object v0, p0, Lorg/codehaus/groovy/tools/ErrorReporter;->output:Ljava/lang/Object;

    instance-of v1, v0, Ljava/io/PrintStream;

    if-eqz v1, :cond_0

    .line 152
    check-cast v0, Ljava/io/PrintStream;

    invoke-virtual {v0, p1}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    goto :goto_0

    .line 154
    :cond_0
    check-cast v0, Ljava/io/PrintWriter;

    invoke-virtual {v0, p1}, Ljava/io/PrintWriter;->println(Ljava/lang/String;)V

    :goto_0
    return-void
.end method

.method protected println(Ljava/lang/StringBuffer;)V
    .locals 2

    .line 159
    iget-object v0, p0, Lorg/codehaus/groovy/tools/ErrorReporter;->output:Ljava/lang/Object;

    instance-of v1, v0, Ljava/io/PrintStream;

    if-eqz v1, :cond_0

    .line 160
    check-cast v0, Ljava/io/PrintStream;

    invoke-virtual {v0, p1}, Ljava/io/PrintStream;->println(Ljava/lang/Object;)V

    goto :goto_0

    .line 162
    :cond_0
    check-cast v0, Ljava/io/PrintWriter;

    invoke-virtual {v0, p1}, Ljava/io/PrintWriter;->println(Ljava/lang/Object;)V

    :goto_0
    return-void
.end method

.method protected report(Ljava/lang/Exception;Z)V
    .locals 0

    .line 129
    invoke-virtual {p1}, Ljava/lang/Exception;->getMessage()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p2}, Lorg/codehaus/groovy/tools/ErrorReporter;->println(Ljava/lang/String;)V

    const/4 p2, 0x0

    .line 130
    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/tools/ErrorReporter;->stacktrace(Ljava/lang/Throwable;Z)V

    return-void
.end method

.method protected report(Ljava/lang/Throwable;Z)V
    .locals 1

    .line 138
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, ">>> a serious error occurred: "

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p1}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p2}, Lorg/codehaus/groovy/tools/ErrorReporter;->println(Ljava/lang/String;)V

    const/4 p2, 0x1

    .line 139
    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/tools/ErrorReporter;->stacktrace(Ljava/lang/Throwable;Z)V

    return-void
.end method

.method protected report(Lorg/codehaus/groovy/GroovyExceptionInterface;Z)V
    .locals 0

    .line 120
    check-cast p1, Ljava/lang/Exception;

    invoke-virtual {p1}, Ljava/lang/Exception;->getMessage()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p2}, Lorg/codehaus/groovy/tools/ErrorReporter;->println(Ljava/lang/String;)V

    const/4 p2, 0x0

    .line 121
    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/tools/ErrorReporter;->stacktrace(Ljava/lang/Throwable;Z)V

    return-void
.end method

.method protected report(Lorg/codehaus/groovy/control/CompilationFailedException;Z)V
    .locals 0

    .line 111
    invoke-virtual {p1}, Lorg/codehaus/groovy/control/CompilationFailedException;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p2}, Lorg/codehaus/groovy/tools/ErrorReporter;->println(Ljava/lang/String;)V

    const/4 p2, 0x0

    .line 112
    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/tools/ErrorReporter;->stacktrace(Ljava/lang/Throwable;Z)V

    return-void
.end method

.method protected stacktrace(Ljava/lang/Throwable;Z)V
    .locals 1

    .line 172
    iget-boolean v0, p0, Lorg/codehaus/groovy/tools/ErrorReporter;->debug:Z

    if-nez v0, :cond_0

    if-eqz p2, :cond_2

    :cond_0
    const-string p2, ">>> stacktrace:"

    .line 173
    invoke-virtual {p0, p2}, Lorg/codehaus/groovy/tools/ErrorReporter;->println(Ljava/lang/String;)V

    .line 174
    iget-object p2, p0, Lorg/codehaus/groovy/tools/ErrorReporter;->output:Ljava/lang/Object;

    instance-of v0, p2, Ljava/io/PrintStream;

    if-eqz v0, :cond_1

    .line 175
    check-cast p2, Ljava/io/PrintStream;

    invoke-virtual {p1, p2}, Ljava/lang/Throwable;->printStackTrace(Ljava/io/PrintStream;)V

    goto :goto_0

    .line 177
    :cond_1
    check-cast p2, Ljava/io/PrintWriter;

    invoke-virtual {p1, p2}, Ljava/lang/Throwable;->printStackTrace(Ljava/io/PrintWriter;)V

    :cond_2
    :goto_0
    return-void
.end method

.method public write(Ljava/io/PrintStream;)V
    .locals 2

    .line 68
    iput-object p1, p0, Lorg/codehaus/groovy/tools/ErrorReporter;->output:Ljava/lang/Object;

    .line 69
    iget-object v0, p0, Lorg/codehaus/groovy/tools/ErrorReporter;->base:Ljava/lang/Throwable;

    const/4 v1, 0x0

    invoke-virtual {p0, v0, v1}, Lorg/codehaus/groovy/tools/ErrorReporter;->dispatch(Ljava/lang/Throwable;Z)V

    .line 70
    invoke-virtual {p1}, Ljava/io/PrintStream;->flush()V

    return-void
.end method

.method public write(Ljava/io/PrintWriter;)V
    .locals 2

    .line 78
    iput-object p1, p0, Lorg/codehaus/groovy/tools/ErrorReporter;->output:Ljava/lang/Object;

    .line 79
    iget-object v0, p0, Lorg/codehaus/groovy/tools/ErrorReporter;->base:Ljava/lang/Throwable;

    const/4 v1, 0x0

    invoke-virtual {p0, v0, v1}, Lorg/codehaus/groovy/tools/ErrorReporter;->dispatch(Ljava/lang/Throwable;Z)V

    .line 80
    invoke-virtual {p1}, Ljava/io/PrintWriter;->flush()V

    return-void
.end method
