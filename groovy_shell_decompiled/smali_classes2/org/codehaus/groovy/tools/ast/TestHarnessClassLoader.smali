.class Lorg/codehaus/groovy/tools/ast/TestHarnessClassLoader;
.super Lgroovy/lang/GroovyClassLoader;
.source "TransformTestHelper.groovy"

# interfaces
.implements Lgroovy/lang/GroovyObject;


# static fields
.field private static synthetic $callSiteArray:Ljava/lang/ref/SoftReference;

.field private static synthetic $staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

.field private static synthetic $staticClassInfo$:Lorg/codehaus/groovy/reflection/ClassInfo;

.field public static transient synthetic __$stMC:Z


# instance fields
.field private transient synthetic metaClass:Lgroovy/lang/MetaClass;

.field private final phase:Lorg/codehaus/groovy/control/CompilePhase;

.field private final transform:Lorg/codehaus/groovy/transform/ASTTransformation;


# direct methods
.method private static synthetic $createCallSiteArray()Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;
    .locals 3

    const/4 v0, 0x3

    new-array v0, v0, [Ljava/lang/String;

    invoke-static {v0}, Lorg/codehaus/groovy/tools/ast/TestHarnessClassLoader;->$createCallSiteArray_1([Ljava/lang/String;)V

    new-instance v1, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;

    const-class v2, Lorg/codehaus/groovy/tools/ast/TestHarnessClassLoader;

    invoke-direct {v1, v2, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;-><init>(Ljava/lang/Class;[Ljava/lang/String;)V

    return-object v1
.end method

.method private static synthetic $createCallSiteArray_1([Ljava/lang/String;)V
    .locals 2

    const/4 v0, 0x0

    const-string v1, "addPhaseOperation"

    aput-object v1, p0, v0

    const/4 v0, 0x1

    const-string v1, "<$constructor$>"

    aput-object v1, p0, v0

    const/4 v0, 0x2

    const-string v1, "phaseNumber"

    aput-object v1, p0, v0

    return-void
.end method

.method private static synthetic $getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;
    .locals 2

    sget-object v0, Lorg/codehaus/groovy/tools/ast/TestHarnessClassLoader;->$callSiteArray:Ljava/lang/ref/SoftReference;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Ljava/lang/ref/SoftReference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;

    if-nez v0, :cond_1

    :cond_0
    invoke-static {}, Lorg/codehaus/groovy/tools/ast/TestHarnessClassLoader;->$createCallSiteArray()Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;

    move-result-object v0

    new-instance v1, Ljava/lang/ref/SoftReference;

    invoke-direct {v1, v0}, Ljava/lang/ref/SoftReference;-><init>(Ljava/lang/Object;)V

    sput-object v1, Lorg/codehaus/groovy/tools/ast/TestHarnessClassLoader;->$callSiteArray:Ljava/lang/ref/SoftReference;

    :cond_1
    iget-object v0, v0, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;->array:[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    return-object v0
.end method

.method public constructor <init>(Lorg/codehaus/groovy/transform/ASTTransformation;Lorg/codehaus/groovy/control/CompilePhase;)V
    .locals 1

    invoke-static {}, Lorg/codehaus/groovy/tools/ast/TestHarnessClassLoader;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    invoke-direct {p0}, Lgroovy/lang/GroovyClassLoader;-><init>()V

    invoke-virtual {p0}, Lorg/codehaus/groovy/tools/ast/TestHarnessClassLoader;->$getStaticMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/tools/ast/TestHarnessClassLoader;->metaClass:Lgroovy/lang/MetaClass;

    .line 91
    iput-object p1, p0, Lorg/codehaus/groovy/tools/ast/TestHarnessClassLoader;->transform:Lorg/codehaus/groovy/transform/ASTTransformation;

    .line 92
    iput-object p2, p0, Lorg/codehaus/groovy/tools/ast/TestHarnessClassLoader;->phase:Lorg/codehaus/groovy/control/CompilePhase;

    return-void
.end method


# virtual methods
.method protected synthetic $getStaticMetaClass()Lgroovy/lang/MetaClass;
    .locals 2

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    const-class v1, Lorg/codehaus/groovy/tools/ast/TestHarnessClassLoader;

    if-eq v0, v1, :cond_0

    invoke-static {p0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->initMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0

    :cond_0
    sget-object v0, Lorg/codehaus/groovy/tools/ast/TestHarnessClassLoader;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    if-nez v0, :cond_1

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getClassInfo(Ljava/lang/Class;)Lorg/codehaus/groovy/reflection/ClassInfo;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/tools/ast/TestHarnessClassLoader;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    :cond_1
    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0
.end method

.method protected createCompilationUnit(Lorg/codehaus/groovy/control/CompilerConfiguration;Ljava/security/CodeSource;)Lorg/codehaus/groovy/control/CompilationUnit;
    .locals 6

    invoke-static {}, Lorg/codehaus/groovy/tools/ast/TestHarnessClassLoader;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v0

    .line 96
    const-class v1, Lgroovy/lang/GroovyClassLoader;

    const-string v2, "createCompilationUnit"

    move-object v3, v2

    check-cast v3, Ljava/lang/String;

    const/4 v3, 0x2

    new-array v4, v3, [Ljava/lang/Object;

    const/4 v5, 0x0

    aput-object p1, v4, v5

    const/4 p1, 0x1

    aput-object p2, v4, p1

    invoke-static {v1, p0, v2, v4}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodOnSuperN(Ljava/lang/Class;Lgroovy/lang/GroovyObject;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p2

    const-class v1, Lorg/codehaus/groovy/control/CompilationUnit;

    invoke-static {p2, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lorg/codehaus/groovy/control/CompilationUnit;

    .line 97
    aget-object v1, v0, v5

    aget-object p1, v0, p1

    const-class v2, Lorg/codehaus/groovy/tools/ast/TestHarnessOperation;

    iget-object v4, p0, Lorg/codehaus/groovy/tools/ast/TestHarnessClassLoader;->transform:Lorg/codehaus/groovy/transform/ASTTransformation;

    invoke-interface {p1, v2, v4}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    aget-object v0, v0, v3

    iget-object v2, p0, Lorg/codehaus/groovy/tools/ast/TestHarnessClassLoader;->phase:Lorg/codehaus/groovy/control/CompilePhase;

    invoke-interface {v0, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    invoke-interface {v1, p2, p1, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-object p2
.end method

.method public getMetaClass()Lgroovy/lang/MetaClass;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    .annotation runtime Lgroovy/transform/Internal;
    .end annotation

    .annotation runtime Lgroovyjarjaropenbeans/Transient;
    .end annotation

    iget-object v0, p0, Lorg/codehaus/groovy/tools/ast/TestHarnessClassLoader;->metaClass:Lgroovy/lang/MetaClass;

    if-eqz v0, :cond_0

    return-object v0

    :cond_0
    invoke-virtual {p0}, Lorg/codehaus/groovy/tools/ast/TestHarnessClassLoader;->$getStaticMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/tools/ast/TestHarnessClassLoader;->metaClass:Lgroovy/lang/MetaClass;

    return-object v0
.end method

.method public setMetaClass(Lgroovy/lang/MetaClass;)V
    .locals 0
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    .annotation runtime Lgroovy/transform/Internal;
    .end annotation

    iput-object p1, p0, Lorg/codehaus/groovy/tools/ast/TestHarnessClassLoader;->metaClass:Lgroovy/lang/MetaClass;

    return-void
.end method

.method public synthetic super$5$createCompilationUnit(Lorg/codehaus/groovy/control/CompilerConfiguration;Ljava/security/CodeSource;)Lorg/codehaus/groovy/control/CompilationUnit;
    .locals 0

    invoke-super {p0, p1, p2}, Lgroovy/lang/GroovyClassLoader;->createCompilationUnit(Lorg/codehaus/groovy/control/CompilerConfiguration;Ljava/security/CodeSource;)Lorg/codehaus/groovy/control/CompilationUnit;

    move-result-object p1

    return-object p1
.end method
