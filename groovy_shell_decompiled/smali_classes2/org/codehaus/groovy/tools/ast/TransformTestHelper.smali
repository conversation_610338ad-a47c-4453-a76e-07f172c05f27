.class public Lorg/codehaus/groovy/tools/ast/TransformTestHelper;
.super Ljava/lang/Object;
.source "TransformTestHelper.groovy"

# interfaces
.implements Lgroovy/lang/GroovyObject;


# static fields
.field private static synthetic $callSiteArray:Ljava/lang/ref/SoftReference;

.field private static synthetic $staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

.field private static synthetic $staticClassInfo$:Lorg/codehaus/groovy/reflection/ClassInfo;

.field public static transient synthetic __$stMC:Z


# instance fields
.field private transient synthetic metaClass:Lgroovy/lang/MetaClass;

.field private final phase:Lorg/codehaus/groovy/control/CompilePhase;

.field private final transform:Lorg/codehaus/groovy/transform/ASTTransformation;


# direct methods
.method private static synthetic $createCallSiteArray()Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;
    .locals 3

    const/4 v0, 0x4

    new-array v0, v0, [Ljava/lang/String;

    invoke-static {v0}, Lorg/codehaus/groovy/tools/ast/TransformTestHelper;->$createCallSiteArray_1([Ljava/lang/String;)V

    new-instance v1, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;

    const-class v2, Lorg/codehaus/groovy/tools/ast/TransformTestHelper;

    invoke-direct {v1, v2, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;-><init>(Ljava/lang/Class;[Ljava/lang/String;)V

    return-object v1
.end method

.method private static synthetic $createCallSiteArray_1([Ljava/lang/String;)V
    .locals 3

    const/4 v0, 0x0

    const-string v1, "<$constructor$>"

    aput-object v1, p0, v0

    const/4 v0, 0x1

    const-string v2, "parseClass"

    aput-object v2, p0, v0

    const/4 v0, 0x2

    aput-object v1, p0, v0

    const/4 v0, 0x3

    aput-object v2, p0, v0

    return-void
.end method

.method private static synthetic $getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;
    .locals 2

    sget-object v0, Lorg/codehaus/groovy/tools/ast/TransformTestHelper;->$callSiteArray:Ljava/lang/ref/SoftReference;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Ljava/lang/ref/SoftReference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;

    if-nez v0, :cond_1

    :cond_0
    invoke-static {}, Lorg/codehaus/groovy/tools/ast/TransformTestHelper;->$createCallSiteArray()Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;

    move-result-object v0

    new-instance v1, Ljava/lang/ref/SoftReference;

    invoke-direct {v1, v0}, Ljava/lang/ref/SoftReference;-><init>(Ljava/lang/Object;)V

    sput-object v1, Lorg/codehaus/groovy/tools/ast/TransformTestHelper;->$callSiteArray:Ljava/lang/ref/SoftReference;

    :cond_1
    iget-object v0, v0, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;->array:[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    return-object v0
.end method

.method public constructor <init>(Lorg/codehaus/groovy/transform/ASTTransformation;Lorg/codehaus/groovy/control/CompilePhase;)V
    .locals 1

    invoke-static {}, Lorg/codehaus/groovy/tools/ast/TransformTestHelper;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    invoke-virtual {p0}, Lorg/codehaus/groovy/tools/ast/TransformTestHelper;->$getStaticMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/tools/ast/TransformTestHelper;->metaClass:Lgroovy/lang/MetaClass;

    .line 58
    iput-object p1, p0, Lorg/codehaus/groovy/tools/ast/TransformTestHelper;->transform:Lorg/codehaus/groovy/transform/ASTTransformation;

    .line 59
    iput-object p2, p0, Lorg/codehaus/groovy/tools/ast/TransformTestHelper;->phase:Lorg/codehaus/groovy/control/CompilePhase;

    return-void
.end method


# virtual methods
.method protected synthetic $getStaticMetaClass()Lgroovy/lang/MetaClass;
    .locals 2

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    const-class v1, Lorg/codehaus/groovy/tools/ast/TransformTestHelper;

    if-eq v0, v1, :cond_0

    invoke-static {p0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->initMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0

    :cond_0
    sget-object v0, Lorg/codehaus/groovy/tools/ast/TransformTestHelper;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    if-nez v0, :cond_1

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getClassInfo(Ljava/lang/Class;)Lorg/codehaus/groovy/reflection/ClassInfo;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/tools/ast/TransformTestHelper;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    :cond_1
    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0
.end method

.method public getMetaClass()Lgroovy/lang/MetaClass;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    .annotation runtime Lgroovy/transform/Internal;
    .end annotation

    .annotation runtime Lgroovyjarjaropenbeans/Transient;
    .end annotation

    iget-object v0, p0, Lorg/codehaus/groovy/tools/ast/TransformTestHelper;->metaClass:Lgroovy/lang/MetaClass;

    if-eqz v0, :cond_0

    return-object v0

    :cond_0
    invoke-virtual {p0}, Lorg/codehaus/groovy/tools/ast/TransformTestHelper;->$getStaticMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/tools/ast/TransformTestHelper;->metaClass:Lgroovy/lang/MetaClass;

    return-object v0
.end method

.method public parse(Ljava/io/File;)Ljava/lang/Class;
    .locals 5

    invoke-static {}, Lorg/codehaus/groovy/tools/ast/TransformTestHelper;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v0

    const/4 v1, 0x0

    .line 67
    aget-object v1, v0, v1

    const-class v2, Lorg/codehaus/groovy/tools/ast/TestHarnessClassLoader;

    iget-object v3, p0, Lorg/codehaus/groovy/tools/ast/TransformTestHelper;->transform:Lorg/codehaus/groovy/transform/ASTTransformation;

    iget-object v4, p0, Lorg/codehaus/groovy/tools/ast/TransformTestHelper;->phase:Lorg/codehaus/groovy/control/CompilePhase;

    invoke-interface {v1, v2, v3, v4}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    const-class v2, Lorg/codehaus/groovy/tools/ast/TestHarnessClassLoader;

    invoke-static {v1, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/tools/ast/TestHarnessClassLoader;

    const/4 v2, 0x1

    .line 68
    aget-object v0, v0, v2

    invoke-interface {v0, v1, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToClass(Ljava/lang/Object;)Ljava/lang/Class;

    move-result-object p1

    move-object v0, p1

    check-cast v0, Ljava/lang/Class;

    return-object p1
.end method

.method public parse(Ljava/lang/String;)Ljava/lang/Class;
    .locals 5

    invoke-static {}, Lorg/codehaus/groovy/tools/ast/TransformTestHelper;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v0

    const/4 v1, 0x2

    .line 76
    aget-object v1, v0, v1

    const-class v2, Lorg/codehaus/groovy/tools/ast/TestHarnessClassLoader;

    iget-object v3, p0, Lorg/codehaus/groovy/tools/ast/TransformTestHelper;->transform:Lorg/codehaus/groovy/transform/ASTTransformation;

    iget-object v4, p0, Lorg/codehaus/groovy/tools/ast/TransformTestHelper;->phase:Lorg/codehaus/groovy/control/CompilePhase;

    invoke-interface {v1, v2, v3, v4}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    const-class v2, Lorg/codehaus/groovy/tools/ast/TestHarnessClassLoader;

    invoke-static {v1, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/tools/ast/TestHarnessClassLoader;

    const/4 v2, 0x3

    .line 77
    aget-object v0, v0, v2

    invoke-interface {v0, v1, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToClass(Ljava/lang/Object;)Ljava/lang/Class;

    move-result-object p1

    move-object v0, p1

    check-cast v0, Ljava/lang/Class;

    return-object p1
.end method

.method public setMetaClass(Lgroovy/lang/MetaClass;)V
    .locals 0
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    .annotation runtime Lgroovy/transform/Internal;
    .end annotation

    iput-object p1, p0, Lorg/codehaus/groovy/tools/ast/TransformTestHelper;->metaClass:Lgroovy/lang/MetaClass;

    return-void
.end method
