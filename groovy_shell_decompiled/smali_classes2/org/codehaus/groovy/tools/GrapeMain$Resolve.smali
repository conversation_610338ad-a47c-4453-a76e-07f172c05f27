.class Lorg/codehaus/groovy/tools/GrapeMain$Resolve;
.super Ljava/lang/Object;
.source "GrapeMain.groovy"

# interfaces
.implements Ljava/lang/Runnable;
.implements Lgroovy/lang/GroovyObject;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/codehaus/groovy/tools/GrapeMain;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0xa
    name = "Resolve"
.end annotation

.annotation runtime Lgroovyjarjarpicocli/CommandLine$Command;
    customSynopsis = {
        "grape resolve [-adhisv] (<groupId> <artifactId> <version>)+"
    }
    description = {
        "Prints the file locations of the jars representing the artifcats for the specified module(s) and the respective transitive dependencies.",
        "",
        "Parameters:",
        "      <group>     Which module group the module comes from. Translates directly",
        "                    to a Maven groupId or an Ivy Organization. Any group",
        "                    matching /groovy[x][\\..*]^/ is reserved and may have",
        "                    special meaning to the groovy endorsed modules.",
        "      <module>    The name of the module to load. Translated directly to a",
        "                    Maven artifactId or an Ivy artifact.",
        "      <version>   The version of the module to use. Either a literal version",
        "                    `1.1-RC3` or an Ivy Range `[2.2.1,)` meaning 2.2.1 or any",
        "                    greater version)."
    }
    header = {
        "Enumerates the jars used by a grape"
    }
    name = "resolve"
.end annotation


# static fields
.field private static synthetic $callSiteArray:Ljava/lang/ref/SoftReference;

.field private static synthetic $staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

.field private static synthetic $staticClassInfo$:Lorg/codehaus/groovy/reflection/ClassInfo;

.field public static transient synthetic __$stMC:Z


# instance fields
.field private ant:Z
    .annotation runtime Lgroovyjarjarpicocli/CommandLine$Option;
        description = {
            "Express dependencies in a format applicable for an ant script"
        }
        names = {
            "-a",
            "--ant"
        }
    .end annotation
.end field

.field private args:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .annotation runtime Lgroovyjarjarpicocli/CommandLine$Parameters;
        hidden = true
    .end annotation
.end field

.field private dos:Z
    .annotation runtime Lgroovyjarjarpicocli/CommandLine$Option;
        description = {
            "Express dependencies in a format applicable for a windows batch file"
        }
        names = {
            "-d",
            "--dos"
        }
    .end annotation
.end field

.field private ivyFormatRequested:Z
    .annotation runtime Lgroovyjarjarpicocli/CommandLine$Option;
        description = {
            "Express dependencies in an ivy-like format"
        }
        names = {
            "-i",
            "--ivy"
        }
    .end annotation
.end field

.field private transient synthetic metaClass:Lgroovy/lang/MetaClass;

.field private parentCommand:Lorg/codehaus/groovy/tools/GrapeMain;
    .annotation runtime Lgroovyjarjarpicocli/CommandLine$ParentCommand;
    .end annotation
.end field

.field private shell:Z
    .annotation runtime Lgroovyjarjarpicocli/CommandLine$Option;
        description = {
            "Express dependencies in a format applicable for a unix shell script"
        }
        names = {
            "-s",
            "--shell"
        }
    .end annotation
.end field


# direct methods
.method private static synthetic $createCallSiteArray()Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;
    .locals 3

    const/16 v0, 0x28

    new-array v0, v0, [Ljava/lang/String;

    invoke-static {v0}, Lorg/codehaus/groovy/tools/GrapeMain$Resolve;->$createCallSiteArray_1([Ljava/lang/String;)V

    new-instance v1, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;

    const-class v2, Lorg/codehaus/groovy/tools/GrapeMain$Resolve;

    invoke-direct {v1, v2, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;-><init>(Ljava/lang/Class;[Ljava/lang/String;)V

    return-object v1
.end method

.method private static synthetic $createCallSiteArray_1([Ljava/lang/String;)V
    .locals 5

    const/4 v0, 0x0

    const-string v1, "<$constructor$>"

    aput-object v1, p0, v0

    const/4 v0, 0x1

    const-string v2, "init"

    aput-object v2, p0, v0

    const/4 v0, 0x2

    const-string v2, "instance"

    aput-object v2, p0, v0

    const/4 v0, 0x3

    const-string v2, "setupLogging"

    aput-object v2, p0, v0

    const/4 v0, 0x4

    const-string v2, "MSG_ERR"

    aput-object v2, p0, v0

    const/4 v0, 0x5

    const-string v2, "mod"

    aput-object v2, p0, v0

    const/4 v0, 0x6

    const-string v2, "size"

    aput-object v2, p0, v0

    const/4 v0, 0x7

    const-string v3, "println"

    aput-object v3, p0, v0

    const/16 v0, 0x8

    aput-object v2, p0, v0

    const/16 v0, 0x9

    aput-object v3, p0, v0

    const/16 v0, 0xa

    const-string v2, "iterator"

    aput-object v2, p0, v0

    const/16 v0, 0xb

    const-string v4, "leftShift"

    aput-object v4, p0, v0

    const/16 v0, 0xc

    const-string v4, "hasNext"

    aput-object v4, p0, v0

    const/16 v0, 0xd

    const-string v4, "add"

    aput-object v4, p0, v0

    const/16 v0, 0xe

    const-string v4, "next"

    aput-object v4, p0, v0

    const/16 v0, 0xf

    aput-object v4, p0, v0

    const/16 v0, 0x10

    aput-object v4, p0, v0

    const/16 v0, 0x11

    const-string v4, "resolve"

    aput-object v4, p0, v0

    const/16 v0, 0x12

    aput-object v2, p0, v0

    const/16 v0, 0x13

    const-string v2, "scheme"

    aput-object v2, p0, v0

    const/16 v0, 0x14

    const-string v2, "plus"

    aput-object v2, p0, v0

    const/16 v0, 0x15

    const-string v4, "path"

    aput-object v4, p0, v0

    const/16 v0, 0x16

    aput-object v1, p0, v0

    const/16 v0, 0x17

    aput-object v2, p0, v0

    const/16 v0, 0x18

    const-string v1, "toASCIIString"

    aput-object v1, p0, v0

    const/16 v0, 0x19

    const-string v1, "each"

    aput-object v1, p0, v0

    const/16 v0, 0x1a

    aput-object v3, p0, v0

    const/16 v0, 0x1b

    const-string v1, "join"

    aput-object v1, p0, v0

    const/16 v0, 0x1c

    aput-object v3, p0, v0

    const/16 v0, 0x1d

    aput-object v3, p0, v0

    const/16 v0, 0x1e

    const-string v1, "err"

    aput-object v1, p0, v0

    const/16 v0, 0x1f

    const-string v1, "message"

    aput-object v1, p0, v0

    const/16 v0, 0x20

    aput-object v1, p0, v0

    const/16 v0, 0x21

    aput-object v3, p0, v0

    const/16 v0, 0x22

    const-string v1, "length"

    aput-object v1, p0, v0

    const/16 v0, 0x23

    const-string v2, "getAt"

    aput-object v2, p0, v0

    const/16 v0, 0x24

    aput-object v1, p0, v0

    const/16 v0, 0x25

    aput-object v1, p0, v0

    const/16 v0, 0x26

    aput-object v2, p0, v0

    const/16 v0, 0x27

    aput-object v1, p0, v0

    return-void
.end method

.method private static synthetic $getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;
    .locals 2

    sget-object v0, Lorg/codehaus/groovy/tools/GrapeMain$Resolve;->$callSiteArray:Ljava/lang/ref/SoftReference;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Ljava/lang/ref/SoftReference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;

    if-nez v0, :cond_1

    :cond_0
    invoke-static {}, Lorg/codehaus/groovy/tools/GrapeMain$Resolve;->$createCallSiteArray()Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;

    move-result-object v0

    new-instance v1, Ljava/lang/ref/SoftReference;

    invoke-direct {v1, v0}, Ljava/lang/ref/SoftReference;-><init>(Ljava/lang/Object;)V

    sput-object v1, Lorg/codehaus/groovy/tools/GrapeMain$Resolve;->$callSiteArray:Ljava/lang/ref/SoftReference;

    :cond_1
    iget-object v0, v0, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;->array:[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    return-object v0
.end method

.method public static synthetic $static_methodMissing(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 8

    const-class v0, Lorg/codehaus/groovy/tools/GrapeMain$Resolve;

    invoke-static {}, Lorg/codehaus/groovy/tools/GrapeMain$Resolve;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v1

    instance-of v2, p1, [Ljava/lang/Object;

    const/4 v3, 0x1

    xor-int/2addr v2, v3

    const-string v4, ""

    const/4 v5, 0x0

    if-eqz v2, :cond_0

    const-class v1, Lorg/codehaus/groovy/tools/GrapeMain;

    new-instance v2, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v6, v3, [Ljava/lang/Object;

    aput-object p0, v6, v5

    filled-new-array {v4, v4}, [Ljava/lang/String;

    move-result-object p0

    invoke-direct {v2, v6, p0}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p0

    move-object v2, p0

    check-cast v2, Ljava/lang/String;

    new-array v2, v3, [Ljava/lang/Object;

    aput-object p1, v2, v5

    invoke-static {v0, v1, p0, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodN(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    return-object p0

    :cond_0
    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->isOrigInt()Z

    move-result v2

    if-eqz v2, :cond_1

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->isOrigZ()Z

    move-result v2

    if-eqz v2, :cond_1

    sget-boolean v2, Lorg/codehaus/groovy/tools/GrapeMain$Resolve;->__$stMC:Z

    if-nez v2, :cond_1

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->disabledStandardMetaClass()Z

    move-result v2

    if-nez v2, :cond_1

    const/16 v2, 0x27

    aget-object v1, v1, v2

    const-class v2, [Ljava/lang/Object;

    invoke-static {p1, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, [Ljava/lang/Object;

    invoke-interface {v1, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    invoke-static {v1, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_2

    const-class v1, Lorg/codehaus/groovy/tools/GrapeMain;

    new-instance v2, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v6, v3, [Ljava/lang/Object;

    aput-object p0, v6, v5

    filled-new-array {v4, v4}, [Ljava/lang/String;

    move-result-object p0

    invoke-direct {v2, v6, p0}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p0

    move-object v2, p0

    check-cast v2, Ljava/lang/String;

    new-array v2, v3, [Ljava/lang/Object;

    const-class v3, [Ljava/lang/Object;

    invoke-static {p1, v3}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, [Ljava/lang/Object;

    invoke-static {p1, v5}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->objectArrayGet([Ljava/lang/Object;I)Ljava/lang/Object;

    move-result-object p1

    aput-object p1, v2, v5

    invoke-static {v0, v1, p0, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodN(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    return-object p0

    :cond_1
    const/16 v2, 0x25

    aget-object v2, v1, v2

    const-class v6, [Ljava/lang/Object;

    invoke-static {p1, v6}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v6

    check-cast v6, [Ljava/lang/Object;

    invoke-interface {v2, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v6

    invoke-static {v2, v6}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_2

    const-class v2, Lorg/codehaus/groovy/tools/GrapeMain;

    new-instance v6, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v7, v3, [Ljava/lang/Object;

    aput-object p0, v7, v5

    filled-new-array {v4, v4}, [Ljava/lang/String;

    move-result-object p0

    invoke-direct {v6, v7, p0}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v6}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p0

    move-object v4, p0

    check-cast v4, Ljava/lang/String;

    new-array v3, v3, [Ljava/lang/Object;

    const/16 v4, 0x26

    aget-object v1, v1, v4

    const-class v4, [Ljava/lang/Object;

    invoke-static {p1, v4}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, [Ljava/lang/Object;

    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v4

    invoke-interface {v1, p1, v4}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    aput-object p1, v3, v5

    invoke-static {v0, v2, p0, v3}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodN(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    return-object p0

    :cond_2
    const-class v1, Lorg/codehaus/groovy/tools/GrapeMain;

    new-instance v2, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v6, v3, [Ljava/lang/Object;

    aput-object p0, v6, v5

    filled-new-array {v4, v4}, [Ljava/lang/String;

    move-result-object p0

    invoke-direct {v2, v6, p0}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p0

    move-object v2, p0

    check-cast v2, Ljava/lang/String;

    new-array v2, v5, [Ljava/lang/Object;

    new-array v4, v3, [Ljava/lang/Object;

    aput-object p1, v4, v5

    new-array p1, v3, [I

    aput v5, p1, v5

    invoke-static {v2, v4, p1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->despreadList([Ljava/lang/Object;[Ljava/lang/Object;[I)[Ljava/lang/Object;

    move-result-object p1

    invoke-static {v0, v1, p0, p1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodN(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic $static_propertyMissing(Ljava/lang/String;)Ljava/lang/Object;
    .locals 5

    invoke-static {}, Lorg/codehaus/groovy/tools/GrapeMain$Resolve;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    const-class v0, Lorg/codehaus/groovy/tools/GrapeMain$Resolve;

    const-class v1, Lorg/codehaus/groovy/tools/GrapeMain;

    new-instance v2, Lorg/codehaus/groovy/runtime/GStringImpl;

    const/4 v3, 0x1

    new-array v3, v3, [Ljava/lang/Object;

    const/4 v4, 0x0

    aput-object p0, v3, v4

    const-string p0, ""

    filled-new-array {p0, p0}, [Ljava/lang/String;

    move-result-object p0

    invoke-direct {v2, v3, p0}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p0

    move-object v2, p0

    check-cast v2, Ljava/lang/String;

    invoke-static {v0, v1, p0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->getProperty(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic $static_propertyMissing(Ljava/lang/String;Ljava/lang/Object;)V
    .locals 4

    invoke-static {}, Lorg/codehaus/groovy/tools/GrapeMain$Resolve;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    const-class v0, Lorg/codehaus/groovy/tools/GrapeMain;

    new-instance v1, Lorg/codehaus/groovy/runtime/GStringImpl;

    const/4 v2, 0x1

    new-array v2, v2, [Ljava/lang/Object;

    const/4 v3, 0x0

    aput-object p0, v2, v3

    const-string p0, ""

    filled-new-array {p0, p0}, [Ljava/lang/String;

    move-result-object p0

    invoke-direct {v1, v2, p0}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p0

    move-object v1, p0

    check-cast v1, Ljava/lang/String;

    const/4 v1, 0x0

    invoke-static {p1, v1, v0, p0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->setProperty(Ljava/lang/Object;Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;)V

    return-void
.end method

.method public constructor <init>()V
    .locals 2
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    invoke-static {}, Lorg/codehaus/groovy/tools/GrapeMain$Resolve;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v1, 0x0

    .line 231
    aget-object v0, v0, v1

    const-class v1, Ljava/util/ArrayList;

    invoke-interface {v0, v1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    const-class v1, Ljava/util/List;

    invoke-static {v0, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/List;

    iput-object v0, p0, Lorg/codehaus/groovy/tools/GrapeMain$Resolve;->args:Ljava/util/List;

    invoke-virtual {p0}, Lorg/codehaus/groovy/tools/GrapeMain$Resolve;->$getStaticMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/tools/GrapeMain$Resolve;->metaClass:Lgroovy/lang/MetaClass;

    return-void
.end method


# virtual methods
.method protected synthetic $getStaticMetaClass()Lgroovy/lang/MetaClass;
    .locals 2

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    const-class v1, Lorg/codehaus/groovy/tools/GrapeMain$Resolve;

    if-eq v0, v1, :cond_0

    invoke-static {p0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->initMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0

    :cond_0
    sget-object v0, Lorg/codehaus/groovy/tools/GrapeMain$Resolve;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    if-nez v0, :cond_1

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getClassInfo(Ljava/lang/Class;)Lorg/codehaus/groovy/reflection/ClassInfo;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/tools/GrapeMain$Resolve;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    :cond_1
    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0
.end method

.method public getArgs()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-object v0, p0, Lorg/codehaus/groovy/tools/GrapeMain$Resolve;->args:Ljava/util/List;

    return-object v0
.end method

.method public getMetaClass()Lgroovy/lang/MetaClass;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    .annotation runtime Lgroovy/transform/Internal;
    .end annotation

    .annotation runtime Lgroovyjarjaropenbeans/Transient;
    .end annotation

    iget-object v0, p0, Lorg/codehaus/groovy/tools/GrapeMain$Resolve;->metaClass:Lgroovy/lang/MetaClass;

    if-eqz v0, :cond_0

    return-object v0

    :cond_0
    invoke-virtual {p0}, Lorg/codehaus/groovy/tools/GrapeMain$Resolve;->$getStaticMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/tools/GrapeMain$Resolve;->metaClass:Lgroovy/lang/MetaClass;

    return-object v0
.end method

.method public getParentCommand()Lorg/codehaus/groovy/tools/GrapeMain;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-object v0, p0, Lorg/codehaus/groovy/tools/GrapeMain$Resolve;->parentCommand:Lorg/codehaus/groovy/tools/GrapeMain;

    return-object v0
.end method

.method public synthetic methodMissing(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 8

    const-class v0, Lorg/codehaus/groovy/tools/GrapeMain$Resolve;

    invoke-static {}, Lorg/codehaus/groovy/tools/GrapeMain$Resolve;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v1

    instance-of v2, p2, [Ljava/lang/Object;

    const/4 v3, 0x1

    xor-int/2addr v2, v3

    const-string v4, ""

    const/4 v5, 0x0

    if-eqz v2, :cond_0

    const-class v1, Lorg/codehaus/groovy/tools/GrapeMain;

    new-instance v2, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v6, v3, [Ljava/lang/Object;

    aput-object p1, v6, v5

    filled-new-array {v4, v4}, [Ljava/lang/String;

    move-result-object p1

    invoke-direct {v2, v6, p1}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    move-object v2, p1

    check-cast v2, Ljava/lang/String;

    new-array v2, v3, [Ljava/lang/Object;

    aput-object p2, v2, v5

    invoke-static {v0, v1, p1, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodN(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1

    :cond_0
    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->isOrigInt()Z

    move-result v2

    if-eqz v2, :cond_1

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->isOrigZ()Z

    move-result v2

    if-eqz v2, :cond_1

    sget-boolean v2, Lorg/codehaus/groovy/tools/GrapeMain$Resolve;->__$stMC:Z

    if-nez v2, :cond_1

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->disabledStandardMetaClass()Z

    move-result v2

    if-nez v2, :cond_1

    const/16 v2, 0x24

    aget-object v1, v1, v2

    const-class v2, [Ljava/lang/Object;

    invoke-static {p2, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, [Ljava/lang/Object;

    invoke-interface {v1, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    invoke-static {v1, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_2

    const-class v1, Lorg/codehaus/groovy/tools/GrapeMain;

    new-instance v2, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v6, v3, [Ljava/lang/Object;

    aput-object p1, v6, v5

    filled-new-array {v4, v4}, [Ljava/lang/String;

    move-result-object p1

    invoke-direct {v2, v6, p1}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    move-object v2, p1

    check-cast v2, Ljava/lang/String;

    new-array v2, v3, [Ljava/lang/Object;

    const-class v3, [Ljava/lang/Object;

    invoke-static {p2, v3}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, [Ljava/lang/Object;

    invoke-static {p2, v5}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->objectArrayGet([Ljava/lang/Object;I)Ljava/lang/Object;

    move-result-object p2

    aput-object p2, v2, v5

    invoke-static {v0, v1, p1, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodN(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1

    :cond_1
    const/16 v2, 0x22

    aget-object v2, v1, v2

    const-class v6, [Ljava/lang/Object;

    invoke-static {p2, v6}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v6

    check-cast v6, [Ljava/lang/Object;

    invoke-interface {v2, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v6

    invoke-static {v2, v6}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_2

    const-class v2, Lorg/codehaus/groovy/tools/GrapeMain;

    new-instance v6, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v7, v3, [Ljava/lang/Object;

    aput-object p1, v7, v5

    filled-new-array {v4, v4}, [Ljava/lang/String;

    move-result-object p1

    invoke-direct {v6, v7, p1}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v6}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    move-object v4, p1

    check-cast v4, Ljava/lang/String;

    new-array v3, v3, [Ljava/lang/Object;

    const/16 v4, 0x23

    aget-object v1, v1, v4

    const-class v4, [Ljava/lang/Object;

    invoke-static {p2, v4}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, [Ljava/lang/Object;

    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v4

    invoke-interface {v1, p2, v4}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p2

    aput-object p2, v3, v5

    invoke-static {v0, v2, p1, v3}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodN(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1

    :cond_2
    const-class v1, Lorg/codehaus/groovy/tools/GrapeMain;

    new-instance v2, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v6, v3, [Ljava/lang/Object;

    aput-object p1, v6, v5

    filled-new-array {v4, v4}, [Ljava/lang/String;

    move-result-object p1

    invoke-direct {v2, v6, p1}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    move-object v2, p1

    check-cast v2, Ljava/lang/String;

    new-array v2, v5, [Ljava/lang/Object;

    new-array v4, v3, [Ljava/lang/Object;

    aput-object p2, v4, v5

    new-array p2, v3, [I

    aput v5, p2, v5

    invoke-static {v2, v4, p2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->despreadList([Ljava/lang/Object;[Ljava/lang/Object;[I)[Ljava/lang/Object;

    move-result-object p2

    invoke-static {v0, v1, p1, p2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodN(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public synthetic propertyMissing(Ljava/lang/String;)Ljava/lang/Object;
    .locals 5

    invoke-static {}, Lorg/codehaus/groovy/tools/GrapeMain$Resolve;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    const-class v0, Lorg/codehaus/groovy/tools/GrapeMain$Resolve;

    const-class v1, Lorg/codehaus/groovy/tools/GrapeMain;

    new-instance v2, Lorg/codehaus/groovy/runtime/GStringImpl;

    const/4 v3, 0x1

    new-array v3, v3, [Ljava/lang/Object;

    const/4 v4, 0x0

    aput-object p1, v3, v4

    const-string p1, ""

    filled-new-array {p1, p1}, [Ljava/lang/String;

    move-result-object p1

    invoke-direct {v2, v3, p1}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    move-object v2, p1

    check-cast v2, Ljava/lang/String;

    invoke-static {v0, v1, p1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->getProperty(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public synthetic propertyMissing(Ljava/lang/String;Ljava/lang/Object;)V
    .locals 4

    invoke-static {}, Lorg/codehaus/groovy/tools/GrapeMain$Resolve;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    const-class v0, Lorg/codehaus/groovy/tools/GrapeMain;

    new-instance v1, Lorg/codehaus/groovy/runtime/GStringImpl;

    const/4 v2, 0x1

    new-array v2, v2, [Ljava/lang/Object;

    const/4 v3, 0x0

    aput-object p1, v2, v3

    const-string p1, ""

    filled-new-array {p1, p1}, [Ljava/lang/String;

    move-result-object p1

    invoke-direct {v1, v2, p1}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    move-object v1, p1

    check-cast v1, Ljava/lang/String;

    const/4 v1, 0x0

    invoke-static {p2, v1, v0, p1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->setProperty(Ljava/lang/Object;Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;)V

    return-void
.end method

.method public run()V
    .locals 19

    move-object/from16 v1, p0

    invoke-static {}, Lorg/codehaus/groovy/tools/GrapeMain$Resolve;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v2

    const/4 v3, 0x1

    .line 236
    aget-object v0, v2, v3

    iget-object v4, v1, Lorg/codehaus/groovy/tools/GrapeMain$Resolve;->parentCommand:Lorg/codehaus/groovy/tools/GrapeMain;

    invoke-interface {v0, v4}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    const/4 v0, 0x2

    .line 239
    aget-object v4, v2, v0

    const-class v5, Lgroovy/grape/Grape;

    invoke-interface {v4, v5}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    const/4 v4, 0x3

    .line 242
    invoke-static {v4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v5

    .line 240
    aget-object v6, v2, v4

    iget-object v7, v1, Lorg/codehaus/groovy/tools/GrapeMain$Resolve;->parentCommand:Lorg/codehaus/groovy/tools/GrapeMain;

    const/4 v8, 0x4

    aget-object v9, v2, v8

    const-class v10, Lorg/apache/ivy/util/Message;

    invoke-interface {v9, v10}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v9

    invoke-interface {v6, v7, v9}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/4 v6, 0x5

    .line 242
    aget-object v7, v2, v6

    const/4 v9, 0x6

    aget-object v10, v2, v9

    iget-object v11, v1, Lorg/codehaus/groovy/tools/GrapeMain$Resolve;->args:Ljava/util/List;

    invoke-interface {v10, v11}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v10

    invoke-interface {v7, v10, v5}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v7

    const/4 v10, 0x0

    invoke-static {v10}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v11

    invoke-static {v7, v11}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareNotEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v7

    if-eqz v7, :cond_0

    const/4 v0, 0x7

    .line 243
    aget-object v0, v2, v0

    const-string v2, "There needs to be a multiple of three arguments: (group module version)+"

    invoke-interface {v0, v1, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callCurrent(Lgroovy/lang/GroovyObject;Ljava/lang/Object;)Ljava/lang/Object;

    return-void

    :cond_0
    const/16 v7, 0x8

    .line 246
    aget-object v7, v2, v7

    iget-object v11, v1, Lorg/codehaus/groovy/tools/GrapeMain$Resolve;->args:Ljava/util/List;

    invoke-interface {v7, v11}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v7

    invoke-static {v7, v5}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareLessThan(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v5

    if-eqz v5, :cond_1

    const/16 v0, 0x9

    .line 247
    aget-object v0, v2, v0

    const-string v2, "At least one Grape reference is required"

    invoke-interface {v0, v1, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callCurrent(Lgroovy/lang/GroovyObject;Ljava/lang/Object;)Ljava/lang/Object;

    return-void

    .line 252
    :cond_1
    iget-boolean v5, v1, Lorg/codehaus/groovy/tools/GrapeMain$Resolve;->ant:Z

    const-string v7, "\n"

    const-string v11, "\">"

    const-string v12, ""

    if-eqz v5, :cond_2

    const-string v5, "<pathelement location=\""

    const-string v7, "\">\n<pathelement location=\""

    goto :goto_1

    .line 256
    :cond_2
    iget-boolean v5, v1, Lorg/codehaus/groovy/tools/GrapeMain$Resolve;->dos:Z

    if-eqz v5, :cond_3

    const-string v5, "set CLASSPATH="

    const-string v7, ";"

    :goto_0
    move-object v11, v12

    goto :goto_1

    .line 260
    :cond_3
    iget-boolean v5, v1, Lorg/codehaus/groovy/tools/GrapeMain$Resolve;->shell:Z

    if-eqz v5, :cond_4

    const-string v5, "export CLASSPATH="

    const-string v7, ":"

    goto :goto_0

    .line 264
    :cond_4
    iget-boolean v5, v1, Lorg/codehaus/groovy/tools/GrapeMain$Resolve;->ivyFormatRequested:Z

    if-eqz v5, :cond_5

    const-string v5, "<dependency "

    const-string v7, "\">\n<dependency "

    goto :goto_1

    :cond_5
    move-object v11, v7

    move-object v5, v12

    :goto_1
    const/16 v13, 0xa

    .line 274
    aget-object v13, v2, v13

    iget-object v14, v1, Lorg/codehaus/groovy/tools/GrapeMain$Resolve;->args:Ljava/util/List;

    invoke-interface {v13, v14}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v13

    new-array v14, v3, [Ljava/lang/Object;

    new-array v15, v10, [Ljava/lang/Object;

    .line 275
    invoke-static {v15}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createMap([Ljava/lang/Object;)Ljava/util/Map;

    move-result-object v15

    aput-object v15, v14, v10

    invoke-static {v14}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v14

    new-array v15, v10, [Ljava/lang/Object;

    .line 276
    invoke-static {v15}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v15

    .line 277
    iget-boolean v6, v1, Lorg/codehaus/groovy/tools/GrapeMain$Resolve;->ivyFormatRequested:Z

    if-eqz v6, :cond_6

    const/16 v6, 0xb

    .line 278
    aget-object v6, v2, v6

    invoke-interface {v6, v14, v15}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_6
    :goto_2
    const/16 v6, 0xc

    .line 280
    aget-object v6, v2, v6

    invoke-interface {v6, v13}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    invoke-static {v6}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v6

    if-eqz v6, :cond_7

    const/16 v6, 0xd

    .line 281
    aget-object v6, v2, v6

    new-array v8, v9, [Ljava/lang/Object;

    const-string v18, "group"

    aput-object v18, v8, v10

    const/16 v18, 0xe

    aget-object v9, v2, v18

    invoke-interface {v9, v13}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v9

    aput-object v9, v8, v3

    const-string v9, "module"

    aput-object v9, v8, v0

    const/16 v9, 0xf

    aget-object v9, v2, v9

    invoke-interface {v9, v13}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v9

    aput-object v9, v8, v4

    const-string v9, "version"

    const/16 v17, 0x4

    aput-object v9, v8, v17

    const/16 v9, 0x10

    aget-object v9, v2, v9

    invoke-interface {v9, v13}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v9

    const/16 v16, 0x5

    aput-object v9, v8, v16

    invoke-static {v8}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createMap([Ljava/lang/Object;)Ljava/util/Map;

    move-result-object v8

    invoke-interface {v6, v14, v8}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move/from16 v8, v17

    const/4 v9, 0x6

    goto :goto_2

    :cond_7
    :try_start_0
    new-array v6, v10, [Ljava/lang/Object;

    .line 284
    invoke-static {v6}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v6

    new-instance v8, Lgroovy/lang/Reference;

    invoke-direct {v8, v6}, Lgroovy/lang/Reference;-><init>(Ljava/lang/Object;)V

    const/16 v6, 0x11

    .line 285
    aget-object v6, v2, v6

    const-class v9, Lgroovy/grape/Grape;

    new-array v13, v10, [Ljava/lang/Object;

    new-array v0, v3, [Ljava/lang/Object;

    aput-object v14, v0, v10

    new-array v14, v3, [I

    aput v10, v14, v10

    invoke-static {v13, v0, v14}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->despreadList([Ljava/lang/Object;[Ljava/lang/Object;[I)[Ljava/lang/Object;

    move-result-object v0

    invoke-interface {v6, v9, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    .line 286
    iget-boolean v6, v1, Lorg/codehaus/groovy/tools/GrapeMain$Resolve;->ivyFormatRequested:Z

    xor-int/2addr v6, v3

    if-eqz v6, :cond_9

    const/16 v6, 0x12

    .line 287
    aget-object v6, v2, v6

    invoke-interface {v6, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    const-class v6, Ljava/util/Iterator;

    invoke-static {v0, v6}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/Iterator;

    if-eqz v0, :cond_a

    :goto_3
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v6

    if-eqz v6, :cond_a

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v6

    const-class v9, Ljava/net/URI;

    invoke-static {v6, v9}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Ljava/net/URI;

    const/16 v9, 0x13

    .line 288
    aget-object v9, v2, v9

    invoke-interface {v9, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v9

    const-string v13, "file"

    invoke-static {v9, v13}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v9

    if-eqz v9, :cond_8

    const/16 v9, 0x14

    .line 289
    aget-object v9, v2, v9

    invoke-virtual {v8}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v13

    const/16 v14, 0x15

    aget-object v14, v2, v14

    const/16 v15, 0x16

    aget-object v15, v2, v15

    const-class v3, Ljava/io/File;

    invoke-interface {v15, v3, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    invoke-interface {v14, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    invoke-interface {v9, v13, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    move-object v6, v8

    check-cast v6, Lgroovy/lang/Reference;

    invoke-virtual {v8, v3}, Lgroovy/lang/Reference;->set(Ljava/lang/Object;)V

    goto :goto_4

    :cond_8
    const/16 v3, 0x17

    .line 291
    aget-object v3, v2, v3

    invoke-virtual {v8}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v9

    const/16 v13, 0x18

    aget-object v13, v2, v13

    invoke-interface {v13, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    invoke-interface {v3, v9, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    move-object v6, v8

    check-cast v6, Lgroovy/lang/Reference;

    invoke-virtual {v8, v3}, Lgroovy/lang/Reference;->set(Ljava/lang/Object;)V

    :goto_4
    const/4 v3, 0x1

    goto :goto_3

    :cond_9
    const/16 v0, 0x19

    .line 295
    aget-object v0, v2, v0

    new-instance v3, Lorg/codehaus/groovy/tools/GrapeMain$Resolve$_run_closure1;

    invoke-direct {v3, v1, v1, v8}, Lorg/codehaus/groovy/tools/GrapeMain$Resolve$_run_closure1;-><init>(Ljava/lang/Object;Ljava/lang/Object;Lgroovy/lang/Reference;)V

    invoke-interface {v0, v15, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 300
    :cond_a
    invoke-virtual {v8}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_b

    const/16 v0, 0x1a

    .line 301
    aget-object v0, v2, v0

    new-instance v3, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v4, v4, [Ljava/lang/Object;

    aput-object v5, v4, v10

    const/16 v5, 0x1b

    aget-object v5, v2, v5

    invoke-virtual {v8}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v6

    invoke-interface {v5, v6, v7}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    const/4 v6, 0x1

    aput-object v5, v4, v6

    const/4 v5, 0x2

    aput-object v11, v4, v5

    filled-new-array {v12, v12, v12, v12}, [Ljava/lang/String;

    move-result-object v5

    invoke-direct {v3, v4, v5}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-interface {v0, v1, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callCurrent(Lgroovy/lang/GroovyObject;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_5

    :cond_b
    const/16 v0, 0x1c

    .line 303
    aget-object v0, v2, v0

    const-string v3, "Nothing was resolved"

    invoke-interface {v0, v1, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callCurrent(Lgroovy/lang/GroovyObject;Ljava/lang/Object;)Ljava/lang/Object;
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_5

    :catchall_0
    move-exception v0

    goto :goto_6

    :catch_0
    move-exception v0

    const/16 v3, 0x1d

    .line 306
    :try_start_1
    aget-object v3, v2, v3

    const/16 v4, 0x1e

    aget-object v4, v2, v4

    const-class v5, Ljava/lang/System;

    invoke-interface {v4, v5}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    new-instance v5, Lorg/codehaus/groovy/runtime/GStringImpl;

    const/4 v6, 0x1

    new-array v6, v6, [Ljava/lang/Object;

    const/16 v7, 0x1f

    aget-object v7, v2, v7

    invoke-interface {v7, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v7

    aput-object v7, v6, v10

    const-string v7, "Error in resolve:\n\t"

    filled-new-array {v7, v12}, [Ljava/lang/String;

    move-result-object v7

    invoke-direct {v5, v6, v7}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-interface {v3, v4, v5}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v3, 0x20

    .line 307
    aget-object v3, v2, v3

    invoke-interface {v3, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    const-string v3, "unresolved dependency"

    invoke-static {v0, v3}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->findRegex(Ljava/lang/Object;Ljava/lang/Object;)Ljava/util/regex/Matcher;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_c

    const/16 v0, 0x21

    aget-object v0, v2, v0

    const-string v2, "Perhaps the grape is not installed?"

    invoke-interface {v0, v1, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callCurrent(Lgroovy/lang/GroovyObject;Ljava/lang/Object;)Ljava/lang/Object;
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    :cond_c
    :goto_5
    return-void

    .line 308
    :goto_6
    throw v0
.end method

.method public setArgs(Ljava/util/List;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;)V"
        }
    .end annotation

    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iput-object p1, p0, Lorg/codehaus/groovy/tools/GrapeMain$Resolve;->args:Ljava/util/List;

    return-void
.end method

.method public setMetaClass(Lgroovy/lang/MetaClass;)V
    .locals 0
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    .annotation runtime Lgroovy/transform/Internal;
    .end annotation

    iput-object p1, p0, Lorg/codehaus/groovy/tools/GrapeMain$Resolve;->metaClass:Lgroovy/lang/MetaClass;

    return-void
.end method

.method public setParentCommand(Lorg/codehaus/groovy/tools/GrapeMain;)V
    .locals 0
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iput-object p1, p0, Lorg/codehaus/groovy/tools/GrapeMain$Resolve;->parentCommand:Lorg/codehaus/groovy/tools/GrapeMain;

    return-void
.end method
