.class public Lorg/codehaus/groovy/tools/FileSystemCompiler$CompilationOptions;
.super Ljava/lang/Object;
.source "FileSystemCompiler.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/codehaus/groovy/tools/FileSystemCompiler;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "CompilationOptions"
.end annotation

.annotation runtime Lgroovyjarjarpicocli/CommandLine$Command;
    customSynopsis = {
        "groovyc [options] <source-files>"
    }
    name = "groovyc"
    sortOptions = false
    versionProvider = Lorg/codehaus/groovy/tools/FileSystemCompiler$VersionProvider;
.end annotation


# instance fields
.field private classpath:Ljava/lang/String;
    .annotation runtime Lgroovyjarjarpicocli/CommandLine$Option;
        description = {
            "Specify where to find the class files - must be first argument"
        }
        names = {
            "-cp",
            "-classpath",
            "--classpath"
        }
        paramLabel = "<path>"
    .end annotation
.end field

.field private compileStatic:Z
    .annotation runtime Lgroovyjarjarpicocli/CommandLine$Option;
        description = {
            "Use CompileStatic"
        }
        names = {
            "--compile-static"
        }
    .end annotation
.end field

.field private configScript:Ljava/lang/String;
    .annotation runtime Lgroovyjarjarpicocli/CommandLine$Option;
        description = {
            "A script for tweaking the configuration options"
        }
        names = {
            "--configscript"
        }
        paramLabel = "<script>"
    .end annotation
.end field

.field private encoding:Ljava/lang/String;
    .annotation runtime Lgroovyjarjarpicocli/CommandLine$Option;
        description = {
            "Specify the encoding of the user class files"
        }
        names = {
            "--encoding"
        }
    .end annotation
.end field

.field private files:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .annotation runtime Lgroovyjarjarpicocli/CommandLine$Parameters;
        description = {
            "The groovy source files to compile, or @-files containing a list of source files to compile"
        }
        paramLabel = "<source-files>"
    .end annotation
.end field

.field private flags:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .annotation runtime Lgroovyjarjarpicocli/CommandLine$Option;
        description = {
            "Passed to javac for joint compilation"
        }
        names = {
            "-F"
        }
        paramLabel = "<flag>"
    .end annotation
.end field

.field private helpRequested:Z
    .annotation runtime Lgroovyjarjarpicocli/CommandLine$Option;
        description = {
            "Show this help message and exit"
        }
        names = {
            "-h",
            "--help"
        }
        usageHelp = true
    .end annotation
.end field

.field private indy:Z
    .annotation runtime Lgroovyjarjarpicocli/CommandLine$Option;
        description = {
            "Enables compilation using invokedynamic"
        }
        names = {
            "--indy"
        }
    .end annotation
.end field

.field private javacOptionsMap:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .annotation runtime Lgroovyjarjarpicocli/CommandLine$Option;
        description = {
            "Name-value pairs to pass to javac"
        }
        names = {
            "-J"
        }
        paramLabel = "<property=value>"
    .end annotation
.end field

.field private jointCompilation:Z
    .annotation runtime Lgroovyjarjarpicocli/CommandLine$Option;
        description = {
            "Attach javac compiler to compile .java files"
        }
        names = {
            "-j",
            "--jointCompilation"
        }
    .end annotation
.end field

.field private parameterMetadata:Z
    .annotation runtime Lgroovyjarjarpicocli/CommandLine$Option;
        description = {
            "Generate metadata for reflection on method parameter names (jdk8+ only)"
        }
        names = {
            "-pa",
            "--parameters"
        }
    .end annotation
.end field

.field private previewFeatures:Z
    .annotation runtime Lgroovyjarjarpicocli/CommandLine$Option;
        description = {
            "Enable preview Java features (jdk12+ only) - must be after classpath but before other arguments"
        }
        names = {
            "-pr",
            "--enable-preview"
        }
    .end annotation
.end field

.field private printStack:Z
    .annotation runtime Lgroovyjarjarpicocli/CommandLine$Option;
        description = {
            "Print stack trace on error"
        }
        names = {
            "-e",
            "--exception"
        }
    .end annotation
.end field

.field private scriptBaseClass:Ljava/lang/String;
    .annotation runtime Lgroovyjarjarpicocli/CommandLine$Option;
        description = {
            "Base class name for scripts (must derive from Script)"
        }
        names = {
            "-b",
            "--basescript"
        }
        paramLabel = "<class>"
    .end annotation
.end field

.field private sourcepath:Ljava/io/File;
    .annotation runtime Lgroovyjarjarpicocli/CommandLine$Option;
        description = {
            "Specify where to find the source files"
        }
        names = {
            "-sourcepath",
            "--sourcepath"
        }
        paramLabel = "<path>"
    .end annotation
.end field

.field private targetDir:Ljava/io/File;
    .annotation runtime Lgroovyjarjarpicocli/CommandLine$Option;
        description = {
            "Specify where to place generated class files"
        }
        names = {
            "-d"
        }
        paramLabel = "<dir>"
    .end annotation
.end field

.field private temp:Ljava/io/File;
    .annotation runtime Lgroovyjarjarpicocli/CommandLine$Option;
        description = {
            "Specify temporary directory"
        }
        names = {
            "--temp"
        }
        paramLabel = "<temp>"
    .end annotation
.end field

.field private typeChecked:Z
    .annotation runtime Lgroovyjarjarpicocli/CommandLine$Option;
        description = {
            "Use TypeChecked"
        }
        names = {
            "--type-checked"
        }
    .end annotation
.end field

.field private versionRequested:Z
    .annotation runtime Lgroovyjarjarpicocli/CommandLine$Option;
        description = {
            "Print version information and exit"
        }
        names = {
            "-v",
            "--version"
        }
        versionHelp = true
    .end annotation
.end field


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 340
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method static synthetic access$000(Lorg/codehaus/groovy/tools/FileSystemCompiler$CompilationOptions;)Z
    .locals 0

    .line 340
    iget-boolean p0, p0, Lorg/codehaus/groovy/tools/FileSystemCompiler$CompilationOptions;->printStack:Z

    return p0
.end method

.method private javacFlags()[Ljava/lang/String;
    .locals 2

    .line 475
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 476
    iget-object v1, p0, Lorg/codehaus/groovy/tools/FileSystemCompiler$CompilationOptions;->flags:Ljava/util/List;

    if-eqz v1, :cond_0

    .line 477
    invoke-interface {v0, v1}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 479
    :cond_0
    iget-boolean v1, p0, Lorg/codehaus/groovy/tools/FileSystemCompiler$CompilationOptions;->parameterMetadata:Z

    if-eqz v1, :cond_1

    const-string v1, "parameters"

    .line 480
    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 482
    :cond_1
    iget-boolean v1, p0, Lorg/codehaus/groovy/tools/FileSystemCompiler$CompilationOptions;->previewFeatures:Z

    if-eqz v1, :cond_2

    const-string v1, "-enable-preview"

    .line 483
    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 485
    :cond_2
    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v1

    if-eqz v1, :cond_3

    const/4 v0, 0x0

    goto :goto_0

    :cond_3
    const/4 v1, 0x0

    new-array v1, v1, [Ljava/lang/String;

    invoke-interface {v0, v1}, Ljava/util/List;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Ljava/lang/String;

    :goto_0
    return-object v0
.end method

.method private javacNamedValues()[Ljava/lang/String;
    .locals 4

    .line 464
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 465
    iget-object v1, p0, Lorg/codehaus/groovy/tools/FileSystemCompiler$CompilationOptions;->javacOptionsMap:Ljava/util/Map;

    if-eqz v1, :cond_0

    .line 466
    invoke-interface {v1}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_0

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/util/Map$Entry;

    .line 467
    invoke-interface {v2}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/lang/String;

    invoke-interface {v0, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 468
    invoke-interface {v2}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/lang/String;

    invoke-interface {v0, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    .line 471
    :cond_0
    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v1

    if-eqz v1, :cond_1

    const/4 v0, 0x0

    goto :goto_1

    :cond_1
    const/4 v1, 0x0

    new-array v1, v1, [Ljava/lang/String;

    invoke-interface {v0, v1}, Ljava/util/List;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Ljava/lang/String;

    :goto_1
    return-object v0
.end method


# virtual methods
.method public generateFileNames()[Ljava/lang/String;
    .locals 1

    .line 460
    iget-object v0, p0, Lorg/codehaus/groovy/tools/FileSystemCompiler$CompilationOptions;->files:Ljava/util/List;

    invoke-static {v0}, Lorg/codehaus/groovy/tools/FileSystemCompiler;->access$100(Ljava/util/List;)[Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public toCompilerConfiguration()Lorg/codehaus/groovy/control/CompilerConfiguration;
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 405
    new-instance v0, Lorg/codehaus/groovy/control/CompilerConfiguration;

    invoke-direct {v0}, Lorg/codehaus/groovy/control/CompilerConfiguration;-><init>()V

    .line 407
    iget-object v1, p0, Lorg/codehaus/groovy/tools/FileSystemCompiler$CompilationOptions;->classpath:Ljava/lang/String;

    if-eqz v1, :cond_0

    .line 408
    invoke-virtual {v0, v1}, Lorg/codehaus/groovy/control/CompilerConfiguration;->setClasspath(Ljava/lang/String;)V

    .line 411
    :cond_0
    iget-object v1, p0, Lorg/codehaus/groovy/tools/FileSystemCompiler$CompilationOptions;->targetDir:Ljava/io/File;

    if-eqz v1, :cond_1

    invoke-virtual {v1}, Ljava/io/File;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    if-lez v1, :cond_1

    .line 412
    iget-object v1, p0, Lorg/codehaus/groovy/tools/FileSystemCompiler$CompilationOptions;->targetDir:Ljava/io/File;

    invoke-virtual {v0, v1}, Lorg/codehaus/groovy/control/CompilerConfiguration;->setTargetDirectory(Ljava/io/File;)V

    .line 415
    :cond_1
    iget-boolean v1, p0, Lorg/codehaus/groovy/tools/FileSystemCompiler$CompilationOptions;->parameterMetadata:Z

    invoke-virtual {v0, v1}, Lorg/codehaus/groovy/control/CompilerConfiguration;->setParameters(Z)V

    .line 416
    iget-boolean v1, p0, Lorg/codehaus/groovy/tools/FileSystemCompiler$CompilationOptions;->previewFeatures:Z

    invoke-virtual {v0, v1}, Lorg/codehaus/groovy/control/CompilerConfiguration;->setPreviewFeatures(Z)V

    .line 417
    iget-object v1, p0, Lorg/codehaus/groovy/tools/FileSystemCompiler$CompilationOptions;->encoding:Ljava/lang/String;

    invoke-virtual {v0, v1}, Lorg/codehaus/groovy/control/CompilerConfiguration;->setSourceEncoding(Ljava/lang/String;)V

    .line 418
    iget-object v1, p0, Lorg/codehaus/groovy/tools/FileSystemCompiler$CompilationOptions;->scriptBaseClass:Ljava/lang/String;

    invoke-virtual {v0, v1}, Lorg/codehaus/groovy/control/CompilerConfiguration;->setScriptBaseClass(Ljava/lang/String;)V

    .line 421
    iget-boolean v1, p0, Lorg/codehaus/groovy/tools/FileSystemCompiler$CompilationOptions;->jointCompilation:Z

    if-eqz v1, :cond_2

    .line 422
    new-instance v1, Ljava/util/HashMap;

    invoke-direct {v1}, Ljava/util/HashMap;-><init>()V

    .line 423
    invoke-direct {p0}, Lorg/codehaus/groovy/tools/FileSystemCompiler$CompilationOptions;->javacFlags()[Ljava/lang/String;

    move-result-object v2

    const-string v3, "flags"

    invoke-interface {v1, v3, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 424
    invoke-direct {p0}, Lorg/codehaus/groovy/tools/FileSystemCompiler$CompilationOptions;->javacNamedValues()[Ljava/lang/String;

    move-result-object v2

    const-string v3, "namedValues"

    invoke-interface {v1, v3, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 425
    invoke-virtual {v0, v1}, Lorg/codehaus/groovy/control/CompilerConfiguration;->setJointCompilationOptions(Ljava/util/Map;)V

    .line 428
    :cond_2
    iget-boolean v1, p0, Lorg/codehaus/groovy/tools/FileSystemCompiler$CompilationOptions;->indy:Z

    if-eqz v1, :cond_3

    .line 429
    invoke-virtual {v0}, Lorg/codehaus/groovy/control/CompilerConfiguration;->getOptimizationOptions()Ljava/util/Map;

    move-result-object v1

    sget-object v2, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    const-string v3, "int"

    invoke-interface {v1, v3, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 430
    invoke-virtual {v0}, Lorg/codehaus/groovy/control/CompilerConfiguration;->getOptimizationOptions()Ljava/util/Map;

    move-result-object v1

    sget-object v2, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    const-string v3, "indy"

    invoke-interface {v1, v3, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 433
    :cond_3
    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    .line 434
    iget-boolean v2, p0, Lorg/codehaus/groovy/tools/FileSystemCompiler$CompilationOptions;->compileStatic:Z

    if-eqz v2, :cond_4

    const-string v2, "ast(groovy.transform.CompileStatic)"

    .line 435
    invoke-interface {v1, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 437
    :cond_4
    iget-boolean v2, p0, Lorg/codehaus/groovy/tools/FileSystemCompiler$CompilationOptions;->typeChecked:Z

    if-eqz v2, :cond_5

    const-string v2, "ast(groovy.transform.TypeChecked)"

    .line 438
    invoke-interface {v1, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 440
    :cond_5
    invoke-interface {v1}, Ljava/util/List;->isEmpty()Z

    move-result v2

    if-nez v2, :cond_6

    .line 441
    invoke-static {v1}, Lgroovy/ui/GroovyMain;->buildConfigScriptText(Ljava/util/List;)Ljava/lang/String;

    move-result-object v1

    invoke-static {v1, v0}, Lgroovy/ui/GroovyMain;->processConfigScriptText(Ljava/lang/String;Lorg/codehaus/groovy/control/CompilerConfiguration;)V

    :cond_6
    const/4 v1, 0x0

    const-string v2, "groovy.starter.configscripts"

    .line 444
    invoke-static {v2, v1}, Ljava/lang/System;->getProperty(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    .line 445
    iget-object v2, p0, Lorg/codehaus/groovy/tools/FileSystemCompiler$CompilationOptions;->configScript:Ljava/lang/String;

    if-nez v2, :cond_7

    if-eqz v1, :cond_a

    invoke-virtual {v1}, Ljava/lang/String;->isEmpty()Z

    move-result v2

    if-nez v2, :cond_a

    .line 446
    :cond_7
    new-instance v2, Ljava/util/ArrayList;

    invoke-direct {v2}, Ljava/util/ArrayList;-><init>()V

    .line 447
    iget-object v3, p0, Lorg/codehaus/groovy/tools/FileSystemCompiler$CompilationOptions;->configScript:Ljava/lang/String;

    if-eqz v3, :cond_8

    .line 448
    invoke-interface {v2, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :cond_8
    if-eqz v1, :cond_9

    const/16 v3, 0x2c

    .line 451
    invoke-static {v3}, Ljava/lang/Character;->valueOf(C)Ljava/lang/Character;

    move-result-object v3

    invoke-static {v1, v3}, Lorg/codehaus/groovy/runtime/StringGroovyMethods;->tokenize(Ljava/lang/CharSequence;Ljava/lang/Character;)Ljava/util/List;

    move-result-object v1

    invoke-interface {v2, v1}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 453
    :cond_9
    invoke-static {v2, v0}, Lgroovy/ui/GroovyMain;->processConfigScripts(Ljava/util/List;Lorg/codehaus/groovy/control/CompilerConfiguration;)V

    :cond_a
    return-object v0
.end method
