.class public Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingSupport$IntArrayStaticTypesHelper;
.super Ljava/lang/Object;
.source "StaticTypeCheckingSupport.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingSupport;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "IntArrayStaticTypesHelper"
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 2349
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static getAt([II)Ljava/lang/Integer;
    .locals 0

    if-eqz p0, :cond_0

    .line 2351
    aget p0, p0, p1

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    goto :goto_0

    :cond_0
    const/4 p0, 0x0

    :goto_0
    return-object p0
.end method

.method public static putAt([III)V
    .locals 0

    if-eqz p0, :cond_0

    .line 2355
    aput p2, p0, p1

    :cond_0
    return-void
.end method
