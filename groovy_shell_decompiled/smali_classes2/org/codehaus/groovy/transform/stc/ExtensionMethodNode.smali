.class public Lorg/codehaus/groovy/transform/stc/ExtensionMethodNode;
.super Lorg/codehaus/groovy/ast/MethodNode;
.source "ExtensionMethodNode.java"


# instance fields
.field private final extensionMethodNode:Lorg/codehaus/groovy/ast/MethodNode;

.field private final isStaticExtension:Z


# direct methods
.method public constructor <init>(Lorg/codehaus/groovy/ast/MethodNode;Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)V
    .locals 9

    const/4 v8, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move v3, p3

    move-object v4, p4

    move-object v5, p5

    move-object v6, p6

    move-object/from16 v7, p7

    .line 45
    invoke-direct/range {v0 .. v8}, Lorg/codehaus/groovy/transform/stc/ExtensionMethodNode;-><init>(Lorg/codehaus/groovy/ast/MethodNode;Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;Z)V

    return-void
.end method

.method public constructor <init>(Lorg/codehaus/groovy/ast/MethodNode;Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;Z)V
    .locals 7

    move-object v0, p0

    move-object v1, p2

    move v2, p3

    move-object v3, p4

    move-object v4, p5

    move-object v5, p6

    move-object v6, p7

    .line 37
    invoke-direct/range {v0 .. v6}, Lorg/codehaus/groovy/ast/MethodNode;-><init>(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 38
    iput-object p1, p0, Lorg/codehaus/groovy/transform/stc/ExtensionMethodNode;->extensionMethodNode:Lorg/codehaus/groovy/ast/MethodNode;

    .line 39
    iput-boolean p8, p0, Lorg/codehaus/groovy/transform/stc/ExtensionMethodNode;->isStaticExtension:Z

    return-void
.end method


# virtual methods
.method public getExtensionMethodNode()Lorg/codehaus/groovy/ast/MethodNode;
    .locals 1

    .line 49
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/ExtensionMethodNode;->extensionMethodNode:Lorg/codehaus/groovy/ast/MethodNode;

    return-object v0
.end method

.method public isStaticExtension()Z
    .locals 1

    .line 53
    iget-boolean v0, p0, Lorg/codehaus/groovy/transform/stc/ExtensionMethodNode;->isStaticExtension:Z

    return v0
.end method
