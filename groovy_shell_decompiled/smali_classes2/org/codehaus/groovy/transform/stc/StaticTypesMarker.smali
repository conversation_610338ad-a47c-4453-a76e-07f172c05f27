.class public final enum Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;
.super Ljava/lang/Enum;
.source "StaticTypesMarker.java"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;",
        ">;"
    }
.end annotation


# static fields
.field private static final synthetic $VALUES:[Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

.field public static final enum CLOSURE_ARGUMENTS:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

.field public static final enum CONSTRUCTED_LAMBDA_EXPRESSION:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

.field public static final enum DECLARATION_INFERRED_TYPE:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

.field public static final enum DELEGATION_METADATA:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

.field public static final enum DIRECT_METHOD_CALL_TARGET:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

.field public static final enum DYNAMIC_RESOLUTION:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

.field public static final enum IMPLICIT_RECEIVER:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

.field public static final enum INFERRED_FUNCTIONAL_INTERFACE_TYPE:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

.field public static final enum INFERRED_RETURN_TYPE:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

.field public static final enum INFERRED_TYPE:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

.field public static final enum INITIAL_EXPRESSION:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

.field public static final enum PARAMETER_TYPE:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

.field public static final enum PV_FIELDS_ACCESS:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

.field public static final enum PV_FIELDS_MUTATION:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

.field public static final enum PV_METHODS_ACCESS:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

.field public static final enum READONLY_PROPERTY:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

.field public static final enum SUPER_MOP_METHOD_REQUIRED:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

.field public static final enum SWITCH_CONDITION_EXPRESSION_TYPE:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

.field public static final enum TYPE:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;


# direct methods
.method private static synthetic $values()[Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;
    .locals 3

    const/16 v0, 0x13

    new-array v0, v0, [Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    .line 26
    sget-object v1, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->INFERRED_TYPE:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    const/4 v2, 0x0

    aput-object v1, v0, v2

    sget-object v1, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->DECLARATION_INFERRED_TYPE:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    const/4 v2, 0x1

    aput-object v1, v0, v2

    sget-object v1, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->INFERRED_RETURN_TYPE:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    const/4 v2, 0x2

    aput-object v1, v0, v2

    sget-object v1, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->CLOSURE_ARGUMENTS:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    const/4 v2, 0x3

    aput-object v1, v0, v2

    sget-object v1, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->READONLY_PROPERTY:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    const/4 v2, 0x4

    aput-object v1, v0, v2

    sget-object v1, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->INITIAL_EXPRESSION:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    const/4 v2, 0x5

    aput-object v1, v0, v2

    sget-object v1, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->DIRECT_METHOD_CALL_TARGET:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    const/4 v2, 0x6

    aput-object v1, v0, v2

    sget-object v1, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->DELEGATION_METADATA:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    const/4 v2, 0x7

    aput-object v1, v0, v2

    sget-object v1, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->IMPLICIT_RECEIVER:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    const/16 v2, 0x8

    aput-object v1, v0, v2

    sget-object v1, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->PV_FIELDS_ACCESS:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    const/16 v2, 0x9

    aput-object v1, v0, v2

    sget-object v1, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->PV_FIELDS_MUTATION:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    const/16 v2, 0xa

    aput-object v1, v0, v2

    sget-object v1, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->PV_METHODS_ACCESS:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    const/16 v2, 0xb

    aput-object v1, v0, v2

    sget-object v1, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->DYNAMIC_RESOLUTION:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    const/16 v2, 0xc

    aput-object v1, v0, v2

    sget-object v1, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->SUPER_MOP_METHOD_REQUIRED:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    const/16 v2, 0xd

    aput-object v1, v0, v2

    sget-object v1, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->PARAMETER_TYPE:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    const/16 v2, 0xe

    aput-object v1, v0, v2

    sget-object v1, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->INFERRED_FUNCTIONAL_INTERFACE_TYPE:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    const/16 v2, 0xf

    aput-object v1, v0, v2

    sget-object v1, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->CONSTRUCTED_LAMBDA_EXPRESSION:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    const/16 v2, 0x10

    aput-object v1, v0, v2

    sget-object v1, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->SWITCH_CONDITION_EXPRESSION_TYPE:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    const/16 v2, 0x11

    aput-object v1, v0, v2

    sget-object v1, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->TYPE:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    const/16 v2, 0x12

    aput-object v1, v0, v2

    return-object v0
.end method

.method static constructor <clinit>()V
    .locals 3

    .line 27
    new-instance v0, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    const-string v1, "INFERRED_TYPE"

    const/4 v2, 0x0

    invoke-direct {v0, v1, v2}, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->INFERRED_TYPE:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    .line 28
    new-instance v0, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    const-string v1, "DECLARATION_INFERRED_TYPE"

    const/4 v2, 0x1

    invoke-direct {v0, v1, v2}, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->DECLARATION_INFERRED_TYPE:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    .line 29
    new-instance v0, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    const-string v1, "INFERRED_RETURN_TYPE"

    const/4 v2, 0x2

    invoke-direct {v0, v1, v2}, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->INFERRED_RETURN_TYPE:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    .line 30
    new-instance v0, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    const-string v1, "CLOSURE_ARGUMENTS"

    const/4 v2, 0x3

    invoke-direct {v0, v1, v2}, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->CLOSURE_ARGUMENTS:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    .line 31
    new-instance v0, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    const-string v1, "READONLY_PROPERTY"

    const/4 v2, 0x4

    invoke-direct {v0, v1, v2}, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->READONLY_PROPERTY:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    .line 32
    new-instance v0, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    const-string v1, "INITIAL_EXPRESSION"

    const/4 v2, 0x5

    invoke-direct {v0, v1, v2}, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->INITIAL_EXPRESSION:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    .line 33
    new-instance v0, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    const-string v1, "DIRECT_METHOD_CALL_TARGET"

    const/4 v2, 0x6

    invoke-direct {v0, v1, v2}, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->DIRECT_METHOD_CALL_TARGET:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    .line 34
    new-instance v0, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    const-string v1, "DELEGATION_METADATA"

    const/4 v2, 0x7

    invoke-direct {v0, v1, v2}, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->DELEGATION_METADATA:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    .line 35
    new-instance v0, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    const-string v1, "IMPLICIT_RECEIVER"

    const/16 v2, 0x8

    invoke-direct {v0, v1, v2}, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->IMPLICIT_RECEIVER:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    .line 36
    new-instance v0, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    const-string v1, "PV_FIELDS_ACCESS"

    const/16 v2, 0x9

    invoke-direct {v0, v1, v2}, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->PV_FIELDS_ACCESS:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    .line 37
    new-instance v0, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    const-string v1, "PV_FIELDS_MUTATION"

    const/16 v2, 0xa

    invoke-direct {v0, v1, v2}, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->PV_FIELDS_MUTATION:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    .line 38
    new-instance v0, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    const-string v1, "PV_METHODS_ACCESS"

    const/16 v2, 0xb

    invoke-direct {v0, v1, v2}, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->PV_METHODS_ACCESS:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    .line 39
    new-instance v0, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    const-string v1, "DYNAMIC_RESOLUTION"

    const/16 v2, 0xc

    invoke-direct {v0, v1, v2}, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->DYNAMIC_RESOLUTION:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    .line 40
    new-instance v0, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    const-string v1, "SUPER_MOP_METHOD_REQUIRED"

    const/16 v2, 0xd

    invoke-direct {v0, v1, v2}, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->SUPER_MOP_METHOD_REQUIRED:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    .line 41
    new-instance v0, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    const-string v1, "PARAMETER_TYPE"

    const/16 v2, 0xe

    invoke-direct {v0, v1, v2}, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->PARAMETER_TYPE:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    .line 42
    new-instance v0, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    const-string v1, "INFERRED_FUNCTIONAL_INTERFACE_TYPE"

    const/16 v2, 0xf

    invoke-direct {v0, v1, v2}, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->INFERRED_FUNCTIONAL_INTERFACE_TYPE:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    .line 43
    new-instance v0, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    const-string v1, "CONSTRUCTED_LAMBDA_EXPRESSION"

    const/16 v2, 0x10

    invoke-direct {v0, v1, v2}, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->CONSTRUCTED_LAMBDA_EXPRESSION:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    .line 44
    new-instance v0, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    const-string v1, "SWITCH_CONDITION_EXPRESSION_TYPE"

    const/16 v2, 0x11

    invoke-direct {v0, v1, v2}, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->SWITCH_CONDITION_EXPRESSION_TYPE:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    .line 45
    new-instance v0, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    const-string v1, "TYPE"

    const/16 v2, 0x12

    invoke-direct {v0, v1, v2}, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->TYPE:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    .line 26
    invoke-static {}, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->$values()[Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->$VALUES:[Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .line 26
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    return-void
.end method

.method public static valueOf(Ljava/lang/String;)Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;
    .locals 1

    .line 26
    const-class v0, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object p0

    check-cast p0, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    return-object p0
.end method

.method public static values()[Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;
    .locals 1

    .line 26
    sget-object v0, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->$VALUES:[Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    invoke-virtual {v0}, [Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    return-object v0
.end method
