.class public Lorg/codehaus/groovy/transform/stc/TraitTypeCheckingExtension;
.super Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;
.source "TraitTypeCheckingExtension.java"


# static fields
.field private static final NOTFOUND:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/MethodNode;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 47
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/transform/stc/TraitTypeCheckingExtension;->NOTFOUND:Ljava/util/List;

    return-void
.end method

.method public constructor <init>(Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;)V
    .locals 0

    .line 55
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;-><init>(Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;)V

    return-void
.end method

.method private convertToDynamicCall(Lorg/codehaus/groovy/ast/expr/MethodCall;Lorg/codehaus/groovy/ast/ClassNode;[Ljava/lang/String;[Lorg/codehaus/groovy/ast/ClassNode;)Ljava/util/List;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/expr/MethodCall;",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            "[",
            "Ljava/lang/String;",
            "[",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            ")",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/MethodNode;",
            ">;"
        }
    .end annotation

    const/4 v0, 0x0

    .line 120
    aget-object v1, p3, v0

    const/4 v2, 0x1

    .line 121
    aget-object p3, p3, v2

    .line 122
    new-instance v3, Ljava/util/LinkedHashSet;

    invoke-direct {v3}, Ljava/util/LinkedHashSet;-><init>()V

    invoke-static {p2, v3}, Lorg/codehaus/groovy/transform/trait/Traits;->collectAllInterfacesReverseOrder(Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/LinkedHashSet;)Ljava/util/LinkedHashSet;

    move-result-object p2

    .line 123
    sget-object v3, Lorg/codehaus/groovy/ast/ClassNode;->EMPTY_ARRAY:[Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {p2, v3}, Ljava/util/LinkedHashSet;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object p2

    check-cast p2, [Lorg/codehaus/groovy/ast/ClassNode;

    const/4 v3, 0x0

    move v4, v0

    .line 125
    :goto_0
    array-length v5, p2

    sub-int/2addr v5, v2

    if-ge v4, v5, :cond_1

    .line 126
    aget-object v5, p2, v4

    .line 127
    invoke-virtual {v5}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v5, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v5

    if-eqz v5, :cond_0

    add-int/lit8 v3, v4, 0x1

    .line 128
    aget-object v3, p2, v3

    :cond_0
    add-int/lit8 v4, v4, 0x1

    goto :goto_0

    .line 131
    :cond_1
    array-length p2, p4

    new-array v1, p2, [Lorg/codehaus/groovy/ast/ClassNode;

    .line 132
    invoke-static {p4, v0, v1, v0, p2}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    .line 133
    invoke-direct {p0, v3, p3, v1}, Lorg/codehaus/groovy/transform/stc/TraitTypeCheckingExtension;->inferTraitMethodReturnType(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;[Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p2

    .line 135
    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/transform/stc/TraitTypeCheckingExtension;->makeDynamic(Lorg/codehaus/groovy/ast/expr/MethodCall;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/MethodNode;

    move-result-object p1

    invoke-static {p1}, Ljava/util/Collections;->singletonList(Ljava/lang/Object;)Ljava/util/List;

    move-result-object p1

    return-object p1
.end method

.method private inferTraitMethodReturnType(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;[Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/ClassNode;
    .locals 2

    .line 139
    sget-object v0, Lorg/codehaus/groovy/ast/ClassHelper;->OBJECT_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    if-eqz p1, :cond_0

    .line 141
    iget-object v1, p0, Lorg/codehaus/groovy/transform/stc/TraitTypeCheckingExtension;->typeCheckingVisitor:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;

    invoke-virtual {v1, p1, p2, p3}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->findMethod(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;[Lorg/codehaus/groovy/ast/ClassNode;)Ljava/util/List;

    move-result-object p1

    .line 142
    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result p2

    const/4 p3, 0x1

    if-ne p2, p3, :cond_0

    const/4 p2, 0x0

    .line 143
    invoke-interface {p1, p2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lorg/codehaus/groovy/ast/MethodNode;

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getReturnType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    :cond_0
    return-object v0
.end method

.method private static isStaticTraitReceiver(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/VariableExpression;)Z
    .locals 1

    .line 112
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->getName()Ljava/lang/String;

    move-result-object p1

    const-string v0, "$static$self"

    invoke-virtual {v0, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_0

    invoke-static {p0}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingSupport;->isClassClassNodeWrappingConcreteType(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result p0

    if-eqz p0, :cond_0

    const/4 p0, 0x1

    goto :goto_0

    :cond_0
    const/4 p0, 0x0

    :goto_0
    return p0
.end method

.method private static isThisTraitReceiver(Lorg/codehaus/groovy/ast/expr/VariableExpression;)Z
    .locals 1

    .line 116
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->getName()Ljava/lang/String;

    move-result-object p0

    const-string v0, "$self"

    invoke-virtual {v0, p0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p0

    return p0
.end method


# virtual methods
.method public handleMissingMethod(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/MethodCall;)Ljava/util/List;
    .locals 10
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            "Ljava/lang/String;",
            "Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;",
            "[",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            "Lorg/codehaus/groovy/ast/expr/MethodCall;",
            ")",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/MethodNode;",
            ">;"
        }
    .end annotation

    .line 64
    invoke-static {p2}, Lorg/codehaus/groovy/transform/trait/Traits;->decomposeSuperCallName(Ljava/lang/String;)[Ljava/lang/String;

    move-result-object p3

    if-eqz p3, :cond_0

    .line 66
    invoke-direct {p0, p5, p1, p3, p4}, Lorg/codehaus/groovy/transform/stc/TraitTypeCheckingExtension;->convertToDynamicCall(Lorg/codehaus/groovy/ast/expr/MethodCall;Lorg/codehaus/groovy/ast/ClassNode;[Ljava/lang/String;[Lorg/codehaus/groovy/ast/ClassNode;)Ljava/util/List;

    move-result-object p1

    return-object p1

    .line 68
    :cond_0
    instance-of p3, p5, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    if-eqz p3, :cond_7

    .line 69
    move-object p3, p5

    check-cast p3, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    .line 70
    invoke-virtual {p3}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->getReceiver()Lorg/codehaus/groovy/ast/ASTNode;

    move-result-object v0

    instance-of v0, v0, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    if-eqz v0, :cond_6

    .line 71
    invoke-virtual {p3}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->getReceiver()Lorg/codehaus/groovy/ast/ASTNode;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    const/4 v1, 0x0

    .line 76
    invoke-static {p1, v0}, Lorg/codehaus/groovy/transform/stc/TraitTypeCheckingExtension;->isStaticTraitReceiver(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/VariableExpression;)Z

    move-result v2

    const/4 v3, 0x0

    if-eqz v2, :cond_1

    .line 77
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getGenericsTypes()[Lorg/codehaus/groovy/ast/GenericsType;

    move-result-object p1

    aget-object p1, p1, v3

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/GenericsType;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    goto :goto_0

    .line 78
    :cond_1
    invoke-static {v0}, Lorg/codehaus/groovy/transform/stc/TraitTypeCheckingExtension;->isThisTraitReceiver(Lorg/codehaus/groovy/ast/expr/VariableExpression;)Z

    move-result v0

    if-eqz v0, :cond_2

    goto :goto_0

    :cond_2
    move-object p1, v1

    .line 81
    :goto_0
    invoke-static {p1}, Lorg/codehaus/groovy/transform/trait/Traits;->isTrait(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v0

    if-eqz v0, :cond_6

    instance-of v0, p1, Lorg/codehaus/groovy/transform/stc/UnionTypeClassNode;

    if-nez v0, :cond_6

    .line 82
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 83
    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 84
    :goto_1
    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result p1

    if-nez p1, :cond_6

    .line 85
    invoke-interface {v0, v3}, Ljava/util/List;->remove(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lorg/codehaus/groovy/ast/ClassNode;

    .line 86
    invoke-static {p1}, Lorg/codehaus/groovy/transform/trait/Traits;->isTrait(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v1

    if-nez v1, :cond_3

    goto :goto_1

    .line 87
    :cond_3
    invoke-static {p1}, Lorg/codehaus/groovy/transform/trait/Traits;->findHelper(Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    .line 88
    array-length v2, p4

    const/4 v4, 0x1

    add-int/2addr v2, v4

    new-array v5, v2, [Lorg/codehaus/groovy/ast/Parameter;

    .line 89
    new-instance v6, Lorg/codehaus/groovy/ast/Parameter;

    sget-object v7, Lorg/codehaus/groovy/ast/ClassHelper;->CLASS_Type:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v7}, Lorg/codehaus/groovy/ast/ClassNode;->getPlainNodeReference()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v7

    const-string v8, "staticSelf"

    invoke-direct {v6, v7, v8}, Lorg/codehaus/groovy/ast/Parameter;-><init>(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)V

    aput-object v6, v5, v3

    :goto_2
    if-ge v4, v2, :cond_4

    .line 91
    new-instance v6, Lorg/codehaus/groovy/ast/Parameter;

    add-int/lit8 v7, v4, -0x1

    aget-object v7, p4, v7

    new-instance v8, Ljava/lang/StringBuilder;

    invoke-direct {v8}, Ljava/lang/StringBuilder;-><init>()V

    const-string v9, "p"

    invoke-virtual {v8, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v8

    invoke-virtual {v8, v4}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v8

    invoke-virtual {v8}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v8

    invoke-direct {v6, v7, v8}, Lorg/codehaus/groovy/ast/Parameter;-><init>(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)V

    aput-object v6, v5, v4

    add-int/lit8 v4, v4, 0x1

    goto :goto_2

    .line 93
    :cond_4
    invoke-virtual {v1, p2, v5}, Lorg/codehaus/groovy/ast/ClassNode;->getDeclaredMethod(Ljava/lang/String;[Lorg/codehaus/groovy/ast/Parameter;)Lorg/codehaus/groovy/ast/MethodNode;

    move-result-object v1

    if-eqz v1, :cond_5

    .line 95
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/MethodNode;->getReturnType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    invoke-virtual {p0, p5, p1}, Lorg/codehaus/groovy/transform/stc/TraitTypeCheckingExtension;->makeDynamic(Lorg/codehaus/groovy/ast/expr/MethodCall;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/MethodNode;

    move-result-object p1

    invoke-static {p1}, Ljava/util/Collections;->singletonList(Ljava/lang/Object;)Ljava/util/List;

    move-result-object p1

    return-object p1

    .line 98
    :cond_5
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getInterfaces()[Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    invoke-static {p1}, Ljava/util/Arrays;->asList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object p1

    invoke-interface {v0, p1}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    goto :goto_1

    .line 103
    :cond_6
    sget-object p1, Lorg/codehaus/groovy/transform/trait/TraitASTTransformation;->DO_DYNAMIC:Ljava/lang/String;

    invoke-virtual {p3, p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->getNodeMetaData(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lorg/codehaus/groovy/ast/ClassNode;

    if-eqz p1, :cond_7

    .line 105
    invoke-virtual {p0, p5, p1}, Lorg/codehaus/groovy/transform/stc/TraitTypeCheckingExtension;->makeDynamic(Lorg/codehaus/groovy/ast/expr/MethodCall;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/MethodNode;

    move-result-object p1

    invoke-static {p1}, Ljava/util/Collections;->singletonList(Ljava/lang/Object;)Ljava/util/List;

    move-result-object p1

    return-object p1

    .line 108
    :cond_7
    sget-object p1, Lorg/codehaus/groovy/transform/stc/TraitTypeCheckingExtension;->NOTFOUND:Ljava/util/List;

    return-object p1
.end method

.method public setup()V
    .locals 0

    return-void
.end method
