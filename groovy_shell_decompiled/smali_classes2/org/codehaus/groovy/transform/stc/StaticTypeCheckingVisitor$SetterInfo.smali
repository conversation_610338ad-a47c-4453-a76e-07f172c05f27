.class Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$SetterInfo;
.super Ljava/lang/Object;
.source "StaticTypeCheckingVisitor.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0xa
    name = "SetterInfo"
.end annotation


# instance fields
.field final name:Ljava/lang/String;

.field final receiverType:Lorg/codehaus/groovy/ast/ClassNode;

.field final setters:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/MethodNode;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method private constructor <init>(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Ljava/util/List;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/MethodNode;",
            ">;)V"
        }
    .end annotation

    .line 5972
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 5973
    iput-object p1, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$SetterInfo;->receiverType:Lorg/codehaus/groovy/ast/ClassNode;

    .line 5974
    iput-object p3, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$SetterInfo;->setters:Ljava/util/List;

    .line 5975
    iput-object p2, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$SetterInfo;->name:Ljava/lang/String;

    return-void
.end method

.method synthetic constructor <init>(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Ljava/util/List;Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$1;)V
    .locals 0

    .line 5967
    invoke-direct {p0, p1, p2, p3}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$SetterInfo;-><init>(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Ljava/util/List;)V

    return-void
.end method
