.class public Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingSupport$LongArrayStaticTypesHelper;
.super Ljava/lang/Object;
.source "StaticTypeCheckingSupport.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingSupport;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "LongArrayStaticTypesHelper"
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 2360
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static getAt([JI)Ljava/lang/Long;
    .locals 2

    if-eqz p0, :cond_0

    .line 2362
    aget-wide v0, p0, p1

    invoke-static {v0, v1}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object p0

    goto :goto_0

    :cond_0
    const/4 p0, 0x0

    :goto_0
    return-object p0
.end method

.method public static putAt([JIJ)V
    .locals 0

    if-eqz p0, :cond_0

    .line 2366
    aput-wide p2, p0, p1

    :cond_0
    return-void
.end method
