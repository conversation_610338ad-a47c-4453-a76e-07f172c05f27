.class public Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingSupport$ObjectArrayStaticTypesHelper;
.super Ljava/lang/Object;
.source "StaticTypeCheckingSupport.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingSupport;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "ObjectArrayStaticTypesHelper"
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 2294
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static getAt([Ljava/lang/Object;I)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">([TT;I)TT;"
        }
    .end annotation

    if-eqz p0, :cond_0

    .line 2296
    aget-object p0, p0, p1

    goto :goto_0

    :cond_0
    const/4 p0, 0x0

    :goto_0
    return-object p0
.end method

.method public static putAt([Ljava/lang/Object;ILjava/lang/Object;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            "U:TT;>([TT;ITU;)V"
        }
    .end annotation

    if-eqz p0, :cond_0

    .line 2300
    aput-object p2, p0, p1

    :cond_0
    return-void
.end method
