.class Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$PropertyLookup;
.super Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;
.source "StaticTypeCheckingVisitor.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x2
    name = "PropertyLookup"
.end annotation


# instance fields
.field propertyType:Lorg/codehaus/groovy/ast/ClassNode;

.field receiverType:Lorg/codehaus/groovy/ast/ClassNode;

.field final synthetic this$0:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;


# direct methods
.method constructor <init>(Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 0

    .line 5982
    iput-object p1, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$PropertyLookup;->this$0:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;

    invoke-direct {p0}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;-><init>()V

    .line 5983
    iput-object p2, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$PropertyLookup;->receiverType:Lorg/codehaus/groovy/ast/ClassNode;

    return-void
.end method

.method private storePropertyType(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 1

    if-eqz p2, :cond_0

    .line 6007
    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GenericsUtils;->hasUnresolvedGenerics(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 6008
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$PropertyLookup;->receiverType:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {v0, p2}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->access$300(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;)Ljava/util/Map;

    move-result-object p2

    .line 6009
    invoke-static {p2, p1}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingSupport;->applyGenericsContext(Ljava/util/Map;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    .line 6012
    :cond_0
    iput-object p1, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$PropertyLookup;->propertyType:Lorg/codehaus/groovy/ast/ClassNode;

    return-void
.end method


# virtual methods
.method protected getSourceUnit()Lorg/codehaus/groovy/control/SourceUnit;
    .locals 1

    .line 5988
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$PropertyLookup;->this$0:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;

    invoke-virtual {v0}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->getSourceUnit()Lorg/codehaus/groovy/control/SourceUnit;

    move-result-object v0

    return-object v0
.end method

.method public visitField(Lorg/codehaus/groovy/ast/FieldNode;)V
    .locals 2

    .line 5993
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/FieldNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/FieldNode;->isStatic()Z

    move-result v1

    if-eqz v1, :cond_0

    const/4 p1, 0x0

    goto :goto_0

    :cond_0
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/FieldNode;->getDeclaringClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    :goto_0
    invoke-direct {p0, v0, p1}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$PropertyLookup;->storePropertyType(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;)V

    return-void
.end method

.method public visitMethod(Lorg/codehaus/groovy/ast/MethodNode;)V
    .locals 2

    .line 5998
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getReturnType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->isStatic()Z

    move-result v1

    if-eqz v1, :cond_0

    const/4 p1, 0x0

    goto :goto_0

    :cond_0
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getDeclaringClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    :goto_0
    invoke-direct {p0, v0, p1}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$PropertyLookup;->storePropertyType(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;)V

    return-void
.end method

.method public visitProperty(Lorg/codehaus/groovy/ast/PropertyNode;)V
    .locals 2

    .line 6003
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/PropertyNode;->getOriginType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/PropertyNode;->isStatic()Z

    move-result v1

    if-eqz v1, :cond_0

    const/4 p1, 0x0

    goto :goto_0

    :cond_0
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/PropertyNode;->getDeclaringClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    :goto_0
    invoke-direct {p0, v0, p1}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$PropertyLookup;->storePropertyType(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;)V

    return-void
.end method
