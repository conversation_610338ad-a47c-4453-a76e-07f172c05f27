.class Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$1;
.super Ljava/lang/Object;
.source "StaticTypeCheckingVisitor.java"

# interfaces
.implements Lorg/codehaus/groovy/classgen/ReturnAdder$ReturnStatementListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;


# direct methods
.method constructor <init>(Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;)V
    .locals 0

    .line 345
    iput-object p1, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$1;->this$0:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public returnStatementAdded(Lorg/codehaus/groovy/ast/stmt/ReturnStatement;)V
    .locals 3

    .line 348
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/stmt/ReturnStatement;->getExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->isNullConstant(Lorg/codehaus/groovy/ast/expr/Expression;)Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    .line 349
    :cond_0
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$1;->this$0:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;

    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->checkReturnType(Lorg/codehaus/groovy/ast/stmt/ReturnStatement;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    .line 350
    iget-object v1, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$1;->this$0:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;

    iget-object v1, v1, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->typeCheckingContext:Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;

    invoke-virtual {v1}, Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;->getEnclosingClosure()Lorg/codehaus/groovy/transform/stc/TypeCheckingContext$EnclosingClosure;

    move-result-object v1

    if-eqz v1, :cond_1

    .line 351
    iget-object p1, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$1;->this$0:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;

    invoke-virtual {p1, v0}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->addClosureReturnType(Lorg/codehaus/groovy/ast/ClassNode;)V

    goto :goto_0

    .line 352
    :cond_1
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$1;->this$0:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;

    iget-object v0, v0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->typeCheckingContext:Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;

    invoke-virtual {v0}, Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;->getEnclosingMethod()Lorg/codehaus/groovy/ast/MethodNode;

    move-result-object v0

    if-eqz v0, :cond_2

    :goto_0
    return-void

    .line 353
    :cond_2
    new-instance v0, Lorg/codehaus/groovy/GroovyBugError;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "Unexpected return statement at "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/stmt/ReturnStatement;->getLineNumber()I

    move-result v2

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v2, ":"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/stmt/ReturnStatement;->getColumnNumber()I

    move-result v2

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v2, " "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/stmt/ReturnStatement;->getText()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {v0, p1}, Lorg/codehaus/groovy/GroovyBugError;-><init>(Ljava/lang/String;)V

    throw v0
.end method
