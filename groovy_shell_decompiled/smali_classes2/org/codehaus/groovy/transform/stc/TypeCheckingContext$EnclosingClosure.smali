.class public Lorg/codehaus/groovy/transform/stc/TypeCheckingContext$EnclosingClosure;
.super Ljava/lang/Object;
.source "TypeCheckingContext.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "EnclosingClosure"
.end annotation


# instance fields
.field private final closureExpression:Lorg/codehaus/groovy/ast/expr/ClosureExpression;

.field private final returnTypes:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lorg/codehaus/groovy/ast/expr/ClosureExpression;)V
    .locals 1

    .line 403
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 401
    new-instance v0, Ljava/util/LinkedList;

    invoke-direct {v0}, Ljava/util/LinkedList;-><init>()V

    iput-object v0, p0, Lorg/codehaus/groovy/transform/stc/TypeCheckingContext$EnclosingClosure;->returnTypes:Ljava/util/List;

    .line 404
    iput-object p1, p0, Lorg/codehaus/groovy/transform/stc/TypeCheckingContext$EnclosingClosure;->closureExpression:Lorg/codehaus/groovy/ast/expr/ClosureExpression;

    return-void
.end method


# virtual methods
.method public addReturnType(Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 1

    .line 416
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/TypeCheckingContext$EnclosingClosure;->returnTypes:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public getClosureExpression()Lorg/codehaus/groovy/ast/expr/ClosureExpression;
    .locals 1

    .line 408
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/TypeCheckingContext$EnclosingClosure;->closureExpression:Lorg/codehaus/groovy/ast/expr/ClosureExpression;

    return-object v0
.end method

.method public getReturnTypes()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            ">;"
        }
    .end annotation

    .line 412
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/TypeCheckingContext$EnclosingClosure;->returnTypes:Ljava/util/List;

    return-object v0
.end method

.method public toString()Ljava/lang/String;
    .locals 2

    .line 421
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "EnclosingClosure{closureExpression="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v1, p0, Lorg/codehaus/groovy/transform/stc/TypeCheckingContext$EnclosingClosure;->closureExpression:Lorg/codehaus/groovy/ast/expr/ClosureExpression;

    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/expr/ClosureExpression;->getText()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ", returnTypes="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v1, p0, Lorg/codehaus/groovy/transform/stc/TypeCheckingContext$EnclosingClosure;->returnTypes:Ljava/util/List;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "}"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
