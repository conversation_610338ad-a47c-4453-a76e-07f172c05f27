.class Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$ParameterVariableExpression;
.super Lorg/codehaus/groovy/ast/expr/VariableExpression;
.source "StaticTypeCheckingVisitor.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0xa
    name = "ParameterVariableExpression"
.end annotation


# instance fields
.field private final parameter:Lorg/codehaus/groovy/ast/Parameter;


# direct methods
.method constructor <init>(Lorg/codehaus/groovy/ast/Parameter;)V
    .locals 2

    .line 6028
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/ast/expr/VariableExpression;-><init>(Lorg/codehaus/groovy/ast/Variable;)V

    .line 6029
    iput-object p1, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$ParameterVariableExpression;->parameter:Lorg/codehaus/groovy/ast/Parameter;

    .line 6030
    sget-object v0, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->INFERRED_TYPE:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    new-instance v1, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$ParameterVariableExpression$$ExternalSyntheticLambda0;

    invoke-direct {v1, p1}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$ParameterVariableExpression$$ExternalSyntheticLambda0;-><init>(Lorg/codehaus/groovy/ast/Parameter;)V

    invoke-virtual {p1, v0, v1}, Lorg/codehaus/groovy/ast/Parameter;->getNodeMetaData(Ljava/lang/Object;Ljava/util/function/Function;)Ljava/lang/Object;

    return-void
.end method

.method static synthetic access$100(Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$ParameterVariableExpression;)Lorg/codehaus/groovy/ast/Parameter;
    .locals 0

    .line 6024
    iget-object p0, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$ParameterVariableExpression;->parameter:Lorg/codehaus/groovy/ast/Parameter;

    return-object p0
.end method

.method static synthetic lambda$new$0(Lorg/codehaus/groovy/ast/Parameter;Ljava/lang/Object;)Lorg/codehaus/groovy/ast/ClassNode;
    .locals 0

    .line 6030
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/Parameter;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public getMetaDataMap()Ljava/util/Map;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "**>;"
        }
    .end annotation

    .line 6035
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$ParameterVariableExpression;->parameter:Lorg/codehaus/groovy/ast/Parameter;

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/Parameter;->getMetaDataMap()Ljava/util/Map;

    move-result-object v0

    return-object v0
.end method

.method public setMetaDataMap(Ljava/util/Map;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "**>;)V"
        }
    .end annotation

    .line 6040
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$ParameterVariableExpression;->parameter:Lorg/codehaus/groovy/ast/Parameter;

    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/ast/Parameter;->setMetaDataMap(Ljava/util/Map;)V

    return-void
.end method
