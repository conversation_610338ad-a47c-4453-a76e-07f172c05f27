.class public final synthetic Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingSupport$$ExternalSyntheticLambda2;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Function;


# instance fields
.field public final synthetic f$0:Lorg/codehaus/groovy/control/CompilerConfiguration;

.field public final synthetic f$1:Lgroovy/lang/GroovyClassLoader;


# direct methods
.method public synthetic constructor <init>(Lorg/codehaus/groovy/control/CompilerConfiguration;Lgroovy/lang/GroovyClassLoader;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingSupport$$ExternalSyntheticLambda2;->f$0:Lorg/codehaus/groovy/control/CompilerConfiguration;

    iput-object p2, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingSupport$$ExternalSyntheticLambda2;->f$1:Lgroovy/lang/GroovyClassLoader;

    return-void
.end method


# virtual methods
.method public final apply(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingSupport$$ExternalSyntheticLambda2;->f$0:Lorg/codehaus/groovy/control/CompilerConfiguration;

    iget-object v1, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingSupport$$ExternalSyntheticLambda2;->f$1:Lgroovy/lang/GroovyClassLoader;

    check-cast p1, Lorg/codehaus/groovy/ast/expr/Expression;

    invoke-static {v0, v1, p1}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingSupport;->lambda$evaluateExpression$6(Lorg/codehaus/groovy/control/CompilerConfiguration;Lgroovy/lang/GroovyClassLoader;Lorg/codehaus/groovy/ast/expr/Expression;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method
