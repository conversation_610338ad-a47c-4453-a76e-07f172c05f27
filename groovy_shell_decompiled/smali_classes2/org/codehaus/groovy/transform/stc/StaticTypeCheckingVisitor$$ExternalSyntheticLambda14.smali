.class public final synthetic Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda14;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Function;


# instance fields
.field public final synthetic f$0:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;


# direct methods
.method public synthetic constructor <init>(Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda14;->f$0:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;

    return-void
.end method


# virtual methods
.method public final apply(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda14;->f$0:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;

    check-cast p1, Lorg/codehaus/groovy/ast/MethodNode;

    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->lambda$allowStaticAccessToMember$9$org-codehaus-groovy-transform-stc-StaticTypeCheckingVisitor(Lorg/codehaus/groovy/ast/MethodNode;)Lorg/codehaus/groovy/ast/MethodNode;

    move-result-object p1

    return-object p1
.end method
