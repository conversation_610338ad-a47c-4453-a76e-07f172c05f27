.class public abstract Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport$TypeCheckingDSL;
.super Lgroovy/lang/Script;
.source "GroovyTypeCheckingExtensionSupport.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x409
    name = "TypeCheckingDSL"
.end annotation


# instance fields
.field private extension:Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 468
    invoke-direct {p0}, Lgroovy/lang/Script;-><init>()V

    return-void
.end method

.method static synthetic access$002(Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport$TypeCheckingDSL;Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;)Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;
    .locals 0

    .line 468
    iput-object p1, p0, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport$TypeCheckingDSL;->extension:Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;

    return-object p1
.end method

.method static synthetic lambda$invokeMethod$0(Ljava/lang/String;)Ljava/util/List;
    .locals 0

    .line 508
    new-instance p0, Ljava/util/LinkedList;

    invoke-direct {p0}, Ljava/util/LinkedList;-><init>()V

    return-object p0
.end method


# virtual methods
.method public getProperty(Ljava/lang/String;)Ljava/lang/Object;
    .locals 1

    .line 474
    :try_start_0
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport$TypeCheckingDSL;->extension:Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;

    invoke-static {v0, p1}, Lorg/codehaus/groovy/runtime/InvokerHelper;->getProperty(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return-object p1

    .line 476
    :catch_0
    invoke-super {p0, p1}, Lgroovy/lang/Script;->getProperty(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public invokeMethod(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 5

    const-string v0, "is"

    .line 491
    invoke-virtual {p1, v0}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v0

    const/4 v1, 0x1

    const/4 v2, 0x0

    .line 494
    invoke-static {v2}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v3

    if-eqz v0, :cond_1

    const-string v0, "Expression"

    .line 491
    invoke-virtual {p1, v0}, Ljava/lang/String;->endsWith(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_1

    instance-of v0, p2, [Ljava/lang/Object;

    if-eqz v0, :cond_1

    move-object v0, p2

    check-cast v0, [Ljava/lang/Object;

    array-length v4, v0

    if-ne v4, v1, :cond_1

    const/4 p2, 0x2

    .line 492
    invoke-virtual {p1, p2}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object p1

    .line 493
    aget-object p2, v0, v2

    if-nez p2, :cond_0

    return-object v3

    .line 496
    :cond_0
    :try_start_0
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "org.codehaus.groovy.ast.expr."

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {p1}, Ljava/lang/Class;->forName(Ljava/lang/String;)Ljava/lang/Class;

    move-result-object p1

    .line 497
    invoke-virtual {p2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object p2

    invoke-virtual {p1, p2}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    move-result p1

    invoke-static {p1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object p1
    :try_end_0
    .catch Ljava/lang/ClassNotFoundException; {:try_start_0 .. :try_end_0} :catch_0

    return-object p1

    :catch_0
    return-object v3

    .line 502
    :cond_1
    instance-of v0, p2, [Ljava/lang/Object;

    if-eqz v0, :cond_3

    move-object v0, p2

    check-cast v0, [Ljava/lang/Object;

    array-length v3, v0

    if-ne v3, v1, :cond_3

    aget-object v1, v0, v2

    instance-of v1, v1, Lgroovy/lang/Closure;

    if-eqz v1, :cond_3

    .line 504
    invoke-static {}, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->access$100()Ljava/util/Map;

    move-result-object v1

    invoke-interface {v1, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/String;

    if-nez v1, :cond_2

    .line 506
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport$TypeCheckingDSL;->extension:Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;

    invoke-static {v0, p1, p2}, Lorg/codehaus/groovy/runtime/InvokerHelper;->invokeMethod(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1

    .line 508
    :cond_2
    iget-object p1, p0, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport$TypeCheckingDSL;->extension:Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;

    invoke-static {p1}, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->access$200(Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;)Ljava/util/Map;

    move-result-object p1

    sget-object p2, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport$TypeCheckingDSL$$ExternalSyntheticLambda0;->INSTANCE:Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport$TypeCheckingDSL$$ExternalSyntheticLambda0;

    invoke-interface {p1, v1, p2}, Ljava/util/Map;->computeIfAbsent(Ljava/lang/Object;Ljava/util/function/Function;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/util/List;

    .line 509
    aget-object p2, v0, v2

    check-cast p2, Lgroovy/lang/Closure;

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    const/4 p1, 0x0

    return-object p1

    .line 512
    :cond_3
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport$TypeCheckingDSL;->extension:Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;

    invoke-static {v0, p1, p2}, Lorg/codehaus/groovy/runtime/InvokerHelper;->invokeMethod(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public setProperty(Ljava/lang/String;Ljava/lang/Object;)V
    .locals 1

    .line 483
    :try_start_0
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport$TypeCheckingDSL;->extension:Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;

    invoke-static {v0, p1, p2}, Lorg/codehaus/groovy/runtime/InvokerHelper;->setProperty(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    .line 485
    :catch_0
    invoke-super {p0, p1, p2}, Lgroovy/lang/Script;->setProperty(Ljava/lang/String;Ljava/lang/Object;)V

    :goto_0
    return-void
.end method
