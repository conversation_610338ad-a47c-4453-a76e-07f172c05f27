.class public Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingSupport$ShortArrayStaticTypesHelper;
.super Ljava/lang/Object;
.source "StaticTypeCheckingSupport.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingSupport;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "ShortArrayStaticTypesHelper"
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 2338
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static getAt([SI)Ljava/lang/Short;
    .locals 0

    if-eqz p0, :cond_0

    .line 2340
    aget-short p0, p0, p1

    invoke-static {p0}, Ljava/lang/Short;->valueOf(S)Ljava/lang/Short;

    move-result-object p0

    goto :goto_0

    :cond_0
    const/4 p0, 0x0

    :goto_0
    return-object p0
.end method

.method public static putAt([SIS)V
    .locals 0

    if-eqz p0, :cond_0

    .line 2344
    aput-short p2, p0, p1

    :cond_0
    return-void
.end method
