.class public final synthetic Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda28;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/IntFunction;


# static fields
.field public static final synthetic INSTANCE:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda28;


# direct methods
.method static synthetic constructor <clinit>()V
    .locals 1

    new-instance v0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda28;

    invoke-direct {v0}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda28;-><init>()V

    sput-object v0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda28;->INSTANCE:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda28;

    return-void
.end method

.method private synthetic constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final apply(I)Ljava/lang/Object;
    .locals 0

    invoke-static {p1}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->lambda$convertToStringArray$22(I)[Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method
