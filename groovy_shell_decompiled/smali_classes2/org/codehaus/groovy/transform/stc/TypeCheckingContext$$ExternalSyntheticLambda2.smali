.class public final synthetic Lorg/codehaus/groovy/transform/stc/TypeCheckingContext$$ExternalSyntheticLambda2;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Supplier;


# instance fields
.field public final synthetic f$0:Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;


# direct methods
.method public synthetic constructor <init>(Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/codehaus/groovy/transform/stc/TypeCheckingContext$$ExternalSyntheticLambda2;->f$0:Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;

    return-void
.end method


# virtual methods
.method public final get()Ljava/lang/Object;
    .locals 1

    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/TypeCheckingContext$$ExternalSyntheticLambda2;->f$0:Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;

    invoke-virtual {v0}, Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;->lambda$pushErrorCollector$0$org-codehaus-groovy-transform-stc-TypeCheckingContext()Lorg/codehaus/groovy/control/CompilerConfiguration;

    move-result-object v0

    return-object v0
.end method
