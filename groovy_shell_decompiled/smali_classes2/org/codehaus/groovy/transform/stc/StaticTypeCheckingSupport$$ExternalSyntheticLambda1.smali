.class public final synthetic Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingSupport$$ExternalSyntheticLambda1;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Function;


# instance fields
.field public final synthetic f$0:Ljava/util/Map;


# direct methods
.method public synthetic constructor <init>(Ljava/util/Map;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingSupport$$ExternalSyntheticLambda1;->f$0:Ljava/util/Map;

    return-void
.end method


# virtual methods
.method public final apply(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingSupport$$ExternalSyntheticLambda1;->f$0:Ljava/util/Map;

    check-cast p1, Lorg/codehaus/groovy/ast/Parameter;

    invoke-static {v0, p1}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingSupport;->lambda$makeRawTypes$3(Ljava/util/Map;Lorg/codehaus/groovy/ast/Parameter;)Lorg/codehaus/groovy/ast/Parameter;

    move-result-object p1

    return-object p1
.end method
