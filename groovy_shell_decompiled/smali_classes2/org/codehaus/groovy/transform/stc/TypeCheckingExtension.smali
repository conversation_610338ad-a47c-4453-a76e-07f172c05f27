.class public Lorg/codehaus/groovy/transform/stc/TypeCheckingExtension;
.super Ljava/lang/Object;
.source "TypeCheckingExtension.java"


# instance fields
.field protected final typeCheckingVisitor:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;


# direct methods
.method public constructor <init>(Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;)V
    .locals 0

    .line 57
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 58
    iput-object p1, p0, Lorg/codehaus/groovy/transform/stc/TypeCheckingExtension;->typeCheckingVisitor:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;

    return-void
.end method


# virtual methods
.method public addStaticTypeError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V
    .locals 1

    .line 276
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/TypeCheckingExtension;->typeCheckingVisitor:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;

    invoke-virtual {v0, p1, p2}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->addStaticTypeError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    return-void
.end method

.method public afterMethodCall(Lorg/codehaus/groovy/ast/expr/MethodCall;)V
    .locals 0

    return-void
.end method

.method public afterVisitClass(Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 0

    return-void
.end method

.method public afterVisitMethod(Lorg/codehaus/groovy/ast/MethodNode;)V
    .locals 0

    return-void
.end method

.method public beforeMethodCall(Lorg/codehaus/groovy/ast/expr/MethodCall;)Z
    .locals 0

    const/4 p1, 0x0

    return p1
.end method

.method public beforeVisitClass(Lorg/codehaus/groovy/ast/ClassNode;)Z
    .locals 0

    const/4 p1, 0x0

    return p1
.end method

.method public beforeVisitMethod(Lorg/codehaus/groovy/ast/MethodNode;)Z
    .locals 0

    const/4 p1, 0x0

    return p1
.end method

.method public buildListType(Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/ClassNode;
    .locals 3

    .line 351
    sget-object v0, Lorg/codehaus/groovy/ast/ClassHelper;->LIST_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    const/4 v1, 0x1

    new-array v1, v1, [Lorg/codehaus/groovy/ast/ClassNode;

    const/4 v2, 0x0

    aput-object p1, v1, v2

    invoke-virtual {p0, v0, v1}, Lorg/codehaus/groovy/transform/stc/TypeCheckingExtension;->parameterizedType(Lorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    return-object p1
.end method

.method public buildMapType(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/ClassNode;
    .locals 3

    .line 362
    sget-object v0, Lorg/codehaus/groovy/ast/ClassHelper;->MAP_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    const/4 v1, 0x2

    new-array v1, v1, [Lorg/codehaus/groovy/ast/ClassNode;

    const/4 v2, 0x0

    aput-object p1, v1, v2

    const/4 p1, 0x1

    aput-object p2, v1, p1

    invoke-virtual {p0, v0, v1}, Lorg/codehaus/groovy/transform/stc/TypeCheckingExtension;->parameterizedType(Lorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    return-object p1
.end method

.method public classNodeFor(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;
    .locals 0

    .line 305
    invoke-static {p1}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    return-object p1
.end method

.method public classNodeFor(Ljava/lang/String;)Lorg/codehaus/groovy/ast/ClassNode;
    .locals 0

    .line 309
    invoke-static {p1}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/String;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    return-object p1
.end method

.method public existsProperty(Lorg/codehaus/groovy/ast/expr/PropertyExpression;Z)Z
    .locals 1

    .line 289
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/TypeCheckingExtension;->typeCheckingVisitor:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;

    invoke-virtual {v0, p1, p2}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->existsProperty(Lorg/codehaus/groovy/ast/expr/PropertyExpression;Z)Z

    move-result p1

    return p1
.end method

.method public existsProperty(Lorg/codehaus/groovy/ast/expr/PropertyExpression;ZLorg/codehaus/groovy/ast/ClassCodeVisitorSupport;)Z
    .locals 1

    .line 293
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/TypeCheckingExtension;->typeCheckingVisitor:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;

    invoke-virtual {v0, p1, p2, p3}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->existsProperty(Lorg/codehaus/groovy/ast/expr/PropertyExpression;ZLorg/codehaus/groovy/ast/ClassCodeVisitorSupport;)Z

    move-result p1

    return p1
.end method

.method public extractStaticReceiver(Lorg/codehaus/groovy/ast/expr/MethodCall;)Lorg/codehaus/groovy/ast/ClassNode;
    .locals 4

    .line 374
    instance-of v0, p1, Lorg/codehaus/groovy/ast/expr/StaticMethodCallExpression;

    if-eqz v0, :cond_0

    .line 375
    check-cast p1, Lorg/codehaus/groovy/ast/expr/StaticMethodCallExpression;

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/StaticMethodCallExpression;->getOwnerType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    return-object p1

    .line 376
    :cond_0
    instance-of v0, p1, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    if-eqz v0, :cond_2

    .line 377
    check-cast p1, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->getObjectExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    .line 378
    instance-of v0, p1, Lorg/codehaus/groovy/ast/expr/ClassExpression;

    if-eqz v0, :cond_1

    sget-object v1, Lorg/codehaus/groovy/ast/ClassHelper;->CLASS_Type:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/Expression;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v2

    invoke-virtual {v1, v2}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_1

    .line 379
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/Expression;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/ClassNode;->getGenericsTypes()[Lorg/codehaus/groovy/ast/GenericsType;

    move-result-object v1

    if-eqz v1, :cond_1

    .line 380
    array-length v2, v1

    const/4 v3, 0x1

    if-ne v2, v3, :cond_1

    const/4 p1, 0x0

    .line 381
    aget-object p1, v1, p1

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/GenericsType;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    return-object p1

    :cond_1
    if-eqz v0, :cond_2

    .line 385
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/Expression;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    return-object p1

    :cond_2
    const/4 p1, 0x0

    return-object p1
.end method

.method public finish()V
    .locals 0

    return-void
.end method

.method public getArgumentTypes(Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;)[Lorg/codehaus/groovy/ast/ClassNode;
    .locals 1

    .line 297
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/TypeCheckingExtension;->typeCheckingVisitor:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;

    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->getArgumentTypes(Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;)[Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    return-object p1
.end method

.method public getTargetMethod(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/MethodNode;
    .locals 1

    .line 301
    sget-object v0, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->DIRECT_METHOD_CALL_TARGET:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    invoke-virtual {p1, v0}, Lorg/codehaus/groovy/ast/expr/Expression;->getNodeMetaData(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lorg/codehaus/groovy/ast/MethodNode;

    return-object p1
.end method

.method public getType(Lorg/codehaus/groovy/ast/ASTNode;)Lorg/codehaus/groovy/ast/ClassNode;
    .locals 1

    .line 267
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/TypeCheckingExtension;->typeCheckingVisitor:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;

    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->getType(Lorg/codehaus/groovy/ast/ASTNode;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    return-object p1
.end method

.method public handleAmbiguousMethods(Ljava/util/List;Lorg/codehaus/groovy/ast/expr/Expression;)Ljava/util/List;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/MethodNode;",
            ">;",
            "Lorg/codehaus/groovy/ast/expr/Expression;",
            ")",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/MethodNode;",
            ">;"
        }
    .end annotation

    return-object p1
.end method

.method public handleIncompatibleAssignment(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;)Z
    .locals 0

    const/4 p1, 0x0

    return p1
.end method

.method public handleIncompatibleReturnType(Lorg/codehaus/groovy/ast/stmt/ReturnStatement;Lorg/codehaus/groovy/ast/ClassNode;)Z
    .locals 0

    const/4 p1, 0x0

    return p1
.end method

.method public handleMissingMethod(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/MethodCall;)Ljava/util/List;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            "Ljava/lang/String;",
            "Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;",
            "[",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            "Lorg/codehaus/groovy/ast/expr/MethodCall;",
            ")",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/MethodNode;",
            ">;"
        }
    .end annotation

    .line 129
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object p1

    return-object p1
.end method

.method public handleUnresolvedAttribute(Lorg/codehaus/groovy/ast/expr/AttributeExpression;)Z
    .locals 0

    const/4 p1, 0x0

    return p1
.end method

.method public handleUnresolvedProperty(Lorg/codehaus/groovy/ast/expr/PropertyExpression;)Z
    .locals 0

    const/4 p1, 0x0

    return p1
.end method

.method public handleUnresolvedVariableExpression(Lorg/codehaus/groovy/ast/expr/VariableExpression;)Z
    .locals 0

    const/4 p1, 0x0

    return p1
.end method

.method public isStaticMethodCallOnClass(Lorg/codehaus/groovy/ast/expr/MethodCall;Lorg/codehaus/groovy/ast/ClassNode;)Z
    .locals 0

    .line 399
    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/transform/stc/TypeCheckingExtension;->extractStaticReceiver(Lorg/codehaus/groovy/ast/expr/MethodCall;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    if-eqz p1, :cond_0

    .line 400
    invoke-virtual {p1, p2}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public lookupClassNodeFor(Ljava/lang/String;)Lorg/codehaus/groovy/ast/ClassNode;
    .locals 3

    .line 319
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/TypeCheckingExtension;->typeCheckingVisitor:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;

    invoke-virtual {v0}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->getSourceUnit()Lorg/codehaus/groovy/control/SourceUnit;

    move-result-object v0

    invoke-virtual {v0}, Lorg/codehaus/groovy/control/SourceUnit;->getAST()Lorg/codehaus/groovy/ast/ModuleNode;

    move-result-object v0

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ModuleNode;->getClasses()Ljava/util/List;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/ast/ClassNode;

    .line 320
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v2, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_0

    return-object v1

    :cond_1
    const/4 p1, 0x0

    return-object p1
.end method

.method public onMethodSelection(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/MethodNode;)V
    .locals 0

    return-void
.end method

.method public varargs parameterizedType(Lorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/ClassNode;
    .locals 6

    .line 328
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getPlainNodeReference()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    .line 329
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->isUsingGenerics()Z

    move-result v1

    if-eqz v1, :cond_2

    .line 330
    array-length v1, p2

    new-array v2, v1, [Lorg/codehaus/groovy/ast/GenericsType;

    .line 331
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->getGenericsTypes()[Lorg/codehaus/groovy/ast/GenericsType;

    move-result-object v3

    array-length v3, v3

    .line 332
    array-length v4, p2

    const/4 v5, 0x0

    if-ne v3, v4, :cond_1

    :goto_0
    if-ge v5, v1, :cond_0

    .line 337
    new-instance p1, Lorg/codehaus/groovy/ast/GenericsType;

    aget-object v3, p2, v5

    invoke-direct {p1, v3}, Lorg/codehaus/groovy/ast/GenericsType;-><init>(Lorg/codehaus/groovy/ast/ClassNode;)V

    aput-object p1, v2, v5

    add-int/lit8 v5, v5, 0x1

    goto :goto_0

    .line 339
    :cond_0
    invoke-virtual {v0, v2}, Lorg/codehaus/groovy/ast/ClassNode;->setGenericsTypes([Lorg/codehaus/groovy/ast/GenericsType;)V

    goto :goto_1

    .line 333
    :cond_1
    new-instance v0, Lorg/codehaus/groovy/GroovyBugError;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "Expected number of generic type arguments for "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {p1, v5}, Lorg/codehaus/groovy/ast/ClassNode;->toString(Z)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v1, " is "

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v3}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v1, " but you gave "

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    array-length p2, p2

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {v0, p1}, Lorg/codehaus/groovy/GroovyBugError;-><init>(Ljava/lang/String;)V

    throw v0

    :cond_2
    :goto_1
    return-object v0
.end method

.method public setup()V
    .locals 0

    return-void
.end method

.method public storeType(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 1

    .line 285
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/TypeCheckingExtension;->typeCheckingVisitor:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;

    invoke-virtual {v0, p1, p2}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->storeType(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/ClassNode;)V

    return-void
.end method
