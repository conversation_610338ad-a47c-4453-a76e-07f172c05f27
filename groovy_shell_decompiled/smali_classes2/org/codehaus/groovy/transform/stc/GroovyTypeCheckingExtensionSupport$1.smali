.class Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport$1;
.super Ljava/util/HashMap;
.source "GroovyTypeCheckingExtensionSupport.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/util/HashMap<",
        "Ljava/lang/String;",
        "Ljava/lang/String;",
        ">;"
    }
.end annotation


# static fields
.field private static final serialVersionUID:J = 0x7c0cab814c7a7d7dL


# direct methods
.method constructor <init>()V
    .locals 2

    .line 67
    invoke-direct {p0}, Ljava/util/HashMap;-><init>()V

    const-string v0, "onMethodSelection"

    .line 71
    invoke-virtual {p0, v0, v0}, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport$1;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const-string v0, "afterMethodCall"

    .line 72
    invoke-virtual {p0, v0, v0}, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport$1;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const-string v0, "beforeMethodCall"

    .line 73
    invoke-virtual {p0, v0, v0}, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport$1;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const-string v0, "unresolvedVariable"

    const-string v1, "handleUnresolvedVariableExpression"

    .line 74
    invoke-virtual {p0, v0, v1}, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport$1;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const-string v0, "unresolvedProperty"

    const-string v1, "handleUnresolvedProperty"

    .line 75
    invoke-virtual {p0, v0, v1}, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport$1;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const-string v0, "unresolvedAttribute"

    const-string v1, "handleUnresolvedAttribute"

    .line 76
    invoke-virtual {p0, v0, v1}, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport$1;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const-string v0, "ambiguousMethods"

    const-string v1, "handleAmbiguousMethods"

    .line 77
    invoke-virtual {p0, v0, v1}, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport$1;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const-string v0, "methodNotFound"

    const-string v1, "handleMissingMethod"

    .line 78
    invoke-virtual {p0, v0, v1}, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport$1;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const-string v0, "afterVisitMethod"

    .line 79
    invoke-virtual {p0, v0, v0}, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport$1;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const-string v0, "beforeVisitMethod"

    .line 80
    invoke-virtual {p0, v0, v0}, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport$1;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const-string v0, "afterVisitClass"

    .line 81
    invoke-virtual {p0, v0, v0}, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport$1;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const-string v0, "beforeVisitClass"

    .line 82
    invoke-virtual {p0, v0, v0}, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport$1;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const-string v0, "incompatibleAssignment"

    const-string v1, "handleIncompatibleAssignment"

    .line 83
    invoke-virtual {p0, v0, v1}, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport$1;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const-string v0, "incompatibleReturnType"

    const-string v1, "handleIncompatibleReturnType"

    .line 84
    invoke-virtual {p0, v0, v1}, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport$1;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const-string v0, "setup"

    .line 85
    invoke-virtual {p0, v0, v0}, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport$1;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const-string v0, "finish"

    .line 86
    invoke-virtual {p0, v0, v0}, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport$1;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method
