.class Lorg/codehaus/groovy/transform/stc/SecondPassExpression;
.super Ljava/lang/Object;
.source "SecondPassExpression.java"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;"
    }
.end annotation


# instance fields
.field private final data:Ljava/lang/Object;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "TT;"
        }
    .end annotation
.end field

.field private final expression:Lorg/codehaus/groovy/ast/expr/Expression;


# direct methods
.method constructor <init>(Lorg/codehaus/groovy/ast/expr/Expression;)V
    .locals 0

    .line 34
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 35
    iput-object p1, p0, Lorg/codehaus/groovy/transform/stc/SecondPassExpression;->expression:Lorg/codehaus/groovy/ast/expr/Expression;

    const/4 p1, 0x0

    .line 36
    iput-object p1, p0, Lorg/codehaus/groovy/transform/stc/SecondPassExpression;->data:Ljava/lang/Object;

    return-void
.end method

.method constructor <init>(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/Object;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/expr/Expression;",
            "TT;)V"
        }
    .end annotation

    .line 39
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 40
    iput-object p2, p0, Lorg/codehaus/groovy/transform/stc/SecondPassExpression;->data:Ljava/lang/Object;

    .line 41
    iput-object p1, p0, Lorg/codehaus/groovy/transform/stc/SecondPassExpression;->expression:Lorg/codehaus/groovy/ast/expr/Expression;

    return-void
.end method


# virtual methods
.method public equals(Ljava/lang/Object;)Z
    .locals 4

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    :cond_0
    const/4 v1, 0x0

    if-eqz p1, :cond_4

    .line 55
    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v2

    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v3

    if-eq v2, v3, :cond_1

    goto :goto_0

    .line 57
    :cond_1
    check-cast p1, Lorg/codehaus/groovy/transform/stc/SecondPassExpression;

    .line 59
    iget-object v2, p0, Lorg/codehaus/groovy/transform/stc/SecondPassExpression;->data:Ljava/lang/Object;

    iget-object v3, p1, Lorg/codehaus/groovy/transform/stc/SecondPassExpression;->data:Ljava/lang/Object;

    invoke-static {v2, v3}, Ljava/util/Objects;->equals(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-nez v2, :cond_2

    return v1

    .line 60
    :cond_2
    iget-object v2, p0, Lorg/codehaus/groovy/transform/stc/SecondPassExpression;->expression:Lorg/codehaus/groovy/ast/expr/Expression;

    iget-object p1, p1, Lorg/codehaus/groovy/transform/stc/SecondPassExpression;->expression:Lorg/codehaus/groovy/ast/expr/Expression;

    invoke-virtual {v2, p1}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-nez p1, :cond_3

    return v1

    :cond_3
    return v0

    :cond_4
    :goto_0
    return v1
.end method

.method public getData()Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()TT;"
        }
    .end annotation

    .line 45
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/SecondPassExpression;->data:Ljava/lang/Object;

    return-object v0
.end method

.method public getExpression()Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 1

    .line 49
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/SecondPassExpression;->expression:Lorg/codehaus/groovy/ast/expr/Expression;

    return-object v0
.end method

.method public hashCode()I
    .locals 2

    .line 67
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/SecondPassExpression;->expression:Lorg/codehaus/groovy/ast/expr/Expression;

    invoke-virtual {v0}, Ljava/lang/Object;->hashCode()I

    move-result v0

    mul-int/lit8 v0, v0, 0x1f

    .line 68
    iget-object v1, p0, Lorg/codehaus/groovy/transform/stc/SecondPassExpression;->data:Ljava/lang/Object;

    if-eqz v1, :cond_0

    invoke-virtual {v1}, Ljava/lang/Object;->hashCode()I

    move-result v1

    goto :goto_0

    :cond_0
    const/4 v1, 0x0

    :goto_0
    add-int/2addr v0, v1

    return v0
.end method
