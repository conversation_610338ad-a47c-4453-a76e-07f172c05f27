.class public final synthetic Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda3;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Consumer;


# instance fields
.field public final synthetic f$0:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;

.field public final synthetic f$1:Ljava/util/Map;

.field public final synthetic f$2:Lorg/codehaus/groovy/ast/expr/VariableExpression;


# direct methods
.method public synthetic constructor <init>(Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;Ljava/util/Map;Lorg/codehaus/groovy/ast/expr/VariableExpression;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda3;->f$0:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;

    iput-object p2, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda3;->f$1:Ljava/util/Map;

    iput-object p3, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda3;->f$2:Lorg/codehaus/groovy/ast/expr/VariableExpression;

    return-void
.end method


# virtual methods
.method public final accept(Ljava/lang/Object;)V
    .locals 3

    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda3;->f$0:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;

    iget-object v1, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda3;->f$1:Ljava/util/Map;

    iget-object v2, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda3;->f$2:Lorg/codehaus/groovy/ast/expr/VariableExpression;

    check-cast p1, Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v0, v1, v2, p1}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->lambda$popAssignmentTracking$31$org-codehaus-groovy-transform-stc-StaticTypeCheckingVisitor(Ljava/util/Map;Lorg/codehaus/groovy/ast/expr/VariableExpression;Lorg/codehaus/groovy/ast/ClassNode;)V

    return-void
.end method
