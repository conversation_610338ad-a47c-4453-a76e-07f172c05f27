.class Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$ExtensionMethodDeclaringClass;
.super Ljava/lang/Object;
.source "StaticTypeCheckingVisitor.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0xa
    name = "ExtensionMethodDeclaringClass"
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 5350
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
