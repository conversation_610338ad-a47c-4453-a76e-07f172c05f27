.class public final synthetic Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda1;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Consumer;


# instance fields
.field public final synthetic f$0:Ljava/util/Map;

.field public final synthetic f$1:Lorg/codehaus/groovy/ast/GenericsType;


# direct methods
.method public synthetic constructor <init>(Ljava/util/Map;Lorg/codehaus/groovy/ast/GenericsType;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda1;->f$0:Ljava/util/Map;

    iput-object p2, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda1;->f$1:Lorg/codehaus/groovy/ast/GenericsType;

    return-void
.end method


# virtual methods
.method public final accept(Ljava/lang/Object;)V
    .locals 2

    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda1;->f$0:Ljava/util/Map;

    iget-object v1, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda1;->f$1:Lorg/codehaus/groovy/ast/GenericsType;

    check-cast p1, Lorg/codehaus/groovy/ast/GenericsType;

    invoke-static {v0, v1, p1}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->lambda$resolvePlaceholdersFromImplicitTypeHints$38(Ljava/util/Map;Lorg/codehaus/groovy/ast/GenericsType;Lorg/codehaus/groovy/ast/GenericsType;)V

    return-void
.end method
