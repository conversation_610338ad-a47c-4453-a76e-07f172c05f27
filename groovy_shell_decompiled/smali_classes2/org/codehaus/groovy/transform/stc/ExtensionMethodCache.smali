.class public Lorg/codehaus/groovy/transform/stc/ExtensionMethodCache;
.super Lorg/codehaus/groovy/transform/stc/AbstractExtensionMethodCache;
.source "ExtensionMethodCache.java"


# static fields
.field public static final INSTANCE:Lorg/codehaus/groovy/transform/stc/ExtensionMethodCache;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 39
    new-instance v0, Lorg/codehaus/groovy/transform/stc/ExtensionMethodCache;

    invoke-direct {v0}, Lorg/codehaus/groovy/transform/stc/ExtensionMethodCache;-><init>()V

    sput-object v0, Lorg/codehaus/groovy/transform/stc/ExtensionMethodCache;->INSTANCE:Lorg/codehaus/groovy/transform/stc/ExtensionMethodCache;

    return-void
.end method

.method private constructor <init>()V
    .locals 0

    .line 41
    invoke-direct {p0}, Lorg/codehaus/groovy/transform/stc/AbstractExtensionMethodCache;-><init>()V

    return-void
.end method

.method static synthetic lambda$getMethodFilter$0(Lorg/codehaus/groovy/ast/MethodNode;)Z
    .locals 1

    .line 65
    sget-object v0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingSupport;->Deprecated_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/ast/MethodNode;->getAnnotations(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/util/List;

    move-result-object p0

    invoke-interface {p0}, Ljava/util/List;->isEmpty()Z

    move-result p0

    xor-int/lit8 p0, p0, 0x1

    return p0
.end method

.method static synthetic lambda$getMethodMapper$1(Lorg/codehaus/groovy/ast/MethodNode;)Ljava/lang/String;
    .locals 1

    .line 70
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/MethodNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object p0

    const/4 v0, 0x0

    aget-object p0, p0, v0

    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/Parameter;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p0

    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method protected addAdditionalClassesToScan(Ljava/util/Set;Ljava/util/Set;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Set<",
            "Ljava/lang/Class;",
            ">;",
            "Ljava/util/Set<",
            "Ljava/lang/Class;",
            ">;)V"
        }
    .end annotation

    .line 45
    sget-object v0, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->DGM_LIKE_CLASSES:[Ljava/lang/Class;

    invoke-static {p1, v0}, Ljava/util/Collections;->addAll(Ljava/util/Collection;[Ljava/lang/Object;)Z

    .line 46
    sget-object v0, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->ADDITIONAL_CLASSES:[Ljava/lang/Class;

    invoke-static {p1, v0}, Ljava/util/Collections;->addAll(Ljava/util/Collection;[Ljava/lang/Object;)Z

    .line 47
    const-class v0, Lorg/codehaus/groovy/runtime/DefaultGroovyStaticMethods;

    invoke-interface {p2, v0}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 49
    const-class v0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingSupport$ObjectArrayStaticTypesHelper;

    invoke-interface {p1, v0}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 50
    const-class v0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingSupport$BooleanArrayStaticTypesHelper;

    invoke-interface {p1, v0}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 51
    const-class v0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingSupport$CharArrayStaticTypesHelper;

    invoke-interface {p1, v0}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 52
    const-class v0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingSupport$ByteArrayStaticTypesHelper;

    invoke-interface {p1, v0}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 53
    const-class v0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingSupport$ShortArrayStaticTypesHelper;

    invoke-interface {p1, v0}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 54
    const-class v0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingSupport$IntArrayStaticTypesHelper;

    invoke-interface {p1, v0}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 55
    const-class v0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingSupport$LongArrayStaticTypesHelper;

    invoke-interface {p1, v0}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 56
    const-class v0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingSupport$FloatArrayStaticTypesHelper;

    invoke-interface {p1, v0}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 57
    const-class v0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingSupport$DoubleArrayStaticTypesHelper;

    invoke-interface {p1, v0}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 59
    invoke-static {}, Lorg/codehaus/groovy/vmplugin/VMPluginFactory;->getPlugin()Lorg/codehaus/groovy/vmplugin/VMPlugin;

    move-result-object v0

    invoke-interface {v0}, Lorg/codehaus/groovy/vmplugin/VMPlugin;->getPluginDefaultGroovyMethods()[Ljava/lang/Class;

    move-result-object v0

    invoke-static {p1, v0}, Ljava/util/Collections;->addAll(Ljava/util/Collection;[Ljava/lang/Object;)Z

    .line 60
    invoke-static {}, Lorg/codehaus/groovy/vmplugin/VMPluginFactory;->getPlugin()Lorg/codehaus/groovy/vmplugin/VMPlugin;

    move-result-object p1

    invoke-interface {p1}, Lorg/codehaus/groovy/vmplugin/VMPlugin;->getPluginStaticGroovyMethods()[Ljava/lang/Class;

    move-result-object p1

    invoke-static {p2, p1}, Ljava/util/Collections;->addAll(Ljava/util/Collection;[Ljava/lang/Object;)Z

    return-void
.end method

.method protected getMethodFilter()Ljava/util/function/Predicate;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/function/Predicate<",
            "Lorg/codehaus/groovy/ast/MethodNode;",
            ">;"
        }
    .end annotation

    .line 65
    sget-object v0, Lorg/codehaus/groovy/transform/stc/ExtensionMethodCache$$ExternalSyntheticLambda1;->INSTANCE:Lorg/codehaus/groovy/transform/stc/ExtensionMethodCache$$ExternalSyntheticLambda1;

    return-object v0
.end method

.method protected getMethodMapper()Ljava/util/function/Function;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/function/Function<",
            "Lorg/codehaus/groovy/ast/MethodNode;",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .line 70
    sget-object v0, Lorg/codehaus/groovy/transform/stc/ExtensionMethodCache$$ExternalSyntheticLambda0;->INSTANCE:Lorg/codehaus/groovy/transform/stc/ExtensionMethodCache$$ExternalSyntheticLambda0;

    return-object v0
.end method
