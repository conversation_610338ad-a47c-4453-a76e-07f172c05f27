.class public final synthetic Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda40;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Predicate;


# instance fields
.field public final synthetic f$0:Lorg/codehaus/groovy/ast/MethodNode;


# direct methods
.method public synthetic constructor <init>(Lorg/codehaus/groovy/ast/MethodNode;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda40;->f$0:Lorg/codehaus/groovy/ast/MethodNode;

    return-void
.end method


# virtual methods
.method public final test(Ljava/lang/Object;)Z
    .locals 1

    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda40;->f$0:Lorg/codehaus/groovy/ast/MethodNode;

    check-cast p1, Lorg/codehaus/groovy/ast/MethodNode;

    invoke-static {v0, p1}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->lambda$visitMethodPointerExpression$16(Lorg/codehaus/groovy/ast/MethodNode;Lorg/codehaus/groovy/ast/MethodNode;)Z

    move-result p1

    return p1
.end method
