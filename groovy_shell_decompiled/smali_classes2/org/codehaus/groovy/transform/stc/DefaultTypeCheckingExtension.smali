.class public Lorg/codehaus/groovy/transform/stc/DefaultTypeCheckingExtension;
.super Lorg/codehaus/groovy/transform/stc/TypeCheckingExtension;
.source "DefaultTypeCheckingExtension.java"


# instance fields
.field protected final handlers:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/transform/stc/TypeCheckingExtension;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;)V
    .locals 0

    .line 53
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/transform/stc/TypeCheckingExtension;-><init>(Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;)V

    .line 50
    new-instance p1, Ljava/util/LinkedList;

    invoke-direct {p1}, Ljava/util/LinkedList;-><init>()V

    iput-object p1, p0, Lorg/codehaus/groovy/transform/stc/DefaultTypeCheckingExtension;->handlers:Ljava/util/List;

    return-void
.end method


# virtual methods
.method public addHandler(Lorg/codehaus/groovy/transform/stc/TypeCheckingExtension;)V
    .locals 1

    .line 57
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/DefaultTypeCheckingExtension;->handlers:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public afterMethodCall(Lorg/codehaus/groovy/ast/expr/MethodCall;)V
    .locals 2

    .line 157
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/DefaultTypeCheckingExtension;->handlers:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/transform/stc/TypeCheckingExtension;

    .line 158
    invoke-virtual {v1, p1}, Lorg/codehaus/groovy/transform/stc/TypeCheckingExtension;->afterMethodCall(Lorg/codehaus/groovy/ast/expr/MethodCall;)V

    goto :goto_0

    :cond_0
    return-void
.end method

.method public afterVisitClass(Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 2

    .line 142
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/DefaultTypeCheckingExtension;->handlers:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/transform/stc/TypeCheckingExtension;

    .line 143
    invoke-virtual {v1, p1}, Lorg/codehaus/groovy/transform/stc/TypeCheckingExtension;->afterVisitClass(Lorg/codehaus/groovy/ast/ClassNode;)V

    goto :goto_0

    :cond_0
    return-void
.end method

.method public afterVisitMethod(Lorg/codehaus/groovy/ast/MethodNode;)V
    .locals 2

    .line 127
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/DefaultTypeCheckingExtension;->handlers:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/transform/stc/TypeCheckingExtension;

    .line 128
    invoke-virtual {v1, p1}, Lorg/codehaus/groovy/transform/stc/TypeCheckingExtension;->afterVisitMethod(Lorg/codehaus/groovy/ast/MethodNode;)V

    goto :goto_0

    :cond_0
    return-void
.end method

.method public beforeMethodCall(Lorg/codehaus/groovy/ast/expr/MethodCall;)Z
    .locals 2

    .line 165
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/DefaultTypeCheckingExtension;->handlers:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/transform/stc/TypeCheckingExtension;

    .line 166
    invoke-virtual {v1, p1}, Lorg/codehaus/groovy/transform/stc/TypeCheckingExtension;->beforeMethodCall(Lorg/codehaus/groovy/ast/expr/MethodCall;)Z

    move-result v1

    if-eqz v1, :cond_0

    const/4 p1, 0x1

    return p1

    :cond_1
    const/4 p1, 0x0

    return p1
.end method

.method public beforeVisitClass(Lorg/codehaus/groovy/ast/ClassNode;)Z
    .locals 2

    .line 149
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/DefaultTypeCheckingExtension;->handlers:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/transform/stc/TypeCheckingExtension;

    .line 150
    invoke-virtual {v1, p1}, Lorg/codehaus/groovy/transform/stc/TypeCheckingExtension;->beforeVisitClass(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v1

    if-eqz v1, :cond_0

    const/4 p1, 0x1

    return p1

    :cond_1
    const/4 p1, 0x0

    return p1
.end method

.method public beforeVisitMethod(Lorg/codehaus/groovy/ast/MethodNode;)Z
    .locals 2

    .line 134
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/DefaultTypeCheckingExtension;->handlers:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/transform/stc/TypeCheckingExtension;

    .line 135
    invoke-virtual {v1, p1}, Lorg/codehaus/groovy/transform/stc/TypeCheckingExtension;->beforeVisitMethod(Lorg/codehaus/groovy/ast/MethodNode;)Z

    move-result v1

    if-eqz v1, :cond_0

    const/4 p1, 0x1

    return p1

    :cond_1
    const/4 p1, 0x0

    return p1
.end method

.method public finish()V
    .locals 2

    .line 189
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/DefaultTypeCheckingExtension;->handlers:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/transform/stc/TypeCheckingExtension;

    .line 190
    invoke-virtual {v1}, Lorg/codehaus/groovy/transform/stc/TypeCheckingExtension;->finish()V

    goto :goto_0

    :cond_0
    return-void
.end method

.method public handleAmbiguousMethods(Ljava/util/List;Lorg/codehaus/groovy/ast/expr/Expression;)Ljava/util/List;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/MethodNode;",
            ">;",
            "Lorg/codehaus/groovy/ast/expr/Expression;",
            ")",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/MethodNode;",
            ">;"
        }
    .end annotation

    .line 104
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/DefaultTypeCheckingExtension;->handlers:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    .line 105
    :goto_0
    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result v1

    const/4 v2, 0x1

    if-le v1, v2, :cond_0

    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    .line 106
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/transform/stc/TypeCheckingExtension;

    invoke-virtual {v1, p1, p2}, Lorg/codehaus/groovy/transform/stc/TypeCheckingExtension;->handleAmbiguousMethods(Ljava/util/List;Lorg/codehaus/groovy/ast/expr/Expression;)Ljava/util/List;

    move-result-object p1

    goto :goto_0

    :cond_0
    return-object p1
.end method

.method public handleIncompatibleAssignment(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;)Z
    .locals 2

    .line 87
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/DefaultTypeCheckingExtension;->handlers:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/transform/stc/TypeCheckingExtension;

    .line 88
    invoke-virtual {v1, p1, p2, p3}, Lorg/codehaus/groovy/transform/stc/TypeCheckingExtension;->handleIncompatibleAssignment(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;)Z

    move-result v1

    if-eqz v1, :cond_0

    const/4 p1, 0x1

    return p1

    :cond_1
    const/4 p1, 0x0

    return p1
.end method

.method public handleIncompatibleReturnType(Lorg/codehaus/groovy/ast/stmt/ReturnStatement;Lorg/codehaus/groovy/ast/ClassNode;)Z
    .locals 2

    .line 95
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/DefaultTypeCheckingExtension;->handlers:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/transform/stc/TypeCheckingExtension;

    .line 96
    invoke-virtual {v1, p1, p2}, Lorg/codehaus/groovy/transform/stc/TypeCheckingExtension;->handleIncompatibleReturnType(Lorg/codehaus/groovy/ast/stmt/ReturnStatement;Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v1

    if-eqz v1, :cond_0

    const/4 p1, 0x1

    return p1

    :cond_1
    const/4 p1, 0x0

    return p1
.end method

.method public handleMissingMethod(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/MethodCall;)Ljava/util/List;
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            "Ljava/lang/String;",
            "Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;",
            "[",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            "Lorg/codehaus/groovy/ast/expr/MethodCall;",
            ")",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/MethodNode;",
            ">;"
        }
    .end annotation

    .line 112
    new-instance v0, Ljava/util/LinkedList;

    invoke-direct {v0}, Ljava/util/LinkedList;-><init>()V

    .line 113
    iget-object v1, p0, Lorg/codehaus/groovy/transform/stc/DefaultTypeCheckingExtension;->handlers:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_2

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    move-object v3, v2

    check-cast v3, Lorg/codehaus/groovy/transform/stc/TypeCheckingExtension;

    move-object v4, p1

    move-object v5, p2

    move-object v6, p3

    move-object v7, p4

    move-object v8, p5

    .line 114
    invoke-virtual/range {v3 .. v8}, Lorg/codehaus/groovy/transform/stc/TypeCheckingExtension;->handleMissingMethod(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/MethodCall;)Ljava/util/List;

    move-result-object v2

    .line 115
    invoke-interface {v2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v3

    :cond_0
    :goto_1
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    move-result v4

    if-eqz v4, :cond_1

    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lorg/codehaus/groovy/ast/MethodNode;

    .line 116
    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/MethodNode;->getDeclaringClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v5

    if-nez v5, :cond_0

    .line 117
    sget-object v5, Lorg/codehaus/groovy/ast/ClassHelper;->OBJECT_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v4, v5}, Lorg/codehaus/groovy/ast/MethodNode;->setDeclaringClass(Lorg/codehaus/groovy/ast/ClassNode;)V

    goto :goto_1

    .line 120
    :cond_1
    invoke-interface {v0, v2}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    goto :goto_0

    :cond_2
    return-object v0
.end method

.method public handleUnresolvedAttribute(Lorg/codehaus/groovy/ast/expr/AttributeExpression;)Z
    .locals 2

    .line 79
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/DefaultTypeCheckingExtension;->handlers:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/transform/stc/TypeCheckingExtension;

    .line 80
    invoke-virtual {v1, p1}, Lorg/codehaus/groovy/transform/stc/TypeCheckingExtension;->handleUnresolvedAttribute(Lorg/codehaus/groovy/ast/expr/AttributeExpression;)Z

    move-result v1

    if-eqz v1, :cond_0

    const/4 p1, 0x1

    return p1

    :cond_1
    const/4 p1, 0x0

    return p1
.end method

.method public handleUnresolvedProperty(Lorg/codehaus/groovy/ast/expr/PropertyExpression;)Z
    .locals 2

    .line 72
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/DefaultTypeCheckingExtension;->handlers:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/transform/stc/TypeCheckingExtension;

    .line 73
    invoke-virtual {v1, p1}, Lorg/codehaus/groovy/transform/stc/TypeCheckingExtension;->handleUnresolvedProperty(Lorg/codehaus/groovy/ast/expr/PropertyExpression;)Z

    move-result v1

    if-eqz v1, :cond_0

    const/4 p1, 0x1

    return p1

    :cond_1
    const/4 p1, 0x0

    return p1
.end method

.method public handleUnresolvedVariableExpression(Lorg/codehaus/groovy/ast/expr/VariableExpression;)Z
    .locals 2

    .line 65
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/DefaultTypeCheckingExtension;->handlers:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/transform/stc/TypeCheckingExtension;

    .line 66
    invoke-virtual {v1, p1}, Lorg/codehaus/groovy/transform/stc/TypeCheckingExtension;->handleUnresolvedVariableExpression(Lorg/codehaus/groovy/ast/expr/VariableExpression;)Z

    move-result v1

    if-eqz v1, :cond_0

    const/4 p1, 0x1

    return p1

    :cond_1
    const/4 p1, 0x0

    return p1
.end method

.method public onMethodSelection(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/MethodNode;)V
    .locals 2

    .line 173
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/DefaultTypeCheckingExtension;->handlers:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/transform/stc/TypeCheckingExtension;

    .line 174
    invoke-virtual {v1, p1, p2}, Lorg/codehaus/groovy/transform/stc/TypeCheckingExtension;->onMethodSelection(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/MethodNode;)V

    goto :goto_0

    :cond_0
    return-void
.end method

.method public removeHandler(Lorg/codehaus/groovy/transform/stc/TypeCheckingExtension;)V
    .locals 1

    .line 61
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/DefaultTypeCheckingExtension;->handlers:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->remove(Ljava/lang/Object;)Z

    return-void
.end method

.method public setup()V
    .locals 2

    .line 180
    new-instance v0, Ljava/util/ArrayList;

    iget-object v1, p0, Lorg/codehaus/groovy/transform/stc/DefaultTypeCheckingExtension;->handlers:Ljava/util/List;

    invoke-direct {v0, v1}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    .line 182
    invoke-virtual {v0}, Ljava/util/ArrayList;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/transform/stc/TypeCheckingExtension;

    .line 183
    invoke-virtual {v1}, Lorg/codehaus/groovy/transform/stc/TypeCheckingExtension;->setup()V

    goto :goto_0

    :cond_0
    return-void
.end method
