.class public final synthetic Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda55;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Lorg/codehaus/groovy/classgen/ReturnAdder$ReturnStatementListener;


# instance fields
.field public final synthetic f$0:Lorg/codehaus/groovy/ast/expr/Expression;

.field public final synthetic f$1:[Z


# direct methods
.method public synthetic constructor <init>(Lorg/codehaus/groovy/ast/expr/Expression;[Z)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda55;->f$0:Lorg/codehaus/groovy/ast/expr/Expression;

    iput-object p2, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda55;->f$1:[Z

    return-void
.end method


# virtual methods
.method public final returnStatementAdded(Lorg/codehaus/groovy/ast/stmt/ReturnStatement;)V
    .locals 2

    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda55;->f$0:Lorg/codehaus/groovy/ast/expr/Expression;

    iget-object v1, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda55;->f$1:[Z

    invoke-static {v0, v1, p1}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->lambda$isTypeSource$33(Lorg/codehaus/groovy/ast/expr/Expression;[ZLorg/codehaus/groovy/ast/stmt/ReturnStatement;)V

    return-void
.end method
