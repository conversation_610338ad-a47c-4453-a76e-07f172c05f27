.class public final synthetic Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda2;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Consumer;


# instance fields
.field public final synthetic f$0:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;


# direct methods
.method public synthetic constructor <init>(Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda2;->f$0:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;

    return-void
.end method


# virtual methods
.method public final accept(Ljava/lang/Object;)V
    .locals 1

    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda2;->f$0:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;

    check-cast p1, Lorg/codehaus/groovy/ast/InnerClassNode;

    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->visitClass(Lorg/codehaus/groovy/ast/ClassNode;)V

    return-void
.end method
