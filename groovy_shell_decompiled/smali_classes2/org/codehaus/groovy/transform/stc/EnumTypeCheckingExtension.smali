.class public Lorg/codehaus/groovy/transform/stc/EnumTypeCheckingExtension;
.super Lorg/codehaus/groovy/transform/stc/TypeCheckingExtension;
.source "EnumTypeCheckingExtension.java"


# direct methods
.method public constructor <init>(Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;)V
    .locals 0

    .line 38
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/transform/stc/TypeCheckingExtension;-><init>(Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;)V

    return-void
.end method


# virtual methods
.method public handleUnresolvedVariableExpression(Lorg/codehaus/groovy/ast/expr/VariableExpression;)Z
    .locals 5

    .line 43
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/EnumTypeCheckingExtension;->typeCheckingVisitor:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;

    iget-object v0, v0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->typeCheckingContext:Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;

    invoke-virtual {v0}, Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;->getEnclosingSwitchStatement()Lorg/codehaus/groovy/ast/stmt/SwitchStatement;

    move-result-object v0

    const/4 v1, 0x0

    if-nez v0, :cond_0

    return v1

    .line 47
    :cond_0
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/stmt/SwitchStatement;->getExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    sget-object v2, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->TYPE:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    invoke-virtual {v0, v2}, Lorg/codehaus/groovy/ast/expr/Expression;->getNodeMetaData(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/ast/ClassNode;

    if-nez v0, :cond_1

    return v1

    .line 51
    :cond_1
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->isEnum()Z

    move-result v2

    if-eqz v2, :cond_2

    .line 52
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->redirect()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v2

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->getName()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Lorg/codehaus/groovy/ast/ClassNode;->getField(Ljava/lang/String;)Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object v2

    if-eqz v2, :cond_2

    .line 54
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/FieldNode;->getModifiers()I

    move-result v3

    .line 55
    invoke-static {v3}, Ljava/lang/reflect/Modifier;->isPublic(I)Z

    move-result v4

    if-eqz v4, :cond_2

    invoke-static {v3}, Ljava/lang/reflect/Modifier;->isStatic(I)Z

    move-result v4

    if-eqz v4, :cond_2

    invoke-static {v3}, Ljava/lang/reflect/Modifier;->isFinal(I)Z

    move-result v3

    if-eqz v3, :cond_2

    .line 56
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/FieldNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v2

    invoke-virtual {v0, v2}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_2

    .line 57
    sget-object v1, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->SWITCH_CONDITION_EXPRESSION_TYPE:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    invoke-virtual {p1, v1, v0}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->putNodeMetaData(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/4 p1, 0x1

    return p1

    :cond_2
    return v1
.end method
