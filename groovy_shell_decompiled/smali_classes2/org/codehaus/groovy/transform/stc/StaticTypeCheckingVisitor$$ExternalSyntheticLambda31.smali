.class public final synthetic Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda31;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/BiConsumer;


# instance fields
.field public final synthetic f$0:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;

.field public final synthetic f$1:Ljava/util/Map;


# direct methods
.method public synthetic constructor <init>(Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;Ljava/util/Map;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda31;->f$0:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;

    iput-object p2, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda31;->f$1:Ljava/util/Map;

    return-void
.end method


# virtual methods
.method public final accept(Ljava/lang/Object;Ljava/lang/Object;)V
    .locals 2

    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda31;->f$0:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;

    iget-object v1, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda31;->f$1:Ljava/util/Map;

    check-cast p1, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    check-cast p2, Ljava/util/List;

    invoke-virtual {v0, v1, p1, p2}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->lambda$popAssignmentTracking$32$org-codehaus-groovy-transform-stc-StaticTypeCheckingVisitor(Ljava/util/Map;Lorg/codehaus/groovy/ast/expr/VariableExpression;Ljava/util/List;)V

    return-void
.end method
