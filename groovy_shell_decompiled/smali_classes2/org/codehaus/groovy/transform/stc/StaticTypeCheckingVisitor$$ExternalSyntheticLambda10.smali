.class public final synthetic Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda10;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Function;


# instance fields
.field public final synthetic f$0:Ljava/util/Map;

.field public final synthetic f$1:Lorg/codehaus/groovy/ast/GenericsType;


# direct methods
.method public synthetic constructor <init>(Ljava/util/Map;Lorg/codehaus/groovy/ast/GenericsType;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda10;->f$0:Ljava/util/Map;

    iput-object p2, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda10;->f$1:Lorg/codehaus/groovy/ast/GenericsType;

    return-void
.end method


# virtual methods
.method public final apply(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda10;->f$0:Ljava/util/Map;

    iget-object v1, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda10;->f$1:Lorg/codehaus/groovy/ast/GenericsType;

    check-cast p1, Lorg/codehaus/groovy/ast/GenericsType$GenericsTypeName;

    invoke-static {v0, v1, p1}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->lambda$inferReturnTypeGenerics$36(Ljava/util/Map;Lorg/codehaus/groovy/ast/GenericsType;Lorg/codehaus/groovy/ast/GenericsType$GenericsTypeName;)Lorg/codehaus/groovy/ast/GenericsType;

    move-result-object p1

    return-object p1
.end method
