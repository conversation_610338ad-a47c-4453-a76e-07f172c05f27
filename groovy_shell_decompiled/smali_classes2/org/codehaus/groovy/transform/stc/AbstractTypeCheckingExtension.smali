.class public Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;
.super Lorg/codehaus/groovy/transform/stc/TypeCheckingExtension;
.source "AbstractTypeCheckingExtension.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension$TypeCheckingScope;
    }
.end annotation


# static fields
.field private static final LOG:Ljava/util/logging/Logger;


# instance fields
.field protected final context:Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;

.field protected debug:Z

.field private final generatedMethods:Ljava/util/Set;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Set<",
            "Lorg/codehaus/groovy/ast/MethodNode;",
            ">;"
        }
    .end annotation
.end field

.field protected handled:Z

.field private final scopeData:Ljava/util/LinkedList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/LinkedList<",
            "Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension$TypeCheckingScope;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 60
    const-class v0, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;

    invoke-virtual {v0}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Ljava/util/logging/Logger;->getLogger(Ljava/lang/String;)Ljava/util/logging/Logger;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->LOG:Ljava/util/logging/Logger;

    return-void
.end method

.method public constructor <init>(Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;)V
    .locals 1

    .line 69
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/transform/stc/TypeCheckingExtension;-><init>(Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;)V

    .line 62
    new-instance v0, Ljava/util/LinkedHashSet;

    invoke-direct {v0}, Ljava/util/LinkedHashSet;-><init>()V

    iput-object v0, p0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->generatedMethods:Ljava/util/Set;

    .line 63
    new-instance v0, Ljava/util/LinkedList;

    invoke-direct {v0}, Ljava/util/LinkedList;-><init>()V

    iput-object v0, p0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->scopeData:Ljava/util/LinkedList;

    const/4 v0, 0x0

    .line 65
    iput-boolean v0, p0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->handled:Z

    .line 66
    iput-boolean v0, p0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->debug:Z

    .line 70
    iget-object p1, p1, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->typeCheckingContext:Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;

    iput-object p1, p0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->context:Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;

    return-void
.end method

.method private static matchWithOrWithourBoxing(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/Class;)Z
    .locals 1

    .line 206
    invoke-static {p1}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    .line 207
    invoke-static {p1}, Lorg/codehaus/groovy/ast/ClassHelper;->isPrimitiveType(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-static {p0}, Lorg/codehaus/groovy/ast/ClassHelper;->isPrimitiveType(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v0

    if-nez v0, :cond_0

    .line 208
    invoke-static {p1}, Lorg/codehaus/groovy/ast/ClassHelper;->getWrapper(Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    goto :goto_0

    .line 209
    :cond_0
    invoke-static {p0}, Lorg/codehaus/groovy/ast/ClassHelper;->isPrimitiveType(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v0

    if-eqz v0, :cond_1

    invoke-static {p1}, Lorg/codehaus/groovy/ast/ClassHelper;->isPrimitiveType(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v0

    if-nez v0, :cond_1

    .line 210
    invoke-static {p1}, Lorg/codehaus/groovy/ast/ClassHelper;->getUnwrapper(Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    .line 212
    :cond_1
    :goto_0
    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result p0

    return p0
.end method


# virtual methods
.method public argTypeMatches(Lorg/codehaus/groovy/ast/expr/MethodCall;ILjava/lang/Class;)Z
    .locals 1

    .line 244
    invoke-interface {p1}, Lorg/codehaus/groovy/ast/expr/MethodCall;->getArguments()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/classgen/asm/InvocationWriter;->makeArgumentList(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    move-result-object p1

    .line 245
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->typeCheckingVisitor:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;

    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->getArgumentTypes(Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;)[Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    .line 246
    invoke-virtual {p0, p1, p2, p3}, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->argTypeMatches([Lorg/codehaus/groovy/ast/ClassNode;ILjava/lang/Class;)Z

    move-result p1

    return p1
.end method

.method public argTypeMatches([Lorg/codehaus/groovy/ast/ClassNode;ILjava/lang/Class;)Z
    .locals 1

    .line 239
    array-length v0, p1

    if-lt p2, v0, :cond_0

    const/4 p1, 0x0

    return p1

    .line 240
    :cond_0
    aget-object p1, p1, p2

    invoke-static {p1, p3}, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->matchWithOrWithourBoxing(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/Class;)Z

    move-result p1

    return p1
.end method

.method public varargs argTypesMatches(Lorg/codehaus/groovy/ast/expr/MethodCall;[Ljava/lang/Class;)Z
    .locals 1

    .line 217
    invoke-interface {p1}, Lorg/codehaus/groovy/ast/expr/MethodCall;->getArguments()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/classgen/asm/InvocationWriter;->makeArgumentList(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    move-result-object p1

    .line 218
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->typeCheckingVisitor:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;

    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->getArgumentTypes(Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;)[Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    .line 219
    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->argTypesMatches([Lorg/codehaus/groovy/ast/ClassNode;[Ljava/lang/Class;)Z

    move-result p1

    return p1
.end method

.method public varargs argTypesMatches([Lorg/codehaus/groovy/ast/ClassNode;[Ljava/lang/Class;)Z
    .locals 4

    const/4 v0, 0x0

    const/4 v1, 0x1

    if-nez p2, :cond_2

    if-eqz p1, :cond_0

    .line 195
    array-length p1, p1

    if-nez p1, :cond_1

    :cond_0
    move v0, v1

    :cond_1
    return v0

    .line 196
    :cond_2
    array-length v2, p1

    array-length v3, p2

    if-eq v2, v3, :cond_3

    return v0

    .line 198
    :cond_3
    :goto_0
    array-length v2, p1

    if-ge v0, v2, :cond_4

    if-eqz v1, :cond_4

    .line 199
    aget-object v1, p1, v0

    aget-object v2, p2, v0

    invoke-static {v1, v2}, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->matchWithOrWithourBoxing(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/Class;)Z

    move-result v1

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_4
    return v1
.end method

.method public delegatesTo(Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 1

    const/4 v0, 0x0

    .line 150
    invoke-virtual {p0, p1, v0}, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->delegatesTo(Lorg/codehaus/groovy/ast/ClassNode;I)V

    return-void
.end method

.method public delegatesTo(Lorg/codehaus/groovy/ast/ClassNode;I)V
    .locals 1

    .line 154
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->typeCheckingVisitor:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;

    iget-object v0, v0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->typeCheckingContext:Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;

    iget-object v0, v0, Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;->delegationMetadata:Lorg/codehaus/groovy/transform/stc/DelegationMetadata;

    invoke-virtual {p0, p1, p2, v0}, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->delegatesTo(Lorg/codehaus/groovy/ast/ClassNode;ILorg/codehaus/groovy/transform/stc/DelegationMetadata;)V

    return-void
.end method

.method public delegatesTo(Lorg/codehaus/groovy/ast/ClassNode;ILorg/codehaus/groovy/transform/stc/DelegationMetadata;)V
    .locals 2

    .line 158
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->typeCheckingVisitor:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;

    iget-object v0, v0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->typeCheckingContext:Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;

    new-instance v1, Lorg/codehaus/groovy/transform/stc/DelegationMetadata;

    invoke-direct {v1, p1, p2, p3}, Lorg/codehaus/groovy/transform/stc/DelegationMetadata;-><init>(Lorg/codehaus/groovy/ast/ClassNode;ILorg/codehaus/groovy/transform/stc/DelegationMetadata;)V

    iput-object v1, v0, Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;->delegationMetadata:Lorg/codehaus/groovy/transform/stc/DelegationMetadata;

    return-void
.end method

.method public varargs firstArgTypesMatches(Lorg/codehaus/groovy/ast/expr/MethodCall;[Ljava/lang/Class;)Z
    .locals 1

    .line 233
    invoke-interface {p1}, Lorg/codehaus/groovy/ast/expr/MethodCall;->getArguments()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/classgen/asm/InvocationWriter;->makeArgumentList(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    move-result-object p1

    .line 234
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->typeCheckingVisitor:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;

    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->getArgumentTypes(Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;)[Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    .line 235
    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->firstArgTypesMatches([Lorg/codehaus/groovy/ast/ClassNode;[Ljava/lang/Class;)Z

    move-result p1

    return p1
.end method

.method public varargs firstArgTypesMatches([Lorg/codehaus/groovy/ast/ClassNode;[Ljava/lang/Class;)Z
    .locals 4

    const/4 v0, 0x0

    const/4 v1, 0x1

    if-nez p2, :cond_2

    if-eqz p1, :cond_0

    .line 223
    array-length p1, p1

    if-nez p1, :cond_1

    :cond_0
    move v0, v1

    :cond_1
    return v0

    .line 224
    :cond_2
    array-length v2, p1

    array-length v3, p2

    if-ge v2, v3, :cond_3

    return v0

    .line 226
    :cond_3
    :goto_0
    array-length v2, p2

    if-ge v0, v2, :cond_4

    if-eqz v1, :cond_4

    .line 227
    aget-object v1, p1, v0

    aget-object v2, p2, v0

    invoke-static {v1, v2}, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->matchWithOrWithourBoxing(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/Class;)Z

    move-result v1

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_4
    return v1
.end method

.method public getArguments(Lorg/codehaus/groovy/ast/expr/MethodCall;)Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;
    .locals 0

    .line 178
    invoke-interface {p1}, Lorg/codehaus/groovy/ast/expr/MethodCall;->getArguments()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/classgen/asm/InvocationWriter;->makeArgumentList(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    move-result-object p1

    return-object p1
.end method

.method public getCurrentScope()Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension$TypeCheckingScope;
    .locals 1

    .line 95
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->scopeData:Ljava/util/LinkedList;

    invoke-virtual {v0}, Ljava/util/LinkedList;->peek()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension$TypeCheckingScope;

    return-object v0
.end method

.method public getEnclosingBinaryExpression()Lorg/codehaus/groovy/ast/expr/BinaryExpression;
    .locals 1

    .line 346
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->context:Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;

    invoke-virtual {v0}, Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;->getEnclosingBinaryExpression()Lorg/codehaus/groovy/ast/expr/BinaryExpression;

    move-result-object v0

    return-object v0
.end method

.method public getEnclosingBinaryExpressionStack()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/expr/BinaryExpression;",
            ">;"
        }
    .end annotation

    .line 414
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->context:Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;

    invoke-virtual {v0}, Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;->getEnclosingBinaryExpressionStack()Ljava/util/List;

    move-result-object v0

    return-object v0
.end method

.method public getEnclosingClassNode()Lorg/codehaus/groovy/ast/ClassNode;
    .locals 1

    .line 370
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->context:Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;

    invoke-virtual {v0}, Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;->getEnclosingClassNode()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    return-object v0
.end method

.method public getEnclosingClassNodes()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            ">;"
        }
    .end annotation

    .line 394
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->context:Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;

    invoke-virtual {v0}, Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;->getEnclosingClassNodes()Ljava/util/List;

    move-result-object v0

    return-object v0
.end method

.method public getEnclosingClosure()Lorg/codehaus/groovy/transform/stc/TypeCheckingContext$EnclosingClosure;
    .locals 1

    .line 418
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->context:Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;

    invoke-virtual {v0}, Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;->getEnclosingClosure()Lorg/codehaus/groovy/transform/stc/TypeCheckingContext$EnclosingClosure;

    move-result-object v0

    return-object v0
.end method

.method public getEnclosingClosureStack()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/transform/stc/TypeCheckingContext$EnclosingClosure;",
            ">;"
        }
    .end annotation

    .line 398
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->context:Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;

    invoke-virtual {v0}, Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;->getEnclosingClosureStack()Ljava/util/List;

    move-result-object v0

    return-object v0
.end method

.method public getEnclosingMethod()Lorg/codehaus/groovy/ast/MethodNode;
    .locals 1

    .line 378
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->context:Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;

    invoke-virtual {v0}, Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;->getEnclosingMethod()Lorg/codehaus/groovy/ast/MethodNode;

    move-result-object v0

    return-object v0
.end method

.method public getEnclosingMethodCall()Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 1

    .line 358
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->context:Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;

    invoke-virtual {v0}, Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;->getEnclosingMethodCall()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    return-object v0
.end method

.method public getEnclosingMethodCalls()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/expr/Expression;",
            ">;"
        }
    .end annotation

    .line 422
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->context:Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;

    invoke-virtual {v0}, Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;->getEnclosingMethodCalls()Ljava/util/List;

    move-result-object v0

    return-object v0
.end method

.method public getEnclosingMethods()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/MethodNode;",
            ">;"
        }
    .end annotation

    .line 374
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->context:Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;

    invoke-virtual {v0}, Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;->getEnclosingMethods()Ljava/util/List;

    move-result-object v0

    return-object v0
.end method

.method public getGeneratedMethods()Ljava/util/Set;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Set<",
            "Lorg/codehaus/groovy/ast/MethodNode;",
            ">;"
        }
    .end annotation

    .line 410
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->generatedMethods:Ljava/util/Set;

    return-object v0
.end method

.method public isAnnotatedBy(Lorg/codehaus/groovy/ast/ASTNode;Ljava/lang/Class;)Z
    .locals 0

    .line 162
    invoke-static {p2}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p2

    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->isAnnotatedBy(Lorg/codehaus/groovy/ast/ASTNode;Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result p1

    return p1
.end method

.method public isAnnotatedBy(Lorg/codehaus/groovy/ast/ASTNode;Lorg/codehaus/groovy/ast/ClassNode;)Z
    .locals 1

    .line 166
    instance-of v0, p1, Lorg/codehaus/groovy/ast/AnnotatedNode;

    if-eqz v0, :cond_0

    check-cast p1, Lorg/codehaus/groovy/ast/AnnotatedNode;

    invoke-virtual {p1, p2}, Lorg/codehaus/groovy/ast/AnnotatedNode;->getAnnotations(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/util/List;

    move-result-object p1

    invoke-interface {p1}, Ljava/util/List;->isEmpty()Z

    move-result p1

    if-nez p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public isDynamic(Lorg/codehaus/groovy/ast/expr/VariableExpression;)Z
    .locals 0

    .line 170
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->getAccessedVariable()Lorg/codehaus/groovy/ast/Variable;

    move-result-object p1

    instance-of p1, p1, Lorg/codehaus/groovy/ast/DynamicVariable;

    return p1
.end method

.method public isExtensionMethod(Lorg/codehaus/groovy/ast/MethodNode;)Z
    .locals 0

    .line 174
    instance-of p1, p1, Lorg/codehaus/groovy/transform/stc/ExtensionMethodNode;

    return p1
.end method

.method public isGenerated(Lorg/codehaus/groovy/ast/MethodNode;)Z
    .locals 1

    .line 106
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->generatedMethods:Ljava/util/Set;

    invoke-interface {v0, p1}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result p1

    return p1
.end method

.method public isMethodCall(Ljava/lang/Object;)Z
    .locals 0

    .line 191
    instance-of p1, p1, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    return p1
.end method

.method public log(Ljava/lang/String;)V
    .locals 1

    .line 342
    sget-object v0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->LOG:Ljava/util/logging/Logger;

    invoke-virtual {v0, p1}, Ljava/util/logging/Logger;->info(Ljava/lang/String;)V

    return-void
.end method

.method public makeDynamic(Lorg/codehaus/groovy/ast/expr/MethodCall;)Lorg/codehaus/groovy/ast/MethodNode;
    .locals 1

    .line 265
    sget-object v0, Lorg/codehaus/groovy/ast/ClassHelper;->OBJECT_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {p0, p1, v0}, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->makeDynamic(Lorg/codehaus/groovy/ast/expr/MethodCall;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/MethodNode;

    move-result-object p1

    return-object p1
.end method

.method public makeDynamic(Lorg/codehaus/groovy/ast/expr/MethodCall;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/MethodNode;
    .locals 9

    .line 276
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->context:Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;

    invoke-virtual {v0}, Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;->getEnclosingClosure()Lorg/codehaus/groovy/transform/stc/TypeCheckingContext$EnclosingClosure;

    move-result-object v0

    .line 277
    iget-object v1, p0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->context:Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;

    invoke-virtual {v1}, Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;->getEnclosingMethod()Lorg/codehaus/groovy/ast/MethodNode;

    move-result-object v1

    .line 278
    move-object v2, p1

    check-cast v2, Lorg/codehaus/groovy/ast/ASTNode;

    sget-object v3, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->DYNAMIC_RESOLUTION:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    invoke-virtual {v2, v3, p2}, Lorg/codehaus/groovy/ast/ASTNode;->putNodeMetaData(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    if-eqz v0, :cond_0

    .line 280
    invoke-virtual {v0}, Lorg/codehaus/groovy/transform/stc/TypeCheckingContext$EnclosingClosure;->getClosureExpression()Lorg/codehaus/groovy/ast/expr/ClosureExpression;

    move-result-object v0

    sget-object v1, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->DYNAMIC_RESOLUTION:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    sget-object v2, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    invoke-virtual {v0, v1, v2}, Lorg/codehaus/groovy/ast/expr/ClosureExpression;->putNodeMetaData(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    .line 282
    :cond_0
    sget-object v0, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->DYNAMIC_RESOLUTION:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    sget-object v2, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    invoke-virtual {v1, v0, v2}, Lorg/codehaus/groovy/ast/MethodNode;->putNodeMetaData(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :goto_0
    const/4 v0, 0x1

    .line 284
    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->setHandled(Z)V

    .line 285
    iget-boolean v0, p0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->debug:Z

    if-eqz v0, :cond_1

    .line 286
    sget-object v0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->LOG:Ljava/util/logging/Logger;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "Turning "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-interface {p1}, Lorg/codehaus/groovy/ast/expr/MethodCall;->getText()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v2, " into a dynamic method call returning "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    const/4 v2, 0x0

    invoke-virtual {p2, v2}, Lorg/codehaus/groovy/ast/ClassNode;->toString(Z)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/util/logging/Logger;->info(Ljava/lang/String;)V

    .line 288
    :cond_1
    new-instance v0, Lorg/codehaus/groovy/ast/MethodNode;

    invoke-interface {p1}, Lorg/codehaus/groovy/ast/expr/MethodCall;->getMethodAsString()Ljava/lang/String;

    move-result-object v3

    const/4 v4, 0x0

    sget-object v6, Lorg/codehaus/groovy/ast/Parameter;->EMPTY_ARRAY:[Lorg/codehaus/groovy/ast/Parameter;

    sget-object v7, Lorg/codehaus/groovy/ast/ClassNode;->EMPTY_ARRAY:[Lorg/codehaus/groovy/ast/ClassNode;

    sget-object v8, Lorg/codehaus/groovy/ast/stmt/EmptyStatement;->INSTANCE:Lorg/codehaus/groovy/ast/stmt/EmptyStatement;

    move-object v2, v0

    move-object v5, p2

    invoke-direct/range {v2 .. v8}, Lorg/codehaus/groovy/ast/MethodNode;-><init>(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)V

    return-object v0
.end method

.method public makeDynamic(Lorg/codehaus/groovy/ast/expr/PropertyExpression;)V
    .locals 1

    .line 297
    sget-object v0, Lorg/codehaus/groovy/ast/ClassHelper;->OBJECT_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {p0, p1, v0}, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->makeDynamic(Lorg/codehaus/groovy/ast/expr/PropertyExpression;Lorg/codehaus/groovy/ast/ClassNode;)V

    return-void
.end method

.method public makeDynamic(Lorg/codehaus/groovy/ast/expr/PropertyExpression;Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 3

    .line 307
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->context:Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;

    invoke-virtual {v0}, Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;->getEnclosingMethod()Lorg/codehaus/groovy/ast/MethodNode;

    move-result-object v0

    sget-object v1, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->DYNAMIC_RESOLUTION:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    sget-object v2, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    invoke-virtual {v0, v1, v2}, Lorg/codehaus/groovy/ast/MethodNode;->putNodeMetaData(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 308
    sget-object v0, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->DYNAMIC_RESOLUTION:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    invoke-virtual {p1, v0, p2}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->putNodeMetaData(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 309
    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->storeType(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/ClassNode;)V

    const/4 v0, 0x1

    .line 310
    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->setHandled(Z)V

    .line 311
    iget-boolean v0, p0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->debug:Z

    if-eqz v0, :cond_0

    .line 312
    sget-object v0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->LOG:Ljava/util/logging/Logger;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "Turning \'"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->getText()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v1, "\' into a dynamic property access of type "

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const/4 v1, 0x0

    invoke-virtual {p2, v1}, Lorg/codehaus/groovy/ast/ClassNode;->toString(Z)Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/util/logging/Logger;->info(Ljava/lang/String;)V

    :cond_0
    return-void
.end method

.method public makeDynamic(Lorg/codehaus/groovy/ast/expr/VariableExpression;)V
    .locals 1

    .line 322
    sget-object v0, Lorg/codehaus/groovy/ast/ClassHelper;->OBJECT_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {p0, p1, v0}, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->makeDynamic(Lorg/codehaus/groovy/ast/expr/VariableExpression;Lorg/codehaus/groovy/ast/ClassNode;)V

    return-void
.end method

.method public makeDynamic(Lorg/codehaus/groovy/ast/expr/VariableExpression;Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 3

    .line 332
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->context:Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;

    invoke-virtual {v0}, Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;->getEnclosingMethod()Lorg/codehaus/groovy/ast/MethodNode;

    move-result-object v0

    sget-object v1, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->DYNAMIC_RESOLUTION:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    sget-object v2, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    invoke-virtual {v0, v1, v2}, Lorg/codehaus/groovy/ast/MethodNode;->putNodeMetaData(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 333
    sget-object v0, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->DYNAMIC_RESOLUTION:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    invoke-virtual {p1, v0, p2}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->putNodeMetaData(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 334
    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->storeType(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/ClassNode;)V

    const/4 v0, 0x1

    .line 335
    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->setHandled(Z)V

    .line 336
    iget-boolean v0, p0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->debug:Z

    if-eqz v0, :cond_0

    .line 337
    sget-object v0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->LOG:Ljava/util/logging/Logger;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "Turning \'"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->getText()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v1, "\' into a dynamic variable access of type "

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const/4 v1, 0x0

    invoke-virtual {p2, v1}, Lorg/codehaus/groovy/ast/ClassNode;->toString(Z)Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/util/logging/Logger;->info(Ljava/lang/String;)V

    :cond_0
    return-void
.end method

.method public newMethod(Ljava/lang/String;Ljava/lang/Class;)Lorg/codehaus/groovy/ast/MethodNode;
    .locals 0

    .line 114
    invoke-static {p2}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p2

    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->newMethod(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/MethodNode;

    move-result-object p1

    return-object p1
.end method

.method public newMethod(Ljava/lang/String;Ljava/util/concurrent/Callable;)Lorg/codehaus/groovy/ast/MethodNode;
    .locals 10
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/util/concurrent/Callable<",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            ">;)",
            "Lorg/codehaus/groovy/ast/MethodNode;"
        }
    .end annotation

    .line 130
    new-instance v9, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension$1;

    sget-object v4, Lorg/codehaus/groovy/ast/ClassHelper;->OBJECT_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    sget-object v5, Lorg/codehaus/groovy/ast/Parameter;->EMPTY_ARRAY:[Lorg/codehaus/groovy/ast/Parameter;

    sget-object v6, Lorg/codehaus/groovy/ast/ClassNode;->EMPTY_ARRAY:[Lorg/codehaus/groovy/ast/ClassNode;

    sget-object v7, Lorg/codehaus/groovy/ast/stmt/EmptyStatement;->INSTANCE:Lorg/codehaus/groovy/ast/stmt/EmptyStatement;

    const/4 v3, 0x1

    move-object v0, v9

    move-object v1, p0

    move-object v2, p1

    move-object v8, p2

    invoke-direct/range {v0 .. v8}, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension$1;-><init>(Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;Ljava/util/concurrent/Callable;)V

    .line 145
    iget-object p1, p0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->generatedMethods:Ljava/util/Set;

    invoke-interface {p1, v9}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    return-object v9
.end method

.method public newMethod(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/MethodNode;
    .locals 8

    .line 118
    new-instance v7, Lorg/codehaus/groovy/ast/MethodNode;

    sget-object v4, Lorg/codehaus/groovy/ast/Parameter;->EMPTY_ARRAY:[Lorg/codehaus/groovy/ast/Parameter;

    sget-object v5, Lorg/codehaus/groovy/ast/ClassNode;->EMPTY_ARRAY:[Lorg/codehaus/groovy/ast/ClassNode;

    sget-object v6, Lorg/codehaus/groovy/ast/stmt/EmptyStatement;->INSTANCE:Lorg/codehaus/groovy/ast/stmt/EmptyStatement;

    const/4 v2, 0x1

    move-object v0, v7

    move-object v1, p1

    move-object v3, p2

    invoke-direct/range {v0 .. v6}, Lorg/codehaus/groovy/ast/MethodNode;-><init>(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 124
    iget-object p1, p0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->generatedMethods:Ljava/util/Set;

    invoke-interface {p1, v7}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    return-object v7
.end method

.method public newScope()Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension$TypeCheckingScope;
    .locals 3

    .line 78
    new-instance v0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension$TypeCheckingScope;

    iget-object v1, p0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->scopeData:Ljava/util/LinkedList;

    invoke-virtual {v1}, Ljava/util/LinkedList;->peek()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension$TypeCheckingScope;

    const/4 v2, 0x0

    invoke-direct {v0, v1, v2}, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension$TypeCheckingScope;-><init>(Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension$TypeCheckingScope;Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension$1;)V

    .line 79
    iget-object v1, p0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->scopeData:Ljava/util/LinkedList;

    invoke-virtual {v1, v0}, Ljava/util/LinkedList;->addFirst(Ljava/lang/Object;)V

    return-object v0
.end method

.method public newScope(Lgroovy/lang/Closure;)Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension$TypeCheckingScope;
    .locals 1

    .line 84
    invoke-virtual {p0}, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->newScope()Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension$TypeCheckingScope;

    move-result-object v0

    .line 85
    invoke-virtual {p1, v0, p0, p0}, Lgroovy/lang/Closure;->rehydrate(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Lgroovy/lang/Closure;

    move-result-object p1

    .line 86
    invoke-virtual {p1}, Lgroovy/lang/Closure;->call()Ljava/lang/Object;

    return-object v0
.end method

.method public popEnclosingBinaryExpression()Lorg/codehaus/groovy/ast/expr/BinaryExpression;
    .locals 1

    .line 390
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->context:Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;

    invoke-virtual {v0}, Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;->popEnclosingBinaryExpression()Lorg/codehaus/groovy/ast/expr/BinaryExpression;

    move-result-object v0

    return-object v0
.end method

.method public popEnclosingClassNode()Lorg/codehaus/groovy/ast/ClassNode;
    .locals 1

    .line 402
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->context:Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;

    invoke-virtual {v0}, Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;->popEnclosingClassNode()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    return-object v0
.end method

.method public popEnclosingClosure()Lorg/codehaus/groovy/transform/stc/TypeCheckingContext$EnclosingClosure;
    .locals 1

    .line 430
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->context:Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;

    invoke-virtual {v0}, Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;->popEnclosingClosure()Lorg/codehaus/groovy/transform/stc/TypeCheckingContext$EnclosingClosure;

    move-result-object v0

    return-object v0
.end method

.method public popEnclosingMethod()Lorg/codehaus/groovy/ast/MethodNode;
    .locals 1

    .line 366
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->context:Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;

    invoke-virtual {v0}, Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;->popEnclosingMethod()Lorg/codehaus/groovy/ast/MethodNode;

    move-result-object v0

    return-object v0
.end method

.method public popEnclosingMethodCall()Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 1

    .line 362
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->context:Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;

    invoke-virtual {v0}, Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;->popEnclosingMethodCall()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    return-object v0
.end method

.method public popTemporaryTypeInfo()V
    .locals 1

    .line 382
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->context:Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;

    invoke-virtual {v0}, Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;->popTemporaryTypeInfo()V

    return-void
.end method

.method public pushEnclosingBinaryExpression(Lorg/codehaus/groovy/ast/expr/BinaryExpression;)V
    .locals 1

    .line 350
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->context:Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;

    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;->pushEnclosingBinaryExpression(Lorg/codehaus/groovy/ast/expr/BinaryExpression;)V

    return-void
.end method

.method public pushEnclosingClassNode(Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 1

    .line 386
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->context:Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;

    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;->pushEnclosingClassNode(Lorg/codehaus/groovy/ast/ClassNode;)V

    return-void
.end method

.method public pushEnclosingClosureExpression(Lorg/codehaus/groovy/ast/expr/ClosureExpression;)V
    .locals 1

    .line 354
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->context:Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;

    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;->pushEnclosingClosureExpression(Lorg/codehaus/groovy/ast/expr/ClosureExpression;)V

    return-void
.end method

.method public pushEnclosingMethod(Lorg/codehaus/groovy/ast/MethodNode;)V
    .locals 1

    .line 406
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->context:Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;

    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;->pushEnclosingMethod(Lorg/codehaus/groovy/ast/MethodNode;)V

    return-void
.end method

.method public pushEnclosingMethodCall(Lorg/codehaus/groovy/ast/expr/Expression;)V
    .locals 1

    .line 426
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->context:Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;

    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;->pushEnclosingMethodCall(Lorg/codehaus/groovy/ast/expr/Expression;)V

    return-void
.end method

.method public pushTemporaryTypeInfo()V
    .locals 1

    .line 434
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->context:Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;

    invoke-virtual {v0}, Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;->pushTemporaryTypeInfo()V

    return-void
.end method

.method protected varargs safeCall(Lgroovy/lang/Closure;[Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 183
    :try_start_0
    invoke-virtual {p1, p2}, Lgroovy/lang/Closure;->call([Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return-object p1

    :catch_0
    move-exception p1

    .line 185
    iget-object p2, p0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->typeCheckingVisitor:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;

    invoke-virtual {p2}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->getSourceUnit()Lorg/codehaus/groovy/control/SourceUnit;

    move-result-object p2

    invoke-virtual {p2, p1}, Lorg/codehaus/groovy/control/SourceUnit;->addException(Ljava/lang/Exception;)V

    const/4 p1, 0x0

    return-object p1
.end method

.method public scopeExit()Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension$TypeCheckingScope;
    .locals 1

    .line 91
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->scopeData:Ljava/util/LinkedList;

    invoke-virtual {v0}, Ljava/util/LinkedList;->removeFirst()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension$TypeCheckingScope;

    return-object v0
.end method

.method public scopeExit(Lgroovy/lang/Closure;)Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension$TypeCheckingScope;
    .locals 1

    .line 99
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->scopeData:Ljava/util/LinkedList;

    invoke-virtual {v0}, Ljava/util/LinkedList;->peek()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension$TypeCheckingScope;

    .line 100
    invoke-virtual {p1, v0, p0, p0}, Lgroovy/lang/Closure;->rehydrate(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Lgroovy/lang/Closure;

    move-result-object p1

    .line 101
    invoke-virtual {p1}, Lgroovy/lang/Closure;->call()Ljava/lang/Object;

    .line 102
    invoke-virtual {p0}, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->scopeExit()Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension$TypeCheckingScope;

    move-result-object p1

    return-object p1
.end method

.method public setHandled(Z)V
    .locals 0

    .line 74
    iput-boolean p1, p0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->handled:Z

    return-void
.end method

.method public unique(Lorg/codehaus/groovy/ast/MethodNode;)Ljava/util/List;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/MethodNode;",
            ")",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/MethodNode;",
            ">;"
        }
    .end annotation

    .line 110
    invoke-static {p1}, Ljava/util/Collections;->singletonList(Ljava/lang/Object;)Ljava/util/List;

    move-result-object p1

    return-object p1
.end method

.method public withTypeChecker(Lgroovy/lang/Closure;)Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<R:",
            "Ljava/lang/Object;",
            ">(",
            "Lgroovy/lang/Closure<",
            "TR;>;)TR;"
        }
    .end annotation

    .line 251
    invoke-virtual {p1}, Lgroovy/lang/Closure;->clone()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovy/lang/Closure;

    .line 252
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;->typeCheckingVisitor:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;

    invoke-virtual {p1, v0}, Lgroovy/lang/Closure;->setDelegate(Ljava/lang/Object;)V

    const/4 v0, 0x1

    .line 253
    invoke-virtual {p1, v0}, Lgroovy/lang/Closure;->setResolveStrategy(I)V

    .line 254
    invoke-virtual {p1}, Lgroovy/lang/Closure;->call()Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method
