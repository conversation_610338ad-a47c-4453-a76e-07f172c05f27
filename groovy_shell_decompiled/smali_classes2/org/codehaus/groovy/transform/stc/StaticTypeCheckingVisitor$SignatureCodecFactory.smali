.class public Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$SignatureCodecFactory;
.super Ljava/lang/Object;
.source "StaticTypeCheckingVisitor.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "SignatureCodecFactory"
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 5954
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static getCodec(ILjava/lang/ClassLoader;)Lorg/codehaus/groovy/transform/stc/SignatureCodec;
    .locals 1

    const/4 v0, 0x1

    if-eq p0, v0, :cond_0

    const/4 p0, 0x0

    return-object p0

    .line 5958
    :cond_0
    new-instance p0, Lorg/codehaus/groovy/transform/stc/SignatureCodecVersion1;

    invoke-direct {p0, p1}, Lorg/codehaus/groovy/transform/stc/SignatureCodecVersion1;-><init>(Ljava/lang/ClassLoader;)V

    return-object p0
.end method
