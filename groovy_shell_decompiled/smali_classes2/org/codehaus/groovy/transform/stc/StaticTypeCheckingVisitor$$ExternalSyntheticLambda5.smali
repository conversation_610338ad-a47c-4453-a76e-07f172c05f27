.class public final synthetic Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda5;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Consumer;


# instance fields
.field public final synthetic f$0:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;

.field public final synthetic f$1:Lorg/codehaus/groovy/ast/expr/MethodPointerExpression;


# direct methods
.method public synthetic constructor <init>(Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;Lorg/codehaus/groovy/ast/expr/MethodPointerExpression;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda5;->f$0:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;

    iput-object p2, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda5;->f$1:Lorg/codehaus/groovy/ast/expr/MethodPointerExpression;

    return-void
.end method


# virtual methods
.method public final accept(Ljava/lang/Object;)V
    .locals 2

    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda5;->f$0:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;

    iget-object v1, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda5;->f$1:Lorg/codehaus/groovy/ast/expr/MethodPointerExpression;

    check-cast p1, Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v0, v1, p1}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->lambda$visitMethodPointerExpression$18$org-codehaus-groovy-transform-stc-StaticTypeCheckingVisitor(Lorg/codehaus/groovy/ast/expr/MethodPointerExpression;Lorg/codehaus/groovy/ast/ClassNode;)V

    return-void
.end method
