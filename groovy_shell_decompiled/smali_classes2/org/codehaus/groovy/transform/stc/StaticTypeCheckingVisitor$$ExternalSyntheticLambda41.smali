.class public final synthetic Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda41;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Predicate;


# instance fields
.field public final synthetic f$0:Lorg/codehaus/groovy/ast/expr/MethodPointerExpression;

.field public final synthetic f$1:[Lorg/codehaus/groovy/ast/ClassNode;


# direct methods
.method public synthetic constructor <init>(Lorg/codehaus/groovy/ast/expr/MethodPointerExpression;[Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda41;->f$0:Lorg/codehaus/groovy/ast/expr/MethodPointerExpression;

    iput-object p2, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda41;->f$1:[Lorg/codehaus/groovy/ast/ClassNode;

    return-void
.end method


# virtual methods
.method public final test(Ljava/lang/Object;)Z
    .locals 2

    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda41;->f$0:Lorg/codehaus/groovy/ast/expr/MethodPointerExpression;

    iget-object v1, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda41;->f$1:[Lorg/codehaus/groovy/ast/ClassNode;

    check-cast p1, Lorg/codehaus/groovy/ast/MethodNode;

    invoke-static {v0, v1, p1}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->lambda$chooseMethod$39(Lorg/codehaus/groovy/ast/expr/MethodPointerExpression;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/MethodNode;)Z

    move-result p1

    return p1
.end method
