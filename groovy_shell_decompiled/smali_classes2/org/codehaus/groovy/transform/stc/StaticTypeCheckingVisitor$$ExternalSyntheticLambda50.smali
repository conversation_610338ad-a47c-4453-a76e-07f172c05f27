.class public final synthetic Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda50;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Supplier;


# instance fields
.field public final synthetic f$0:Ljava/util/Map;

.field public final synthetic f$1:[Lorg/codehaus/groovy/ast/Parameter;


# direct methods
.method public synthetic constructor <init>(Ljava/util/Map;[Lorg/codehaus/groovy/ast/Parameter;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda50;->f$0:Ljava/util/Map;

    iput-object p2, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda50;->f$1:[Lorg/codehaus/groovy/ast/Parameter;

    return-void
.end method


# virtual methods
.method public final get()Ljava/lang/Object;
    .locals 2

    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda50;->f$0:Ljava/util/Map;

    iget-object v1, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda50;->f$1:[Lorg/codehaus/groovy/ast/Parameter;

    invoke-static {v0, v1}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->lambda$convertClosureTypeToSAMType$40(Ljava/util/Map;[Lorg/codehaus/groovy/ast/Parameter;)[Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    return-object v0
.end method
