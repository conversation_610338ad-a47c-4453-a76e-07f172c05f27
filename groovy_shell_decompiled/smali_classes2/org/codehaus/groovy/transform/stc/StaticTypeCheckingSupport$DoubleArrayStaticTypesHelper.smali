.class public Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingSupport$DoubleArrayStaticTypesHelper;
.super Ljava/lang/Object;
.source "StaticTypeCheckingSupport.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingSupport;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "DoubleArrayStaticTypesHelper"
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 2382
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static getAt([DI)Ljava/lang/Double;
    .locals 2

    if-eqz p0, :cond_0

    .line 2384
    aget-wide v0, p0, p1

    invoke-static {v0, v1}, Ljava/lang/Double;->valueOf(D)Ljava/lang/Double;

    move-result-object p0

    goto :goto_0

    :cond_0
    const/4 p0, 0x0

    :goto_0
    return-object p0
.end method

.method public static putAt([DID)V
    .locals 0

    if-eqz p0, :cond_0

    .line 2388
    aput-wide p2, p0, p1

    :cond_0
    return-void
.end method
