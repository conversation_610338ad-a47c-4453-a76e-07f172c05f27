.class public Lorg/codehaus/groovy/transform/stc/Receiver;
.super Ljava/lang/Object;
.source "Receiver.java"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;"
    }
.end annotation


# instance fields
.field private final data:Ljava/lang/Object;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "TT;"
        }
    .end annotation
.end field

.field private final type:Lorg/codehaus/groovy/ast/ClassNode;


# direct methods
.method public constructor <init>(Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 0

    .line 32
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 33
    iput-object p1, p0, Lorg/codehaus/groovy/transform/stc/Receiver;->type:Lorg/codehaus/groovy/ast/ClassNode;

    const/4 p1, 0x0

    .line 34
    iput-object p1, p0, Lorg/codehaus/groovy/transform/stc/Receiver;->data:Ljava/lang/Object;

    return-void
.end method

.method public constructor <init>(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/Object;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            "TT;)V"
        }
    .end annotation

    .line 37
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 38
    iput-object p2, p0, Lorg/codehaus/groovy/transform/stc/Receiver;->data:Ljava/lang/Object;

    .line 39
    iput-object p1, p0, Lorg/codehaus/groovy/transform/stc/Receiver;->type:Lorg/codehaus/groovy/ast/ClassNode;

    return-void
.end method

.method public static make(Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/transform/stc/Receiver;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            ")",
            "Lorg/codehaus/groovy/transform/stc/Receiver<",
            "TT;>;"
        }
    .end annotation

    .line 29
    new-instance v0, Lorg/codehaus/groovy/transform/stc/Receiver;

    if-nez p0, :cond_0

    sget-object p0, Lorg/codehaus/groovy/ast/ClassHelper;->OBJECT_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    :cond_0
    invoke-direct {v0, p0}, Lorg/codehaus/groovy/transform/stc/Receiver;-><init>(Lorg/codehaus/groovy/ast/ClassNode;)V

    return-object v0
.end method


# virtual methods
.method public getData()Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()TT;"
        }
    .end annotation

    .line 43
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/Receiver;->data:Ljava/lang/Object;

    return-object v0
.end method

.method public getType()Lorg/codehaus/groovy/ast/ClassNode;
    .locals 1

    .line 47
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/Receiver;->type:Lorg/codehaus/groovy/ast/ClassNode;

    return-object v0
.end method

.method public toString()Ljava/lang/String;
    .locals 2

    .line 52
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Receiver{type="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v1, p0, Lorg/codehaus/groovy/transform/stc/Receiver;->type:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ", data="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v1, p0, Lorg/codehaus/groovy/transform/stc/Receiver;->data:Ljava/lang/Object;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    const/16 v1, 0x7d

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
