.class public abstract Lorg/codehaus/groovy/transform/stc/AbstractExtensionMethodCache;
.super Ljava/lang/Object;
.source "AbstractExtensionMethodCache.java"


# instance fields
.field final cache:Lorg/codehaus/groovy/runtime/memoize/EvictableCache;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lorg/codehaus/groovy/runtime/memoize/EvictableCache<",
            "Ljava/lang/ClassLoader;",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/MethodNode;",
            ">;>;>;"
        }
    .end annotation
.end field


# direct methods
.method public static synthetic $r8$lambda$yZSxQiRj3m2ug9_Yr890CBLc5bQ(Lorg/codehaus/groovy/transform/stc/AbstractExtensionMethodCache;Ljava/lang/ClassLoader;)Ljava/util/Map;
    .locals 0

    invoke-direct {p0, p1}, Lorg/codehaus/groovy/transform/stc/AbstractExtensionMethodCache;->getMethodsFromClassLoader(Ljava/lang/ClassLoader;)Ljava/util/Map;

    move-result-object p0

    return-object p0
.end method

.method public constructor <init>()V
    .locals 2

    .line 47
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 48
    new-instance v0, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;

    new-instance v1, Ljava/util/WeakHashMap;

    invoke-direct {v1}, Ljava/util/WeakHashMap;-><init>()V

    invoke-direct {v0, v1}, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;-><init>(Ljava/util/Map;)V

    iput-object v0, p0, Lorg/codehaus/groovy/transform/stc/AbstractExtensionMethodCache;->cache:Lorg/codehaus/groovy/runtime/memoize/EvictableCache;

    return-void
.end method

.method private accumulate(Ljava/util/Map;ZLorg/codehaus/groovy/ast/MethodNode;Ljava/util/function/Function;)V
    .locals 13
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/MethodNode;",
            ">;>;Z",
            "Lorg/codehaus/groovy/ast/MethodNode;",
            "Ljava/util/function/Function<",
            "Lorg/codehaus/groovy/ast/MethodNode;",
            "Ljava/lang/String;",
            ">;)V"
        }
    .end annotation

    .line 131
    invoke-virtual/range {p3 .. p3}, Lorg/codehaus/groovy/ast/MethodNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v0

    .line 132
    array-length v1, v0

    const/4 v2, 0x1

    sub-int/2addr v1, v2

    new-array v8, v1, [Lorg/codehaus/groovy/ast/Parameter;

    const/4 v12, 0x0

    .line 133
    invoke-static {v0, v2, v8, v12, v1}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    .line 134
    new-instance v1, Lorg/codehaus/groovy/transform/stc/ExtensionMethodNode;

    .line 136
    invoke-virtual/range {p3 .. p3}, Lorg/codehaus/groovy/ast/MethodNode;->getName()Ljava/lang/String;

    move-result-object v5

    .line 137
    invoke-virtual/range {p3 .. p3}, Lorg/codehaus/groovy/ast/MethodNode;->getModifiers()I

    move-result v6

    .line 138
    invoke-virtual/range {p3 .. p3}, Lorg/codehaus/groovy/ast/MethodNode;->getReturnType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v7

    sget-object v9, Lorg/codehaus/groovy/ast/ClassNode;->EMPTY_ARRAY:[Lorg/codehaus/groovy/ast/ClassNode;

    const/4 v10, 0x0

    move-object v3, v1

    move-object/from16 v4, p3

    move v11, p2

    invoke-direct/range {v3 .. v11}, Lorg/codehaus/groovy/transform/stc/ExtensionMethodNode;-><init>(Lorg/codehaus/groovy/ast/MethodNode;Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;Z)V

    .line 142
    invoke-virtual/range {p3 .. p3}, Lorg/codehaus/groovy/ast/MethodNode;->getGenericsTypes()[Lorg/codehaus/groovy/ast/GenericsType;

    move-result-object v2

    invoke-virtual {v1, v2}, Lorg/codehaus/groovy/transform/stc/ExtensionMethodNode;->setGenericsTypes([Lorg/codehaus/groovy/ast/GenericsType;)V

    .line 143
    aget-object v0, v0, v12

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/Parameter;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    .line 144
    invoke-virtual {v1, v0}, Lorg/codehaus/groovy/transform/stc/ExtensionMethodNode;->setDeclaringClass(Lorg/codehaus/groovy/ast/ClassNode;)V

    move-object/from16 v0, p3

    move-object/from16 v2, p4

    .line 146
    invoke-interface {v2, v0}, Ljava/util/function/Function;->apply(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/String;

    .line 148
    sget-object v2, Lorg/codehaus/groovy/transform/stc/AbstractExtensionMethodCache$$ExternalSyntheticLambda1;->INSTANCE:Lorg/codehaus/groovy/transform/stc/AbstractExtensionMethodCache$$ExternalSyntheticLambda1;

    move-object v3, p1

    invoke-interface {p1, v0, v2}, Ljava/util/Map;->computeIfAbsent(Ljava/lang/Object;Ljava/util/function/Function;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/List;

    .line 149
    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method private getMethods(Ljava/util/List;)Ljava/util/Map;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/runtime/m12n/ExtensionModule;",
            ">;)",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/MethodNode;",
            ">;>;"
        }
    .end annotation

    .line 85
    new-instance v0, Ljava/util/LinkedHashSet;

    invoke-direct {v0}, Ljava/util/LinkedHashSet;-><init>()V

    .line 86
    new-instance v1, Ljava/util/LinkedHashSet;

    invoke-direct {v1}, Ljava/util/LinkedHashSet;-><init>()V

    .line 87
    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_0

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lorg/codehaus/groovy/runtime/m12n/ExtensionModule;

    .line 88
    check-cast v2, Lorg/codehaus/groovy/runtime/m12n/MetaInfExtensionModule;

    .line 89
    invoke-virtual {v2}, Lorg/codehaus/groovy/runtime/m12n/MetaInfExtensionModule;->getInstanceMethodsExtensionClasses()Ljava/util/List;

    move-result-object v3

    invoke-interface {v0, v3}, Ljava/util/Set;->addAll(Ljava/util/Collection;)Z

    .line 90
    invoke-virtual {v2}, Lorg/codehaus/groovy/runtime/m12n/MetaInfExtensionModule;->getStaticMethodsExtensionClasses()Ljava/util/List;

    move-result-object v2

    invoke-interface {v1, v2}, Ljava/util/Set;->addAll(Ljava/util/Collection;)Z

    goto :goto_0

    .line 92
    :cond_0
    new-instance p1, Ljava/util/HashMap;

    invoke-direct {p1}, Ljava/util/HashMap;-><init>()V

    .line 94
    invoke-virtual {p0, v0, v1}, Lorg/codehaus/groovy/transform/stc/AbstractExtensionMethodCache;->addAdditionalClassesToScan(Ljava/util/Set;Ljava/util/Set;)V

    const/4 v2, 0x1

    .line 96
    invoke-direct {p0, p1, v1, v2}, Lorg/codehaus/groovy/transform/stc/AbstractExtensionMethodCache;->scan(Ljava/util/Map;Ljava/lang/Iterable;Z)V

    const/4 v1, 0x0

    .line 97
    invoke-direct {p0, p1, v0, v1}, Lorg/codehaus/groovy/transform/stc/AbstractExtensionMethodCache;->scan(Ljava/util/Map;Ljava/lang/Iterable;Z)V

    return-object p1
.end method

.method private getMethodsFromClassLoader(Ljava/lang/ClassLoader;)Ljava/util/Map;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/ClassLoader;",
            ")",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/MethodNode;",
            ">;>;"
        }
    .end annotation

    .line 55
    new-instance v0, Ljava/util/LinkedList;

    invoke-direct {v0}, Ljava/util/LinkedList;-><init>()V

    .line 56
    new-instance v1, Lorg/codehaus/groovy/runtime/m12n/ExtensionModuleScanner;

    new-instance v2, Lorg/codehaus/groovy/transform/stc/AbstractExtensionMethodCache$$ExternalSyntheticLambda2;

    invoke-direct {v2, v0}, Lorg/codehaus/groovy/transform/stc/AbstractExtensionMethodCache$$ExternalSyntheticLambda2;-><init>(Ljava/util/List;)V

    invoke-direct {v1, v2, p1}, Lorg/codehaus/groovy/runtime/m12n/ExtensionModuleScanner;-><init>(Lorg/codehaus/groovy/runtime/m12n/ExtensionModuleScanner$ExtensionModuleListener;Ljava/lang/ClassLoader;)V

    .line 71
    invoke-virtual {v1}, Lorg/codehaus/groovy/runtime/m12n/ExtensionModuleScanner;->scanClasspathModules()V

    .line 73
    invoke-direct {p0, v0}, Lorg/codehaus/groovy/transform/stc/AbstractExtensionMethodCache;->getMethods(Ljava/util/List;)Ljava/util/Map;

    move-result-object p1

    invoke-direct {p0, p1}, Lorg/codehaus/groovy/transform/stc/AbstractExtensionMethodCache;->makeMethodsUnmodifiable(Ljava/util/Map;)Ljava/util/Map;

    move-result-object p1

    return-object p1
.end method

.method static synthetic lambda$accumulate$2(Ljava/lang/String;)Ljava/util/List;
    .locals 0

    .line 148
    new-instance p0, Ljava/util/ArrayList;

    invoke-direct {p0}, Ljava/util/ArrayList;-><init>()V

    return-object p0
.end method

.method static synthetic lambda$getMethodsFromClassLoader$0(Ljava/util/List;Lorg/codehaus/groovy/runtime/m12n/ExtensionModule;)V
    .locals 4

    .line 58
    instance-of v0, p1, Lorg/codehaus/groovy/runtime/m12n/MetaInfExtensionModule;

    if-nez v0, :cond_0

    return-void

    :cond_0
    const/4 v0, 0x0

    .line 61
    invoke-interface {p0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :cond_1
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_2

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lorg/codehaus/groovy/runtime/m12n/ExtensionModule;

    .line 62
    invoke-virtual {v2}, Lorg/codehaus/groovy/runtime/m12n/ExtensionModule;->getName()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {p1}, Lorg/codehaus/groovy/runtime/m12n/ExtensionModule;->getName()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_1

    const/4 v0, 0x1

    :cond_2
    if-nez v0, :cond_3

    .line 67
    invoke-interface {p0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :cond_3
    return-void
.end method

.method static synthetic lambda$makeMethodsUnmodifiable$1(Ljava/lang/String;Ljava/util/List;)Ljava/util/List;
    .locals 0

    .line 103
    invoke-static {p1}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object p0

    return-object p0
.end method

.method private makeMethodsUnmodifiable(Ljava/util/Map;)Ljava/util/Map;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/MethodNode;",
            ">;>;)",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/MethodNode;",
            ">;>;"
        }
    .end annotation

    .line 103
    sget-object v0, Lorg/codehaus/groovy/transform/stc/AbstractExtensionMethodCache$$ExternalSyntheticLambda0;->INSTANCE:Lorg/codehaus/groovy/transform/stc/AbstractExtensionMethodCache$$ExternalSyntheticLambda0;

    invoke-interface {p1, v0}, Ljava/util/Map;->replaceAll(Ljava/util/function/BiFunction;)V

    .line 105
    invoke-static {p1}, Ljava/util/Collections;->unmodifiableMap(Ljava/util/Map;)Ljava/util/Map;

    move-result-object p1

    return-object p1
.end method

.method private scan(Ljava/util/Map;Ljava/lang/Iterable;Z)V
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/MethodNode;",
            ">;>;",
            "Ljava/lang/Iterable<",
            "Ljava/lang/Class;",
            ">;Z)V"
        }
    .end annotation

    .line 111
    invoke-virtual {p0}, Lorg/codehaus/groovy/transform/stc/AbstractExtensionMethodCache;->getMethodFilter()Ljava/util/function/Predicate;

    move-result-object v0

    .line 112
    invoke-virtual {p0}, Lorg/codehaus/groovy/transform/stc/AbstractExtensionMethodCache;->getMethodMapper()Ljava/util/function/Function;

    move-result-object v1

    .line 114
    invoke-interface {p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object p2

    :cond_0
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_4

    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/lang/Class;

    const/4 v3, 0x1

    .line 115
    invoke-static {v2, v3}, Lorg/codehaus/groovy/ast/ClassHelper;->makeWithoutCaching(Ljava/lang/Class;Z)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v2

    .line 116
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/ClassNode;->getMethods()Ljava/util/List;

    move-result-object v2

    invoke-interface {v2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :cond_1
    :goto_0
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_0

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lorg/codehaus/groovy/ast/MethodNode;

    .line 117
    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/MethodNode;->isStatic()Z

    move-result v4

    if-eqz v4, :cond_1

    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/MethodNode;->isPublic()Z

    move-result v4

    if-eqz v4, :cond_1

    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/MethodNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v4

    array-length v4, v4

    if-nez v4, :cond_2

    goto :goto_0

    .line 118
    :cond_2
    invoke-interface {v0, v3}, Ljava/util/function/Predicate;->test(Ljava/lang/Object;)Z

    move-result v4

    if-eqz v4, :cond_3

    goto :goto_0

    .line 120
    :cond_3
    invoke-direct {p0, p1, p3, v3, v1}, Lorg/codehaus/groovy/transform/stc/AbstractExtensionMethodCache;->accumulate(Ljava/util/Map;ZLorg/codehaus/groovy/ast/MethodNode;Ljava/util/function/Function;)V

    goto :goto_0

    :cond_4
    return-void
.end method


# virtual methods
.method protected abstract addAdditionalClassesToScan(Ljava/util/Set;Ljava/util/Set;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Set<",
            "Ljava/lang/Class;",
            ">;",
            "Ljava/util/Set<",
            "Ljava/lang/Class;",
            ">;)V"
        }
    .end annotation
.end method

.method public get(Ljava/lang/ClassLoader;)Ljava/util/Map;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/ClassLoader;",
            ")",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/MethodNode;",
            ">;>;"
        }
    .end annotation

    .line 51
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/AbstractExtensionMethodCache;->cache:Lorg/codehaus/groovy/runtime/memoize/EvictableCache;

    new-instance v1, Lorg/codehaus/groovy/transform/stc/AbstractExtensionMethodCache$$ExternalSyntheticLambda3;

    invoke-direct {v1, p0}, Lorg/codehaus/groovy/transform/stc/AbstractExtensionMethodCache$$ExternalSyntheticLambda3;-><init>(Lorg/codehaus/groovy/transform/stc/AbstractExtensionMethodCache;)V

    invoke-interface {v0, p1, v1}, Lorg/codehaus/groovy/runtime/memoize/EvictableCache;->getAndPut(Ljava/lang/Object;Lorg/codehaus/groovy/runtime/memoize/MemoizeCache$ValueProvider;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/util/Map;

    return-object p1
.end method

.method protected abstract getMethodFilter()Ljava/util/function/Predicate;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/function/Predicate<",
            "Lorg/codehaus/groovy/ast/MethodNode;",
            ">;"
        }
    .end annotation
.end method

.method protected abstract getMethodMapper()Ljava/util/function/Function;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/function/Function<",
            "Lorg/codehaus/groovy/ast/MethodNode;",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end method
