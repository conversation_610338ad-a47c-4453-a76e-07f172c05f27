.class Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$2;
.super Lorg/codehaus/groovy/ast/CodeVisitorSupport;
.source "StaticTypeCheckingVisitor.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->isTypeSource(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/MethodNode;)Z
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic val$expr:Lorg/codehaus/groovy/ast/expr/Expression;

.field final synthetic val$returned:[Z


# direct methods
.method constructor <init>(Lorg/codehaus/groovy/ast/expr/Expression;[Z)V
    .locals 0

    .line 4374
    iput-object p1, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$2;->val$expr:Lorg/codehaus/groovy/ast/expr/Expression;

    iput-object p2, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$2;->val$returned:[Z

    invoke-direct {p0}, Lorg/codehaus/groovy/ast/CodeVisitorSupport;-><init>()V

    return-void
.end method


# virtual methods
.method public visitClosureExpression(Lorg/codehaus/groovy/ast/expr/ClosureExpression;)V
    .locals 0

    return-void
.end method

.method public visitReturnStatement(Lorg/codehaus/groovy/ast/stmt/ReturnStatement;)V
    .locals 2

    .line 4377
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$2;->val$expr:Lorg/codehaus/groovy/ast/expr/Expression;

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/stmt/ReturnStatement;->getExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    invoke-static {v0, p1}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->access$200(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Z

    move-result p1

    if-eqz p1, :cond_0

    .line 4378
    iget-object p1, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$2;->val$returned:[Z

    const/4 v0, 0x0

    const/4 v1, 0x1

    aput-boolean v1, p1, v0

    :cond_0
    return-void
.end method
