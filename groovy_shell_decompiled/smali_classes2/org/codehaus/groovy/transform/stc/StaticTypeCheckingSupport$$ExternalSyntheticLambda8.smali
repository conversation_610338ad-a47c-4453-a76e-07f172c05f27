.class public final synthetic Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingSupport$$ExternalSyntheticLambda8;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Supplier;


# instance fields
.field public final synthetic f$0:Lorg/codehaus/groovy/ast/Parameter;


# direct methods
.method public synthetic constructor <init>(Lorg/codehaus/groovy/ast/Parameter;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingSupport$$ExternalSyntheticLambda8;->f$0:Lorg/codehaus/groovy/ast/Parameter;

    return-void
.end method


# virtual methods
.method public final get()Ljava/lang/Object;
    .locals 1

    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingSupport$$ExternalSyntheticLambda8;->f$0:Lorg/codehaus/groovy/ast/Parameter;

    invoke-static {v0}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingSupport;->lambda$makeRawTypes$2(Lorg/codehaus/groovy/ast/Parameter;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    return-object v0
.end method
