.class public Lorg/codehaus/groovy/transform/stc/SharedVariableCollector;
.super Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;
.source "SharedVariableCollector.java"


# instance fields
.field private final closureSharedExpressions:Ljava/util/Set;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Set<",
            "Lorg/codehaus/groovy/ast/expr/VariableExpression;",
            ">;"
        }
    .end annotation
.end field

.field private final unit:Lorg/codehaus/groovy/control/SourceUnit;

.field private visited:Z


# direct methods
.method public constructor <init>(Lorg/codehaus/groovy/control/SourceUnit;)V
    .locals 1

    .line 37
    invoke-direct {p0}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;-><init>()V

    .line 34
    new-instance v0, Ljava/util/LinkedHashSet;

    invoke-direct {v0}, Ljava/util/LinkedHashSet;-><init>()V

    iput-object v0, p0, Lorg/codehaus/groovy/transform/stc/SharedVariableCollector;->closureSharedExpressions:Ljava/util/Set;

    const/4 v0, 0x0

    .line 35
    iput-boolean v0, p0, Lorg/codehaus/groovy/transform/stc/SharedVariableCollector;->visited:Z

    .line 38
    iput-object p1, p0, Lorg/codehaus/groovy/transform/stc/SharedVariableCollector;->unit:Lorg/codehaus/groovy/control/SourceUnit;

    return-void
.end method


# virtual methods
.method public getClosureSharedExpressions()Ljava/util/Set;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Set<",
            "Lorg/codehaus/groovy/ast/expr/VariableExpression;",
            ">;"
        }
    .end annotation

    .line 47
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/SharedVariableCollector;->closureSharedExpressions:Ljava/util/Set;

    invoke-static {v0}, Ljava/util/Collections;->unmodifiableSet(Ljava/util/Set;)Ljava/util/Set;

    move-result-object v0

    return-object v0
.end method

.method protected getSourceUnit()Lorg/codehaus/groovy/control/SourceUnit;
    .locals 1

    .line 43
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/SharedVariableCollector;->unit:Lorg/codehaus/groovy/control/SourceUnit;

    return-object v0
.end method

.method public visitVariableExpression(Lorg/codehaus/groovy/ast/expr/VariableExpression;)V
    .locals 1

    .line 52
    iget-boolean v0, p0, Lorg/codehaus/groovy/transform/stc/SharedVariableCollector;->visited:Z

    if-eqz v0, :cond_0

    return-void

    :cond_0
    const/4 v0, 0x1

    .line 56
    iput-boolean v0, p0, Lorg/codehaus/groovy/transform/stc/SharedVariableCollector;->visited:Z

    .line 57
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->isClosureSharedVariable()Z

    move-result v0

    if-eqz v0, :cond_1

    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/SharedVariableCollector;->closureSharedExpressions:Ljava/util/Set;

    invoke-interface {v0, p1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 58
    :cond_1
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;->visitVariableExpression(Lorg/codehaus/groovy/ast/expr/VariableExpression;)V

    return-void
.end method
