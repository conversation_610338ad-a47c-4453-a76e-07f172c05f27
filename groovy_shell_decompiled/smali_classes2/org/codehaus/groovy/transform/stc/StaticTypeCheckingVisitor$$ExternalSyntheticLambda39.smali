.class public final synthetic Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda39;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Predicate;


# instance fields
.field public final synthetic f$0:Lorg/codehaus/groovy/ast/GenericsType;


# direct methods
.method public synthetic constructor <init>(Lorg/codehaus/groovy/ast/GenericsType;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda39;->f$0:Lorg/codehaus/groovy/ast/GenericsType;

    return-void
.end method


# virtual methods
.method public final test(Ljava/lang/Object;)Z
    .locals 1

    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda39;->f$0:Lorg/codehaus/groovy/ast/GenericsType;

    check-cast p1, Lorg/codehaus/groovy/ast/GenericsType;

    invoke-static {v0, p1}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->lambda$resolvePlaceholdersFromImplicitTypeHints$37(Lorg/codehaus/groovy/ast/GenericsType;Lorg/codehaus/groovy/ast/GenericsType;)Z

    move-result p1

    return p1
.end method
