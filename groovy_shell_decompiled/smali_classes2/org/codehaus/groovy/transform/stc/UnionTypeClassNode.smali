.class Lorg/codehaus/groovy/transform/stc/UnionTypeClassNode;
.super Lorg/codehaus/groovy/ast/ClassNode;
.source "UnionTypeClassNode.java"


# instance fields
.field private final delegates:[Lorg/codehaus/groovy/ast/ClassNode;


# direct methods
.method varargs constructor <init>([Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 3

    .line 62
    invoke-static {p1}, Lorg/codehaus/groovy/transform/stc/UnionTypeClassNode;->makeName([Lorg/codehaus/groovy/ast/ClassNode;)Ljava/lang/String;

    move-result-object v0

    sget-object v1, Lorg/codehaus/groovy/ast/ClassHelper;->OBJECT_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    const/4 v2, 0x0

    invoke-direct {p0, v0, v2, v1}, Lorg/codehaus/groovy/ast/ClassNode;-><init>(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;)V

    if-nez p1, :cond_0

    .line 63
    sget-object p1, Lorg/codehaus/groovy/ast/ClassNode;->EMPTY_ARRAY:[Lorg/codehaus/groovy/ast/ClassNode;

    :cond_0
    iput-object p1, p0, Lorg/codehaus/groovy/transform/stc/UnionTypeClassNode;->delegates:[Lorg/codehaus/groovy/ast/ClassNode;

    return-void
.end method

.method private static makeName([Lorg/codehaus/groovy/ast/ClassNode;)Ljava/lang/String;
    .locals 4

    .line 67
    new-instance v0, Ljava/util/StringJoiner;

    const-string v1, "+"

    const-string v2, "<UnionType:"

    const-string v3, ">"

    invoke-direct {v0, v1, v2, v3}, Ljava/util/StringJoiner;-><init>(Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;)V

    .line 68
    array-length v1, p0

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v1, :cond_0

    aget-object v3, p0, v2

    .line 69
    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/ClassNode;->getText()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v0, v3}, Ljava/util/StringJoiner;->add(Ljava/lang/CharSequence;)Ljava/util/StringJoiner;

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 71
    :cond_0
    invoke-virtual {v0}, Ljava/util/StringJoiner;->toString()Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public addConstructor(I[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/ConstructorNode;
    .locals 0

    .line 80
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    invoke-direct {p1}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw p1
.end method

.method public addConstructor(Lorg/codehaus/groovy/ast/ConstructorNode;)V
    .locals 0

    .line 85
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    invoke-direct {p1}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw p1
.end method

.method public addField(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/FieldNode;
    .locals 0

    .line 90
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    invoke-direct {p1}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw p1
.end method

.method public addField(Lorg/codehaus/groovy/ast/FieldNode;)V
    .locals 0

    .line 95
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    invoke-direct {p1}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw p1
.end method

.method public addFieldFirst(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/FieldNode;
    .locals 0

    .line 100
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    invoke-direct {p1}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw p1
.end method

.method public addFieldFirst(Lorg/codehaus/groovy/ast/FieldNode;)V
    .locals 0

    .line 105
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    invoke-direct {p1}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw p1
.end method

.method public addInterface(Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 0

    .line 110
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    invoke-direct {p1}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw p1
.end method

.method public addMethod(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/MethodNode;
    .locals 0

    .line 115
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    invoke-direct {p1}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw p1
.end method

.method public addMethod(Lorg/codehaus/groovy/ast/MethodNode;)V
    .locals 0

    .line 120
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    invoke-direct {p1}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw p1
.end method

.method public addMixin(Lorg/codehaus/groovy/ast/MixinNode;)V
    .locals 0

    .line 125
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    invoke-direct {p1}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw p1
.end method

.method public addObjectInitializerStatements(Lorg/codehaus/groovy/ast/stmt/Statement;)V
    .locals 0

    .line 130
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    invoke-direct {p1}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw p1
.end method

.method public addProperty(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/stmt/Statement;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/PropertyNode;
    .locals 0

    .line 135
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    invoke-direct {p1}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw p1
.end method

.method public addProperty(Lorg/codehaus/groovy/ast/PropertyNode;)V
    .locals 0

    .line 140
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    invoke-direct {p1}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw p1
.end method

.method public addStaticInitializerStatements(Ljava/util/List;Z)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/stmt/Statement;",
            ">;Z)V"
        }
    .end annotation

    .line 145
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    invoke-direct {p1}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw p1
.end method

.method public addSyntheticMethod(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/MethodNode;
    .locals 0

    .line 150
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    invoke-direct {p1}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw p1
.end method

.method public addTransform(Ljava/lang/Class;Lorg/codehaus/groovy/ast/ASTNode;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Class<",
            "+",
            "Lorg/codehaus/groovy/transform/ASTTransformation;",
            ">;",
            "Lorg/codehaus/groovy/ast/ASTNode;",
            ")V"
        }
    .end annotation

    .line 155
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    invoke-direct {p1}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw p1
.end method

.method public declaresInterface(Lorg/codehaus/groovy/ast/ClassNode;)Z
    .locals 5

    .line 160
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/UnionTypeClassNode;->delegates:[Lorg/codehaus/groovy/ast/ClassNode;

    array-length v1, v0

    const/4 v2, 0x0

    move v3, v2

    :goto_0
    if-ge v3, v1, :cond_1

    aget-object v4, v0, v3

    .line 161
    invoke-virtual {v4, p1}, Lorg/codehaus/groovy/ast/ClassNode;->declaresInterface(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v4

    if-eqz v4, :cond_0

    const/4 p1, 0x1

    return p1

    :cond_0
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    :cond_1
    return v2
.end method

.method public getAbstractMethods()Ljava/util/List;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/MethodNode;",
            ">;"
        }
    .end annotation

    .line 168
    new-instance v0, Ljava/util/LinkedList;

    invoke-direct {v0}, Ljava/util/LinkedList;-><init>()V

    .line 169
    iget-object v1, p0, Lorg/codehaus/groovy/transform/stc/UnionTypeClassNode;->delegates:[Lorg/codehaus/groovy/ast/ClassNode;

    array-length v2, v1

    const/4 v3, 0x0

    :goto_0
    if-ge v3, v2, :cond_0

    aget-object v4, v1, v3

    .line 170
    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/ClassNode;->getAbstractMethods()Ljava/util/List;

    move-result-object v4

    invoke-interface {v0, v4}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    :cond_0
    return-object v0
.end method

.method public getAllDeclaredMethods()Ljava/util/List;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/MethodNode;",
            ">;"
        }
    .end annotation

    .line 177
    new-instance v0, Ljava/util/LinkedList;

    invoke-direct {v0}, Ljava/util/LinkedList;-><init>()V

    .line 178
    iget-object v1, p0, Lorg/codehaus/groovy/transform/stc/UnionTypeClassNode;->delegates:[Lorg/codehaus/groovy/ast/ClassNode;

    array-length v2, v1

    const/4 v3, 0x0

    :goto_0
    if-ge v3, v2, :cond_0

    aget-object v4, v1, v3

    .line 179
    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/ClassNode;->getAllDeclaredMethods()Ljava/util/List;

    move-result-object v4

    invoke-interface {v0, v4}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    :cond_0
    return-object v0
.end method

.method public getAllInterfaces()Ljava/util/Set;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Set<",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            ">;"
        }
    .end annotation

    .line 186
    new-instance v0, Ljava/util/HashSet;

    invoke-direct {v0}, Ljava/util/HashSet;-><init>()V

    .line 187
    iget-object v1, p0, Lorg/codehaus/groovy/transform/stc/UnionTypeClassNode;->delegates:[Lorg/codehaus/groovy/ast/ClassNode;

    array-length v2, v1

    const/4 v3, 0x0

    :goto_0
    if-ge v3, v2, :cond_0

    aget-object v4, v1, v3

    .line 188
    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/ClassNode;->getAllInterfaces()Ljava/util/Set;

    move-result-object v4

    invoke-interface {v0, v4}, Ljava/util/Set;->addAll(Ljava/util/Collection;)Z

    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    :cond_0
    return-object v0
.end method

.method public getAnnotations()Ljava/util/List;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/AnnotationNode;",
            ">;"
        }
    .end annotation

    .line 195
    new-instance v0, Ljava/util/LinkedList;

    invoke-direct {v0}, Ljava/util/LinkedList;-><init>()V

    .line 196
    iget-object v1, p0, Lorg/codehaus/groovy/transform/stc/UnionTypeClassNode;->delegates:[Lorg/codehaus/groovy/ast/ClassNode;

    array-length v2, v1

    const/4 v3, 0x0

    :goto_0
    if-ge v3, v2, :cond_1

    aget-object v4, v1, v3

    .line 197
    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/ClassNode;->getAnnotations()Ljava/util/List;

    move-result-object v4

    if-eqz v4, :cond_0

    .line 198
    invoke-interface {v0, v4}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    :cond_0
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    :cond_1
    return-object v0
.end method

.method public getAnnotations(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/util/List;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            ")",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/AnnotationNode;",
            ">;"
        }
    .end annotation

    .line 205
    new-instance v0, Ljava/util/LinkedList;

    invoke-direct {v0}, Ljava/util/LinkedList;-><init>()V

    .line 206
    iget-object v1, p0, Lorg/codehaus/groovy/transform/stc/UnionTypeClassNode;->delegates:[Lorg/codehaus/groovy/ast/ClassNode;

    array-length v2, v1

    const/4 v3, 0x0

    :goto_0
    if-ge v3, v2, :cond_1

    aget-object v4, v1, v3

    .line 207
    invoke-virtual {v4, p1}, Lorg/codehaus/groovy/ast/ClassNode;->getAnnotations(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/util/List;

    move-result-object v4

    if-eqz v4, :cond_0

    .line 208
    invoke-interface {v0, v4}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    :cond_0
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    :cond_1
    return-object v0
.end method

.method public getComponentType()Lorg/codehaus/groovy/ast/ClassNode;
    .locals 1

    .line 215
    new-instance v0, Ljava/lang/UnsupportedOperationException;

    invoke-direct {v0}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw v0
.end method

.method public getDeclaredConstructors()Ljava/util/List;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/ConstructorNode;",
            ">;"
        }
    .end annotation

    .line 220
    new-instance v0, Ljava/util/LinkedList;

    invoke-direct {v0}, Ljava/util/LinkedList;-><init>()V

    .line 221
    iget-object v1, p0, Lorg/codehaus/groovy/transform/stc/UnionTypeClassNode;->delegates:[Lorg/codehaus/groovy/ast/ClassNode;

    array-length v2, v1

    const/4 v3, 0x0

    :goto_0
    if-ge v3, v2, :cond_0

    aget-object v4, v1, v3

    .line 222
    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/ClassNode;->getDeclaredConstructors()Ljava/util/List;

    move-result-object v4

    invoke-interface {v0, v4}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    :cond_0
    return-object v0
.end method

.method public getDeclaredField(Ljava/lang/String;)Lorg/codehaus/groovy/ast/FieldNode;
    .locals 4

    .line 229
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/UnionTypeClassNode;->delegates:[Lorg/codehaus/groovy/ast/ClassNode;

    array-length v1, v0

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v1, :cond_1

    aget-object v3, v0, v2

    .line 230
    invoke-virtual {v3, p1}, Lorg/codehaus/groovy/ast/ClassNode;->getDeclaredField(Ljava/lang/String;)Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object v3

    if-eqz v3, :cond_0

    return-object v3

    :cond_0
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_1
    const/4 p1, 0x0

    return-object p1
.end method

.method public getDeclaredMethod(Ljava/lang/String;[Lorg/codehaus/groovy/ast/Parameter;)Lorg/codehaus/groovy/ast/MethodNode;
    .locals 4

    .line 238
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/UnionTypeClassNode;->delegates:[Lorg/codehaus/groovy/ast/ClassNode;

    array-length v1, v0

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v1, :cond_1

    aget-object v3, v0, v2

    .line 239
    invoke-virtual {v3, p1, p2}, Lorg/codehaus/groovy/ast/ClassNode;->getDeclaredMethod(Ljava/lang/String;[Lorg/codehaus/groovy/ast/Parameter;)Lorg/codehaus/groovy/ast/MethodNode;

    move-result-object v3

    if-eqz v3, :cond_0

    return-object v3

    :cond_0
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_1
    const/4 p1, 0x0

    return-object p1
.end method

.method public getDeclaredMethods(Ljava/lang/String;)Ljava/util/List;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            ")",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/MethodNode;",
            ">;"
        }
    .end annotation

    .line 247
    new-instance v0, Ljava/util/LinkedList;

    invoke-direct {v0}, Ljava/util/LinkedList;-><init>()V

    .line 248
    iget-object v1, p0, Lorg/codehaus/groovy/transform/stc/UnionTypeClassNode;->delegates:[Lorg/codehaus/groovy/ast/ClassNode;

    array-length v2, v1

    const/4 v3, 0x0

    :goto_0
    if-ge v3, v2, :cond_1

    aget-object v4, v1, v3

    .line 249
    invoke-virtual {v4, p1}, Lorg/codehaus/groovy/ast/ClassNode;->getDeclaredMethods(Ljava/lang/String;)Ljava/util/List;

    move-result-object v4

    if-eqz v4, :cond_0

    .line 250
    invoke-interface {v0, v4}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    :cond_0
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    :cond_1
    return-object v0
.end method

.method public getDeclaredMethodsMap()Ljava/util/Map;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Lorg/codehaus/groovy/ast/MethodNode;",
            ">;"
        }
    .end annotation

    .line 257
    new-instance v0, Ljava/lang/UnsupportedOperationException;

    invoke-direct {v0}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw v0
.end method

.method public getDelegates()[Lorg/codehaus/groovy/ast/ClassNode;
    .locals 1

    .line 75
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/UnionTypeClassNode;->delegates:[Lorg/codehaus/groovy/ast/ClassNode;

    return-object v0
.end method

.method public getEnclosingMethod()Lorg/codehaus/groovy/ast/MethodNode;
    .locals 1

    .line 262
    new-instance v0, Ljava/lang/UnsupportedOperationException;

    invoke-direct {v0}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw v0
.end method

.method public getField(Ljava/lang/String;)Lorg/codehaus/groovy/ast/FieldNode;
    .locals 4

    .line 267
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/UnionTypeClassNode;->delegates:[Lorg/codehaus/groovy/ast/ClassNode;

    array-length v1, v0

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v1, :cond_1

    aget-object v3, v0, v2

    .line 268
    invoke-virtual {v3, p1}, Lorg/codehaus/groovy/ast/ClassNode;->getField(Ljava/lang/String;)Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object v3

    if-eqz v3, :cond_0

    return-object v3

    :cond_0
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_1
    const/4 p1, 0x0

    return-object p1
.end method

.method public getFields()Ljava/util/List;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/FieldNode;",
            ">;"
        }
    .end annotation

    .line 276
    new-instance v0, Ljava/util/LinkedList;

    invoke-direct {v0}, Ljava/util/LinkedList;-><init>()V

    .line 277
    iget-object v1, p0, Lorg/codehaus/groovy/transform/stc/UnionTypeClassNode;->delegates:[Lorg/codehaus/groovy/ast/ClassNode;

    array-length v2, v1

    const/4 v3, 0x0

    :goto_0
    if-ge v3, v2, :cond_1

    aget-object v4, v1, v3

    .line 278
    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/ClassNode;->getFields()Ljava/util/List;

    move-result-object v4

    if-eqz v4, :cond_0

    .line 279
    invoke-interface {v0, v4}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    :cond_0
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    :cond_1
    return-object v0
.end method

.method public getInnerClasses()Ljava/util/Iterator;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Iterator<",
            "Lorg/codehaus/groovy/ast/InnerClassNode;",
            ">;"
        }
    .end annotation

    .line 286
    new-instance v0, Ljava/lang/UnsupportedOperationException;

    invoke-direct {v0}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw v0
.end method

.method public getInterfaces()[Lorg/codehaus/groovy/ast/ClassNode;
    .locals 5

    .line 291
    new-instance v0, Ljava/util/LinkedHashSet;

    invoke-direct {v0}, Ljava/util/LinkedHashSet;-><init>()V

    .line 292
    iget-object v1, p0, Lorg/codehaus/groovy/transform/stc/UnionTypeClassNode;->delegates:[Lorg/codehaus/groovy/ast/ClassNode;

    array-length v2, v1

    const/4 v3, 0x0

    :goto_0
    if-ge v3, v2, :cond_1

    aget-object v4, v1, v3

    .line 293
    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/ClassNode;->getInterfaces()[Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v4

    if-eqz v4, :cond_0

    .line 294
    invoke-static {v0, v4}, Ljava/util/Collections;->addAll(Ljava/util/Collection;[Ljava/lang/Object;)Z

    :cond_0
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    .line 296
    :cond_1
    sget-object v1, Lorg/codehaus/groovy/ast/ClassNode;->EMPTY_ARRAY:[Lorg/codehaus/groovy/ast/ClassNode;

    invoke-interface {v0, v1}, Ljava/util/Set;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lorg/codehaus/groovy/ast/ClassNode;

    return-object v0
.end method

.method public getMethods()Ljava/util/List;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/MethodNode;",
            ">;"
        }
    .end annotation

    .line 301
    new-instance v0, Ljava/util/LinkedList;

    invoke-direct {v0}, Ljava/util/LinkedList;-><init>()V

    .line 302
    iget-object v1, p0, Lorg/codehaus/groovy/transform/stc/UnionTypeClassNode;->delegates:[Lorg/codehaus/groovy/ast/ClassNode;

    array-length v2, v1

    const/4 v3, 0x0

    :goto_0
    if-ge v3, v2, :cond_1

    aget-object v4, v1, v3

    .line 303
    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/ClassNode;->getMethods()Ljava/util/List;

    move-result-object v4

    if-eqz v4, :cond_0

    .line 304
    invoke-interface {v0, v4}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    :cond_0
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    :cond_1
    return-object v0
.end method

.method public getPlainNodeReference()Lorg/codehaus/groovy/ast/ClassNode;
    .locals 4

    .line 311
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/UnionTypeClassNode;->delegates:[Lorg/codehaus/groovy/ast/ClassNode;

    array-length v0, v0

    new-array v1, v0, [Lorg/codehaus/groovy/ast/ClassNode;

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v0, :cond_0

    .line 313
    iget-object v3, p0, Lorg/codehaus/groovy/transform/stc/UnionTypeClassNode;->delegates:[Lorg/codehaus/groovy/ast/ClassNode;

    aget-object v3, v3, v2

    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/ClassNode;->getPlainNodeReference()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v3

    aput-object v3, v1, v2

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 315
    :cond_0
    new-instance v0, Lorg/codehaus/groovy/transform/stc/UnionTypeClassNode;

    invoke-direct {v0, v1}, Lorg/codehaus/groovy/transform/stc/UnionTypeClassNode;-><init>([Lorg/codehaus/groovy/ast/ClassNode;)V

    return-object v0
.end method

.method public getProperties()Ljava/util/List;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/PropertyNode;",
            ">;"
        }
    .end annotation

    .line 320
    new-instance v0, Ljava/util/LinkedList;

    invoke-direct {v0}, Ljava/util/LinkedList;-><init>()V

    .line 321
    iget-object v1, p0, Lorg/codehaus/groovy/transform/stc/UnionTypeClassNode;->delegates:[Lorg/codehaus/groovy/ast/ClassNode;

    array-length v2, v1

    const/4 v3, 0x0

    :goto_0
    if-ge v3, v2, :cond_1

    aget-object v4, v1, v3

    .line 322
    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/ClassNode;->getProperties()Ljava/util/List;

    move-result-object v4

    if-eqz v4, :cond_0

    .line 323
    invoke-interface {v0, v4}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    :cond_0
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    :cond_1
    return-object v0
.end method

.method public getTypeClass()Ljava/lang/Class;
    .locals 1

    .line 330
    invoke-super {p0}, Lorg/codehaus/groovy/ast/ClassNode;->getTypeClass()Ljava/lang/Class;

    move-result-object v0

    return-object v0
.end method

.method public getUnresolvedInterfaces()[Lorg/codehaus/groovy/ast/ClassNode;
    .locals 5

    .line 335
    new-instance v0, Ljava/util/LinkedHashSet;

    invoke-direct {v0}, Ljava/util/LinkedHashSet;-><init>()V

    .line 336
    iget-object v1, p0, Lorg/codehaus/groovy/transform/stc/UnionTypeClassNode;->delegates:[Lorg/codehaus/groovy/ast/ClassNode;

    array-length v2, v1

    const/4 v3, 0x0

    :goto_0
    if-ge v3, v2, :cond_1

    aget-object v4, v1, v3

    .line 337
    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/ClassNode;->getUnresolvedInterfaces()[Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v4

    if-eqz v4, :cond_0

    .line 338
    invoke-static {v0, v4}, Ljava/util/Collections;->addAll(Ljava/util/Collection;[Ljava/lang/Object;)Z

    :cond_0
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    .line 340
    :cond_1
    sget-object v1, Lorg/codehaus/groovy/ast/ClassNode;->EMPTY_ARRAY:[Lorg/codehaus/groovy/ast/ClassNode;

    invoke-interface {v0, v1}, Ljava/util/Set;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lorg/codehaus/groovy/ast/ClassNode;

    return-object v0
.end method

.method public getUnresolvedInterfaces(Z)[Lorg/codehaus/groovy/ast/ClassNode;
    .locals 5

    .line 345
    new-instance v0, Ljava/util/LinkedHashSet;

    invoke-direct {v0}, Ljava/util/LinkedHashSet;-><init>()V

    .line 346
    iget-object v1, p0, Lorg/codehaus/groovy/transform/stc/UnionTypeClassNode;->delegates:[Lorg/codehaus/groovy/ast/ClassNode;

    array-length v2, v1

    const/4 v3, 0x0

    :goto_0
    if-ge v3, v2, :cond_1

    aget-object v4, v1, v3

    .line 347
    invoke-virtual {v4, p1}, Lorg/codehaus/groovy/ast/ClassNode;->getUnresolvedInterfaces(Z)[Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v4

    if-eqz v4, :cond_0

    .line 348
    invoke-static {v0, v4}, Ljava/util/Collections;->addAll(Ljava/util/Collection;[Ljava/lang/Object;)Z

    :cond_0
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    .line 350
    :cond_1
    sget-object p1, Lorg/codehaus/groovy/ast/ClassNode;->EMPTY_ARRAY:[Lorg/codehaus/groovy/ast/ClassNode;

    invoke-interface {v0, p1}, Ljava/util/Set;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object p1

    check-cast p1, [Lorg/codehaus/groovy/ast/ClassNode;

    return-object p1
.end method

.method public hashCode()I
    .locals 5

    .line 356
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/UnionTypeClassNode;->delegates:[Lorg/codehaus/groovy/ast/ClassNode;

    array-length v1, v0

    const/16 v2, 0xd

    const/4 v3, 0x0

    :goto_0
    if-ge v3, v1, :cond_0

    aget-object v4, v0, v3

    mul-int/lit8 v2, v2, 0x1f

    .line 357
    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/ClassNode;->hashCode()I

    move-result v4

    add-int/2addr v2, v4

    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    :cond_0
    return v2
.end method

.method public implementsInterface(Lorg/codehaus/groovy/ast/ClassNode;)Z
    .locals 5

    .line 364
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/UnionTypeClassNode;->delegates:[Lorg/codehaus/groovy/ast/ClassNode;

    array-length v1, v0

    const/4 v2, 0x0

    move v3, v2

    :goto_0
    if-ge v3, v1, :cond_1

    aget-object v4, v0, v3

    .line 365
    invoke-virtual {v4, p1}, Lorg/codehaus/groovy/ast/ClassNode;->implementsInterface(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v4

    if-eqz v4, :cond_0

    const/4 p1, 0x1

    return p1

    :cond_0
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    :cond_1
    return v2
.end method

.method public isAnnotated()Z
    .locals 5

    .line 372
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/UnionTypeClassNode;->delegates:[Lorg/codehaus/groovy/ast/ClassNode;

    array-length v1, v0

    const/4 v2, 0x0

    move v3, v2

    :goto_0
    if-ge v3, v1, :cond_1

    aget-object v4, v0, v3

    .line 373
    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/ClassNode;->isAnnotated()Z

    move-result v4

    if-eqz v4, :cond_0

    const/4 v0, 0x1

    return v0

    :cond_0
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    :cond_1
    return v2
.end method

.method public isDerivedFrom(Lorg/codehaus/groovy/ast/ClassNode;)Z
    .locals 5

    .line 380
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/UnionTypeClassNode;->delegates:[Lorg/codehaus/groovy/ast/ClassNode;

    array-length v1, v0

    const/4 v2, 0x0

    move v3, v2

    :goto_0
    if-ge v3, v1, :cond_1

    aget-object v4, v0, v3

    .line 381
    invoke-virtual {v4, p1}, Lorg/codehaus/groovy/ast/ClassNode;->isDerivedFrom(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v4

    if-eqz v4, :cond_0

    const/4 p1, 0x1

    return p1

    :cond_0
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    :cond_1
    return v2
.end method

.method public isDerivedFromGroovyObject()Z
    .locals 5

    .line 388
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/UnionTypeClassNode;->delegates:[Lorg/codehaus/groovy/ast/ClassNode;

    array-length v1, v0

    const/4 v2, 0x0

    move v3, v2

    :goto_0
    if-ge v3, v1, :cond_1

    aget-object v4, v0, v3

    .line 389
    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/ClassNode;->isDerivedFromGroovyObject()Z

    move-result v4

    if-eqz v4, :cond_0

    const/4 v0, 0x1

    return v0

    :cond_0
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    :cond_1
    return v2
.end method

.method public removeField(Ljava/lang/String;)V
    .locals 0

    .line 396
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    invoke-direct {p1}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw p1
.end method

.method public renameField(Ljava/lang/String;Ljava/lang/String;)V
    .locals 0

    .line 401
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    invoke-direct {p1}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw p1
.end method

.method public setAnnotated(Z)V
    .locals 0

    .line 406
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    invoke-direct {p1}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw p1
.end method

.method public setEnclosingMethod(Lorg/codehaus/groovy/ast/MethodNode;)V
    .locals 0

    .line 411
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    invoke-direct {p1}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw p1
.end method

.method public setGenericsPlaceHolder(Z)V
    .locals 0

    .line 416
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    invoke-direct {p1}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw p1
.end method

.method public setGenericsTypes([Lorg/codehaus/groovy/ast/GenericsType;)V
    .locals 0

    .line 421
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    invoke-direct {p1}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw p1
.end method

.method public setInterfaces([Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 0

    .line 426
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    invoke-direct {p1}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw p1
.end method

.method public setModifiers(I)V
    .locals 0

    .line 431
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    invoke-direct {p1}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw p1
.end method

.method public setName(Ljava/lang/String;)Ljava/lang/String;
    .locals 0

    .line 436
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    invoke-direct {p1}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw p1
.end method

.method public setRedirect(Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 0

    .line 441
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    invoke-direct {p1}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw p1
.end method

.method public setScript(Z)V
    .locals 0

    .line 446
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    invoke-direct {p1}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw p1
.end method

.method public setScriptBody(Z)V
    .locals 0

    .line 451
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    invoke-direct {p1}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw p1
.end method

.method public setStaticClass(Z)V
    .locals 0

    .line 456
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    invoke-direct {p1}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw p1
.end method

.method public setSuperClass(Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 0

    .line 461
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    invoke-direct {p1}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw p1
.end method

.method public setSyntheticPublic(Z)V
    .locals 0

    .line 466
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    invoke-direct {p1}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw p1
.end method

.method public setUnresolvedSuperClass(Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 0

    .line 471
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    invoke-direct {p1}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw p1
.end method

.method public setUsingGenerics(Z)V
    .locals 0

    .line 476
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    invoke-direct {p1}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw p1
.end method

.method public visitContents(Lorg/codehaus/groovy/ast/GroovyClassVisitor;)V
    .locals 4

    .line 481
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/UnionTypeClassNode;->delegates:[Lorg/codehaus/groovy/ast/ClassNode;

    array-length v1, v0

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v1, :cond_0

    aget-object v3, v0, v2

    .line 482
    invoke-virtual {v3, p1}, Lorg/codehaus/groovy/ast/ClassNode;->visitContents(Lorg/codehaus/groovy/ast/GroovyClassVisitor;)V

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_0
    return-void
.end method
