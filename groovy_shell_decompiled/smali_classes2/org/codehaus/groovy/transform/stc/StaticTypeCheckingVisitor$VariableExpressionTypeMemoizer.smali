.class public Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$VariableExpressionTypeMemoizer;
.super Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;
.source "StaticTypeCheckingVisitor.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x4
    name = "VariableExpressionTypeMemoizer"
.end annotation


# instance fields
.field private final onlySharedVariables:Z

.field final synthetic this$0:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;

.field private final varOrigType:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Lorg/codehaus/groovy/ast/expr/VariableExpression;",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;Ljava/util/Map;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Lorg/codehaus/groovy/ast/expr/VariableExpression;",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            ">;)V"
        }
    .end annotation

    const/4 v0, 0x0

    .line 6049
    invoke-direct {p0, p1, p2, v0}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$VariableExpressionTypeMemoizer;-><init>(Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;Ljava/util/Map;Z)V

    return-void
.end method

.method public constructor <init>(Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;Ljava/util/Map;Z)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Lorg/codehaus/groovy/ast/expr/VariableExpression;",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            ">;Z)V"
        }
    .end annotation

    .line 6052
    iput-object p1, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$VariableExpressionTypeMemoizer;->this$0:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;

    invoke-direct {p0}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;-><init>()V

    .line 6053
    iput-object p2, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$VariableExpressionTypeMemoizer;->varOrigType:Ljava/util/Map;

    .line 6054
    iput-boolean p3, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$VariableExpressionTypeMemoizer;->onlySharedVariables:Z

    return-void
.end method


# virtual methods
.method protected getSourceUnit()Lorg/codehaus/groovy/control/SourceUnit;
    .locals 1

    .line 6059
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$VariableExpressionTypeMemoizer;->this$0:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;

    invoke-virtual {v0}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->getSourceUnit()Lorg/codehaus/groovy/control/SourceUnit;

    move-result-object v0

    return-object v0
.end method

.method public visitVariableExpression(Lorg/codehaus/groovy/ast/expr/VariableExpression;)V
    .locals 3

    .line 6064
    invoke-static {p1}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingSupport;->findTargetVariable(Lorg/codehaus/groovy/ast/expr/VariableExpression;)Lorg/codehaus/groovy/ast/Variable;

    move-result-object v0

    .line 6065
    iget-boolean v1, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$VariableExpressionTypeMemoizer;->onlySharedVariables:Z

    if-eqz v1, :cond_0

    invoke-interface {v0}, Lorg/codehaus/groovy/ast/Variable;->isClosureSharedVariable()Z

    move-result v1

    if-eqz v1, :cond_2

    :cond_0
    instance-of v1, v0, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    if-eqz v1, :cond_2

    .line 6066
    check-cast v0, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    .line 6067
    sget-object v1, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->INFERRED_TYPE:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    invoke-virtual {v0, v1}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->getNodeMetaData(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/ast/ClassNode;

    if-nez v1, :cond_1

    .line 6068
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->getOriginType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    .line 6069
    :cond_1
    iget-object v2, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$VariableExpressionTypeMemoizer;->varOrigType:Ljava/util/Map;

    invoke-interface {v2, v0, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 6071
    :cond_2
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;->visitVariableExpression(Lorg/codehaus/groovy/ast/expr/VariableExpression;)V

    return-void
.end method
