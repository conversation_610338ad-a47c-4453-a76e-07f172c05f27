.class public final synthetic Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda35;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/IntPredicate;


# instance fields
.field public final synthetic f$0:[Lorg/codehaus/groovy/ast/GenericsType;

.field public final synthetic f$1:[Lorg/codehaus/groovy/ast/GenericsType;


# direct methods
.method public synthetic constructor <init>([Lorg/codehaus/groovy/ast/GenericsType;[Lorg/codehaus/groovy/ast/GenericsType;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda35;->f$0:[Lorg/codehaus/groovy/ast/GenericsType;

    iput-object p2, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda35;->f$1:[Lorg/codehaus/groovy/ast/GenericsType;

    return-void
.end method


# virtual methods
.method public final test(I)Z
    .locals 2

    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda35;->f$0:[Lorg/codehaus/groovy/ast/GenericsType;

    iget-object v1, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda35;->f$1:[Lorg/codehaus/groovy/ast/GenericsType;

    invoke-static {v0, v1, p1}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->lambda$inferDiamondType$6([Lorg/codehaus/groovy/ast/GenericsType;[Lorg/codehaus/groovy/ast/GenericsType;I)Z

    move-result p1

    return p1
.end method
