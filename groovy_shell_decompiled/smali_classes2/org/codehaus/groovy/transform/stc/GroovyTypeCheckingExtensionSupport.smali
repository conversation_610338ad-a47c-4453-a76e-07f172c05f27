.class public Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;
.super Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;
.source "GroovyTypeCheckingExtensionSupport.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport$TypeCheckingDSL;
    }
.end annotation


# static fields
.field private static final METHOD_ALIASES:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field private final compilationUnit:Lorg/codehaus/groovy/control/CompilationUnit;

.field private final eventHandlers:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Lgroovy/lang/Closure;",
            ">;>;"
        }
    .end annotation
.end field

.field private final scriptPath:Ljava/lang/String;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 66
    new-instance v0, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport$1;

    invoke-direct {v0}, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport$1;-><init>()V

    invoke-static {v0}, Ljava/util/Collections;->unmodifiableMap(Ljava/util/Map;)Ljava/util/Map;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->METHOD_ALIASES:Ljava/util/Map;

    return-void
.end method

.method public constructor <init>(Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;Ljava/lang/String;Lorg/codehaus/groovy/control/CompilationUnit;)V
    .locals 0

    .line 107
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/transform/stc/AbstractTypeCheckingExtension;-><init>(Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;)V

    .line 91
    new-instance p1, Ljava/util/HashMap;

    invoke-direct {p1}, Ljava/util/HashMap;-><init>()V

    iput-object p1, p0, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->eventHandlers:Ljava/util/Map;

    .line 108
    iput-object p2, p0, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->scriptPath:Ljava/lang/String;

    .line 109
    iput-object p3, p0, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->compilationUnit:Lorg/codehaus/groovy/control/CompilationUnit;

    return-void
.end method

.method static synthetic access$100()Ljava/util/Map;
    .locals 1

    .line 63
    sget-object v0, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->METHOD_ALIASES:Ljava/util/Map;

    return-object v0
.end method

.method static synthetic access$200(Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;)Ljava/util/Map;
    .locals 0

    .line 63
    iget-object p0, p0, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->eventHandlers:Ljava/util/Map;

    return-object p0
.end method

.method private addLoadingError(Lorg/codehaus/groovy/control/CompilerConfiguration;)V
    .locals 4

    .line 198
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->context:Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;

    invoke-virtual {v0}, Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;->getErrorCollector()Lorg/codehaus/groovy/control/ErrorCollector;

    move-result-object v0

    new-instance v1, Lorg/codehaus/groovy/control/messages/SimpleMessage;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "Static type checking extension \'"

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    iget-object v3, p0, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->scriptPath:Ljava/lang/String;

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    const-string v3, "\' could not be loaded."

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    .line 200
    invoke-virtual {p1}, Lorg/codehaus/groovy/control/CompilerConfiguration;->getDebug()Z

    move-result p1

    invoke-static {p1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object p1

    iget-object v3, p0, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->typeCheckingVisitor:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;

    invoke-virtual {v3}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->getSourceUnit()Lorg/codehaus/groovy/control/SourceUnit;

    move-result-object v3

    invoke-direct {v1, v2, p1, v3}, Lorg/codehaus/groovy/control/messages/SimpleMessage;-><init>(Ljava/lang/String;Ljava/lang/Object;Lorg/codehaus/groovy/control/ProcessingUnit;)V

    .line 198
    invoke-virtual {v0, v1}, Lorg/codehaus/groovy/control/ErrorCollector;->addFatalError(Lorg/codehaus/groovy/control/messages/Message;)V

    return-void
.end method


# virtual methods
.method public afterMethodCall(Lorg/codehaus/groovy/ast/expr/MethodCall;)V
    .locals 4

    .line 226
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->eventHandlers:Ljava/util/Map;

    const-string v1, "afterMethodCall"

    invoke-interface {v0, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/List;

    if-eqz v0, :cond_0

    .line 228
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovy/lang/Closure;

    const/4 v2, 0x1

    new-array v2, v2, [Ljava/lang/Object;

    const/4 v3, 0x0

    aput-object p1, v2, v3

    .line 229
    invoke-virtual {p0, v1, v2}, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->safeCall(Lgroovy/lang/Closure;[Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    :cond_0
    return-void
.end method

.method public afterVisitClass(Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 4

    .line 306
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->eventHandlers:Ljava/util/Map;

    const-string v1, "afterVisitClass"

    invoke-interface {v0, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/List;

    if-eqz v0, :cond_0

    .line 308
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovy/lang/Closure;

    const/4 v2, 0x1

    new-array v2, v2, [Ljava/lang/Object;

    const/4 v3, 0x0

    aput-object p1, v2, v3

    .line 309
    invoke-virtual {p0, v1, v2}, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->safeCall(Lgroovy/lang/Closure;[Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    :cond_0
    return-void
.end method

.method public afterVisitMethod(Lorg/codehaus/groovy/ast/MethodNode;)V
    .locals 4

    .line 284
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->eventHandlers:Ljava/util/Map;

    const-string v1, "afterVisitMethod"

    invoke-interface {v0, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/List;

    if-eqz v0, :cond_0

    .line 286
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovy/lang/Closure;

    const/4 v2, 0x1

    new-array v2, v2, [Ljava/lang/Object;

    const/4 v3, 0x0

    aput-object p1, v2, v3

    .line 287
    invoke-virtual {p0, v1, v2}, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->safeCall(Lgroovy/lang/Closure;[Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    :cond_0
    return-void
.end method

.method public beforeMethodCall(Lorg/codehaus/groovy/ast/expr/MethodCall;)Z
    .locals 4

    const/4 v0, 0x0

    .line 236
    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->setHandled(Z)V

    .line 237
    iget-object v1, p0, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->eventHandlers:Ljava/util/Map;

    const-string v2, "beforeMethodCall"

    invoke-interface {v1, v2}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/util/List;

    if-eqz v1, :cond_0

    .line 239
    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_0

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovy/lang/Closure;

    const/4 v3, 0x1

    new-array v3, v3, [Ljava/lang/Object;

    aput-object p1, v3, v0

    .line 240
    invoke-virtual {p0, v2, v3}, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->safeCall(Lgroovy/lang/Closure;[Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    .line 243
    :cond_0
    iget-boolean p1, p0, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->handled:Z

    return p1
.end method

.method public beforeVisitClass(Lorg/codehaus/groovy/ast/ClassNode;)Z
    .locals 4

    const/4 v0, 0x0

    .line 294
    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->setHandled(Z)V

    .line 295
    iget-object v1, p0, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->eventHandlers:Ljava/util/Map;

    const-string v2, "beforeVisitClass"

    invoke-interface {v1, v2}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/util/List;

    if-eqz v1, :cond_0

    .line 297
    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_0

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovy/lang/Closure;

    const/4 v3, 0x1

    new-array v3, v3, [Ljava/lang/Object;

    aput-object p1, v3, v0

    .line 298
    invoke-virtual {p0, v2, v3}, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->safeCall(Lgroovy/lang/Closure;[Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    .line 301
    :cond_0
    iget-boolean p1, p0, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->handled:Z

    return p1
.end method

.method public beforeVisitMethod(Lorg/codehaus/groovy/ast/MethodNode;)Z
    .locals 4

    const/4 v0, 0x0

    .line 316
    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->setHandled(Z)V

    .line 317
    iget-object v1, p0, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->eventHandlers:Ljava/util/Map;

    const-string v2, "beforeVisitMethod"

    invoke-interface {v1, v2}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/util/List;

    if-eqz v1, :cond_0

    .line 319
    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_0

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovy/lang/Closure;

    const/4 v3, 0x1

    new-array v3, v3, [Ljava/lang/Object;

    aput-object p1, v3, v0

    .line 320
    invoke-virtual {p0, v2, v3}, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->safeCall(Lgroovy/lang/Closure;[Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    .line 323
    :cond_0
    iget-boolean p1, p0, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->handled:Z

    return p1
.end method

.method public finish()V
    .locals 3

    .line 206
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->eventHandlers:Ljava/util/Map;

    const-string v1, "finish"

    invoke-interface {v0, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/List;

    if-eqz v0, :cond_0

    .line 208
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovy/lang/Closure;

    const/4 v2, 0x0

    new-array v2, v2, [Ljava/lang/Object;

    .line 209
    invoke-virtual {p0, v1, v2}, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->safeCall(Lgroovy/lang/Closure;[Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    :cond_0
    return-void
.end method

.method public handleAmbiguousMethods(Ljava/util/List;Lorg/codehaus/groovy/ast/expr/Expression;)Ljava/util/List;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/MethodNode;",
            ">;",
            "Lorg/codehaus/groovy/ast/expr/Expression;",
            ")",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/MethodNode;",
            ">;"
        }
    .end annotation

    .line 375
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->eventHandlers:Ljava/util/Map;

    const-string v1, "handleAmbiguousMethods"

    invoke-interface {v0, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/List;

    if-eqz v0, :cond_3

    .line 378
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    .line 379
    :cond_0
    :goto_0
    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result v1

    const/4 v2, 0x1

    if-le v1, v2, :cond_3

    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_3

    .line 380
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovy/lang/Closure;

    const/4 v3, 0x2

    new-array v3, v3, [Ljava/lang/Object;

    const/4 v4, 0x0

    aput-object p1, v3, v4

    aput-object p2, v3, v2

    .line 381
    invoke-virtual {p0, v1, v3}, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->safeCall(Lgroovy/lang/Closure;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    if-eqz v1, :cond_0

    .line 383
    instance-of p1, v1, Lorg/codehaus/groovy/ast/MethodNode;

    if-eqz p1, :cond_1

    .line 384
    check-cast v1, Lorg/codehaus/groovy/ast/MethodNode;

    invoke-static {v1}, Ljava/util/Collections;->singletonList(Ljava/lang/Object;)Ljava/util/List;

    move-result-object p1

    goto :goto_0

    .line 385
    :cond_1
    instance-of p1, v1, Ljava/util/Collection;

    if-eqz p1, :cond_2

    .line 386
    new-instance p1, Ljava/util/LinkedList;

    check-cast v1, Ljava/util/Collection;

    invoke-direct {p1, v1}, Ljava/util/LinkedList;-><init>(Ljava/util/Collection;)V

    goto :goto_0

    .line 388
    :cond_2
    new-instance p1, Lorg/codehaus/groovy/GroovyBugError;

    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "Type checking extension returned unexpected method list: "

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-direct {p1, p2}, Lorg/codehaus/groovy/GroovyBugError;-><init>(Ljava/lang/String;)V

    throw p1

    :cond_3
    return-object p1
.end method

.method public handleIncompatibleAssignment(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;)Z
    .locals 5

    const/4 v0, 0x0

    .line 328
    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->setHandled(Z)V

    .line 329
    iget-object v1, p0, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->eventHandlers:Ljava/util/Map;

    const-string v2, "handleIncompatibleAssignment"

    invoke-interface {v1, v2}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/util/List;

    if-eqz v1, :cond_0

    .line 331
    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_0

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovy/lang/Closure;

    const/4 v3, 0x3

    new-array v3, v3, [Ljava/lang/Object;

    aput-object p1, v3, v0

    const/4 v4, 0x1

    aput-object p2, v3, v4

    const/4 v4, 0x2

    aput-object p3, v3, v4

    .line 332
    invoke-virtual {p0, v2, v3}, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->safeCall(Lgroovy/lang/Closure;[Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    .line 335
    :cond_0
    iget-boolean p1, p0, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->handled:Z

    return p1
.end method

.method public handleIncompatibleReturnType(Lorg/codehaus/groovy/ast/stmt/ReturnStatement;Lorg/codehaus/groovy/ast/ClassNode;)Z
    .locals 5

    const/4 v0, 0x0

    .line 340
    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->setHandled(Z)V

    .line 341
    iget-object v1, p0, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->eventHandlers:Ljava/util/Map;

    const-string v2, "handleIncompatibleReturnType"

    invoke-interface {v1, v2}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/util/List;

    if-eqz v1, :cond_0

    .line 343
    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_0

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovy/lang/Closure;

    const/4 v3, 0x2

    new-array v3, v3, [Ljava/lang/Object;

    aput-object p1, v3, v0

    const/4 v4, 0x1

    aput-object p2, v3, v4

    .line 344
    invoke-virtual {p0, v2, v3}, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->safeCall(Lgroovy/lang/Closure;[Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    .line 347
    :cond_0
    iget-boolean p1, p0, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->handled:Z

    return p1
.end method

.method public handleMissingMethod(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/MethodCall;)Ljava/util/List;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            "Ljava/lang/String;",
            "Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;",
            "[",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            "Lorg/codehaus/groovy/ast/expr/MethodCall;",
            ")",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/MethodNode;",
            ">;"
        }
    .end annotation

    .line 353
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->eventHandlers:Ljava/util/Map;

    const-string v1, "handleMissingMethod"

    invoke-interface {v0, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/List;

    .line 354
    new-instance v1, Ljava/util/LinkedList;

    invoke-direct {v1}, Ljava/util/LinkedList;-><init>()V

    if-eqz v0, :cond_3

    .line 356
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_0
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_3

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovy/lang/Closure;

    const/4 v3, 0x5

    new-array v3, v3, [Ljava/lang/Object;

    const/4 v4, 0x0

    aput-object p1, v3, v4

    const/4 v4, 0x1

    aput-object p2, v3, v4

    const/4 v4, 0x2

    aput-object p3, v3, v4

    const/4 v4, 0x3

    aput-object p4, v3, v4

    const/4 v4, 0x4

    aput-object p5, v3, v4

    .line 357
    invoke-virtual {p0, v2, v3}, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->safeCall(Lgroovy/lang/Closure;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    if-eqz v2, :cond_0

    .line 359
    instance-of v3, v2, Lorg/codehaus/groovy/ast/MethodNode;

    if-eqz v3, :cond_1

    .line 360
    check-cast v2, Lorg/codehaus/groovy/ast/MethodNode;

    invoke-interface {v1, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    .line 361
    :cond_1
    instance-of v3, v2, Ljava/util/Collection;

    if-eqz v3, :cond_2

    .line 362
    check-cast v2, Ljava/util/Collection;

    invoke-interface {v1, v2}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    goto :goto_0

    .line 364
    :cond_2
    new-instance p1, Lorg/codehaus/groovy/GroovyBugError;

    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string p3, "Type checking extension returned unexpected method list: "

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-direct {p1, p2}, Lorg/codehaus/groovy/GroovyBugError;-><init>(Ljava/lang/String;)V

    throw p1

    :cond_3
    return-object v1
.end method

.method public handleUnresolvedAttribute(Lorg/codehaus/groovy/ast/expr/AttributeExpression;)Z
    .locals 4

    const/4 v0, 0x0

    .line 272
    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->setHandled(Z)V

    .line 273
    iget-object v1, p0, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->eventHandlers:Ljava/util/Map;

    const-string v2, "handleUnresolvedAttribute"

    invoke-interface {v1, v2}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/util/List;

    if-eqz v1, :cond_0

    .line 275
    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_0

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovy/lang/Closure;

    const/4 v3, 0x1

    new-array v3, v3, [Ljava/lang/Object;

    aput-object p1, v3, v0

    .line 276
    invoke-virtual {p0, v2, v3}, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->safeCall(Lgroovy/lang/Closure;[Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    .line 279
    :cond_0
    iget-boolean p1, p0, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->handled:Z

    return p1
.end method

.method public handleUnresolvedProperty(Lorg/codehaus/groovy/ast/expr/PropertyExpression;)Z
    .locals 4

    const/4 v0, 0x0

    .line 260
    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->setHandled(Z)V

    .line 261
    iget-object v1, p0, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->eventHandlers:Ljava/util/Map;

    const-string v2, "handleUnresolvedProperty"

    invoke-interface {v1, v2}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/util/List;

    if-eqz v1, :cond_0

    .line 263
    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_0

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovy/lang/Closure;

    const/4 v3, 0x1

    new-array v3, v3, [Ljava/lang/Object;

    aput-object p1, v3, v0

    .line 264
    invoke-virtual {p0, v2, v3}, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->safeCall(Lgroovy/lang/Closure;[Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    .line 267
    :cond_0
    iget-boolean p1, p0, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->handled:Z

    return p1
.end method

.method public handleUnresolvedVariableExpression(Lorg/codehaus/groovy/ast/expr/VariableExpression;)Z
    .locals 4

    const/4 v0, 0x0

    .line 248
    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->setHandled(Z)V

    .line 249
    iget-object v1, p0, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->eventHandlers:Ljava/util/Map;

    const-string v2, "handleUnresolvedVariableExpression"

    invoke-interface {v1, v2}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/util/List;

    if-eqz v1, :cond_0

    .line 251
    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_0

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovy/lang/Closure;

    const/4 v3, 0x1

    new-array v3, v3, [Ljava/lang/Object;

    aput-object p1, v3, v0

    .line 252
    invoke-virtual {p0, v2, v3}, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->safeCall(Lgroovy/lang/Closure;[Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    .line 255
    :cond_0
    iget-boolean p1, p0, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->handled:Z

    return p1
.end method

.method public onMethodSelection(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/MethodNode;)V
    .locals 4

    .line 216
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->eventHandlers:Ljava/util/Map;

    const-string v1, "onMethodSelection"

    invoke-interface {v0, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/List;

    if-eqz v0, :cond_0

    .line 218
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovy/lang/Closure;

    const/4 v2, 0x2

    new-array v2, v2, [Ljava/lang/Object;

    const/4 v3, 0x0

    aput-object p1, v2, v3

    const/4 v3, 0x1

    aput-object p2, v2, v3

    .line 219
    invoke-virtual {p0, v1, v2}, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->safeCall(Lgroovy/lang/Closure;[Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    :cond_0
    return-void
.end method

.method public setDebug(Z)V
    .locals 0

    .line 113
    iput-boolean p1, p0, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->debug:Z

    return-void
.end method

.method public setup()V
    .locals 10

    const-string v0, "Static type checking extension \'"

    .line 118
    new-instance v1, Lorg/codehaus/groovy/control/CompilerConfiguration;

    invoke-direct {v1}, Lorg/codehaus/groovy/control/CompilerConfiguration;-><init>()V

    const-string v2, "org.codehaus.groovy.transform.stc.GroovyTypeCheckingExtensionSupport.TypeCheckingDSL"

    .line 119
    invoke-virtual {v1, v2}, Lorg/codehaus/groovy/control/CompilerConfiguration;->setScriptBaseClass(Ljava/lang/String;)V

    .line 120
    new-instance v2, Lorg/codehaus/groovy/control/customizers/ImportCustomizer;

    invoke-direct {v2}, Lorg/codehaus/groovy/control/customizers/ImportCustomizer;-><init>()V

    const-string v3, "org.codehaus.groovy.ast.expr"

    .line 121
    filled-new-array {v3}, [Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Lorg/codehaus/groovy/control/customizers/ImportCustomizer;->addStarImports([Ljava/lang/String;)Lorg/codehaus/groovy/control/customizers/ImportCustomizer;

    const-string v3, "org.codehaus.groovy.ast.ClassHelper"

    .line 122
    filled-new-array {v3}, [Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Lorg/codehaus/groovy/control/customizers/ImportCustomizer;->addStaticStars([Ljava/lang/String;)Lorg/codehaus/groovy/control/customizers/ImportCustomizer;

    const-string v3, "org.codehaus.groovy.transform.stc.StaticTypeCheckingSupport"

    .line 123
    filled-new-array {v3}, [Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Lorg/codehaus/groovy/control/customizers/ImportCustomizer;->addStaticStars([Ljava/lang/String;)Lorg/codehaus/groovy/control/customizers/ImportCustomizer;

    const/4 v3, 0x1

    new-array v4, v3, [Lorg/codehaus/groovy/control/customizers/CompilationCustomizer;

    const/4 v5, 0x0

    aput-object v2, v4, v5

    .line 124
    invoke-virtual {v1, v4}, Lorg/codehaus/groovy/control/CompilerConfiguration;->addCompilationCustomizers([Lorg/codehaus/groovy/control/customizers/CompilationCustomizer;)Lorg/codehaus/groovy/control/CompilerConfiguration;

    .line 125
    iget-object v2, p0, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->compilationUnit:Lorg/codehaus/groovy/control/CompilationUnit;

    if-eqz v2, :cond_0

    invoke-virtual {v2}, Lorg/codehaus/groovy/control/CompilationUnit;->getTransformLoader()Lgroovy/lang/GroovyClassLoader;

    move-result-object v2

    goto :goto_0

    :cond_0
    iget-object v2, p0, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->typeCheckingVisitor:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;

    invoke-virtual {v2}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->getSourceUnit()Lorg/codehaus/groovy/control/SourceUnit;

    move-result-object v2

    invoke-virtual {v2}, Lorg/codehaus/groovy/control/SourceUnit;->getClassLoader()Lgroovy/lang/GroovyClassLoader;

    move-result-object v2

    :goto_0
    const/4 v4, 0x0

    .line 130
    :try_start_0
    iget-object v6, p0, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->scriptPath:Ljava/lang/String;

    invoke-virtual {v2, v6, v5, v3}, Lgroovy/lang/GroovyClassLoader;->loadClass(Ljava/lang/String;ZZ)Ljava/lang/Class;

    move-result-object v6

    .line 131
    const-class v7, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport$TypeCheckingDSL;

    invoke-virtual {v7, v6}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    move-result v7

    if-eqz v7, :cond_1

    new-array v3, v5, [Ljava/lang/Class;

    .line 132
    invoke-virtual {v6, v3}, Ljava/lang/Class;->getDeclaredConstructor([Ljava/lang/Class;)Ljava/lang/reflect/Constructor;

    move-result-object v3

    new-array v6, v5, [Ljava/lang/Object;

    invoke-virtual {v3, v6}, Ljava/lang/reflect/Constructor;->newInstance([Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport$TypeCheckingDSL;

    move-object v4, v3

    goto :goto_1

    .line 133
    :cond_1
    const-class v7, Lorg/codehaus/groovy/transform/stc/TypeCheckingExtension;

    invoke-virtual {v7, v6}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    move-result v7
    :try_end_0
    .catch Ljava/lang/ClassNotFoundException; {:try_start_0 .. :try_end_0} :catch_3
    .catch Ljava/lang/NoSuchMethodException; {:try_start_0 .. :try_end_0} :catch_3
    .catch Ljava/lang/InstantiationException; {:try_start_0 .. :try_end_0} :catch_2
    .catch Ljava/lang/IllegalAccessException; {:try_start_0 .. :try_end_0} :catch_2
    .catch Ljava/lang/reflect/InvocationTargetException; {:try_start_0 .. :try_end_0} :catch_2

    if-eqz v7, :cond_2

    :try_start_1
    new-array v7, v3, [Ljava/lang/Class;

    .line 136
    const-class v8, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;

    aput-object v8, v7, v5

    invoke-virtual {v6, v7}, Ljava/lang/Class;->getDeclaredConstructor([Ljava/lang/Class;)Ljava/lang/reflect/Constructor;

    move-result-object v6

    new-array v3, v3, [Ljava/lang/Object;

    .line 137
    iget-object v7, p0, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->typeCheckingVisitor:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;

    aput-object v7, v3, v5

    invoke-virtual {v6, v3}, Ljava/lang/reflect/Constructor;->newInstance([Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lorg/codehaus/groovy/transform/stc/TypeCheckingExtension;

    .line 138
    iget-object v6, p0, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->typeCheckingVisitor:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;

    invoke-virtual {v6, v3}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->addTypeCheckingExtension(Lorg/codehaus/groovy/transform/stc/TypeCheckingExtension;)V

    .line 139
    invoke-virtual {v3}, Lorg/codehaus/groovy/transform/stc/TypeCheckingExtension;->setup()V
    :try_end_1
    .catch Ljava/lang/InstantiationException; {:try_start_1 .. :try_end_1} :catch_1
    .catch Ljava/lang/reflect/InvocationTargetException; {:try_start_1 .. :try_end_1} :catch_1
    .catch Ljava/lang/IllegalAccessException; {:try_start_1 .. :try_end_1} :catch_1
    .catch Ljava/lang/NoSuchMethodException; {:try_start_1 .. :try_end_1} :catch_0
    .catch Ljava/lang/ClassNotFoundException; {:try_start_1 .. :try_end_1} :catch_3

    return-void

    .line 144
    :catch_0
    :try_start_2
    iget-object v3, p0, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->context:Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;

    invoke-virtual {v3}, Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;->getErrorCollector()Lorg/codehaus/groovy/control/ErrorCollector;

    move-result-object v3

    new-instance v6, Lorg/codehaus/groovy/control/messages/SimpleMessage;

    new-instance v7, Ljava/lang/StringBuilder;

    invoke-direct {v7}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v7, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v7

    iget-object v8, p0, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->scriptPath:Ljava/lang/String;

    invoke-virtual {v7, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v7

    const-string v8, "\' could not be loaded because it doesn\'t have a constructor accepting StaticTypeCheckingVisitor."

    invoke-virtual {v7, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v7

    invoke-virtual {v7}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v7

    .line 146
    invoke-virtual {v1}, Lorg/codehaus/groovy/control/CompilerConfiguration;->getDebug()Z

    move-result v8

    invoke-static {v8}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v8

    iget-object v9, p0, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->typeCheckingVisitor:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;

    invoke-virtual {v9}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->getSourceUnit()Lorg/codehaus/groovy/control/SourceUnit;

    move-result-object v9

    invoke-direct {v6, v7, v8, v9}, Lorg/codehaus/groovy/control/messages/SimpleMessage;-><init>(Ljava/lang/String;Ljava/lang/Object;Lorg/codehaus/groovy/control/ProcessingUnit;)V

    .line 144
    invoke-virtual {v3, v6}, Lorg/codehaus/groovy/control/ErrorCollector;->addFatalError(Lorg/codehaus/groovy/control/messages/Message;)V

    goto :goto_1

    .line 142
    :catch_1
    invoke-direct {p0, v1}, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->addLoadingError(Lorg/codehaus/groovy/control/CompilerConfiguration;)V
    :try_end_2
    .catch Ljava/lang/ClassNotFoundException; {:try_start_2 .. :try_end_2} :catch_3
    .catch Ljava/lang/NoSuchMethodException; {:try_start_2 .. :try_end_2} :catch_3
    .catch Ljava/lang/InstantiationException; {:try_start_2 .. :try_end_2} :catch_2
    .catch Ljava/lang/IllegalAccessException; {:try_start_2 .. :try_end_2} :catch_2
    .catch Ljava/lang/reflect/InvocationTargetException; {:try_start_2 .. :try_end_2} :catch_2

    goto :goto_1

    .line 153
    :catch_2
    invoke-direct {p0, v1}, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->addLoadingError(Lorg/codehaus/groovy/control/CompilerConfiguration;)V

    :catch_3
    :cond_2
    :goto_1
    if-nez v4, :cond_6

    .line 156
    iget-object v3, p0, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->typeCheckingVisitor:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;

    invoke-virtual {v3}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->getSourceUnit()Lorg/codehaus/groovy/control/SourceUnit;

    move-result-object v3

    invoke-virtual {v3}, Lorg/codehaus/groovy/control/SourceUnit;->getClassLoader()Lgroovy/lang/GroovyClassLoader;

    move-result-object v3

    .line 158
    iget-object v4, p0, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->scriptPath:Ljava/lang/String;

    invoke-virtual {v2, v4}, Ljava/lang/ClassLoader;->getResourceAsStream(Ljava/lang/String;)Ljava/io/InputStream;

    move-result-object v4

    if-nez v4, :cond_3

    .line 161
    iget-object v4, p0, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->scriptPath:Ljava/lang/String;

    invoke-virtual {v3, v4}, Ljava/lang/ClassLoader;->getResourceAsStream(Ljava/lang/String;)Ljava/io/InputStream;

    move-result-object v4

    :cond_3
    if-nez v4, :cond_4

    .line 165
    const-class v3, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;

    invoke-virtual {v3}, Ljava/lang/Class;->getClassLoader()Ljava/lang/ClassLoader;

    move-result-object v3

    .line 166
    iget-object v4, p0, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->scriptPath:Ljava/lang/String;

    invoke-virtual {v3, v4}, Ljava/lang/ClassLoader;->getResourceAsStream(Ljava/lang/String;)Ljava/io/InputStream;

    move-result-object v4

    :cond_4
    if-nez v4, :cond_5

    .line 170
    iget-object v3, p0, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->context:Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;

    invoke-virtual {v3}, Lorg/codehaus/groovy/transform/stc/TypeCheckingContext;->getErrorCollector()Lorg/codehaus/groovy/control/ErrorCollector;

    move-result-object v3

    new-instance v6, Lorg/codehaus/groovy/control/messages/SimpleMessage;

    new-instance v7, Ljava/lang/StringBuilder;

    invoke-direct {v7}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v7, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v7, p0, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->scriptPath:Ljava/lang/String;

    invoke-virtual {v0, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v7, "\' was not found on the classpath."

    invoke-virtual {v0, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    .line 172
    invoke-virtual {v1}, Lorg/codehaus/groovy/control/CompilerConfiguration;->getDebug()Z

    move-result v7

    invoke-static {v7}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v7

    iget-object v8, p0, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->typeCheckingVisitor:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;

    invoke-virtual {v8}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->getSourceUnit()Lorg/codehaus/groovy/control/SourceUnit;

    move-result-object v8

    invoke-direct {v6, v0, v7, v8}, Lorg/codehaus/groovy/control/messages/SimpleMessage;-><init>(Ljava/lang/String;Ljava/lang/Object;Lorg/codehaus/groovy/control/ProcessingUnit;)V

    .line 170
    invoke-virtual {v3, v6}, Lorg/codehaus/groovy/control/ErrorCollector;->addFatalError(Lorg/codehaus/groovy/control/messages/Message;)V

    .line 175
    :cond_5
    :try_start_3
    new-instance v0, Lgroovy/lang/GroovyShell;

    new-instance v3, Lgroovy/lang/Binding;

    invoke-direct {v3}, Lgroovy/lang/Binding;-><init>()V

    invoke-direct {v0, v2, v3, v1}, Lgroovy/lang/GroovyShell;-><init>(Ljava/lang/ClassLoader;Lgroovy/lang/Binding;Lorg/codehaus/groovy/control/CompilerConfiguration;)V

    .line 176
    new-instance v1, Ljava/io/InputStreamReader;

    iget-object v2, p0, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->typeCheckingVisitor:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;

    .line 177
    invoke-virtual {v2}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->getSourceUnit()Lorg/codehaus/groovy/control/SourceUnit;

    move-result-object v2

    invoke-virtual {v2}, Lorg/codehaus/groovy/control/SourceUnit;->getConfiguration()Lorg/codehaus/groovy/control/CompilerConfiguration;

    move-result-object v2

    invoke-virtual {v2}, Lorg/codehaus/groovy/control/CompilerConfiguration;->getSourceEncoding()Ljava/lang/String;

    move-result-object v2

    invoke-direct {v1, v4, v2}, Ljava/io/InputStreamReader;-><init>(Ljava/io/InputStream;Ljava/lang/String;)V

    .line 176
    invoke-virtual {v0, v1}, Lgroovy/lang/GroovyShell;->parse(Ljava/io/Reader;)Lgroovy/lang/Script;

    move-result-object v0

    move-object v4, v0

    check-cast v4, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport$TypeCheckingDSL;
    :try_end_3
    .catch Lorg/codehaus/groovy/control/CompilationFailedException; {:try_start_3 .. :try_end_3} :catch_5
    .catch Ljava/io/UnsupportedEncodingException; {:try_start_3 .. :try_end_3} :catch_4

    goto :goto_2

    :catch_4
    move-exception v0

    .line 182
    new-instance v1, Lorg/codehaus/groovy/GroovyBugError;

    const-string v2, "Unsupported encoding found in compiler configuration"

    invoke-direct {v1, v2, v0}, Lorg/codehaus/groovy/GroovyBugError;-><init>(Ljava/lang/String;Ljava/lang/Exception;)V

    throw v1

    :catch_5
    move-exception v0

    .line 180
    new-instance v1, Lorg/codehaus/groovy/GroovyBugError;

    const-string v2, "An unexpected error was thrown during custom type checking"

    invoke-direct {v1, v2, v0}, Lorg/codehaus/groovy/GroovyBugError;-><init>(Ljava/lang/String;Ljava/lang/Exception;)V

    throw v1

    :cond_6
    :goto_2
    if-eqz v4, :cond_7

    .line 186
    invoke-static {v4, p0}, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport$TypeCheckingDSL;->access$002(Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport$TypeCheckingDSL;Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;)Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;

    .line 187
    invoke-virtual {v4}, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport$TypeCheckingDSL;->run()Ljava/lang/Object;

    .line 188
    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->eventHandlers:Ljava/util/Map;

    const-string v1, "setup"

    invoke-interface {v0, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/List;

    if-eqz v0, :cond_7

    .line 190
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_3
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_7

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovy/lang/Closure;

    new-array v2, v5, [Ljava/lang/Object;

    .line 191
    invoke-virtual {p0, v1, v2}, Lorg/codehaus/groovy/transform/stc/GroovyTypeCheckingExtensionSupport;->safeCall(Lgroovy/lang/Closure;[Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_3

    :cond_7
    return-void
.end method
