.class public final synthetic Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda43;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Predicate;


# instance fields
.field public final synthetic f$0:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;


# direct methods
.method public synthetic constructor <init>(Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda43;->f$0:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;

    return-void
.end method


# virtual methods
.method public final test(Ljava/lang/Object;)Z
    .locals 1

    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda43;->f$0:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;

    check-cast p1, Lorg/codehaus/groovy/ast/MethodNode;

    invoke-static {v0, p1}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->$r8$lambda$TF8ZkpiZ5Xkdt5EDcghnhx0XUvk(Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;Lorg/codehaus/groovy/ast/MethodNode;)Z

    move-result p1

    return p1
.end method
