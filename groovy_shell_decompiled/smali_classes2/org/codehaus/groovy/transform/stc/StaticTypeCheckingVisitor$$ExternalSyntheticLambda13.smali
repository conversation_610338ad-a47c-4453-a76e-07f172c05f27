.class public final synthetic Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda13;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Function;


# instance fields
.field public final synthetic f$0:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$SetterInfo;


# direct methods
.method public synthetic constructor <init>(Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$SetterInfo;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda13;->f$0:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$SetterInfo;

    return-void
.end method


# virtual methods
.method public final apply(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda13;->f$0:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$SetterInfo;

    check-cast p1, Lorg/codehaus/groovy/ast/MethodNode;

    invoke-static {v0, p1}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->lambda$ensureValidSetter$4(Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$SetterInfo;Lorg/codehaus/groovy/ast/MethodNode;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    return-object p1
.end method
