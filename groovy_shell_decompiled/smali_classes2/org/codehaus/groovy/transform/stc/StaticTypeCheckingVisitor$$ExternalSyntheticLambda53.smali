.class public final synthetic Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda53;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Supplier;


# instance fields
.field public final synthetic f$0:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;


# direct methods
.method public synthetic constructor <init>(Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda53;->f$0:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;

    return-void
.end method


# virtual methods
.method public final get()Ljava/lang/Object;
    .locals 1

    iget-object v0, p0, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor$$ExternalSyntheticLambda53;->f$0:Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;

    invoke-virtual {v0}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->lambda$getTransformLoader$21$org-codehaus-groovy-transform-stc-StaticTypeCheckingVisitor()Lgroovy/lang/GroovyClassLoader;

    move-result-object v0

    return-object v0
.end method
