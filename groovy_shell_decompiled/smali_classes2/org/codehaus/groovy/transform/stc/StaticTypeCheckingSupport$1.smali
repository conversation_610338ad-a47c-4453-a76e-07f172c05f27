.class Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingSupport$1;
.super Ljava/lang/Object;
.source "StaticTypeCheckingSupport.java"

# interfaces
.implements Ljava/util/Comparator;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingSupport;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ljava/util/Comparator<",
        "Lorg/codehaus/groovy/ast/MethodNode;",
        ">;"
    }
.end annotation


# direct methods
.method constructor <init>()V
    .locals 0

    .line 219
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public bridge synthetic compare(Ljava/lang/Object;Ljava/lang/Object;)I
    .locals 0

    .line 219
    check-cast p1, Lorg/codehaus/groovy/ast/MethodNode;

    check-cast p2, Lorg/codehaus/groovy/ast/MethodNode;

    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingSupport$1;->compare(Lorg/codehaus/groovy/ast/MethodNode;Lorg/codehaus/groovy/ast/MethodNode;)I

    move-result p1

    return p1
.end method

.method public compare(Lorg/codehaus/groovy/ast/MethodNode;Lorg/codehaus/groovy/ast/MethodNode;)I
    .locals 7

    .line 222
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getName()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/MethodNode;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    const/4 v1, 0x1

    if-eqz v0, :cond_3

    .line 223
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v0

    .line 224
    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/MethodNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v2

    .line 225
    array-length v3, v0

    array-length v4, v2

    if-ne v3, v4, :cond_2

    const/4 v3, 0x0

    move v5, v1

    move v4, v3

    .line 227
    :goto_0
    array-length v6, v0

    if-ge v4, v6, :cond_0

    if-eqz v5, :cond_0

    .line 228
    aget-object v5, v0, v4

    invoke-virtual {v5}, Lorg/codehaus/groovy/ast/Parameter;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v5

    aget-object v6, v2, v4

    invoke-virtual {v6}, Lorg/codehaus/groovy/ast/Parameter;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v6

    invoke-virtual {v5, v6}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result v5

    add-int/lit8 v4, v4, 0x1

    goto :goto_0

    :cond_0
    if-eqz v5, :cond_3

    .line 231
    instance-of v0, p1, Lorg/codehaus/groovy/transform/stc/ExtensionMethodNode;

    if-eqz v0, :cond_1

    instance-of v0, p2, Lorg/codehaus/groovy/transform/stc/ExtensionMethodNode;

    if-eqz v0, :cond_1

    .line 232
    check-cast p1, Lorg/codehaus/groovy/transform/stc/ExtensionMethodNode;

    invoke-virtual {p1}, Lorg/codehaus/groovy/transform/stc/ExtensionMethodNode;->getExtensionMethodNode()Lorg/codehaus/groovy/ast/MethodNode;

    move-result-object p1

    check-cast p2, Lorg/codehaus/groovy/transform/stc/ExtensionMethodNode;

    invoke-virtual {p2}, Lorg/codehaus/groovy/transform/stc/ExtensionMethodNode;->getExtensionMethodNode()Lorg/codehaus/groovy/ast/MethodNode;

    move-result-object p2

    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingSupport$1;->compare(Lorg/codehaus/groovy/ast/MethodNode;Lorg/codehaus/groovy/ast/MethodNode;)I

    move-result p1

    return p1

    :cond_1
    return v3

    .line 237
    :cond_2
    array-length p1, v0

    array-length p2, v2

    sub-int/2addr p1, p2

    return p1

    :cond_3
    return v1
.end method
