.class Lorg/codehaus/groovy/transform/trait/TraitASTTransformation$PostTypeCheckingExpressionReplacer;
.super Lorg/codehaus/groovy/ast/ClassCodeExpressionTransformer;
.source "TraitASTTransformation.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/codehaus/groovy/transform/trait/TraitASTTransformation;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0xa
    name = "PostTypeCheckingExpressionReplacer"
.end annotation


# instance fields
.field private final sourceUnit:Lorg/codehaus/groovy/control/SourceUnit;


# direct methods
.method constructor <init>(Lorg/codehaus/groovy/control/SourceUnit;)V
    .locals 0

    .line 644
    invoke-direct {p0}, Lorg/codehaus/groovy/ast/ClassCodeExpressionTransformer;-><init>()V

    .line 645
    iput-object p1, p0, Lorg/codehaus/groovy/transform/trait/TraitASTTransformation$PostTypeCheckingExpressionReplacer;->sourceUnit:Lorg/codehaus/groovy/control/SourceUnit;

    return-void
.end method


# virtual methods
.method protected getSourceUnit()Lorg/codehaus/groovy/control/SourceUnit;
    .locals 1

    .line 650
    iget-object v0, p0, Lorg/codehaus/groovy/transform/trait/TraitASTTransformation$PostTypeCheckingExpressionReplacer;->sourceUnit:Lorg/codehaus/groovy/control/SourceUnit;

    return-object v0
.end method

.method public transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 1

    if-eqz p1, :cond_0

    .line 656
    sget-object v0, Lorg/codehaus/groovy/transform/trait/TraitASTTransformation;->POST_TYPECHECKING_REPLACEMENT:Ljava/lang/String;

    invoke-virtual {p1, v0}, Lorg/codehaus/groovy/ast/expr/Expression;->getNodeMetaData(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/ast/expr/Expression;

    if-eqz v0, :cond_0

    return-object v0

    .line 661
    :cond_0
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeExpressionTransformer;->transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    return-object p1
.end method
