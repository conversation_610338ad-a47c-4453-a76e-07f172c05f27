.class Lorg/codehaus/groovy/transform/trait/TraitASTTransformation$DefaultArgsMethodsAdder;
.super Lorg/codehaus/groovy/classgen/Verifier;
.source "TraitASTTransformation.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/codehaus/groovy/transform/trait/TraitASTTransformation;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0xa
    name = "DefaultArgsMethodsAdder"
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 632
    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/Verifier;-><init>()V

    return-void
.end method

.method synthetic constructor <init>(Lorg/codehaus/groovy/transform/trait/TraitASTTransformation$1;)V
    .locals 0

    .line 632
    invoke-direct {p0}, Lorg/codehaus/groovy/transform/trait/TraitASTTransformation$DefaultArgsMethodsAdder;-><init>()V

    return-void
.end method


# virtual methods
.method public addDefaultParameterMethods(Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 0

    .line 636
    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/transform/trait/TraitASTTransformation$DefaultArgsMethodsAdder;->setClassNode(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 637
    invoke-super {p0, p1}, Lorg/codehaus/groovy/classgen/Verifier;->addDefaultParameterMethods(Lorg/codehaus/groovy/ast/ClassNode;)V

    return-void
.end method
