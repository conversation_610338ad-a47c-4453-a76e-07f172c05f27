.class public final synthetic Lorg/codehaus/groovy/transform/trait/TraitComposer$$ExternalSyntheticLambda0;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Function;


# static fields
.field public static final synthetic INSTANCE:Lorg/codehaus/groovy/transform/trait/TraitComposer$$ExternalSyntheticLambda0;


# direct methods
.method static synthetic constructor <clinit>()V
    .locals 1

    new-instance v0, Lorg/codehaus/groovy/transform/trait/TraitComposer$$ExternalSyntheticLambda0;

    invoke-direct {v0}, Lorg/codehaus/groovy/transform/trait/TraitComposer$$ExternalSyntheticLambda0;-><init>()V

    sput-object v0, Lorg/codehaus/groovy/transform/trait/TraitComposer$$ExternalSyntheticLambda0;->INSTANCE:Lorg/codehaus/groovy/transform/trait/TraitComposer$$ExternalSyntheticLambda0;

    return-void
.end method

.method private synthetic constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final apply(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    check-cast p1, Lorg/codehaus/groovy/ast/Parameter;

    invoke-static {p1}, Lorg/codehaus/groovy/transform/trait/TraitComposer;->lambda$createSuperFallback$0(Lorg/codehaus/groovy/ast/Parameter;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object p1

    return-object p1
.end method
