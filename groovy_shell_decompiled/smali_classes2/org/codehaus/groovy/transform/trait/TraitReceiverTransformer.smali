.class Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;
.super Lorg/codehaus/groovy/ast/ClassCodeExpressionTransformer;
.source "TraitReceiverTransformer.java"


# instance fields
.field private final fieldHelper:Lorg/codehaus/groovy/ast/ClassNode;

.field private inClosure:Z

.field private final knownFields:Ljava/util/Collection;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Collection<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field private final traitClass:Lorg/codehaus/groovy/ast/ClassNode;

.field private final traitHelperClass:Lorg/codehaus/groovy/ast/ClassNode;

.field private final unit:Lorg/codehaus/groovy/control/SourceUnit;

.field private final weaved:Lorg/codehaus/groovy/ast/expr/VariableExpression;


# direct methods
.method public constructor <init>(Lorg/codehaus/groovy/ast/expr/VariableExpression;Lorg/codehaus/groovy/control/SourceUnit;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/Collection;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/expr/VariableExpression;",
            "Lorg/codehaus/groovy/control/SourceUnit;",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            "Ljava/util/Collection<",
            "Ljava/lang/String;",
            ">;)V"
        }
    .end annotation

    .line 83
    invoke-direct {p0}, Lorg/codehaus/groovy/ast/ClassCodeExpressionTransformer;-><init>()V

    .line 84
    iput-object p1, p0, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->weaved:Lorg/codehaus/groovy/ast/expr/VariableExpression;

    .line 85
    iput-object p2, p0, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->unit:Lorg/codehaus/groovy/control/SourceUnit;

    .line 86
    iput-object p3, p0, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->traitClass:Lorg/codehaus/groovy/ast/ClassNode;

    .line 87
    iput-object p4, p0, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->traitHelperClass:Lorg/codehaus/groovy/ast/ClassNode;

    .line 88
    iput-object p5, p0, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->fieldHelper:Lorg/codehaus/groovy/ast/ClassNode;

    .line 89
    iput-object p6, p0, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->knownFields:Ljava/util/Collection;

    return-void
.end method

.method private asClass(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 2

    .line 352
    sget-object v0, Lorg/codehaus/groovy/ast/ClassHelper;->CLASS_Type:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->getPlainNodeReference()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    .line 353
    invoke-static {p1, v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->isInstanceOfX(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/BooleanExpression;

    move-result-object v0

    const-string v1, "getClass"

    invoke-static {p1, v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object v1

    invoke-static {v0, p1, v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ternaryX(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/TernaryExpression;

    move-result-object p1

    return-object p1
.end method

.method private createArgumentList(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;
    .locals 1

    .line 335
    new-instance v0, Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    invoke-direct {v0}, Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;-><init>()V

    .line 336
    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;->addExpression(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/TupleExpression;

    .line 337
    instance-of p1, p2, Lorg/codehaus/groovy/ast/expr/TupleExpression;

    if-eqz p1, :cond_0

    .line 338
    check-cast p2, Lorg/codehaus/groovy/ast/expr/TupleExpression;

    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/expr/TupleExpression;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result p2

    if-eqz p2, :cond_1

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lorg/codehaus/groovy/ast/expr/Expression;

    .line 339
    invoke-virtual {p0, p2}, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p2

    invoke-virtual {v0, p2}, Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;->addExpression(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/TupleExpression;

    goto :goto_0

    .line 342
    :cond_0
    invoke-virtual {p0, p2}, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;->addExpression(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/TupleExpression;

    :cond_1
    return-object v0
.end method

.method private createFieldHelperReceiver()Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 2

    .line 348
    iget-object v0, p0, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->weaved:Lorg/codehaus/groovy/ast/expr/VariableExpression;

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->getOriginType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    sget-object v1, Lorg/codehaus/groovy/ast/ClassHelper;->CLASS_Type:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v0, v1}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->weaved:Lorg/codehaus/groovy/ast/expr/VariableExpression;

    goto :goto_0

    :cond_0
    iget-object v0, p0, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->fieldHelper:Lorg/codehaus/groovy/ast/ClassNode;

    iget-object v1, p0, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->weaved:Lorg/codehaus/groovy/ast/expr/VariableExpression;

    invoke-static {v0, v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->castX(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/CastExpression;

    move-result-object v0

    :goto_0
    return-object v0
.end method

.method private static markDynamicCall(Lorg/codehaus/groovy/ast/expr/MethodCallExpression;Lorg/codehaus/groovy/ast/FieldNode;Z)V
    .locals 0

    if-eqz p2, :cond_0

    .line 251
    sget-object p2, Lorg/codehaus/groovy/transform/trait/TraitASTTransformation;->DO_DYNAMIC:Ljava/lang/String;

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/FieldNode;->getOriginType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    invoke-virtual {p0, p2, p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->putNodeMetaData(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_0
    return-void
.end method

.method private throwSuperError(Lorg/codehaus/groovy/ast/ASTNode;)V
    .locals 4

    .line 268
    iget-object v0, p0, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->unit:Lorg/codehaus/groovy/control/SourceUnit;

    new-instance v1, Lorg/codehaus/groovy/syntax/SyntaxException;

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ASTNode;->getLineNumber()I

    move-result v2

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ASTNode;->getColumnNumber()I

    move-result p1

    const-string v3, "Call to super is not allowed in a trait"

    invoke-direct {v1, v3, v2, p1}, Lorg/codehaus/groovy/syntax/SyntaxException;-><init>(Ljava/lang/String;II)V

    invoke-virtual {v0, v1}, Lorg/codehaus/groovy/control/SourceUnit;->addError(Lorg/codehaus/groovy/syntax/SyntaxException;)V

    return-void
.end method

.method private transformBinaryExpression(Lorg/codehaus/groovy/ast/expr/BinaryExpression;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 12

    .line 187
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/BinaryExpression;->getLeftExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    .line 188
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/BinaryExpression;->getRightExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v1

    .line 189
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/BinaryExpression;->getOperation()Lorg/codehaus/groovy/syntax/Token;

    move-result-object v2

    .line 190
    invoke-virtual {v2}, Lorg/codehaus/groovy/syntax/Token;->getType()I

    move-result v3

    const/16 v4, 0x64

    if-ne v3, v4, :cond_a

    const/4 v3, 0x0

    .line 192
    instance-of v4, v0, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    if-eqz v4, :cond_0

    move-object v4, v0

    check-cast v4, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->getAccessedVariable()Lorg/codehaus/groovy/ast/Variable;

    move-result-object v5

    instance-of v5, v5, Lorg/codehaus/groovy/ast/FieldNode;

    if-eqz v5, :cond_0

    .line 193
    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->getAccessedVariable()Lorg/codehaus/groovy/ast/Variable;

    move-result-object v3

    invoke-interface {v3}, Lorg/codehaus/groovy/ast/Variable;->getName()Ljava/lang/String;

    move-result-object v3

    goto :goto_0

    .line 194
    :cond_0
    instance-of v4, v0, Lorg/codehaus/groovy/ast/expr/FieldExpression;

    if-eqz v4, :cond_1

    .line 195
    move-object v3, v0

    check-cast v3, Lorg/codehaus/groovy/ast/expr/FieldExpression;

    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/expr/FieldExpression;->getFieldName()Ljava/lang/String;

    move-result-object v3

    goto :goto_0

    .line 196
    :cond_1
    instance-of v4, v0, Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    if-eqz v4, :cond_3

    move-object v4, v0

    check-cast v4, Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    .line 197
    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->isImplicitThis()Z

    move-result v5

    if-nez v5, :cond_2

    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->getObjectExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v5

    invoke-virtual {v5}, Lorg/codehaus/groovy/ast/expr/Expression;->getText()Ljava/lang/String;

    move-result-object v5

    const-string v6, "this"

    invoke-virtual {v6, v5}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v5

    if-eqz v5, :cond_3

    .line 198
    :cond_2
    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->getPropertyAsString()Ljava/lang/String;

    move-result-object v3

    :cond_3
    :goto_0
    if-eqz v3, :cond_a

    .line 201
    invoke-static {p2, v3}, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->tryGetFieldNode(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object p1

    .line 202
    iget-object v10, p0, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->fieldHelper:Lorg/codehaus/groovy/ast/ClassNode;

    if-eqz v10, :cond_9

    if-nez p1, :cond_4

    new-instance v11, Lorg/codehaus/groovy/ast/FieldNode;

    const/4 v6, 0x0

    sget-object v7, Lorg/codehaus/groovy/ast/ClassHelper;->OBJECT_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    const/4 v9, 0x0

    move-object v4, v11

    move-object v5, v3

    move-object v8, p2

    invoke-direct/range {v4 .. v9}, Lorg/codehaus/groovy/ast/FieldNode;-><init>(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;)V

    invoke-static {v11}, Lorg/codehaus/groovy/transform/trait/Traits;->helperSetterName(Lorg/codehaus/groovy/ast/FieldNode;)Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v10, v4, v1}, Lorg/codehaus/groovy/ast/ClassNode;->hasPossibleMethod(Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Z

    move-result v4

    if-nez v4, :cond_4

    goto :goto_2

    .line 205
    :cond_4
    invoke-virtual {p2, v3}, Lorg/codehaus/groovy/ast/ClassNode;->getDeclaredField(Ljava/lang/String;)Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object v2

    if-nez v2, :cond_5

    .line 207
    new-instance v2, Lorg/codehaus/groovy/ast/FieldNode;

    const/4 v6, 0x0

    sget-object v7, Lorg/codehaus/groovy/ast/ClassHelper;->OBJECT_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    const/4 v9, 0x0

    move-object v4, v2

    move-object v5, v3

    move-object v8, p2

    invoke-direct/range {v4 .. v9}, Lorg/codehaus/groovy/ast/FieldNode;-><init>(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;)V

    .line 209
    :cond_5
    invoke-direct {p0}, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->createFieldHelperReceiver()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p2

    const/4 v3, 0x0

    if-eqz p1, :cond_6

    .line 210
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/FieldNode;->isStatic()Z

    move-result v4

    if-eqz v4, :cond_6

    const/4 v4, 0x1

    goto :goto_1

    :cond_6
    move v4, v3

    .line 211
    :goto_1
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/FieldNode;->isStatic()Z

    move-result v5

    if-eqz v5, :cond_7

    const-string v5, "class"

    .line 212
    invoke-static {p2, v5}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->propX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    move-result-object p2

    .line 214
    :cond_7
    invoke-static {v2}, Lorg/codehaus/groovy/transform/trait/Traits;->helperSetterName(Lorg/codehaus/groovy/ast/FieldNode;)Ljava/lang/String;

    move-result-object v2

    .line 218
    invoke-super {p0, v1}, Lorg/codehaus/groovy/ast/ClassCodeExpressionTransformer;->transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v1

    .line 215
    invoke-static {p2, v2, v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object p2

    .line 220
    invoke-virtual {p2, v3}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setImplicitThis(Z)V

    .line 221
    instance-of v1, v0, Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    if-eqz v1, :cond_8

    check-cast v0, Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->getProperty()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    :cond_8
    invoke-virtual {p2, v0}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setSourcePosition(Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 222
    invoke-static {p2, p1, v4}, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->markDynamicCall(Lorg/codehaus/groovy/ast/expr/MethodCallExpression;Lorg/codehaus/groovy/ast/FieldNode;Z)V

    return-object p2

    .line 203
    :cond_9
    :goto_2
    iget-object p1, p0, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->weaved:Lorg/codehaus/groovy/ast/expr/VariableExpression;

    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object p1

    invoke-static {p1, v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->propX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    move-result-object p1

    invoke-virtual {p0, v1}, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p2

    invoke-static {p1, v2, p2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->binX(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/syntax/Token;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/BinaryExpression;

    move-result-object p1

    return-object p1

    .line 227
    :cond_a
    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p2

    .line 228
    invoke-virtual {p0, v1}, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    .line 229
    instance-of v1, p1, Lorg/codehaus/groovy/ast/expr/DeclarationExpression;

    if-eqz v1, :cond_b

    .line 230
    new-instance v1, Lorg/codehaus/groovy/ast/expr/DeclarationExpression;

    invoke-direct {v1, p2, v2, v0}, Lorg/codehaus/groovy/ast/expr/DeclarationExpression;-><init>(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/syntax/Token;Lorg/codehaus/groovy/ast/expr/Expression;)V

    goto :goto_3

    :cond_b
    invoke-static {p2, v2, v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->binX(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/syntax/Token;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/BinaryExpression;

    move-result-object v1

    .line 231
    :goto_3
    invoke-virtual {v1, p1}, Lorg/codehaus/groovy/ast/expr/Expression;->setSourcePosition(Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 232
    invoke-virtual {v1, p1}, Lorg/codehaus/groovy/ast/expr/Expression;->copyNodeMetaData(Lorg/codehaus/groovy/ast/ASTNode;)V

    return-object v1
.end method

.method private transformFieldReference(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/FieldNode;Z)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 2

    .line 237
    invoke-direct {p0}, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->createFieldHelperReceiver()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    if-eqz p3, :cond_0

    .line 239
    invoke-direct {p0, v0}, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->asClass(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    .line 242
    :cond_0
    invoke-static {p2}, Lorg/codehaus/groovy/transform/trait/Traits;->helperGetterName(Lorg/codehaus/groovy/ast/FieldNode;)Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object v0

    const/4 v1, 0x0

    .line 243
    invoke-virtual {v0, v1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setImplicitThis(Z)V

    .line 244
    instance-of v1, p1, Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    if-eqz v1, :cond_1

    check-cast p1, Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->getProperty()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    :cond_1
    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setSourcePosition(Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 245
    invoke-static {v0, p2, p3}, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->markDynamicCall(Lorg/codehaus/groovy/ast/expr/MethodCallExpression;Lorg/codehaus/groovy/ast/FieldNode;Z)V

    return-object v0
.end method

.method private transformMethodCallOnThis(Lorg/codehaus/groovy/ast/expr/MethodCallExpression;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 8

    .line 300
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->getMethod()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    .line 301
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->getArguments()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v1

    .line 302
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->getObjectExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v2

    .line 304
    instance-of v3, v0, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    const/4 v4, 0x0

    if-eqz v3, :cond_5

    .line 305
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->getMethodAsString()Ljava/lang/String;

    move-result-object v3

    .line 306
    iget-object v5, p0, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->traitClass:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v5, v3}, Lorg/codehaus/groovy/ast/ClassNode;->getMethods(Ljava/lang/String;)Ljava/util/List;

    move-result-object v5

    invoke-interface {v5}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v5

    :cond_0
    invoke-interface {v5}, Ljava/util/Iterator;->hasNext()Z

    move-result v6

    if-eqz v6, :cond_5

    invoke-interface {v5}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Lorg/codehaus/groovy/ast/MethodNode;

    .line 307
    invoke-virtual {v6}, Lorg/codehaus/groovy/ast/MethodNode;->getName()Ljava/lang/String;

    move-result-object v7

    invoke-virtual {v3, v7}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v7

    if-eqz v7, :cond_0

    invoke-virtual {v6}, Lorg/codehaus/groovy/ast/MethodNode;->isStatic()Z

    move-result v7

    if-nez v7, :cond_1

    invoke-virtual {v6}, Lorg/codehaus/groovy/ast/MethodNode;->isPrivate()Z

    move-result v7

    if-eqz v7, :cond_0

    .line 309
    :cond_1
    iget-boolean v0, p0, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->inClosure:Z

    if-nez v0, :cond_2

    invoke-virtual {v6}, Lorg/codehaus/groovy/ast/MethodNode;->isStatic()Z

    move-result v0

    if-eqz v0, :cond_2

    .line 310
    iget-object v0, p0, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->weaved:Lorg/codehaus/groovy/ast/expr/VariableExpression;

    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v0

    invoke-virtual {p0, v1}, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v1

    invoke-static {v0, v3, v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object v0

    .line 311
    invoke-virtual {v0, v4}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setImplicitThis(Z)V

    .line 312
    invoke-virtual {v0, v4}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setSafe(Z)V

    goto :goto_1

    .line 314
    :cond_2
    invoke-virtual {v6}, Lorg/codehaus/groovy/ast/MethodNode;->isStatic()Z

    move-result v0

    if-eqz v0, :cond_3

    const-string v0, "this"

    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v0

    invoke-direct {p0, v0}, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->asClass(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    goto :goto_0

    :cond_3
    iget-object v0, p0, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->weaved:Lorg/codehaus/groovy/ast/expr/VariableExpression;

    :goto_0
    invoke-direct {p0, v0, v1}, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->createArgumentList(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    move-result-object v0

    .line 315
    iget-boolean v1, p0, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->inClosure:Z

    if-eqz v1, :cond_4

    iget-object v1, p0, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->traitHelperClass:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->classX(Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/ClassExpression;

    move-result-object v2

    :cond_4
    invoke-static {v2, v3, v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object v0

    const/4 v1, 0x1

    .line 316
    invoke-virtual {v0, v1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setImplicitThis(Z)V

    .line 317
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->isSafe()Z

    move-result v1

    invoke-virtual {v0, v1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setSafe(Z)V

    .line 319
    :goto_1
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->isSpreadSafe()Z

    move-result v1

    invoke-virtual {v0, v1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setSpreadSafe(Z)V

    .line 320
    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setSourcePosition(Lorg/codehaus/groovy/ast/ASTNode;)V

    return-object v0

    .line 326
    :cond_5
    iget-boolean v3, p0, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->inClosure:Z

    if-eqz v3, :cond_6

    goto :goto_2

    :cond_6
    iget-object v2, p0, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->weaved:Lorg/codehaus/groovy/ast/expr/VariableExpression;

    :goto_2
    invoke-virtual {p0, v1}, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v1

    invoke-static {v2, v0, v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object v0

    .line 327
    iget-boolean v1, p0, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->inClosure:Z

    if-eqz v1, :cond_7

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->isImplicitThis()Z

    move-result v1

    goto :goto_3

    :cond_7
    move v1, v4

    :goto_3
    invoke-virtual {v0, v1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setImplicitThis(Z)V

    .line 328
    iget-boolean v1, p0, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->inClosure:Z

    if-eqz v1, :cond_8

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->isSafe()Z

    move-result v4

    :cond_8
    invoke-virtual {v0, v4}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setSafe(Z)V

    .line 329
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->isSpreadSafe()Z

    move-result v1

    invoke-virtual {v0, v1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setSpreadSafe(Z)V

    .line 330
    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setSourcePosition(Lorg/codehaus/groovy/ast/ASTNode;)V

    return-object v0
.end method

.method private transformSuperMethodCall(Lorg/codehaus/groovy/ast/expr/MethodCallExpression;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 4

    .line 272
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->getMethodAsString()Ljava/lang/String;

    move-result-object v0

    if-nez v0, :cond_0

    .line 274
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->throwSuperError(Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 277
    :cond_0
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->getArguments()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v1

    invoke-virtual {p0, v1}, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v1

    .line 278
    new-instance v2, Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    invoke-direct {v2}, Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;-><init>()V

    .line 279
    instance-of v3, v1, Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    if-eqz v3, :cond_1

    .line 280
    check-cast v1, Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    .line 281
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_2

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lorg/codehaus/groovy/ast/expr/Expression;

    .line 282
    invoke-virtual {v2, v3}, Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;->addExpression(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/TupleExpression;

    goto :goto_0

    .line 285
    :cond_1
    invoke-virtual {v2, v1}, Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;->addExpression(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/TupleExpression;

    .line 287
    :cond_2
    iget-object v1, p0, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->weaved:Lorg/codehaus/groovy/ast/expr/VariableExpression;

    iget-object v3, p0, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->traitClass:Lorg/codehaus/groovy/ast/ClassNode;

    .line 289
    invoke-static {v3, v0}, Lorg/codehaus/groovy/transform/trait/Traits;->getSuperTraitMethodName(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    .line 287
    invoke-static {v1, v0, v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object v0

    .line 292
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->getMethod()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v1

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->getMethod()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v2

    invoke-virtual {v1, v2}, Lorg/codehaus/groovy/ast/expr/Expression;->setSourcePosition(Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 293
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->isSpreadSafe()Z

    move-result v1

    invoke-virtual {v0, v1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setSpreadSafe(Z)V

    .line 294
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->isSafe()Z

    move-result p1

    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setSafe(Z)V

    const/4 p1, 0x0

    .line 295
    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setImplicitThis(Z)V

    return-object v0
.end method

.method private static tryGetFieldNode(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/FieldNode;
    .locals 3

    .line 256
    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/ast/ClassNode;->getDeclaredField(Ljava/lang/String;)Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object v0

    if-nez v0, :cond_0

    .line 257
    sget-object v1, Lorg/codehaus/groovy/ast/ClassHelper;->CLASS_Type:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v1, p0}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_0

    .line 258
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/ClassNode;->getGenericsTypes()[Lorg/codehaus/groovy/ast/GenericsType;

    move-result-object p0

    if-eqz p0, :cond_0

    .line 259
    array-length v1, p0

    const/4 v2, 0x1

    if-ne v1, v2, :cond_0

    const/4 v0, 0x0

    .line 261
    aget-object p0, p0, v0

    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/GenericsType;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p0

    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/ast/ClassNode;->getDeclaredField(Ljava/lang/String;)Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object v0

    :cond_0
    return-object v0
.end method


# virtual methods
.method protected getSourceUnit()Lorg/codehaus/groovy/control/SourceUnit;
    .locals 1

    .line 94
    iget-object v0, p0, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->unit:Lorg/codehaus/groovy/control/SourceUnit;

    return-object v0
.end method

.method public transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 8

    .line 99
    iget-object v0, p0, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->weaved:Lorg/codehaus/groovy/ast/expr/VariableExpression;

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->getOriginType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v5

    .line 100
    instance-of v0, p1, Lorg/codehaus/groovy/ast/expr/BinaryExpression;

    if-eqz v0, :cond_0

    .line 101
    check-cast p1, Lorg/codehaus/groovy/ast/expr/BinaryExpression;

    invoke-direct {p0, p1, v5}, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->transformBinaryExpression(Lorg/codehaus/groovy/ast/expr/BinaryExpression;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    return-object p1

    .line 102
    :cond_0
    instance-of v0, p1, Lorg/codehaus/groovy/ast/expr/StaticMethodCallExpression;

    const/4 v7, 0x0

    if-eqz v0, :cond_1

    .line 103
    move-object v0, p1

    check-cast v0, Lorg/codehaus/groovy/ast/expr/StaticMethodCallExpression;

    .line 104
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/StaticMethodCallExpression;->getOwnerType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    iget-object v2, p0, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->traitClass:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v1, v2}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_f

    .line 105
    iget-object v1, p0, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->weaved:Lorg/codehaus/groovy/ast/expr/VariableExpression;

    .line 106
    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v1

    .line 107
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/StaticMethodCallExpression;->getMethod()Ljava/lang/String;

    move-result-object v2

    .line 108
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/StaticMethodCallExpression;->getArguments()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    .line 105
    invoke-static {v1, v2, v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object v0

    .line 110
    invoke-virtual {v0, v7}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setSafe(Z)V

    .line 111
    invoke-virtual {v0, v7}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setSpreadSafe(Z)V

    .line 112
    invoke-virtual {v0, v7}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setImplicitThis(Z)V

    .line 113
    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setSourcePosition(Lorg/codehaus/groovy/ast/ASTNode;)V

    return-object v0

    .line 116
    :cond_1
    instance-of v0, p1, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    const-string v1, "this"

    if-eqz v0, :cond_4

    .line 117
    move-object v0, p1

    check-cast v0, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    .line 118
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->getObjectExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v2

    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/expr/Expression;->getText()Ljava/lang/String;

    move-result-object v2

    .line 119
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->isImplicitThis()Z

    move-result v3

    if-nez v3, :cond_3

    invoke-virtual {v1, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_2

    goto :goto_0

    :cond_2
    const-string v1, "super"

    .line 121
    invoke-virtual {v1, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_f

    .line 122
    invoke-direct {p0, v0}, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->transformSuperMethodCall(Lorg/codehaus/groovy/ast/expr/MethodCallExpression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    return-object p1

    .line 120
    :cond_3
    :goto_0
    invoke-direct {p0, v0}, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->transformMethodCallOnThis(Lorg/codehaus/groovy/ast/expr/MethodCallExpression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    return-object p1

    .line 124
    :cond_4
    instance-of v0, p1, Lorg/codehaus/groovy/ast/expr/FieldExpression;

    if-eqz v0, :cond_5

    .line 125
    move-object v0, p1

    check-cast v0, Lorg/codehaus/groovy/ast/expr/FieldExpression;

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/FieldExpression;->getField()Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object v0

    .line 126
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/FieldNode;->isStatic()Z

    move-result v1

    invoke-direct {p0, p1, v0, v1}, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->transformFieldReference(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/FieldNode;Z)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    return-object p1

    .line 127
    :cond_5
    instance-of v0, p1, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    if-eqz v0, :cond_c

    .line 128
    move-object v0, p1

    check-cast v0, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    .line 129
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->getAccessedVariable()Lorg/codehaus/groovy/ast/Variable;

    move-result-object v1

    .line 130
    instance-of v2, v1, Lorg/codehaus/groovy/ast/FieldNode;

    if-nez v2, :cond_9

    instance-of v3, v1, Lorg/codehaus/groovy/ast/PropertyNode;

    if-eqz v3, :cond_6

    goto :goto_1

    .line 140
    :cond_6
    instance-of v1, v1, Lorg/codehaus/groovy/ast/DynamicVariable;

    if-eqz v1, :cond_7

    iget-boolean v1, p0, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->inClosure:Z

    if-nez v1, :cond_7

    .line 141
    iget-object v1, p0, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->weaved:Lorg/codehaus/groovy/ast/expr/VariableExpression;

    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v1

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->getName()Ljava/lang/String;

    move-result-object v0

    invoke-static {v1, v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->propX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    move-result-object v0

    .line 142
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->getProperty()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v1

    invoke-virtual {v1, p1}, Lorg/codehaus/groovy/ast/expr/Expression;->setSourcePosition(Lorg/codehaus/groovy/ast/ASTNode;)V

    return-object v0

    .line 145
    :cond_7
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->isThisExpression()Z

    move-result v1

    if-eqz v1, :cond_8

    .line 146
    iget-object v0, p0, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->weaved:Lorg/codehaus/groovy/ast/expr/VariableExpression;

    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v0

    .line 147
    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->setSourcePosition(Lorg/codehaus/groovy/ast/ASTNode;)V

    return-object v0

    .line 150
    :cond_8
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->isSuperExpression()Z

    move-result v1

    if-eqz v1, :cond_f

    .line 151
    invoke-direct {p0, v0}, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->throwSuperError(Lorg/codehaus/groovy/ast/ASTNode;)V

    goto/16 :goto_3

    .line 131
    :cond_9
    :goto_1
    iget-object v3, p0, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->knownFields:Ljava/util/Collection;

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->getName()Ljava/lang/String;

    move-result-object v4

    invoke-interface {v3, v4}, Ljava/util/Collection;->contains(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_b

    .line 132
    invoke-interface {v1}, Lorg/codehaus/groovy/ast/Variable;->getModifiers()I

    move-result v0

    invoke-static {v0}, Ljava/lang/reflect/Modifier;->isStatic(I)Z

    move-result v0

    if-eqz v2, :cond_a

    .line 134
    check-cast v1, Lorg/codehaus/groovy/ast/FieldNode;

    goto :goto_2

    :cond_a
    check-cast v1, Lorg/codehaus/groovy/ast/PropertyNode;

    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/PropertyNode;->getField()Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object v1

    .line 133
    :goto_2
    invoke-direct {p0, p1, v1, v0}, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->transformFieldReference(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/FieldNode;Z)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    return-object p1

    .line 136
    :cond_b
    iget-object v1, p0, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->weaved:Lorg/codehaus/groovy/ast/expr/VariableExpression;

    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v1

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->getName()Ljava/lang/String;

    move-result-object v0

    invoke-static {v1, v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->propX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    move-result-object v0

    .line 137
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->getProperty()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v1

    invoke-virtual {v1, p1}, Lorg/codehaus/groovy/ast/expr/Expression;->setSourcePosition(Lorg/codehaus/groovy/ast/ASTNode;)V

    return-object v0

    .line 153
    :cond_c
    instance-of v0, p1, Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    if-eqz v0, :cond_e

    .line 154
    move-object v0, p1

    check-cast v0, Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    .line 155
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->getObjectExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v2

    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/expr/Expression;->getText()Ljava/lang/String;

    move-result-object v2

    .line 156
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->isImplicitThis()Z

    move-result v3

    if-nez v3, :cond_d

    invoke-virtual {v1, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_f

    .line 157
    :cond_d
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->getPropertyAsString()Ljava/lang/String;

    move-result-object v2

    .line 158
    iget-object v0, p0, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->knownFields:Ljava/util/Collection;

    invoke-interface {v0, v2}, Ljava/util/Collection;->contains(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_f

    .line 159
    new-instance v0, Lorg/codehaus/groovy/ast/FieldNode;

    const/4 v3, 0x0

    sget-object v4, Lorg/codehaus/groovy/ast/ClassHelper;->OBJECT_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    const/4 v6, 0x0

    move-object v1, v0

    invoke-direct/range {v1 .. v6}, Lorg/codehaus/groovy/ast/FieldNode;-><init>(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;)V

    .line 160
    invoke-direct {p0, p1, v0, v7}, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->transformFieldReference(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/FieldNode;Z)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    return-object p1

    .line 163
    :cond_e
    instance-of v0, p1, Lorg/codehaus/groovy/ast/expr/ClosureExpression;

    if-eqz v0, :cond_f

    const/4 v0, 0x3

    new-array v0, v0, [Lorg/codehaus/groovy/ast/expr/Expression;

    .line 164
    iget-object v1, p0, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->weaved:Lorg/codehaus/groovy/ast/expr/VariableExpression;

    .line 165
    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v1

    aput-object v1, v0, v7

    iget-object v1, p0, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->weaved:Lorg/codehaus/groovy/ast/expr/VariableExpression;

    .line 166
    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v1

    const/4 v2, 0x1

    aput-object v1, v0, v2

    const/4 v1, 0x2

    iget-object v3, p0, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->weaved:Lorg/codehaus/groovy/ast/expr/VariableExpression;

    .line 167
    invoke-static {v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v3

    aput-object v3, v0, v1

    .line 164
    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->args([Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    move-result-object v0

    const-string v1, "rehydrate"

    invoke-static {p1, v1, v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object v0

    .line 169
    invoke-virtual {v0, v7}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setImplicitThis(Z)V

    .line 170
    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setSourcePosition(Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 171
    iget-boolean v1, p0, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->inClosure:Z

    .line 172
    iput-boolean v2, p0, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->inClosure:Z

    .line 173
    move-object v2, p1

    check-cast v2, Lorg/codehaus/groovy/ast/expr/ClosureExpression;

    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/expr/ClosureExpression;->getCode()Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v2

    invoke-virtual {v2, p0}, Lorg/codehaus/groovy/ast/stmt/Statement;->visit(Lorg/codehaus/groovy/ast/GroovyCodeVisitor;)V

    .line 174
    iput-boolean v1, p0, Lorg/codehaus/groovy/transform/trait/TraitReceiverTransformer;->inClosure:Z

    .line 178
    sget-object v1, Lorg/codehaus/groovy/transform/trait/TraitASTTransformation;->POST_TYPECHECKING_REPLACEMENT:Ljava/lang/String;

    invoke-virtual {p1, v1, v0}, Lorg/codehaus/groovy/ast/expr/Expression;->putNodeMetaData(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-object p1

    .line 183
    :cond_f
    :goto_3
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeExpressionTransformer;->transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    return-object p1
.end method
