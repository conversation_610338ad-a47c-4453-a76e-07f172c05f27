.class public final synthetic Lorg/codehaus/groovy/transform/trait/SuperCallTraitTransformer$$ExternalSyntheticLambda1;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Function;


# static fields
.field public static final synthetic INSTANCE:Lorg/codehaus/groovy/transform/trait/SuperCallTraitTransformer$$ExternalSyntheticLambda1;


# direct methods
.method static synthetic constructor <clinit>()V
    .locals 1

    new-instance v0, Lorg/codehaus/groovy/transform/trait/SuperCallTraitTransformer$$ExternalSyntheticLambda1;

    invoke-direct {v0}, Lorg/codehaus/groovy/transform/trait/SuperCallTraitTransformer$$ExternalSyntheticLambda1;-><init>()V

    sput-object v0, Lorg/codehaus/groovy/transform/trait/SuperCallTraitTransformer$$ExternalSyntheticLambda1;->INSTANCE:Lorg/codehaus/groovy/transform/trait/SuperCallTraitTransformer$$ExternalSyntheticLambda1;

    return-void
.end method

.method private synthetic constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final apply(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    check-cast p1, Lorg/codehaus/groovy/ast/MethodNode;

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object p1

    return-object p1
.end method
