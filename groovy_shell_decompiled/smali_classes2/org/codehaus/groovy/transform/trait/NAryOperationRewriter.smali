.class Lorg/codehaus/groovy/transform/trait/NAryOperationRewriter;
.super Lorg/codehaus/groovy/ast/ClassCodeExpressionTransformer;
.source "NAryOperationRewriter.java"


# instance fields
.field private final knownFields:Ljava/util/Collection;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Collection<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field private final sourceUnit:Lorg/codehaus/groovy/control/SourceUnit;


# direct methods
.method public constructor <init>(Lorg/codehaus/groovy/control/SourceUnit;Ljava/util/Collection;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/control/SourceUnit;",
            "Ljava/util/Collection<",
            "Ljava/lang/String;",
            ">;)V"
        }
    .end annotation

    .line 48
    invoke-direct {p0}, Lorg/codehaus/groovy/ast/ClassCodeExpressionTransformer;-><init>()V

    .line 49
    iput-object p1, p0, Lorg/codehaus/groovy/transform/trait/NAryOperationRewriter;->sourceUnit:Lorg/codehaus/groovy/control/SourceUnit;

    .line 50
    iput-object p2, p0, Lorg/codehaus/groovy/transform/trait/NAryOperationRewriter;->knownFields:Ljava/util/Collection;

    return-void
.end method

.method private isInternalFieldAccess(Lorg/codehaus/groovy/ast/expr/Expression;)Z
    .locals 2

    .line 73
    instance-of v0, p1, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    if-eqz v0, :cond_0

    .line 74
    move-object v0, p1

    check-cast v0, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->getAccessedVariable()Lorg/codehaus/groovy/ast/Variable;

    move-result-object v0

    .line 75
    instance-of v1, v0, Lorg/codehaus/groovy/ast/FieldNode;

    if-eqz v1, :cond_0

    .line 76
    iget-object p1, p0, Lorg/codehaus/groovy/transform/trait/NAryOperationRewriter;->knownFields:Ljava/util/Collection;

    invoke-interface {v0}, Lorg/codehaus/groovy/ast/Variable;->getName()Ljava/lang/String;

    move-result-object v0

    invoke-interface {p1, v0}, Ljava/util/Collection;->contains(Ljava/lang/Object;)Z

    move-result p1

    return p1

    .line 79
    :cond_0
    instance-of v0, p1, Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    if-eqz v0, :cond_2

    .line 80
    check-cast p1, Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->isImplicitThis()Z

    move-result v0

    if-nez v0, :cond_1

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->getObjectExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/Expression;->getText()Ljava/lang/String;

    move-result-object v0

    const-string v1, "this"

    invoke-virtual {v1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_2

    .line 81
    :cond_1
    iget-object v0, p0, Lorg/codehaus/groovy/transform/trait/NAryOperationRewriter;->knownFields:Ljava/util/Collection;

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->getProperty()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/Expression;->getText()Ljava/lang/String;

    move-result-object p1

    invoke-interface {v0, p1}, Ljava/util/Collection;->contains(Ljava/lang/Object;)Z

    move-result p1

    return p1

    :cond_2
    const/4 p1, 0x0

    return p1
.end method

.method private transformBinaryExpression(Lorg/codehaus/groovy/ast/expr/BinaryExpression;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 5

    .line 108
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/BinaryExpression;->getOperation()Lorg/codehaus/groovy/syntax/Token;

    move-result-object v0

    invoke-virtual {v0}, Lorg/codehaus/groovy/syntax/Token;->getType()I

    move-result v0

    .line 109
    invoke-static {v0}, Lorg/codehaus/groovy/syntax/TokenUtil;->removeAssignment(I)I

    move-result v1

    if-ne v1, v0, :cond_0

    .line 112
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeExpressionTransformer;->transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    return-object p1

    .line 114
    :cond_0
    new-instance v0, Lorg/codehaus/groovy/ast/expr/BinaryExpression;

    .line 115
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/BinaryExpression;->getLeftExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v2

    const/4 v3, -0x1

    .line 116
    invoke-static {v1, v3, v3}, Lorg/codehaus/groovy/syntax/Token;->newSymbol(III)Lorg/codehaus/groovy/syntax/Token;

    move-result-object v1

    .line 117
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/BinaryExpression;->getRightExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v4

    invoke-direct {v0, v2, v1, v4}, Lorg/codehaus/groovy/ast/expr/BinaryExpression;-><init>(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/syntax/Token;Lorg/codehaus/groovy/ast/expr/Expression;)V

    .line 119
    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/ast/expr/BinaryExpression;->setSourcePosition(Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 120
    new-instance v1, Lorg/codehaus/groovy/ast/expr/BinaryExpression;

    .line 121
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/BinaryExpression;->getLeftExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v2

    const/16 v4, 0x64

    .line 122
    invoke-static {v4, v3, v3}, Lorg/codehaus/groovy/syntax/Token;->newSymbol(III)Lorg/codehaus/groovy/syntax/Token;

    move-result-object v3

    invoke-direct {v1, v2, v3, v0}, Lorg/codehaus/groovy/ast/expr/BinaryExpression;-><init>(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/syntax/Token;Lorg/codehaus/groovy/ast/expr/Expression;)V

    .line 125
    invoke-virtual {v1, p1}, Lorg/codehaus/groovy/ast/expr/BinaryExpression;->setSourcePosition(Lorg/codehaus/groovy/ast/ASTNode;)V

    return-object v1
.end method

.method private transformPostfixExpression(Lorg/codehaus/groovy/ast/expr/PostfixExpression;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 5

    .line 98
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/PostfixExpression;->getExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    invoke-direct {p0, v0}, Lorg/codehaus/groovy/transform/trait/NAryOperationRewriter;->isInternalFieldAccess(Lorg/codehaus/groovy/ast/expr/Expression;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 99
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/PostfixExpression;->getOperation()Lorg/codehaus/groovy/syntax/Token;

    move-result-object v0

    .line 100
    iget-object v1, p0, Lorg/codehaus/groovy/transform/trait/NAryOperationRewriter;->sourceUnit:Lorg/codehaus/groovy/control/SourceUnit;

    new-instance v2, Lorg/codehaus/groovy/syntax/SyntaxException;

    invoke-virtual {v0}, Lorg/codehaus/groovy/syntax/Token;->getStartLine()I

    move-result v3

    invoke-virtual {v0}, Lorg/codehaus/groovy/syntax/Token;->getStartColumn()I

    move-result v0

    const-string v4, "Postfix expressions on trait fields/properties  are not supported in traits."

    invoke-direct {v2, v4, v3, v0}, Lorg/codehaus/groovy/syntax/SyntaxException;-><init>(Ljava/lang/String;II)V

    invoke-virtual {v1, v2}, Lorg/codehaus/groovy/control/SourceUnit;->addError(Lorg/codehaus/groovy/syntax/SyntaxException;)V

    return-object p1

    .line 103
    :cond_0
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeExpressionTransformer;->transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    return-object p1
.end method

.method private transformPrefixExpression(Lorg/codehaus/groovy/ast/expr/PrefixExpression;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 5

    .line 88
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/PrefixExpression;->getExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    invoke-direct {p0, v0}, Lorg/codehaus/groovy/transform/trait/NAryOperationRewriter;->isInternalFieldAccess(Lorg/codehaus/groovy/ast/expr/Expression;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 89
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/PrefixExpression;->getOperation()Lorg/codehaus/groovy/syntax/Token;

    move-result-object v0

    .line 90
    iget-object v1, p0, Lorg/codehaus/groovy/transform/trait/NAryOperationRewriter;->sourceUnit:Lorg/codehaus/groovy/control/SourceUnit;

    new-instance v2, Lorg/codehaus/groovy/syntax/SyntaxException;

    invoke-virtual {v0}, Lorg/codehaus/groovy/syntax/Token;->getStartLine()I

    move-result v3

    invoke-virtual {v0}, Lorg/codehaus/groovy/syntax/Token;->getStartColumn()I

    move-result v0

    const-string v4, "Prefix expressions on trait fields/properties are not supported in traits."

    invoke-direct {v2, v4, v3, v0}, Lorg/codehaus/groovy/syntax/SyntaxException;-><init>(Ljava/lang/String;II)V

    invoke-virtual {v1, v2}, Lorg/codehaus/groovy/control/SourceUnit;->addError(Lorg/codehaus/groovy/syntax/SyntaxException;)V

    return-object p1

    .line 93
    :cond_0
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeExpressionTransformer;->transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    return-object p1
.end method


# virtual methods
.method protected getSourceUnit()Lorg/codehaus/groovy/control/SourceUnit;
    .locals 1

    .line 55
    iget-object v0, p0, Lorg/codehaus/groovy/transform/trait/NAryOperationRewriter;->sourceUnit:Lorg/codehaus/groovy/control/SourceUnit;

    return-object v0
.end method

.method public transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 1

    .line 60
    instance-of v0, p1, Lorg/codehaus/groovy/ast/expr/BinaryExpression;

    if-eqz v0, :cond_0

    .line 61
    check-cast p1, Lorg/codehaus/groovy/ast/expr/BinaryExpression;

    invoke-direct {p0, p1}, Lorg/codehaus/groovy/transform/trait/NAryOperationRewriter;->transformBinaryExpression(Lorg/codehaus/groovy/ast/expr/BinaryExpression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    return-object p1

    .line 63
    :cond_0
    instance-of v0, p1, Lorg/codehaus/groovy/ast/expr/PrefixExpression;

    if-eqz v0, :cond_1

    .line 64
    check-cast p1, Lorg/codehaus/groovy/ast/expr/PrefixExpression;

    invoke-direct {p0, p1}, Lorg/codehaus/groovy/transform/trait/NAryOperationRewriter;->transformPrefixExpression(Lorg/codehaus/groovy/ast/expr/PrefixExpression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    return-object p1

    .line 66
    :cond_1
    instance-of v0, p1, Lorg/codehaus/groovy/ast/expr/PostfixExpression;

    if-eqz v0, :cond_2

    .line 67
    check-cast p1, Lorg/codehaus/groovy/ast/expr/PostfixExpression;

    invoke-direct {p0, p1}, Lorg/codehaus/groovy/transform/trait/NAryOperationRewriter;->transformPostfixExpression(Lorg/codehaus/groovy/ast/expr/PostfixExpression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    return-object p1

    .line 69
    :cond_2
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeExpressionTransformer;->transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    return-object p1
.end method
