.class public final synthetic Lorg/codehaus/groovy/transform/trait/TraitASTTransformation$$ExternalSyntheticLambda0;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Lorg/codehaus/groovy/control/CompilationUnit$IPrimaryClassNodeOperation;


# instance fields
.field public final synthetic f$0:Lorg/codehaus/groovy/ast/ClassNode;


# direct methods
.method public synthetic constructor <init>(Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/codehaus/groovy/transform/trait/TraitASTTransformation$$ExternalSyntheticLambda0;->f$0:Lorg/codehaus/groovy/ast/ClassNode;

    return-void
.end method


# virtual methods
.method public final call(Lorg/codehaus/groovy/control/SourceUnit;Lorg/codehaus/groovy/classgen/GeneratorContext;Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 1

    iget-object v0, p0, Lorg/codehaus/groovy/transform/trait/TraitASTTransformation$$ExternalSyntheticLambda0;->f$0:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {v0, p1, p2, p3}, Lorg/codehaus/groovy/transform/trait/TraitASTTransformation;->lambda$registerASTTransformations$0(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/control/SourceUnit;Lorg/codehaus/groovy/classgen/GeneratorContext;Lorg/codehaus/groovy/ast/ClassNode;)V

    return-void
.end method
