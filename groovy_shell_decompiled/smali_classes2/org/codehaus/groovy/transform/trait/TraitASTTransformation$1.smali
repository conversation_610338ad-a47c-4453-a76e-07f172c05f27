.class synthetic Lorg/codehaus/groovy/transform/trait/TraitASTTransformation$1;
.super Ljava/lang/Object;
.source "TraitASTTransformation.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/codehaus/groovy/transform/trait/TraitASTTransformation;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1008
    name = null
.end annotation
