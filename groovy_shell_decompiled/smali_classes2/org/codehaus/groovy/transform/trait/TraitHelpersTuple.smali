.class Lorg/codehaus/groovy/transform/trait/TraitHelpersTuple;
.super Ljava/lang/Object;
.source "TraitHelpersTuple.java"


# instance fields
.field private final fieldHelper:Lorg/codehaus/groovy/ast/ClassNode;

.field private final helper:Lorg/codehaus/groovy/ast/ClassNode;

.field private final staticFieldHelper:Lorg/codehaus/groovy/ast/ClassNode;


# direct methods
.method public constructor <init>(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 1

    const/4 v0, 0x0

    .line 34
    invoke-direct {p0, p1, p2, v0}, Lorg/codehaus/groovy/transform/trait/TraitHelpersTuple;-><init>(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;)V

    return-void
.end method

.method public constructor <init>(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 0

    .line 37
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 38
    iput-object p1, p0, Lorg/codehaus/groovy/transform/trait/TraitHelpersTuple;->helper:Lorg/codehaus/groovy/ast/ClassNode;

    .line 39
    iput-object p2, p0, Lorg/codehaus/groovy/transform/trait/TraitHelpersTuple;->fieldHelper:Lorg/codehaus/groovy/ast/ClassNode;

    .line 40
    iput-object p3, p0, Lorg/codehaus/groovy/transform/trait/TraitHelpersTuple;->staticFieldHelper:Lorg/codehaus/groovy/ast/ClassNode;

    return-void
.end method


# virtual methods
.method public getFieldHelper()Lorg/codehaus/groovy/ast/ClassNode;
    .locals 1

    .line 48
    iget-object v0, p0, Lorg/codehaus/groovy/transform/trait/TraitHelpersTuple;->fieldHelper:Lorg/codehaus/groovy/ast/ClassNode;

    return-object v0
.end method

.method public getHelper()Lorg/codehaus/groovy/ast/ClassNode;
    .locals 1

    .line 44
    iget-object v0, p0, Lorg/codehaus/groovy/transform/trait/TraitHelpersTuple;->helper:Lorg/codehaus/groovy/ast/ClassNode;

    return-object v0
.end method

.method public getStaticFieldHelper()Lorg/codehaus/groovy/ast/ClassNode;
    .locals 1

    .line 55
    iget-object v0, p0, Lorg/codehaus/groovy/transform/trait/TraitHelpersTuple;->staticFieldHelper:Lorg/codehaus/groovy/ast/ClassNode;

    return-object v0
.end method
