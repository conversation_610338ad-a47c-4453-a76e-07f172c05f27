.class public final synthetic Lorg/codehaus/groovy/transform/trait/SuperCallTraitTransformer$$ExternalSyntheticLambda0;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Function;


# instance fields
.field public final synthetic f$0:Lorg/codehaus/groovy/ast/ClassNode;

.field public final synthetic f$1:Lorg/codehaus/groovy/ast/expr/PropertyExpression;


# direct methods
.method public synthetic constructor <init>(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/PropertyExpression;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/codehaus/groovy/transform/trait/SuperCallTraitTransformer$$ExternalSyntheticLambda0;->f$0:Lorg/codehaus/groovy/ast/ClassNode;

    iput-object p2, p0, Lorg/codehaus/groovy/transform/trait/SuperCallTraitTransformer$$ExternalSyntheticLambda0;->f$1:Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    return-void
.end method


# virtual methods
.method public final apply(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    iget-object v0, p0, Lorg/codehaus/groovy/transform/trait/SuperCallTraitTransformer$$ExternalSyntheticLambda0;->f$0:Lorg/codehaus/groovy/ast/ClassNode;

    iget-object v1, p0, Lorg/codehaus/groovy/transform/trait/SuperCallTraitTransformer$$ExternalSyntheticLambda0;->f$1:Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    check-cast p1, Lorg/codehaus/groovy/ast/MethodNode;

    invoke-static {v0, v1, p1}, Lorg/codehaus/groovy/transform/trait/SuperCallTraitTransformer;->lambda$transformPropertyExpression$0(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/PropertyExpression;Lorg/codehaus/groovy/ast/MethodNode;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object p1

    return-object p1
.end method
