.class Lorg/codehaus/groovy/transform/trait/SuperCallTraitTransformer;
.super Lorg/codehaus/groovy/ast/ClassCodeExpressionTransformer;
.source "SuperCallTraitTransformer.java"


# static fields
.field static final UNRESOLVED_HELPER_CLASS:Ljava/lang/String; = "UNRESOLVED_HELPER_CLASS"


# instance fields
.field private final unit:Lorg/codehaus/groovy/control/SourceUnit;


# direct methods
.method constructor <init>(Lorg/codehaus/groovy/control/SourceUnit;)V
    .locals 0

    .line 61
    invoke-direct {p0}, Lorg/codehaus/groovy/ast/ClassCodeExpressionTransformer;-><init>()V

    .line 62
    iput-object p1, p0, Lorg/codehaus/groovy/transform/trait/SuperCallTraitTransformer;->unit:Lorg/codehaus/groovy/control/SourceUnit;

    return-void
.end method

.method private getHelper(Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/ClassNode;
    .locals 8

    .line 221
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->redirect()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->getInnerClasses()Ljava/util/Iterator;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-nez v0, :cond_0

    .line 222
    invoke-virtual {p0}, Lorg/codehaus/groovy/transform/trait/SuperCallTraitTransformer;->getSourceUnit()Lorg/codehaus/groovy/control/SourceUnit;

    move-result-object v0

    invoke-virtual {v0}, Lorg/codehaus/groovy/control/SourceUnit;->getAST()Lorg/codehaus/groovy/ast/ModuleNode;

    move-result-object v0

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ModuleNode;->getClasses()Ljava/util/List;

    move-result-object v0

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->redirect()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/util/List;->contains(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 223
    new-instance v0, Lorg/codehaus/groovy/ast/InnerClassNode;

    .line 225
    invoke-static {p1}, Lorg/codehaus/groovy/transform/trait/Traits;->helperClassName(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/lang/String;

    move-result-object v3

    const/16 v4, 0x1409

    sget-object v5, Lorg/codehaus/groovy/ast/ClassHelper;->OBJECT_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    sget-object v6, Lorg/codehaus/groovy/ast/ClassNode;->EMPTY_ARRAY:[Lorg/codehaus/groovy/ast/ClassNode;

    const/4 v7, 0x0

    move-object v1, v0

    move-object v2, p1

    invoke-direct/range {v1 .. v7}, Lorg/codehaus/groovy/ast/InnerClassNode;-><init>(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/MixinNode;)V

    .line 230
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/InnerClassNode;->getPlainNodeReference()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    const/4 v1, 0x0

    .line 231
    invoke-virtual {v0, v1}, Lorg/codehaus/groovy/ast/ClassNode;->setRedirect(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 232
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->redirect()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    const-string v1, "UNRESOLVED_HELPER_CLASS"

    invoke-virtual {p1, v1, v0}, Lorg/codehaus/groovy/ast/ClassNode;->setNodeMetaData(Ljava/lang/Object;Ljava/lang/Object;)V

    return-object v0

    .line 235
    :cond_0
    invoke-static {p1}, Lorg/codehaus/groovy/transform/trait/Traits;->findHelper(Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    return-object p1
.end method

.method private getTraitSuperTarget(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/ClassNode;
    .locals 2

    .line 239
    instance-of v0, p1, Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    if-eqz v0, :cond_0

    .line 240
    check-cast p1, Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    .line 241
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->getObjectExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    .line 242
    instance-of v1, v0, Lorg/codehaus/groovy/ast/expr/ClassExpression;

    if-eqz v1, :cond_0

    .line 243
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/Expression;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    .line 244
    invoke-static {v0}, Lorg/codehaus/groovy/transform/trait/Traits;->isTrait(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->getPropertyAsString()Ljava/lang/String;

    move-result-object p1

    const-string v1, "super"

    invoke-virtual {v1, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_0

    return-object v0

    :cond_0
    const/4 p1, 0x0

    return-object p1
.end method

.method private static isSelfType(Lorg/codehaus/groovy/ast/Parameter;Lorg/codehaus/groovy/ast/ClassNode;)Z
    .locals 3

    .line 253
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/Parameter;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p0

    .line 254
    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result v0

    const/4 v1, 0x1

    if-eqz v0, :cond_0

    return v1

    .line 255
    :cond_0
    sget-object v0, Lorg/codehaus/groovy/ast/ClassHelper;->CLASS_Type:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result v0

    const/4 v2, 0x0

    if-eqz v0, :cond_1

    .line 256
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/ClassNode;->getGenericsTypes()[Lorg/codehaus/groovy/ast/GenericsType;

    move-result-object v0

    if-eqz v0, :cond_1

    .line 257
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/ClassNode;->getGenericsTypes()[Lorg/codehaus/groovy/ast/GenericsType;

    move-result-object p0

    aget-object p0, p0, v2

    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/GenericsType;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p0

    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result p0

    if-eqz p0, :cond_1

    goto :goto_0

    :cond_1
    move v1, v2

    :goto_0
    return v1
.end method

.method static synthetic lambda$transformMethodCallExpression$1([Lorg/codehaus/groovy/ast/Parameter;)Z
    .locals 2

    .line 192
    array-length v0, p0

    const/4 v1, 0x0

    if-lez v0, :cond_0

    aget-object p0, p0, v1

    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/Parameter;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p0

    sget-object v0, Lorg/codehaus/groovy/ast/ClassHelper;->CLASS_Type:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result p0

    if-eqz p0, :cond_0

    const/4 v1, 0x1

    :cond_0
    return v1
.end method

.method static synthetic lambda$transformPropertyExpression$0(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/PropertyExpression;Lorg/codehaus/groovy/ast/MethodNode;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;
    .locals 6

    .line 145
    new-instance v0, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    new-instance v1, Lorg/codehaus/groovy/ast/expr/ClassExpression;

    invoke-direct {v1, p0}, Lorg/codehaus/groovy/ast/expr/ClassExpression;-><init>(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 147
    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/MethodNode;->getName()Ljava/lang/String;

    move-result-object p0

    new-instance v2, Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    .line 149
    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/MethodNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v3

    const/4 v4, 0x0

    aget-object v3, v3, v4

    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/Parameter;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v3

    sget-object v5, Lorg/codehaus/groovy/ast/ClassHelper;->CLASS_Type:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v3, v5}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_0

    const-string v3, "class"

    .line 150
    invoke-static {v4, v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->thisPropX(ZLjava/lang/String;)Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    move-result-object v3

    goto :goto_0

    :cond_0
    const-string v3, "this"

    invoke-static {v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v3

    :goto_0
    invoke-direct {v2, v3}, Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;-><init>(Lorg/codehaus/groovy/ast/expr/Expression;)V

    invoke-direct {v0, v1, p0, v2}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;-><init>(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)V

    .line 153
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->getObjectExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p0

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->getObjectExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->getObjectExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v1

    invoke-virtual {p0, v1}, Lorg/codehaus/groovy/ast/expr/Expression;->setSourcePosition(Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 154
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->getMethod()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p0

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->getProperty()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v1

    invoke-virtual {p0, v1}, Lorg/codehaus/groovy/ast/expr/Expression;->setSourcePosition(Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 155
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->isSpreadSafe()Z

    move-result p0

    invoke-virtual {v0, p0}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setSpreadSafe(Z)V

    .line 156
    invoke-virtual {v0, p2}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setMethodTarget(Lorg/codehaus/groovy/ast/MethodNode;)V

    .line 157
    invoke-virtual {v0, v4}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setImplicitThis(Z)V

    return-object v0
.end method

.method private transformBinaryExpression(Lorg/codehaus/groovy/ast/expr/BinaryExpression;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 9

    .line 88
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/BinaryExpression;->getOperation()Lorg/codehaus/groovy/syntax/Token;

    move-result-object v0

    invoke-virtual {v0}, Lorg/codehaus/groovy/syntax/Token;->getType()I

    move-result v0

    invoke-static {v0}, Lorg/codehaus/groovy/syntax/Types;->isAssignment(I)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 90
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/BinaryExpression;->getLeftExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/BinaryExpression;->getOperation()Lorg/codehaus/groovy/syntax/Token;

    move-result-object v1

    const-string v2, "assign.target"

    invoke-virtual {v0, v2, v1}, Lorg/codehaus/groovy/ast/expr/Expression;->putNodeMetaData(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 92
    :cond_0
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeExpressionTransformer;->transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    .line 93
    instance-of v0, p1, Lorg/codehaus/groovy/ast/expr/BinaryExpression;

    if-eqz v0, :cond_3

    .line 94
    move-object v0, p1

    check-cast v0, Lorg/codehaus/groovy/ast/expr/BinaryExpression;

    .line 95
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/BinaryExpression;->getOperation()Lorg/codehaus/groovy/syntax/Token;

    move-result-object v1

    invoke-virtual {v1}, Lorg/codehaus/groovy/syntax/Token;->getType()I

    move-result v1

    const/16 v2, 0x64

    if-ne v1, v2, :cond_3

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/BinaryExpression;->getLeftExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v1

    instance-of v1, v1, Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    if-eqz v1, :cond_3

    .line 96
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/BinaryExpression;->getLeftExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    .line 97
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->getObjectExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v2

    invoke-direct {p0, v2}, Lorg/codehaus/groovy/transform/trait/SuperCallTraitTransformer;->getTraitSuperTarget(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v2

    if-eqz v2, :cond_3

    .line 99
    invoke-direct {p0, v2}, Lorg/codehaus/groovy/transform/trait/SuperCallTraitTransformer;->getHelper(Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v3

    .line 102
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->getPropertyAsString()Ljava/lang/String;

    move-result-object v4

    invoke-static {v4}, Lgroovy/lang/MetaProperty;->getSetterName(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v4

    .line 103
    invoke-virtual {v3, v4}, Lorg/codehaus/groovy/ast/ClassNode;->getMethods(Ljava/lang/String;)Ljava/util/List;

    move-result-object v5

    invoke-interface {v5}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v5

    :cond_1
    invoke-interface {v5}, Ljava/util/Iterator;->hasNext()Z

    move-result v6

    if-eqz v6, :cond_3

    invoke-interface {v5}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Lorg/codehaus/groovy/ast/MethodNode;

    .line 104
    invoke-virtual {v6}, Lorg/codehaus/groovy/ast/MethodNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v6

    .line 105
    array-length v7, v6

    const/4 v8, 0x2

    if-ne v7, v8, :cond_1

    const/4 v7, 0x0

    aget-object v8, v6, v7

    invoke-static {v8, v2}, Lorg/codehaus/groovy/transform/trait/SuperCallTraitTransformer;->isSelfType(Lorg/codehaus/groovy/ast/Parameter;Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v8

    if-eqz v8, :cond_1

    .line 106
    new-instance p1, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    new-instance v2, Lorg/codehaus/groovy/ast/expr/ClassExpression;

    invoke-direct {v2, v3}, Lorg/codehaus/groovy/ast/expr/ClassExpression;-><init>(Lorg/codehaus/groovy/ast/ClassNode;)V

    new-instance v3, Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    .line 110
    aget-object v5, v6, v7

    invoke-virtual {v5}, Lorg/codehaus/groovy/ast/Parameter;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v5

    sget-object v6, Lorg/codehaus/groovy/ast/ClassHelper;->CLASS_Type:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v5, v6}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result v5

    if-eqz v5, :cond_2

    const-string v5, "class"

    .line 111
    invoke-static {v7, v5}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->thisPropX(ZLjava/lang/String;)Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    move-result-object v5

    goto :goto_0

    :cond_2
    const-string v5, "this"

    invoke-static {v5}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v5

    .line 112
    :goto_0
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/BinaryExpression;->getRightExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    invoke-direct {v3, v5, v0}, Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;-><init>(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)V

    invoke-direct {p1, v2, v4, v3}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;-><init>(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)V

    .line 115
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->getObjectExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->getObjectExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v2

    invoke-virtual {v0, v2}, Lorg/codehaus/groovy/ast/expr/Expression;->setSourcePosition(Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 116
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->getMethod()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->getProperty()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v2

    invoke-virtual {v0, v2}, Lorg/codehaus/groovy/ast/expr/Expression;->setSourcePosition(Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 117
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->isSpreadSafe()Z

    move-result v0

    invoke-virtual {p1, v0}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setSpreadSafe(Z)V

    .line 118
    invoke-virtual {p1, v7}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setImplicitThis(Z)V

    :cond_3
    return-object p1
.end method

.method private transformClosureExpression(Lorg/codehaus/groovy/ast/expr/ClosureExpression;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 5

    .line 129
    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/ClosureUtils;->getParametersSafe(Lorg/codehaus/groovy/ast/expr/ClosureExpression;)[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v0

    array-length v1, v0

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v1, :cond_0

    aget-object v3, v0, v2

    .line 130
    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/Parameter;->getInitialExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v4

    invoke-virtual {p0, v4}, Lorg/codehaus/groovy/transform/trait/SuperCallTraitTransformer;->transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v4

    .line 131
    invoke-virtual {v3, v4}, Lorg/codehaus/groovy/ast/Parameter;->setInitialExpression(Lorg/codehaus/groovy/ast/expr/Expression;)V

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 133
    :cond_0
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/ClosureExpression;->getCode()Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v0

    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/transform/trait/SuperCallTraitTransformer;->visitClassCodeContainer(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 134
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeExpressionTransformer;->transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    return-object p1
.end method

.method private transformMethodCallExpression(Lorg/codehaus/groovy/ast/expr/MethodCallExpression;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 5

    .line 185
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->getObjectExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    invoke-direct {p0, v0}, Lorg/codehaus/groovy/transform/trait/SuperCallTraitTransformer;->getTraitSuperTarget(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    if-eqz v0, :cond_4

    .line 187
    invoke-direct {p0, v0}, Lorg/codehaus/groovy/transform/trait/SuperCallTraitTransformer;->getHelper(Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    .line 190
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->getMethodAsString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Lorg/codehaus/groovy/ast/ClassNode;->getMethods(Ljava/lang/String;)Ljava/util/List;

    move-result-object v1

    .line 191
    invoke-interface {v1}, Ljava/util/List;->isEmpty()Z

    move-result v2

    const/4 v3, 0x0

    if-nez v2, :cond_0

    invoke-interface {v1}, Ljava/util/List;->stream()Ljava/util/stream/Stream;

    move-result-object v1

    sget-object v2, Lorg/codehaus/groovy/transform/trait/SuperCallTraitTransformer$$ExternalSyntheticLambda1;->INSTANCE:Lorg/codehaus/groovy/transform/trait/SuperCallTraitTransformer$$ExternalSyntheticLambda1;

    invoke-interface {v1, v2}, Ljava/util/stream/Stream;->map(Ljava/util/function/Function;)Ljava/util/stream/Stream;

    move-result-object v1

    sget-object v2, Lorg/codehaus/groovy/transform/trait/SuperCallTraitTransformer$$ExternalSyntheticLambda2;->INSTANCE:Lorg/codehaus/groovy/transform/trait/SuperCallTraitTransformer$$ExternalSyntheticLambda2;

    .line 192
    invoke-interface {v1, v2}, Ljava/util/stream/Stream;->allMatch(Ljava/util/function/Predicate;)Z

    move-result v1

    if-eqz v1, :cond_0

    const/4 v1, 0x1

    goto :goto_0

    :cond_0
    move v1, v3

    .line 194
    :goto_0
    new-instance v2, Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    if-eqz v1, :cond_1

    const-string v1, "class"

    .line 195
    invoke-static {v3, v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->thisPropX(ZLjava/lang/String;)Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    move-result-object v1

    goto :goto_1

    :cond_1
    const-string v1, "this"

    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v1

    :goto_1
    invoke-direct {v2, v1}, Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;-><init>(Lorg/codehaus/groovy/ast/expr/Expression;)V

    .line 196
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->getArguments()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v1

    .line 197
    instance-of v4, v1, Lorg/codehaus/groovy/ast/expr/TupleExpression;

    if-eqz v4, :cond_2

    .line 198
    check-cast v1, Lorg/codehaus/groovy/ast/expr/TupleExpression;

    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/expr/TupleExpression;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_2
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v4

    if-eqz v4, :cond_3

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lorg/codehaus/groovy/ast/expr/Expression;

    .line 199
    invoke-virtual {p0, v4}, Lorg/codehaus/groovy/transform/trait/SuperCallTraitTransformer;->transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v4

    invoke-virtual {v2, v4}, Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;->addExpression(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/TupleExpression;

    goto :goto_2

    .line 202
    :cond_2
    invoke-virtual {p0, v1}, Lorg/codehaus/groovy/transform/trait/SuperCallTraitTransformer;->transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v1

    invoke-virtual {v2, v1}, Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;->addExpression(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/TupleExpression;

    .line 205
    :cond_3
    new-instance v1, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    new-instance v4, Lorg/codehaus/groovy/ast/expr/ClassExpression;

    invoke-direct {v4, v0}, Lorg/codehaus/groovy/ast/expr/ClassExpression;-><init>(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 207
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->getMethod()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/transform/trait/SuperCallTraitTransformer;->transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    invoke-direct {v1, v4, v0, v2}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;-><init>(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)V

    .line 210
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->getObjectExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->getObjectExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v2

    check-cast v2, Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->getObjectExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v2

    invoke-virtual {v0, v2}, Lorg/codehaus/groovy/ast/expr/Expression;->setSourcePosition(Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 211
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->isSpreadSafe()Z

    move-result p1

    invoke-virtual {v1, p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setSpreadSafe(Z)V

    .line 212
    invoke-virtual {v1, v3}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setImplicitThis(Z)V

    return-object v1

    .line 215
    :cond_4
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeExpressionTransformer;->transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    return-object p1
.end method

.method private transformPropertyExpression(Lorg/codehaus/groovy/ast/expr/PropertyExpression;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 10

    const-string v0, "assign.target"

    .line 138
    invoke-virtual {p1, v0}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->getNodeMetaData(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    if-nez v1, :cond_3

    .line 139
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->getObjectExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v1

    invoke-direct {p0, v1}, Lorg/codehaus/groovy/transform/trait/SuperCallTraitTransformer;->getTraitSuperTarget(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    if-eqz v1, :cond_3

    .line 141
    invoke-direct {p0, v1}, Lorg/codehaus/groovy/transform/trait/SuperCallTraitTransformer;->getHelper(Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v2

    .line 144
    new-instance v3, Lorg/codehaus/groovy/transform/trait/SuperCallTraitTransformer$$ExternalSyntheticLambda0;

    invoke-direct {v3, v2, p1}, Lorg/codehaus/groovy/transform/trait/SuperCallTraitTransformer$$ExternalSyntheticLambda0;-><init>(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/PropertyExpression;)V

    .line 161
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->getPropertyAsString()Ljava/lang/String;

    move-result-object v4

    const/4 v5, 0x0

    invoke-static {v4, v5}, Lgroovy/lang/MetaProperty;->getGetterName(Ljava/lang/String;Ljava/lang/Class;)Ljava/lang/String;

    move-result-object v4

    .line 162
    invoke-virtual {v2, v4}, Lorg/codehaus/groovy/ast/ClassNode;->getMethods(Ljava/lang/String;)Ljava/util/List;

    move-result-object v5

    invoke-interface {v5}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v5

    :cond_0
    invoke-interface {v5}, Ljava/util/Iterator;->hasNext()Z

    move-result v6

    const/4 v7, 0x0

    const/4 v8, 0x1

    if-eqz v6, :cond_1

    invoke-interface {v5}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Lorg/codehaus/groovy/ast/MethodNode;

    .line 163
    invoke-virtual {v6}, Lorg/codehaus/groovy/ast/MethodNode;->isStatic()Z

    move-result v9

    if-eqz v9, :cond_0

    invoke-virtual {v6}, Lorg/codehaus/groovy/ast/MethodNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v9

    array-length v9, v9

    if-ne v9, v8, :cond_0

    .line 164
    invoke-virtual {v6}, Lorg/codehaus/groovy/ast/MethodNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v8

    aget-object v7, v8, v7

    invoke-static {v7, v1}, Lorg/codehaus/groovy/transform/trait/SuperCallTraitTransformer;->isSelfType(Lorg/codehaus/groovy/ast/Parameter;Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v7

    if-eqz v7, :cond_0

    .line 165
    invoke-virtual {v6}, Lorg/codehaus/groovy/ast/MethodNode;->getReturnType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v7

    sget-object v8, Lorg/codehaus/groovy/ast/ClassHelper;->VOID_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v7, v8}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result v7

    if-nez v7, :cond_0

    .line 166
    invoke-interface {v3, v6}, Ljava/util/function/Function;->apply(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lorg/codehaus/groovy/ast/expr/Expression;

    return-object p1

    .line 170
    :cond_1
    new-instance v5, Ljava/lang/StringBuilder;

    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    const-string v6, "is"

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    const/4 v6, 0x3

    invoke-virtual {v4, v6}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v5, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    .line 171
    invoke-virtual {v2, v4}, Lorg/codehaus/groovy/ast/ClassNode;->getMethods(Ljava/lang/String;)Ljava/util/List;

    move-result-object v2

    invoke-interface {v2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :cond_2
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v4

    if-eqz v4, :cond_3

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lorg/codehaus/groovy/ast/MethodNode;

    .line 172
    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/MethodNode;->isStatic()Z

    move-result v5

    if-eqz v5, :cond_2

    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/MethodNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v5

    array-length v5, v5

    if-ne v5, v8, :cond_2

    .line 173
    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/MethodNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v5

    aget-object v5, v5, v7

    invoke-static {v5, v1}, Lorg/codehaus/groovy/transform/trait/SuperCallTraitTransformer;->isSelfType(Lorg/codehaus/groovy/ast/Parameter;Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v5

    if-eqz v5, :cond_2

    .line 174
    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/MethodNode;->getReturnType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v5

    sget-object v6, Lorg/codehaus/groovy/ast/ClassHelper;->boolean_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v5, v6}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result v5

    if-eqz v5, :cond_2

    .line 175
    invoke-interface {v3, v4}, Ljava/util/function/Function;->apply(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lorg/codehaus/groovy/ast/expr/Expression;

    return-object p1

    .line 180
    :cond_3
    invoke-virtual {p1, v0}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->removeNodeMetaData(Ljava/lang/Object;)V

    .line 181
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeExpressionTransformer;->transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    return-object p1
.end method


# virtual methods
.method protected getSourceUnit()Lorg/codehaus/groovy/control/SourceUnit;
    .locals 1

    .line 67
    iget-object v0, p0, Lorg/codehaus/groovy/transform/trait/SuperCallTraitTransformer;->unit:Lorg/codehaus/groovy/control/SourceUnit;

    return-object v0
.end method

.method public transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 1

    .line 72
    instance-of v0, p1, Lorg/codehaus/groovy/ast/expr/BinaryExpression;

    if-eqz v0, :cond_0

    .line 73
    check-cast p1, Lorg/codehaus/groovy/ast/expr/BinaryExpression;

    invoke-direct {p0, p1}, Lorg/codehaus/groovy/transform/trait/SuperCallTraitTransformer;->transformBinaryExpression(Lorg/codehaus/groovy/ast/expr/BinaryExpression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    return-object p1

    .line 75
    :cond_0
    instance-of v0, p1, Lorg/codehaus/groovy/ast/expr/ClosureExpression;

    if-eqz v0, :cond_1

    .line 76
    check-cast p1, Lorg/codehaus/groovy/ast/expr/ClosureExpression;

    invoke-direct {p0, p1}, Lorg/codehaus/groovy/transform/trait/SuperCallTraitTransformer;->transformClosureExpression(Lorg/codehaus/groovy/ast/expr/ClosureExpression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    return-object p1

    .line 78
    :cond_1
    instance-of v0, p1, Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    if-eqz v0, :cond_2

    .line 79
    check-cast p1, Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    invoke-direct {p0, p1}, Lorg/codehaus/groovy/transform/trait/SuperCallTraitTransformer;->transformPropertyExpression(Lorg/codehaus/groovy/ast/expr/PropertyExpression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    return-object p1

    .line 81
    :cond_2
    instance-of v0, p1, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    if-eqz v0, :cond_3

    .line 82
    check-cast p1, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    invoke-direct {p0, p1}, Lorg/codehaus/groovy/transform/trait/SuperCallTraitTransformer;->transformMethodCallExpression(Lorg/codehaus/groovy/ast/expr/MethodCallExpression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    return-object p1

    .line 84
    :cond_3
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeExpressionTransformer;->transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    return-object p1
.end method
