.class public Lorg/codehaus/groovy/transform/CompileDynamicProcessor;
.super Lorg/codehaus/groovy/transform/AnnotationCollectorTransform;
.source "CompileDynamicProcessor.java"


# static fields
.field private static final COMPILESTATIC_NODE:Lorg/codehaus/groovy/ast/ClassNode;

.field private static final TYPECHECKINGMODE_NODE:Lorg/codehaus/groovy/ast/ClassNode;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 36
    const-class v0, Lgroovy/transform/CompileStatic;

    invoke-static {v0}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/transform/CompileDynamicProcessor;->COMPILESTATIC_NODE:Lorg/codehaus/groovy/ast/ClassNode;

    .line 37
    const-class v0, Lgroovy/transform/TypeCheckingMode;

    invoke-static {v0}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/transform/CompileDynamicProcessor;->TYPECHECKINGMODE_NODE:Lorg/codehaus/groovy/ast/ClassNode;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 34
    invoke-direct {p0}, Lorg/codehaus/groovy/transform/AnnotationCollectorTransform;-><init>()V

    return-void
.end method


# virtual methods
.method public visit(Lorg/codehaus/groovy/ast/AnnotationNode;Lorg/codehaus/groovy/ast/AnnotationNode;Lorg/codehaus/groovy/ast/AnnotatedNode;Lorg/codehaus/groovy/control/SourceUnit;)Ljava/util/List;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/AnnotationNode;",
            "Lorg/codehaus/groovy/ast/AnnotationNode;",
            "Lorg/codehaus/groovy/ast/AnnotatedNode;",
            "Lorg/codehaus/groovy/control/SourceUnit;",
            ")",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/AnnotationNode;",
            ">;"
        }
    .end annotation

    .line 40
    new-instance p1, Lorg/codehaus/groovy/ast/AnnotationNode;

    sget-object p2, Lorg/codehaus/groovy/transform/CompileDynamicProcessor;->COMPILESTATIC_NODE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-direct {p1, p2}, Lorg/codehaus/groovy/ast/AnnotationNode;-><init>(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 41
    new-instance p2, Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    new-instance p3, Lorg/codehaus/groovy/ast/expr/ClassExpression;

    sget-object p4, Lorg/codehaus/groovy/transform/CompileDynamicProcessor;->TYPECHECKINGMODE_NODE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-direct {p3, p4}, Lorg/codehaus/groovy/ast/expr/ClassExpression;-><init>(Lorg/codehaus/groovy/ast/ClassNode;)V

    const-string p4, "SKIP"

    invoke-direct {p2, p3, p4}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;-><init>(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;)V

    const-string p3, "value"

    invoke-virtual {p1, p3, p2}, Lorg/codehaus/groovy/ast/AnnotationNode;->addMember(Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)V

    .line 42
    invoke-static {p1}, Ljava/util/Collections;->singletonList(Ljava/lang/Object;)Ljava/util/List;

    move-result-object p1

    return-object p1
.end method
