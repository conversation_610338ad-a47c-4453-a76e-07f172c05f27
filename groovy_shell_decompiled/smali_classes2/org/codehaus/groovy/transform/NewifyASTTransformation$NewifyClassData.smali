.class Lorg/codehaus/groovy/transform/NewifyASTTransformation$NewifyClassData;
.super Ljava/lang/Object;
.source "NewifyASTTransformation.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/codehaus/groovy/transform/NewifyASTTransformation;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0xa
    name = "NewifyClassData"
.end annotation


# instance fields
.field final name:Ljava/lang/String;

.field final type:Lorg/codehaus/groovy/ast/ClassNode;

.field types:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 0

    .line 516
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 517
    iput-object p1, p0, Lorg/codehaus/groovy/transform/NewifyASTTransformation$NewifyClassData;->name:Ljava/lang/String;

    .line 518
    iput-object p2, p0, Lorg/codehaus/groovy/transform/NewifyASTTransformation$NewifyClassData;->type:Lorg/codehaus/groovy/ast/ClassNode;

    return-void
.end method


# virtual methods
.method public addAdditionalType(Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 2

    .line 522
    iget-object v0, p0, Lorg/codehaus/groovy/transform/NewifyASTTransformation$NewifyClassData;->types:Ljava/util/List;

    if-nez v0, :cond_0

    .line 523
    new-instance v0, Ljava/util/LinkedList;

    invoke-direct {v0}, Ljava/util/LinkedList;-><init>()V

    iput-object v0, p0, Lorg/codehaus/groovy/transform/NewifyASTTransformation$NewifyClassData;->types:Ljava/util/List;

    .line 524
    iget-object v1, p0, Lorg/codehaus/groovy/transform/NewifyASTTransformation$NewifyClassData;->type:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 526
    :cond_0
    iget-object v0, p0, Lorg/codehaus/groovy/transform/NewifyASTTransformation$NewifyClassData;->types:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method
