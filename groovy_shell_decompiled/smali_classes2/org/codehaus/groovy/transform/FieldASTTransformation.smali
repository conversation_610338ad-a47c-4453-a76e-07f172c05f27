.class public Lorg/codehaus/groovy/transform/FieldASTTransformation;
.super Lorg/codehaus/groovy/ast/ClassCodeExpressionTransformer;
.source "FieldASTTransformation.java"

# interfaces
.implements Lorg/codehaus/groovy/transform/ASTTransformation;
.implements Lgroovyjarjarasm/asm/Opcodes;


# annotations
.annotation runtime Lorg/codehaus/groovy/transform/GroovyASTTransformation;
    phase = .enum Lorg/codehaus/groovy/control/CompilePhase;->SEMANTIC_ANALYSIS:Lorg/codehaus/groovy/control/CompilePhase;
.end annotation


# static fields
.field private static final ASTTRANSFORMCLASS_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

.field private static final LAZY_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

.field private static final MY_CLASS:Ljava/lang/Class;

.field private static final MY_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

.field private static final MY_TYPE_NAME:Ljava/lang/String;

.field private static final OPTION_TYPE:Lorg/codehaus/groovy/ast/ClassNode;


# instance fields
.field private candidate:Lorg/codehaus/groovy/ast/expr/DeclarationExpression;

.field private currentAIC:Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;

.field private currentClosure:Lorg/codehaus/groovy/ast/expr/ClosureExpression;

.field private fieldNode:Lorg/codehaus/groovy/ast/FieldNode;

.field private insideScriptBody:Z

.field private sourceUnit:Lorg/codehaus/groovy/control/SourceUnit;

.field private variableName:Ljava/lang/String;


# direct methods
.method static constructor <clinit>()V
    .locals 3

    .line 73
    const-class v0, Lgroovy/transform/Field;

    sput-object v0, Lorg/codehaus/groovy/transform/FieldASTTransformation;->MY_CLASS:Ljava/lang/Class;

    .line 74
    invoke-static {v0}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/transform/FieldASTTransformation;->MY_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    .line 75
    const-class v1, Lgroovy/lang/Lazy;

    invoke-static {v1}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    sput-object v1, Lorg/codehaus/groovy/transform/FieldASTTransformation;->LAZY_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    .line 76
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "@"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->getNameWithoutPackage()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/transform/FieldASTTransformation;->MY_TYPE_NAME:Ljava/lang/String;

    .line 77
    const-class v0, Lorg/codehaus/groovy/transform/GroovyASTTransformationClass;

    invoke-static {v0}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/transform/FieldASTTransformation;->ASTTRANSFORMCLASS_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    .line 78
    const-class v0, Lgroovy/cli/Option;

    invoke-static {v0}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/transform/FieldASTTransformation;->OPTION_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 71
    invoke-direct {p0}, Lorg/codehaus/groovy/ast/ClassCodeExpressionTransformer;-><init>()V

    return-void
.end method

.method private static acceptableTransform(Lorg/codehaus/groovy/ast/AnnotationNode;)Z
    .locals 1

    .line 153
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/AnnotationNode;->getClassNode()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p0

    sget-object v0, Lorg/codehaus/groovy/transform/FieldASTTransformation;->MY_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result p0

    xor-int/lit8 p0, p0, 0x1

    return p0
.end method

.method private adjustConstructorAndFields(ILorg/codehaus/groovy/ast/ClassNode;)V
    .locals 7

    .line 216
    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/ClassNode;->getDeclaredConstructors()Ljava/util/List;

    move-result-object v0

    .line 217
    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v1

    const/4 v2, 0x1

    if-ne v1, v2, :cond_2

    const/4 v1, 0x0

    .line 218
    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/ast/ConstructorNode;

    .line 219
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ConstructorNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v3

    .line 220
    array-length v4, v3

    sub-int/2addr v4, v2

    new-array v2, v4, [Lorg/codehaus/groovy/ast/Parameter;

    move v4, v1

    .line 222
    :goto_0
    array-length v5, v3

    if-ge v1, v5, :cond_1

    if-eq v1, p1, :cond_0

    add-int/lit8 v5, v4, 0x1

    .line 224
    aget-object v6, v3, v1

    aput-object v6, v2, v4

    move v4, v5

    :cond_0
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 227
    :cond_1
    invoke-virtual {p2, v0}, Lorg/codehaus/groovy/ast/ClassNode;->removeConstructor(Lorg/codehaus/groovy/ast/ConstructorNode;)V

    .line 229
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ConstructorNode;->getModifiers()I

    move-result p1

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ConstructorNode;->getExceptions()[Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ConstructorNode;->getCode()Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v0

    invoke-static {p2, p1, v2, v1, v0}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->addGeneratedConstructor(Lorg/codehaus/groovy/ast/ClassNode;I[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/ConstructorNode;

    .line 230
    iget-object p1, p0, Lorg/codehaus/groovy/transform/FieldASTTransformation;->variableName:Ljava/lang/String;

    invoke-virtual {p2, p1}, Lorg/codehaus/groovy/ast/ClassNode;->removeField(Ljava/lang/String;)V

    :cond_2
    return-void
.end method

.method private adjustToClassVar(Lorg/codehaus/groovy/ast/expr/VariableExpression;)V
    .locals 3

    .line 238
    iget-object v0, p0, Lorg/codehaus/groovy/transform/FieldASTTransformation;->fieldNode:Lorg/codehaus/groovy/ast/FieldNode;

    invoke-virtual {p1, v0}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->setAccessedVariable(Lorg/codehaus/groovy/ast/Variable;)V

    .line 239
    iget-object p1, p0, Lorg/codehaus/groovy/transform/FieldASTTransformation;->currentClosure:Lorg/codehaus/groovy/ast/expr/ClosureExpression;

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/ClosureExpression;->getVariableScope()Lorg/codehaus/groovy/ast/VariableScope;

    move-result-object p1

    .line 240
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/VariableScope;->getReferencedLocalVariablesIterator()Ljava/util/Iterator;

    move-result-object v0

    .line 241
    :cond_0
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    .line 242
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/ast/Variable;

    .line 243
    invoke-interface {v1}, Lorg/codehaus/groovy/ast/Variable;->getName()Ljava/lang/String;

    move-result-object v1

    iget-object v2, p0, Lorg/codehaus/groovy/transform/FieldASTTransformation;->variableName:Ljava/lang/String;

    invoke-virtual {v1, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {v0}, Ljava/util/Iterator;->remove()V

    goto :goto_0

    .line 245
    :cond_1
    iget-object v0, p0, Lorg/codehaus/groovy/transform/FieldASTTransformation;->fieldNode:Lorg/codehaus/groovy/ast/FieldNode;

    invoke-virtual {p1, v0}, Lorg/codehaus/groovy/ast/VariableScope;->putReferencedClassVariable(Lorg/codehaus/groovy/ast/Variable;)V

    return-void
.end method

.method private adjustedArgList(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/util/List;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/expr/Expression;",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/expr/Expression;",
            ">;)",
            "Lorg/codehaus/groovy/ast/expr/Expression;"
        }
    .end annotation

    .line 206
    new-instance v0, Ljava/util/ArrayList;

    invoke-interface {p2}, Ljava/util/List;->size()I

    move-result v1

    add-int/lit8 v1, v1, -0x1

    invoke-direct {v0, v1}, Ljava/util/ArrayList;-><init>(I)V

    .line 207
    invoke-interface {p2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p2

    :cond_0
    :goto_0
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/ast/expr/Expression;

    if-eq p1, v1, :cond_0

    .line 209
    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    .line 212
    :cond_1
    new-instance p1, Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    invoke-direct {p1, v0}, Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;-><init>(Ljava/util/List;)V

    return-object p1
.end method

.method private matchesCandidate(Lorg/codehaus/groovy/ast/expr/Expression;)Z
    .locals 1

    .line 202
    instance-of v0, p1, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    if-eqz v0, :cond_0

    check-cast p1, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->getAccessedVariable()Lorg/codehaus/groovy/ast/Variable;

    move-result-object p1

    iget-object v0, p0, Lorg/codehaus/groovy/transform/FieldASTTransformation;->candidate:Lorg/codehaus/groovy/ast/expr/DeclarationExpression;

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/DeclarationExpression;->getVariableExpression()Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v0

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->getAccessedVariable()Lorg/codehaus/groovy/ast/Variable;

    move-result-object v0

    if-ne p1, v0, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method private static notTransform(Lorg/codehaus/groovy/ast/ClassNode;)Z
    .locals 1

    .line 157
    sget-object v0, Lorg/codehaus/groovy/transform/FieldASTTransformation;->ASTTRANSFORMCLASS_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/ast/ClassNode;->getAnnotations(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/util/List;

    move-result-object p0

    invoke-interface {p0}, Ljava/util/List;->isEmpty()Z

    move-result p0

    return p0
.end method


# virtual methods
.method protected getSourceUnit()Lorg/codehaus/groovy/control/SourceUnit;
    .locals 1

    .line 286
    iget-object v0, p0, Lorg/codehaus/groovy/transform/FieldASTTransformation;->sourceUnit:Lorg/codehaus/groovy/control/SourceUnit;

    return-object v0
.end method

.method public transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 5

    const/4 v0, 0x0

    if-nez p1, :cond_0

    return-object v0

    .line 163
    :cond_0
    instance-of v1, p1, Lorg/codehaus/groovy/ast/expr/DeclarationExpression;

    if-eqz v1, :cond_2

    .line 164
    move-object v0, p1

    check-cast v0, Lorg/codehaus/groovy/ast/expr/DeclarationExpression;

    .line 165
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/DeclarationExpression;->getLeftExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    iget-object v1, p0, Lorg/codehaus/groovy/transform/FieldASTTransformation;->candidate:Lorg/codehaus/groovy/ast/expr/DeclarationExpression;

    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/expr/DeclarationExpression;->getLeftExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v1

    if-ne v0, v1, :cond_6

    .line 166
    iget-boolean v0, p0, Lorg/codehaus/groovy/transform/FieldASTTransformation;->insideScriptBody:Z

    if-eqz v0, :cond_1

    .line 170
    invoke-static {}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->nullX()Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    move-result-object p1

    return-object p1

    .line 172
    :cond_1
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Annotation "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    sget-object v1, Lorg/codehaus/groovy/transform/FieldASTTransformation;->MY_TYPE_NAME:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, " can only be used within a Script body."

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0, p1}, Lorg/codehaus/groovy/transform/FieldASTTransformation;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    return-object p1

    .line 175
    :cond_2
    iget-boolean v1, p0, Lorg/codehaus/groovy/transform/FieldASTTransformation;->insideScriptBody:Z

    if-eqz v1, :cond_3

    instance-of v1, p1, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    if-eqz v1, :cond_3

    iget-object v1, p0, Lorg/codehaus/groovy/transform/FieldASTTransformation;->currentClosure:Lorg/codehaus/groovy/ast/expr/ClosureExpression;

    if-eqz v1, :cond_3

    .line 176
    move-object v0, p1

    check-cast v0, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    .line 177
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->getName()Ljava/lang/String;

    move-result-object v1

    iget-object v2, p0, Lorg/codehaus/groovy/transform/FieldASTTransformation;->variableName:Ljava/lang/String;

    invoke-virtual {v1, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_6

    .line 178
    invoke-direct {p0, v0}, Lorg/codehaus/groovy/transform/FieldASTTransformation;->adjustToClassVar(Lorg/codehaus/groovy/ast/expr/VariableExpression;)V

    return-object v0

    .line 181
    :cond_3
    iget-object v1, p0, Lorg/codehaus/groovy/transform/FieldASTTransformation;->currentAIC:Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;

    if-eqz v1, :cond_6

    instance-of v1, p1, Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    if-eqz v1, :cond_6

    .line 185
    move-object v1, p1

    check-cast v1, Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;->getExpressions()Ljava/util/List;

    move-result-object v1

    const/4 v2, 0x0

    .line 186
    :goto_0
    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v3

    if-ge v2, v3, :cond_5

    .line 187
    invoke-interface {v1, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lorg/codehaus/groovy/ast/expr/Expression;

    .line 188
    invoke-direct {p0, v3}, Lorg/codehaus/groovy/transform/FieldASTTransformation;->matchesCandidate(Lorg/codehaus/groovy/ast/expr/Expression;)Z

    move-result v4

    if-eqz v4, :cond_4

    .line 190
    iget-object v0, p0, Lorg/codehaus/groovy/transform/FieldASTTransformation;->currentAIC:Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    invoke-direct {p0, v2, v0}, Lorg/codehaus/groovy/transform/FieldASTTransformation;->adjustConstructorAndFields(ILorg/codehaus/groovy/ast/ClassNode;)V

    move-object v0, v3

    goto :goto_1

    :cond_4
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_5
    :goto_1
    if-eqz v0, :cond_6

    .line 195
    invoke-direct {p0, v0, v1}, Lorg/codehaus/groovy/transform/FieldASTTransformation;->adjustedArgList(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/util/List;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    return-object p1

    .line 198
    :cond_6
    invoke-virtual {p1, p0}, Lorg/codehaus/groovy/ast/expr/Expression;->transformExpression(Lorg/codehaus/groovy/ast/expr/ExpressionTransformer;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    return-object p1
.end method

.method public visit([Lorg/codehaus/groovy/ast/ASTNode;Lorg/codehaus/groovy/control/SourceUnit;)V
    .locals 11

    .line 88
    iput-object p2, p0, Lorg/codehaus/groovy/transform/FieldASTTransformation;->sourceUnit:Lorg/codehaus/groovy/control/SourceUnit;

    .line 89
    array-length v0, p1

    const/4 v1, 0x2

    if-ne v0, v1, :cond_a

    const/4 v0, 0x0

    aget-object v1, p1, v0

    instance-of v1, v1, Lorg/codehaus/groovy/ast/AnnotationNode;

    if-eqz v1, :cond_a

    const/4 v1, 0x1

    aget-object v2, p1, v1

    instance-of v2, v2, Lorg/codehaus/groovy/ast/AnnotatedNode;

    if-eqz v2, :cond_a

    .line 93
    aget-object v2, p1, v1

    check-cast v2, Lorg/codehaus/groovy/ast/AnnotatedNode;

    .line 94
    aget-object p1, p1, v0

    check-cast p1, Lorg/codehaus/groovy/ast/AnnotationNode;

    .line 95
    sget-object v3, Lorg/codehaus/groovy/transform/FieldASTTransformation;->MY_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/AnnotationNode;->getClassNode()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    invoke-virtual {v3, p1}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-nez p1, :cond_0

    return-void

    .line 97
    :cond_0
    instance-of p1, v2, Lorg/codehaus/groovy/ast/expr/DeclarationExpression;

    if-eqz p1, :cond_9

    .line 98
    move-object p1, v2

    check-cast p1, Lorg/codehaus/groovy/ast/expr/DeclarationExpression;

    .line 99
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/DeclarationExpression;->getDeclaringClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v10

    .line 100
    invoke-virtual {v10}, Lorg/codehaus/groovy/ast/ClassNode;->isScript()Z

    move-result v3

    const-string v4, "Annotation "

    if-nez v3, :cond_1

    .line 101
    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p1, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    sget-object p2, Lorg/codehaus/groovy/transform/FieldASTTransformation;->MY_TYPE_NAME:Ljava/lang/String;

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string p2, " can only be used within a Script."

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1, v2}, Lorg/codehaus/groovy/transform/FieldASTTransformation;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    return-void

    .line 104
    :cond_1
    iput-object p1, p0, Lorg/codehaus/groovy/transform/FieldASTTransformation;->candidate:Lorg/codehaus/groovy/ast/expr/DeclarationExpression;

    .line 106
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/DeclarationExpression;->isMultipleAssignmentDeclaration()Z

    move-result v3

    if-eqz v3, :cond_2

    .line 107
    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p1, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    sget-object p2, Lorg/codehaus/groovy/transform/FieldASTTransformation;->MY_TYPE_NAME:Ljava/lang/String;

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string p2, " not supported with multiple assignment notation."

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1, v2}, Lorg/codehaus/groovy/transform/FieldASTTransformation;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    return-void

    .line 110
    :cond_2
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/DeclarationExpression;->getVariableExpression()Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v2

    .line 111
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->getName()Ljava/lang/String;

    move-result-object v3

    iput-object v3, p0, Lorg/codehaus/groovy/transform/FieldASTTransformation;->variableName:Ljava/lang/String;

    .line 113
    new-instance v3, Lorg/codehaus/groovy/ast/FieldNode;

    iget-object v5, p0, Lorg/codehaus/groovy/transform/FieldASTTransformation;->variableName:Ljava/lang/String;

    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->getModifiers()I

    move-result v6

    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v7

    const/4 v8, 0x0

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/DeclarationExpression;->getRightExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v9

    move-object v4, v3

    invoke-direct/range {v4 .. v9}, Lorg/codehaus/groovy/ast/FieldNode;-><init>(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;)V

    iput-object v3, p0, Lorg/codehaus/groovy/transform/FieldASTTransformation;->fieldNode:Lorg/codehaus/groovy/ast/FieldNode;

    .line 114
    invoke-virtual {v3, p1}, Lorg/codehaus/groovy/ast/FieldNode;->setSourcePosition(Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 115
    iget-object v3, p0, Lorg/codehaus/groovy/transform/FieldASTTransformation;->fieldNode:Lorg/codehaus/groovy/ast/FieldNode;

    invoke-virtual {v10, v3}, Lorg/codehaus/groovy/ast/ClassNode;->addField(Lorg/codehaus/groovy/ast/FieldNode;)V

    .line 117
    iget-object v3, p0, Lorg/codehaus/groovy/transform/FieldASTTransformation;->fieldNode:Lorg/codehaus/groovy/ast/FieldNode;

    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/FieldNode;->isFinal()Z

    move-result v3

    if-eqz v3, :cond_3

    .line 118
    sget-object v0, Lorg/codehaus/groovy/transform/FieldASTTransformation;->OPTION_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {p1, v0}, Lorg/codehaus/groovy/ast/expr/DeclarationExpression;->getAnnotations(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/util/List;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/List;->isEmpty()Z

    move-result v1

    if-nez v1, :cond_4

    .line 119
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "Can\'t have a final field also annotated with @"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->getNameWithoutPackage()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0, p1}, Lorg/codehaus/groovy/transform/FieldASTTransformation;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    goto :goto_0

    .line 122
    :cond_3
    iget-object v3, p0, Lorg/codehaus/groovy/transform/FieldASTTransformation;->variableName:Ljava/lang/String;

    invoke-static {v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->getSetterName(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v4

    const/16 v5, 0x1001

    .line 123
    sget-object v6, Lorg/codehaus/groovy/ast/ClassHelper;->VOID_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    new-array v3, v1, [Lorg/codehaus/groovy/ast/Parameter;

    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v2

    iget-object v7, p0, Lorg/codehaus/groovy/transform/FieldASTTransformation;->variableName:Ljava/lang/String;

    invoke-static {v2, v7}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->param(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v2

    aput-object v2, v3, v0

    invoke-static {v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->params([Lorg/codehaus/groovy/ast/Parameter;)[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v7

    sget-object v8, Lorg/codehaus/groovy/ast/ClassNode;->EMPTY_ARRAY:[Lorg/codehaus/groovy/ast/ClassNode;

    new-array v1, v1, [Lorg/codehaus/groovy/ast/stmt/Statement;

    const-string v2, "this"

    .line 124
    invoke-static {v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v2

    iget-object v3, p0, Lorg/codehaus/groovy/transform/FieldASTTransformation;->variableName:Ljava/lang/String;

    invoke-static {v2, v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->propX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    move-result-object v2

    iget-object v3, p0, Lorg/codehaus/groovy/transform/FieldASTTransformation;->variableName:Ljava/lang/String;

    invoke-static {v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v3

    invoke-static {v2, v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignX(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v2

    invoke-static {v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->stmt(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v2

    aput-object v2, v1, v0

    .line 123
    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->block([Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    move-result-object v9

    move-object v3, v10

    invoke-virtual/range {v3 .. v9}, Lorg/codehaus/groovy/ast/ClassNode;->addMethod(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/MethodNode;

    .line 128
    :cond_4
    :goto_0
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/DeclarationExpression;->getAnnotations()Ljava/util/List;

    move-result-object p1

    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_5
    :goto_1
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_8

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/ast/AnnotationNode;

    .line 130
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/AnnotationNode;->getClassNode()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    sget-object v2, Lorg/codehaus/groovy/transform/FieldASTTransformation;->LAZY_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v1, v2}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_6

    .line 131
    iget-object v1, p0, Lorg/codehaus/groovy/transform/FieldASTTransformation;->fieldNode:Lorg/codehaus/groovy/ast/FieldNode;

    invoke-static {p0, v0, v1}, Lorg/codehaus/groovy/transform/LazyASTTransformation;->visitField(Lorg/codehaus/groovy/transform/ErrorCollecting;Lorg/codehaus/groovy/ast/AnnotationNode;Lorg/codehaus/groovy/ast/FieldNode;)V

    .line 134
    :cond_6
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/AnnotationNode;->getClassNode()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    invoke-static {v1}, Lorg/codehaus/groovy/transform/FieldASTTransformation;->notTransform(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v1

    if-nez v1, :cond_7

    invoke-static {v0}, Lorg/codehaus/groovy/transform/FieldASTTransformation;->acceptableTransform(Lorg/codehaus/groovy/ast/AnnotationNode;)Z

    move-result v1

    if-eqz v1, :cond_5

    .line 135
    :cond_7
    iget-object v1, p0, Lorg/codehaus/groovy/transform/FieldASTTransformation;->fieldNode:Lorg/codehaus/groovy/ast/FieldNode;

    invoke-virtual {v1, v0}, Lorg/codehaus/groovy/ast/FieldNode;->addAnnotation(Lorg/codehaus/groovy/ast/AnnotationNode;)V

    goto :goto_1

    .line 139
    :cond_8
    invoke-super {p0, v10}, Lorg/codehaus/groovy/ast/ClassCodeExpressionTransformer;->visitClass(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 143
    new-instance p1, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;

    invoke-direct {p1, p2}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;-><init>(Lorg/codehaus/groovy/control/SourceUnit;)V

    invoke-virtual {p1, v10}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->visitClass(Lorg/codehaus/groovy/ast/ClassNode;)V

    :cond_9
    return-void

    .line 90
    :cond_a
    new-instance p2, Lorg/codehaus/groovy/GroovyBugError;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Internal error: expecting [AnnotationNode, AnnotatedNode] but got: "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-static {p1}, Ljava/util/Arrays;->asList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {p2, p1}, Lorg/codehaus/groovy/GroovyBugError;-><init>(Ljava/lang/String;)V

    throw p2
.end method

.method public visitClosureExpression(Lorg/codehaus/groovy/ast/expr/ClosureExpression;)V
    .locals 1

    .line 250
    iget-object v0, p0, Lorg/codehaus/groovy/transform/FieldASTTransformation;->currentClosure:Lorg/codehaus/groovy/ast/expr/ClosureExpression;

    .line 251
    iput-object p1, p0, Lorg/codehaus/groovy/transform/FieldASTTransformation;->currentClosure:Lorg/codehaus/groovy/ast/expr/ClosureExpression;

    .line 252
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeExpressionTransformer;->visitClosureExpression(Lorg/codehaus/groovy/ast/expr/ClosureExpression;)V

    .line 253
    iput-object v0, p0, Lorg/codehaus/groovy/transform/FieldASTTransformation;->currentClosure:Lorg/codehaus/groovy/ast/expr/ClosureExpression;

    return-void
.end method

.method public visitConstructorCallExpression(Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;)V
    .locals 3

    .line 258
    iget-boolean v0, p0, Lorg/codehaus/groovy/transform/FieldASTTransformation;->insideScriptBody:Z

    if-eqz v0, :cond_2

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;->isUsingAnonymousInnerClass()Z

    move-result v0

    if-nez v0, :cond_0

    goto :goto_0

    .line 259
    :cond_0
    iget-object v0, p0, Lorg/codehaus/groovy/transform/FieldASTTransformation;->currentAIC:Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;

    .line 260
    iput-object p1, p0, Lorg/codehaus/groovy/transform/FieldASTTransformation;->currentAIC:Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;

    .line 261
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;->getArguments()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v1

    invoke-virtual {p0, v1}, Lorg/codehaus/groovy/transform/FieldASTTransformation;->transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v1

    .line 262
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;->getArguments()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v2

    instance-of v2, v2, Lorg/codehaus/groovy/ast/expr/TupleExpression;

    if-eqz v2, :cond_1

    instance-of v2, v1, Lorg/codehaus/groovy/ast/expr/TupleExpression;

    if-eqz v2, :cond_1

    .line 263
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;->getArguments()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    check-cast p1, Lorg/codehaus/groovy/ast/expr/TupleExpression;

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/TupleExpression;->getExpressions()Ljava/util/List;

    move-result-object p1

    .line 264
    invoke-interface {p1}, Ljava/util/List;->clear()V

    .line 265
    check-cast v1, Lorg/codehaus/groovy/ast/expr/TupleExpression;

    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/expr/TupleExpression;->getExpressions()Ljava/util/List;

    move-result-object v1

    invoke-interface {p1, v1}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 267
    :cond_1
    iput-object v0, p0, Lorg/codehaus/groovy/transform/FieldASTTransformation;->currentAIC:Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;

    :cond_2
    :goto_0
    return-void
.end method

.method public visitExpressionStatement(Lorg/codehaus/groovy/ast/stmt/ExpressionStatement;)V
    .locals 1

    .line 280
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/stmt/ExpressionStatement;->getExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    .line 281
    invoke-virtual {v0, p0}, Lorg/codehaus/groovy/ast/expr/Expression;->visit(Lorg/codehaus/groovy/ast/GroovyCodeVisitor;)V

    .line 282
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeExpressionTransformer;->visitExpressionStatement(Lorg/codehaus/groovy/ast/stmt/ExpressionStatement;)V

    return-void
.end method

.method public visitMethod(Lorg/codehaus/groovy/ast/MethodNode;)V
    .locals 2

    .line 272
    iget-boolean v0, p0, Lorg/codehaus/groovy/transform/FieldASTTransformation;->insideScriptBody:Z

    .line 273
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->isScriptBody()Z

    move-result v1

    if-eqz v1, :cond_0

    const/4 v1, 0x1

    iput-boolean v1, p0, Lorg/codehaus/groovy/transform/FieldASTTransformation;->insideScriptBody:Z

    .line 274
    :cond_0
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeExpressionTransformer;->visitMethod(Lorg/codehaus/groovy/ast/MethodNode;)V

    .line 275
    iput-boolean v0, p0, Lorg/codehaus/groovy/transform/FieldASTTransformation;->insideScriptBody:Z

    return-void
.end method
