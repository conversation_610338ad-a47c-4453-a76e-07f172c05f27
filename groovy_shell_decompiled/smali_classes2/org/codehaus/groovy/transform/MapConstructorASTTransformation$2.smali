.class Lorg/codehaus/groovy/transform/MapConstructorASTTransformation$2;
.super Lorg/codehaus/groovy/ast/ClassCodeExpressionTransformer;
.source "MapConstructorASTTransformation.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/codehaus/groovy/transform/MapConstructorASTTransformation;->makeMapTypedArgsTransformer()Lorg/codehaus/groovy/ast/ClassCodeExpressionTransformer;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# direct methods
.method constructor <init>()V
    .locals 0

    .line 234
    invoke-direct {p0}, Lorg/codehaus/groovy/ast/ClassCodeExpressionTransformer;-><init>()V

    return-void
.end method


# virtual methods
.method protected getSourceUnit()Lorg/codehaus/groovy/control/SourceUnit;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 3

    .line 237
    instance-of v0, p1, Lorg/codehaus/groovy/ast/expr/ClosureExpression;

    if-eqz v0, :cond_0

    .line 238
    move-object v0, p1

    check-cast v0, Lorg/codehaus/groovy/ast/expr/ClosureExpression;

    .line 239
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/ClosureExpression;->getCode()Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v0

    invoke-virtual {v0, p0}, Lorg/codehaus/groovy/ast/stmt/Statement;->visit(Lorg/codehaus/groovy/ast/GroovyCodeVisitor;)V

    goto :goto_0

    .line 240
    :cond_0
    instance-of v0, p1, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    if-eqz v0, :cond_1

    .line 241
    move-object v0, p1

    check-cast v0, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    .line 242
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->getName()Ljava/lang/String;

    move-result-object v1

    const-string v2, "args"

    invoke-virtual {v2, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->getAccessedVariable()Lorg/codehaus/groovy/ast/Variable;

    move-result-object v1

    instance-of v1, v1, Lorg/codehaus/groovy/ast/DynamicVariable;

    if-eqz v1, :cond_1

    .line 243
    invoke-static {}, Lorg/codehaus/groovy/transform/MapConstructorASTTransformation;->access$000()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    invoke-static {p1, v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->param(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/Parameter;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object p1

    .line 244
    invoke-virtual {p1, v0}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->setSourcePosition(Lorg/codehaus/groovy/ast/ASTNode;)V

    return-object p1

    .line 248
    :cond_1
    :goto_0
    invoke-virtual {p1, p0}, Lorg/codehaus/groovy/ast/expr/Expression;->transformExpression(Lorg/codehaus/groovy/ast/expr/ExpressionTransformer;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    return-object p1
.end method
