.class public final synthetic Lorg/codehaus/groovy/transform/AutoFinalASTTransformation$$ExternalSyntheticLambda0;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Predicate;


# instance fields
.field public final synthetic f$0:Lorg/codehaus/groovy/transform/AutoFinalASTTransformation;


# direct methods
.method public synthetic constructor <init>(Lorg/codehaus/groovy/transform/AutoFinalASTTransformation;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/codehaus/groovy/transform/AutoFinalASTTransformation$$ExternalSyntheticLambda0;->f$0:Lorg/codehaus/groovy/transform/AutoFinalASTTransformation;

    return-void
.end method


# virtual methods
.method public final test(Ljava/lang/Object;)Z
    .locals 1

    iget-object v0, p0, Lorg/codehaus/groovy/transform/AutoFinalASTTransformation$$ExternalSyntheticLambda0;->f$0:Lorg/codehaus/groovy/transform/AutoFinalASTTransformation;

    check-cast p1, Lorg/codehaus/groovy/ast/AnnotationNode;

    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/transform/AutoFinalASTTransformation;->lambda$isEnabled$0$org-codehaus-groovy-transform-AutoFinalASTTransformation(Lorg/codehaus/groovy/ast/AnnotationNode;)Z

    move-result p1

    return p1
.end method
