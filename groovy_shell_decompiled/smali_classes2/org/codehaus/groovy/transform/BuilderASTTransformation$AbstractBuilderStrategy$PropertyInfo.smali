.class public Lorg/codehaus/groovy/transform/BuilderASTTransformation$AbstractBuilderStrategy$PropertyInfo;
.super Ljava/lang/Object;
.source "BuilderASTTransformation.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/codehaus/groovy/transform/BuilderASTTransformation$AbstractBuilderStrategy;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0xc
    name = "PropertyInfo"
.end annotation


# instance fields
.field private name:Ljava/lang/String;

.field private type:Lorg/codehaus/groovy/ast/ClassNode;


# direct methods
.method public constructor <init>(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 0

    .line 213
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 214
    iput-object p1, p0, Lorg/codehaus/groovy/transform/BuilderASTTransformation$AbstractBuilderStrategy$PropertyInfo;->name:Ljava/lang/String;

    .line 215
    iput-object p2, p0, Lorg/codehaus/groovy/transform/BuilderASTTransformation$AbstractBuilderStrategy$PropertyInfo;->type:Lorg/codehaus/groovy/ast/ClassNode;

    return-void
.end method


# virtual methods
.method public getName()Ljava/lang/String;
    .locals 1

    .line 222
    iget-object v0, p0, Lorg/codehaus/groovy/transform/BuilderASTTransformation$AbstractBuilderStrategy$PropertyInfo;->name:Ljava/lang/String;

    return-object v0
.end method

.method public getType()Lorg/codehaus/groovy/ast/ClassNode;
    .locals 1

    .line 226
    iget-object v0, p0, Lorg/codehaus/groovy/transform/BuilderASTTransformation$AbstractBuilderStrategy$PropertyInfo;->type:Lorg/codehaus/groovy/ast/ClassNode;

    return-object v0
.end method

.method public setName(Ljava/lang/String;)V
    .locals 0

    .line 230
    iput-object p1, p0, Lorg/codehaus/groovy/transform/BuilderASTTransformation$AbstractBuilderStrategy$PropertyInfo;->name:Ljava/lang/String;

    return-void
.end method

.method public setType(Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 0

    .line 234
    iput-object p1, p0, Lorg/codehaus/groovy/transform/BuilderASTTransformation$AbstractBuilderStrategy$PropertyInfo;->type:Lorg/codehaus/groovy/ast/ClassNode;

    return-void
.end method
