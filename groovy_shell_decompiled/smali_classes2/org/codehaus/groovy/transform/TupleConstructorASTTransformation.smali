.class public Lorg/codehaus/groovy/transform/TupleConstructorASTTransformation;
.super Lorg/codehaus/groovy/transform/AbstractASTTransformation;
.source "TupleConstructorASTTransformation.java"

# interfaces
.implements Lgroovy/transform/CompilationUnitAware;


# annotations
.annotation runtime Lorg/codehaus/groovy/transform/GroovyASTTransformation;
    phase = .enum Lorg/codehaus/groovy/control/CompilePhase;->CANONICALIZATION:Lorg/codehaus/groovy/control/CompilePhase;
.end annotation


# static fields
.field private static final CHECK_METHOD_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

.field private static final LHMAP_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

.field private static final MAP_CONSTRUCTOR_CLASS:Ljava/lang/Class;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/lang/Class<",
            "+",
            "Ljava/lang/annotation/Annotation;",
            ">;"
        }
    .end annotation
.end field

.field static final MY_CLASS:Ljava/lang/Class;

.field static final MY_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

.field static final MY_TYPE_NAME:Ljava/lang/String;

.field private static final primitivesInitialValues:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/Class<",
            "*>;",
            "Lorg/codehaus/groovy/ast/expr/Expression;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field private compilationUnit:Lorg/codehaus/groovy/control/CompilationUnit;


# direct methods
.method static constructor <clinit>()V
    .locals 4

    .line 92
    const-class v0, Lgroovy/transform/TupleConstructor;

    sput-object v0, Lorg/codehaus/groovy/transform/TupleConstructorASTTransformation;->MY_CLASS:Ljava/lang/Class;

    .line 93
    invoke-static {v0}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/transform/TupleConstructorASTTransformation;->MY_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    .line 94
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "@"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->getNameWithoutPackage()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/transform/TupleConstructorASTTransformation;->MY_TYPE_NAME:Ljava/lang/String;

    .line 95
    const-class v0, Ljava/util/LinkedHashMap;

    const/4 v1, 0x0

    invoke-static {v0, v1}, Lorg/codehaus/groovy/ast/ClassHelper;->makeWithoutCaching(Ljava/lang/Class;Z)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/transform/TupleConstructorASTTransformation;->LHMAP_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    .line 96
    const-class v0, Lorg/codehaus/groovy/transform/ImmutableASTTransformation;

    invoke-static {v0}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/transform/TupleConstructorASTTransformation;->CHECK_METHOD_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    .line 97
    const-class v0, Lgroovy/transform/MapConstructor;

    sput-object v0, Lorg/codehaus/groovy/transform/TupleConstructorASTTransformation;->MAP_CONSTRUCTOR_CLASS:Ljava/lang/Class;

    .line 101
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->constX(Ljava/lang/Object;)Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    move-result-object v0

    const-wide/16 v1, 0x0

    .line 102
    invoke-static {v1, v2}, Ljava/lang/Double;->valueOf(D)Ljava/lang/Double;

    move-result-object v1

    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->constX(Ljava/lang/Object;)Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    move-result-object v1

    .line 103
    new-instance v2, Ljava/util/HashMap;

    invoke-direct {v2}, Ljava/util/HashMap;-><init>()V

    sput-object v2, Lorg/codehaus/groovy/transform/TupleConstructorASTTransformation;->primitivesInitialValues:Ljava/util/Map;

    .line 104
    sget-object v3, Ljava/lang/Integer;->TYPE:Ljava/lang/Class;

    invoke-interface {v2, v3, v0}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 105
    sget-object v3, Ljava/lang/Long;->TYPE:Ljava/lang/Class;

    invoke-interface {v2, v3, v0}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 106
    sget-object v3, Ljava/lang/Short;->TYPE:Ljava/lang/Class;

    invoke-interface {v2, v3, v0}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 107
    sget-object v3, Ljava/lang/Byte;->TYPE:Ljava/lang/Class;

    invoke-interface {v2, v3, v0}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 108
    sget-object v3, Ljava/lang/Character;->TYPE:Ljava/lang/Class;

    invoke-interface {v2, v3, v0}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 109
    sget-object v0, Ljava/lang/Float;->TYPE:Ljava/lang/Class;

    invoke-interface {v2, v0, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 110
    sget-object v0, Ljava/lang/Double;->TYPE:Ljava/lang/Class;

    invoke-interface {v2, v0, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 111
    sget-object v0, Ljava/lang/Boolean;->TYPE:Ljava/lang/Class;

    sget-object v1, Lorg/codehaus/groovy/ast/expr/ConstantExpression;->FALSE:Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    invoke-interface {v2, v0, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 89
    invoke-direct {p0}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;-><init>()V

    return-void
.end method

.method public static addSpecialMapConstructors(ILorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Z)V
    .locals 5

    const/4 v0, 0x1

    new-array v0, v0, [Lorg/codehaus/groovy/ast/Parameter;

    .line 306
    new-instance v1, Lorg/codehaus/groovy/ast/Parameter;

    sget-object v2, Lorg/codehaus/groovy/transform/TupleConstructorASTTransformation;->LHMAP_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    const-string v3, "__namedArgs"

    invoke-direct {v1, v2, v3}, Lorg/codehaus/groovy/ast/Parameter;-><init>(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)V

    const/4 v4, 0x0

    aput-object v1, v0, v4

    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->params([Lorg/codehaus/groovy/ast/Parameter;)[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v0

    .line 307
    new-instance v1, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    invoke-direct {v1}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;-><init>()V

    .line 308
    invoke-static {v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v3

    .line 309
    aget-object v4, v0, v4

    invoke-virtual {v3, v4}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->setAccessedVariable(Lorg/codehaus/groovy/ast/Variable;)V

    .line 310
    invoke-static {v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->equalsNullX(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/BooleanExpression;

    move-result-object v4

    .line 311
    invoke-static {p2}, Lorg/codehaus/groovy/transform/TupleConstructorASTTransformation;->illegalArgumentBlock(Ljava/lang/String;)Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    move-result-object p2

    .line 312
    invoke-static {p1, v3}, Lorg/codehaus/groovy/transform/TupleConstructorASTTransformation;->processArgsBlock(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/VariableExpression;)Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    move-result-object v3

    .line 310
    invoke-static {v4, p2, v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ifElseS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/stmt/Statement;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/IfStatement;

    move-result-object p2

    invoke-virtual {v1, p2}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 313
    sget-object p2, Lorg/codehaus/groovy/ast/ClassNode;->EMPTY_ARRAY:[Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {p1, p0, v0, p2, v1}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->addGeneratedConstructor(Lorg/codehaus/groovy/ast/ClassNode;I[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/ConstructorNode;

    if-eqz p3, :cond_0

    .line 316
    new-instance p2, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    invoke-direct {p2}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;-><init>()V

    .line 317
    sget-object p3, Lorg/codehaus/groovy/ast/ClassNode;->THIS:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ctorX(Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;

    move-result-object v0

    invoke-static {p3, v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ctorX(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;

    move-result-object p3

    invoke-static {p3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->stmt(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p3

    invoke-virtual {p2, p3}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 318
    sget-object p3, Lorg/codehaus/groovy/ast/Parameter;->EMPTY_ARRAY:[Lorg/codehaus/groovy/ast/Parameter;

    sget-object v0, Lorg/codehaus/groovy/ast/ClassNode;->EMPTY_ARRAY:[Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {p1, p0, p3, v0, p2}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->addGeneratedConstructor(Lorg/codehaus/groovy/ast/ClassNode;I[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/ConstructorNode;

    :cond_0
    return-void
.end method

.method private static createConstructor(Lorg/codehaus/groovy/transform/AbstractASTTransformation;Lorg/codehaus/groovy/ast/AnnotationNode;Lorg/codehaus/groovy/ast/ClassNode;ZZZZLjava/util/List;Ljava/util/List;ZZLorg/codehaus/groovy/control/SourceUnit;Lgroovy/transform/options/PropertyHandler;Lorg/codehaus/groovy/ast/expr/ClosureExpression;Lorg/codehaus/groovy/ast/expr/ClosureExpression;)V
    .locals 28
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/transform/AbstractASTTransformation;",
            "Lorg/codehaus/groovy/ast/AnnotationNode;",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            "ZZZZ",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;ZZ",
            "Lorg/codehaus/groovy/control/SourceUnit;",
            "Lgroovy/transform/options/PropertyHandler;",
            "Lorg/codehaus/groovy/ast/expr/ClosureExpression;",
            "Lorg/codehaus/groovy/ast/expr/ClosureExpression;",
            ")V"
        }
    .end annotation

    move-object/from16 v6, p0

    move-object/from16 v7, p1

    move-object/from16 v5, p2

    move-object/from16 v4, p7

    move-object/from16 v3, p8

    move/from16 v2, p9

    move-object/from16 v1, p11

    move-object/from16 v0, p13

    const/4 v15, 0x1

    .line 174
    invoke-static {v15}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v8

    const-string v9, "callSuper"

    invoke-virtual {v6, v7, v9, v8}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->memberHasValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;Ljava/lang/Object;)Z

    move-result v16

    const-string v9, "force"

    .line 175
    invoke-virtual {v6, v7, v9, v8}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->memberHasValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;Ljava/lang/Object;)Z

    move-result v17

    const/4 v14, 0x0

    .line 176
    invoke-static {v14}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v8

    const-string v9, "defaults"

    invoke-virtual {v6, v7, v9, v8}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->memberHasValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;Ljava/lang/Object;)Z

    move-result v8

    xor-int/lit8 v13, v8, 0x1

    .line 177
    new-instance v8, Ljava/util/HashSet;

    invoke-direct {v8}, Ljava/util/HashSet;-><init>()V

    if-nez p6, :cond_1

    if-eqz p5, :cond_0

    goto :goto_0

    .line 182
    :cond_0
    new-instance v9, Ljava/util/ArrayList;

    invoke-direct {v9}, Ljava/util/ArrayList;-><init>()V

    goto :goto_1

    .line 180
    :cond_1
    :goto_0
    invoke-virtual/range {p2 .. p2}, Lorg/codehaus/groovy/ast/ClassNode;->getSuperClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v19

    const/16 v22, 0x0

    const/16 v24, 0x1

    const/16 v25, 0x1

    move-object/from16 v18, v8

    move/from16 v20, p6

    move/from16 v21, p5

    move/from16 v23, p10

    invoke-static/range {v18 .. v25}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->getAllProperties(Ljava/util/Set;Lorg/codehaus/groovy/ast/ClassNode;ZZZZZZ)Ljava/util/List;

    move-result-object v9

    :goto_1
    move-object v12, v9

    const/16 v18, 0x0

    const/16 v19, 0x0

    const/16 v20, 0x1

    move-object/from16 v9, p2

    move/from16 v10, p4

    move/from16 v11, p3

    move-object/from16 p3, v12

    move/from16 v12, v18

    move/from16 v26, v13

    move/from16 v13, p10

    move/from16 v14, v19

    move v7, v15

    move/from16 v15, v20

    .line 185
    invoke-static/range {v8 .. v15}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->getAllProperties(Ljava/util/Set;Lorg/codehaus/groovy/ast/ClassNode;ZZZZZZ)Ljava/util/List;

    move-result-object v8

    .line 187
    invoke-static/range {p2 .. p2}, Lorg/codehaus/groovy/transform/ImmutableASTTransformation;->makeImmutable(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v9

    move/from16 v10, v26

    xor-int/lit8 v11, v10, 0x1

    .line 188
    invoke-static {v8, v11}, Lorg/codehaus/groovy/transform/ImmutableASTTransformation;->isSpecialNamedArgCase(Ljava/util/List;Z)Z

    move-result v11

    if-eqz v11, :cond_3

    invoke-interface/range {p3 .. p3}, Ljava/util/List;->isEmpty()Z

    move-result v11

    if-nez v11, :cond_2

    goto :goto_2

    :cond_2
    move-object/from16 v12, p3

    goto :goto_3

    :cond_3
    :goto_2
    xor-int/lit8 v11, v10, 0x1

    move-object/from16 v12, p3

    .line 189
    invoke-static {v12, v11}, Lorg/codehaus/groovy/transform/ImmutableASTTransformation;->isSpecialNamedArgCase(Ljava/util/List;Z)Z

    move-result v11

    if-eqz v11, :cond_4

    invoke-interface {v8}, Ljava/util/List;->isEmpty()Z

    move-result v11

    if-eqz v11, :cond_4

    :goto_3
    move v15, v7

    goto :goto_4

    :cond_4
    const/4 v15, 0x0

    :goto_4
    const/4 v11, 0x0

    .line 192
    invoke-static {v11, v5}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->hasExplicitConstructor(Lorg/codehaus/groovy/transform/AbstractASTTransformation;Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v11

    if-eqz v11, :cond_5

    if-nez v17, :cond_5

    if-nez v9, :cond_5

    return-void

    .line 194
    :cond_5
    new-instance v11, Ljava/util/ArrayList;

    invoke-direct {v11}, Ljava/util/ArrayList;-><init>()V

    .line 195
    new-instance v13, Ljava/util/ArrayList;

    invoke-direct {v13}, Ljava/util/ArrayList;-><init>()V

    .line 196
    new-instance v14, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    invoke-direct {v14}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;-><init>()V

    if-eqz v0, :cond_7

    .line 199
    invoke-static {v0, v14}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->copyStatementsWithSuperAdjustment(Lorg/codehaus/groovy/ast/expr/ClosureExpression;Lorg/codehaus/groovy/ast/stmt/BlockStatement;)Z

    move-result v0

    if-eqz v0, :cond_6

    if-eqz v16, :cond_6

    .line 201
    new-instance v7, Ljava/lang/StringBuilder;

    invoke-direct {v7}, Ljava/lang/StringBuilder;-><init>()V

    move/from16 p4, v0

    const-string v0, "Error during "

    invoke-virtual {v7, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    sget-object v7, Lorg/codehaus/groovy/transform/TupleConstructorASTTransformation;->MY_TYPE_NAME:Ljava/lang/String;

    invoke-virtual {v0, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v7, " processing, can\'t have a super call in \'pre\' closure and also \'callSuper\' enabled"

    invoke-virtual {v0, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v6, v0, v5}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    goto :goto_5

    :cond_6
    move/from16 p4, v0

    :goto_5
    move/from16 v7, p4

    goto :goto_6

    :cond_7
    const/4 v7, 0x0

    .line 206
    :goto_6
    new-instance v0, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    invoke-direct {v0}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;-><init>()V

    .line 208
    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1, v8}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    .line 209
    invoke-interface {v1, v12}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    move-object/from16 p4, v8

    move-object/from16 v8, p12

    .line 210
    invoke-virtual {v8, v6, v0, v5, v1}, Lgroovy/transform/options/PropertyHandler;->validateProperties(Lorg/codehaus/groovy/transform/AbstractASTTransformation;Lorg/codehaus/groovy/ast/stmt/BlockStatement;Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/List;)Z

    move-result v1

    if-nez v1, :cond_8

    return-void

    .line 214
    :cond_8
    invoke-interface {v12}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v12

    :goto_7
    invoke-interface {v12}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_d

    invoke-interface {v12}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v17, v1

    check-cast v17, Lorg/codehaus/groovy/ast/PropertyNode;

    .line 215
    invoke-virtual/range {v17 .. v17}, Lorg/codehaus/groovy/ast/PropertyNode;->getName()Ljava/lang/String;

    move-result-object v1

    move-object/from16 p5, v0

    .line 216
    invoke-virtual/range {v17 .. v17}, Lorg/codehaus/groovy/ast/PropertyNode;->getField()Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object v0

    .line 217
    invoke-static {v1, v4, v3, v2}, Lorg/codehaus/groovy/transform/TupleConstructorASTTransformation;->shouldSkipUndefinedAware(Ljava/lang/String;Ljava/util/List;Ljava/util/List;Z)Z

    move-result v18

    if-eqz v18, :cond_9

    move-object/from16 v0, p5

    goto :goto_7

    .line 218
    :cond_9
    invoke-static {v0, v1, v10, v6, v9}, Lorg/codehaus/groovy/transform/TupleConstructorASTTransformation;->createParam(Lorg/codehaus/groovy/ast/FieldNode;Ljava/lang/String;ZLorg/codehaus/groovy/transform/AbstractASTTransformation;Z)Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v0

    invoke-interface {v11, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    if-eqz v16, :cond_a

    .line 220
    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v0

    invoke-interface {v13, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_8

    :cond_a
    if-nez v7, :cond_b

    if-nez v15, :cond_b

    const/16 v18, 0x0

    move-object/from16 v1, p5

    move-object/from16 v0, p12

    move-object/from16 v27, v1

    move-object/from16 v1, p0

    move-object/from16 v2, p1

    move-object/from16 v3, p2

    move/from16 p5, v7

    move-object v7, v4

    move-object/from16 v4, v17

    move-object v8, v5

    move-object/from16 v5, v18

    .line 222
    invoke-virtual/range {v0 .. v5}, Lgroovy/transform/options/PropertyHandler;->createPropInit(Lorg/codehaus/groovy/transform/AbstractASTTransformation;Lorg/codehaus/groovy/ast/AnnotationNode;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/PropertyNode;Lorg/codehaus/groovy/ast/Parameter;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v0

    move-object/from16 v5, v27

    if-eqz v0, :cond_c

    .line 224
    invoke-virtual {v5, v0}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    goto :goto_9

    :cond_b
    :goto_8
    move-object v8, v5

    move-object/from16 v5, p5

    move/from16 p5, v7

    move-object v7, v4

    :cond_c
    :goto_9
    move-object/from16 v3, p8

    move/from16 v2, p9

    move-object v0, v5

    move-object v4, v7

    move-object v5, v8

    move/from16 v7, p5

    move-object/from16 v8, p12

    goto :goto_7

    :cond_d
    move-object v7, v4

    move-object v8, v5

    move-object v5, v0

    if-eqz v16, :cond_e

    .line 229
    sget-object v0, Lorg/codehaus/groovy/ast/ClassNode;->SUPER:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {v13}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->args(Ljava/util/List;)Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    move-result-object v1

    invoke-static {v0, v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ctorX(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->stmt(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v0

    invoke-virtual {v5, v0}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 231
    :cond_e
    invoke-virtual {v14}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_f

    .line 232
    invoke-virtual {v14}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->getStatements()Ljava/util/List;

    move-result-object v0

    invoke-virtual {v5, v0}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatements(Ljava/util/List;)V

    .line 235
    :cond_f
    invoke-interface/range {p4 .. p4}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v12

    :goto_a
    invoke-interface {v12}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_12

    invoke-interface {v12}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    move-object v4, v0

    check-cast v4, Lorg/codehaus/groovy/ast/PropertyNode;

    .line 236
    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/PropertyNode;->getName()Ljava/lang/String;

    move-result-object v0

    .line 237
    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/PropertyNode;->getField()Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object v1

    move-object/from16 v13, p8

    move/from16 v14, p9

    .line 238
    invoke-static {v0, v7, v13, v14}, Lorg/codehaus/groovy/transform/TupleConstructorASTTransformation;->shouldSkipUndefinedAware(Ljava/lang/String;Ljava/util/List;Ljava/util/List;Z)Z

    move-result v2

    if-eqz v2, :cond_10

    goto :goto_a

    .line 239
    :cond_10
    invoke-static {v1, v0, v10, v6, v9}, Lorg/codehaus/groovy/transform/TupleConstructorASTTransformation;->createParam(Lorg/codehaus/groovy/ast/FieldNode;Ljava/lang/String;ZLorg/codehaus/groovy/transform/AbstractASTTransformation;Z)Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v0

    .line 240
    invoke-interface {v11, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    const/16 v16, 0x0

    move-object/from16 v0, p12

    move-object/from16 v1, p0

    move-object/from16 v2, p1

    move-object/from16 v3, p2

    move-object v6, v5

    move-object/from16 v5, v16

    .line 241
    invoke-virtual/range {v0 .. v5}, Lgroovy/transform/options/PropertyHandler;->createPropInit(Lorg/codehaus/groovy/transform/AbstractASTTransformation;Lorg/codehaus/groovy/ast/AnnotationNode;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/PropertyNode;Lorg/codehaus/groovy/ast/Parameter;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v0

    if-eqz v0, :cond_11

    .line 243
    invoke-virtual {v6, v0}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    :cond_11
    move-object v5, v6

    move-object/from16 v6, p0

    goto :goto_a

    :cond_12
    move-object/from16 v13, p8

    move-object v6, v5

    if-eqz p14, :cond_13

    .line 248
    invoke-virtual/range {p14 .. p14}, Lorg/codehaus/groovy/ast/expr/ClosureExpression;->getCode()Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v0

    invoke-virtual {v6, v0}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    :cond_13
    if-eqz v13, :cond_14

    .line 252
    new-instance v0, Lorg/codehaus/groovy/transform/TupleConstructorASTTransformation$$ExternalSyntheticLambda0;

    invoke-direct {v0, v13}, Lorg/codehaus/groovy/transform/TupleConstructorASTTransformation$$ExternalSyntheticLambda0;-><init>(Ljava/util/List;)V

    invoke-static {v0}, Ljava/util/Comparator;->comparingInt(Ljava/util/function/ToIntFunction;)Ljava/util/Comparator;

    move-result-object v0

    .line 253
    invoke-interface {v11, v0}, Ljava/util/List;->sort(Ljava/util/Comparator;)V

    .line 256
    :cond_14
    sget-object v0, Lorg/codehaus/groovy/transform/MapConstructorASTTransformation;->MY_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {v8, v0}, Lorg/apache/groovy/ast/tools/AnnotatedNodeUtils;->hasAnnotation(Lorg/codehaus/groovy/ast/AnnotatedNode;Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v0

    .line 257
    const-class v1, Lorg/codehaus/groovy/ast/ConstructorNode;

    move-object/from16 v2, p1

    const/4 v3, 0x1

    invoke-static {v2, v8, v1, v3}, Lorg/apache/groovy/ast/tools/VisibilityUtils;->getVisibility(Lorg/codehaus/groovy/ast/AnnotationNode;Lorg/codehaus/groovy/ast/AnnotatedNode;Ljava/lang/Class;I)I

    move-result v1

    .line 258
    sget-object v2, Lorg/codehaus/groovy/ast/Parameter;->EMPTY_ARRAY:[Lorg/codehaus/groovy/ast/Parameter;

    invoke-interface {v11, v2}, Ljava/util/List;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object v2

    check-cast v2, [Lorg/codehaus/groovy/ast/Parameter;

    sget-object v4, Lorg/codehaus/groovy/ast/ClassNode;->EMPTY_ARRAY:[Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {v8, v1, v2, v4, v6}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->addGeneratedConstructor(Lorg/codehaus/groovy/ast/ClassNode;I[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/ConstructorNode;

    move-object/from16 v2, p11

    if-eqz v2, :cond_15

    .line 260
    invoke-virtual {v6}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->isEmpty()Z

    move-result v4

    if-nez v4, :cond_15

    .line 261
    new-instance v4, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;

    invoke-direct {v4, v2}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;-><init>(Lorg/codehaus/groovy/control/SourceUnit;)V

    .line 262
    invoke-virtual {v4, v8}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->visitClass(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 266
    :cond_15
    invoke-virtual {v6}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->isEmpty()Z

    move-result v2

    if-eqz v2, :cond_16

    .line 267
    new-instance v2, Lorg/codehaus/groovy/ast/stmt/ExpressionStatement;

    sget-object v4, Lorg/codehaus/groovy/ast/expr/ConstantExpression;->EMPTY_EXPRESSION:Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    invoke-direct {v2, v4}, Lorg/codehaus/groovy/ast/stmt/ExpressionStatement;-><init>(Lorg/codehaus/groovy/ast/expr/Expression;)V

    invoke-virtual {v6, v2}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 274
    :cond_16
    invoke-interface {v11}, Ljava/util/List;->isEmpty()Z

    move-result v2

    if-nez v2, :cond_18

    if-eqz v10, :cond_18

    if-nez v0, :cond_18

    if-eqz v15, :cond_18

    const/4 v0, 0x0

    .line 275
    invoke-interface {v11, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lorg/codehaus/groovy/ast/Parameter;

    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/Parameter;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v2

    .line 276
    invoke-interface {v11}, Ljava/util/List;->size()I

    move-result v4

    if-gt v4, v3, :cond_17

    sget-object v3, Lorg/codehaus/groovy/ast/ClassHelper;->OBJECT_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v2, v3}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_18

    .line 277
    :cond_17
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "The class "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual/range {p2 .. p2}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    const-string v3, " was incorrectly initialized via the map constructor with null."

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    .line 278
    invoke-static {v1, v8, v2, v0}, Lorg/codehaus/groovy/transform/TupleConstructorASTTransformation;->addSpecialMapConstructors(ILorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Z)V

    :cond_18
    return-void
.end method

.method private static createParam(Lorg/codehaus/groovy/ast/FieldNode;Ljava/lang/String;ZLorg/codehaus/groovy/transform/AbstractASTTransformation;Z)Lorg/codehaus/groovy/ast/Parameter;
    .locals 2

    .line 284
    new-instance v0, Lorg/codehaus/groovy/ast/Parameter;

    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/FieldNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    invoke-direct {v0, v1, p1}, Lorg/codehaus/groovy/ast/Parameter;-><init>(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)V

    if-eqz p2, :cond_0

    .line 286
    invoke-static {p0}, Lorg/codehaus/groovy/transform/TupleConstructorASTTransformation;->providedOrDefaultInitialValue(Lorg/codehaus/groovy/ast/FieldNode;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p0

    invoke-virtual {v0, p0}, Lorg/codehaus/groovy/ast/Parameter;->setInitialExpression(Lorg/codehaus/groovy/ast/expr/Expression;)V

    goto :goto_0

    :cond_0
    if-nez p4, :cond_1

    .line 289
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/FieldNode;->getInitialExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    if-eqz p1, :cond_1

    .line 290
    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    const-string p2, "Error during "

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    sget-object p2, Lorg/codehaus/groovy/transform/TupleConstructorASTTransformation;->MY_TYPE_NAME:Ljava/lang/String;

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string p2, " processing, default value processing disabled but default value found for \'"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string p2, "\'"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p3, p1, p0}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    :cond_1
    :goto_0
    return-object v0
.end method

.method private static illegalArgumentBlock(Ljava/lang/String;)Lorg/codehaus/groovy/ast/stmt/BlockStatement;
    .locals 4

    const/4 v0, 0x1

    new-array v1, v0, [Lorg/codehaus/groovy/ast/stmt/Statement;

    .line 323
    const-class v2, Ljava/lang/IllegalArgumentException;

    invoke-static {v2}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v2

    new-array v0, v0, [Lorg/codehaus/groovy/ast/expr/Expression;

    invoke-static {p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->constX(Ljava/lang/Object;)Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    move-result-object p0

    const/4 v3, 0x0

    aput-object p0, v0, v3

    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->args([Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    move-result-object p0

    invoke-static {v2, p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ctorX(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;

    move-result-object p0

    invoke-static {p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->throwS(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/ThrowStatement;

    move-result-object p0

    aput-object p0, v1, v3

    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->block([Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    move-result-object p0

    return-object p0
.end method

.method static synthetic lambda$createConstructor$0(Ljava/util/List;Lorg/codehaus/groovy/ast/Parameter;)I
    .locals 0

    .line 252
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/Parameter;->getName()Ljava/lang/String;

    move-result-object p1

    invoke-interface {p0, p1}, Ljava/util/List;->indexOf(Ljava/lang/Object;)I

    move-result p0

    return p0
.end method

.method private static processArgsBlock(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/VariableExpression;)Lorg/codehaus/groovy/ast/stmt/BlockStatement;
    .locals 5

    .line 327
    new-instance v0, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    invoke-direct {v0}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;-><init>()V

    .line 328
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/ClassNode;->getProperties()Ljava/util/List;

    move-result-object p0

    invoke-interface {p0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p0

    :goto_0
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    const/4 v2, 0x0

    if-eqz v1, :cond_1

    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/ast/PropertyNode;

    .line 329
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/PropertyNode;->isStatic()Z

    move-result v3

    if-eqz v3, :cond_0

    goto :goto_0

    .line 332
    :cond_0
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/PropertyNode;->getName()Ljava/lang/String;

    move-result-object v3

    invoke-static {v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->constX(Ljava/lang/Object;)Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    move-result-object v3

    const-string v4, "containsKey"

    invoke-static {p1, v4, v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object v3

    .line 333
    invoke-virtual {v3, v2}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setImplicitThis(Z)V

    .line 334
    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v2

    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/PropertyNode;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-static {p1, v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->propX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    move-result-object v1

    invoke-static {v2, v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v1

    invoke-static {v3, v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ifS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/IfStatement;

    move-result-object v1

    invoke-virtual {v0, v1}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    goto :goto_0

    .line 336
    :cond_1
    sget-object p0, Lorg/codehaus/groovy/transform/TupleConstructorASTTransformation;->CHECK_METHOD_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    const/4 v1, 0x2

    new-array v1, v1, [Lorg/codehaus/groovy/ast/expr/Expression;

    const-string v3, "this"

    invoke-static {v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v3

    aput-object v3, v1, v2

    const/4 v2, 0x1

    aput-object p1, v1, v2

    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->args([Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    move-result-object p1

    const-string v1, "checkPropNames"

    invoke-static {p0, v1, p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/StaticMethodCallExpression;

    move-result-object p0

    invoke-static {p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->stmt(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p0

    invoke-virtual {v0, p0}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    return-object v0
.end method

.method private static providedOrDefaultInitialValue(Lorg/codehaus/groovy/ast/FieldNode;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 2

    .line 297
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/FieldNode;->hasInitialExpression()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/FieldNode;->getInitialExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    goto :goto_0

    :cond_0
    invoke-static {}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->nullX()Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    move-result-object v0

    .line 298
    :goto_0
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/FieldNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p0

    .line 299
    invoke-static {p0}, Lorg/codehaus/groovy/ast/ClassHelper;->isPrimitiveType(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-static {v0}, Lorg/apache/groovy/ast/tools/ExpressionUtils;->isNullConstant(Lorg/codehaus/groovy/ast/expr/Expression;)Z

    move-result v1

    if-eqz v1, :cond_1

    .line 300
    sget-object v0, Lorg/codehaus/groovy/transform/TupleConstructorASTTransformation;->primitivesInitialValues:Ljava/util/Map;

    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/ClassNode;->getTypeClass()Ljava/lang/Class;

    move-result-object p0

    invoke-interface {v0, p0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v0, p0

    check-cast v0, Lorg/codehaus/groovy/ast/expr/Expression;

    :cond_1
    return-object v0
.end method


# virtual methods
.method public getAnnotationName()Ljava/lang/String;
    .locals 1

    .line 116
    sget-object v0, Lorg/codehaus/groovy/transform/TupleConstructorASTTransformation;->MY_TYPE_NAME:Ljava/lang/String;

    return-object v0
.end method

.method public setCompilationUnit(Lorg/codehaus/groovy/control/CompilationUnit;)V
    .locals 0

    .line 342
    iput-object p1, p0, Lorg/codehaus/groovy/transform/TupleConstructorASTTransformation;->compilationUnit:Lorg/codehaus/groovy/control/CompilationUnit;

    return-void
.end method

.method public visit([Lorg/codehaus/groovy/ast/ASTNode;Lorg/codehaus/groovy/control/SourceUnit;)V
    .locals 26

    move-object/from16 v15, p0

    .line 120
    invoke-virtual/range {p0 .. p2}, Lorg/codehaus/groovy/transform/TupleConstructorASTTransformation;->init([Lorg/codehaus/groovy/ast/ASTNode;Lorg/codehaus/groovy/control/SourceUnit;)V

    const/4 v0, 0x1

    .line 128
    invoke-static {v0}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v1

    .line 121
    aget-object v2, p1, v0

    check-cast v2, Lorg/codehaus/groovy/ast/AnnotatedNode;

    const/4 v3, 0x0

    .line 122
    aget-object v4, p1, v3

    move-object v14, v4

    check-cast v14, Lorg/codehaus/groovy/ast/AnnotationNode;

    .line 123
    sget-object v4, Lorg/codehaus/groovy/transform/TupleConstructorASTTransformation;->MY_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v14}, Lorg/codehaus/groovy/ast/AnnotationNode;->getClassNode()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v5

    invoke-virtual {v4, v5}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result v4

    if-nez v4, :cond_0

    return-void

    .line 125
    :cond_0
    instance-of v4, v2, Lorg/codehaus/groovy/ast/ClassNode;

    if-eqz v4, :cond_b

    .line 126
    move-object v11, v2

    check-cast v11, Lorg/codehaus/groovy/ast/ClassNode;

    .line 127
    sget-object v12, Lorg/codehaus/groovy/transform/TupleConstructorASTTransformation;->MY_TYPE_NAME:Ljava/lang/String;

    invoke-virtual {v15, v11, v12}, Lorg/codehaus/groovy/transform/TupleConstructorASTTransformation;->checkNotInterface(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Z

    move-result v2

    if-nez v2, :cond_1

    return-void

    :cond_1
    const-string v2, "includeFields"

    .line 128
    invoke-virtual {v15, v14, v2, v1}, Lorg/codehaus/groovy/transform/TupleConstructorASTTransformation;->memberHasValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;Ljava/lang/Object;)Z

    move-result v13

    .line 129
    invoke-static {v3}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v2

    const-string v3, "includeProperties"

    invoke-virtual {v15, v14, v3, v2}, Lorg/codehaus/groovy/transform/TupleConstructorASTTransformation;->memberHasValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;Ljava/lang/Object;)Z

    move-result v2

    xor-int/lit8 v16, v2, 0x1

    const-string v0, "includeSuperFields"

    .line 130
    invoke-virtual {v15, v14, v0, v1}, Lorg/codehaus/groovy/transform/TupleConstructorASTTransformation;->memberHasValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;Ljava/lang/Object;)Z

    move-result v17

    const-string v0, "includeSuperProperties"

    .line 131
    invoke-virtual {v15, v14, v0, v1}, Lorg/codehaus/groovy/transform/TupleConstructorASTTransformation;->memberHasValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;Ljava/lang/Object;)Z

    move-result v18

    const-string v0, "allProperties"

    .line 132
    invoke-virtual {v15, v14, v0, v1}, Lorg/codehaus/groovy/transform/TupleConstructorASTTransformation;->memberHasValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;Ljava/lang/Object;)Z

    move-result v19

    const-string v0, "excludes"

    .line 133
    invoke-static {v14, v0}, Lorg/codehaus/groovy/transform/TupleConstructorASTTransformation;->getMemberStringList(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;)Ljava/util/List;

    move-result-object v10

    const-string v0, "includes"

    .line 134
    invoke-static {v14, v0}, Lorg/codehaus/groovy/transform/TupleConstructorASTTransformation;->getMemberStringList(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;)Ljava/util/List;

    move-result-object v9

    const-string v0, "allNames"

    .line 135
    invoke-virtual {v15, v14, v0, v1}, Lorg/codehaus/groovy/transform/TupleConstructorASTTransformation;->memberHasValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;Ljava/lang/Object;)Z

    move-result v20

    .line 136
    invoke-virtual {v15, v14, v10, v9, v12}, Lorg/codehaus/groovy/transform/TupleConstructorASTTransformation;->checkIncludeExcludeUndefinedAware(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/util/List;Ljava/util/List;Ljava/lang/String;)Z

    move-result v0

    if-nez v0, :cond_2

    return-void

    :cond_2
    const/16 v21, 0x0

    const-string v3, "includes"

    move-object/from16 v0, p0

    move-object v1, v11

    move-object v2, v9

    move-object v4, v14

    move-object v5, v12

    move v6, v13

    move/from16 v7, v18

    move/from16 v8, v19

    move-object/from16 v22, v9

    move/from16 v9, v17

    move-object/from16 v23, v10

    move/from16 v10, v21

    .line 137
    invoke-virtual/range {v0 .. v10}, Lorg/codehaus/groovy/transform/TupleConstructorASTTransformation;->checkPropertyList(Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/List;Ljava/lang/String;Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;ZZZZZ)Z

    move-result v0

    if-nez v0, :cond_3

    return-void

    :cond_3
    const/4 v10, 0x0

    const-string v3, "excludes"

    move-object/from16 v0, p0

    move-object v1, v11

    move-object/from16 v2, v23

    move-object v4, v14

    move-object v5, v12

    move v6, v13

    move/from16 v7, v18

    move/from16 v8, v19

    move/from16 v9, v17

    .line 139
    invoke-virtual/range {v0 .. v10}, Lorg/codehaus/groovy/transform/TupleConstructorASTTransformation;->checkPropertyList(Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/List;Ljava/lang/String;Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;ZZZZZ)Z

    move-result v0

    if-nez v0, :cond_4

    return-void

    .line 141
    :cond_4
    iget-object v0, v15, Lorg/codehaus/groovy/transform/TupleConstructorASTTransformation;->compilationUnit:Lorg/codehaus/groovy/control/CompilationUnit;

    if-eqz v0, :cond_5

    invoke-virtual {v0}, Lorg/codehaus/groovy/control/CompilationUnit;->getTransformLoader()Lgroovy/lang/GroovyClassLoader;

    move-result-object v0

    goto :goto_0

    :cond_5
    invoke-virtual/range {p2 .. p2}, Lorg/codehaus/groovy/control/SourceUnit;->getClassLoader()Lgroovy/lang/GroovyClassLoader;

    move-result-object v0

    .line 142
    :goto_0
    invoke-static {v15, v0, v11}, Lgroovy/transform/options/PropertyHandler;->createPropertyHandler(Lorg/codehaus/groovy/transform/AbstractASTTransformation;Lgroovy/lang/GroovyClassLoader;Lorg/codehaus/groovy/ast/ClassNode;)Lgroovy/transform/options/PropertyHandler;

    move-result-object v12

    if-nez v12, :cond_6

    return-void

    .line 144
    :cond_6
    invoke-virtual {v12, v15, v14}, Lgroovy/transform/options/PropertyHandler;->validateAttributes(Lorg/codehaus/groovy/transform/AbstractASTTransformation;Lorg/codehaus/groovy/ast/AnnotationNode;)Z

    move-result v0

    if-nez v0, :cond_7

    return-void

    :cond_7
    const-string v10, "pre"

    .line 146
    invoke-virtual {v14, v10}, Lorg/codehaus/groovy/ast/AnnotationNode;->getMember(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v9

    if-eqz v9, :cond_8

    .line 147
    instance-of v0, v9, Lorg/codehaus/groovy/ast/expr/ClosureExpression;

    if-nez v0, :cond_8

    .line 148
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Expected closure value for annotation parameter \'pre\'. Found "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v15, v0, v11}, Lorg/codehaus/groovy/transform/TupleConstructorASTTransformation;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    return-void

    :cond_8
    const-string v8, "post"

    .line 151
    invoke-virtual {v14, v8}, Lorg/codehaus/groovy/ast/AnnotationNode;->getMember(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v7

    if-eqz v7, :cond_9

    .line 152
    instance-of v0, v7, Lorg/codehaus/groovy/ast/expr/ClosureExpression;

    if-nez v0, :cond_9

    .line 153
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Expected closure value for annotation parameter \'post\'. Found "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v15, v0, v11}, Lorg/codehaus/groovy/transform/TupleConstructorASTTransformation;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    return-void

    .line 157
    :cond_9
    iget-object v6, v15, Lorg/codehaus/groovy/transform/TupleConstructorASTTransformation;->sourceUnit:Lorg/codehaus/groovy/control/SourceUnit;

    move-object/from16 v21, v9

    check-cast v21, Lorg/codehaus/groovy/ast/expr/ClosureExpression;

    move-object/from16 v24, v7

    check-cast v24, Lorg/codehaus/groovy/ast/expr/ClosureExpression;

    move-object/from16 v0, p0

    move-object v1, v14

    move-object v2, v11

    move v3, v13

    move/from16 v4, v16

    move/from16 v5, v17

    move-object v11, v6

    move/from16 v6, v18

    move-object/from16 v16, v7

    move-object/from16 v7, v23

    move-object v13, v8

    move-object/from16 v8, v22

    move-object/from16 v17, v9

    move/from16 v9, v20

    move-object v15, v10

    move/from16 v10, v19

    move-object/from16 v25, v13

    move-object/from16 v13, v21

    move-object/from16 v18, v15

    move-object v15, v14

    move-object/from16 v14, v24

    invoke-static/range {v0 .. v14}, Lorg/codehaus/groovy/transform/TupleConstructorASTTransformation;->createConstructor(Lorg/codehaus/groovy/transform/AbstractASTTransformation;Lorg/codehaus/groovy/ast/AnnotationNode;Lorg/codehaus/groovy/ast/ClassNode;ZZZZLjava/util/List;Ljava/util/List;ZZLorg/codehaus/groovy/control/SourceUnit;Lgroovy/transform/options/PropertyHandler;Lorg/codehaus/groovy/ast/expr/ClosureExpression;Lorg/codehaus/groovy/ast/expr/ClosureExpression;)V

    if-eqz v17, :cond_a

    .line 162
    new-instance v0, Lorg/codehaus/groovy/ast/expr/ClosureExpression;

    sget-object v1, Lorg/codehaus/groovy/ast/Parameter;->EMPTY_ARRAY:[Lorg/codehaus/groovy/ast/Parameter;

    sget-object v2, Lorg/codehaus/groovy/ast/stmt/EmptyStatement;->INSTANCE:Lorg/codehaus/groovy/ast/stmt/EmptyStatement;

    invoke-direct {v0, v1, v2}, Lorg/codehaus/groovy/ast/expr/ClosureExpression;-><init>([Lorg/codehaus/groovy/ast/Parameter;Lorg/codehaus/groovy/ast/stmt/Statement;)V

    move-object/from16 v1, v18

    invoke-virtual {v15, v1, v0}, Lorg/codehaus/groovy/ast/AnnotationNode;->setMember(Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)V

    :cond_a
    if-eqz v16, :cond_b

    .line 165
    new-instance v0, Lorg/codehaus/groovy/ast/expr/ClosureExpression;

    sget-object v1, Lorg/codehaus/groovy/ast/Parameter;->EMPTY_ARRAY:[Lorg/codehaus/groovy/ast/Parameter;

    sget-object v2, Lorg/codehaus/groovy/ast/stmt/EmptyStatement;->INSTANCE:Lorg/codehaus/groovy/ast/stmt/EmptyStatement;

    invoke-direct {v0, v1, v2}, Lorg/codehaus/groovy/ast/expr/ClosureExpression;-><init>([Lorg/codehaus/groovy/ast/Parameter;Lorg/codehaus/groovy/ast/stmt/Statement;)V

    move-object/from16 v1, v25

    invoke-virtual {v15, v1, v0}, Lorg/codehaus/groovy/ast/AnnotationNode;->setMember(Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)V

    :cond_b
    return-void
.end method
