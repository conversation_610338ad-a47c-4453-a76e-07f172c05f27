.class public Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor;
.super Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;
.source "StaticCompilationVisitor.java"


# static fields
.field public static final ARRAYLIST_ADD_METHOD:Lorg/codehaus/groovy/ast/MethodNode;

.field public static final ARRAYLIST_CLASSNODE:Lorg/codehaus/groovy/ast/ClassNode;

.field public static final ARRAYLIST_CONSTRUCTOR:Lorg/codehaus/groovy/ast/MethodNode;

.field public static final COMPILESTATIC_CLASSNODE:Lorg/codehaus/groovy/ast/ClassNode;

.field public static final TYPECHECKED_CLASSNODE:Lorg/codehaus/groovy/ast/ClassNode;


# instance fields
.field private classNode:Lorg/codehaus/groovy/ast/ClassNode;

.field private final typeChooser:Lorg/codehaus/groovy/classgen/asm/TypeChooser;


# direct methods
.method public static synthetic $r8$lambda$iYTu5B5KBSEC72rBfdKaNLM4FjE()Ljava/util/Set;
    .locals 1

    invoke-static {}, Ljava/util/Collections;->emptySet()Ljava/util/Set;

    move-result-object v0

    return-object v0
.end method

.method static constructor <clinit>()V
    .locals 6

    .line 120
    const-class v0, Lgroovy/transform/TypeChecked;

    invoke-static {v0}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor;->TYPECHECKED_CLASSNODE:Lorg/codehaus/groovy/ast/ClassNode;

    .line 121
    const-class v0, Lgroovy/transform/CompileStatic;

    invoke-static {v0}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor;->COMPILESTATIC_CLASSNODE:Lorg/codehaus/groovy/ast/ClassNode;

    .line 123
    const-class v0, Ljava/util/ArrayList;

    invoke-static {v0}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor;->ARRAYLIST_CLASSNODE:Lorg/codehaus/groovy/ast/ClassNode;

    const/4 v1, 0x1

    new-array v2, v1, [Lorg/codehaus/groovy/ast/Parameter;

    .line 124
    new-instance v3, Lorg/codehaus/groovy/ast/Parameter;

    sget-object v4, Lorg/codehaus/groovy/ast/ClassHelper;->OBJECT_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    const-string v5, "o"

    invoke-direct {v3, v4, v5}, Lorg/codehaus/groovy/ast/Parameter;-><init>(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)V

    const/4 v4, 0x0

    aput-object v3, v2, v4

    const-string v3, "add"

    invoke-virtual {v0, v3, v2}, Lorg/codehaus/groovy/ast/ClassNode;->getMethod(Ljava/lang/String;[Lorg/codehaus/groovy/ast/Parameter;)Lorg/codehaus/groovy/ast/MethodNode;

    move-result-object v2

    sput-object v2, Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor;->ARRAYLIST_ADD_METHOD:Lorg/codehaus/groovy/ast/MethodNode;

    .line 125
    new-instance v2, Lorg/codehaus/groovy/ast/ConstructorNode;

    sget-object v3, Lorg/codehaus/groovy/ast/Parameter;->EMPTY_ARRAY:[Lorg/codehaus/groovy/ast/Parameter;

    sget-object v4, Lorg/codehaus/groovy/ast/ClassNode;->EMPTY_ARRAY:[Lorg/codehaus/groovy/ast/ClassNode;

    sget-object v5, Lorg/codehaus/groovy/ast/stmt/EmptyStatement;->INSTANCE:Lorg/codehaus/groovy/ast/stmt/EmptyStatement;

    invoke-direct {v2, v1, v3, v4, v5}, Lorg/codehaus/groovy/ast/ConstructorNode;-><init>(I[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)V

    sput-object v2, Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor;->ARRAYLIST_CONSTRUCTOR:Lorg/codehaus/groovy/ast/MethodNode;

    .line 127
    invoke-virtual {v2, v0}, Lorg/codehaus/groovy/ast/MethodNode;->setDeclaringClass(Lorg/codehaus/groovy/ast/ClassNode;)V

    return-void
.end method

.method public constructor <init>(Lorg/codehaus/groovy/control/SourceUnit;Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 0

    .line 135
    invoke-direct {p0, p1, p2}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;-><init>(Lorg/codehaus/groovy/control/SourceUnit;Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 130
    new-instance p1, Lorg/codehaus/groovy/classgen/asm/sc/StaticTypesTypeChooser;

    invoke-direct {p1}, Lorg/codehaus/groovy/classgen/asm/sc/StaticTypesTypeChooser;-><init>()V

    iput-object p1, p0, Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor;->typeChooser:Lorg/codehaus/groovy/classgen/asm/TypeChooser;

    return-void
.end method

.method private addDynamicOuterClassAccessorsCallback(Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 2

    if-eqz p1, :cond_1

    .line 168
    invoke-static {p1}, Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor;->isStaticallyCompiled(Lorg/codehaus/groovy/ast/AnnotatedNode;)Z

    move-result v0

    if-nez v0, :cond_0

    sget-object v0, Lorg/codehaus/groovy/transform/sc/StaticCompilationMetadataKeys;->DYNAMIC_OUTER_NODE_CALLBACK:Lorg/codehaus/groovy/transform/sc/StaticCompilationMetadataKeys;

    invoke-virtual {p1, v0}, Lorg/codehaus/groovy/ast/ClassNode;->getNodeMetaData(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    if-nez v0, :cond_0

    .line 169
    sget-object v0, Lorg/codehaus/groovy/transform/sc/StaticCompilationMetadataKeys;->DYNAMIC_OUTER_NODE_CALLBACK:Lorg/codehaus/groovy/transform/sc/StaticCompilationMetadataKeys;

    new-instance v1, Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor$$ExternalSyntheticLambda2;

    invoke-direct {v1, p1}, Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor$$ExternalSyntheticLambda2;-><init>(Lorg/codehaus/groovy/ast/ClassNode;)V

    invoke-virtual {p1, v0, v1}, Lorg/codehaus/groovy/ast/ClassNode;->putNodeMetaData(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 177
    :cond_0
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getOuterClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    invoke-direct {p0, p1}, Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor;->addDynamicOuterClassAccessorsCallback(Lorg/codehaus/groovy/ast/ClassNode;)V

    :cond_1
    return-void
.end method

.method private static addPrivateBridgeMethods(Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 16

    move-object/from16 v7, p0

    .line 319
    sget-object v0, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->PV_METHODS_ACCESS:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    invoke-virtual {v7, v0}, Lorg/codehaus/groovy/ast/ClassNode;->getNodeMetaData(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    move-object v8, v0

    check-cast v8, Ljava/util/Set;

    if-nez v8, :cond_0

    return-void

    .line 321
    :cond_0
    new-instance v0, Ljava/util/ArrayList;

    invoke-virtual/range {p0 .. p0}, Lorg/codehaus/groovy/ast/ClassNode;->getAllDeclaredMethods()Ljava/util/List;

    move-result-object v1

    invoke-direct {v0, v1}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    .line 322
    invoke-virtual/range {p0 .. p0}, Lorg/codehaus/groovy/ast/ClassNode;->getDeclaredConstructors()Ljava/util/List;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 323
    sget-object v1, Lorg/codehaus/groovy/transform/sc/StaticCompilationMetadataKeys;->PRIVATE_BRIDGE_METHODS:Lorg/codehaus/groovy/transform/sc/StaticCompilationMetadataKeys;

    invoke-virtual {v7, v1}, Lorg/codehaus/groovy/ast/ClassNode;->getNodeMetaData(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/util/Map;

    if-eqz v1, :cond_1

    return-void

    .line 328
    :cond_1
    new-instance v9, Ljava/util/HashMap;

    invoke-direct {v9}, Ljava/util/HashMap;-><init>()V

    const/4 v1, -0x1

    .line 331
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v10

    :cond_2
    :goto_0
    invoke-interface {v10}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_b

    invoke-interface {v10}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    move-object v11, v0

    check-cast v11, Lorg/codehaus/groovy/ast/MethodNode;

    .line 332
    invoke-interface {v8, v11}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_2

    .line 333
    invoke-static {v11}, Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor;->methodSpecificGenerics(Lorg/codehaus/groovy/ast/MethodNode;)Ljava/util/List;

    move-result-object v0

    add-int/lit8 v12, v1, 0x1

    .line 335
    invoke-virtual {v11}, Lorg/codehaus/groovy/ast/MethodNode;->getDeclaringClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    .line 336
    invoke-static/range {p0 .. p0}, Lorg/codehaus/groovy/ast/tools/GenericsUtils;->createGenericsSpec(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/util/Map;

    move-result-object v2

    .line 337
    invoke-static {v11, v2}, Lorg/codehaus/groovy/ast/tools/GenericsUtils;->addMethodGenerics(Lorg/codehaus/groovy/ast/MethodNode;Ljava/util/Map;)Ljava/util/Map;

    move-result-object v13

    .line 338
    invoke-static {v7, v1, v13}, Lorg/codehaus/groovy/ast/tools/GenericsUtils;->extractSuperClassGenerics(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/Map;)V

    .line 339
    invoke-virtual {v11}, Lorg/codehaus/groovy/ast/MethodNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v1

    .line 340
    array-length v2, v1

    const/4 v3, 0x1

    add-int/2addr v2, v3

    new-array v4, v2, [Lorg/codehaus/groovy/ast/Parameter;

    :goto_1
    if-ge v3, v2, :cond_3

    add-int/lit8 v5, v3, -0x1

    .line 342
    aget-object v5, v1, v5

    .line 343
    new-instance v6, Lorg/codehaus/groovy/ast/Parameter;

    .line 344
    invoke-virtual {v5}, Lorg/codehaus/groovy/ast/Parameter;->getOriginType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v14

    invoke-static {v13, v14, v0}, Lorg/codehaus/groovy/ast/tools/GenericsUtils;->correctToGenericsSpecRecurse(Ljava/util/Map;Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/List;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v14

    .line 345
    invoke-virtual {v5}, Lorg/codehaus/groovy/ast/Parameter;->getName()Ljava/lang/String;

    move-result-object v5

    invoke-direct {v6, v14, v5}, Lorg/codehaus/groovy/ast/Parameter;-><init>(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)V

    aput-object v6, v4, v3

    add-int/lit8 v3, v3, 0x1

    goto :goto_1

    .line 349
    :cond_3
    invoke-virtual {v11}, Lorg/codehaus/groovy/ast/MethodNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v2

    const/4 v3, 0x0

    if-eqz v2, :cond_6

    invoke-virtual {v11}, Lorg/codehaus/groovy/ast/MethodNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v2

    array-length v2, v2

    if-nez v2, :cond_4

    goto :goto_3

    .line 352
    :cond_4
    new-instance v2, Ljava/util/ArrayList;

    invoke-direct {v2}, Ljava/util/ArrayList;-><init>()V

    .line 353
    array-length v5, v1

    move v6, v3

    :goto_2
    if-ge v6, v5, :cond_5

    aget-object v14, v1, v6

    .line 354
    invoke-static {v14}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v14

    invoke-interface {v2, v14}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    add-int/lit8 v6, v6, 0x1

    goto :goto_2

    .line 356
    :cond_5
    invoke-static {v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->args(Ljava/util/List;)Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    move-result-object v1

    goto :goto_4

    .line 350
    :cond_6
    :goto_3
    sget-object v1, Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;->EMPTY_ARGUMENTS:Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    .line 360
    :goto_4
    instance-of v2, v11, Lorg/codehaus/groovy/ast/ConstructorNode;

    const-string v5, "$that"

    if-eqz v2, :cond_8

    .line 363
    invoke-virtual/range {p0 .. p0}, Lorg/codehaus/groovy/ast/ClassNode;->getInnerClasses()Ljava/util/Iterator;

    move-result-object v0

    .line 364
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_7

    .line 365
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/ast/ClassNode;

    goto :goto_5

    .line 367
    :cond_7
    new-instance v0, Lorg/codehaus/groovy/ast/InnerClassNode;

    invoke-virtual/range {p0 .. p0}, Lorg/codehaus/groovy/ast/ClassNode;->redirect()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v2

    new-instance v6, Ljava/lang/StringBuilder;

    invoke-direct {v6}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual/range {p0 .. p0}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v14

    invoke-virtual {v6, v14}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v6

    const-string v14, "$1"

    invoke-virtual {v6, v14}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v6

    invoke-virtual {v6}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v6

    const/16 v14, 0x1008

    sget-object v15, Lorg/codehaus/groovy/ast/ClassHelper;->OBJECT_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-direct {v0, v2, v6, v14, v15}, Lorg/codehaus/groovy/ast/InnerClassNode;-><init>(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;)V

    .line 368
    invoke-virtual/range {p0 .. p0}, Lorg/codehaus/groovy/ast/ClassNode;->getModule()Lorg/codehaus/groovy/ast/ModuleNode;

    move-result-object v2

    invoke-virtual {v2, v0}, Lorg/codehaus/groovy/ast/ModuleNode;->addClass(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 370
    :goto_5
    new-instance v2, Lorg/codehaus/groovy/ast/Parameter;

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->getPlainNodeReference()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    invoke-direct {v2, v0, v5}, Lorg/codehaus/groovy/ast/Parameter;-><init>(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)V

    aput-object v2, v4, v3

    .line 371
    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ctorThisS(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v0

    const/16 v1, 0x1000

    .line 373
    sget-object v2, Lorg/codehaus/groovy/ast/ClassNode;->EMPTY_ARRAY:[Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v7, v1, v4, v2, v0}, Lorg/codehaus/groovy/ast/ClassNode;->addConstructor(I[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/ConstructorNode;

    move-result-object v0

    goto :goto_7

    .line 375
    :cond_8
    new-instance v2, Lorg/codehaus/groovy/ast/Parameter;

    invoke-virtual/range {p0 .. p0}, Lorg/codehaus/groovy/ast/ClassNode;->getPlainNodeReference()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v6

    invoke-direct {v2, v6, v5}, Lorg/codehaus/groovy/ast/Parameter;-><init>(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)V

    aput-object v2, v4, v3

    .line 376
    invoke-virtual {v11}, Lorg/codehaus/groovy/ast/MethodNode;->isStatic()Z

    move-result v2

    if-eqz v2, :cond_9

    invoke-static/range {p0 .. p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->classX(Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/ClassExpression;

    move-result-object v2

    goto :goto_6

    :cond_9
    aget-object v2, v4, v3

    invoke-static {v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v2

    .line 377
    :goto_6
    invoke-virtual {v11}, Lorg/codehaus/groovy/ast/MethodNode;->getName()Ljava/lang/String;

    move-result-object v3

    invoke-static {v2, v3, v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object v1

    .line 378
    invoke-virtual {v1, v11}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setMethodTarget(Lorg/codehaus/groovy/ast/MethodNode;)V

    .line 379
    new-instance v6, Lorg/codehaus/groovy/ast/stmt/ExpressionStatement;

    invoke-direct {v6, v1}, Lorg/codehaus/groovy/ast/stmt/ExpressionStatement;-><init>(Lorg/codehaus/groovy/ast/expr/Expression;)V

    .line 381
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "access$"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, v12}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    const/16 v2, 0x1009

    .line 383
    invoke-virtual {v11}, Lorg/codehaus/groovy/ast/MethodNode;->getReturnType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v3

    invoke-static {v13, v3, v0}, Lorg/codehaus/groovy/ast/tools/GenericsUtils;->correctToGenericsSpecRecurse(Ljava/util/Map;Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/List;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v3

    .line 385
    invoke-virtual {v11}, Lorg/codehaus/groovy/ast/MethodNode;->getExceptions()[Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v5

    move-object/from16 v0, p0

    .line 381
    invoke-virtual/range {v0 .. v6}, Lorg/codehaus/groovy/ast/ClassNode;->addMethod(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/MethodNode;

    move-result-object v0

    .line 388
    :goto_7
    invoke-virtual {v11}, Lorg/codehaus/groovy/ast/MethodNode;->getGenericsTypes()[Lorg/codehaus/groovy/ast/GenericsType;

    move-result-object v1

    if-eqz v1, :cond_a

    .line 390
    invoke-static {v13, v1}, Lorg/codehaus/groovy/ast/tools/GenericsUtils;->applyGenericsContextToPlaceHolders(Ljava/util/Map;[Lorg/codehaus/groovy/ast/GenericsType;)[Lorg/codehaus/groovy/ast/GenericsType;

    move-result-object v1

    invoke-virtual {v0, v1}, Lorg/codehaus/groovy/ast/MethodNode;->setGenericsTypes([Lorg/codehaus/groovy/ast/GenericsType;)V

    .line 392
    :cond_a
    sget-object v1, Lorg/codehaus/groovy/transform/sc/StaticCompilationMetadataKeys;->STATIC_COMPILE_NODE:Lorg/codehaus/groovy/transform/sc/StaticCompilationMetadataKeys;

    sget-object v2, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    invoke-virtual {v0, v1, v2}, Lorg/codehaus/groovy/ast/MethodNode;->setNodeMetaData(Ljava/lang/Object;Ljava/lang/Object;)V

    .line 393
    invoke-interface {v9, v11, v0}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move v1, v12

    goto/16 :goto_0

    .line 396
    :cond_b
    invoke-interface {v9}, Ljava/util/Map;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_c

    .line 397
    sget-object v0, Lorg/codehaus/groovy/transform/sc/StaticCompilationMetadataKeys;->PRIVATE_BRIDGE_METHODS:Lorg/codehaus/groovy/transform/sc/StaticCompilationMetadataKeys;

    invoke-virtual {v7, v0, v9}, Lorg/codehaus/groovy/ast/ClassNode;->setNodeMetaData(Ljava/lang/Object;Ljava/lang/Object;)V

    :cond_c
    return-void
.end method

.method private addPrivateFieldAndMethodAccessors(Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 1

    .line 159
    invoke-static {p1}, Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor;->addPrivateBridgeMethods(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 160
    invoke-static {p1}, Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor;->addPrivateFieldsAccessors(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 161
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getInnerClasses()Ljava/util/Iterator;

    move-result-object p1

    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 162
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/ast/ClassNode;

    invoke-direct {p0, v0}, Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor;->addPrivateFieldAndMethodAccessors(Lorg/codehaus/groovy/ast/ClassNode;)V

    goto :goto_0

    :cond_0
    return-void
.end method

.method private static addPrivateFieldsAccessors(Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 21

    move-object/from16 v7, p0

    .line 260
    sget-object v0, Lorg/codehaus/groovy/transform/sc/StaticCompilationMetadataKeys;->PRIVATE_FIELDS_ACCESSORS:Lorg/codehaus/groovy/transform/sc/StaticCompilationMetadataKeys;

    invoke-virtual {v7, v0}, Lorg/codehaus/groovy/ast/ClassNode;->getNodeMetaData(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/Map;

    .line 261
    sget-object v1, Lorg/codehaus/groovy/transform/sc/StaticCompilationMetadataKeys;->PRIVATE_FIELDS_MUTATORS:Lorg/codehaus/groovy/transform/sc/StaticCompilationMetadataKeys;

    invoke-virtual {v7, v1}, Lorg/codehaus/groovy/ast/ClassNode;->getNodeMetaData(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/util/Map;

    if-nez v0, :cond_e

    if-eqz v1, :cond_0

    goto/16 :goto_8

    .line 266
    :cond_0
    sget-object v0, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->PV_FIELDS_ACCESS:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    invoke-virtual {v7, v0}, Lorg/codehaus/groovy/ast/ClassNode;->getNodeMetaData(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/Set;

    .line 267
    sget-object v1, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->PV_FIELDS_MUTATION:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    invoke-virtual {v7, v1}, Lorg/codehaus/groovy/ast/ClassNode;->getNodeMetaData(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    move-object v8, v1

    check-cast v8, Ljava/util/Set;

    if-nez v0, :cond_1

    if-nez v8, :cond_1

    return-void

    :cond_1
    if-eqz v8, :cond_2

    .line 271
    new-instance v1, Ljava/util/HashSet;

    invoke-static {v0}, Ljava/util/Optional;->ofNullable(Ljava/lang/Object;)Ljava/util/Optional;

    move-result-object v0

    sget-object v2, Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor$$ExternalSyntheticLambda1;->INSTANCE:Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor$$ExternalSyntheticLambda1;

    invoke-virtual {v0, v2}, Ljava/util/Optional;->orElseGet(Ljava/util/function/Supplier;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/Collection;

    invoke-direct {v1, v0}, Ljava/util/HashSet;-><init>(Ljava/util/Collection;)V

    .line 272
    invoke-interface {v1, v8}, Ljava/util/Set;->addAll(Ljava/util/Collection;)Z

    move-object v9, v1

    goto :goto_0

    :cond_2
    move-object v9, v0

    :goto_0
    const/4 v0, -0x1

    const/4 v1, 0x0

    if-eqz v9, :cond_3

    .line 276
    new-instance v2, Ljava/util/HashMap;

    invoke-direct {v2}, Ljava/util/HashMap;-><init>()V

    move-object v10, v2

    goto :goto_1

    :cond_3
    move-object v10, v1

    :goto_1
    if-eqz v8, :cond_4

    .line 277
    new-instance v1, Ljava/util/HashMap;

    invoke-direct {v1}, Ljava/util/HashMap;-><init>()V

    :cond_4
    move-object v11, v1

    .line 279
    invoke-virtual/range {p0 .. p0}, Lorg/codehaus/groovy/ast/ClassNode;->getFields()Ljava/util/List;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v12

    :cond_5
    :goto_2
    invoke-interface {v12}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_c

    invoke-interface {v12}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    move-object v13, v1

    check-cast v13, Lorg/codehaus/groovy/ast/FieldNode;

    const/4 v14, 0x0

    const/4 v15, 0x1

    if-eqz v9, :cond_6

    .line 280
    invoke-interface {v9, v13}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_6

    move/from16 v16, v15

    goto :goto_3

    :cond_6
    move/from16 v16, v14

    :goto_3
    if-eqz v8, :cond_7

    .line 281
    invoke-interface {v8, v13}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_7

    move/from16 v17, v15

    goto :goto_4

    :cond_7
    move/from16 v17, v14

    :goto_4
    const-string v6, "$that"

    if-eqz v16, :cond_9

    add-int/lit8 v5, v0, 0x1

    .line 284
    new-instance v0, Lorg/codehaus/groovy/ast/Parameter;

    invoke-virtual/range {p0 .. p0}, Lorg/codehaus/groovy/ast/ClassNode;->getPlainNodeReference()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    invoke-direct {v0, v1, v6}, Lorg/codehaus/groovy/ast/Parameter;-><init>(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)V

    .line 285
    invoke-virtual {v13}, Lorg/codehaus/groovy/ast/FieldNode;->isStatic()Z

    move-result v1

    if-eqz v1, :cond_8

    invoke-static/range {p0 .. p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->classX(Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/ClassExpression;

    move-result-object v1

    goto :goto_5

    :cond_8
    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v1

    .line 286
    :goto_5
    invoke-virtual {v13}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object v2

    invoke-static {v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->constX(Ljava/lang/Object;)Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    move-result-object v2

    invoke-static {v1, v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->attrX(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v1

    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->returnS(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v18

    .line 287
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "pfaccess$"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, v5}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    const/16 v2, 0x1009

    invoke-virtual {v13}, Lorg/codehaus/groovy/ast/FieldNode;->getOriginType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v3

    new-array v4, v15, [Lorg/codehaus/groovy/ast/Parameter;

    aput-object v0, v4, v14

    sget-object v19, Lorg/codehaus/groovy/ast/ClassNode;->EMPTY_ARRAY:[Lorg/codehaus/groovy/ast/ClassNode;

    move-object/from16 v0, p0

    move/from16 v20, v5

    move-object/from16 v5, v19

    move-object v15, v6

    move-object/from16 v6, v18

    invoke-virtual/range {v0 .. v6}, Lorg/codehaus/groovy/ast/ClassNode;->addMethod(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/MethodNode;

    move-result-object v0

    .line 288
    sget-object v1, Lorg/codehaus/groovy/transform/sc/StaticCompilationMetadataKeys;->STATIC_COMPILE_NODE:Lorg/codehaus/groovy/transform/sc/StaticCompilationMetadataKeys;

    sget-object v2, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    invoke-virtual {v0, v1, v2}, Lorg/codehaus/groovy/ast/MethodNode;->setNodeMetaData(Ljava/lang/Object;Ljava/lang/Object;)V

    .line 289
    invoke-virtual {v13}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-interface {v10, v1, v0}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move/from16 v0, v20

    goto :goto_6

    :cond_9
    move-object v15, v6

    :goto_6
    if-eqz v17, :cond_5

    if-nez v16, :cond_a

    add-int/lit8 v0, v0, 0x1

    :cond_a
    move v6, v0

    .line 294
    new-instance v0, Lorg/codehaus/groovy/ast/Parameter;

    invoke-virtual/range {p0 .. p0}, Lorg/codehaus/groovy/ast/ClassNode;->getPlainNodeReference()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    invoke-direct {v0, v1, v15}, Lorg/codehaus/groovy/ast/Parameter;-><init>(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)V

    .line 295
    invoke-virtual {v13}, Lorg/codehaus/groovy/ast/FieldNode;->isStatic()Z

    move-result v1

    if-eqz v1, :cond_b

    invoke-static/range {p0 .. p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->classX(Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/ClassExpression;

    move-result-object v1

    goto :goto_7

    :cond_b
    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v1

    .line 296
    :goto_7
    new-instance v2, Lorg/codehaus/groovy/ast/Parameter;

    invoke-virtual {v13}, Lorg/codehaus/groovy/ast/FieldNode;->getOriginType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v3

    const-string v4, "$value"

    invoke-direct {v2, v3, v4}, Lorg/codehaus/groovy/ast/Parameter;-><init>(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)V

    .line 297
    invoke-virtual {v13}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object v3

    invoke-static {v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->constX(Ljava/lang/Object;)Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    move-result-object v3

    invoke-static {v1, v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->attrX(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v1

    invoke-static {v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v3

    invoke-static {v1, v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v15

    .line 298
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "pfaccess$0"

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, v6}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    const/16 v3, 0x1009

    invoke-virtual {v13}, Lorg/codehaus/groovy/ast/FieldNode;->getOriginType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v4

    const/4 v5, 0x2

    new-array v5, v5, [Lorg/codehaus/groovy/ast/Parameter;

    aput-object v0, v5, v14

    const/4 v0, 0x1

    aput-object v2, v5, v0

    sget-object v14, Lorg/codehaus/groovy/ast/ClassNode;->EMPTY_ARRAY:[Lorg/codehaus/groovy/ast/ClassNode;

    move-object/from16 v0, p0

    move v2, v3

    move-object v3, v4

    move-object v4, v5

    move-object v5, v14

    move v14, v6

    move-object v6, v15

    invoke-virtual/range {v0 .. v6}, Lorg/codehaus/groovy/ast/ClassNode;->addMethod(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/MethodNode;

    move-result-object v0

    .line 299
    sget-object v1, Lorg/codehaus/groovy/transform/sc/StaticCompilationMetadataKeys;->STATIC_COMPILE_NODE:Lorg/codehaus/groovy/transform/sc/StaticCompilationMetadataKeys;

    sget-object v2, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    invoke-virtual {v0, v1, v2}, Lorg/codehaus/groovy/ast/MethodNode;->setNodeMetaData(Ljava/lang/Object;Ljava/lang/Object;)V

    .line 300
    invoke-virtual {v13}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-interface {v11, v1, v0}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move v0, v14

    goto/16 :goto_2

    :cond_c
    if-eqz v10, :cond_d

    .line 304
    sget-object v0, Lorg/codehaus/groovy/transform/sc/StaticCompilationMetadataKeys;->PRIVATE_FIELDS_ACCESSORS:Lorg/codehaus/groovy/transform/sc/StaticCompilationMetadataKeys;

    invoke-virtual {v7, v0, v10}, Lorg/codehaus/groovy/ast/ClassNode;->setNodeMetaData(Ljava/lang/Object;Ljava/lang/Object;)V

    :cond_d
    if-eqz v11, :cond_e

    .line 307
    sget-object v0, Lorg/codehaus/groovy/transform/sc/StaticCompilationMetadataKeys;->PRIVATE_FIELDS_MUTATORS:Lorg/codehaus/groovy/transform/sc/StaticCompilationMetadataKeys;

    invoke-virtual {v7, v0, v11}, Lorg/codehaus/groovy/ast/ClassNode;->setNodeMetaData(Ljava/lang/Object;Ljava/lang/Object;)V

    :cond_e
    :goto_8
    return-void
.end method

.method private anyMethodSkip(Lorg/codehaus/groovy/ast/ClassNode;)Z
    .locals 1

    .line 210
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getMethods()Ljava/util/List;

    move-result-object p1

    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_1

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/ast/MethodNode;

    .line 211
    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor;->isSkipMode(Lorg/codehaus/groovy/ast/AnnotatedNode;)Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 p1, 0x1

    return p1

    :cond_1
    const/4 p1, 0x0

    return p1
.end method

.method public static isStaticallyCompiled(Lorg/codehaus/groovy/ast/AnnotatedNode;)Z
    .locals 2

    if-eqz p0, :cond_0

    .line 144
    sget-object v0, Lorg/codehaus/groovy/transform/sc/StaticCompilationMetadataKeys;->STATIC_COMPILE_NODE:Lorg/codehaus/groovy/transform/sc/StaticCompilationMetadataKeys;

    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/ast/AnnotatedNode;->getNodeMetaData(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 145
    sget-object v0, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    sget-object v1, Lorg/codehaus/groovy/transform/sc/StaticCompilationMetadataKeys;->STATIC_COMPILE_NODE:Lorg/codehaus/groovy/transform/sc/StaticCompilationMetadataKeys;

    invoke-virtual {p0, v1}, Lorg/codehaus/groovy/ast/AnnotatedNode;->getNodeMetaData(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    invoke-virtual {v0, p0}, Ljava/lang/Boolean;->equals(Ljava/lang/Object;)Z

    move-result p0

    return p0

    .line 147
    :cond_0
    instance-of v0, p0, Lorg/codehaus/groovy/ast/MethodNode;

    if-eqz v0, :cond_1

    .line 149
    sget-object v0, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    const-string v1, "DEFAULT_PARAMETER_GENERATED"

    invoke-virtual {p0, v1}, Lorg/codehaus/groovy/ast/AnnotatedNode;->getNodeMetaData(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/Boolean;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_2

    .line 150
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/AnnotatedNode;->getDeclaringClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p0

    invoke-static {p0}, Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor;->isStaticallyCompiled(Lorg/codehaus/groovy/ast/AnnotatedNode;)Z

    move-result p0

    return p0

    .line 152
    :cond_1
    instance-of v0, p0, Lorg/codehaus/groovy/ast/ClassNode;

    if-eqz v0, :cond_2

    .line 153
    check-cast p0, Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/ClassNode;->getOuterClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p0

    invoke-static {p0}, Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor;->isStaticallyCompiled(Lorg/codehaus/groovy/ast/AnnotatedNode;)Z

    move-result p0

    return p0

    :cond_2
    const/4 p0, 0x0

    return p0
.end method

.method static synthetic lambda$addDynamicOuterClassAccessorsCallback$0(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/control/SourceUnit;Lorg/codehaus/groovy/classgen/GeneratorContext;Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lorg/codehaus/groovy/control/CompilationFailedException;
        }
    .end annotation

    if-ne p3, p0, :cond_0

    .line 171
    invoke-static {p3}, Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor;->addPrivateBridgeMethods(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 172
    invoke-static {p3}, Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor;->addPrivateFieldsAccessors(Lorg/codehaus/groovy/ast/ClassNode;)V

    :cond_0
    return-void
.end method

.method private static memorizeInitialExpressions(Lorg/codehaus/groovy/ast/MethodNode;)V
    .locals 5

    .line 414
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/MethodNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 415
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/MethodNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object p0

    array-length v0, p0

    const/4 v1, 0x0

    :goto_0
    if-ge v1, v0, :cond_0

    aget-object v2, p0, v1

    .line 416
    sget-object v3, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->INITIAL_EXPRESSION:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/Parameter;->getInitialExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v4

    invoke-virtual {v2, v3, v4}, Lorg/codehaus/groovy/ast/Parameter;->putNodeMetaData(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    return-void
.end method

.method private static methodSpecificGenerics(Lorg/codehaus/groovy/ast/MethodNode;)Ljava/util/List;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/MethodNode;",
            ")",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .line 402
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 403
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/MethodNode;->getGenericsTypes()[Lorg/codehaus/groovy/ast/GenericsType;

    move-result-object p0

    if-eqz p0, :cond_0

    .line 405
    array-length v1, p0

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v1, :cond_0

    aget-object v3, p0, v2

    .line 406
    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/GenericsType;->getName()Ljava/lang/String;

    move-result-object v3

    invoke-interface {v0, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_0
    return-object v0
.end method

.method private visitConstructorOrMethod(Lorg/codehaus/groovy/ast/MethodNode;)V
    .locals 3

    .line 217
    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor;->isSkipMode(Lorg/codehaus/groovy/ast/AnnotatedNode;)Z

    move-result v0

    if-nez v0, :cond_0

    .line 218
    invoke-static {p1}, Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor;->isStaticallyCompiled(Lorg/codehaus/groovy/ast/AnnotatedNode;)Z

    move-result v1

    if-eqz v1, :cond_0

    const/4 v1, 0x1

    goto :goto_0

    :cond_0
    const/4 v1, 0x0

    :goto_0
    if-eqz v0, :cond_1

    .line 220
    sget-object v0, Lorg/codehaus/groovy/transform/sc/StaticCompilationMetadataKeys;->STATIC_COMPILE_NODE:Lorg/codehaus/groovy/transform/sc/StaticCompilationMetadataKeys;

    sget-object v2, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    invoke-virtual {p1, v0, v2}, Lorg/codehaus/groovy/ast/MethodNode;->putNodeMetaData(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 222
    :cond_1
    instance-of v0, p1, Lorg/codehaus/groovy/ast/ConstructorNode;

    if-eqz v0, :cond_3

    .line 223
    move-object v0, p1

    check-cast v0, Lorg/codehaus/groovy/ast/ConstructorNode;

    invoke-super {p0, v0}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->visitConstructor(Lorg/codehaus/groovy/ast/ConstructorNode;)V

    .line 224
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getDeclaringClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    if-eqz v1, :cond_4

    .line 225
    invoke-static {v0}, Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor;->isStaticallyCompiled(Lorg/codehaus/groovy/ast/AnnotatedNode;)Z

    move-result v2

    if-nez v2, :cond_4

    .line 231
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->getFields()Ljava/util/List;

    move-result-object v2

    invoke-interface {v2}, Ljava/util/List;->isEmpty()Z

    move-result v2

    if-eqz v2, :cond_2

    .line 232
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->getProperties()Ljava/util/List;

    move-result-object v2

    invoke-interface {v2}, Ljava/util/List;->isEmpty()Z

    move-result v2

    if-eqz v2, :cond_2

    .line 233
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->getObjectInitializerStatements()Ljava/util/List;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_4

    :cond_2
    const-string v0, "Cannot statically compile constructor implicitly including non-static elements from fields, properties or initializers"

    .line 234
    invoke-virtual {p0, v0, p1}, Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor;->addStaticTypeError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    goto :goto_1

    .line 238
    :cond_3
    invoke-super {p0, p1}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->visitMethod(Lorg/codehaus/groovy/ast/MethodNode;)V

    :cond_4
    :goto_1
    if-eqz v1, :cond_5

    .line 241
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getDeclaringClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    .line 242
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor;->addDynamicOuterClassAccessorsCallback(Lorg/codehaus/groovy/ast/ClassNode;)V

    :cond_5
    return-void
.end method


# virtual methods
.method protected existsProperty(Lorg/codehaus/groovy/ast/expr/PropertyExpression;ZLorg/codehaus/groovy/ast/ClassCodeVisitorSupport;)Z
    .locals 4

    .line 494
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->getObjectExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    .line 495
    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor;->getType(Lorg/codehaus/groovy/ast/ASTNode;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    .line 496
    new-instance v2, Lgroovy/lang/Reference;

    invoke-direct {v2, v1}, Lgroovy/lang/Reference;-><init>(Ljava/lang/Object;)V

    .line 497
    new-instance v3, Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor$1;

    invoke-direct {v3, p0, p3, p1, v2}, Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor$1;-><init>(Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor;Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;Lorg/codehaus/groovy/ast/expr/PropertyExpression;Lgroovy/lang/Reference;)V

    .line 559
    invoke-super {p0, p1, p2, v3}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->existsProperty(Lorg/codehaus/groovy/ast/expr/PropertyExpression;ZLorg/codehaus/groovy/ast/ClassCodeVisitorSupport;)Z

    move-result p1

    if-eqz p1, :cond_1

    .line 561
    sget-object p2, Lorg/codehaus/groovy/transform/sc/StaticCompilationMetadataKeys;->PROPERTY_OWNER:Lorg/codehaus/groovy/transform/sc/StaticCompilationMetadataKeys;

    invoke-virtual {v0, p2}, Lorg/codehaus/groovy/ast/expr/Expression;->getNodeMetaData(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p2

    if-nez p2, :cond_0

    .line 562
    sget-object p2, Lorg/codehaus/groovy/transform/sc/StaticCompilationMetadataKeys;->PROPERTY_OWNER:Lorg/codehaus/groovy/transform/sc/StaticCompilationMetadataKeys;

    invoke-virtual {v2}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object p3

    invoke-virtual {v0, p2, p3}, Lorg/codehaus/groovy/ast/expr/Expression;->putNodeMetaData(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 564
    :cond_0
    sget-object p2, Lorg/codehaus/groovy/ast/ClassHelper;->LIST_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {v1, p2}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingSupport;->implementsInterfaceOrIsSubclassOf(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result p2

    if-eqz p2, :cond_1

    .line 565
    sget-object p2, Lorg/codehaus/groovy/transform/sc/StaticCompilationMetadataKeys;->COMPONENT_TYPE:Lorg/codehaus/groovy/transform/sc/StaticCompilationMetadataKeys;

    sget-object p3, Lorg/codehaus/groovy/ast/ClassHelper;->int_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {p0, v1, p3}, Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor;->inferComponentType(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p3

    invoke-virtual {v0, p2, p3}, Lorg/codehaus/groovy/ast/expr/Expression;->putNodeMetaData(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_1
    return p1
.end method

.method protected varargs findMethodOrFail(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;[Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/MethodNode;
    .locals 2

    .line 485
    invoke-super {p0, p1, p2, p3, p4}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->findMethodOrFail(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;[Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/MethodNode;

    move-result-object p2

    .line 486
    instance-of p4, p1, Lorg/codehaus/groovy/ast/expr/BinaryExpression;

    if-eqz p4, :cond_0

    if-eqz p2, :cond_0

    .line 487
    sget-object p4, Lorg/codehaus/groovy/transform/sc/StaticCompilationMetadataKeys;->BINARY_EXP_TARGET:Lorg/codehaus/groovy/transform/sc/StaticCompilationMetadataKeys;

    const/4 v0, 0x2

    new-array v0, v0, [Ljava/lang/Object;

    const/4 v1, 0x0

    aput-object p2, v0, v1

    const/4 v1, 0x1

    aput-object p3, v0, v1

    invoke-virtual {p1, p4, v0}, Lorg/codehaus/groovy/ast/expr/Expression;->putNodeMetaData(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_0
    return-object p2
.end method

.method protected getTypeCheckingAnnotations()[Lorg/codehaus/groovy/ast/ClassNode;
    .locals 3

    const/4 v0, 0x2

    new-array v0, v0, [Lorg/codehaus/groovy/ast/ClassNode;

    .line 140
    sget-object v1, Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor;->TYPECHECKED_CLASSNODE:Lorg/codehaus/groovy/ast/ClassNode;

    const/4 v2, 0x0

    aput-object v1, v0, v2

    sget-object v1, Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor;->COMPILESTATIC_CLASSNODE:Lorg/codehaus/groovy/ast/ClassNode;

    const/4 v2, 0x1

    aput-object v1, v0, v2

    return-object v0
.end method

.method public synthetic lambda$visitClass$1$org-codehaus-groovy-transform-sc-StaticCompilationVisitor(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/InnerClassNode;)V
    .locals 3

    .line 191
    invoke-virtual {p0, p2}, Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor;->isSkipMode(Lorg/codehaus/groovy/ast/AnnotatedNode;)Z

    move-result v0

    if-nez v0, :cond_1

    invoke-static {p1}, Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor;->isStaticallyCompiled(Lorg/codehaus/groovy/ast/AnnotatedNode;)Z

    move-result v0

    if-nez v0, :cond_0

    sget-object v0, Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor;->COMPILESTATIC_CLASSNODE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {p2, v0}, Lorg/apache/groovy/ast/tools/AnnotatedNodeUtils;->hasAnnotation(Lorg/codehaus/groovy/ast/AnnotatedNode;Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v0

    if-eqz v0, :cond_1

    :cond_0
    const/4 v0, 0x1

    goto :goto_0

    :cond_1
    const/4 v0, 0x0

    .line 193
    :goto_0
    sget-object v1, Lorg/codehaus/groovy/transform/sc/StaticCompilationMetadataKeys;->STATIC_COMPILE_NODE:Lorg/codehaus/groovy/transform/sc/StaticCompilationMetadataKeys;

    invoke-static {v0}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v2

    invoke-virtual {p2, v1, v2}, Lorg/codehaus/groovy/ast/InnerClassNode;->putNodeMetaData(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    if-eqz v0, :cond_2

    .line 194
    invoke-direct {p0, p2}, Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor;->anyMethodSkip(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v0

    if-nez v0, :cond_2

    .line 195
    const-class v0, Lorg/codehaus/groovy/classgen/asm/MopWriter$Factory;

    sget-object v1, Lorg/codehaus/groovy/classgen/asm/sc/StaticCompilationMopWriter;->FACTORY:Lorg/codehaus/groovy/classgen/asm/MopWriter$Factory;

    invoke-virtual {p2, v0, v1}, Lorg/codehaus/groovy/ast/InnerClassNode;->putNodeMetaData(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 197
    :cond_2
    const-class v0, Lorg/codehaus/groovy/classgen/asm/WriterControllerFactory;

    const-class v1, Lorg/codehaus/groovy/classgen/asm/WriterControllerFactory;

    invoke-virtual {p1, v1}, Lorg/codehaus/groovy/ast/ClassNode;->getNodeMetaData(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    invoke-virtual {p2, v0, p1}, Lorg/codehaus/groovy/ast/InnerClassNode;->putNodeMetaData(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method public visitClass(Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 3

    .line 183
    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor;->shouldSkipClassNode(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v0

    if-nez v0, :cond_0

    .line 184
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor;->anyMethodSkip(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v0

    if-nez v0, :cond_0

    .line 185
    const-class v0, Lorg/codehaus/groovy/classgen/asm/MopWriter$Factory;

    sget-object v1, Lorg/codehaus/groovy/classgen/asm/sc/StaticCompilationMopWriter;->FACTORY:Lorg/codehaus/groovy/classgen/asm/MopWriter$Factory;

    invoke-virtual {p1, v0, v1}, Lorg/codehaus/groovy/ast/ClassNode;->putNodeMetaData(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 188
    :cond_0
    iget-object v0, p0, Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor;->classNode:Lorg/codehaus/groovy/ast/ClassNode;

    iput-object p1, p0, Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor;->classNode:Lorg/codehaus/groovy/ast/ClassNode;

    .line 190
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getInnerClasses()Ljava/util/Iterator;

    move-result-object v1

    new-instance v2, Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor$$ExternalSyntheticLambda0;

    invoke-direct {v2, p0, p1}, Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor$$ExternalSyntheticLambda0;-><init>(Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor;Lorg/codehaus/groovy/ast/ClassNode;)V

    invoke-interface {v1, v2}, Ljava/util/Iterator;->forEachRemaining(Ljava/util/function/Consumer;)V

    .line 199
    invoke-super {p0, p1}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->visitClass(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 200
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor;->addPrivateFieldAndMethodAccessors(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 201
    invoke-static {p1}, Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor;->isStaticallyCompiled(Lorg/codehaus/groovy/ast/AnnotatedNode;)Z

    move-result v1

    if-eqz v1, :cond_1

    .line 202
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getOuterClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    .line 203
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor;->addDynamicOuterClassAccessorsCallback(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 206
    :cond_1
    iput-object v0, p0, Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor;->classNode:Lorg/codehaus/groovy/ast/ClassNode;

    return-void
.end method

.method public visitConstructor(Lorg/codehaus/groovy/ast/ConstructorNode;)V
    .locals 0

    .line 248
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor;->visitConstructorOrMethod(Lorg/codehaus/groovy/ast/MethodNode;)V

    return-void
.end method

.method public visitConstructorCallExpression(Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;)V
    .locals 7

    .line 438
    invoke-super {p0, p1}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->visitConstructorCallExpression(Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;)V

    .line 440
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;->isUsingAnonymousInnerClass()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    const-class v1, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;

    invoke-virtual {v0, v1}, Lorg/codehaus/groovy/ast/ClassNode;->getNodeMetaData(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 441
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    .line 442
    sget-object v1, Lorg/codehaus/groovy/transform/sc/StaticCompilationMetadataKeys;->STATIC_COMPILE_NODE:Lorg/codehaus/groovy/transform/sc/StaticCompilationMetadataKeys;

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->getEnclosingMethod()Lorg/codehaus/groovy/ast/MethodNode;

    move-result-object v2

    sget-object v3, Lorg/codehaus/groovy/transform/sc/StaticCompilationMetadataKeys;->STATIC_COMPILE_NODE:Lorg/codehaus/groovy/transform/sc/StaticCompilationMetadataKeys;

    invoke-virtual {v2, v3}, Lorg/codehaus/groovy/ast/MethodNode;->getNodeMetaData(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-virtual {v0, v1, v2}, Lorg/codehaus/groovy/ast/ClassNode;->putNodeMetaData(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 443
    const-class v1, Lorg/codehaus/groovy/classgen/asm/WriterControllerFactory;

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->getOuterClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v2

    const-class v3, Lorg/codehaus/groovy/classgen/asm/WriterControllerFactory;

    invoke-virtual {v2, v3}, Lorg/codehaus/groovy/ast/ClassNode;->getNodeMetaData(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-virtual {v0, v1, v2}, Lorg/codehaus/groovy/ast/ClassNode;->putNodeMetaData(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 446
    :cond_0
    sget-object v0, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->DIRECT_METHOD_CALL_TARGET:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    invoke-virtual {p1, v0}, Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;->getNodeMetaData(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/ast/MethodNode;

    if-nez v0, :cond_1

    .line 447
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;->getLineNumber()I

    move-result v1

    if-lez v1, :cond_1

    const-string v1, "Target constructor for constructor call expression hasn\'t been set"

    .line 448
    invoke-virtual {p0, v1, p1}, Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    goto :goto_2

    :cond_1
    if-nez v0, :cond_4

    .line 451
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;->getArguments()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/classgen/asm/InvocationWriter;->makeArgumentList(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    move-result-object v0

    .line 452
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;->getExpressions()Ljava/util/List;

    move-result-object v0

    .line 453
    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v1

    new-array v2, v1, [Lorg/codehaus/groovy/ast/ClassNode;

    const/4 v3, 0x0

    :goto_0
    if-ge v3, v1, :cond_2

    .line 455
    iget-object v4, p0, Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor;->typeChooser:Lorg/codehaus/groovy/classgen/asm/TypeChooser;

    invoke-interface {v0, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lorg/codehaus/groovy/ast/expr/Expression;

    iget-object v6, p0, Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor;->classNode:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-interface {v4, v5, v6}, Lorg/codehaus/groovy/classgen/asm/TypeChooser;->resolveType(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v4

    aput-object v4, v2, v3

    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    .line 457
    :cond_2
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;->isSuperCall()Z

    move-result v0

    if-eqz v0, :cond_3

    iget-object v0, p0, Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor;->classNode:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->getSuperClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    goto :goto_1

    :cond_3
    iget-object v0, p0, Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor;->classNode:Lorg/codehaus/groovy/ast/ClassNode;

    :goto_1
    const-string v1, "<init>"

    invoke-virtual {p0, p1, v0, v1, v2}, Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor;->findMethodOrFail(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;[Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/MethodNode;

    move-result-object v0

    .line 458
    sget-object v1, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->DIRECT_METHOD_CALL_TARGET:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    invoke-virtual {p1, v1, v0}, Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;->putNodeMetaData(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_4
    :goto_2
    if-eqz v0, :cond_5

    .line 461
    invoke-static {v0}, Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor;->memorizeInitialExpressions(Lorg/codehaus/groovy/ast/MethodNode;)V

    :cond_5
    return-void
.end method

.method public visitForLoop(Lorg/codehaus/groovy/ast/stmt/ForStatement;)V
    .locals 4

    .line 467
    invoke-super {p0, p1}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->visitForLoop(Lorg/codehaus/groovy/ast/stmt/ForStatement;)V

    .line 468
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/stmt/ForStatement;->getCollectionExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    .line 469
    instance-of v1, v0, Lorg/codehaus/groovy/ast/expr/ClosureListExpression;

    if-nez v1, :cond_1

    .line 470
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/stmt/ForStatement;->getVariableType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    .line 471
    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor;->getType(Lorg/codehaus/groovy/ast/ASTNode;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    .line 473
    sget-object v2, Lorg/codehaus/groovy/ast/ClassHelper;->Character_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {v1}, Lorg/codehaus/groovy/ast/ClassHelper;->getWrapper(Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v3

    invoke-virtual {v2, v3}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_0

    sget-object v2, Lorg/codehaus/groovy/ast/ClassHelper;->STRING_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v2, v0}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_0

    goto :goto_0

    .line 477
    :cond_0
    invoke-static {v0}, Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor;->inferLoopElementType(Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    .line 479
    :goto_0
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/stmt/ForStatement;->getVariable()Lorg/codehaus/groovy/ast/Parameter;

    move-result-object p1

    invoke-virtual {p1, v1}, Lorg/codehaus/groovy/ast/Parameter;->setType(Lorg/codehaus/groovy/ast/ClassNode;)V

    :cond_1
    return-void
.end method

.method public visitMethod(Lorg/codehaus/groovy/ast/MethodNode;)V
    .locals 0

    .line 253
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor;->visitConstructorOrMethod(Lorg/codehaus/groovy/ast/MethodNode;)V

    return-void
.end method

.method public visitMethodCallExpression(Lorg/codehaus/groovy/ast/expr/MethodCallExpression;)V
    .locals 1

    .line 423
    invoke-super {p0, p1}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->visitMethodCallExpression(Lorg/codehaus/groovy/ast/expr/MethodCallExpression;)V

    .line 425
    sget-object v0, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->DIRECT_METHOD_CALL_TARGET:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    invoke-virtual {p1, v0}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->getNodeMetaData(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/ast/MethodNode;

    if-eqz v0, :cond_0

    .line 427
    invoke-virtual {p1, v0}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setMethodTarget(Lorg/codehaus/groovy/ast/MethodNode;)V

    .line 428
    invoke-static {v0}, Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor;->memorizeInitialExpressions(Lorg/codehaus/groovy/ast/MethodNode;)V

    .line 431
    :cond_0
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->getMethodTarget()Lorg/codehaus/groovy/ast/MethodNode;

    move-result-object v0

    if-nez v0, :cond_1

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->getLineNumber()I

    move-result v0

    if-lez v0, :cond_1

    const-string v0, "Target method for method call expression hasn\'t been set"

    .line 432
    invoke-virtual {p0, v0, p1}, Lorg/codehaus/groovy/transform/sc/StaticCompilationVisitor;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    :cond_1
    return-void
.end method

.method public visitPropertyExpression(Lorg/codehaus/groovy/ast/expr/PropertyExpression;)V
    .locals 2

    .line 573
    invoke-super {p0, p1}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingVisitor;->visitPropertyExpression(Lorg/codehaus/groovy/ast/expr/PropertyExpression;)V

    .line 574
    sget-object v0, Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;->DYNAMIC_RESOLUTION:Lorg/codehaus/groovy/transform/stc/StaticTypesMarker;

    invoke-virtual {p1, v0}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->getNodeMetaData(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 576
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->getObjectExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    sget-object v1, Lorg/codehaus/groovy/transform/sc/StaticCompilationMetadataKeys;->RECEIVER_OF_DYNAMIC_PROPERTY:Lorg/codehaus/groovy/transform/sc/StaticCompilationMetadataKeys;

    invoke-virtual {p1, v1, v0}, Lorg/codehaus/groovy/ast/expr/Expression;->putNodeMetaData(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_0
    return-void
.end method

.method public visitSpreadExpression(Lorg/codehaus/groovy/ast/expr/SpreadExpression;)V
    .locals 0

    return-void
.end method
