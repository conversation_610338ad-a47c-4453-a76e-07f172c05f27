.class public interface abstract Lorg/codehaus/groovy/transform/BuilderASTTransformation$BuilderStrategy;
.super Ljava/lang/Object;
.source "BuilderASTTransformation.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/codehaus/groovy/transform/BuilderASTTransformation;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "BuilderStrategy"
.end annotation


# virtual methods
.method public abstract build(Lorg/codehaus/groovy/transform/BuilderASTTransformation;Lorg/codehaus/groovy/ast/AnnotatedNode;Lorg/codehaus/groovy/ast/AnnotationNode;)V
.end method
