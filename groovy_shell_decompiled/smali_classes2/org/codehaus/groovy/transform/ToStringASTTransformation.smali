.class public Lorg/codehaus/groovy/transform/ToStringASTTransformation;
.super Lorg/codehaus/groovy/transform/AbstractASTTransformation;
.source "ToStringASTTransformation.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/codehaus/groovy/transform/ToStringASTTransformation$ToStringElement;
    }
.end annotation

.annotation runtime Lorg/codehaus/groovy/transform/GroovyASTTransformation;
    phase = .enum Lorg/codehaus/groovy/control/CompilePhase;->CANONICALIZATION:Lorg/codehaus/groovy/control/CompilePhase;
.end annotation


# static fields
.field private static final INVOKER_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

.field static final MY_CLASS:Ljava/lang/Class;

.field static final MY_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

.field static final MY_TYPE_NAME:Ljava/lang/String;

.field private static final STRINGBUILDER_TYPE:Lorg/codehaus/groovy/ast/ClassNode;


# direct methods
.method static constructor <clinit>()V
    .locals 3

    .line 75
    const-class v0, Lgroovy/transform/ToString;

    sput-object v0, Lorg/codehaus/groovy/transform/ToStringASTTransformation;->MY_CLASS:Ljava/lang/Class;

    .line 76
    invoke-static {v0}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/transform/ToStringASTTransformation;->MY_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    .line 77
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "@"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->getNameWithoutPackage()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/transform/ToStringASTTransformation;->MY_TYPE_NAME:Ljava/lang/String;

    .line 78
    const-class v0, Ljava/lang/StringBuilder;

    invoke-static {v0}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/transform/ToStringASTTransformation;->STRINGBUILDER_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    .line 79
    const-class v0, Lorg/codehaus/groovy/runtime/InvokerHelper;

    invoke-static {v0}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/transform/ToStringASTTransformation;->INVOKER_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 73
    invoke-direct {p0}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;-><init>()V

    return-void
.end method

.method private static appendCommaIfNotFirst(Lorg/codehaus/groovy/ast/stmt/BlockStatement;Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/VariableExpression;)V
    .locals 2

    .line 255
    sget-object v0, Lorg/codehaus/groovy/ast/expr/ConstantExpression;->FALSE:Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    .line 257
    invoke-static {p2, v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v0

    const-string v1, ", "

    .line 258
    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->constX(Ljava/lang/Object;)Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    move-result-object v1

    invoke-static {p1, v1}, Lorg/codehaus/groovy/transform/ToStringASTTransformation;->appendS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    .line 255
    invoke-static {p2, v0, p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ifElseS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/stmt/Statement;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/IfStatement;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    return-void
.end method

.method private static appendPrefix(Lorg/codehaus/groovy/ast/stmt/BlockStatement;Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;Z)V
    .locals 0

    if-eqz p3, :cond_0

    .line 262
    invoke-static {p1, p2}, Lorg/codehaus/groovy/transform/ToStringASTTransformation;->toStringPropertyName(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    :cond_0
    return-void
.end method

.method private static appendS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;
    .locals 1

    const-string v0, "append"

    .line 272
    invoke-static {p0, v0, p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object p0

    const/4 p1, 0x0

    .line 273
    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setImplicitThis(Z)V

    .line 274
    invoke-static {p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->stmt(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p0

    return-object p0
.end method

.method private static appendValue(Lorg/codehaus/groovy/ast/stmt/BlockStatement;Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/VariableExpression;Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;ZZZ)V
    .locals 1

    .line 233
    new-instance v0, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    invoke-direct {v0}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;-><init>()V

    if-eqz p6, :cond_0

    .line 234
    invoke-static {p3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->notNullX(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/BooleanExpression;

    move-result-object p6

    invoke-static {p6, v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ifS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/IfStatement;

    move-result-object p6

    goto :goto_0

    :cond_0
    move-object p6, v0

    .line 235
    :goto_0
    invoke-static {v0, p1, p2}, Lorg/codehaus/groovy/transform/ToStringASTTransformation;->appendCommaIfNotFirst(Lorg/codehaus/groovy/ast/stmt/BlockStatement;Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/VariableExpression;)V

    .line 236
    invoke-static {v0, p1, p4, p5}, Lorg/codehaus/groovy/transform/ToStringASTTransformation;->appendPrefix(Lorg/codehaus/groovy/ast/stmt/BlockStatement;Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;Z)V

    const-string p2, "toString"

    if-eqz p7, :cond_1

    .line 238
    new-instance p4, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    const-string p5, "this"

    invoke-direct {p4, p5}, Lorg/codehaus/groovy/ast/expr/VariableExpression;-><init>(Ljava/lang/String;)V

    .line 239
    invoke-static {p3, p4}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->sameX(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/BooleanExpression;

    move-result-object p4

    const-string p5, "(this)"

    .line 240
    invoke-static {p5}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->constX(Ljava/lang/Object;)Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    move-result-object p5

    invoke-static {p1, p5}, Lorg/codehaus/groovy/transform/ToStringASTTransformation;->appendS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p5

    sget-object p7, Lorg/codehaus/groovy/transform/ToStringASTTransformation;->INVOKER_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    .line 241
    invoke-static {p7, p2, p3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/StaticMethodCallExpression;

    move-result-object p2

    invoke-static {p1, p2}, Lorg/codehaus/groovy/transform/ToStringASTTransformation;->appendS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    .line 238
    invoke-static {p4, p5, p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ifElseS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/stmt/Statement;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/IfStatement;

    move-result-object p1

    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    goto :goto_1

    .line 243
    :cond_1
    sget-object p4, Lorg/codehaus/groovy/transform/ToStringASTTransformation;->INVOKER_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {p4, p2, p3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/StaticMethodCallExpression;

    move-result-object p2

    invoke-static {p1, p2}, Lorg/codehaus/groovy/transform/ToStringASTTransformation;->appendS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 246
    :goto_1
    invoke-virtual {p0, p6}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    return-void
.end method

.method private static calculateToStringStatements(Lorg/codehaus/groovy/ast/ClassNode;ZZZLjava/util/List;Ljava/util/List;ZZZZZLorg/codehaus/groovy/ast/stmt/BlockStatement;Z)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 22
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            "ZZZ",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;ZZZZZ",
            "Lorg/codehaus/groovy/ast/stmt/BlockStatement;",
            "Z)",
            "Lorg/codehaus/groovy/ast/expr/Expression;"
        }
    .end annotation

    move-object/from16 v12, p0

    move-object/from16 v13, p5

    move-object/from16 v14, p11

    const-string v0, "_result"

    .line 178
    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->localVarX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v15

    .line 179
    sget-object v0, Lorg/codehaus/groovy/transform/ToStringASTTransformation;->STRINGBUILDER_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ctorX(Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;

    move-result-object v0

    invoke-static {v15, v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->declS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v0

    invoke-virtual {v14, v0}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 180
    new-instance v11, Ljava/util/ArrayList;

    invoke-direct {v11}, Ljava/util/ArrayList;-><init>()V

    const-string v0, "$toStringFirst"

    .line 183
    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->localVarX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v10

    .line 184
    sget-object v0, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->constX(Ljava/lang/Object;)Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    move-result-object v0

    invoke-static {v10, v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->declS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v0

    invoke-virtual {v14, v0}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    if-eqz p8, :cond_0

    .line 187
    invoke-virtual/range {p0 .. p0}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v0

    goto :goto_0

    :cond_0
    invoke-virtual/range {p0 .. p0}, Lorg/codehaus/groovy/ast/ClassNode;->getNameWithoutPackage()Ljava/lang/String;

    move-result-object v0

    .line 188
    :goto_0
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "("

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->constX(Ljava/lang/Object;)Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    move-result-object v0

    invoke-static {v15, v0}, Lorg/codehaus/groovy/transform/ToStringASTTransformation;->appendS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v0

    invoke-virtual {v14, v0}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 190
    new-instance v16, Ljava/util/HashSet;

    invoke-direct/range {v16 .. v16}, Ljava/util/HashSet;-><init>()V

    const/4 v3, 0x1

    const/16 v17, 0x0

    const/16 v18, 0x0

    const/16 v19, 0x0

    const/4 v7, 0x0

    const/4 v9, 0x0

    const/4 v6, 0x0

    const/4 v8, 0x0

    const/16 v20, 0x0

    move-object/from16 v0, v16

    move-object/from16 v1, p0

    move-object/from16 v2, p0

    move/from16 v4, p2

    move/from16 v5, p10

    move-object/from16 v21, v10

    move/from16 v10, p12

    move-object v14, v11

    move/from16 v11, v20

    .line 192
    invoke-static/range {v0 .. v11}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->getAllProperties(Ljava/util/Set;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;ZZZZZZZZZ)Ljava/util/List;

    move-result-object v11

    if-nez p9, :cond_2

    if-eqz p3, :cond_1

    goto :goto_1

    :cond_1
    move-object/from16 v16, v15

    move-object v15, v11

    goto :goto_2

    .line 194
    :cond_2
    :goto_1
    invoke-virtual/range {p0 .. p0}, Lorg/codehaus/groovy/ast/ClassNode;->getSuperClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v2

    const/4 v7, 0x1

    const/4 v9, 0x1

    move-object/from16 v0, v16

    move-object/from16 v1, p0

    move/from16 v3, p9

    move/from16 v4, p3

    move/from16 v5, p10

    move/from16 v6, v17

    move/from16 v8, v18

    move/from16 v10, p12

    move-object/from16 v16, v15

    move-object v15, v11

    move/from16 v11, v19

    invoke-static/range {v0 .. v11}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->getAllProperties(Ljava/util/Set;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;ZZZZZZZZZ)Ljava/util/List;

    move-result-object v0

    invoke-interface {v15, v0}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 197
    :goto_2
    invoke-interface {v15}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_3
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_5

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/ast/PropertyNode;

    .line 198
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/PropertyNode;->getName()Ljava/lang/String;

    move-result-object v2

    move-object/from16 v3, p4

    move/from16 v4, p12

    .line 199
    invoke-static {v2, v3, v13, v4}, Lorg/codehaus/groovy/transform/ToStringASTTransformation;->shouldSkipUndefinedAware(Ljava/lang/String;Ljava/util/List;Ljava/util/List;Z)Z

    move-result v5

    if-eqz v5, :cond_3

    goto :goto_3

    .line 200
    :cond_3
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/PropertyNode;->getField()Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object v5

    .line 201
    invoke-virtual {v12, v2}, Lorg/codehaus/groovy/ast/ClassNode;->hasProperty(Ljava/lang/String;)Z

    move-result v6

    if-nez v6, :cond_4

    invoke-virtual {v5}, Lorg/codehaus/groovy/ast/FieldNode;->getDeclaringClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v6

    if-eqz v6, :cond_4

    .line 203
    new-instance v1, Lorg/codehaus/groovy/transform/ToStringASTTransformation$ToStringElement;

    invoke-static {v5}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v6

    invoke-virtual {v5}, Lorg/codehaus/groovy/ast/FieldNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v5

    invoke-static {v12, v5}, Lorg/codehaus/groovy/transform/ToStringASTTransformation;->canBeSelf(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v5

    invoke-direct {v1, v6, v2, v5}, Lorg/codehaus/groovy/transform/ToStringASTTransformation$ToStringElement;-><init>(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;Z)V

    invoke-interface {v14, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_3

    .line 205
    :cond_4
    invoke-static {v12, v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->getterThisX(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/PropertyNode;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v5

    .line 206
    new-instance v6, Lorg/codehaus/groovy/transform/ToStringASTTransformation$ToStringElement;

    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/PropertyNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    invoke-static {v12, v1}, Lorg/codehaus/groovy/transform/ToStringASTTransformation;->canBeSelf(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v1

    invoke-direct {v6, v5, v2, v1}, Lorg/codehaus/groovy/transform/ToStringASTTransformation$ToStringElement;-><init>(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;Z)V

    invoke-interface {v14, v6}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_3

    :cond_5
    const/4 v8, 0x0

    const-string v9, "toString"

    if-eqz p1, :cond_6

    .line 213
    new-instance v0, Lorg/codehaus/groovy/transform/ToStringASTTransformation$ToStringElement;

    invoke-static {v9}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callSuperX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object v1

    const-string v2, "super"

    invoke-direct {v0, v1, v2, v8}, Lorg/codehaus/groovy/transform/ToStringASTTransformation$ToStringElement;-><init>(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;Z)V

    invoke-interface {v14, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :cond_6
    if-eqz v13, :cond_7

    .line 217
    new-instance v0, Lorg/codehaus/groovy/transform/ToStringASTTransformation$$ExternalSyntheticLambda0;

    invoke-direct {v0, v13}, Lorg/codehaus/groovy/transform/ToStringASTTransformation$$ExternalSyntheticLambda0;-><init>(Ljava/util/List;)V

    invoke-static {v0}, Ljava/util/Comparator;->comparingInt(Ljava/util/function/ToIntFunction;)Ljava/util/Comparator;

    move-result-object v0

    .line 218
    invoke-interface {v14, v0}, Ljava/util/List;->sort(Ljava/util/Comparator;)V

    .line 221
    :cond_7
    invoke-interface {v14}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v10

    :goto_4
    invoke-interface {v10}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_8

    invoke-interface {v10}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/transform/ToStringASTTransformation$ToStringElement;

    .line 222
    iget-object v3, v0, Lorg/codehaus/groovy/transform/ToStringASTTransformation$ToStringElement;->value:Lorg/codehaus/groovy/ast/expr/Expression;

    iget-object v4, v0, Lorg/codehaus/groovy/transform/ToStringASTTransformation$ToStringElement;->name:Ljava/lang/String;

    iget-boolean v7, v0, Lorg/codehaus/groovy/transform/ToStringASTTransformation$ToStringElement;->canBeSelf:Z

    move-object/from16 v0, p11

    move-object/from16 v1, v16

    move-object/from16 v2, v21

    move/from16 v5, p6

    move/from16 v6, p7

    invoke-static/range {v0 .. v7}, Lorg/codehaus/groovy/transform/ToStringASTTransformation;->appendValue(Lorg/codehaus/groovy/ast/stmt/BlockStatement;Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/VariableExpression;Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;ZZZ)V

    goto :goto_4

    :cond_8
    const-string v0, ")"

    .line 226
    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->constX(Ljava/lang/Object;)Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    move-result-object v0

    move-object/from16 v1, v16

    invoke-static {v1, v0}, Lorg/codehaus/groovy/transform/ToStringASTTransformation;->appendS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v0

    move-object/from16 v2, p11

    invoke-virtual {v2, v0}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 227
    invoke-static {v1, v9}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object v0

    .line 228
    invoke-virtual {v0, v8}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setImplicitThis(Z)V

    return-object v0
.end method

.method private static canBeSelf(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;)Z
    .locals 0

    .line 250
    invoke-static {p1, p0}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingSupport;->implementsInterfaceOrIsSubclassOf(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result p0

    return p0
.end method

.method public static createToString(Lorg/codehaus/groovy/ast/ClassNode;ZZLjava/util/List;Ljava/util/List;Z)V
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            "ZZ",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;Z)V"
        }
    .end annotation

    const/4 v6, 0x0

    move-object v0, p0

    move v1, p1

    move v2, p2

    move-object v3, p3

    move-object v4, p4

    move v5, p5

    .line 117
    invoke-static/range {v0 .. v6}, Lorg/codehaus/groovy/transform/ToStringASTTransformation;->createToString(Lorg/codehaus/groovy/ast/ClassNode;ZZLjava/util/List;Ljava/util/List;ZZ)V

    return-void
.end method

.method public static createToString(Lorg/codehaus/groovy/ast/ClassNode;ZZLjava/util/List;Ljava/util/List;ZZ)V
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            "ZZ",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;ZZ)V"
        }
    .end annotation

    const/4 v7, 0x1

    move-object v0, p0

    move v1, p1

    move v2, p2

    move-object v3, p3

    move-object v4, p4

    move v5, p5

    move v6, p6

    .line 121
    invoke-static/range {v0 .. v7}, Lorg/codehaus/groovy/transform/ToStringASTTransformation;->createToString(Lorg/codehaus/groovy/ast/ClassNode;ZZLjava/util/List;Ljava/util/List;ZZZ)V

    return-void
.end method

.method public static createToString(Lorg/codehaus/groovy/ast/ClassNode;ZZLjava/util/List;Ljava/util/List;ZZZ)V
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            "ZZ",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;ZZZ)V"
        }
    .end annotation

    const/4 v8, 0x0

    move-object v0, p0

    move v1, p1

    move v2, p2

    move-object v3, p3

    move-object v4, p4

    move v5, p5

    move v6, p6

    move/from16 v7, p7

    .line 125
    invoke-static/range {v0 .. v8}, Lorg/codehaus/groovy/transform/ToStringASTTransformation;->createToString(Lorg/codehaus/groovy/ast/ClassNode;ZZLjava/util/List;Ljava/util/List;ZZZZ)V

    return-void
.end method

.method public static createToString(Lorg/codehaus/groovy/ast/ClassNode;ZZLjava/util/List;Ljava/util/List;ZZZZ)V
    .locals 10
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            "ZZ",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;ZZZZ)V"
        }
    .end annotation

    const/4 v9, 0x0

    move-object v0, p0

    move v1, p1

    move v2, p2

    move-object v3, p3

    move-object v4, p4

    move v5, p5

    move/from16 v6, p6

    move/from16 v7, p7

    move/from16 v8, p8

    .line 129
    invoke-static/range {v0 .. v9}, Lorg/codehaus/groovy/transform/ToStringASTTransformation;->createToString(Lorg/codehaus/groovy/ast/ClassNode;ZZLjava/util/List;Ljava/util/List;ZZZZZ)V

    return-void
.end method

.method public static createToString(Lorg/codehaus/groovy/ast/ClassNode;ZZLjava/util/List;Ljava/util/List;ZZZZZ)V
    .locals 11
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            "ZZ",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;ZZZZZ)V"
        }
    .end annotation

    const/4 v10, 0x1

    move-object v0, p0

    move v1, p1

    move v2, p2

    move-object v3, p3

    move-object v4, p4

    move/from16 v5, p5

    move/from16 v6, p6

    move/from16 v7, p7

    move/from16 v8, p8

    move/from16 v9, p9

    .line 133
    invoke-static/range {v0 .. v10}, Lorg/codehaus/groovy/transform/ToStringASTTransformation;->createToString(Lorg/codehaus/groovy/ast/ClassNode;ZZLjava/util/List;Ljava/util/List;ZZZZZZ)V

    return-void
.end method

.method public static createToString(Lorg/codehaus/groovy/ast/ClassNode;ZZLjava/util/List;Ljava/util/List;ZZZZZZ)V
    .locals 13
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            "ZZ",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;ZZZZZZ)V"
        }
    .end annotation

    const/4 v11, 0x0

    const/4 v12, 0x0

    move-object v0, p0

    move v1, p1

    move v2, p2

    move-object/from16 v3, p3

    move-object/from16 v4, p4

    move/from16 v5, p5

    move/from16 v6, p6

    move/from16 v7, p7

    move/from16 v8, p8

    move/from16 v9, p9

    move/from16 v10, p10

    .line 137
    invoke-static/range {v0 .. v12}, Lorg/codehaus/groovy/transform/ToStringASTTransformation;->createToString(Lorg/codehaus/groovy/ast/ClassNode;ZZLjava/util/List;Ljava/util/List;ZZZZZZZZ)V

    return-void
.end method

.method public static createToString(Lorg/codehaus/groovy/ast/ClassNode;ZZLjava/util/List;Ljava/util/List;ZZZZZZZZ)V
    .locals 18
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            "ZZ",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;ZZZZZZZZ)V"
        }
    .end annotation

    move-object/from16 v13, p0

    const-string v14, "toString"

    const/4 v0, 0x0

    .line 142
    invoke-static {v13, v14, v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->hasDeclaredMethod(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;I)Z

    move-result v15

    const-string v12, "_toString"

    if-eqz v15, :cond_0

    .line 143
    invoke-static {v13, v12, v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->hasDeclaredMethod(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;I)Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    .line 145
    :cond_0
    new-instance v11, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    invoke-direct {v11}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;-><init>()V

    if-eqz p8, :cond_1

    const/16 v0, 0x1002

    .line 148
    sget-object v1, Lorg/codehaus/groovy/ast/ClassHelper;->STRING_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    const/4 v2, 0x0

    const-string v3, "$to$string"

    invoke-virtual {v13, v3, v0, v1, v2}, Lorg/codehaus/groovy/ast/ClassNode;->addField(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object v0

    .line 149
    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v10

    .line 151
    invoke-static {v10}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->equalsNullX(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/BooleanExpression;

    move-result-object v9

    move-object/from16 v0, p0

    move/from16 v1, p1

    move/from16 v2, p2

    move/from16 v3, p12

    move-object/from16 v4, p3

    move-object/from16 v5, p4

    move/from16 v6, p5

    move/from16 v7, p6

    move/from16 v8, p7

    move-object v13, v9

    move/from16 v9, p9

    move-object/from16 v16, v14

    move-object v14, v10

    move/from16 v10, p10

    move-object/from16 p8, v11

    move-object/from16 v17, v12

    move/from16 v12, p11

    .line 152
    invoke-static/range {v0 .. v12}, Lorg/codehaus/groovy/transform/ToStringASTTransformation;->calculateToStringStatements(Lorg/codehaus/groovy/ast/ClassNode;ZZZLjava/util/List;Ljava/util/List;ZZZZZLorg/codehaus/groovy/ast/stmt/BlockStatement;Z)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    invoke-static {v14, v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v0

    .line 150
    invoke-static {v13, v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ifS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/IfStatement;

    move-result-object v0

    move-object/from16 v13, p8

    invoke-virtual {v13, v0}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    move-object v10, v14

    goto :goto_0

    :cond_1
    move-object v13, v11

    move-object/from16 v17, v12

    move-object/from16 v16, v14

    move-object/from16 v0, p0

    move/from16 v1, p1

    move/from16 v2, p2

    move/from16 v3, p12

    move-object/from16 v4, p3

    move-object/from16 v5, p4

    move/from16 v6, p5

    move/from16 v7, p6

    move/from16 v8, p7

    move/from16 v9, p9

    move/from16 v10, p10

    move/from16 v12, p11

    .line 156
    invoke-static/range {v0 .. v12}, Lorg/codehaus/groovy/transform/ToStringASTTransformation;->calculateToStringStatements(Lorg/codehaus/groovy/ast/ClassNode;ZZZLjava/util/List;Ljava/util/List;ZZZZZLorg/codehaus/groovy/ast/stmt/BlockStatement;Z)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v10

    .line 158
    :goto_0
    invoke-static {v10}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->returnS(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v0

    invoke-virtual {v13, v0}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    if-eqz v15, :cond_2

    move-object/from16 v16, v17

    :cond_2
    if-eqz v15, :cond_3

    const/4 v0, 0x2

    goto :goto_1

    :cond_3
    const/4 v0, 0x1

    .line 160
    :goto_1
    sget-object v1, Lorg/codehaus/groovy/ast/ClassHelper;->STRING_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    sget-object v2, Lorg/codehaus/groovy/ast/Parameter;->EMPTY_ARRAY:[Lorg/codehaus/groovy/ast/Parameter;

    sget-object v3, Lorg/codehaus/groovy/ast/ClassNode;->EMPTY_ARRAY:[Lorg/codehaus/groovy/ast/ClassNode;

    move-object/from16 p1, v16

    move/from16 p2, v0

    move-object/from16 p3, v1

    move-object/from16 p4, v2

    move-object/from16 p5, v3

    move-object/from16 p6, v13

    invoke-static/range {p0 .. p6}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->addGeneratedMethod(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/MethodNode;

    return-void
.end method

.method static synthetic lambda$calculateToStringStatements$0(Ljava/util/List;Lorg/codehaus/groovy/transform/ToStringASTTransformation$ToStringElement;)I
    .locals 0

    .line 217
    iget-object p1, p1, Lorg/codehaus/groovy/transform/ToStringASTTransformation$ToStringElement;->name:Ljava/lang/String;

    invoke-interface {p0, p1}, Ljava/util/List;->indexOf(Ljava/lang/Object;)I

    move-result p0

    return p0
.end method

.method private static toStringPropertyName(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;)Lorg/codehaus/groovy/ast/stmt/Statement;
    .locals 2

    .line 266
    new-instance v0, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    invoke-direct {v0}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;-><init>()V

    .line 267
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v1, ":"

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->constX(Ljava/lang/Object;)Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    move-result-object p1

    invoke-static {p0, p1}, Lorg/codehaus/groovy/transform/ToStringASTTransformation;->appendS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p0

    invoke-virtual {v0, p0}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    return-object v0
.end method


# virtual methods
.method public visit([Lorg/codehaus/groovy/ast/ASTNode;Lorg/codehaus/groovy/control/SourceUnit;)V
    .locals 25

    move-object/from16 v9, p0

    .line 82
    invoke-virtual/range {p0 .. p2}, Lorg/codehaus/groovy/transform/ToStringASTTransformation;->init([Lorg/codehaus/groovy/ast/ASTNode;Lorg/codehaus/groovy/control/SourceUnit;)V

    const/4 v0, 0x1

    .line 90
    invoke-static {v0}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v1

    .line 83
    aget-object v2, p1, v0

    check-cast v2, Lorg/codehaus/groovy/ast/AnnotatedNode;

    const/4 v3, 0x0

    .line 105
    invoke-static {v3}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v4

    .line 84
    aget-object v3, p1, v3

    move-object v10, v3

    check-cast v10, Lorg/codehaus/groovy/ast/AnnotationNode;

    .line 85
    sget-object v3, Lorg/codehaus/groovy/transform/ToStringASTTransformation;->MY_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v10}, Lorg/codehaus/groovy/ast/AnnotationNode;->getClassNode()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v5

    invoke-virtual {v3, v5}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-nez v3, :cond_0

    return-void

    .line 87
    :cond_0
    instance-of v3, v2, Lorg/codehaus/groovy/ast/ClassNode;

    if-eqz v3, :cond_8

    .line 88
    move-object v11, v2

    check-cast v11, Lorg/codehaus/groovy/ast/ClassNode;

    .line 89
    sget-object v12, Lorg/codehaus/groovy/transform/ToStringASTTransformation;->MY_TYPE_NAME:Ljava/lang/String;

    invoke-virtual {v9, v11, v12}, Lorg/codehaus/groovy/transform/ToStringASTTransformation;->checkNotInterface(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Z

    move-result v2

    if-nez v2, :cond_1

    return-void

    :cond_1
    const-string v2, "includeSuper"

    .line 90
    invoke-virtual {v9, v10, v2, v1}, Lorg/codehaus/groovy/transform/ToStringASTTransformation;->memberHasValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;Ljava/lang/Object;)Z

    move-result v2

    const-string v3, "includeSuperProperties"

    .line 91
    invoke-virtual {v9, v10, v3, v1}, Lorg/codehaus/groovy/transform/ToStringASTTransformation;->memberHasValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;Ljava/lang/Object;)Z

    move-result v20

    const-string v3, "includeSuperFields"

    .line 92
    invoke-virtual {v9, v10, v3, v1}, Lorg/codehaus/groovy/transform/ToStringASTTransformation;->memberHasValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;Ljava/lang/Object;)Z

    move-result v23

    const-string v3, "cache"

    .line 93
    invoke-virtual {v9, v10, v3, v1}, Lorg/codehaus/groovy/transform/ToStringASTTransformation;->memberHasValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;Ljava/lang/Object;)Z

    move-result v19

    const-string v3, "excludes"

    .line 94
    invoke-static {v10, v3}, Lorg/codehaus/groovy/transform/ToStringASTTransformation;->getMemberStringList(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;)Ljava/util/List;

    move-result-object v14

    const-string v3, "includes"

    .line 95
    invoke-static {v10, v3}, Lorg/codehaus/groovy/transform/ToStringASTTransformation;->getMemberStringList(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;)Ljava/util/List;

    move-result-object v15

    const-string v3, "super"

    if-eqz v15, :cond_2

    .line 96
    invoke-interface {v15, v3}, Ljava/util/List;->contains(Ljava/lang/Object;)Z

    move-result v5

    if-eqz v5, :cond_2

    move v13, v0

    goto :goto_0

    :cond_2
    move v13, v2

    :goto_0
    if-eqz v13, :cond_3

    .line 99
    invoke-virtual {v11}, Lorg/codehaus/groovy/ast/ClassNode;->getSuperClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v2

    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v2

    const-string v5, "java.lang.Object"

    invoke-virtual {v2, v5}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_3

    .line 100
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v5, "Error during "

    invoke-virtual {v2, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    const-string v5, " processing: includeSuper=true but \'"

    invoke-virtual {v2, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v11}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v2, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    const-string v5, "\' has no super class."

    invoke-virtual {v2, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v9, v2, v10}, Lorg/codehaus/groovy/transform/ToStringASTTransformation;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    :cond_3
    const-string v2, "includeNames"

    .line 102
    invoke-virtual {v9, v10, v2, v1}, Lorg/codehaus/groovy/transform/ToStringASTTransformation;->memberHasValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;Ljava/lang/Object;)Z

    move-result v16

    const-string v2, "includeFields"

    .line 103
    invoke-virtual {v9, v10, v2, v1}, Lorg/codehaus/groovy/transform/ToStringASTTransformation;->memberHasValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;Ljava/lang/Object;)Z

    move-result v17

    const-string v2, "ignoreNulls"

    .line 104
    invoke-virtual {v9, v10, v2, v1}, Lorg/codehaus/groovy/transform/ToStringASTTransformation;->memberHasValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;Ljava/lang/Object;)Z

    move-result v18

    const-string v2, "includePackage"

    .line 105
    invoke-virtual {v9, v10, v2, v4}, Lorg/codehaus/groovy/transform/ToStringASTTransformation;->memberHasValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;Ljava/lang/Object;)Z

    move-result v2

    xor-int/lit8 v21, v2, 0x1

    const-string v2, "allProperties"

    .line 106
    invoke-virtual {v9, v10, v2, v4}, Lorg/codehaus/groovy/transform/ToStringASTTransformation;->memberHasValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;Ljava/lang/Object;)Z

    move-result v2

    xor-int/lit8 v22, v2, 0x1

    const-string v0, "allNames"

    .line 107
    invoke-virtual {v9, v10, v0, v1}, Lorg/codehaus/groovy/transform/ToStringASTTransformation;->memberHasValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;Ljava/lang/Object;)Z

    move-result v24

    .line 109
    invoke-virtual {v9, v10, v14, v15, v12}, Lorg/codehaus/groovy/transform/ToStringASTTransformation;->checkIncludeExcludeUndefinedAware(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/util/List;Ljava/util/List;Ljava/lang/String;)Z

    move-result v0

    if-nez v0, :cond_4

    return-void

    :cond_4
    if-eqz v15, :cond_5

    .line 110
    invoke-static {v15, v3}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->minus(Ljava/util/List;Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    goto :goto_1

    :cond_5
    const/4 v0, 0x0

    :goto_1
    move-object v2, v0

    const-string v3, "includes"

    move-object/from16 v0, p0

    move-object v1, v11

    move-object v4, v10

    move-object v5, v12

    move/from16 v6, v17

    move/from16 v7, v20

    move/from16 v8, v22

    invoke-virtual/range {v0 .. v8}, Lorg/codehaus/groovy/transform/ToStringASTTransformation;->checkPropertyList(Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/List;Ljava/lang/String;Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;ZZZ)Z

    move-result v0

    if-nez v0, :cond_6

    return-void

    :cond_6
    const-string v3, "excludes"

    move-object/from16 v0, p0

    move-object v1, v11

    move-object v2, v14

    move-object v4, v10

    move-object v5, v12

    move/from16 v6, v17

    move/from16 v7, v20

    move/from16 v8, v22

    .line 111
    invoke-virtual/range {v0 .. v8}, Lorg/codehaus/groovy/transform/ToStringASTTransformation;->checkPropertyList(Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/List;Ljava/lang/String;Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;ZZZ)Z

    move-result v0

    if-nez v0, :cond_7

    return-void

    :cond_7
    move v12, v13

    move/from16 v13, v17

    move/from16 v17, v18

    move/from16 v18, v21

    move/from16 v21, v22

    move/from16 v22, v24

    .line 112
    invoke-static/range {v11 .. v23}, Lorg/codehaus/groovy/transform/ToStringASTTransformation;->createToString(Lorg/codehaus/groovy/ast/ClassNode;ZZLjava/util/List;Ljava/util/List;ZZZZZZZZ)V

    :cond_8
    return-void
.end method
