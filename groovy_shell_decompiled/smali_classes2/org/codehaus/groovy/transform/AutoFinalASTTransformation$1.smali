.class Lorg/codehaus/groovy/transform/AutoFinalASTTransformation$1;
.super Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;
.source "AutoFinalASTTransformation.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/codehaus/groovy/transform/AutoFinalASTTransformation;->createVisitor()Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lorg/codehaus/groovy/transform/AutoFinalASTTransformation;


# direct methods
.method constructor <init>(Lorg/codehaus/groovy/transform/AutoFinalASTTransformation;)V
    .locals 0

    .line 131
    iput-object p1, p0, Lorg/codehaus/groovy/transform/AutoFinalASTTransformation$1;->this$0:Lorg/codehaus/groovy/transform/AutoFinalASTTransformation;

    invoke-direct {p0}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;-><init>()V

    return-void
.end method


# virtual methods
.method protected getSourceUnit()Lorg/codehaus/groovy/control/SourceUnit;
    .locals 1

    .line 165
    iget-object v0, p0, Lorg/codehaus/groovy/transform/AutoFinalASTTransformation$1;->this$0:Lorg/codehaus/groovy/transform/AutoFinalASTTransformation;

    iget-object v0, v0, Lorg/codehaus/groovy/transform/AutoFinalASTTransformation;->sourceUnit:Lorg/codehaus/groovy/control/SourceUnit;

    return-object v0
.end method

.method public visitClosureExpression(Lorg/codehaus/groovy/ast/expr/ClosureExpression;)V
    .locals 5

    .line 134
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/ClosureExpression;->isSynthetic()Z

    move-result v0

    if-nez v0, :cond_1

    .line 135
    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/ClosureUtils;->getParametersSafe(Lorg/codehaus/groovy/ast/expr/ClosureExpression;)[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v0

    array-length v1, v0

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v1, :cond_0

    aget-object v3, v0, v2

    .line 136
    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/Parameter;->getModifiers()I

    move-result v4

    or-int/lit8 v4, v4, 0x10

    invoke-virtual {v3, v4}, Lorg/codehaus/groovy/ast/Parameter;->setModifiers(I)V

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 138
    :cond_0
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;->visitClosureExpression(Lorg/codehaus/groovy/ast/expr/ClosureExpression;)V

    :cond_1
    return-void
.end method

.method protected visitConstructorOrMethod(Lorg/codehaus/groovy/ast/MethodNode;Z)V
    .locals 1

    .line 144
    iget-object v0, p0, Lorg/codehaus/groovy/transform/AutoFinalASTTransformation$1;->this$0:Lorg/codehaus/groovy/transform/AutoFinalASTTransformation;

    invoke-static {v0}, Lorg/codehaus/groovy/transform/AutoFinalASTTransformation;->access$000(Lorg/codehaus/groovy/transform/AutoFinalASTTransformation;)Lorg/codehaus/groovy/ast/AnnotatedNode;

    move-result-object v0

    if-eq v0, p1, :cond_0

    invoke-static {p1}, Lorg/codehaus/groovy/transform/AutoFinalASTTransformation;->access$100(Lorg/codehaus/groovy/ast/AnnotatedNode;)Z

    move-result v0

    if-eqz v0, :cond_1

    .line 145
    :cond_0
    invoke-super {p0, p1, p2}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;->visitConstructorOrMethod(Lorg/codehaus/groovy/ast/MethodNode;Z)V

    :cond_1
    return-void
.end method

.method public visitDeclarationExpression(Lorg/codehaus/groovy/ast/expr/DeclarationExpression;)V
    .locals 1

    .line 158
    iget-object v0, p0, Lorg/codehaus/groovy/transform/AutoFinalASTTransformation$1;->this$0:Lorg/codehaus/groovy/transform/AutoFinalASTTransformation;

    invoke-static {v0}, Lorg/codehaus/groovy/transform/AutoFinalASTTransformation;->access$000(Lorg/codehaus/groovy/transform/AutoFinalASTTransformation;)Lorg/codehaus/groovy/ast/AnnotatedNode;

    move-result-object v0

    if-eq v0, p1, :cond_0

    invoke-static {p1}, Lorg/codehaus/groovy/transform/AutoFinalASTTransformation;->access$100(Lorg/codehaus/groovy/ast/AnnotatedNode;)Z

    move-result v0

    if-eqz v0, :cond_1

    .line 159
    :cond_0
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;->visitDeclarationExpression(Lorg/codehaus/groovy/ast/expr/DeclarationExpression;)V

    :cond_1
    return-void
.end method

.method public visitField(Lorg/codehaus/groovy/ast/FieldNode;)V
    .locals 1

    .line 151
    iget-object v0, p0, Lorg/codehaus/groovy/transform/AutoFinalASTTransformation$1;->this$0:Lorg/codehaus/groovy/transform/AutoFinalASTTransformation;

    invoke-static {v0}, Lorg/codehaus/groovy/transform/AutoFinalASTTransformation;->access$000(Lorg/codehaus/groovy/transform/AutoFinalASTTransformation;)Lorg/codehaus/groovy/ast/AnnotatedNode;

    move-result-object v0

    if-eq v0, p1, :cond_0

    invoke-static {p1}, Lorg/codehaus/groovy/transform/AutoFinalASTTransformation;->access$100(Lorg/codehaus/groovy/ast/AnnotatedNode;)Z

    move-result v0

    if-eqz v0, :cond_1

    .line 152
    :cond_0
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;->visitField(Lorg/codehaus/groovy/ast/FieldNode;)V

    :cond_1
    return-void
.end method
