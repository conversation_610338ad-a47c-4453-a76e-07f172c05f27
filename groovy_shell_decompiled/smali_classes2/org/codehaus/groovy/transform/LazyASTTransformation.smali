.class public Lorg/codehaus/groovy/transform/LazyASTTransformation;
.super Lorg/codehaus/groovy/transform/AbstractASTTransformation;
.source "LazyASTTransformation.java"


# annotations
.annotation runtime Lorg/codehaus/groovy/transform/GroovyASTTransformation;
    phase = .enum Lorg/codehaus/groovy/control/CompilePhase;->SEMANTIC_ANALYSIS:Lorg/codehaus/groovy/control/CompilePhase;
.end annotation


# static fields
.field private static final SOFT_REF:Lorg/codehaus/groovy/ast/ClassNode;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 72
    const-class v0, Ljava/lang/ref/SoftReference;

    const/4 v1, 0x0

    invoke-static {v0, v1}, Lorg/codehaus/groovy/ast/ClassHelper;->makeWithoutCaching(Ljava/lang/Class;Z)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/transform/LazyASTTransformation;->SOFT_REF:Lorg/codehaus/groovy/ast/ClassNode;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 70
    invoke-direct {p0}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;-><init>()V

    return-void
.end method

.method private static addDoubleCheckedLockingBody(Lorg/codehaus/groovy/ast/stmt/BlockStatement;Lorg/codehaus/groovy/ast/FieldNode;Lorg/codehaus/groovy/ast/expr/Expression;)V
    .locals 6

    .line 144
    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v0

    .line 145
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v2, "_local"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->localVarX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v1

    .line 146
    invoke-static {v1, v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->declS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v2

    invoke-virtual {p0, v2}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 148
    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->notNullX(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/BooleanExpression;

    move-result-object v2

    .line 149
    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->returnS(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v1

    new-instance v3, Lorg/codehaus/groovy/ast/stmt/SynchronizedStatement;

    .line 151
    invoke-static {p1}, Lorg/codehaus/groovy/transform/LazyASTTransformation;->syncTarget(Lorg/codehaus/groovy/ast/FieldNode;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    .line 153
    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->notNullX(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/BooleanExpression;

    move-result-object v4

    .line 154
    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->returnS(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v5

    .line 155
    invoke-static {v0, p2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignX(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p2

    invoke-static {p2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->returnS(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p2

    .line 152
    invoke-static {v4, v5, p2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ifElseS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/stmt/Statement;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/IfStatement;

    move-result-object p2

    invoke-direct {v3, p1, p2}, Lorg/codehaus/groovy/ast/stmt/SynchronizedStatement;-><init>(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 147
    invoke-static {v2, v1, v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ifElseS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/stmt/Statement;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/IfStatement;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    return-void
.end method

.method private static addHolderClassIdiomBody(Lorg/codehaus/groovy/ast/stmt/BlockStatement;Lorg/codehaus/groovy/ast/FieldNode;Lorg/codehaus/groovy/ast/expr/Expression;)V
    .locals 10

    .line 121
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/FieldNode;->getDeclaringClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v7

    .line 122
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/FieldNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v8

    .line 124
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v7}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "$"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v8}, Lorg/codehaus/groovy/ast/ClassNode;->getNameWithoutPackage()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "Holder_"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object p1

    const/4 v1, 0x1

    invoke-virtual {p1, v1}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    .line 125
    new-instance v9, Lorg/codehaus/groovy/ast/InnerClassNode;

    sget-object v0, Lorg/codehaus/groovy/ast/ClassHelper;->OBJECT_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    const/16 v1, 0xa

    invoke-direct {v9, v7, p1, v1, v0}, Lorg/codehaus/groovy/ast/InnerClassNode;-><init>(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;)V

    .line 132
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, "_initExpr"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    const/16 v0, 0x2e

    const/16 v1, 0x5f

    invoke-virtual {p1, v0, v1}, Ljava/lang/String;->replace(CC)Ljava/lang/String;

    move-result-object p1

    .line 133
    sget-object v4, Lorg/codehaus/groovy/ast/Parameter;->EMPTY_ARRAY:[Lorg/codehaus/groovy/ast/Parameter;

    sget-object v5, Lorg/codehaus/groovy/ast/ClassNode;->EMPTY_ARRAY:[Lorg/codehaus/groovy/ast/ClassNode;

    .line 134
    invoke-static {p2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->returnS(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v6

    const/16 v2, 0x1a

    move-object v0, v7

    move-object v1, p1

    move-object v3, v8

    .line 133
    invoke-static/range {v0 .. v6}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->addGeneratedMethod(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/MethodNode;

    .line 136
    invoke-static {v7, p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/StaticMethodCallExpression;

    move-result-object p1

    const-string p2, "INSTANCE"

    const/16 v0, 0x1a

    .line 135
    invoke-virtual {v9, p2, v0, v8, p1}, Lorg/codehaus/groovy/ast/InnerClassNode;->addField(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/FieldNode;

    .line 138
    invoke-static {v9}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->classX(Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/ClassExpression;

    move-result-object p1

    invoke-static {p1, p2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->propX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    move-result-object p1

    .line 139
    invoke-virtual {v7}, Lorg/codehaus/groovy/ast/ClassNode;->getModule()Lorg/codehaus/groovy/ast/ModuleNode;

    move-result-object p2

    invoke-virtual {p2, v9}, Lorg/codehaus/groovy/ast/ModuleNode;->addClass(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 140
    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->returnS(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    return-void
.end method

.method private static addMethod(Lorg/codehaus/groovy/ast/FieldNode;Lorg/codehaus/groovy/ast/stmt/BlockStatement;Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 10

    .line 168
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/FieldNode;->isStatic()Z

    move-result v0

    const/4 v1, 0x1

    if-eqz v0, :cond_0

    const/16 v0, 0x9

    goto :goto_0

    :cond_0
    move v0, v1

    .line 169
    :goto_0
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v2, v1}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Lorg/apache/groovy/util/BeanUtils;->capitalize(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    .line 170
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/FieldNode;->getDeclaringClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p0

    .line 171
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v9, "get"

    invoke-virtual {v2, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    sget-object v6, Lorg/codehaus/groovy/ast/Parameter;->EMPTY_ARRAY:[Lorg/codehaus/groovy/ast/Parameter;

    sget-object v7, Lorg/codehaus/groovy/ast/ClassNode;->EMPTY_ARRAY:[Lorg/codehaus/groovy/ast/ClassNode;

    move-object v2, p0

    move v4, v0

    move-object v5, p2

    move-object v8, p1

    invoke-static/range {v2 .. v8}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->addGeneratedMethod(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/MethodNode;

    .line 172
    sget-object p1, Lorg/codehaus/groovy/ast/ClassHelper;->boolean_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {p1, p2}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_1

    .line 173
    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "is"

    invoke-virtual {p1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    sget-object v6, Lorg/codehaus/groovy/ast/Parameter;->EMPTY_ARRAY:[Lorg/codehaus/groovy/ast/Parameter;

    sget-object v7, Lorg/codehaus/groovy/ast/ClassNode;->EMPTY_ARRAY:[Lorg/codehaus/groovy/ast/ClassNode;

    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p1, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    .line 174
    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callThisX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->stmt(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v8

    move-object v2, p0

    move v4, v0

    move-object v5, p2

    .line 173
    invoke-static/range {v2 .. v8}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->addGeneratedMethod(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/MethodNode;

    :cond_1
    return-void
.end method

.method private static addNonThreadSafeBody(Lorg/codehaus/groovy/ast/stmt/BlockStatement;Lorg/codehaus/groovy/ast/FieldNode;Lorg/codehaus/groovy/ast/expr/Expression;)V
    .locals 2

    .line 162
    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object p1

    .line 163
    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->notNullX(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/BooleanExpression;

    move-result-object v0

    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->stmt(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v1

    invoke-static {p1, p2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    invoke-static {v0, v1, p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ifElseS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/stmt/Statement;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/IfStatement;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    return-void
.end method

.method private static create(Lorg/codehaus/groovy/ast/FieldNode;Lorg/codehaus/groovy/ast/expr/Expression;)V
    .locals 2

    .line 109
    new-instance v0, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    invoke-direct {v0}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;-><init>()V

    .line 110
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/FieldNode;->isStatic()Z

    move-result v1

    if-eqz v1, :cond_0

    .line 111
    invoke-static {v0, p0, p1}, Lorg/codehaus/groovy/transform/LazyASTTransformation;->addHolderClassIdiomBody(Lorg/codehaus/groovy/ast/stmt/BlockStatement;Lorg/codehaus/groovy/ast/FieldNode;Lorg/codehaus/groovy/ast/expr/Expression;)V

    goto :goto_0

    .line 112
    :cond_0
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/FieldNode;->isVolatile()Z

    move-result v1

    if-eqz v1, :cond_1

    .line 113
    invoke-static {v0, p0, p1}, Lorg/codehaus/groovy/transform/LazyASTTransformation;->addDoubleCheckedLockingBody(Lorg/codehaus/groovy/ast/stmt/BlockStatement;Lorg/codehaus/groovy/ast/FieldNode;Lorg/codehaus/groovy/ast/expr/Expression;)V

    goto :goto_0

    .line 115
    :cond_1
    invoke-static {v0, p0, p1}, Lorg/codehaus/groovy/transform/LazyASTTransformation;->addNonThreadSafeBody(Lorg/codehaus/groovy/ast/stmt/BlockStatement;Lorg/codehaus/groovy/ast/FieldNode;Lorg/codehaus/groovy/ast/expr/Expression;)V

    .line 117
    :goto_0
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/FieldNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    invoke-static {p0, v0, p1}, Lorg/codehaus/groovy/transform/LazyASTTransformation;->addMethod(Lorg/codehaus/groovy/ast/FieldNode;Lorg/codehaus/groovy/ast/stmt/BlockStatement;Lorg/codehaus/groovy/ast/ClassNode;)V

    return-void
.end method

.method private static createSoft(Lorg/codehaus/groovy/ast/FieldNode;Lorg/codehaus/groovy/ast/expr/Expression;)V
    .locals 2

    .line 179
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/FieldNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    .line 180
    sget-object v1, Lorg/codehaus/groovy/transform/LazyASTTransformation;->SOFT_REF:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {p0, v1}, Lorg/codehaus/groovy/ast/FieldNode;->setType(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 181
    invoke-static {p0, p1, v0}, Lorg/codehaus/groovy/transform/LazyASTTransformation;->createSoftGetter(Lorg/codehaus/groovy/ast/FieldNode;Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 182
    invoke-static {p0, v0}, Lorg/codehaus/groovy/transform/LazyASTTransformation;->createSoftSetter(Lorg/codehaus/groovy/ast/FieldNode;Lorg/codehaus/groovy/ast/ClassNode;)V

    return-void
.end method

.method private static createSoftGetter(Lorg/codehaus/groovy/ast/FieldNode;Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 10

    .line 186
    new-instance v0, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    invoke-direct {v0}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;-><init>()V

    .line 187
    invoke-static {p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v1

    const-string v2, "_result"

    .line 188
    invoke-static {v2, p2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->localVarX(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v2

    const-string v3, "get"

    .line 189
    invoke-static {v1, v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object v3

    const/4 v4, 0x1

    .line 190
    invoke-virtual {v3, v4}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setSafe(Z)V

    .line 191
    invoke-static {v2, v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->declS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v5

    invoke-virtual {v0, v5}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 193
    invoke-static {v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->notNullX(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/BooleanExpression;

    move-result-object v5

    invoke-static {v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->stmt(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v6

    const/4 v7, 0x3

    new-array v7, v7, [Lorg/codehaus/groovy/ast/stmt/Statement;

    .line 194
    invoke-static {v2, p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    const/4 v8, 0x0

    aput-object p1, v7, v8

    sget-object p1, Lorg/codehaus/groovy/transform/LazyASTTransformation;->SOFT_REF:Lorg/codehaus/groovy/ast/ClassNode;

    .line 195
    invoke-static {p1, v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ctorX(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;

    move-result-object p1

    invoke-static {v1, p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    aput-object p1, v7, v4

    .line 196
    invoke-static {v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->stmt(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    const/4 v1, 0x2

    aput-object p1, v7, v1

    .line 193
    invoke-static {v7}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->block([Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    move-result-object p1

    invoke-static {v5, v6, p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ifElseS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/stmt/Statement;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/IfStatement;

    move-result-object p1

    .line 198
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/FieldNode;->isVolatile()Z

    move-result v5

    if-eqz v5, :cond_0

    .line 200
    invoke-static {v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->notNullX(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/BooleanExpression;

    move-result-object v5

    .line 201
    invoke-static {v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->stmt(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v6

    new-instance v7, Lorg/codehaus/groovy/ast/stmt/SynchronizedStatement;

    .line 202
    invoke-static {p0}, Lorg/codehaus/groovy/transform/LazyASTTransformation;->syncTarget(Lorg/codehaus/groovy/ast/FieldNode;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v9

    new-array v1, v1, [Lorg/codehaus/groovy/ast/stmt/Statement;

    .line 203
    invoke-static {v2, v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v2

    aput-object v2, v1, v8

    aput-object p1, v1, v4

    .line 202
    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->block([Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    move-result-object p1

    invoke-direct {v7, v9, p1}, Lorg/codehaus/groovy/ast/stmt/SynchronizedStatement;-><init>(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 199
    invoke-static {v5, v6, v7}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ifElseS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/stmt/Statement;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/IfStatement;

    move-result-object p1

    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    goto :goto_0

    .line 208
    :cond_0
    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 210
    :goto_0
    invoke-static {p0, v0, p2}, Lorg/codehaus/groovy/transform/LazyASTTransformation;->addMethod(Lorg/codehaus/groovy/ast/FieldNode;Lorg/codehaus/groovy/ast/stmt/BlockStatement;Lorg/codehaus/groovy/ast/ClassNode;)V

    return-void
.end method

.method private static createSoftSetter(Lorg/codehaus/groovy/ast/FieldNode;Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 7

    .line 214
    new-instance v6, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    invoke-direct {v6}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;-><init>()V

    .line 215
    invoke-static {p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v0

    .line 216
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x1

    invoke-virtual {v1, v2}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->getSetterName(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    const-string v3, "value"

    .line 217
    invoke-static {p1, v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->param(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/Parameter;

    move-result-object p1

    .line 218
    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v3

    .line 220
    invoke-static {v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->notNullX(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/BooleanExpression;

    move-result-object v4

    sget-object v5, Lorg/codehaus/groovy/transform/LazyASTTransformation;->SOFT_REF:Lorg/codehaus/groovy/ast/ClassNode;

    .line 221
    invoke-static {v5, v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ctorX(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;

    move-result-object v3

    invoke-static {v0, v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v3

    .line 222
    invoke-static {}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->nullX()Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    move-result-object v5

    invoke-static {v0, v5}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v0

    .line 219
    invoke-static {v4, v3, v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ifElseS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/stmt/Statement;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/IfStatement;

    move-result-object v0

    invoke-virtual {v6, v0}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 225
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/FieldNode;->isStatic()Z

    move-result v0

    if-eqz v0, :cond_0

    const/16 v0, 0x9

    move v3, v0

    goto :goto_0

    :cond_0
    move v3, v2

    .line 226
    :goto_0
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/FieldNode;->getDeclaringClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    .line 227
    sget-object p0, Lorg/codehaus/groovy/ast/ClassHelper;->VOID_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    new-array v2, v2, [Lorg/codehaus/groovy/ast/Parameter;

    const/4 v4, 0x0

    aput-object p1, v2, v4

    invoke-static {v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->params([Lorg/codehaus/groovy/ast/Parameter;)[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v4

    sget-object v5, Lorg/codehaus/groovy/ast/ClassNode;->EMPTY_ARRAY:[Lorg/codehaus/groovy/ast/ClassNode;

    move v2, v3

    move-object v3, p0

    invoke-static/range {v0 .. v6}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->addGeneratedMethod(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/MethodNode;

    return-void
.end method

.method private static getInitExpr(Lorg/codehaus/groovy/transform/ErrorCollecting;Lorg/codehaus/groovy/ast/FieldNode;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 2

    .line 235
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/FieldNode;->getInitialValueExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    const/4 v1, 0x0

    .line 236
    invoke-virtual {p1, v1}, Lorg/codehaus/groovy/ast/FieldNode;->setInitialValueExpression(Lorg/codehaus/groovy/ast/expr/Expression;)V

    if-eqz v0, :cond_0

    .line 238
    instance-of v1, v0, Lorg/codehaus/groovy/ast/expr/EmptyExpression;

    if-eqz v1, :cond_2

    .line 239
    :cond_0
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/FieldNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->isAbstract()Z

    move-result v0

    if-eqz v0, :cond_1

    .line 240
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "You cannot lazily initialize \'"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "\' from the abstract class \'"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    .line 241
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/FieldNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "\'"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    .line 240
    invoke-interface {p0, v0, p1}, Lorg/codehaus/groovy/transform/ErrorCollecting;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 243
    :cond_1
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/FieldNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p0

    invoke-static {p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ctorX(Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;

    move-result-object v0

    :cond_2
    return-object v0
.end method

.method private static syncTarget(Lorg/codehaus/groovy/ast/FieldNode;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 1

    .line 231
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/FieldNode;->isStatic()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/FieldNode;->getDeclaringClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p0

    invoke-static {p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->classX(Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/ClassExpression;

    move-result-object p0

    goto :goto_0

    :cond_0
    const-string p0, "this"

    invoke-static {p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object p0

    :goto_0
    return-object p0
.end method

.method static visitField(Lorg/codehaus/groovy/transform/ErrorCollecting;Lorg/codehaus/groovy/ast/AnnotationNode;Lorg/codehaus/groovy/ast/FieldNode;)V
    .locals 2

    const-string v0, "soft"

    .line 86
    invoke-virtual {p1, v0}, Lorg/codehaus/groovy/ast/AnnotationNode;->getMember(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    .line 87
    invoke-static {p0, p2}, Lorg/codehaus/groovy/transform/LazyASTTransformation;->getInitExpr(Lorg/codehaus/groovy/transform/ErrorCollecting;Lorg/codehaus/groovy/ast/FieldNode;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p0

    .line 89
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "$"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    .line 90
    invoke-virtual {p2, v0}, Lorg/codehaus/groovy/ast/FieldNode;->rename(Ljava/lang/String;)V

    .line 91
    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/FieldNode;->getModifiers()I

    move-result v1

    and-int/lit8 v1, v1, -0x6

    or-int/lit16 v1, v1, 0x1002

    invoke-virtual {p2, v1}, Lorg/codehaus/groovy/ast/FieldNode;->setModifiers(I)V

    .line 92
    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/FieldNode;->getDeclaringClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    invoke-virtual {v1, v0}, Lorg/codehaus/groovy/ast/ClassNode;->getProperty(Ljava/lang/String;)Lorg/codehaus/groovy/ast/PropertyNode;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 94
    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/FieldNode;->getDeclaringClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/ClassNode;->getProperties()Ljava/util/List;

    move-result-object v1

    invoke-interface {v1, v0}, Ljava/util/List;->remove(Ljava/lang/Object;)Z

    .line 97
    :cond_0
    instance-of v0, p1, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    if-eqz v0, :cond_1

    check-cast p1, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/ConstantExpression;->getValue()Ljava/lang/Object;

    move-result-object p1

    const/4 v0, 0x1

    invoke-static {v0}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v0

    invoke-virtual {p1, v0}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_1

    .line 98
    invoke-static {p2, p0}, Lorg/codehaus/groovy/transform/LazyASTTransformation;->createSoft(Lorg/codehaus/groovy/ast/FieldNode;Lorg/codehaus/groovy/ast/expr/Expression;)V

    goto :goto_0

    .line 100
    :cond_1
    invoke-static {p2, p0}, Lorg/codehaus/groovy/transform/LazyASTTransformation;->create(Lorg/codehaus/groovy/ast/FieldNode;Lorg/codehaus/groovy/ast/expr/Expression;)V

    .line 102
    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/FieldNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p0

    invoke-static {p0}, Lorg/codehaus/groovy/ast/ClassHelper;->isPrimitiveType(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result p0

    if-eqz p0, :cond_2

    .line 103
    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/FieldNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p0

    invoke-static {p0}, Lorg/codehaus/groovy/ast/ClassHelper;->getWrapper(Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p0

    invoke-virtual {p2, p0}, Lorg/codehaus/groovy/ast/FieldNode;->setType(Lorg/codehaus/groovy/ast/ClassNode;)V

    :cond_2
    :goto_0
    return-void
.end method


# virtual methods
.method public visit([Lorg/codehaus/groovy/ast/ASTNode;Lorg/codehaus/groovy/control/SourceUnit;)V
    .locals 1

    .line 75
    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/transform/LazyASTTransformation;->init([Lorg/codehaus/groovy/ast/ASTNode;Lorg/codehaus/groovy/control/SourceUnit;)V

    const/4 p2, 0x1

    .line 76
    aget-object p2, p1, p2

    check-cast p2, Lorg/codehaus/groovy/ast/AnnotatedNode;

    const/4 v0, 0x0

    .line 77
    aget-object p1, p1, v0

    check-cast p1, Lorg/codehaus/groovy/ast/AnnotationNode;

    .line 79
    instance-of v0, p2, Lorg/codehaus/groovy/ast/FieldNode;

    if-eqz v0, :cond_0

    .line 80
    check-cast p2, Lorg/codehaus/groovy/ast/FieldNode;

    .line 81
    invoke-static {p0, p1, p2}, Lorg/codehaus/groovy/transform/LazyASTTransformation;->visitField(Lorg/codehaus/groovy/transform/ErrorCollecting;Lorg/codehaus/groovy/ast/AnnotationNode;Lorg/codehaus/groovy/ast/FieldNode;)V

    :cond_0
    return-void
.end method
