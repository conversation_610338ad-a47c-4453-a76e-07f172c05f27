.class public Lorg/codehaus/groovy/transform/MemoizedASTTransformation;
.super Lorg/codehaus/groovy/transform/AbstractASTTransformation;
.source "MemoizedASTTransformation.java"


# annotations
.annotation runtime Lorg/codehaus/groovy/transform/GroovyASTTransformation;
    phase = .enum Lorg/codehaus/groovy/control/CompilePhase;->SEMANTIC_ANALYSIS:Lorg/codehaus/groovy/control/CompilePhase;
.end annotation


# static fields
.field private static final CLOSURE_CALL_METHOD_NAME:Ljava/lang/String; = "call"

.field private static final CLOSURE_LABEL:Ljava/lang/String; = "Closure"

.field private static final MAX_CACHE_SIZE_NAME:Ljava/lang/String; = "maxCacheSize"

.field private static final MEMOIZE_AT_LEAST_METHOD_NAME:Ljava/lang/String; = "memoizeAtLeast"

.field private static final MEMOIZE_AT_MOST_METHOD_NAME:Ljava/lang/String; = "memoizeAtMost"

.field private static final MEMOIZE_BETWEEN_METHOD_NAME:Ljava/lang/String; = "memoizeBetween"

.field private static final MEMOIZE_METHOD_NAME:Ljava/lang/String; = "memoize"

.field private static final METHOD_LABEL:Ljava/lang/String; = "Priv"

.field private static final MY_CLASS:Ljava/lang/Class;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/lang/Class<",
            "Lgroovy/transform/Memoized;",
            ">;"
        }
    .end annotation
.end field

.field private static final MY_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

.field private static final MY_TYPE_NAME:Ljava/lang/String;

.field private static final OVERRIDE_CLASSNODE:Lorg/codehaus/groovy/ast/ClassNode;

.field private static final PROTECTED_CACHE_SIZE_NAME:Ljava/lang/String; = "protectedCacheSize"


# direct methods
.method static constructor <clinit>()V
    .locals 3

    .line 63
    const-class v0, Lgroovy/transform/Memoized;

    sput-object v0, Lorg/codehaus/groovy/transform/MemoizedASTTransformation;->MY_CLASS:Ljava/lang/Class;

    .line 64
    invoke-static {v0}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/transform/MemoizedASTTransformation;->MY_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    .line 65
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "@"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->getNameWithoutPackage()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/transform/MemoizedASTTransformation;->MY_TYPE_NAME:Ljava/lang/String;

    .line 70
    const-class v0, Ljava/lang/Override;

    invoke-static {v0}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/transform/MemoizedASTTransformation;->OVERRIDE_CLASSNODE:Lorg/codehaus/groovy/ast/ClassNode;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 60
    invoke-direct {p0}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;-><init>()V

    return-void
.end method

.method private static buildDelegatingMethod(Lorg/codehaus/groovy/ast/MethodNode;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/MethodNode;
    .locals 8

    .line 122
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/MethodNode;->getCode()Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v6

    .line 124
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/MethodNode;->isStatic()Z

    move-result v0

    if-eqz v0, :cond_0

    const/16 v0, 0xa

    goto :goto_0

    :cond_0
    const/4 v0, 0x4

    :goto_0
    move v2, v0

    .line 127
    new-instance v7, Lorg/codehaus/groovy/ast/MethodNode;

    const-string v0, "Priv"

    .line 128
    invoke-static {p1, v0, p0}, Lorg/codehaus/groovy/transform/MemoizedASTTransformation;->buildUniqueName(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/MethodNode;)Ljava/lang/String;

    move-result-object v1

    .line 130
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/MethodNode;->getReturnType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v3

    .line 131
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/MethodNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->cloneParams([Lorg/codehaus/groovy/ast/Parameter;)[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v4

    .line 132
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/MethodNode;->getExceptions()[Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v5

    move-object v0, v7

    invoke-direct/range {v0 .. v6}, Lorg/codehaus/groovy/ast/MethodNode;-><init>(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 135
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/MethodNode;->getAnnotations()Ljava/util/List;

    move-result-object p0

    invoke-static {p0}, Lorg/codehaus/groovy/transform/MemoizedASTTransformation;->filterAnnotations(Ljava/util/List;)Ljava/util/List;

    move-result-object p0

    invoke-virtual {v7, p0}, Lorg/codehaus/groovy/ast/MethodNode;->addAnnotations(Ljava/util/List;)V

    return-object v7
.end method

.method private static buildMemoizeClosureCallExpression(Lorg/codehaus/groovy/ast/MethodNode;II)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;
    .locals 6

    .line 156
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/MethodNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v0

    .line 157
    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->cloneParams([Lorg/codehaus/groovy/ast/Parameter;)[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v1

    .line 158
    new-instance v2, Ljava/util/ArrayList;

    array-length v3, v1

    invoke-direct {v2, v3}, Ljava/util/ArrayList;-><init>(I)V

    const/4 v3, 0x0

    move v4, v3

    .line 159
    :goto_0
    array-length v5, v0

    if-ge v4, v5, :cond_0

    .line 160
    aget-object v5, v1, v4

    invoke-static {v5}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v5

    invoke-interface {v2, v5}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    add-int/lit8 v4, v4, 0x1

    goto :goto_0

    .line 163
    :cond_0
    new-instance v0, Lorg/codehaus/groovy/ast/expr/ClosureExpression;

    .line 165
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/MethodNode;->getName()Ljava/lang/String;

    move-result-object p0

    invoke-static {v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->args(Ljava/util/List;)Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    move-result-object v2

    invoke-static {p0, v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callThisX(Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object p0

    invoke-static {p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->stmt(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p0

    invoke-direct {v0, v1, p0}, Lorg/codehaus/groovy/ast/expr/ClosureExpression;-><init>([Lorg/codehaus/groovy/ast/Parameter;Lorg/codehaus/groovy/ast/stmt/Statement;)V

    if-nez p1, :cond_1

    if-nez p2, :cond_1

    const-string p0, "memoize"

    .line 169
    invoke-static {v0, p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object p0

    goto :goto_1

    :cond_1
    const/4 p0, 0x1

    if-nez p1, :cond_2

    new-array p0, p0, [Lorg/codehaus/groovy/ast/expr/Expression;

    .line 171
    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->constX(Ljava/lang/Object;)Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    move-result-object p1

    aput-object p1, p0, v3

    invoke-static {p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->args([Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    move-result-object p0

    const-string p1, "memoizeAtMost"

    invoke-static {v0, p1, p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object p0

    goto :goto_1

    :cond_2
    if-nez p2, :cond_3

    new-array p0, p0, [Lorg/codehaus/groovy/ast/expr/Expression;

    .line 173
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->constX(Ljava/lang/Object;)Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    move-result-object p1

    aput-object p1, p0, v3

    invoke-static {p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->args([Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    move-result-object p0

    const-string p1, "memoizeAtLeast"

    invoke-static {v0, p1, p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object p0

    goto :goto_1

    :cond_3
    const/4 v1, 0x2

    new-array v1, v1, [Lorg/codehaus/groovy/ast/expr/Expression;

    .line 175
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->constX(Ljava/lang/Object;)Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    move-result-object p1

    aput-object p1, v1, v3

    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->constX(Ljava/lang/Object;)Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    move-result-object p1

    aput-object p1, v1, p0

    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->args([Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    move-result-object p0

    const-string p1, "memoizeBetween"

    invoke-static {v0, p1, p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object p0

    .line 177
    :goto_1
    invoke-virtual {p0, v3}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setImplicitThis(Z)V

    return-object p0
.end method

.method private static buildTypeName(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/lang/String;
    .locals 2

    .line 196
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/ClassNode;->isArray()Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    new-array v0, v0, [Ljava/lang/Object;

    const/4 v1, 0x0

    .line 197
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/ClassNode;->getComponentType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p0

    invoke-static {p0}, Lorg/codehaus/groovy/transform/MemoizedASTTransformation;->buildTypeName(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/lang/String;

    move-result-object p0

    aput-object p0, v0, v1

    const-string p0, "%sArray"

    invoke-static {p0, v0}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p0

    return-object p0

    .line 199
    :cond_0
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/ClassNode;->getNameWithoutPackage()Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method private static buildUniqueName(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/MethodNode;)Ljava/lang/String;
    .locals 4

    .line 182
    new-instance v0, Ljava/lang/StringBuilder;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "memoizedMethod"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v1, "$"

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {v0, p1}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/MethodNode;->getName()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    .line 183
    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/MethodNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v0

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    .line 184
    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/MethodNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object p2

    array-length v0, p2

    move v2, v1

    :goto_0
    if-ge v2, v0, :cond_0

    aget-object v3, p2, v2

    .line 185
    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/Parameter;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v3

    invoke-static {v3}, Lorg/codehaus/groovy/transform/MemoizedASTTransformation;->buildTypeName(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {p1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 188
    :cond_0
    :goto_1
    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p2}, Lorg/codehaus/groovy/ast/ClassNode;->getField(Ljava/lang/String;)Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object p2

    if-eqz p2, :cond_1

    const-string p2, "_"

    .line 189
    invoke-virtual {p1, v1, p2}, Ljava/lang/StringBuilder;->insert(ILjava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_1

    .line 192
    :cond_1
    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method private static filterAnnotations(Ljava/util/List;)Ljava/util/List;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/AnnotationNode;",
            ">;)",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/AnnotationNode;",
            ">;"
        }
    .end annotation

    .line 140
    new-instance v0, Ljava/util/ArrayList;

    invoke-interface {p0}, Ljava/util/List;->size()I

    move-result v1

    invoke-direct {v0, v1}, Ljava/util/ArrayList;-><init>(I)V

    .line 141
    invoke-interface {p0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p0

    :cond_0
    :goto_0
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/ast/AnnotationNode;

    .line 142
    sget-object v2, Lorg/codehaus/groovy/transform/MemoizedASTTransformation;->OVERRIDE_CLASSNODE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/AnnotationNode;->getClassNode()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v3

    invoke-virtual {v2, v3}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-nez v2, :cond_0

    .line 143
    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    :cond_1
    return-object v0
.end method


# virtual methods
.method public visit([Lorg/codehaus/groovy/ast/ASTNode;Lorg/codehaus/groovy/control/SourceUnit;)V
    .locals 11

    .line 73
    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/transform/MemoizedASTTransformation;->init([Lorg/codehaus/groovy/ast/ASTNode;Lorg/codehaus/groovy/control/SourceUnit;)V

    const/4 v0, 0x0

    .line 74
    aget-object v1, p1, v0

    check-cast v1, Lorg/codehaus/groovy/ast/AnnotationNode;

    const/4 v2, 0x1

    .line 75
    aget-object p1, p1, v2

    check-cast p1, Lorg/codehaus/groovy/ast/AnnotatedNode;

    .line 76
    sget-object v2, Lorg/codehaus/groovy/transform/MemoizedASTTransformation;->MY_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/AnnotationNode;->getClassNode()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v3

    invoke-virtual {v2, v3}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_4

    instance-of v2, p1, Lorg/codehaus/groovy/ast/MethodNode;

    if-eqz v2, :cond_4

    .line 77
    check-cast p1, Lorg/codehaus/groovy/ast/MethodNode;

    .line 78
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->isAbstract()Z

    move-result v2

    const-string v3, "Annotation "

    if-eqz v2, :cond_0

    .line 79
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    sget-object v0, Lorg/codehaus/groovy/transform/MemoizedASTTransformation;->MY_TYPE_NAME:Ljava/lang/String;

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string v0, " cannot be used for abstract methods."

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p2, p1}, Lorg/codehaus/groovy/transform/MemoizedASTTransformation;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    return-void

    .line 82
    :cond_0
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->isVoidMethod()Z

    move-result v2

    if-eqz v2, :cond_1

    .line 83
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    sget-object v0, Lorg/codehaus/groovy/transform/MemoizedASTTransformation;->MY_TYPE_NAME:Ljava/lang/String;

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string v0, " cannot be used for void methods."

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p2, p1}, Lorg/codehaus/groovy/transform/MemoizedASTTransformation;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    return-void

    .line 87
    :cond_1
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getDeclaringClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v2

    .line 88
    invoke-static {p1, v2}, Lorg/codehaus/groovy/transform/MemoizedASTTransformation;->buildDelegatingMethod(Lorg/codehaus/groovy/ast/MethodNode;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/MethodNode;

    move-result-object v3

    .line 89
    invoke-static {v2, v3}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->addGeneratedMethod(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/MethodNode;)V

    const/16 v4, 0x12

    .line 92
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->isStatic()Z

    move-result v5

    if-eqz v5, :cond_2

    const/16 v4, 0x1a

    :cond_2
    move v7, v4

    const-string v4, "protectedCacheSize"

    .line 96
    invoke-virtual {p0, v1, v4}, Lorg/codehaus/groovy/transform/MemoizedASTTransformation;->getMemberIntValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;)I

    move-result v4

    const-string v5, "maxCacheSize"

    .line 97
    invoke-virtual {p0, v1, v5}, Lorg/codehaus/groovy/transform/MemoizedASTTransformation;->getMemberIntValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;)I

    move-result v1

    .line 99
    invoke-static {v3, v4, v1}, Lorg/codehaus/groovy/transform/MemoizedASTTransformation;->buildMemoizeClosureCallExpression(Lorg/codehaus/groovy/ast/MethodNode;II)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object v10

    const-string v1, "Closure"

    .line 101
    invoke-static {v2, v1, p1}, Lorg/codehaus/groovy/transform/MemoizedASTTransformation;->buildUniqueName(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/MethodNode;)Ljava/lang/String;

    move-result-object v6

    .line 102
    new-instance v1, Lorg/codehaus/groovy/ast/FieldNode;

    sget-object v3, Lorg/codehaus/groovy/ast/ClassHelper;->CLOSURE_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    .line 103
    invoke-static {v3}, Lorg/codehaus/groovy/ast/tools/GenericsUtils;->newClass(Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v8

    const/4 v9, 0x0

    move-object v5, v1

    invoke-direct/range {v5 .. v10}, Lorg/codehaus/groovy/ast/FieldNode;-><init>(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;)V

    .line 104
    invoke-virtual {v2, v1}, Lorg/codehaus/groovy/ast/ClassNode;->addField(Lorg/codehaus/groovy/ast/FieldNode;)V

    .line 106
    new-instance v3, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    invoke-direct {v3}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;-><init>()V

    .line 108
    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->fieldX(Lorg/codehaus/groovy/ast/FieldNode;)Lorg/codehaus/groovy/ast/expr/FieldExpression;

    move-result-object v1

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v4

    invoke-static {v4}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->args([Lorg/codehaus/groovy/ast/Parameter;)Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    move-result-object v4

    const-string v5, "call"

    .line 107
    invoke-static {v1, v5, v4}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object v1

    .line 109
    invoke-virtual {v1, v0}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setImplicitThis(Z)V

    .line 110
    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->returnS(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v0

    invoke-virtual {v3, v0}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 111
    invoke-virtual {p1, v3}, Lorg/codehaus/groovy/ast/MethodNode;->setCode(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 112
    new-instance p1, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;

    instance-of v0, v2, Lorg/codehaus/groovy/ast/InnerClassNode;

    invoke-direct {p1, p2, v0}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;-><init>(Lorg/codehaus/groovy/control/SourceUnit;Z)V

    if-eqz v0, :cond_3

    .line 114
    check-cast v2, Lorg/codehaus/groovy/ast/InnerClassNode;

    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/InnerClassNode;->getOuterMostClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p2

    invoke-virtual {p1, p2}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->visitClass(Lorg/codehaus/groovy/ast/ClassNode;)V

    goto :goto_0

    .line 116
    :cond_3
    invoke-virtual {p1, v2}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->visitClass(Lorg/codehaus/groovy/ast/ClassNode;)V

    :cond_4
    :goto_0
    return-void
.end method
