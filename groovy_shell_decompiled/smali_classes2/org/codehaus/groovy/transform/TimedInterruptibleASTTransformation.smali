.class public Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation;
.super Lorg/codehaus/groovy/transform/AbstractASTTransformation;
.source "TimedInterruptibleASTTransformation.groovy"

# interfaces
.implements Lgroovy/lang/GroovyObject;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;
    }
.end annotation

.annotation runtime Lorg/codehaus/groovy/transform/GroovyASTTransformation;
    phase = .enum Lorg/codehaus/groovy/control/CompilePhase;->CANONICALIZATION:Lorg/codehaus/groovy/control/CompilePhase;
.end annotation


# static fields
.field private static synthetic $callSiteArray:Ljava/lang/ref/SoftReference; = null

.field private static synthetic $staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo; = null

.field private static synthetic $staticClassInfo$:Lorg/codehaus/groovy/reflection/ClassInfo; = null

.field private static final APPLY_TO_ALL_CLASSES:Ljava/lang/String; = "applyToAllClasses"

.field private static final APPLY_TO_ALL_MEMBERS:Ljava/lang/String; = "applyToAllMembers"

.field private static final CHECK_METHOD_START_MEMBER:Ljava/lang/String; = "checkOnMethodStart"

.field private static final MY_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

.field private static final THROWN_EXCEPTION_TYPE:Ljava/lang/String; = "thrown"

.field public static transient synthetic __$stMC:Z


# instance fields
.field private transient synthetic metaClass:Lgroovy/lang/MetaClass;


# direct methods
.method private static synthetic $createCallSiteArray()Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;
    .locals 3

    const/16 v0, 0x38

    new-array v0, v0, [Ljava/lang/String;

    invoke-static {v0}, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation;->$createCallSiteArray_1([Ljava/lang/String;)V

    new-instance v1, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;

    const-class v2, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation;

    invoke-direct {v1, v2, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;-><init>(Ljava/lang/Class;[Ljava/lang/String;)V

    return-object v1
.end method

.method private static synthetic $createCallSiteArray_1([Ljava/lang/String;)V
    .locals 13

    const/4 v0, 0x0

    const-string v1, "init"

    aput-object v1, p0, v0

    const/4 v0, 0x1

    const-string v1, "getAt"

    aput-object v1, p0, v0

    const/4 v0, 0x2

    aput-object v1, p0, v0

    const/4 v0, 0x3

    const-string v2, "equals"

    aput-object v2, p0, v0

    const/4 v0, 0x4

    const-string v2, "classNode"

    aput-object v2, p0, v0

    const/4 v0, 0x5

    const-string v3, "internalError"

    aput-object v3, p0, v0

    const/4 v0, 0x6

    const-string v4, "name"

    aput-object v4, p0, v0

    const/4 v0, 0x7

    aput-object v2, p0, v0

    const/16 v0, 0x8

    const-string v2, "getConstantAnnotationParameter"

    aput-object v2, p0, v0

    const/16 v0, 0x9

    const-string v4, "TYPE"

    aput-object v4, p0, v0

    const/16 v0, 0xa

    aput-object v2, p0, v0

    const/16 v0, 0xb

    aput-object v4, p0, v0

    const/16 v0, 0xc

    aput-object v2, p0, v0

    const/16 v0, 0xd

    aput-object v4, p0, v0

    const/16 v0, 0xe

    aput-object v2, p0, v0

    const/16 v0, 0xf

    aput-object v4, p0, v0

    const/16 v0, 0x10

    const-string v2, "MAX_VALUE"

    aput-object v2, p0, v0

    const/16 v0, 0x11

    const-string v2, "getClassAnnotationParameter"

    aput-object v2, p0, v0

    const/16 v0, 0x12

    const-string v2, "make"

    aput-object v2, p0, v0

    const/16 v0, 0x13

    const-string v4, "getMember"

    aput-object v4, p0, v0

    const/16 v0, 0x14

    const-string v5, "propX"

    aput-object v5, p0, v0

    const/16 v0, 0x15

    const-string v5, "classX"

    aput-object v5, p0, v0

    const/16 v0, 0x16

    const-string v5, "each"

    aput-object v5, p0, v0

    const/16 v0, 0x17

    const-string v6, "classes"

    aput-object v6, p0, v0

    const/16 v0, 0x18

    const-string v7, "AST"

    aput-object v7, p0, v0

    const/16 v0, 0x19

    const-string v8, "<$constructor$>"

    aput-object v8, p0, v0

    const/16 v0, 0x1a

    const-string v9, "hashCode"

    aput-object v9, p0, v0

    const/16 v0, 0x1b

    const-string v10, "visitClass"

    aput-object v10, p0, v0

    const/16 v0, 0x1c

    aput-object v8, p0, v0

    const/16 v0, 0x1d

    aput-object v9, p0, v0

    const/16 v0, 0x1e

    const-string v11, "visitMethod"

    aput-object v11, p0, v0

    const/16 v0, 0x1f

    aput-object v10, p0, v0

    const/16 v0, 0x20

    const-string v11, "declaringClass"

    aput-object v11, p0, v0

    const/16 v0, 0x21

    aput-object v8, p0, v0

    const/16 v0, 0x22

    aput-object v9, p0, v0

    const/16 v0, 0x23

    const-string v12, "visitField"

    aput-object v12, p0, v0

    const/16 v0, 0x24

    aput-object v10, p0, v0

    const/16 v0, 0x25

    aput-object v11, p0, v0

    const/16 v0, 0x26

    aput-object v8, p0, v0

    const/16 v0, 0x27

    aput-object v9, p0, v0

    const/16 v0, 0x28

    const-string v9, "visitDeclarationExpression"

    aput-object v9, p0, v0

    const/16 v0, 0x29

    aput-object v10, p0, v0

    const/16 v0, 0x2a

    aput-object v11, p0, v0

    const/16 v0, 0x2b

    aput-object v5, p0, v0

    const/16 v0, 0x2c

    aput-object v6, p0, v0

    const/16 v0, 0x2d

    aput-object v7, p0, v0

    const/16 v0, 0x2e

    aput-object v4, p0, v0

    const/16 v0, 0x2f

    const-string v4, "asType"

    aput-object v4, p0, v0

    const/16 v0, 0x30

    const-string v4, "value"

    aput-object v4, p0, v0

    const/16 v0, 0x31

    aput-object v3, p0, v0

    const/16 v0, 0x32

    aput-object v3, p0, v0

    const/16 v0, 0x33

    aput-object v8, p0, v0

    const/16 v0, 0x34

    const-string v3, "length"

    aput-object v3, p0, v0

    const/16 v0, 0x35

    aput-object v1, p0, v0

    const/16 v0, 0x36

    aput-object v3, p0, v0

    const/16 v0, 0x37

    aput-object v2, p0, v0

    return-void
.end method

.method private static synthetic $getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;
    .locals 2

    sget-object v0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation;->$callSiteArray:Ljava/lang/ref/SoftReference;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Ljava/lang/ref/SoftReference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;

    if-nez v0, :cond_1

    :cond_0
    invoke-static {}, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation;->$createCallSiteArray()Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;

    move-result-object v0

    new-instance v1, Ljava/lang/ref/SoftReference;

    invoke-direct {v1, v0}, Ljava/lang/ref/SoftReference;-><init>(Ljava/lang/Object;)V

    sput-object v1, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation;->$callSiteArray:Ljava/lang/ref/SoftReference;

    :cond_1
    iget-object v0, v0, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;->array:[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    return-object v0
.end method

.method static constructor <clinit>()V
    .locals 3

    .line 72
    invoke-static {}, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v0

    const/16 v1, 0x37

    aget-object v0, v0, v1

    const-class v1, Lorg/codehaus/groovy/ast/ClassHelper;

    const-class v2, Lgroovy/transform/TimedInterrupt;

    invoke-interface {v0, v1, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    const-class v1, Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {v0, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/ast/ClassNode;

    sput-object v0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation;->MY_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    return-void
.end method

.method public constructor <init>()V
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    invoke-static {}, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    invoke-direct {p0}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;-><init>()V

    invoke-virtual {p0}, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation;->$getStaticMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation;->metaClass:Lgroovy/lang/MetaClass;

    return-void
.end method

.method public static getConstantAnnotationParameter(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 10

    const-class v0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation;

    invoke-static {}, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v1

    const/16 v2, 0x2e

    .line 135
    aget-object v2, v1, v2

    invoke-interface {v2, p0, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    .line 136
    invoke-static {p0}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_1

    .line 137
    instance-of v2, p0, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    const-string v3, ""

    const-string v4, " annotation parameter. Found "

    const-string v5, "Expecting boolean value for "

    const/4 v6, 0x1

    const/4 v7, 0x0

    const/4 v8, 0x2

    if-eqz v2, :cond_0

    const/16 v2, 0x2f

    .line 140
    :try_start_0
    aget-object v2, v1, v2

    const/16 v9, 0x30

    aget-object v9, v1, v9

    invoke-interface {v9, p0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v9

    invoke-interface {v2, v9, p2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    return-object p0

    :catchall_0
    move-exception p0

    goto :goto_0

    :catch_0
    const/16 p2, 0x31

    .line 142
    :try_start_1
    aget-object p2, v1, p2

    new-instance v1, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v2, v8, [Ljava/lang/Object;

    aput-object p1, v2, v7

    aput-object p0, v2, v6

    filled-new-array {v5, v4, v3}, [Ljava/lang/String;

    move-result-object p0

    invoke-direct {v1, v2, p0}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-interface {p2, v0, v1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    goto :goto_1

    .line 143
    :goto_0
    throw p0

    :cond_0
    const/16 p2, 0x32

    .line 145
    aget-object p2, v1, p2

    new-instance v1, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v2, v8, [Ljava/lang/Object;

    aput-object p1, v2, v7

    aput-object p0, v2, v6

    filled-new-array {v5, v4, v3}, [Ljava/lang/String;

    move-result-object p0

    invoke-direct {v1, v2, p0}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-interface {p2, v0, v1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_1
    :goto_1
    return-object p3
.end method

.method private static internalError(Ljava/lang/String;)V
    .locals 5

    invoke-static {}, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v0

    const/16 v1, 0x33

    .line 152
    aget-object v0, v0, v1

    const-class v1, Ljava/lang/RuntimeException;

    new-instance v2, Lorg/codehaus/groovy/runtime/GStringImpl;

    const/4 v3, 0x1

    new-array v3, v3, [Ljava/lang/Object;

    const/4 v4, 0x0

    aput-object p0, v3, v4

    const-string p0, "Internal error: "

    const-string v4, ""

    filled-new-array {p0, v4}, [Ljava/lang/String;

    move-result-object p0

    invoke-direct {v2, v3, p0}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-interface {v0, v1, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Ljava/lang/Throwable;

    throw p0
.end method


# virtual methods
.method protected synthetic $getStaticMetaClass()Lgroovy/lang/MetaClass;
    .locals 2

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    const-class v1, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation;

    if-eq v0, v1, :cond_0

    invoke-static {p0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->initMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0

    :cond_0
    sget-object v0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    if-nez v0, :cond_1

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getClassInfo(Ljava/lang/Class;)Lorg/codehaus/groovy/reflection/ClassInfo;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    :cond_1
    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0
.end method

.method public getMetaClass()Lgroovy/lang/MetaClass;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    .annotation runtime Lgroovy/transform/Internal;
    .end annotation

    .annotation runtime Lgroovyjarjaropenbeans/Transient;
    .end annotation

    iget-object v0, p0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation;->metaClass:Lgroovy/lang/MetaClass;

    if-eqz v0, :cond_0

    return-object v0

    :cond_0
    invoke-virtual {p0}, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation;->$getStaticMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation;->metaClass:Lgroovy/lang/MetaClass;

    return-object v0
.end method

.method public setMetaClass(Lgroovy/lang/MetaClass;)V
    .locals 0
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    .annotation runtime Lgroovy/transform/Internal;
    .end annotation

    iput-object p1, p0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation;->metaClass:Lgroovy/lang/MetaClass;

    return-void
.end method

.method public synthetic this$dist$get$2(Ljava/lang/String;)Ljava/lang/Object;
    .locals 4

    invoke-static {}, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    const-class v0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation;

    new-instance v1, Lorg/codehaus/groovy/runtime/GStringImpl;

    const/4 v2, 0x1

    new-array v2, v2, [Ljava/lang/Object;

    const/4 v3, 0x0

    aput-object p1, v2, v3

    const-string p1, ""

    filled-new-array {p1, p1}, [Ljava/lang/String;

    move-result-object p1

    invoke-direct {v1, v2, p1}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    move-object v1, p1

    check-cast v1, Ljava/lang/String;

    invoke-static {v0, p0, p1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->getGroovyObjectProperty(Ljava/lang/Class;Lgroovy/lang/GroovyObject;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public synthetic this$dist$invoke$2(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 7

    const-class v0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation;

    invoke-static {}, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v1

    instance-of v2, p2, [Ljava/lang/Object;

    const/4 v3, 0x1

    xor-int/2addr v2, v3

    const-string v4, ""

    const/4 v5, 0x0

    if-eqz v2, :cond_0

    new-instance v1, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v2, v3, [Ljava/lang/Object;

    aput-object p1, v2, v5

    filled-new-array {v4, v4}, [Ljava/lang/String;

    move-result-object p1

    invoke-direct {v1, v2, p1}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    move-object v1, p1

    check-cast v1, Ljava/lang/String;

    new-array v1, v3, [Ljava/lang/Object;

    aput-object p2, v1, v5

    invoke-static {v0, p0, p1, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodOnCurrentN(Ljava/lang/Class;Lgroovy/lang/GroovyObject;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1

    :cond_0
    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->isOrigInt()Z

    move-result v2

    if-eqz v2, :cond_1

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->isOrigZ()Z

    move-result v2

    if-eqz v2, :cond_1

    sget-boolean v2, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation;->__$stMC:Z

    if-nez v2, :cond_1

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->disabledStandardMetaClass()Z

    move-result v2

    if-nez v2, :cond_1

    const/16 v2, 0x36

    aget-object v1, v1, v2

    const-class v2, [Ljava/lang/Object;

    invoke-static {p2, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, [Ljava/lang/Object;

    invoke-interface {v1, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    invoke-static {v1, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_2

    new-instance v1, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v2, v3, [Ljava/lang/Object;

    aput-object p1, v2, v5

    filled-new-array {v4, v4}, [Ljava/lang/String;

    move-result-object p1

    invoke-direct {v1, v2, p1}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    move-object v1, p1

    check-cast v1, Ljava/lang/String;

    new-array v1, v3, [Ljava/lang/Object;

    const-class v2, [Ljava/lang/Object;

    invoke-static {p2, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, [Ljava/lang/Object;

    invoke-static {p2, v5}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->objectArrayGet([Ljava/lang/Object;I)Ljava/lang/Object;

    move-result-object p2

    aput-object p2, v1, v5

    invoke-static {v0, p0, p1, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodOnCurrentN(Ljava/lang/Class;Lgroovy/lang/GroovyObject;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1

    :cond_1
    const/16 v2, 0x34

    aget-object v2, v1, v2

    const-class v6, [Ljava/lang/Object;

    invoke-static {p2, v6}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v6

    check-cast v6, [Ljava/lang/Object;

    invoke-interface {v2, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v6

    invoke-static {v2, v6}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_2

    new-instance v2, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v6, v3, [Ljava/lang/Object;

    aput-object p1, v6, v5

    filled-new-array {v4, v4}, [Ljava/lang/String;

    move-result-object p1

    invoke-direct {v2, v6, p1}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    move-object v2, p1

    check-cast v2, Ljava/lang/String;

    new-array v2, v3, [Ljava/lang/Object;

    const/16 v3, 0x35

    aget-object v1, v1, v3

    const-class v3, [Ljava/lang/Object;

    invoke-static {p2, v3}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, [Ljava/lang/Object;

    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v3

    invoke-interface {v1, p2, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p2

    aput-object p2, v2, v5

    invoke-static {v0, p0, p1, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodOnCurrentN(Ljava/lang/Class;Lgroovy/lang/GroovyObject;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1

    :cond_2
    new-instance v1, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v2, v3, [Ljava/lang/Object;

    aput-object p1, v2, v5

    filled-new-array {v4, v4}, [Ljava/lang/String;

    move-result-object p1

    invoke-direct {v1, v2, p1}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    move-object v1, p1

    check-cast v1, Ljava/lang/String;

    new-array v1, v5, [Ljava/lang/Object;

    new-array v2, v3, [Ljava/lang/Object;

    aput-object p2, v2, v5

    new-array p2, v3, [I

    aput v5, p2, v5

    invoke-static {v1, v2, p2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->despreadList([Ljava/lang/Object;[Ljava/lang/Object;[I)[Ljava/lang/Object;

    move-result-object p2

    invoke-static {v0, p0, p1, p2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodOnCurrentN(Ljava/lang/Class;Lgroovy/lang/GroovyObject;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public synthetic this$dist$set$2(Ljava/lang/String;Ljava/lang/Object;)V
    .locals 4

    invoke-static {}, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    const-class v0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation;

    new-instance v1, Lorg/codehaus/groovy/runtime/GStringImpl;

    const/4 v2, 0x1

    new-array v2, v2, [Ljava/lang/Object;

    const/4 v3, 0x0

    aput-object p1, v2, v3

    const-string p1, ""

    filled-new-array {p1, p1}, [Ljava/lang/String;

    move-result-object p1

    invoke-direct {v1, v2, p1}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    move-object v1, p1

    check-cast v1, Ljava/lang/String;

    invoke-static {p2, v0, p0, p1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->setGroovyObjectProperty(Ljava/lang/Object;Ljava/lang/Class;Lgroovy/lang/GroovyObject;Ljava/lang/String;)V

    return-void
.end method

.method public visit([Lorg/codehaus/groovy/ast/ASTNode;Lorg/codehaus/groovy/control/SourceUnit;)V
    .locals 22

    move-object/from16 v0, p1

    new-instance v3, Lgroovy/lang/Reference;

    move-object/from16 v1, p2

    invoke-direct {v3, v1}, Lgroovy/lang/Reference;-><init>(Ljava/lang/Object;)V

    invoke-static {}, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v1

    const/4 v2, 0x0

    .line 80
    aget-object v4, v1, v2

    invoke-virtual {v3}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lorg/codehaus/groovy/control/SourceUnit;

    move-object/from16 v11, p0

    invoke-interface {v4, v11, v0, v5}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callCurrent(Lgroovy/lang/GroovyObject;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v10, Lgroovy/lang/Reference;

    const/4 v4, 0x0

    invoke-direct {v10, v4}, Lgroovy/lang/Reference;-><init>(Ljava/lang/Object;)V

    .line 81
    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->isOrigInt()Z

    move-result v4

    const/4 v5, 0x1

    .line 87
    invoke-static {v5}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v6

    if-eqz v4, :cond_0

    .line 81
    sget-boolean v4, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation;->__$stMC:Z

    if-nez v4, :cond_0

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->disabledStandardMetaClass()Z

    move-result v4

    if-nez v4, :cond_0

    invoke-static {v0, v2}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->objectArrayGet([Ljava/lang/Object;I)Ljava/lang/Object;

    move-result-object v4

    goto :goto_0

    :cond_0
    aget-object v4, v1, v5

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v7

    invoke-interface {v4, v0, v7}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    :goto_0
    const-class v7, Lorg/codehaus/groovy/ast/AnnotationNode;

    invoke-static {v4, v7}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lorg/codehaus/groovy/ast/AnnotationNode;

    move-object v7, v10

    check-cast v7, Lgroovy/lang/Reference;

    invoke-virtual {v10, v4}, Lgroovy/lang/Reference;->set(Ljava/lang/Object;)V

    .line 82
    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->isOrigInt()Z

    move-result v4

    if-eqz v4, :cond_1

    sget-boolean v4, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation;->__$stMC:Z

    if-nez v4, :cond_1

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->disabledStandardMetaClass()Z

    move-result v4

    if-nez v4, :cond_1

    invoke-static {v0, v5}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->objectArrayGet([Ljava/lang/Object;I)Ljava/lang/Object;

    move-result-object v0

    goto :goto_1

    :cond_1
    const/4 v4, 0x2

    aget-object v4, v1, v4

    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v7

    invoke-interface {v4, v0, v7}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    :goto_1
    const-class v4, Lorg/codehaus/groovy/ast/AnnotatedNode;

    invoke-static {v0, v4}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/ast/AnnotatedNode;

    const/4 v4, 0x3

    .line 83
    aget-object v4, v1, v4

    sget-object v7, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation;->MY_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    const/4 v8, 0x4

    aget-object v8, v1, v8

    invoke-virtual {v10}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v9

    check-cast v9, Lorg/codehaus/groovy/ast/AnnotationNode;

    invoke-interface {v8, v9}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v8

    invoke-interface {v4, v7, v8}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    invoke-static {v4}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v4

    xor-int/2addr v4, v5

    if-eqz v4, :cond_2

    const/4 v4, 0x5

    .line 84
    aget-object v4, v1, v4

    const-class v7, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation;

    new-instance v8, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v9, v5, [Ljava/lang/Object;

    const/4 v12, 0x6

    aget-object v12, v1, v12

    const/4 v13, 0x7

    aget-object v13, v1, v13

    invoke-virtual {v10}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v14

    check-cast v14, Lorg/codehaus/groovy/ast/AnnotationNode;

    invoke-interface {v13, v14}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v13

    invoke-interface {v12, v13}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v12

    aput-object v12, v9, v2

    const-string v12, "Transformation called from wrong annotation: "

    const-string v13, ""

    filled-new-array {v12, v13}, [Ljava/lang/String;

    move-result-object v12

    invoke-direct {v8, v9, v12}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-interface {v4, v7, v8}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_2
    const/16 v4, 0x8

    .line 87
    aget-object v12, v1, v4

    const-class v13, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation;

    invoke-virtual {v10}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v4

    move-object v14, v4

    check-cast v14, Lorg/codehaus/groovy/ast/AnnotationNode;

    sget-object v15, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation;->CHECK_METHOD_START_MEMBER:Ljava/lang/String;

    const/16 v4, 0x9

    aget-object v4, v1, v4

    const-class v7, Ljava/lang/Boolean;

    invoke-interface {v4, v7}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v16

    move-object/from16 v17, v6

    invoke-interface/range {v12 .. v17}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    new-instance v7, Lgroovy/lang/Reference;

    invoke-direct {v7, v4}, Lgroovy/lang/Reference;-><init>(Ljava/lang/Object;)V

    const/16 v4, 0xa

    .line 88
    aget-object v12, v1, v4

    const-class v13, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation;

    invoke-virtual {v10}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v4

    move-object v14, v4

    check-cast v14, Lorg/codehaus/groovy/ast/AnnotationNode;

    sget-object v15, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation;->APPLY_TO_ALL_MEMBERS:Ljava/lang/String;

    const/16 v4, 0xb

    aget-object v4, v1, v4

    const-class v8, Ljava/lang/Boolean;

    invoke-interface {v4, v8}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v16

    invoke-interface/range {v12 .. v17}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    new-instance v8, Lgroovy/lang/Reference;

    invoke-direct {v8, v4}, Lgroovy/lang/Reference;-><init>(Ljava/lang/Object;)V

    .line 89
    invoke-virtual {v8}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v4

    invoke-static {v4}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v4

    if-eqz v4, :cond_3

    const/16 v4, 0xc

    aget-object v12, v1, v4

    const-class v13, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation;

    invoke-virtual {v10}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v4

    move-object v14, v4

    check-cast v14, Lorg/codehaus/groovy/ast/AnnotationNode;

    sget-object v15, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation;->APPLY_TO_ALL_CLASSES:Ljava/lang/String;

    const/16 v4, 0xd

    aget-object v4, v1, v4

    const-class v9, Ljava/lang/Boolean;

    invoke-interface {v4, v9}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v16

    move-object/from16 v17, v6

    invoke-interface/range {v12 .. v17}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    goto :goto_2

    :cond_3
    invoke-static {v2}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v4

    :goto_2
    new-instance v6, Lgroovy/lang/Reference;

    invoke-direct {v6, v4}, Lgroovy/lang/Reference;-><init>(Ljava/lang/Object;)V

    const/16 v4, 0xe

    .line 90
    aget-object v12, v1, v4

    const-class v13, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation;

    invoke-virtual {v10}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v4

    move-object v14, v4

    check-cast v14, Lorg/codehaus/groovy/ast/AnnotationNode;

    const/16 v4, 0xf

    aget-object v4, v1, v4

    const-class v9, Ljava/lang/Long;

    invoke-interface {v4, v9}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v16

    const/16 v4, 0x10

    aget-object v4, v1, v4

    const-class v9, Ljava/lang/Long;

    invoke-interface {v4, v9}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v17

    const-string v15, "value"

    invoke-interface/range {v12 .. v17}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    new-instance v9, Lgroovy/lang/Reference;

    invoke-direct {v9, v4}, Lgroovy/lang/Reference;-><init>(Ljava/lang/Object;)V

    const/16 v4, 0x11

    .line 91
    aget-object v4, v1, v4

    const-class v12, Lorg/codehaus/groovy/transform/AbstractInterruptibleASTTransformation;

    invoke-virtual {v10}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v13

    check-cast v13, Lorg/codehaus/groovy/ast/AnnotationNode;

    sget-object v14, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation;->THROWN_EXCEPTION_TYPE:Ljava/lang/String;

    const/16 v15, 0x12

    aget-object v15, v1, v15

    const-class v2, Lorg/codehaus/groovy/ast/ClassHelper;

    const-class v5, Ljava/util/concurrent/TimeoutException;

    invoke-interface {v15, v2, v5}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-interface {v4, v12, v13, v14, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    new-instance v12, Lgroovy/lang/Reference;

    invoke-direct {v12, v2}, Lgroovy/lang/Reference;-><init>(Ljava/lang/Object;)V

    const/16 v2, 0x13

    .line 93
    aget-object v2, v1, v2

    invoke-virtual {v10}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lorg/codehaus/groovy/ast/AnnotationNode;

    const-string v5, "unit"

    invoke-interface {v2, v4, v5}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v4

    if-eqz v4, :cond_4

    goto :goto_3

    :cond_4
    const/16 v2, 0x14

    aget-object v2, v1, v2

    const-class v4, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v5, 0x15

    aget-object v5, v1, v5

    const-class v13, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const-class v14, Ljava/util/concurrent/TimeUnit;

    invoke-interface {v5, v13, v14}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    const-string v13, "SECONDS"

    invoke-interface {v2, v4, v5, v13}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    :goto_3
    const-class v4, Lorg/codehaus/groovy/ast/expr/Expression;

    invoke-static {v2, v4}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lorg/codehaus/groovy/ast/expr/Expression;

    new-instance v13, Lgroovy/lang/Reference;

    invoke-direct {v13, v2}, Lgroovy/lang/Reference;-><init>(Ljava/lang/Object;)V

    .line 97
    invoke-virtual {v6}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v2

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_5

    const/16 v0, 0x16

    .line 99
    aget-object v14, v1, v0

    const/16 v0, 0x17

    aget-object v0, v1, v0

    const/16 v2, 0x18

    aget-object v1, v1, v2

    invoke-virtual {v3}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lorg/codehaus/groovy/control/SourceUnit;

    invoke-interface {v1, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-interface {v0, v1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetPropertySafe(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v15

    new-instance v5, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$_visit_closure1;

    move-object v0, v5

    move-object/from16 v1, p0

    move-object/from16 v2, p0

    move-object v4, v7

    move-object v7, v5

    move-object v5, v6

    move-object v6, v8

    move-object v8, v7

    move-object v7, v9

    move-object v9, v8

    move-object v8, v13

    move-object v13, v9

    move-object v9, v12

    invoke-direct/range {v0 .. v10}, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$_visit_closure1;-><init>(Ljava/lang/Object;Ljava/lang/Object;Lgroovy/lang/Reference;Lgroovy/lang/Reference;Lgroovy/lang/Reference;Lgroovy/lang/Reference;Lgroovy/lang/Reference;Lgroovy/lang/Reference;Lgroovy/lang/Reference;Lgroovy/lang/Reference;)V

    invoke-interface {v14, v15, v13}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callSafe(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto/16 :goto_7

    .line 103
    :cond_5
    instance-of v2, v0, Lorg/codehaus/groovy/ast/ClassNode;

    if-eqz v2, :cond_6

    const/16 v2, 0x19

    .line 105
    aget-object v2, v1, v2

    const-class v4, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;

    invoke-virtual {v3}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v3

    move-object v14, v3

    check-cast v14, Lorg/codehaus/groovy/control/SourceUnit;

    invoke-virtual {v7}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v15

    invoke-virtual {v6}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v16

    invoke-virtual {v8}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v17

    invoke-virtual {v9}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v18

    invoke-virtual {v13}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v3

    move-object/from16 v19, v3

    check-cast v19, Lorg/codehaus/groovy/ast/expr/Expression;

    invoke-virtual {v12}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v20

    const/16 v3, 0x1a

    aget-object v3, v1, v3

    invoke-virtual {v10}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lorg/codehaus/groovy/ast/AnnotationNode;

    invoke-interface {v3, v5}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v21

    invoke-static/range {v14 .. v21}, Lorg/codehaus/groovy/runtime/ArrayUtil;->createArray(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object v3

    invoke-interface {v2, v4, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    const/16 v3, 0x1b

    .line 106
    aget-object v1, v1, v3

    invoke-interface {v1, v2, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto/16 :goto_7

    .line 107
    :cond_6
    invoke-virtual {v8}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v2

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v2

    const/4 v4, 0x1

    xor-int/2addr v2, v4

    if-eqz v2, :cond_7

    instance-of v2, v0, Lorg/codehaus/groovy/ast/MethodNode;

    if-eqz v2, :cond_7

    const/4 v2, 0x1

    goto :goto_4

    :cond_7
    const/4 v2, 0x0

    :goto_4
    if-eqz v2, :cond_8

    const/16 v2, 0x1c

    .line 109
    aget-object v2, v1, v2

    const-class v4, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;

    invoke-virtual {v3}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v3

    move-object v14, v3

    check-cast v14, Lorg/codehaus/groovy/control/SourceUnit;

    invoke-virtual {v7}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v15

    invoke-virtual {v6}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v16

    invoke-virtual {v8}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v17

    invoke-virtual {v9}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v18

    invoke-virtual {v13}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v3

    move-object/from16 v19, v3

    check-cast v19, Lorg/codehaus/groovy/ast/expr/Expression;

    invoke-virtual {v12}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v20

    const/16 v3, 0x1d

    aget-object v3, v1, v3

    invoke-virtual {v10}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lorg/codehaus/groovy/ast/AnnotationNode;

    invoke-interface {v3, v5}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v21

    invoke-static/range {v14 .. v21}, Lorg/codehaus/groovy/runtime/ArrayUtil;->createArray(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object v3

    invoke-interface {v2, v4, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    const/16 v3, 0x1e

    .line 110
    aget-object v3, v1, v3

    invoke-interface {v3, v2, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v3, 0x1f

    .line 111
    aget-object v3, v1, v3

    const/16 v4, 0x20

    aget-object v1, v1, v4

    invoke-interface {v1, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    invoke-interface {v3, v2, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto/16 :goto_7

    .line 112
    :cond_8
    invoke-virtual {v8}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v2

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v2

    const/4 v4, 0x1

    xor-int/2addr v2, v4

    if-eqz v2, :cond_9

    instance-of v2, v0, Lorg/codehaus/groovy/ast/FieldNode;

    if-eqz v2, :cond_9

    const/4 v4, 0x1

    goto :goto_5

    :cond_9
    const/4 v4, 0x0

    :goto_5
    if-eqz v4, :cond_a

    const/16 v2, 0x21

    .line 114
    aget-object v2, v1, v2

    const-class v4, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;

    invoke-virtual {v3}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v3

    move-object v14, v3

    check-cast v14, Lorg/codehaus/groovy/control/SourceUnit;

    invoke-virtual {v7}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v15

    invoke-virtual {v6}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v16

    invoke-virtual {v8}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v17

    invoke-virtual {v9}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v18

    invoke-virtual {v13}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v3

    move-object/from16 v19, v3

    check-cast v19, Lorg/codehaus/groovy/ast/expr/Expression;

    invoke-virtual {v12}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v20

    const/16 v3, 0x22

    aget-object v3, v1, v3

    invoke-virtual {v10}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lorg/codehaus/groovy/ast/AnnotationNode;

    invoke-interface {v3, v5}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v21

    invoke-static/range {v14 .. v21}, Lorg/codehaus/groovy/runtime/ArrayUtil;->createArray(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object v3

    invoke-interface {v2, v4, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    const/16 v3, 0x23

    .line 115
    aget-object v3, v1, v3

    invoke-interface {v3, v2, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v3, 0x24

    .line 116
    aget-object v3, v1, v3

    const/16 v4, 0x25

    aget-object v1, v1, v4

    invoke-interface {v1, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    invoke-interface {v3, v2, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto/16 :goto_7

    .line 117
    :cond_a
    invoke-virtual {v8}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v2

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v2

    const/4 v4, 0x1

    xor-int/2addr v2, v4

    if-eqz v2, :cond_b

    instance-of v2, v0, Lorg/codehaus/groovy/ast/expr/DeclarationExpression;

    if-eqz v2, :cond_b

    move v2, v4

    goto :goto_6

    :cond_b
    const/4 v2, 0x0

    :goto_6
    if-eqz v2, :cond_c

    const/16 v2, 0x26

    .line 119
    aget-object v2, v1, v2

    const-class v4, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;

    invoke-virtual {v3}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v3

    move-object v14, v3

    check-cast v14, Lorg/codehaus/groovy/control/SourceUnit;

    invoke-virtual {v7}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v15

    invoke-virtual {v6}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v16

    invoke-virtual {v8}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v17

    invoke-virtual {v9}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v18

    invoke-virtual {v13}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v3

    move-object/from16 v19, v3

    check-cast v19, Lorg/codehaus/groovy/ast/expr/Expression;

    invoke-virtual {v12}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v20

    const/16 v3, 0x27

    aget-object v3, v1, v3

    invoke-virtual {v10}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lorg/codehaus/groovy/ast/AnnotationNode;

    invoke-interface {v3, v5}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v21

    invoke-static/range {v14 .. v21}, Lorg/codehaus/groovy/runtime/ArrayUtil;->createArray(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object v3

    invoke-interface {v2, v4, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    const/16 v3, 0x28

    .line 120
    aget-object v3, v1, v3

    invoke-interface {v3, v2, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v3, 0x29

    .line 121
    aget-object v3, v1, v3

    const/16 v4, 0x2a

    aget-object v1, v1, v4

    invoke-interface {v1, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    invoke-interface {v3, v2, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_7

    :cond_c
    const/16 v0, 0x2b

    .line 124
    aget-object v14, v1, v0

    const/16 v0, 0x2c

    aget-object v0, v1, v0

    const/16 v2, 0x2d

    aget-object v1, v1, v2

    invoke-virtual {v3}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lorg/codehaus/groovy/control/SourceUnit;

    invoke-interface {v1, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-interface {v0, v1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetPropertySafe(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v15

    new-instance v5, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$_visit_closure2;

    move-object v0, v5

    move-object/from16 v1, p0

    move-object/from16 v2, p0

    move-object v4, v7

    move-object v7, v5

    move-object v5, v6

    move-object v6, v8

    move-object v8, v7

    move-object v7, v9

    move-object v9, v8

    move-object v8, v13

    move-object v13, v9

    move-object v9, v12

    invoke-direct/range {v0 .. v10}, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$_visit_closure2;-><init>(Ljava/lang/Object;Ljava/lang/Object;Lgroovy/lang/Reference;Lgroovy/lang/Reference;Lgroovy/lang/Reference;Lgroovy/lang/Reference;Lgroovy/lang/Reference;Lgroovy/lang/Reference;Lgroovy/lang/Reference;Lgroovy/lang/Reference;)V

    invoke-interface {v14, v15, v13}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callSafe(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :goto_7
    return-void
.end method
