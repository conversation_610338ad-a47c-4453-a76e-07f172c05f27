.class Lorg/codehaus/groovy/transform/LogASTTransformation$1;
.super Lorg/codehaus/groovy/ast/ClassCodeExpressionTransformer;
.source "LogASTTransformation.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/codehaus/groovy/transform/LogASTTransformation;->visit([Lorg/codehaus/groovy/ast/ASTNode;Lorg/codehaus/groovy/control/SourceUnit;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field private logNode:Lorg/codehaus/groovy/ast/FieldNode;

.field final synthetic this$0:Lorg/codehaus/groovy/transform/LogASTTransformation;

.field final synthetic val$categoryName:Ljava/lang/String;

.field final synthetic val$logFieldModifiers:I

.field final synthetic val$logFieldName:Ljava/lang/String;

.field final synthetic val$loggingStrategy:Lorg/codehaus/groovy/transform/LogASTTransformation$LoggingStrategy;

.field final synthetic val$sourceUnit:Lorg/codehaus/groovy/control/SourceUnit;


# direct methods
.method constructor <init>(Lorg/codehaus/groovy/transform/LogASTTransformation;Lorg/codehaus/groovy/control/SourceUnit;Ljava/lang/String;Lorg/codehaus/groovy/transform/LogASTTransformation$LoggingStrategy;Ljava/lang/String;I)V
    .locals 0

    .line 96
    iput-object p1, p0, Lorg/codehaus/groovy/transform/LogASTTransformation$1;->this$0:Lorg/codehaus/groovy/transform/LogASTTransformation;

    iput-object p2, p0, Lorg/codehaus/groovy/transform/LogASTTransformation$1;->val$sourceUnit:Lorg/codehaus/groovy/control/SourceUnit;

    iput-object p3, p0, Lorg/codehaus/groovy/transform/LogASTTransformation$1;->val$logFieldName:Ljava/lang/String;

    iput-object p4, p0, Lorg/codehaus/groovy/transform/LogASTTransformation$1;->val$loggingStrategy:Lorg/codehaus/groovy/transform/LogASTTransformation$LoggingStrategy;

    iput-object p5, p0, Lorg/codehaus/groovy/transform/LogASTTransformation$1;->val$categoryName:Ljava/lang/String;

    iput p6, p0, Lorg/codehaus/groovy/transform/LogASTTransformation$1;->val$logFieldModifiers:I

    invoke-direct {p0}, Lorg/codehaus/groovy/ast/ClassCodeExpressionTransformer;-><init>()V

    return-void
.end method

.method private addGuard(Lorg/codehaus/groovy/ast/expr/MethodCallExpression;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 4

    .line 150
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->getObjectExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    instance-of v0, v0, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    const/4 v1, 0x0

    if-nez v0, :cond_0

    return-object v1

    .line 153
    :cond_0
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->getObjectExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    .line 154
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->getName()Ljava/lang/String;

    move-result-object v2

    iget-object v3, p0, Lorg/codehaus/groovy/transform/LogASTTransformation$1;->val$logFieldName:Ljava/lang/String;

    invoke-virtual {v2, v3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_5

    .line 155
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->getAccessedVariable()Lorg/codehaus/groovy/ast/Variable;

    move-result-object v2

    instance-of v2, v2, Lorg/codehaus/groovy/ast/DynamicVariable;

    if-nez v2, :cond_1

    goto :goto_0

    .line 159
    :cond_1
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->getMethodAsString()Ljava/lang/String;

    move-result-object v2

    if-nez v2, :cond_2

    return-object v1

    .line 161
    :cond_2
    iget-object v3, p0, Lorg/codehaus/groovy/transform/LogASTTransformation$1;->val$loggingStrategy:Lorg/codehaus/groovy/transform/LogASTTransformation$LoggingStrategy;

    invoke-interface {v3, v2}, Lorg/codehaus/groovy/transform/LogASTTransformation$LoggingStrategy;->isLoggingMethod(Ljava/lang/String;)Z

    move-result v3

    if-nez v3, :cond_3

    return-object v1

    .line 164
    :cond_3
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/transform/LogASTTransformation$1;->usesSimpleMethodArgumentsOnly(Lorg/codehaus/groovy/ast/expr/MethodCallExpression;)Z

    move-result v3

    if-eqz v3, :cond_4

    return-object v1

    .line 166
    :cond_4
    iget-object v1, p0, Lorg/codehaus/groovy/transform/LogASTTransformation$1;->logNode:Lorg/codehaus/groovy/ast/FieldNode;

    invoke-virtual {v0, v1}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->setAccessedVariable(Lorg/codehaus/groovy/ast/Variable;)V

    .line 167
    iget-object v1, p0, Lorg/codehaus/groovy/transform/LogASTTransformation$1;->val$loggingStrategy:Lorg/codehaus/groovy/transform/LogASTTransformation$LoggingStrategy;

    invoke-interface {v1, v0, v2, p1}, Lorg/codehaus/groovy/transform/LogASTTransformation$LoggingStrategy;->wrapLoggingMethodCall(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    return-object p1

    :cond_5
    :goto_0
    return-object v1
.end method

.method private isSimpleExpression(Lorg/codehaus/groovy/ast/expr/Expression;)Z
    .locals 2

    .line 183
    instance-of v0, p1, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    const/4 v1, 0x1

    if-eqz v0, :cond_0

    return v1

    .line 184
    :cond_0
    instance-of p1, p1, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    if-eqz p1, :cond_1

    return v1

    :cond_1
    const/4 p1, 0x0

    return p1
.end method

.method private transformClosureExpression(Lorg/codehaus/groovy/ast/expr/ClosureExpression;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 1

    .line 136
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/ClosureExpression;->getCode()Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v0

    instance-of v0, v0, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    if-eqz v0, :cond_0

    .line 137
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/ClosureExpression;->getCode()Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    .line 138
    invoke-super {p0, v0}, Lorg/codehaus/groovy/ast/ClassCodeExpressionTransformer;->visitBlockStatement(Lorg/codehaus/groovy/ast/stmt/BlockStatement;)V

    :cond_0
    return-object p1
.end method

.method private transformMethodCallExpression(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 1

    .line 144
    move-object v0, p1

    check-cast v0, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    invoke-direct {p0, v0}, Lorg/codehaus/groovy/transform/LogASTTransformation$1;->addGuard(Lorg/codehaus/groovy/ast/expr/MethodCallExpression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    if-nez v0, :cond_0

    .line 145
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeExpressionTransformer;->transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    :cond_0
    return-object v0
.end method

.method private usesSimpleMethodArgumentsOnly(Lorg/codehaus/groovy/ast/expr/MethodCallExpression;)Z
    .locals 2

    .line 171
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->getArguments()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    .line 172
    instance-of v0, p1, Lorg/codehaus/groovy/ast/expr/TupleExpression;

    const/4 v1, 0x1

    if-eqz v0, :cond_2

    .line 173
    check-cast p1, Lorg/codehaus/groovy/ast/expr/TupleExpression;

    .line 174
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/TupleExpression;->getExpressions()Ljava/util/List;

    move-result-object p1

    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_1

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/ast/expr/Expression;

    .line 175
    invoke-direct {p0, v0}, Lorg/codehaus/groovy/transform/LogASTTransformation$1;->isSimpleExpression(Lorg/codehaus/groovy/ast/expr/Expression;)Z

    move-result v0

    if-nez v0, :cond_0

    const/4 p1, 0x0

    return p1

    :cond_1
    return v1

    .line 179
    :cond_2
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/transform/LogASTTransformation$1;->isSimpleExpression(Lorg/codehaus/groovy/ast/expr/Expression;)Z

    move-result p1

    xor-int/2addr p1, v1

    return p1
.end method


# virtual methods
.method protected getSourceUnit()Lorg/codehaus/groovy/control/SourceUnit;
    .locals 1

    .line 101
    iget-object v0, p0, Lorg/codehaus/groovy/transform/LogASTTransformation$1;->val$sourceUnit:Lorg/codehaus/groovy/control/SourceUnit;

    return-object v0
.end method

.method public transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 1

    if-nez p1, :cond_0

    const/4 p1, 0x0

    return-object p1

    .line 107
    :cond_0
    instance-of v0, p1, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    if-eqz v0, :cond_1

    .line 108
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/transform/LogASTTransformation$1;->transformMethodCallExpression(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    return-object p1

    .line 110
    :cond_1
    instance-of v0, p1, Lorg/codehaus/groovy/ast/expr/ClosureExpression;

    if-eqz v0, :cond_2

    .line 111
    check-cast p1, Lorg/codehaus/groovy/ast/expr/ClosureExpression;

    invoke-direct {p0, p1}, Lorg/codehaus/groovy/transform/LogASTTransformation$1;->transformClosureExpression(Lorg/codehaus/groovy/ast/expr/ClosureExpression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    return-object p1

    .line 113
    :cond_2
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeExpressionTransformer;->transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    return-object p1
.end method

.method public visitClass(Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 4

    .line 118
    iget-object v0, p0, Lorg/codehaus/groovy/transform/LogASTTransformation$1;->val$logFieldName:Ljava/lang/String;

    invoke-virtual {p1, v0}, Lorg/codehaus/groovy/ast/ClassNode;->getField(Ljava/lang/String;)Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 119
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/FieldNode;->getOwner()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    invoke-virtual {v1, p1}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_0

    const-string v1, "Class annotated with Log annotation cannot have log field declared"

    .line 120
    invoke-virtual {p0, v1, v0}, Lorg/codehaus/groovy/transform/LogASTTransformation$1;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    goto :goto_0

    :cond_0
    if-eqz v0, :cond_1

    .line 121
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/FieldNode;->getModifiers()I

    move-result v1

    invoke-static {v1}, Ljava/lang/reflect/Modifier;->isPrivate(I)Z

    move-result v1

    if-nez v1, :cond_1

    .line 122
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "Class annotated with Log annotation cannot have log field declared because the field exists in the parent class: "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/FieldNode;->getOwner()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v2

    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0, v1, v0}, Lorg/codehaus/groovy/transform/LogASTTransformation$1;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    goto :goto_0

    .line 124
    :cond_1
    iget-object v0, p0, Lorg/codehaus/groovy/transform/LogASTTransformation$1;->val$loggingStrategy:Lorg/codehaus/groovy/transform/LogASTTransformation$LoggingStrategy;

    instance-of v1, v0, Lorg/codehaus/groovy/transform/LogASTTransformation$LoggingStrategyV2;

    if-eqz v1, :cond_2

    .line 125
    check-cast v0, Lorg/codehaus/groovy/transform/LogASTTransformation$LoggingStrategyV2;

    .line 126
    iget-object v1, p0, Lorg/codehaus/groovy/transform/LogASTTransformation$1;->val$logFieldName:Ljava/lang/String;

    iget-object v2, p0, Lorg/codehaus/groovy/transform/LogASTTransformation$1;->val$categoryName:Ljava/lang/String;

    iget v3, p0, Lorg/codehaus/groovy/transform/LogASTTransformation$1;->val$logFieldModifiers:I

    invoke-interface {v0, p1, v1, v2, v3}, Lorg/codehaus/groovy/transform/LogASTTransformation$LoggingStrategyV2;->addLoggerFieldToClass(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Ljava/lang/String;I)Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/transform/LogASTTransformation$1;->logNode:Lorg/codehaus/groovy/ast/FieldNode;

    goto :goto_0

    .line 129
    :cond_2
    iget-object v1, p0, Lorg/codehaus/groovy/transform/LogASTTransformation$1;->val$logFieldName:Ljava/lang/String;

    iget-object v2, p0, Lorg/codehaus/groovy/transform/LogASTTransformation$1;->val$categoryName:Ljava/lang/String;

    invoke-interface {v0, p1, v1, v2}, Lorg/codehaus/groovy/transform/LogASTTransformation$LoggingStrategy;->addLoggerFieldToClass(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Ljava/lang/String;)Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/transform/LogASTTransformation$1;->logNode:Lorg/codehaus/groovy/ast/FieldNode;

    .line 132
    :goto_0
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeExpressionTransformer;->visitClass(Lorg/codehaus/groovy/ast/ClassNode;)V

    return-void
.end method
