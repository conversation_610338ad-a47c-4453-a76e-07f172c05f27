.class public Lorg/codehaus/groovy/transform/LogASTTransformation;
.super Lorg/codehaus/groovy/transform/AbstractASTTransformation;
.source "LogASTTransformation.java"

# interfaces
.implements Lgroovy/transform/CompilationUnitAware;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/codehaus/groovy/transform/LogASTTransformation$LoggingStrategy;,
        Lorg/codehaus/groovy/transform/LogASTTransformation$LoggingStrategyV2;,
        Lorg/codehaus/groovy/transform/LogASTTransformation$AbstractLoggingStrategy;,
        Lorg/codehaus/groovy/transform/LogASTTransformation$AbstractLoggingStrategyV2;
    }
.end annotation

.annotation runtime Lorg/codehaus/groovy/transform/GroovyASTTransformation;
    phase = .enum Lorg/codehaus/groovy/control/CompilePhase;->SEMANTIC_ANALYSIS:Lorg/codehaus/groovy/control/CompilePhase;
.end annotation


# static fields
.field public static final DEFAULT_ACCESS_MODIFIER:Ljava/lang/String; = "private"

.field public static final DEFAULT_CATEGORY_NAME:Ljava/lang/String; = "##default-category-name##"


# instance fields
.field private compilationUnit:Lorg/codehaus/groovy/control/CompilationUnit;


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 59
    invoke-direct {p0}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;-><init>()V

    return-void
.end method

.method private static createLoggingStrategy(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/ClassLoader;Ljava/lang/ClassLoader;)Lorg/codehaus/groovy/transform/LogASTTransformation$LoggingStrategy;
    .locals 4

    .line 218
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/AnnotationNode;->getClassNode()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p0

    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object p0

    const/4 v0, 0x0

    .line 222
    :try_start_0
    invoke-static {p0, v0, p2}, Ljava/lang/Class;->forName(Ljava/lang/String;ZLjava/lang/ClassLoader;)Ljava/lang/Class;

    move-result-object p2
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_2

    :try_start_1
    const-string v1, "loggingStrategy"

    const/4 v2, 0x0

    .line 229
    move-object v3, v2

    check-cast v3, [Ljava/lang/Class;

    invoke-virtual {p2, v1, v2}, Ljava/lang/Class;->getDeclaredMethod(Ljava/lang/String;[Ljava/lang/Class;)Ljava/lang/reflect/Method;

    move-result-object p2
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_1

    .line 236
    :try_start_2
    invoke-virtual {p2}, Ljava/lang/reflect/Method;->getDefaultValue()Ljava/lang/Object;

    move-result-object p2
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    .line 241
    const-class v1, Lorg/codehaus/groovy/transform/LogASTTransformation$LoggingStrategy;

    move-object v3, p2

    check-cast v3, Ljava/lang/Class;

    invoke-virtual {v1, v3}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    move-result v1

    if-nez v1, :cond_1

    const-class v1, Lorg/codehaus/groovy/transform/LogASTTransformation$LoggingStrategyV2;

    .line 242
    invoke-virtual {v1, v3}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    move-result v1

    if-eqz v1, :cond_0

    goto :goto_0

    .line 243
    :cond_0
    new-instance p1, Ljava/lang/RuntimeException;

    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "Default loggingStrategy value on class named "

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    const-string p2, " is not a LoggingStrategy"

    invoke-virtual {p0, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    invoke-direct {p1, p0}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/String;)V

    throw p1

    :cond_1
    :goto_0
    const/4 p0, 0x1

    .line 248
    :try_start_3
    move-object v1, p2

    check-cast v1, Ljava/lang/Class;

    .line 249
    const-class v3, Lorg/codehaus/groovy/transform/LogASTTransformation$AbstractLoggingStrategy;

    invoke-virtual {v3, v1}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    move-result v3

    if-eqz v3, :cond_2

    new-array v3, p0, [Ljava/lang/Object;

    aput-object p1, v3, v0

    .line 250
    invoke-static {v1, v3}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->newInstance(Ljava/lang/Class;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/transform/LogASTTransformation$LoggingStrategy;

    return-object v1

    :cond_2
    new-array v3, v0, [Ljava/lang/Class;

    .line 252
    invoke-virtual {v1, v3}, Ljava/lang/Class;->getDeclaredConstructor([Ljava/lang/Class;)Ljava/lang/reflect/Constructor;

    move-result-object v1

    new-array v3, v0, [Ljava/lang/Object;

    invoke-virtual {v1, v3}, Ljava/lang/reflect/Constructor;->newInstance([Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/transform/LogASTTransformation$LoggingStrategy;
    :try_end_3
    .catch Ljava/lang/Exception; {:try_start_3 .. :try_end_3} :catch_0

    return-object v1

    .line 259
    :catch_0
    :try_start_4
    check-cast p2, Ljava/lang/Class;

    .line 260
    const-class v1, Lorg/codehaus/groovy/transform/LogASTTransformation$AbstractLoggingStrategy;

    invoke-virtual {v1, p2}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    move-result v1

    if-eqz v1, :cond_3

    new-array p0, p0, [Ljava/lang/Object;

    aput-object p1, p0, v0

    .line 261
    invoke-static {p2, p0}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->newInstance(Ljava/lang/Class;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Lorg/codehaus/groovy/transform/LogASTTransformation$LoggingStrategy;

    return-object p0

    :cond_3
    new-array p0, v0, [Ljava/lang/Class;

    .line 263
    invoke-virtual {p2, p0}, Ljava/lang/Class;->getDeclaredConstructor([Ljava/lang/Class;)Ljava/lang/reflect/Constructor;

    move-result-object p0

    new-array p1, v0, [Ljava/lang/Object;

    invoke-virtual {p0, p1}, Ljava/lang/reflect/Constructor;->newInstance([Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Lorg/codehaus/groovy/transform/LogASTTransformation$LoggingStrategy;
    :try_end_4
    .catch Ljava/lang/Exception; {:try_start_4 .. :try_end_4} :catch_1

    return-object p0

    :catch_1
    return-object v2

    .line 238
    :catchall_0
    new-instance p1, Ljava/lang/RuntimeException;

    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "Could not find default value of method named loggingStrategy on class named "

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    invoke-direct {p1, p0}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/String;)V

    throw p1

    .line 231
    :catchall_1
    new-instance p1, Ljava/lang/RuntimeException;

    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "Could not find method named loggingStrategy on class named "

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    invoke-direct {p1, p0}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/String;)V

    throw p1

    .line 224
    :catchall_2
    new-instance p1, Ljava/lang/RuntimeException;

    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "Could not resolve class named "

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    invoke-direct {p1, p0}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method private static lookupCategoryName(Lorg/codehaus/groovy/ast/AnnotationNode;)Ljava/lang/String;
    .locals 1

    const-string v0, "category"

    .line 205
    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/ast/AnnotationNode;->getMember(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p0

    if-eqz p0, :cond_0

    .line 206
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/expr/Expression;->getText()Ljava/lang/String;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 207
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/expr/Expression;->getText()Ljava/lang/String;

    move-result-object p0

    return-object p0

    :cond_0
    const-string p0, "##default-category-name##"

    return-object p0
.end method

.method private lookupLogFieldModifiers(Lorg/codehaus/groovy/ast/AnnotatedNode;Lorg/codehaus/groovy/ast/AnnotationNode;)I
    .locals 2

    .line 213
    const-class v0, Lorg/codehaus/groovy/ast/ClassNode;

    const/4 v1, 0x2

    invoke-static {p2, p1, v0, v1}, Lorg/apache/groovy/ast/tools/VisibilityUtils;->getVisibility(Lorg/codehaus/groovy/ast/AnnotationNode;Lorg/codehaus/groovy/ast/AnnotatedNode;Ljava/lang/Class;I)I

    move-result p1

    or-int/lit16 p1, p1, 0x98

    return p1
.end method

.method private static lookupLogFieldName(Lorg/codehaus/groovy/ast/AnnotationNode;)Ljava/lang/String;
    .locals 1

    const-string v0, "value"

    .line 196
    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/ast/AnnotationNode;->getMember(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p0

    if-eqz p0, :cond_0

    .line 197
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/expr/Expression;->getText()Ljava/lang/String;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 198
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/expr/Expression;->getText()Ljava/lang/String;

    move-result-object p0

    return-object p0

    :cond_0
    const-string p0, "log"

    return-object p0
.end method


# virtual methods
.method public setCompilationUnit(Lorg/codehaus/groovy/control/CompilationUnit;)V
    .locals 0

    .line 73
    iput-object p1, p0, Lorg/codehaus/groovy/transform/LogASTTransformation;->compilationUnit:Lorg/codehaus/groovy/control/CompilationUnit;

    return-void
.end method

.method public visit([Lorg/codehaus/groovy/ast/ASTNode;Lorg/codehaus/groovy/control/SourceUnit;)V
    .locals 11

    .line 78
    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/transform/LogASTTransformation;->init([Lorg/codehaus/groovy/ast/ASTNode;Lorg/codehaus/groovy/control/SourceUnit;)V

    const/4 v0, 0x1

    .line 79
    aget-object v1, p1, v0

    check-cast v1, Lorg/codehaus/groovy/ast/AnnotatedNode;

    const/4 v2, 0x0

    .line 80
    aget-object p1, p1, v2

    check-cast p1, Lorg/codehaus/groovy/ast/AnnotationNode;

    .line 82
    invoke-virtual {p2}, Lorg/codehaus/groovy/control/SourceUnit;->getClassLoader()Lgroovy/lang/GroovyClassLoader;

    move-result-object v2

    iget-object v3, p0, Lorg/codehaus/groovy/transform/LogASTTransformation;->compilationUnit:Lorg/codehaus/groovy/control/CompilationUnit;

    invoke-virtual {v3}, Lorg/codehaus/groovy/control/CompilationUnit;->getTransformLoader()Lgroovy/lang/GroovyClassLoader;

    move-result-object v3

    invoke-static {p1, v2, v3}, Lorg/codehaus/groovy/transform/LogASTTransformation;->createLoggingStrategy(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/ClassLoader;Ljava/lang/ClassLoader;)Lorg/codehaus/groovy/transform/LogASTTransformation$LoggingStrategy;

    move-result-object v8

    if-nez v8, :cond_0

    return-void

    .line 85
    :cond_0
    invoke-static {p1}, Lorg/codehaus/groovy/transform/LogASTTransformation;->lookupLogFieldName(Lorg/codehaus/groovy/ast/AnnotationNode;)Ljava/lang/String;

    move-result-object v7

    .line 87
    invoke-static {p1}, Lorg/codehaus/groovy/transform/LogASTTransformation;->lookupCategoryName(Lorg/codehaus/groovy/ast/AnnotationNode;)Ljava/lang/String;

    move-result-object v9

    .line 89
    invoke-direct {p0, v1, p1}, Lorg/codehaus/groovy/transform/LogASTTransformation;->lookupLogFieldModifiers(Lorg/codehaus/groovy/ast/AnnotatedNode;Lorg/codehaus/groovy/ast/AnnotationNode;)I

    move-result v10

    .line 91
    instance-of v2, v1, Lorg/codehaus/groovy/ast/ClassNode;

    if-eqz v2, :cond_1

    .line 94
    check-cast v1, Lorg/codehaus/groovy/ast/ClassNode;

    .line 96
    new-instance p1, Lorg/codehaus/groovy/transform/LogASTTransformation$1;

    move-object v4, p1

    move-object v5, p0

    move-object v6, p2

    invoke-direct/range {v4 .. v10}, Lorg/codehaus/groovy/transform/LogASTTransformation$1;-><init>(Lorg/codehaus/groovy/transform/LogASTTransformation;Lorg/codehaus/groovy/control/SourceUnit;Ljava/lang/String;Lorg/codehaus/groovy/transform/LogASTTransformation$LoggingStrategy;Ljava/lang/String;I)V

    .line 189
    invoke-virtual {p1, v1}, Lorg/codehaus/groovy/ast/ClassCodeExpressionTransformer;->visitClass(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 192
    new-instance p1, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;

    invoke-direct {p1, p2, v0}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;-><init>(Lorg/codehaus/groovy/control/SourceUnit;Z)V

    invoke-virtual {p1, v1}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->visitClass(Lorg/codehaus/groovy/ast/ClassNode;)V

    return-void

    .line 92
    :cond_1
    new-instance p2, Lorg/codehaus/groovy/GroovyBugError;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Class annotation "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/AnnotationNode;->getClassNode()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, " annotated no Class, this must not happen."

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {p2, p1}, Lorg/codehaus/groovy/GroovyBugError;-><init>(Ljava/lang/String;)V

    throw p2
.end method
