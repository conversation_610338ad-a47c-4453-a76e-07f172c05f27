.class public Lorg/codehaus/groovy/transform/SingletonASTTransformation;
.super Lorg/codehaus/groovy/transform/AbstractASTTransformation;
.source "SingletonASTTransformation.java"


# annotations
.annotation runtime Lorg/codehaus/groovy/transform/GroovyASTTransformation;
    phase = .enum Lorg/codehaus/groovy/control/CompilePhase;->CANONICALIZATION:Lorg/codehaus/groovy/control/CompilePhase;
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 57
    invoke-direct {p0}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;-><init>()V

    return-void
.end method

.method private createConstructor(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/FieldNode;Ljava/lang/String;Z)V
    .locals 5

    .line 108
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getDeclaredConstructors()Ljava/util/List;

    move-result-object v0

    .line 110
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :cond_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_1

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lorg/codehaus/groovy/ast/ConstructorNode;

    .line 111
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/ConstructorNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v3

    if-eqz v3, :cond_2

    .line 112
    array-length v3, v3

    if-nez v3, :cond_0

    goto :goto_0

    :cond_1
    const/4 v2, 0x0

    :cond_2
    :goto_0
    if-eqz p4, :cond_3

    .line 118
    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result p4

    if-nez p4, :cond_3

    .line 119
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p4

    :goto_1
    invoke-interface {p4}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_3

    invoke-interface {p4}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/ast/ConstructorNode;

    const-string v1, "@Singleton didn\'t expect to find one or more additional constructors: remove constructor(s) or set strict=false"

    .line 120
    invoke-virtual {p0, v1, v0}, Lorg/codehaus/groovy/transform/SingletonASTTransformation;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    goto :goto_1

    :cond_3
    if-nez v2, :cond_4

    .line 125
    new-instance p4, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    invoke-direct {p4}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;-><init>()V

    .line 127
    invoke-static {p2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object p2

    invoke-static {p2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->notNullX(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/BooleanExpression;

    move-result-object p2

    const-class v0, Ljava/lang/RuntimeException;

    .line 129
    invoke-static {v0}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    const/4 v1, 0x1

    new-array v1, v1, [Lorg/codehaus/groovy/ast/expr/Expression;

    const/4 v2, 0x0

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "Can\'t instantiate singleton "

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    .line 130
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    const-string v4, ". Use "

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    const-string v4, "."

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p3

    invoke-virtual {p3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p3

    invoke-static {p3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->constX(Ljava/lang/Object;)Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    move-result-object p3

    aput-object p3, v1, v2

    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->args([Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    move-result-object p3

    .line 129
    invoke-static {v0, p3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ctorX(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;

    move-result-object p3

    .line 128
    invoke-static {p3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->throwS(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/ThrowStatement;

    move-result-object p3

    .line 126
    invoke-static {p2, p3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ifS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/IfStatement;

    move-result-object p2

    invoke-virtual {p4, p2}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 132
    new-instance p2, Lorg/codehaus/groovy/ast/ConstructorNode;

    const/4 p3, 0x2

    invoke-direct {p2, p3, p4}, Lorg/codehaus/groovy/ast/ConstructorNode;-><init>(ILorg/codehaus/groovy/ast/stmt/Statement;)V

    invoke-static {p1, p2}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->addGeneratedConstructor(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ConstructorNode;)V

    :cond_4
    return-void
.end method

.method private createField(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;ZZ)V
    .locals 8

    if-eqz p3, :cond_0

    const/16 v0, 0x4a

    goto :goto_0

    :cond_0
    const/16 v0, 0x19

    :goto_0
    if-eqz p3, :cond_1

    const/4 v1, 0x0

    goto :goto_1

    .line 75
    :cond_1
    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ctorX(Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;

    move-result-object v1

    .line 76
    :goto_1
    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GenericsUtils;->newClass(Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v2

    invoke-virtual {p1, p2, v0, v2, v1}, Lorg/codehaus/groovy/ast/ClassNode;->addField(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object v0

    .line 77
    invoke-direct {p0, p1, v0, p2, p4}, Lorg/codehaus/groovy/transform/SingletonASTTransformation;->createConstructor(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/FieldNode;Ljava/lang/String;Z)V

    .line 78
    new-instance v7, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    invoke-direct {v7}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;-><init>()V

    if-eqz p3, :cond_2

    .line 79
    invoke-static {p1, v0}, Lorg/codehaus/groovy/transform/SingletonASTTransformation;->lazyBody(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/FieldNode;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p3

    goto :goto_2

    :cond_2
    invoke-static {v0}, Lorg/codehaus/groovy/transform/SingletonASTTransformation;->nonLazyBody(Lorg/codehaus/groovy/ast/FieldNode;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p3

    :goto_2
    invoke-virtual {v7, p3}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 80
    invoke-static {p2}, Lorg/codehaus/groovy/transform/SingletonASTTransformation;->getGetterName(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    const/16 v3, 0x9

    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GenericsUtils;->newClass(Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v4

    sget-object v5, Lorg/codehaus/groovy/ast/Parameter;->EMPTY_ARRAY:[Lorg/codehaus/groovy/ast/Parameter;

    sget-object v6, Lorg/codehaus/groovy/ast/ClassNode;->EMPTY_ARRAY:[Lorg/codehaus/groovy/ast/ClassNode;

    move-object v1, p1

    invoke-static/range {v1 .. v7}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->addGeneratedMethod(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/MethodNode;

    return-void
.end method

.method private static getGetterName(Ljava/lang/String;)Ljava/lang/String;
    .locals 2

    .line 104
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "get"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const/4 v1, 0x0

    invoke-virtual {p0, v1}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-static {v1}, Ljava/lang/Character;->toUpperCase(C)C

    move-result v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    move-result-object v0

    const/4 v1, 0x1

    invoke-virtual {p0, v1}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object p0

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method private static lazyBody(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/FieldNode;)Lorg/codehaus/groovy/ast/stmt/Statement;
    .locals 6

    .line 88
    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object p1

    .line 90
    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->notNullX(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/BooleanExpression;

    move-result-object v0

    .line 91
    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->returnS(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v1

    new-instance v2, Lorg/codehaus/groovy/ast/stmt/SynchronizedStatement;

    .line 93
    invoke-static {p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->classX(Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/ClassExpression;

    move-result-object v3

    .line 95
    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->notNullX(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/BooleanExpression;

    move-result-object v4

    .line 96
    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->returnS(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v5

    .line 97
    invoke-static {p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ctorX(Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;

    move-result-object p0

    invoke-static {p1, p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignX(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p0

    invoke-static {p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->returnS(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p0

    .line 94
    invoke-static {v4, v5, p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ifElseS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/stmt/Statement;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/IfStatement;

    move-result-object p0

    invoke-direct {v2, v3, p0}, Lorg/codehaus/groovy/ast/stmt/SynchronizedStatement;-><init>(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 89
    invoke-static {v0, v1, v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ifElseS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/stmt/Statement;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/IfStatement;

    move-result-object p0

    return-object p0
.end method

.method private static nonLazyBody(Lorg/codehaus/groovy/ast/FieldNode;)Lorg/codehaus/groovy/ast/stmt/Statement;
    .locals 0

    .line 84
    invoke-static {p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object p0

    invoke-static {p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->returnS(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public visit([Lorg/codehaus/groovy/ast/ASTNode;Lorg/codehaus/groovy/control/SourceUnit;)V
    .locals 5

    .line 60
    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/transform/SingletonASTTransformation;->init([Lorg/codehaus/groovy/ast/ASTNode;Lorg/codehaus/groovy/control/SourceUnit;)V

    const/4 p2, 0x1

    .line 61
    aget-object v0, p1, p2

    check-cast v0, Lorg/codehaus/groovy/ast/AnnotatedNode;

    const/4 v1, 0x0

    .line 62
    aget-object p1, p1, v1

    check-cast p1, Lorg/codehaus/groovy/ast/AnnotationNode;

    .line 64
    instance-of v2, v0, Lorg/codehaus/groovy/ast/ClassNode;

    if-eqz v2, :cond_0

    .line 65
    check-cast v0, Lorg/codehaus/groovy/ast/ClassNode;

    const-string v2, "property"

    const-string v3, "instance"

    .line 66
    invoke-static {p1, v2, v3}, Lorg/codehaus/groovy/transform/SingletonASTTransformation;->getMemberStringValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    .line 67
    invoke-static {p2}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v3

    const-string v4, "lazy"

    invoke-virtual {p0, p1, v4, v3}, Lorg/codehaus/groovy/transform/SingletonASTTransformation;->memberHasValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;Ljava/lang/Object;)Z

    move-result v3

    .line 68
    invoke-static {v1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v1

    const-string v4, "strict"

    invoke-virtual {p0, p1, v4, v1}, Lorg/codehaus/groovy/transform/SingletonASTTransformation;->memberHasValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;Ljava/lang/Object;)Z

    move-result p1

    xor-int/2addr p1, p2

    .line 69
    invoke-direct {p0, v0, v2, v3, p1}, Lorg/codehaus/groovy/transform/SingletonASTTransformation;->createField(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;ZZ)V

    :cond_0
    return-void
.end method
