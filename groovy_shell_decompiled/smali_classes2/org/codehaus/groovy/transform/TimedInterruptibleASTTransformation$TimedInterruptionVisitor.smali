.class Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;
.super Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;
.source "TimedInterruptibleASTTransformation.groovy"

# interfaces
.implements Lgroovy/lang/GroovyObject;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0xa
    name = "TimedInterruptionVisitor"
.end annotation


# static fields
.field private static synthetic $callSiteArray:Ljava/lang/ref/SoftReference;

.field private static synthetic $staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

.field private static synthetic $staticClassInfo$:Lorg/codehaus/groovy/reflection/ClassInfo;

.field public static transient synthetic __$stMC:Z


# instance fields
.field private final applyToAllClasses:Z

.field private final applyToAllMembers:Z

.field private final basename:Ljava/lang/String;

.field private final checkOnMethodStart:Z

.field private expireTimeField:Lorg/codehaus/groovy/ast/FieldNode;

.field private final maximum:Ljava/lang/Object;

.field private transient synthetic metaClass:Lgroovy/lang/MetaClass;

.field private final sourceUnit:Lorg/codehaus/groovy/control/SourceUnit;

.field private startTimeField:Lorg/codehaus/groovy/ast/FieldNode;

.field private final thrown:Lorg/codehaus/groovy/ast/ClassNode;

.field private final unit:Lorg/codehaus/groovy/ast/expr/Expression;


# direct methods
.method private static synthetic $createCallSiteArray()Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;
    .locals 3

    const/16 v0, 0x75

    new-array v0, v0, [Ljava/lang/String;

    invoke-static {v0}, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->$createCallSiteArray_1([Ljava/lang/String;)V

    new-instance v1, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;

    const-class v2, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;

    invoke-direct {v1, v2, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;-><init>(Ljava/lang/Class;[Ljava/lang/String;)V

    return-object v1
.end method

.method private static synthetic $createCallSiteArray_1([Ljava/lang/String;)V
    .locals 15

    const/4 v0, 0x0

    const-string v1, "plus"

    aput-object v1, p0, v0

    const/4 v0, 0x1

    const-string v2, "ifS"

    aput-object v2, p0, v0

    const/4 v0, 0x2

    const-string v2, "ltX"

    aput-object v2, p0, v0

    const/4 v0, 0x3

    const-string v2, "propX"

    aput-object v2, p0, v0

    const/4 v0, 0x4

    const-string v3, "varX"

    aput-object v3, p0, v0

    const/4 v0, 0x5

    aput-object v1, p0, v0

    const/4 v0, 0x6

    const-string v4, "callX"

    aput-object v4, p0, v0

    const/4 v0, 0x7

    const-string v5, "make"

    aput-object v5, p0, v0

    const/16 v0, 0x8

    const-string v6, "throwS"

    aput-object v6, p0, v0

    const/16 v0, 0x9

    const-string v6, "ctorX"

    aput-object v6, p0, v0

    const/16 v0, 0xa

    const-string v7, "args"

    aput-object v7, p0, v0

    const/16 v0, 0xb

    const-string v8, "plusX"

    aput-object v8, p0, v0

    const/16 v0, 0xc

    aput-object v8, p0, v0

    const/16 v0, 0xd

    const-string v9, "constX"

    aput-object v9, p0, v0

    const/16 v0, 0xe

    aput-object v1, p0, v0

    const/16 v0, 0xf

    aput-object v1, p0, v0

    const/16 v0, 0x10

    aput-object v4, p0, v0

    const/16 v0, 0x11

    aput-object v4, p0, v0

    const/16 v0, 0x12

    aput-object v2, p0, v0

    const/16 v0, 0x13

    const-string v10, "classX"

    aput-object v10, p0, v0

    const/16 v0, 0x14

    aput-object v8, p0, v0

    const/16 v0, 0x15

    aput-object v9, p0, v0

    const/16 v0, 0x16

    aput-object v2, p0, v0

    const/16 v0, 0x17

    aput-object v3, p0, v0

    const/16 v0, 0x18

    aput-object v1, p0, v0

    const/16 v0, 0x19

    const-string v3, "tap"

    aput-object v3, p0, v0

    const/16 v0, 0x1a

    const-string v3, "block"

    aput-object v3, p0, v0

    const/16 v0, 0x1b

    const-string v3, "getDeclaredField"

    aput-object v3, p0, v0

    const/16 v0, 0x1c

    aput-object v1, p0, v0

    const/16 v0, 0x1d

    const-string v3, "addField"

    aput-object v3, p0, v0

    const/16 v0, 0x1e

    aput-object v1, p0, v0

    const/16 v0, 0x1f

    const-string v11, "or"

    aput-object v11, p0, v0

    const/16 v0, 0x20

    const-string v12, "ACC_FINAL"

    aput-object v12, p0, v0

    const/16 v0, 0x21

    const-string v13, "ACC_PRIVATE"

    aput-object v13, p0, v0

    const/16 v0, 0x22

    const-string v14, "long_TYPE"

    aput-object v14, p0, v0

    const/16 v0, 0x23

    aput-object v8, p0, v0

    const/16 v0, 0x24

    aput-object v4, p0, v0

    const/16 v0, 0x25

    aput-object v5, p0, v0

    const/16 v0, 0x26

    aput-object v4, p0, v0

    const/16 v0, 0x27

    aput-object v2, p0, v0

    const/16 v0, 0x28

    aput-object v10, p0, v0

    const/16 v0, 0x29

    aput-object v7, p0, v0

    const/16 v0, 0x2a

    aput-object v9, p0, v0

    const/16 v0, 0x2b

    aput-object v3, p0, v0

    const/16 v0, 0x2c

    aput-object v1, p0, v0

    const/16 v0, 0x2d

    aput-object v12, p0, v0

    const/16 v0, 0x2e

    aput-object v13, p0, v0

    const/16 v0, 0x2f

    const-string v14, "long_TYPE"

    aput-object v14, p0, v0

    const/16 v0, 0x30

    aput-object v8, p0, v0

    const/16 v0, 0x31

    aput-object v4, p0, v0

    const/16 v0, 0x32

    aput-object v5, p0, v0

    const/16 v0, 0x33

    aput-object v4, p0, v0

    const/16 v0, 0x34

    aput-object v2, p0, v0

    const/16 v0, 0x35

    aput-object v10, p0, v0

    const/16 v0, 0x36

    aput-object v7, p0, v0

    const/16 v0, 0x37

    aput-object v9, p0, v0

    const/16 v0, 0x38

    aput-object v5, p0, v0

    const/16 v0, 0x39

    aput-object v3, p0, v0

    const/16 v0, 0x3a

    aput-object v1, p0, v0

    const/16 v0, 0x3b

    aput-object v11, p0, v0

    const/16 v0, 0x3c

    aput-object v12, p0, v0

    const/16 v0, 0x3d

    aput-object v13, p0, v0

    const/16 v0, 0x3e

    aput-object v6, p0, v0

    const/16 v0, 0x3f

    aput-object v3, p0, v0

    const/16 v0, 0x40

    aput-object v1, p0, v0

    const/16 v0, 0x41

    aput-object v12, p0, v0

    const/16 v0, 0x42

    aput-object v13, p0, v0

    const/16 v0, 0x43

    aput-object v6, p0, v0

    const/16 v0, 0x44

    const-string v1, "remove"

    aput-object v1, p0, v0

    const/16 v0, 0x45

    const-string v1, "fields"

    aput-object v1, p0, v0

    const/16 v0, 0x46

    const-string v2, "remove"

    aput-object v2, p0, v0

    const/16 v0, 0x47

    aput-object v1, p0, v0

    const/16 v0, 0x48

    const-string v2, "add"

    aput-object v2, p0, v0

    const/16 v0, 0x49

    aput-object v1, p0, v0

    const/16 v0, 0x4a

    aput-object v2, p0, v0

    const/16 v0, 0x4b

    aput-object v1, p0, v0

    const/16 v0, 0x4c

    const-string v1, "code"

    aput-object v1, p0, v0

    const/16 v0, 0x4d

    aput-object v2, p0, v0

    const/16 v0, 0x4e

    const-string v3, "statements"

    aput-object v3, p0, v0

    const/16 v0, 0x4f

    const-string v3, "createInterruptStatement"

    aput-object v3, p0, v0

    const/16 v0, 0x50

    const-string v3, "wrapBlock"

    aput-object v3, p0, v0

    const/16 v0, 0x51

    aput-object v2, p0, v0

    const/16 v0, 0x52

    const-string v2, "statements"

    aput-object v2, p0, v0

    const/16 v0, 0x53

    aput-object v3, p0, v0

    const/16 v0, 0x54

    const-string v2, "isStatic"

    aput-object v2, p0, v0

    const/16 v0, 0x55

    const-string v4, "isSynthetic"

    aput-object v4, p0, v0

    const/16 v0, 0x56

    aput-object v2, p0, v0

    const/16 v0, 0x57

    aput-object v4, p0, v0

    const/16 v0, 0x58

    aput-object v2, p0, v0

    const/16 v0, 0x59

    aput-object v4, p0, v0

    const/16 v0, 0x5a

    aput-object v2, p0, v0

    const/16 v0, 0x5b

    aput-object v4, p0, v0

    const/16 v0, 0x5c

    const-string v5, "loopBlock"

    aput-object v5, p0, v0

    const/16 v0, 0x5d

    aput-object v3, p0, v0

    const/16 v0, 0x5e

    const-string v5, "visitLoop"

    aput-object v5, p0, v0

    const/16 v0, 0x5f

    aput-object v5, p0, v0

    const/16 v0, 0x60

    aput-object v5, p0, v0

    const/16 v0, 0x61

    aput-object v4, p0, v0

    const/16 v0, 0x62

    aput-object v2, p0, v0

    const/16 v0, 0x63

    const-string v5, "isAbstract"

    aput-object v5, p0, v0

    const/16 v0, 0x64

    aput-object v1, p0, v0

    const/16 v0, 0x65

    aput-object v3, p0, v0

    const/16 v0, 0x66

    aput-object v4, p0, v0

    const/16 v0, 0x67

    aput-object v2, p0, v0

    const/16 v0, 0x68

    aput-object v4, p0, v0

    const/16 v0, 0x69

    aput-object v2, p0, v0

    const/16 v0, 0x6a

    const-string v5, "isAbstract"

    aput-object v5, p0, v0

    const/16 v0, 0x6b

    aput-object v1, p0, v0

    const/16 v0, 0x6c

    aput-object v3, p0, v0

    const/16 v0, 0x6d

    aput-object v4, p0, v0

    const/16 v0, 0x6e

    aput-object v2, p0, v0

    const/16 v0, 0x6f

    const-string v1, "length"

    aput-object v1, p0, v0

    const/16 v0, 0x70

    const-string v2, "getAt"

    aput-object v2, p0, v0

    const/16 v0, 0x71

    aput-object v1, p0, v0

    const/16 v0, 0x72

    aput-object v1, p0, v0

    const/16 v0, 0x73

    const-string v2, "getAt"

    aput-object v2, p0, v0

    const/16 v0, 0x74

    aput-object v1, p0, v0

    return-void
.end method

.method private static synthetic $getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;
    .locals 2

    sget-object v0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->$callSiteArray:Ljava/lang/ref/SoftReference;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Ljava/lang/ref/SoftReference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;

    if-nez v0, :cond_1

    :cond_0
    invoke-static {}, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->$createCallSiteArray()Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;

    move-result-object v0

    new-instance v1, Ljava/lang/ref/SoftReference;

    invoke-direct {v1, v0}, Ljava/lang/ref/SoftReference;-><init>(Ljava/lang/Object;)V

    sput-object v1, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->$callSiteArray:Ljava/lang/ref/SoftReference;

    :cond_1
    iget-object v0, v0, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;->array:[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    return-object v0
.end method

.method public static synthetic $static_methodMissing(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 8

    const-class v0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;

    invoke-static {}, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v1

    instance-of v2, p1, [Ljava/lang/Object;

    const/4 v3, 0x1

    xor-int/2addr v2, v3

    const-string v4, ""

    const/4 v5, 0x0

    if-eqz v2, :cond_0

    const-class v1, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation;

    new-instance v2, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v6, v3, [Ljava/lang/Object;

    aput-object p0, v6, v5

    filled-new-array {v4, v4}, [Ljava/lang/String;

    move-result-object p0

    invoke-direct {v2, v6, p0}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p0

    move-object v2, p0

    check-cast v2, Ljava/lang/String;

    new-array v2, v3, [Ljava/lang/Object;

    aput-object p1, v2, v5

    invoke-static {v0, v1, p0, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodN(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    return-object p0

    :cond_0
    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->isOrigInt()Z

    move-result v2

    if-eqz v2, :cond_1

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->isOrigZ()Z

    move-result v2

    if-eqz v2, :cond_1

    sget-boolean v2, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->__$stMC:Z

    if-nez v2, :cond_1

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->disabledStandardMetaClass()Z

    move-result v2

    if-nez v2, :cond_1

    const/16 v2, 0x74

    aget-object v1, v1, v2

    const-class v2, [Ljava/lang/Object;

    invoke-static {p1, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, [Ljava/lang/Object;

    invoke-interface {v1, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    invoke-static {v1, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_2

    const-class v1, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation;

    new-instance v2, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v6, v3, [Ljava/lang/Object;

    aput-object p0, v6, v5

    filled-new-array {v4, v4}, [Ljava/lang/String;

    move-result-object p0

    invoke-direct {v2, v6, p0}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p0

    move-object v2, p0

    check-cast v2, Ljava/lang/String;

    new-array v2, v3, [Ljava/lang/Object;

    const-class v3, [Ljava/lang/Object;

    invoke-static {p1, v3}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, [Ljava/lang/Object;

    invoke-static {p1, v5}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->objectArrayGet([Ljava/lang/Object;I)Ljava/lang/Object;

    move-result-object p1

    aput-object p1, v2, v5

    invoke-static {v0, v1, p0, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodN(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    return-object p0

    :cond_1
    const/16 v2, 0x72

    aget-object v2, v1, v2

    const-class v6, [Ljava/lang/Object;

    invoke-static {p1, v6}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v6

    check-cast v6, [Ljava/lang/Object;

    invoke-interface {v2, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v6

    invoke-static {v2, v6}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_2

    const-class v2, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation;

    new-instance v6, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v7, v3, [Ljava/lang/Object;

    aput-object p0, v7, v5

    filled-new-array {v4, v4}, [Ljava/lang/String;

    move-result-object p0

    invoke-direct {v6, v7, p0}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v6}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p0

    move-object v4, p0

    check-cast v4, Ljava/lang/String;

    new-array v3, v3, [Ljava/lang/Object;

    const/16 v4, 0x73

    aget-object v1, v1, v4

    const-class v4, [Ljava/lang/Object;

    invoke-static {p1, v4}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, [Ljava/lang/Object;

    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v4

    invoke-interface {v1, p1, v4}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    aput-object p1, v3, v5

    invoke-static {v0, v2, p0, v3}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodN(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    return-object p0

    :cond_2
    const-class v1, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation;

    new-instance v2, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v6, v3, [Ljava/lang/Object;

    aput-object p0, v6, v5

    filled-new-array {v4, v4}, [Ljava/lang/String;

    move-result-object p0

    invoke-direct {v2, v6, p0}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p0

    move-object v2, p0

    check-cast v2, Ljava/lang/String;

    new-array v2, v5, [Ljava/lang/Object;

    new-array v4, v3, [Ljava/lang/Object;

    aput-object p1, v4, v5

    new-array p1, v3, [I

    aput v5, p1, v5

    invoke-static {v2, v4, p1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->despreadList([Ljava/lang/Object;[Ljava/lang/Object;[I)[Ljava/lang/Object;

    move-result-object p1

    invoke-static {v0, v1, p0, p1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodN(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic $static_propertyMissing(Ljava/lang/String;)Ljava/lang/Object;
    .locals 5

    invoke-static {}, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    const-class v0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;

    const-class v1, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation;

    new-instance v2, Lorg/codehaus/groovy/runtime/GStringImpl;

    const/4 v3, 0x1

    new-array v3, v3, [Ljava/lang/Object;

    const/4 v4, 0x0

    aput-object p0, v3, v4

    const-string p0, ""

    filled-new-array {p0, p0}, [Ljava/lang/String;

    move-result-object p0

    invoke-direct {v2, v3, p0}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p0

    move-object v2, p0

    check-cast v2, Ljava/lang/String;

    invoke-static {v0, v1, p0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->getProperty(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic $static_propertyMissing(Ljava/lang/String;Ljava/lang/Object;)V
    .locals 4

    invoke-static {}, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    const-class v0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation;

    new-instance v1, Lorg/codehaus/groovy/runtime/GStringImpl;

    const/4 v2, 0x1

    new-array v2, v2, [Ljava/lang/Object;

    const/4 v3, 0x0

    aput-object p0, v2, v3

    const-string p0, ""

    filled-new-array {p0, p0}, [Ljava/lang/String;

    move-result-object p0

    invoke-direct {v1, v2, p0}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p0

    move-object v1, p0

    check-cast v1, Ljava/lang/String;

    const/4 v1, 0x0

    invoke-static {p1, v1, v0, p0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->setProperty(Ljava/lang/Object;Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;)V

    return-void
.end method

.method public constructor <init>(Lorg/codehaus/groovy/control/SourceUnit;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)V
    .locals 3

    invoke-static {}, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v0

    invoke-direct {p0}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;-><init>()V

    const-class v1, Lorg/codehaus/groovy/ast/FieldNode;

    const/4 v2, 0x0

    invoke-static {v2, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/ast/FieldNode;

    iput-object v1, p0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->expireTimeField:Lorg/codehaus/groovy/ast/FieldNode;

    const-class v1, Lorg/codehaus/groovy/ast/FieldNode;

    invoke-static {v2, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/ast/FieldNode;

    iput-object v1, p0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->startTimeField:Lorg/codehaus/groovy/ast/FieldNode;

    invoke-virtual {p0}, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->$getStaticMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v1

    iput-object v1, p0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->metaClass:Lgroovy/lang/MetaClass;

    .line 169
    iput-object p1, p0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->sourceUnit:Lorg/codehaus/groovy/control/SourceUnit;

    .line 170
    invoke-static {p2}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result p1

    iput-boolean p1, p0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->checkOnMethodStart:Z

    .line 171
    invoke-static {p3}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result p1

    iput-boolean p1, p0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->applyToAllClasses:Z

    .line 172
    invoke-static {p4}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result p1

    iput-boolean p1, p0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->applyToAllMembers:Z

    .line 173
    const-class p1, Lorg/codehaus/groovy/ast/expr/Expression;

    invoke-static {p6, p1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lorg/codehaus/groovy/ast/expr/Expression;

    iput-object p1, p0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->unit:Lorg/codehaus/groovy/ast/expr/Expression;

    .line 174
    iput-object p5, p0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->maximum:Ljava/lang/Object;

    .line 175
    const-class p1, Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {p7, p1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lorg/codehaus/groovy/ast/ClassNode;

    iput-object p1, p0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->thrown:Lorg/codehaus/groovy/ast/ClassNode;

    const/4 p1, 0x0

    .line 176
    aget-object p1, v0, p1

    const-string p2, "timedInterrupt"

    invoke-interface {p1, p2, p8}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    move-object p2, p1

    check-cast p2, Ljava/lang/String;

    iput-object p1, p0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->basename:Ljava/lang/String;

    return-void
.end method

.method private createInterruptStatement()Lorg/codehaus/groovy/ast/stmt/Statement;
    .locals 26

    move-object/from16 v0, p0

    invoke-static {}, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v1

    const/4 v2, 0x1

    .line 183
    aget-object v2, v1, v2

    const-class v3, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/4 v4, 0x2

    .line 184
    aget-object v4, v1, v4

    const-class v5, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/4 v6, 0x3

    .line 185
    aget-object v6, v1, v6

    const-class v7, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/4 v8, 0x4

    aget-object v8, v1, v8

    const-class v9, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const-string v10, "this"

    invoke-interface {v8, v9, v10}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v8

    const/4 v9, 0x5

    aget-object v9, v1, v9

    iget-object v11, v0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->basename:Ljava/lang/String;

    const-string v12, "$expireTime"

    invoke-interface {v9, v11, v12}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v9

    invoke-interface {v6, v7, v8, v9}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    const/4 v7, 0x6

    .line 186
    aget-object v7, v1, v7

    const-class v8, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/4 v9, 0x7

    aget-object v9, v1, v9

    const-class v11, Lorg/codehaus/groovy/ast/ClassHelper;

    const-class v12, Ljava/lang/System;

    invoke-interface {v9, v11, v12}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v9

    const-string v11, "nanoTime"

    invoke-interface {v7, v8, v9, v11}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v7

    invoke-interface {v4, v5, v6, v7}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    const/16 v5, 0x8

    .line 188
    aget-object v5, v1, v5

    const-class v6, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v7, 0x9

    .line 189
    aget-object v7, v1, v7

    const-class v8, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    iget-object v9, v0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->thrown:Lorg/codehaus/groovy/ast/ClassNode;

    const/16 v11, 0xa

    .line 190
    aget-object v11, v1, v11

    const-class v12, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v13, 0xb

    .line 191
    aget-object v13, v1, v13

    const-class v14, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v15, 0xc

    .line 192
    aget-object v15, v1, v15

    move-object/from16 v16, v2

    const-class v2, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v17, 0xd

    move-object/from16 v18, v3

    .line 193
    aget-object v3, v1, v17

    move-object/from16 v17, v4

    const-class v4, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v19, 0xe

    move-object/from16 v20, v5

    aget-object v5, v1, v19

    const/16 v19, 0xf

    move-object/from16 v21, v6

    aget-object v6, v1, v19

    move-object/from16 v19, v7

    iget-object v7, v0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->maximum:Ljava/lang/Object;

    move-object/from16 v22, v8

    const-string v8, "Execution timed out after "

    invoke-interface {v6, v8, v7}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    const-string v7, " "

    invoke-interface {v5, v6, v7}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    invoke-interface {v3, v4, v5}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    const/16 v4, 0x10

    .line 194
    aget-object v4, v1, v4

    const-class v5, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v6, 0x11

    aget-object v6, v1, v6

    const-class v7, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    iget-object v8, v0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->unit:Lorg/codehaus/groovy/ast/expr/Expression;

    move-object/from16 v23, v9

    const-string v9, "name"

    invoke-interface {v6, v7, v8, v9}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    const/16 v7, 0x12

    aget-object v7, v1, v7

    const-class v8, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v9, 0x13

    aget-object v9, v1, v9

    move-object/from16 v24, v11

    const-class v11, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    move-object/from16 v25, v12

    const-class v12, Ljava/util/Locale;

    invoke-interface {v9, v11, v12}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v9

    const-string v11, "US"

    invoke-interface {v7, v8, v9, v11}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v7

    const-string v8, "toLowerCase"

    invoke-interface {v4, v5, v6, v8, v7}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    invoke-interface {v15, v2, v3, v4}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    const/16 v3, 0x14

    .line 196
    aget-object v3, v1, v3

    const-class v4, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v5, 0x15

    .line 197
    aget-object v5, v1, v5

    const-class v6, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const-string v7, ". Start time: "

    invoke-interface {v5, v6, v7}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    const/16 v6, 0x16

    .line 198
    aget-object v6, v1, v6

    const-class v7, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v8, 0x17

    aget-object v8, v1, v8

    const-class v9, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    invoke-interface {v8, v9, v10}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v8

    const/16 v9, 0x18

    aget-object v1, v1, v9

    iget-object v9, v0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->basename:Ljava/lang/String;

    const-string v10, "$startTime"

    invoke-interface {v1, v9, v10}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-interface {v6, v7, v8, v1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-interface {v3, v4, v5, v1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-interface {v13, v14, v2, v1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v2, v24

    move-object/from16 v3, v25

    invoke-interface {v2, v3, v1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v2, v19

    move-object/from16 v3, v22

    move-object/from16 v4, v23

    invoke-interface {v2, v3, v4, v1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v2, v20

    move-object/from16 v3, v21

    invoke-interface {v2, v3, v1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v2, v16

    move-object/from16 v4, v17

    move-object/from16 v3, v18

    invoke-interface {v2, v3, v4, v1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    const-class v2, Lorg/codehaus/groovy/ast/stmt/Statement;

    invoke-static {v1, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/ast/stmt/Statement;

    return-object v1
.end method

.method private visitLoop(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 3

    invoke-static {}, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v0

    const/16 v1, 0x5c

    .line 288
    aget-object v1, v0, v1

    invoke-interface {v1, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    const/16 v2, 0x5d

    .line 289
    aget-object v0, v0, v2

    invoke-interface {v0, p0, v1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callCurrent(Lgroovy/lang/GroovyObject;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    const-string v1, "loopBlock"

    move-object v2, v1

    check-cast v2, Ljava/lang/String;

    const/4 v2, 0x0

    invoke-static {v0, v2, p1, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->setProperty(Ljava/lang/Object;Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;)V

    return-object v0
.end method

.method private wrapBlock(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 3

    new-instance v0, Lgroovy/lang/Reference;

    invoke-direct {v0, p1}, Lgroovy/lang/Reference;-><init>(Ljava/lang/Object;)V

    invoke-static {}, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object p1

    const/16 v1, 0x19

    .line 215
    aget-object v1, p1, v1

    const/16 v2, 0x1a

    aget-object p1, p1, v2

    const-class v2, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    invoke-interface {p1, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    new-instance v2, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor$_wrapBlock_closure1;

    invoke-direct {v2, p0, p0, v0}, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor$_wrapBlock_closure1;-><init>(Ljava/lang/Object;Ljava/lang/Object;Lgroovy/lang/Reference;)V

    invoke-interface {v1, p1, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method


# virtual methods
.method protected synthetic $getStaticMetaClass()Lgroovy/lang/MetaClass;
    .locals 2

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    const-class v1, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;

    if-eq v0, v1, :cond_0

    invoke-static {p0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->initMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0

    :cond_0
    sget-object v0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    if-nez v0, :cond_1

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getClassInfo(Ljava/lang/Class;)Lorg/codehaus/groovy/reflection/ClassInfo;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    :cond_1
    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0
.end method

.method public getMetaClass()Lgroovy/lang/MetaClass;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    .annotation runtime Lgroovy/transform/Internal;
    .end annotation

    .annotation runtime Lgroovyjarjaropenbeans/Transient;
    .end annotation

    iget-object v0, p0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->metaClass:Lgroovy/lang/MetaClass;

    if-eqz v0, :cond_0

    return-object v0

    :cond_0
    invoke-virtual {p0}, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->$getStaticMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->metaClass:Lgroovy/lang/MetaClass;

    return-object v0
.end method

.method public final getSourceUnit()Lorg/codehaus/groovy/control/SourceUnit;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-object v0, p0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->sourceUnit:Lorg/codehaus/groovy/control/SourceUnit;

    return-object v0
.end method

.method public synthetic methodMissing(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 8

    const-class v0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;

    invoke-static {}, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v1

    instance-of v2, p2, [Ljava/lang/Object;

    const/4 v3, 0x1

    xor-int/2addr v2, v3

    const-string v4, ""

    const/4 v5, 0x0

    if-eqz v2, :cond_0

    const-class v1, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation;

    new-instance v2, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v6, v3, [Ljava/lang/Object;

    aput-object p1, v6, v5

    filled-new-array {v4, v4}, [Ljava/lang/String;

    move-result-object p1

    invoke-direct {v2, v6, p1}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    move-object v2, p1

    check-cast v2, Ljava/lang/String;

    new-array v2, v3, [Ljava/lang/Object;

    aput-object p2, v2, v5

    invoke-static {v0, v1, p1, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodN(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1

    :cond_0
    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->isOrigInt()Z

    move-result v2

    if-eqz v2, :cond_1

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->isOrigZ()Z

    move-result v2

    if-eqz v2, :cond_1

    sget-boolean v2, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->__$stMC:Z

    if-nez v2, :cond_1

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->disabledStandardMetaClass()Z

    move-result v2

    if-nez v2, :cond_1

    const/16 v2, 0x71

    aget-object v1, v1, v2

    const-class v2, [Ljava/lang/Object;

    invoke-static {p2, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, [Ljava/lang/Object;

    invoke-interface {v1, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    invoke-static {v1, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_2

    const-class v1, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation;

    new-instance v2, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v6, v3, [Ljava/lang/Object;

    aput-object p1, v6, v5

    filled-new-array {v4, v4}, [Ljava/lang/String;

    move-result-object p1

    invoke-direct {v2, v6, p1}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    move-object v2, p1

    check-cast v2, Ljava/lang/String;

    new-array v2, v3, [Ljava/lang/Object;

    const-class v3, [Ljava/lang/Object;

    invoke-static {p2, v3}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, [Ljava/lang/Object;

    invoke-static {p2, v5}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->objectArrayGet([Ljava/lang/Object;I)Ljava/lang/Object;

    move-result-object p2

    aput-object p2, v2, v5

    invoke-static {v0, v1, p1, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodN(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1

    :cond_1
    const/16 v2, 0x6f

    aget-object v2, v1, v2

    const-class v6, [Ljava/lang/Object;

    invoke-static {p2, v6}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v6

    check-cast v6, [Ljava/lang/Object;

    invoke-interface {v2, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v6

    invoke-static {v2, v6}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_2

    const-class v2, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation;

    new-instance v6, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v7, v3, [Ljava/lang/Object;

    aput-object p1, v7, v5

    filled-new-array {v4, v4}, [Ljava/lang/String;

    move-result-object p1

    invoke-direct {v6, v7, p1}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v6}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    move-object v4, p1

    check-cast v4, Ljava/lang/String;

    new-array v3, v3, [Ljava/lang/Object;

    const/16 v4, 0x70

    aget-object v1, v1, v4

    const-class v4, [Ljava/lang/Object;

    invoke-static {p2, v4}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, [Ljava/lang/Object;

    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v4

    invoke-interface {v1, p2, v4}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p2

    aput-object p2, v3, v5

    invoke-static {v0, v2, p1, v3}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodN(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1

    :cond_2
    const-class v1, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation;

    new-instance v2, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v6, v3, [Ljava/lang/Object;

    aput-object p1, v6, v5

    filled-new-array {v4, v4}, [Ljava/lang/String;

    move-result-object p1

    invoke-direct {v2, v6, p1}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    move-object v2, p1

    check-cast v2, Ljava/lang/String;

    new-array v2, v5, [Ljava/lang/Object;

    new-array v4, v3, [Ljava/lang/Object;

    aput-object p2, v4, v5

    new-array p2, v3, [I

    aput v5, p2, v5

    invoke-static {v2, v4, p2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->despreadList([Ljava/lang/Object;[Ljava/lang/Object;[I)[Ljava/lang/Object;

    move-result-object p2

    invoke-static {v0, v1, p1, p2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodN(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public synthetic propertyMissing(Ljava/lang/String;)Ljava/lang/Object;
    .locals 5

    invoke-static {}, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    const-class v0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;

    const-class v1, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation;

    new-instance v2, Lorg/codehaus/groovy/runtime/GStringImpl;

    const/4 v3, 0x1

    new-array v3, v3, [Ljava/lang/Object;

    const/4 v4, 0x0

    aput-object p1, v3, v4

    const-string p1, ""

    filled-new-array {p1, p1}, [Ljava/lang/String;

    move-result-object p1

    invoke-direct {v2, v3, p1}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    move-object v2, p1

    check-cast v2, Ljava/lang/String;

    invoke-static {v0, v1, p1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->getProperty(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public synthetic propertyMissing(Ljava/lang/String;Ljava/lang/Object;)V
    .locals 4

    invoke-static {}, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    const-class v0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation;

    new-instance v1, Lorg/codehaus/groovy/runtime/GStringImpl;

    const/4 v2, 0x1

    new-array v2, v2, [Ljava/lang/Object;

    const/4 v3, 0x0

    aput-object p1, v2, v3

    const-string p1, ""

    filled-new-array {p1, p1}, [Ljava/lang/String;

    move-result-object p1

    invoke-direct {v1, v2, p1}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    move-object v1, p1

    check-cast v1, Ljava/lang/String;

    const/4 v1, 0x0

    invoke-static {p2, v1, v0, p1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->setProperty(Ljava/lang/Object;Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;)V

    return-void
.end method

.method public setMetaClass(Lgroovy/lang/MetaClass;)V
    .locals 0
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    .annotation runtime Lgroovy/transform/Internal;
    .end annotation

    iput-object p1, p0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->metaClass:Lgroovy/lang/MetaClass;

    return-void
.end method

.method public synthetic super$2$visitClosureExpression(Lorg/codehaus/groovy/ast/expr/ClosureExpression;)V
    .locals 0

    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/CodeVisitorSupport;->visitClosureExpression(Lorg/codehaus/groovy/ast/expr/ClosureExpression;)V

    return-void
.end method

.method public synthetic super$3$visitClass(Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 0

    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;->visitClass(Lorg/codehaus/groovy/ast/ClassNode;)V

    return-void
.end method

.method public synthetic super$3$visitDoWhileLoop(Lorg/codehaus/groovy/ast/stmt/DoWhileStatement;)V
    .locals 0

    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;->visitDoWhileLoop(Lorg/codehaus/groovy/ast/stmt/DoWhileStatement;)V

    return-void
.end method

.method public synthetic super$3$visitField(Lorg/codehaus/groovy/ast/FieldNode;)V
    .locals 0

    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;->visitField(Lorg/codehaus/groovy/ast/FieldNode;)V

    return-void
.end method

.method public synthetic super$3$visitForLoop(Lorg/codehaus/groovy/ast/stmt/ForStatement;)V
    .locals 0

    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;->visitForLoop(Lorg/codehaus/groovy/ast/stmt/ForStatement;)V

    return-void
.end method

.method public synthetic super$3$visitMethod(Lorg/codehaus/groovy/ast/MethodNode;)V
    .locals 0

    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;->visitMethod(Lorg/codehaus/groovy/ast/MethodNode;)V

    return-void
.end method

.method public synthetic super$3$visitProperty(Lorg/codehaus/groovy/ast/PropertyNode;)V
    .locals 0

    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;->visitProperty(Lorg/codehaus/groovy/ast/PropertyNode;)V

    return-void
.end method

.method public synthetic super$3$visitWhileLoop(Lorg/codehaus/groovy/ast/stmt/WhileStatement;)V
    .locals 0

    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;->visitWhileLoop(Lorg/codehaus/groovy/ast/stmt/WhileStatement;)V

    return-void
.end method

.method public visitClass(Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 20

    move-object/from16 v0, p0

    move-object/from16 v7, p1

    invoke-static {}, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v8

    const/16 v1, 0x1b

    .line 223
    aget-object v1, v8, v1

    const/16 v2, 0x1c

    aget-object v2, v8, v2

    iget-object v3, v0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->basename:Ljava/lang/String;

    const-string v4, "$expireTime"

    invoke-interface {v2, v3, v4}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-interface {v1, v7, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_0

    return-void

    .line 226
    :cond_0
    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->isOrigInt()Z

    move-result v1

    const-string v2, "convert"

    const-string v3, "NANOSECONDS"

    const-string v5, "nanoTime"

    if-eqz v1, :cond_1

    sget-boolean v1, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->__$stMC:Z

    if-nez v1, :cond_1

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->disabledStandardMetaClass()Z

    move-result v1

    if-nez v1, :cond_1

    const/16 v1, 0x2b

    aget-object v1, v8, v1

    const/16 v6, 0x2c

    aget-object v6, v8, v6

    iget-object v10, v0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->basename:Ljava/lang/String;

    invoke-interface {v6, v10, v4}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    const/16 v6, 0x2d

    .line 227
    aget-object v6, v8, v6

    invoke-interface {v6, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGroovyObjectGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    invoke-static {v6}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->intUnbox(Ljava/lang/Object;)I

    move-result v6

    const/16 v10, 0x2e

    aget-object v10, v8, v10

    invoke-interface {v10, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGroovyObjectGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v10

    invoke-static {v10}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->intUnbox(Ljava/lang/Object;)I

    move-result v10

    or-int/2addr v6, v10

    invoke-static {v6}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v6

    const/16 v10, 0x2f

    aget-object v10, v8, v10

    const-class v11, Lorg/codehaus/groovy/ast/ClassHelper;

    invoke-interface {v10, v11}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v10

    const/16 v11, 0x30

    .line 229
    aget-object v11, v8, v11

    const-class v12, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v13, 0x31

    .line 230
    aget-object v13, v8, v13

    const-class v14, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v15, 0x32

    aget-object v15, v8, v15

    const-class v9, Lorg/codehaus/groovy/ast/ClassHelper;

    const-class v7, Ljava/lang/System;

    invoke-interface {v15, v9, v7}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v7

    invoke-interface {v13, v14, v7, v5}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    const/16 v7, 0x33

    .line 231
    aget-object v7, v8, v7

    const-class v9, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v13, 0x34

    .line 232
    aget-object v13, v8, v13

    const-class v14, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v15, 0x35

    aget-object v15, v8, v15

    move-object/from16 v17, v10

    const-class v10, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    move-object/from16 v18, v6

    const-class v6, Ljava/util/concurrent/TimeUnit;

    invoke-interface {v15, v10, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    invoke-interface {v13, v14, v6, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    const/16 v6, 0x36

    .line 234
    aget-object v6, v8, v6

    const-class v10, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v13, 0x37

    aget-object v13, v8, v13

    const-class v14, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    iget-object v15, v0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->maximum:Ljava/lang/Object;

    move-object/from16 v19, v8

    const/16 v16, 0x1

    invoke-static/range {v16 .. v16}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v8

    invoke-interface {v13, v14, v15, v8}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v8

    iget-object v13, v0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->unit:Lorg/codehaus/groovy/ast/expr/Expression;

    invoke-interface {v6, v10, v8, v13}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    invoke-interface {v7, v9, v3, v2, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-interface {v11, v12, v5, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    goto/16 :goto_0

    :cond_1
    move-object/from16 v19, v8

    const/16 v1, 0x1d

    .line 226
    aget-object v1, v19, v1

    const/16 v6, 0x1e

    aget-object v6, v19, v6

    iget-object v7, v0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->basename:Ljava/lang/String;

    invoke-interface {v6, v7, v4}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    const/16 v6, 0x1f

    .line 227
    aget-object v6, v19, v6

    const/16 v7, 0x20

    aget-object v7, v19, v7

    invoke-interface {v7, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGroovyObjectGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v7

    const/16 v8, 0x21

    aget-object v8, v19, v8

    invoke-interface {v8, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGroovyObjectGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v8

    invoke-interface {v6, v7, v8}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    const/16 v7, 0x22

    aget-object v7, v19, v7

    const-class v8, Lorg/codehaus/groovy/ast/ClassHelper;

    invoke-interface {v7, v8}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v7

    const/16 v8, 0x23

    .line 229
    aget-object v8, v19, v8

    const-class v9, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v10, 0x24

    .line 230
    aget-object v10, v19, v10

    const-class v11, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v12, 0x25

    aget-object v12, v19, v12

    const-class v13, Lorg/codehaus/groovy/ast/ClassHelper;

    const-class v14, Ljava/lang/System;

    invoke-interface {v12, v13, v14}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v12

    invoke-interface {v10, v11, v12, v5}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    const/16 v10, 0x26

    .line 231
    aget-object v10, v19, v10

    const-class v11, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v12, 0x27

    .line 232
    aget-object v12, v19, v12

    const-class v13, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v14, 0x28

    aget-object v14, v19, v14

    const-class v15, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    move-object/from16 v17, v7

    const-class v7, Ljava/util/concurrent/TimeUnit;

    invoke-interface {v14, v15, v7}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v7

    invoke-interface {v12, v13, v7, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    const/16 v7, 0x29

    .line 234
    aget-object v7, v19, v7

    const-class v12, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    const/16 v13, 0x2a

    aget-object v13, v19, v13

    const-class v14, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    iget-object v15, v0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->maximum:Ljava/lang/Object;

    move-object/from16 v18, v6

    const/16 v16, 0x1

    invoke-static/range {v16 .. v16}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v6

    invoke-interface {v13, v14, v15, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    iget-object v13, v0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->unit:Lorg/codehaus/groovy/ast/expr/Expression;

    invoke-interface {v7, v12, v6, v13}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    invoke-interface {v10, v11, v3, v2, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-interface {v8, v9, v5, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    :goto_0
    move-object/from16 v2, p1

    move-object v3, v4

    move-object/from16 v4, v18

    move-object/from16 v5, v17

    invoke-interface/range {v1 .. v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    const-class v2, Lorg/codehaus/groovy/ast/FieldNode;

    invoke-static {v1, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/ast/FieldNode;

    iput-object v1, v0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->expireTimeField:Lorg/codehaus/groovy/ast/FieldNode;

    const/4 v1, 0x1

    .line 238
    invoke-static {v1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v2

    iget-object v1, v0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->expireTimeField:Lorg/codehaus/groovy/ast/FieldNode;

    const-string v7, "synthetic"

    move-object v3, v7

    check-cast v3, Ljava/lang/String;

    const/4 v8, 0x0

    invoke-static {v2, v8, v1, v7}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->setProperty(Ljava/lang/Object;Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;)V

    const/16 v1, 0x38

    .line 239
    aget-object v1, v19, v1

    const-class v2, Lorg/codehaus/groovy/ast/ClassHelper;

    const-class v3, Ljava/util/Date;

    invoke-interface {v1, v2, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    const-class v2, Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {v1, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v1

    move-object v5, v1

    check-cast v5, Lorg/codehaus/groovy/ast/ClassNode;

    .line 240
    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->isOrigInt()Z

    move-result v1

    const-string v2, "$startTime"

    if-eqz v1, :cond_2

    sget-boolean v1, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->__$stMC:Z

    if-nez v1, :cond_2

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->disabledStandardMetaClass()Z

    move-result v1

    if-nez v1, :cond_2

    const/16 v1, 0x3f

    aget-object v1, v19, v1

    const/16 v3, 0x40

    aget-object v3, v19, v3

    iget-object v4, v0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->basename:Ljava/lang/String;

    invoke-interface {v3, v4, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    const/16 v2, 0x41

    .line 241
    aget-object v2, v19, v2

    invoke-interface {v2, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGroovyObjectGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->intUnbox(Ljava/lang/Object;)I

    move-result v2

    const/16 v4, 0x42

    aget-object v4, v19, v4

    invoke-interface {v4, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGroovyObjectGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    invoke-static {v4}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->intUnbox(Ljava/lang/Object;)I

    move-result v4

    or-int/2addr v2, v4

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v4

    const/16 v2, 0x43

    .line 243
    aget-object v2, v19, v2

    goto :goto_1

    :cond_2
    const/16 v1, 0x39

    .line 240
    aget-object v1, v19, v1

    const/16 v3, 0x3a

    aget-object v3, v19, v3

    iget-object v4, v0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->basename:Ljava/lang/String;

    invoke-interface {v3, v4, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    const/16 v2, 0x3b

    .line 241
    aget-object v2, v19, v2

    const/16 v4, 0x3c

    aget-object v4, v19, v4

    invoke-interface {v4, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGroovyObjectGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    const/16 v6, 0x3d

    aget-object v6, v19, v6

    invoke-interface {v6, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGroovyObjectGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    invoke-interface {v2, v4, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    const/16 v2, 0x3e

    .line 243
    aget-object v2, v19, v2

    :goto_1
    const-class v6, Lorg/codehaus/groovy/ast/tools/GeneralUtils;

    invoke-interface {v2, v6, v5}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    move-object/from16 v2, p1

    invoke-interface/range {v1 .. v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    const-class v2, Lorg/codehaus/groovy/ast/FieldNode;

    invoke-static {v1, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/ast/FieldNode;

    iput-object v1, v0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->startTimeField:Lorg/codehaus/groovy/ast/FieldNode;

    const/4 v1, 0x1

    .line 245
    invoke-static {v1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v2

    iget-object v1, v0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->startTimeField:Lorg/codehaus/groovy/ast/FieldNode;

    move-object v3, v7

    check-cast v3, Ljava/lang/String;

    invoke-static {v2, v8, v1, v7}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->setProperty(Ljava/lang/Object;Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;)V

    const/16 v1, 0x44

    .line 248
    aget-object v1, v19, v1

    const/16 v2, 0x45

    aget-object v2, v19, v2

    move-object/from16 v3, p1

    invoke-interface {v2, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    iget-object v4, v0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->expireTimeField:Lorg/codehaus/groovy/ast/FieldNode;

    invoke-interface {v1, v2, v4}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v1, 0x46

    .line 249
    aget-object v1, v19, v1

    const/16 v2, 0x47

    aget-object v2, v19, v2

    invoke-interface {v2, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    iget-object v4, v0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->startTimeField:Lorg/codehaus/groovy/ast/FieldNode;

    invoke-interface {v1, v2, v4}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v1, 0x48

    .line 250
    aget-object v1, v19, v1

    const/16 v2, 0x49

    aget-object v2, v19, v2

    invoke-interface {v2, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    const/4 v4, 0x0

    invoke-static {v4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v5

    iget-object v6, v0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->startTimeField:Lorg/codehaus/groovy/ast/FieldNode;

    invoke-interface {v1, v2, v5, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v1, 0x4a

    .line 251
    aget-object v1, v19, v1

    const/16 v2, 0x4b

    aget-object v2, v19, v2

    invoke-interface {v2, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-static {v4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v5

    iget-object v6, v0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->expireTimeField:Lorg/codehaus/groovy/ast/FieldNode;

    invoke-interface {v1, v2, v5, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 252
    iget-boolean v1, v0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->applyToAllMembers:Z

    if-eqz v1, :cond_3

    .line 253
    const-class v1, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;

    const-string v2, "visitClass"

    move-object v5, v2

    check-cast v5, Ljava/lang/String;

    const/4 v5, 0x1

    new-array v5, v5, [Ljava/lang/Object;

    aput-object v3, v5, v4

    invoke-static {v1, v0, v2, v5}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodOnSuperN(Ljava/lang/Class;Lgroovy/lang/GroovyObject;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    :cond_3
    return-void
.end method

.method public visitClosureExpression(Lorg/codehaus/groovy/ast/expr/ClosureExpression;)V
    .locals 7

    invoke-static {}, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v0

    const/16 v1, 0x4c

    .line 260
    aget-object v1, v0, v1

    invoke-interface {v1, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    .line 261
    sget-boolean v2, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->__$stMC:Z

    const-string v3, "code"

    const/4 v4, 0x0

    const/4 v5, 0x0

    .line 262
    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v6

    if-nez v2, :cond_1

    .line 261
    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->disabledStandardMetaClass()Z

    move-result v2

    if-nez v2, :cond_1

    instance-of v2, v1, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    if-eqz v2, :cond_0

    const/16 v2, 0x51

    .line 262
    aget-object v2, v0, v2

    const/16 v3, 0x52

    aget-object v0, v0, v3

    invoke-interface {v0, v1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    invoke-direct {p0}, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->createInterruptStatement()Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v1

    invoke-interface {v2, v0, v6, v1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_1

    :cond_0
    const/16 v2, 0x53

    .line 264
    aget-object v0, v0, v2

    :goto_0
    invoke-interface {v0, p0, v1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callCurrent(Lgroovy/lang/GroovyObject;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    move-object v1, v3

    check-cast v1, Ljava/lang/String;

    invoke-static {v0, v4, p1, v3}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->setProperty(Ljava/lang/Object;Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;)V

    goto :goto_1

    .line 261
    :cond_1
    instance-of v2, v1, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    if-eqz v2, :cond_2

    const/16 v2, 0x4d

    .line 262
    aget-object v2, v0, v2

    const/16 v3, 0x4e

    aget-object v3, v0, v3

    invoke-interface {v3, v1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    const/16 v3, 0x4f

    aget-object v0, v0, v3

    invoke-interface {v0, p0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callCurrent(Lgroovy/lang/GroovyObject;)Ljava/lang/Object;

    move-result-object v0

    invoke-interface {v2, v1, v6, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_1

    :cond_2
    const/16 v2, 0x50

    .line 264
    aget-object v0, v0, v2

    goto :goto_0

    .line 266
    :goto_1
    const-class v0, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;

    const-string v1, "visitClosureExpression"

    move-object v2, v1

    check-cast v2, Ljava/lang/String;

    const/4 v2, 0x1

    new-array v2, v2, [Ljava/lang/Object;

    aput-object p1, v2, v5

    invoke-static {v0, p0, v1, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodOnSuperN(Ljava/lang/Class;Lgroovy/lang/GroovyObject;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method public visitDoWhileLoop(Lorg/codehaus/groovy/ast/stmt/DoWhileStatement;)V
    .locals 4

    invoke-static {}, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v0

    const/16 v1, 0x5f

    .line 300
    aget-object v0, v0, v1

    invoke-interface {v0, p0, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callCurrent(Lgroovy/lang/GroovyObject;Ljava/lang/Object;)Ljava/lang/Object;

    .line 301
    const-class v0, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;

    const-string v1, "visitDoWhileLoop"

    move-object v2, v1

    check-cast v2, Ljava/lang/String;

    const/4 v2, 0x1

    new-array v2, v2, [Ljava/lang/Object;

    const/4 v3, 0x0

    aput-object p1, v2, v3

    invoke-static {v0, p0, v1, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodOnSuperN(Ljava/lang/Class;Lgroovy/lang/GroovyObject;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method public visitField(Lorg/codehaus/groovy/ast/FieldNode;)V
    .locals 5

    invoke-static {}, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v0

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->isOrigZ()Z

    move-result v1

    const-string v2, "visitField"

    const/4 v3, 0x0

    const/4 v4, 0x1

    if-eqz v1, :cond_1

    sget-boolean v1, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->__$stMC:Z

    if-nez v1, :cond_1

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->disabledStandardMetaClass()Z

    move-result v1

    if-nez v1, :cond_1

    const/16 v1, 0x56

    .line 271
    aget-object v1, v0, v1

    invoke-interface {v1, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v1

    xor-int/2addr v1, v4

    if-eqz v1, :cond_0

    const/16 v1, 0x57

    aget-object v0, v0, v1

    invoke-interface {v0, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v0

    xor-int/2addr v0, v4

    if-eqz v0, :cond_0

    move v0, v4

    goto :goto_0

    :cond_0
    move v0, v3

    :goto_0
    if-eqz v0, :cond_3

    .line 272
    const-class v0, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;

    move-object v1, v2

    check-cast v1, Ljava/lang/String;

    new-array v1, v4, [Ljava/lang/Object;

    aput-object p1, v1, v3

    invoke-static {v0, p0, v2, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodOnSuperN(Ljava/lang/Class;Lgroovy/lang/GroovyObject;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_2

    :cond_1
    const/16 v1, 0x54

    .line 271
    aget-object v1, v0, v1

    invoke-interface {v1, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v1

    xor-int/2addr v1, v4

    if-eqz v1, :cond_2

    const/16 v1, 0x55

    aget-object v0, v0, v1

    invoke-interface {v0, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v0

    xor-int/2addr v0, v4

    if-eqz v0, :cond_2

    move v0, v4

    goto :goto_1

    :cond_2
    move v0, v3

    :goto_1
    if-eqz v0, :cond_3

    .line 272
    const-class v0, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;

    move-object v1, v2

    check-cast v1, Ljava/lang/String;

    new-array v1, v4, [Ljava/lang/Object;

    aput-object p1, v1, v3

    invoke-static {v0, p0, v2, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodOnSuperN(Ljava/lang/Class;Lgroovy/lang/GroovyObject;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    :cond_3
    :goto_2
    return-void
.end method

.method public visitForLoop(Lorg/codehaus/groovy/ast/stmt/ForStatement;)V
    .locals 4

    invoke-static {}, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v0

    const/16 v1, 0x5e

    .line 294
    aget-object v0, v0, v1

    invoke-interface {v0, p0, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callCurrent(Lgroovy/lang/GroovyObject;Ljava/lang/Object;)Ljava/lang/Object;

    .line 295
    const-class v0, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;

    const-string v1, "visitForLoop"

    move-object v2, v1

    check-cast v2, Ljava/lang/String;

    const/4 v2, 0x1

    new-array v2, v2, [Ljava/lang/Object;

    const/4 v3, 0x0

    aput-object p1, v2, v3

    invoke-static {v0, p0, v1, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodOnSuperN(Ljava/lang/Class;Lgroovy/lang/GroovyObject;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method public visitMethod(Lorg/codehaus/groovy/ast/MethodNode;)V
    .locals 8

    invoke-static {}, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v0

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->isOrigZ()Z

    move-result v1

    const-string v2, "visitMethod"

    const-string v3, "code"

    const/4 v4, 0x0

    const/4 v5, 0x0

    const/4 v6, 0x1

    if-eqz v1, :cond_5

    sget-boolean v1, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->__$stMC:Z

    if-nez v1, :cond_5

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->disabledStandardMetaClass()Z

    move-result v1

    if-nez v1, :cond_5

    .line 312
    iget-boolean v1, p0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->checkOnMethodStart:Z

    if-eqz v1, :cond_0

    const/16 v1, 0x68

    aget-object v1, v0, v1

    invoke-interface {v1, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v1

    xor-int/2addr v1, v6

    if-eqz v1, :cond_0

    move v1, v6

    goto :goto_0

    :cond_0
    move v1, v5

    :goto_0
    if-eqz v1, :cond_1

    const/16 v1, 0x69

    aget-object v1, v0, v1

    invoke-interface {v1, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v1

    xor-int/2addr v1, v6

    if-eqz v1, :cond_1

    move v1, v6

    goto :goto_1

    :cond_1
    move v1, v5

    :goto_1
    if-eqz v1, :cond_2

    const/16 v1, 0x6a

    aget-object v1, v0, v1

    invoke-interface {v1, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v1

    xor-int/2addr v1, v6

    if-eqz v1, :cond_2

    move v1, v6

    goto :goto_2

    :cond_2
    move v1, v5

    :goto_2
    if-eqz v1, :cond_3

    const/16 v1, 0x6b

    .line 313
    aget-object v1, v0, v1

    invoke-interface {v1, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    const/16 v7, 0x6c

    .line 314
    aget-object v7, v0, v7

    invoke-interface {v7, p0, v1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callCurrent(Lgroovy/lang/GroovyObject;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    move-object v7, v3

    check-cast v7, Ljava/lang/String;

    invoke-static {v1, v4, p1, v3}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->setProperty(Ljava/lang/Object;Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;)V

    :cond_3
    const/16 v1, 0x6d

    .line 316
    aget-object v1, v0, v1

    invoke-interface {v1, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v1

    xor-int/2addr v1, v6

    if-eqz v1, :cond_4

    const/16 v1, 0x6e

    aget-object v0, v0, v1

    invoke-interface {v0, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v0

    xor-int/2addr v0, v6

    if-eqz v0, :cond_4

    move v0, v6

    goto :goto_3

    :cond_4
    move v0, v5

    :goto_3
    if-eqz v0, :cond_b

    .line 317
    const-class v0, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;

    move-object v1, v2

    check-cast v1, Ljava/lang/String;

    new-array v1, v6, [Ljava/lang/Object;

    aput-object p1, v1, v5

    invoke-static {v0, p0, v2, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodOnSuperN(Ljava/lang/Class;Lgroovy/lang/GroovyObject;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    goto/16 :goto_8

    .line 312
    :cond_5
    iget-boolean v1, p0, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->checkOnMethodStart:Z

    if-eqz v1, :cond_6

    const/16 v1, 0x61

    aget-object v1, v0, v1

    invoke-interface {v1, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v1

    xor-int/2addr v1, v6

    if-eqz v1, :cond_6

    move v1, v6

    goto :goto_4

    :cond_6
    move v1, v5

    :goto_4
    if-eqz v1, :cond_7

    const/16 v1, 0x62

    aget-object v1, v0, v1

    invoke-interface {v1, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v1

    xor-int/2addr v1, v6

    if-eqz v1, :cond_7

    move v1, v6

    goto :goto_5

    :cond_7
    move v1, v5

    :goto_5
    if-eqz v1, :cond_8

    const/16 v1, 0x63

    aget-object v1, v0, v1

    invoke-interface {v1, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v1

    xor-int/2addr v1, v6

    if-eqz v1, :cond_8

    move v1, v6

    goto :goto_6

    :cond_8
    move v1, v5

    :goto_6
    if-eqz v1, :cond_9

    const/16 v1, 0x64

    .line 313
    aget-object v1, v0, v1

    invoke-interface {v1, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    const/16 v7, 0x65

    .line 314
    aget-object v7, v0, v7

    invoke-interface {v7, p0, v1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callCurrent(Lgroovy/lang/GroovyObject;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    move-object v7, v3

    check-cast v7, Ljava/lang/String;

    invoke-static {v1, v4, p1, v3}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->setProperty(Ljava/lang/Object;Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;)V

    :cond_9
    const/16 v1, 0x66

    .line 316
    aget-object v1, v0, v1

    invoke-interface {v1, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v1

    xor-int/2addr v1, v6

    if-eqz v1, :cond_a

    const/16 v1, 0x67

    aget-object v0, v0, v1

    invoke-interface {v0, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v0

    xor-int/2addr v0, v6

    if-eqz v0, :cond_a

    move v0, v6

    goto :goto_7

    :cond_a
    move v0, v5

    :goto_7
    if-eqz v0, :cond_b

    .line 317
    const-class v0, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;

    move-object v1, v2

    check-cast v1, Ljava/lang/String;

    new-array v1, v6, [Ljava/lang/Object;

    aput-object p1, v1, v5

    invoke-static {v0, p0, v2, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodOnSuperN(Ljava/lang/Class;Lgroovy/lang/GroovyObject;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    :cond_b
    :goto_8
    return-void
.end method

.method public visitProperty(Lorg/codehaus/groovy/ast/PropertyNode;)V
    .locals 5

    invoke-static {}, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v0

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->isOrigZ()Z

    move-result v1

    const-string v2, "visitProperty"

    const/4 v3, 0x0

    const/4 v4, 0x1

    if-eqz v1, :cond_1

    sget-boolean v1, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->__$stMC:Z

    if-nez v1, :cond_1

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->disabledStandardMetaClass()Z

    move-result v1

    if-nez v1, :cond_1

    const/16 v1, 0x5a

    .line 278
    aget-object v1, v0, v1

    invoke-interface {v1, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v1

    xor-int/2addr v1, v4

    if-eqz v1, :cond_0

    const/16 v1, 0x5b

    aget-object v0, v0, v1

    invoke-interface {v0, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v0

    xor-int/2addr v0, v4

    if-eqz v0, :cond_0

    move v0, v4

    goto :goto_0

    :cond_0
    move v0, v3

    :goto_0
    if-eqz v0, :cond_3

    .line 279
    const-class v0, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;

    move-object v1, v2

    check-cast v1, Ljava/lang/String;

    new-array v1, v4, [Ljava/lang/Object;

    aput-object p1, v1, v3

    invoke-static {v0, p0, v2, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodOnSuperN(Ljava/lang/Class;Lgroovy/lang/GroovyObject;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_2

    :cond_1
    const/16 v1, 0x58

    .line 278
    aget-object v1, v0, v1

    invoke-interface {v1, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v1

    xor-int/2addr v1, v4

    if-eqz v1, :cond_2

    const/16 v1, 0x59

    aget-object v0, v0, v1

    invoke-interface {v0, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v0

    xor-int/2addr v0, v4

    if-eqz v0, :cond_2

    move v0, v4

    goto :goto_1

    :cond_2
    move v0, v3

    :goto_1
    if-eqz v0, :cond_3

    .line 279
    const-class v0, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;

    move-object v1, v2

    check-cast v1, Ljava/lang/String;

    new-array v1, v4, [Ljava/lang/Object;

    aput-object p1, v1, v3

    invoke-static {v0, p0, v2, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodOnSuperN(Ljava/lang/Class;Lgroovy/lang/GroovyObject;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    :cond_3
    :goto_2
    return-void
.end method

.method public visitWhileLoop(Lorg/codehaus/groovy/ast/stmt/WhileStatement;)V
    .locals 4

    invoke-static {}, Lorg/codehaus/groovy/transform/TimedInterruptibleASTTransformation$TimedInterruptionVisitor;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v0

    const/16 v1, 0x60

    .line 306
    aget-object v0, v0, v1

    invoke-interface {v0, p0, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callCurrent(Lgroovy/lang/GroovyObject;Ljava/lang/Object;)Ljava/lang/Object;

    .line 307
    const-class v0, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;

    const-string v1, "visitWhileLoop"

    move-object v2, v1

    check-cast v2, Ljava/lang/String;

    const/4 v2, 0x1

    new-array v2, v2, [Ljava/lang/Object;

    const/4 v3, 0x0

    aput-object p1, v2, v3

    invoke-static {v0, p0, v1, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodOnSuperN(Ljava/lang/Class;Lgroovy/lang/GroovyObject;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method
