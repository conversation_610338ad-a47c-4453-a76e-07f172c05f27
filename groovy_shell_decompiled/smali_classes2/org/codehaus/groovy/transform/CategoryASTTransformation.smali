.class public Lorg/codehaus/groovy/transform/CategoryASTTransformation;
.super Ljava/lang/Object;
.source "CategoryASTTransformation.java"

# interfaces
.implements Lorg/codehaus/groovy/transform/ASTTransformation;


# annotations
.annotation runtime Lorg/codehaus/groovy/transform/GroovyASTTransformation;
    phase = .enum Lorg/codehaus/groovy/control/CompilePhase;->CANONICALIZATION:Lorg/codehaus/groovy/control/CompilePhase;
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 69
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method private static addUnsupportedInstanceMemberError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;Lorg/codehaus/groovy/control/SourceUnit;)V
    .locals 3

    .line 111
    new-instance v0, Lorg/codehaus/groovy/syntax/SyntaxException;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "The @Category transformation does not support instance "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    instance-of v2, p1, Lorg/codehaus/groovy/ast/FieldNode;

    if-eqz v2, :cond_0

    const-string v2, "fields"

    goto :goto_0

    :cond_0
    const-string v2, "properties"

    :goto_0
    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v2, " but found ["

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    const-string v1, "]"

    invoke-virtual {p0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    invoke-direct {v0, p0, p1}, Lorg/codehaus/groovy/syntax/SyntaxException;-><init>(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    invoke-virtual {p2, v0}, Lorg/codehaus/groovy/control/SourceUnit;->addErrorAndContinue(Lorg/codehaus/groovy/syntax/SyntaxException;)V

    return-void
.end method

.method private static ensureNoInstanceFieldOrProperty(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/control/SourceUnit;)Z
    .locals 5

    .line 95
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/ClassNode;->getFields()Ljava/util/List;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    const/4 v1, 0x1

    :cond_0
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    const/4 v3, 0x0

    if-eqz v2, :cond_1

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lorg/codehaus/groovy/ast/FieldNode;

    .line 96
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/FieldNode;->isStatic()Z

    move-result v4

    if-nez v4, :cond_0

    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/FieldNode;->getLineNumber()I

    move-result v4

    if-lez v4, :cond_0

    .line 97
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1, v2, p1}, Lorg/codehaus/groovy/transform/CategoryASTTransformation;->addUnsupportedInstanceMemberError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;Lorg/codehaus/groovy/control/SourceUnit;)V

    move v1, v3

    goto :goto_0

    .line 101
    :cond_1
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/ClassNode;->getProperties()Ljava/util/List;

    move-result-object p0

    invoke-interface {p0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p0

    :cond_2
    :goto_1
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_3

    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/ast/PropertyNode;

    .line 102
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/PropertyNode;->isStatic()Z

    move-result v2

    if-nez v2, :cond_2

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/PropertyNode;->getLineNumber()I

    move-result v2

    if-lez v2, :cond_2

    .line 103
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/PropertyNode;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1, v0, p1}, Lorg/codehaus/groovy/transform/CategoryASTTransformation;->addUnsupportedInstanceMemberError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;Lorg/codehaus/groovy/control/SourceUnit;)V

    move v1, v3

    goto :goto_1

    :cond_3
    return v1
.end method

.method private transformReferencesToThis(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/control/SourceUnit;)V
    .locals 8

    .line 117
    new-instance v6, Lgroovy/lang/Reference;

    invoke-direct {v6}, Lgroovy/lang/Reference;-><init>()V

    .line 118
    new-instance v2, Ljava/util/LinkedList;

    invoke-direct {v2}, Ljava/util/LinkedList;-><init>()V

    .line 120
    new-instance v0, Ljava/util/HashSet;

    invoke-direct {v0}, Ljava/util/HashSet;-><init>()V

    .line 121
    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/ClassNode;->getFields()Ljava/util/List;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_0

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lorg/codehaus/groovy/ast/FieldNode;

    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object v3

    invoke-interface {v0, v3}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    goto :goto_0

    .line 122
    :cond_0
    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/ClassNode;->getProperties()Ljava/util/List;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_1
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_1

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lorg/codehaus/groovy/ast/PropertyNode;

    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/PropertyNode;->getName()Ljava/lang/String;

    move-result-object v3

    invoke-interface {v0, v3}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    goto :goto_1

    .line 123
    :cond_1
    invoke-virtual {v2, v0}, Ljava/util/LinkedList;->add(Ljava/lang/Object;)Z

    .line 125
    new-instance v7, Lorg/codehaus/groovy/transform/CategoryASTTransformation$1;

    move-object v0, v7

    move-object v1, p0

    move-object v3, p1

    move-object v4, p3

    move-object v5, v6

    invoke-direct/range {v0 .. v5}, Lorg/codehaus/groovy/transform/CategoryASTTransformation$1;-><init>(Lorg/codehaus/groovy/transform/CategoryASTTransformation;Ljava/util/LinkedList;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/control/SourceUnit;Lgroovy/lang/Reference;)V

    .line 240
    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/ClassNode;->getMethods()Ljava/util/List;

    move-result-object p2

    invoke-interface {p2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p2

    :cond_2
    :goto_2
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    move-result p3

    if-eqz p3, :cond_3

    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object p3

    check-cast p3, Lorg/codehaus/groovy/ast/MethodNode;

    .line 241
    invoke-virtual {p3}, Lorg/codehaus/groovy/ast/MethodNode;->isStatic()Z

    move-result v0

    if-nez v0, :cond_2

    .line 242
    new-instance v0, Lorg/codehaus/groovy/ast/Parameter;

    const-string v1, "$this"

    invoke-direct {v0, p1, v1}, Lorg/codehaus/groovy/ast/Parameter;-><init>(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)V

    const/4 v1, 0x1

    .line 243
    invoke-virtual {v0, v1}, Lorg/codehaus/groovy/ast/Parameter;->setClosureSharedVariable(Z)V

    .line 244
    invoke-virtual {v6, v0}, Lgroovy/lang/Reference;->set(Ljava/lang/Object;)V

    .line 246
    invoke-virtual {p3}, Lorg/codehaus/groovy/ast/MethodNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v2

    .line 247
    array-length v3, v2

    add-int/2addr v3, v1

    new-array v3, v3, [Lorg/codehaus/groovy/ast/Parameter;

    const/4 v4, 0x0

    .line 248
    aput-object v0, v3, v4

    .line 249
    array-length v0, v2

    invoke-static {v2, v4, v3, v1, v0}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    .line 251
    invoke-virtual {p3}, Lorg/codehaus/groovy/ast/MethodNode;->getModifiers()I

    move-result v0

    or-int/lit8 v0, v0, 0x8

    invoke-virtual {p3, v0}, Lorg/codehaus/groovy/ast/MethodNode;->setModifiers(I)V

    .line 252
    invoke-virtual {p3, v3}, Lorg/codehaus/groovy/ast/MethodNode;->setParameters([Lorg/codehaus/groovy/ast/Parameter;)V

    .line 253
    invoke-virtual {v7, p3}, Lorg/codehaus/groovy/ast/ClassCodeExpressionTransformer;->visitMethod(Lorg/codehaus/groovy/ast/MethodNode;)V

    goto :goto_2

    :cond_3
    return-void
.end method


# virtual methods
.method public visit([Lorg/codehaus/groovy/ast/ASTNode;Lorg/codehaus/groovy/control/SourceUnit;)V
    .locals 6

    .line 73
    array-length v0, p1

    const/4 v1, 0x2

    const/4 v2, 0x1

    const/4 v3, 0x0

    if-ne v0, v1, :cond_0

    aget-object v0, p1, v3

    instance-of v0, v0, Lorg/codehaus/groovy/ast/AnnotationNode;

    if-eqz v0, :cond_0

    aget-object v0, p1, v2

    instance-of v0, v0, Lorg/codehaus/groovy/ast/ClassNode;

    if-nez v0, :cond_2

    .line 74
    :cond_0
    new-instance v0, Lorg/codehaus/groovy/syntax/SyntaxException;

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    const-string v5, "@Category can only be added to a ClassNode but got: "

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    array-length v5, p1

    if-ne v5, v1, :cond_1

    aget-object v1, p1, v2

    goto :goto_0

    :cond_1
    const-string v1, "nothing"

    :goto_0
    invoke-virtual {v4, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    aget-object v4, p1, v3

    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/ASTNode;->getLineNumber()I

    move-result v4

    aget-object v5, p1, v3

    invoke-virtual {v5}, Lorg/codehaus/groovy/ast/ASTNode;->getColumnNumber()I

    move-result v5

    invoke-direct {v0, v1, v4, v5}, Lorg/codehaus/groovy/syntax/SyntaxException;-><init>(Ljava/lang/String;II)V

    invoke-virtual {p2, v0}, Lorg/codehaus/groovy/control/SourceUnit;->addError(Lorg/codehaus/groovy/syntax/SyntaxException;)V

    .line 77
    :cond_2
    aget-object v0, p1, v2

    check-cast v0, Lorg/codehaus/groovy/ast/ClassNode;

    .line 79
    aget-object v1, p1, v3

    check-cast v1, Lorg/codehaus/groovy/ast/AnnotationNode;

    const-string v4, "value"

    invoke-virtual {v1, v4}, Lorg/codehaus/groovy/ast/AnnotationNode;->getMember(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v1

    .line 80
    instance-of v4, v1, Lorg/codehaus/groovy/ast/expr/ClassExpression;

    if-eqz v4, :cond_3

    .line 81
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/expr/Expression;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    goto :goto_1

    .line 83
    :cond_3
    sget-object v1, Lorg/codehaus/groovy/ast/ClassHelper;->OBJECT_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    .line 84
    new-instance v4, Lorg/codehaus/groovy/syntax/SyntaxException;

    aget-object p1, p1, v3

    const-string v3, "@Category must define \'value\' which is the class to apply this category to"

    invoke-direct {v4, v3, p1}, Lorg/codehaus/groovy/syntax/SyntaxException;-><init>(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    invoke-virtual {p2, v4}, Lorg/codehaus/groovy/control/SourceUnit;->addErrorAndContinue(Lorg/codehaus/groovy/syntax/SyntaxException;)V

    move-object p1, v1

    .line 87
    :goto_1
    invoke-static {v0, p2}, Lorg/codehaus/groovy/transform/CategoryASTTransformation;->ensureNoInstanceFieldOrProperty(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/control/SourceUnit;)Z

    move-result v1

    if-eqz v1, :cond_4

    .line 88
    invoke-direct {p0, p1, v0, p2}, Lorg/codehaus/groovy/transform/CategoryASTTransformation;->transformReferencesToThis(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/control/SourceUnit;)V

    .line 89
    new-instance p1, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;

    invoke-direct {p1, p2, v2}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;-><init>(Lorg/codehaus/groovy/control/SourceUnit;Z)V

    invoke-virtual {p1, v0}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->visitClass(Lorg/codehaus/groovy/ast/ClassNode;)V

    :cond_4
    return-void
.end method
