.class public Lorg/codehaus/groovy/transform/ImmutableASTTransformation;
.super Lorg/codehaus/groovy/transform/AbstractASTTransformation;
.source "ImmutableASTTransformation.java"

# interfaces
.implements Lgroovy/transform/CompilationUnitAware;


# annotations
.annotation runtime Lorg/codehaus/groovy/transform/GroovyASTTransformation;
    phase = .enum Lorg/codehaus/groovy/control/CompilePhase;->CANONICALIZATION:Lorg/codehaus/groovy/control/CompilePhase;
.end annotation


# static fields
.field private static final COPY_WITH_METHOD:Ljava/lang/String; = "copyWith"

.field private static final HMAP_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

.field public static final IMMUTABLE_BREADCRUMB:Ljava/lang/String; = "_IMMUTABLE_BREADCRUMB"

.field private static final MEMBER_ADD_COPY_WITH:Ljava/lang/String; = "copyWith"

.field private static final MY_CLASS:Ljava/lang/Class;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/lang/Class<",
            "+",
            "Ljava/lang/annotation/Annotation;",
            ">;"
        }
    .end annotation
.end field

.field public static final MY_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

.field private static final MY_TYPE_NAME:Ljava/lang/String;


# instance fields
.field private compilationUnit:Lorg/codehaus/groovy/control/CompilationUnit;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 91
    const-class v0, Lgroovy/transform/ImmutableBase;

    sput-object v0, Lorg/codehaus/groovy/transform/ImmutableASTTransformation;->MY_CLASS:Ljava/lang/Class;

    const/4 v1, 0x0

    .line 92
    invoke-static {v0, v1}, Lorg/codehaus/groovy/ast/ClassHelper;->makeWithoutCaching(Ljava/lang/Class;Z)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/transform/ImmutableASTTransformation;->MY_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    .line 93
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->getNameWithoutPackage()Ljava/lang/String;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/transform/ImmutableASTTransformation;->MY_TYPE_NAME:Ljava/lang/String;

    .line 99
    const-class v0, Ljava/util/HashMap;

    invoke-static {v0, v1}, Lorg/codehaus/groovy/ast/ClassHelper;->makeWithoutCaching(Ljava/lang/Class;Z)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/transform/ImmutableASTTransformation;->HMAP_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 88
    invoke-direct {p0}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;-><init>()V

    return-void
.end method

.method private static addProperty(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/PropertyNode;)V
    .locals 9

    .line 206
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/PropertyNode;->getField()Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object v0

    .line 207
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/ClassNode;->getFields()Ljava/util/List;

    move-result-object v1

    invoke-interface {v1, v0}, Ljava/util/List;->remove(Ljava/lang/Object;)Z

    .line 208
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/PropertyNode;->getName()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/PropertyNode;->getModifiers()I

    move-result v1

    or-int/lit8 v4, v1, 0x10

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/PropertyNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v5

    .line 209
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/PropertyNode;->getInitialExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v6

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/PropertyNode;->getGetterBlock()Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v7

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/PropertyNode;->getSetterBlock()Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v8

    move-object v2, p0

    .line 208
    invoke-virtual/range {v2 .. v8}, Lorg/codehaus/groovy/ast/ClassNode;->addProperty(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/stmt/Statement;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/PropertyNode;

    .line 210
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/ast/ClassNode;->getField(Ljava/lang/String;)Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object p1

    .line 211
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/ClassNode;->getFields()Ljava/util/List;

    move-result-object v1

    invoke-interface {v1, p1}, Ljava/util/List;->remove(Ljava/lang/Object;)Z

    .line 212
    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/ast/ClassNode;->addField(Lorg/codehaus/groovy/ast/FieldNode;)V

    return-void
.end method

.method private static adjustPropertyForImmutability(Lorg/codehaus/groovy/ast/PropertyNode;Ljava/util/List;Lgroovy/transform/options/PropertyHandler;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/PropertyNode;",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/PropertyNode;",
            ">;",
            "Lgroovy/transform/options/PropertyHandler;",
            ")V"
        }
    .end annotation

    .line 222
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/PropertyNode;->getField()Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object v0

    .line 223
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/PropertyNode;->getModifiers()I

    move-result v1

    and-int/lit8 v1, v1, -0x2

    or-int/lit8 v1, v1, 0x10

    or-int/lit8 v1, v1, 0x2

    invoke-virtual {v0, v1}, Lorg/codehaus/groovy/ast/FieldNode;->setModifiers(I)V

    .line 224
    sget-object v1, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    const-string v2, "_IMMUTABLE_BREADCRUMB"

    invoke-virtual {v0, v2, v1}, Lorg/codehaus/groovy/ast/FieldNode;->setNodeMetaData(Ljava/lang/Object;Ljava/lang/Object;)V

    const/4 v0, 0x0

    .line 225
    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/ast/PropertyNode;->setSetterBlock(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 226
    invoke-virtual {p2, p0}, Lgroovy/transform/options/PropertyHandler;->createPropGetter(Lorg/codehaus/groovy/ast/PropertyNode;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p2

    if-eqz p2, :cond_0

    .line 228
    invoke-virtual {p0, p2}, Lorg/codehaus/groovy/ast/PropertyNode;->setGetterBlock(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 230
    :cond_0
    invoke-interface {p1, p0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public static checkImmutable(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Class<",
            "*>;",
            "Ljava/lang/String;",
            "Ljava/lang/Object;",
            ")",
            "Ljava/lang/Object;"
        }
    .end annotation

    if-eqz p2, :cond_6

    .line 356
    instance-of v0, p2, Ljava/lang/Enum;

    if-nez v0, :cond_6

    invoke-virtual {p2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-static {v0}, Lorg/apache/groovy/ast/tools/ImmutablePropertyUtils;->builtinOrMarkedImmutableClass(Ljava/lang/Class;)Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_2

    .line 361
    :cond_0
    invoke-virtual {p2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/Class;->getAnnotations()[Ljava/lang/annotation/Annotation;

    move-result-object v0

    array-length v1, v0

    const/4 v2, 0x0

    move v3, v2

    :goto_0
    if-ge v3, v1, :cond_2

    aget-object v4, v0, v3

    .line 362
    invoke-virtual {v4}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v4

    invoke-virtual {v4}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v4

    const-string v5, "groovy.transform.Immutable"

    invoke-virtual {v4, v5}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v4

    if-eqz v4, :cond_1

    const/4 v2, 0x1

    goto :goto_1

    :cond_1
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    :cond_2
    :goto_1
    if-eqz v2, :cond_3

    return-object p2

    .line 369
    :cond_3
    instance-of v0, p2, Ljava/util/Collection;

    if-eqz v0, :cond_5

    .line 372
    :try_start_0
    invoke-virtual {p0, p1}, Ljava/lang/Class;->getDeclaredField(Ljava/lang/String;)Ljava/lang/reflect/Field;

    move-result-object v0

    .line 373
    invoke-virtual {v0}, Ljava/lang/reflect/Field;->getType()Ljava/lang/Class;

    move-result-object v0

    .line 374
    const-class v1, Ljava/util/Collection;

    invoke-virtual {v1, v0}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    move-result v1

    if-eqz v1, :cond_4

    .line 375
    move-object v0, p2

    check-cast v0, Ljava/util/Collection;

    invoke-static {v0}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->asImmutable(Ljava/util/Collection;)Ljava/util/Collection;

    move-result-object p0

    return-object p0

    .line 378
    :cond_4
    invoke-static {v0}, Lorg/apache/groovy/ast/tools/ImmutablePropertyUtils;->builtinOrMarkedImmutableClass(Ljava/lang/Class;)Z

    move-result v0
    :try_end_0
    .catch Ljava/lang/NoSuchFieldException; {:try_start_0 .. :try_end_0} :catch_0

    if-eqz v0, :cond_5

    return-object p2

    .line 385
    :catch_0
    :cond_5
    invoke-virtual {p2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object p2

    .line 386
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-virtual {p0}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object p0

    const-string v1, "constructing"

    invoke-static {p0, p1, p2, v1}, Lorg/apache/groovy/ast/tools/ImmutablePropertyUtils;->createErrorMessage(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object p0

    invoke-direct {v0, p0}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/String;)V

    throw v0

    :cond_6
    :goto_2
    return-object p2
.end method

.method public static checkImmutable(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/Object;Ljava/util/List;Ljava/util/List;)Ljava/lang/Object;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Class<",
            "*>;",
            "Ljava/lang/String;",
            "Ljava/lang/Object;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "Ljava/lang/Class;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    if-eqz p2, :cond_7

    .line 391
    instance-of v0, p2, Ljava/lang/Enum;

    if-nez v0, :cond_7

    invoke-virtual {p2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-static {v0}, Lorg/apache/groovy/ast/tools/ImmutablePropertyUtils;->builtinOrMarkedImmutableClass(Ljava/lang/Class;)Z

    move-result v0

    if-nez v0, :cond_7

    invoke-interface {p3, p1}, Ljava/util/List;->contains(Ljava/lang/Object;)Z

    move-result p3

    if-nez p3, :cond_7

    invoke-virtual {p2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object p3

    invoke-interface {p4, p3}, Ljava/util/List;->contains(Ljava/lang/Object;)Z

    move-result p3

    if-eqz p3, :cond_0

    goto :goto_2

    .line 396
    :cond_0
    invoke-virtual {p2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object p3

    invoke-virtual {p3}, Ljava/lang/Class;->getAnnotations()[Ljava/lang/annotation/Annotation;

    move-result-object p3

    array-length v0, p3

    const/4 v1, 0x0

    move v2, v1

    :goto_0
    if-ge v2, v0, :cond_2

    aget-object v3, p3, v2

    .line 397
    invoke-virtual {v3}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v3

    const-string v4, "groovy.transform.Immutable"

    invoke-virtual {v3, v4}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v3

    if-eqz v3, :cond_1

    const/4 v1, 0x1

    goto :goto_1

    :cond_1
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_2
    :goto_1
    if-eqz v1, :cond_3

    return-object p2

    .line 404
    :cond_3
    instance-of p3, p2, Ljava/util/Collection;

    if-eqz p3, :cond_6

    .line 407
    :try_start_0
    invoke-virtual {p0, p1}, Ljava/lang/Class;->getDeclaredField(Ljava/lang/String;)Ljava/lang/reflect/Field;

    move-result-object p3

    .line 408
    invoke-virtual {p3}, Ljava/lang/reflect/Field;->getType()Ljava/lang/Class;

    move-result-object p3

    .line 409
    const-class v0, Ljava/util/Collection;

    invoke-virtual {v0, p3}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    move-result v0

    if-eqz v0, :cond_4

    .line 410
    move-object p3, p2

    check-cast p3, Ljava/util/Collection;

    invoke-static {p3}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->asImmutable(Ljava/util/Collection;)Ljava/util/Collection;

    move-result-object p0

    return-object p0

    .line 413
    :cond_4
    invoke-static {p3}, Lorg/apache/groovy/ast/tools/ImmutablePropertyUtils;->builtinOrMarkedImmutableClass(Ljava/lang/Class;)Z

    move-result v0

    if-nez v0, :cond_5

    invoke-interface {p4, p3}, Ljava/util/List;->contains(Ljava/lang/Object;)Z

    move-result p3
    :try_end_0
    .catch Ljava/lang/NoSuchFieldException; {:try_start_0 .. :try_end_0} :catch_0

    if-eqz p3, :cond_6

    :cond_5
    return-object p2

    .line 420
    :catch_0
    :cond_6
    invoke-virtual {p2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object p2

    .line 421
    new-instance p3, Ljava/lang/RuntimeException;

    invoke-virtual {p0}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object p0

    const-string p4, "constructing"

    invoke-static {p0, p1, p2, p4}, Lorg/apache/groovy/ast/tools/ImmutablePropertyUtils;->createErrorMessage(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object p0

    invoke-direct {p3, p0}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/String;)V

    throw p3

    :cond_7
    :goto_2
    return-object p2
.end method

.method public static checkImmutable(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    if-eqz p2, :cond_3

    .line 333
    instance-of v0, p2, Ljava/lang/Enum;

    if-nez v0, :cond_3

    invoke-virtual {p2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lorg/apache/groovy/ast/tools/ImmutablePropertyUtils;->isBuiltinImmutable(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_0

    .line 334
    :cond_0
    instance-of v0, p2, Ljava/util/Collection;

    if-eqz v0, :cond_1

    check-cast p2, Ljava/util/Collection;

    invoke-static {p2}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->asImmutable(Ljava/util/Collection;)Ljava/util/Collection;

    move-result-object p0

    return-object p0

    :cond_1
    const-string v0, "groovy.transform.Immutable"

    .line 335
    invoke-static {p2, v0}, Lorg/codehaus/groovy/transform/ImmutableASTTransformation;->getAnnotationByName(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/annotation/Annotation;

    move-result-object v0

    if-eqz v0, :cond_2

    return-object p2

    .line 337
    :cond_2
    invoke-virtual {p2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object p2

    .line 338
    new-instance v0, Ljava/lang/RuntimeException;

    const-string v1, "constructing"

    invoke-static {p0, p1, p2, v1}, Lorg/apache/groovy/ast/tools/ImmutablePropertyUtils;->createErrorMessage(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object p0

    invoke-direct {v0, p0}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/String;)V

    throw v0

    :cond_3
    :goto_0
    return-object p2
.end method

.method public static checkPropNames(Ljava/lang/Object;Ljava/util/Map;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/Object;",
            ">;)V"
        }
    .end annotation

    .line 425
    invoke-static {p0}, Lorg/codehaus/groovy/runtime/InvokerHelper;->getMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    .line 426
    invoke-interface {p1}, Ljava/util/Map;->keySet()Ljava/util/Set;

    move-result-object p1

    invoke-interface {p1}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/String;

    .line 427
    invoke-interface {v0, p0, v1}, Lgroovy/lang/MetaClass;->hasProperty(Ljava/lang/Object;Ljava/lang/String;)Lgroovy/lang/MetaProperty;

    move-result-object v2

    if-eqz v2, :cond_0

    goto :goto_0

    .line 428
    :cond_0
    new-instance p1, Lgroovy/lang/MissingPropertyException;

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object p0

    invoke-direct {p1, v1, p0}, Lgroovy/lang/MissingPropertyException;-><init>(Ljava/lang/String;Ljava/lang/Class;)V

    throw p1

    :cond_1
    return-void
.end method

.method private static createCheckForProperty(Lorg/codehaus/groovy/ast/PropertyNode;)Lorg/codehaus/groovy/ast/stmt/Statement;
    .locals 16

    .line 234
    new-instance v0, Lorg/codehaus/groovy/ast/VariableScope;

    invoke-direct {v0}, Lorg/codehaus/groovy/ast/VariableScope;-><init>()V

    const/4 v1, 0x1

    new-array v2, v1, [Lorg/codehaus/groovy/ast/stmt/Statement;

    sget-object v3, Lorg/codehaus/groovy/transform/ImmutableASTTransformation;->HMAP_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    const-string v4, "map"

    .line 238
    invoke-static {v4, v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v5

    new-array v6, v1, [Lorg/codehaus/groovy/ast/expr/Expression;

    .line 240
    invoke-virtual/range {p0 .. p0}, Lorg/codehaus/groovy/ast/PropertyNode;->getName()Ljava/lang/String;

    move-result-object v7

    invoke-static {v7}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->constX(Ljava/lang/Object;)Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    move-result-object v7

    const/4 v8, 0x0

    aput-object v7, v6, v8

    invoke-static {v6}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->args([Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    move-result-object v6

    const-string v7, "containsKey"

    .line 237
    invoke-static {v5, v7, v6}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object v5

    new-instance v6, Lorg/codehaus/groovy/ast/VariableScope;

    invoke-direct {v6}, Lorg/codehaus/groovy/ast/VariableScope;-><init>()V

    const/4 v7, 0x4

    new-array v7, v7, [Lorg/codehaus/groovy/ast/stmt/Statement;

    sget-object v9, Lorg/codehaus/groovy/ast/ClassHelper;->OBJECT_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    const-string v10, "newValue"

    .line 245
    invoke-static {v10, v9}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->localVarX(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v9

    .line 247
    invoke-static {v4, v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v4

    new-array v11, v1, [Lorg/codehaus/groovy/ast/expr/Expression;

    .line 249
    invoke-virtual/range {p0 .. p0}, Lorg/codehaus/groovy/ast/PropertyNode;->getName()Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->constX(Ljava/lang/Object;)Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    move-result-object v12

    aput-object v12, v11, v8

    invoke-static {v11}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->args([Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    move-result-object v11

    const-string v12, "get"

    .line 246
    invoke-static {v4, v12, v11}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object v4

    .line 244
    invoke-static {v9, v4}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->declS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v4

    aput-object v4, v7, v8

    sget-object v4, Lorg/codehaus/groovy/ast/ClassHelper;->OBJECT_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    const-string v9, "oldValue"

    .line 253
    invoke-static {v9, v4}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->localVarX(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v4

    .line 254
    invoke-static/range {p0 .. p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->getGetterName(Lorg/codehaus/groovy/ast/PropertyNode;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callThisX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object v11

    .line 252
    invoke-static {v4, v11}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->declS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v4

    aput-object v4, v7, v1

    sget-object v4, Lorg/codehaus/groovy/ast/ClassHelper;->OBJECT_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    .line 258
    invoke-static {v10, v4}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v4

    sget-object v11, Lorg/codehaus/groovy/ast/ClassHelper;->OBJECT_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    .line 259
    invoke-static {v9, v11}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v11

    .line 257
    invoke-static {v4, v11}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->neX(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/BinaryExpression;

    move-result-object v4

    new-instance v11, Lorg/codehaus/groovy/ast/VariableScope;

    invoke-direct {v11}, Lorg/codehaus/groovy/ast/VariableScope;-><init>()V

    const/4 v12, 0x2

    new-array v13, v12, [Lorg/codehaus/groovy/ast/stmt/Statement;

    sget-object v14, Lorg/codehaus/groovy/ast/ClassHelper;->OBJECT_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    .line 264
    invoke-static {v9, v14}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v14

    sget-object v15, Lorg/codehaus/groovy/ast/ClassHelper;->OBJECT_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    .line 265
    invoke-static {v10, v15}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v10

    .line 263
    invoke-static {v14, v10}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v10

    aput-object v10, v13, v8

    sget-object v10, Lorg/codehaus/groovy/ast/ClassHelper;->boolean_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    const-string v14, "dirty"

    .line 267
    invoke-static {v14, v10}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v10

    sget-object v14, Lorg/codehaus/groovy/ast/expr/ConstantExpression;->TRUE:Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    .line 266
    invoke-static {v10, v14}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v10

    aput-object v10, v13, v1

    .line 261
    invoke-static {v11, v13}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->block(Lorg/codehaus/groovy/ast/VariableScope;[Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    move-result-object v10

    .line 256
    invoke-static {v4, v10}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ifS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/IfStatement;

    move-result-object v4

    aput-object v4, v7, v12

    const-string v4, "construct"

    .line 272
    invoke-static {v4, v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v10

    new-array v11, v12, [Lorg/codehaus/groovy/ast/expr/Expression;

    .line 275
    invoke-virtual/range {p0 .. p0}, Lorg/codehaus/groovy/ast/PropertyNode;->getName()Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->constX(Ljava/lang/Object;)Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    move-result-object v13

    aput-object v13, v11, v8

    sget-object v13, Lorg/codehaus/groovy/ast/ClassHelper;->OBJECT_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    .line 276
    invoke-static {v9, v13}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v9

    aput-object v9, v11, v1

    .line 274
    invoke-static {v11}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->args([Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    move-result-object v9

    const-string v11, "put"

    .line 271
    invoke-static {v10, v11, v9}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object v9

    invoke-static {v9}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->stmt(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v9

    const/4 v10, 0x3

    aput-object v9, v7, v10

    .line 242
    invoke-static {v6, v7}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->block(Lorg/codehaus/groovy/ast/VariableScope;[Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    move-result-object v6

    new-instance v7, Lorg/codehaus/groovy/ast/VariableScope;

    invoke-direct {v7}, Lorg/codehaus/groovy/ast/VariableScope;-><init>()V

    new-array v9, v1, [Lorg/codehaus/groovy/ast/stmt/Statement;

    .line 283
    invoke-static {v4, v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v3

    new-array v4, v12, [Lorg/codehaus/groovy/ast/expr/Expression;

    .line 286
    invoke-virtual/range {p0 .. p0}, Lorg/codehaus/groovy/ast/PropertyNode;->getName()Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->constX(Ljava/lang/Object;)Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    move-result-object v10

    aput-object v10, v4, v8

    .line 287
    invoke-static/range {p0 .. p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->getGetterName(Lorg/codehaus/groovy/ast/PropertyNode;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callThisX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object v10

    aput-object v10, v4, v1

    .line 285
    invoke-static {v4}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->args([Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    move-result-object v1

    .line 282
    invoke-static {v3, v11, v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object v1

    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->stmt(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v1

    aput-object v1, v9, v8

    .line 280
    invoke-static {v7, v9}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->block(Lorg/codehaus/groovy/ast/VariableScope;[Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    move-result-object v1

    .line 236
    invoke-static {v5, v6, v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ifElseS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/stmt/Statement;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/IfStatement;

    move-result-object v1

    aput-object v1, v2, v8

    .line 234
    invoke-static {v0, v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->block(Lorg/codehaus/groovy/ast/VariableScope;[Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    move-result-object v0

    return-object v0
.end method

.method private static createCopyWith(Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/List;)V
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/PropertyNode;",
            ">;)V"
        }
    .end annotation

    .line 296
    new-instance v6, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    invoke-direct {v6}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;-><init>()V

    .line 297
    sget-object v0, Lorg/codehaus/groovy/ast/ClassHelper;->MAP_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    const-string v1, "map"

    .line 299
    invoke-static {v1, v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->equalsNullX(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/BooleanExpression;

    move-result-object v0

    sget-object v2, Lorg/codehaus/groovy/transform/ImmutableASTTransformation;->HMAP_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    .line 300
    invoke-static {v1, v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v3

    const-string v4, "size"

    invoke-static {v3, v4}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object v3

    const/4 v4, 0x0

    invoke-static {v4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v5

    invoke-static {v5}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->constX(Ljava/lang/Object;)Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    move-result-object v5

    invoke-static {v3, v5}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->eqX(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/BinaryExpression;

    move-result-object v3

    .line 298
    invoke-static {v0, v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->orX(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/BinaryExpression;

    move-result-object v0

    const-string v3, "this"

    .line 302
    invoke-static {v3, p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v5

    invoke-static {v5}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->returnS(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v5

    .line 297
    invoke-static {v0, v5}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ifS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/IfStatement;

    move-result-object v0

    invoke-virtual {v6, v0}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 304
    sget-object v0, Lorg/codehaus/groovy/ast/ClassHelper;->boolean_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    const-string v5, "dirty"

    invoke-static {v5, v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->localVarX(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v0

    sget-object v7, Lorg/codehaus/groovy/ast/expr/ConstantExpression;->PRIM_FALSE:Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    invoke-static {v0, v7}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->declS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v0

    invoke-virtual {v6, v0}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    const-string v0, "construct"

    .line 305
    invoke-static {v0, v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->localVarX(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v7

    invoke-static {v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ctorX(Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;

    move-result-object v2

    invoke-static {v7, v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->declS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v2

    invoke-virtual {v6, v2}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 308
    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_0

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lorg/codehaus/groovy/ast/PropertyNode;

    .line 309
    invoke-static {v2}, Lorg/codehaus/groovy/transform/ImmutableASTTransformation;->createCheckForProperty(Lorg/codehaus/groovy/ast/PropertyNode;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v2

    invoke-virtual {v6, v2}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    goto :goto_0

    .line 312
    :cond_0
    sget-object p1, Lorg/codehaus/groovy/ast/ClassHelper;->boolean_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    .line 313
    invoke-static {v5, p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->isTrueX(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/BooleanExpression;

    move-result-object p1

    const/4 v2, 0x1

    new-array v5, v2, [Lorg/codehaus/groovy/ast/expr/Expression;

    sget-object v7, Lorg/codehaus/groovy/transform/ImmutableASTTransformation;->HMAP_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    .line 314
    invoke-static {v0, v7}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v0

    aput-object v0, v5, v4

    invoke-static {v5}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->args([Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    move-result-object v0

    invoke-static {p0, v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ctorX(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;

    move-result-object v0

    .line 315
    invoke-static {v3, p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v3

    .line 312
    invoke-static {p1, v0, v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ternaryX(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/TernaryExpression;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->returnS(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    invoke-virtual {v6, p1}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 318
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/ClassNode;->getPlainNodeReference()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v3

    const/16 p1, 0x11

    new-array v0, v2, [Lorg/codehaus/groovy/ast/Parameter;

    .line 320
    new-instance v2, Lorg/codehaus/groovy/ast/Parameter;

    new-instance v5, Lorg/codehaus/groovy/ast/ClassNode;

    const-class v7, Ljava/util/Map;

    invoke-direct {v5, v7}, Lorg/codehaus/groovy/ast/ClassNode;-><init>(Ljava/lang/Class;)V

    invoke-direct {v2, v5, v1}, Lorg/codehaus/groovy/ast/Parameter;-><init>(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)V

    aput-object v2, v0, v4

    .line 323
    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->params([Lorg/codehaus/groovy/ast/Parameter;)[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v4

    const/4 v5, 0x0

    const-string v1, "copyWith"

    move-object v0, p0

    move v2, p1

    .line 320
    invoke-static/range {v0 .. v6}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->addGeneratedMethod(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/MethodNode;

    return-void
.end method

.method private doMakeImmutable(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/AnnotationNode;Lgroovy/transform/options/PropertyHandler;)V
    .locals 5

    .line 122
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 124
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v1

    .line 125
    sget-object v2, Lorg/codehaus/groovy/transform/ImmutableASTTransformation;->MY_TYPE_NAME:Ljava/lang/String;

    invoke-virtual {p0, p1, v2}, Lorg/codehaus/groovy/transform/ImmutableASTTransformation;->checkNotInterface(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Z

    move-result v2

    if-nez v2, :cond_0

    return-void

    .line 126
    :cond_0
    invoke-static {p0, p1}, Lorg/codehaus/groovy/transform/ImmutableASTTransformation;->makeClassFinal(Lorg/codehaus/groovy/transform/AbstractASTTransformation;Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 128
    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->getInstanceProperties(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/util/List;

    move-result-object v2

    .line 129
    invoke-interface {v2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v3

    :goto_0
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    move-result v4

    if-eqz v4, :cond_1

    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lorg/codehaus/groovy/ast/PropertyNode;

    .line 130
    invoke-static {v4, v0, p3}, Lorg/codehaus/groovy/transform/ImmutableASTTransformation;->adjustPropertyForImmutability(Lorg/codehaus/groovy/ast/PropertyNode;Ljava/util/List;Lgroovy/transform/options/PropertyHandler;)V

    goto :goto_0

    .line 132
    :cond_1
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p3

    :goto_1
    invoke-interface {p3}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_2

    invoke-interface {p3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/ast/PropertyNode;

    .line 133
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getProperties()Ljava/util/List;

    move-result-object v3

    invoke-interface {v3, v0}, Ljava/util/List;->remove(Ljava/lang/Object;)Z

    .line 134
    invoke-static {p1, v0}, Lorg/codehaus/groovy/transform/ImmutableASTTransformation;->addProperty(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/PropertyNode;)V

    goto :goto_1

    .line 136
    :cond_2
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getFields()Ljava/util/List;

    move-result-object p3

    .line 137
    invoke-interface {p3}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p3

    :goto_2
    invoke-interface {p3}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_3

    invoke-interface {p3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/ast/FieldNode;

    .line 138
    invoke-static {p0, v1, v0}, Lorg/codehaus/groovy/transform/ImmutableASTTransformation;->ensureNotPublic(Lorg/codehaus/groovy/transform/AbstractASTTransformation;Ljava/lang/String;Lorg/codehaus/groovy/ast/FieldNode;)V

    goto :goto_2

    .line 140
    :cond_3
    sget-object p3, Lorg/codehaus/groovy/transform/TupleConstructorASTTransformation;->MY_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {p0, p1, p3}, Lorg/codehaus/groovy/transform/ImmutableASTTransformation;->hasAnnotation(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result p3

    if-eqz p3, :cond_a

    .line 142
    sget-object p3, Lorg/codehaus/groovy/transform/TupleConstructorASTTransformation;->MY_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {p1, p3}, Lorg/codehaus/groovy/ast/ClassNode;->getAnnotations(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/util/List;

    move-result-object p3

    const/4 v0, 0x0

    invoke-interface {p3, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p3

    check-cast p3, Lorg/codehaus/groovy/ast/AnnotationNode;

    const-string v0, "excludes"

    .line 143
    invoke-direct {p0, p3, v0}, Lorg/codehaus/groovy/transform/ImmutableASTTransformation;->unsupportedTupleAttribute(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_4

    return-void

    :cond_4
    const-string v0, "includes"

    .line 144
    invoke-direct {p0, p3, v0}, Lorg/codehaus/groovy/transform/ImmutableASTTransformation;->unsupportedTupleAttribute(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_5

    return-void

    :cond_5
    const-string v0, "includeFields"

    .line 145
    invoke-direct {p0, p3, v0}, Lorg/codehaus/groovy/transform/ImmutableASTTransformation;->unsupportedTupleAttribute(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_6

    return-void

    :cond_6
    const-string v0, "includeProperties"

    .line 146
    invoke-direct {p0, p3, v0}, Lorg/codehaus/groovy/transform/ImmutableASTTransformation;->unsupportedTupleAttribute(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_7

    return-void

    :cond_7
    const-string v0, "includeSuperFields"

    .line 147
    invoke-direct {p0, p3, v0}, Lorg/codehaus/groovy/transform/ImmutableASTTransformation;->unsupportedTupleAttribute(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_8

    return-void

    :cond_8
    const-string v0, "callSuper"

    .line 148
    invoke-direct {p0, p3, v0}, Lorg/codehaus/groovy/transform/ImmutableASTTransformation;->unsupportedTupleAttribute(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_9

    return-void

    :cond_9
    const-string v0, "force"

    .line 150
    invoke-direct {p0, p3, v0}, Lorg/codehaus/groovy/transform/ImmutableASTTransformation;->unsupportedTupleAttribute(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;)Z

    move-result p3

    if-eqz p3, :cond_a

    return-void

    .line 152
    :cond_a
    invoke-static {p0, p1}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->hasExplicitConstructor(Lorg/codehaus/groovy/transform/AbstractASTTransformation;Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result p3

    if-eqz p3, :cond_b

    return-void

    :cond_b
    const/4 p3, 0x1

    .line 153
    invoke-static {p3}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v0

    const-string v1, "copyWith"

    invoke-virtual {p0, p2, v1, v0}, Lorg/codehaus/groovy/transform/ImmutableASTTransformation;->memberHasValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;Ljava/lang/Object;)Z

    move-result p2

    if-eqz p2, :cond_c

    invoke-interface {v2}, Ljava/util/List;->isEmpty()Z

    move-result p2

    if-nez p2, :cond_c

    .line 154
    invoke-static {p1, v1, p3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->hasDeclaredMethod(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;I)Z

    move-result p2

    if-nez p2, :cond_c

    .line 155
    invoke-static {p1, v2}, Lorg/codehaus/groovy/transform/ImmutableASTTransformation;->createCopyWith(Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/List;)V

    :cond_c
    return-void
.end method

.method private static ensureNotPublic(Lorg/codehaus/groovy/transform/AbstractASTTransformation;Ljava/lang/String;Lorg/codehaus/groovy/ast/FieldNode;)V
    .locals 3

    .line 198
    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object v0

    .line 200
    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/FieldNode;->isPublic()Z

    move-result v1

    if-eqz v1, :cond_1

    const-string v1, "$"

    invoke-virtual {v0, v1}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result v1

    if-nez v1, :cond_1

    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/FieldNode;->isStatic()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/FieldNode;->isFinal()Z

    move-result v1

    if-nez v1, :cond_1

    .line 201
    :cond_0
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "Public field \'"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "\' not allowed for "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    sget-object v1, Lorg/codehaus/groovy/transform/ImmutableASTTransformation;->MY_TYPE_NAME:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, " class \'"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, "\'."

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    :cond_1
    return-void
.end method

.method private static getAnnotationByName(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/annotation/Annotation;
    .locals 4

    .line 343
    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/Class;->getAnnotations()[Ljava/lang/annotation/Annotation;

    move-result-object p0

    array-length v0, p0

    const/4 v1, 0x0

    :goto_0
    if-ge v1, v0, :cond_1

    aget-object v2, p0, v1

    .line 344
    invoke-virtual {v2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v3, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_0

    return-object v2

    :cond_0
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_1
    const/4 p0, 0x0

    return-object p0
.end method

.method static isSpecialNamedArgCase(Ljava/util/List;Z)Z
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/PropertyNode;",
            ">;Z)Z"
        }
    .end annotation

    const/4 v0, 0x1

    const/4 v1, 0x0

    if-eqz p1, :cond_0

    .line 181
    invoke-interface {p0}, Ljava/util/List;->size()I

    move-result p1

    if-eq p1, v0, :cond_0

    return v1

    .line 182
    :cond_0
    invoke-interface {p0}, Ljava/util/List;->size()I

    move-result p1

    if-nez p1, :cond_1

    return v1

    .line 183
    :cond_1
    invoke-interface {p0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Lorg/codehaus/groovy/ast/PropertyNode;

    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/PropertyNode;->getField()Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object p0

    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/FieldNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p0

    .line 184
    sget-object p1, Lorg/codehaus/groovy/ast/ClassHelper;->MAP_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_2

    return v0

    .line 187
    :cond_2
    sget-object p1, Lorg/codehaus/groovy/transform/ImmutableASTTransformation;->HMAP_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    :goto_0
    if-eqz p1, :cond_4

    .line 189
    invoke-virtual {p1, p0}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_3

    return v0

    .line 192
    :cond_3
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getSuperClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    goto :goto_0

    :cond_4
    return v1
.end method

.method private static makeClassFinal(Lorg/codehaus/groovy/transform/AbstractASTTransformation;Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 3

    .line 170
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getModifiers()I

    move-result v0

    and-int/lit8 v1, v0, 0x10

    if-nez v1, :cond_1

    and-int/lit16 v1, v0, 0x1400

    const/16 v2, 0x1400

    if-ne v1, v2, :cond_0

    .line 173
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Error during "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    sget-object v1, Lorg/codehaus/groovy/transform/ImmutableASTTransformation;->MY_TYPE_NAME:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, " processing: annotation found on inappropriate class "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0, p1}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    return-void

    :cond_0
    or-int/lit8 p0, v0, 0x10

    .line 176
    invoke-virtual {p1, p0}, Lorg/codehaus/groovy/ast/ClassNode;->setModifiers(I)V

    :cond_1
    return-void
.end method

.method static makeImmutable(Lorg/codehaus/groovy/ast/ClassNode;)Z
    .locals 2

    .line 216
    sget-object v0, Lorg/apache/groovy/ast/tools/ImmutablePropertyUtils;->IMMUTABLE_OPTIONS_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/ast/ClassNode;->getAnnotations(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/util/List;

    move-result-object p0

    .line 217
    invoke-interface {p0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    const/4 p0, 0x0

    goto :goto_0

    :cond_0
    invoke-interface {p0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Lorg/codehaus/groovy/ast/AnnotationNode;

    :goto_0
    if-eqz p0, :cond_1

    const/4 v1, 0x1

    :cond_1
    return v1
.end method

.method private unsupportedTupleAttribute(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;)Z
    .locals 4

    .line 160
    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/transform/ImmutableASTTransformation;->getMemberValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 161
    sget-object v0, Lorg/codehaus/groovy/transform/TupleConstructorASTTransformation;->MY_TYPE_NAME:Ljava/lang/String;

    .line 162
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "Error during "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    sget-object v2, Lorg/codehaus/groovy/transform/ImmutableASTTransformation;->MY_TYPE_NAME:Ljava/lang/String;

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v3, " processing: Annotation attribute \'"

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string v1, "\' not supported for "

    invoke-virtual {p2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string v0, " when used with "

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p2, p1}, Lorg/codehaus/groovy/transform/ImmutableASTTransformation;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    const/4 p1, 0x1

    return p1

    :cond_0
    const/4 p1, 0x0

    return p1
.end method


# virtual methods
.method public getAnnotationName()Ljava/lang/String;
    .locals 1

    .line 103
    sget-object v0, Lorg/codehaus/groovy/transform/ImmutableASTTransformation;->MY_TYPE_NAME:Ljava/lang/String;

    return-object v0
.end method

.method public setCompilationUnit(Lorg/codehaus/groovy/control/CompilationUnit;)V
    .locals 0

    .line 434
    iput-object p1, p0, Lorg/codehaus/groovy/transform/ImmutableASTTransformation;->compilationUnit:Lorg/codehaus/groovy/control/CompilationUnit;

    return-void
.end method

.method public visit([Lorg/codehaus/groovy/ast/ASTNode;Lorg/codehaus/groovy/control/SourceUnit;)V
    .locals 3

    .line 107
    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/transform/ImmutableASTTransformation;->init([Lorg/codehaus/groovy/ast/ASTNode;Lorg/codehaus/groovy/control/SourceUnit;)V

    const/4 v0, 0x1

    .line 108
    aget-object v0, p1, v0

    check-cast v0, Lorg/codehaus/groovy/ast/AnnotatedNode;

    const/4 v1, 0x0

    .line 109
    aget-object p1, p1, v1

    check-cast p1, Lorg/codehaus/groovy/ast/AnnotationNode;

    .line 110
    sget-object v1, Lorg/codehaus/groovy/transform/ImmutableASTTransformation;->MY_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/AnnotationNode;->getClassNode()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v2

    invoke-virtual {v1, v2}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_0

    return-void

    .line 112
    :cond_0
    instance-of v1, v0, Lorg/codehaus/groovy/ast/ClassNode;

    if-eqz v1, :cond_4

    .line 113
    iget-object v1, p0, Lorg/codehaus/groovy/transform/ImmutableASTTransformation;->compilationUnit:Lorg/codehaus/groovy/control/CompilationUnit;

    if-eqz v1, :cond_1

    invoke-virtual {v1}, Lorg/codehaus/groovy/control/CompilationUnit;->getTransformLoader()Lgroovy/lang/GroovyClassLoader;

    move-result-object p2

    goto :goto_0

    :cond_1
    invoke-virtual {p2}, Lorg/codehaus/groovy/control/SourceUnit;->getClassLoader()Lgroovy/lang/GroovyClassLoader;

    move-result-object p2

    .line 114
    :goto_0
    check-cast v0, Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {p0, p2, v0}, Lgroovy/transform/options/PropertyHandler;->createPropertyHandler(Lorg/codehaus/groovy/transform/AbstractASTTransformation;Lgroovy/lang/GroovyClassLoader;Lorg/codehaus/groovy/ast/ClassNode;)Lgroovy/transform/options/PropertyHandler;

    move-result-object p2

    if-nez p2, :cond_2

    return-void

    .line 116
    :cond_2
    invoke-virtual {p2, p0, p1}, Lgroovy/transform/options/PropertyHandler;->validateAttributes(Lorg/codehaus/groovy/transform/AbstractASTTransformation;Lorg/codehaus/groovy/ast/AnnotationNode;)Z

    move-result v1

    if-nez v1, :cond_3

    return-void

    .line 117
    :cond_3
    invoke-direct {p0, v0, p1, p2}, Lorg/codehaus/groovy/transform/ImmutableASTTransformation;->doMakeImmutable(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/AnnotationNode;Lgroovy/transform/options/PropertyHandler;)V

    :cond_4
    return-void
.end method
