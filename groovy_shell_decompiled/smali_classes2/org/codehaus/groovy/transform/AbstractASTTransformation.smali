.class public abstract Lorg/codehaus/groovy/transform/AbstractASTTransformation;
.super Ljava/lang/Object;
.source "AbstractASTTransformation.java"

# interfaces
.implements Lgroovyjarjarasm/asm/Opcodes;
.implements Lorg/codehaus/groovy/transform/ASTTransformation;
.implements Lorg/codehaus/groovy/transform/ErrorCollecting;


# static fields
.field public static final RETENTION_CLASSNODE:Lorg/codehaus/groovy/ast/ClassNode;


# instance fields
.field protected sourceUnit:Lorg/codehaus/groovy/control/SourceUnit;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 57
    const-class v0, Ljava/lang/annotation/Retention;

    invoke-static {v0}, Lorg/codehaus/groovy/ast/ClassHelper;->makeWithoutCaching(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->RETENTION_CLASSNODE:Lorg/codehaus/groovy/ast/ClassNode;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 56
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static deemedInternalName(Ljava/lang/String;)Z
    .locals 1

    const-string v0, "$"

    .line 276
    invoke-virtual {p0, v0}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result p0

    return p0
.end method

.method public static getMemberList(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;)Ljava/util/List;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/AnnotationNode;",
            "Ljava/lang/String;",
            ")",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    .line 188
    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/ast/AnnotationNode;->getMember(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    .line 189
    instance-of v1, v0, Lorg/codehaus/groovy/ast/expr/ListExpression;

    if-eqz v1, :cond_0

    .line 190
    check-cast v0, Lorg/codehaus/groovy/ast/expr/ListExpression;

    .line 191
    invoke-static {v0}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->getValueStringList(Lorg/codehaus/groovy/ast/expr/ListExpression;)Ljava/util/List;

    move-result-object p0

    goto :goto_0

    .line 193
    :cond_0
    invoke-static {p0, p1}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->getMemberStringValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;)Ljava/lang/String;

    move-result-object p0

    invoke-static {p0}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->tokenize(Ljava/lang/String;)Ljava/util/List;

    move-result-object p0

    :goto_0
    return-object p0
.end method

.method public static getMemberStringList(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;)Ljava/util/List;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/AnnotationNode;",
            "Ljava/lang/String;",
            ")",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .line 157
    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/ast/AnnotationNode;->getMember(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    const/4 v1, 0x0

    if-nez v0, :cond_0

    return-object v1

    .line 161
    :cond_0
    instance-of v2, v0, Lorg/codehaus/groovy/ast/expr/ListExpression;

    if-eqz v2, :cond_2

    .line 162
    check-cast v0, Lorg/codehaus/groovy/ast/expr/ListExpression;

    .line 163
    invoke-static {v0}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->isUndefinedMarkerList(Lorg/codehaus/groovy/ast/expr/ListExpression;)Z

    move-result p0

    if-eqz p0, :cond_1

    return-object v1

    .line 167
    :cond_1
    invoke-static {v0}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->getValueStringList(Lorg/codehaus/groovy/ast/expr/ListExpression;)Ljava/util/List;

    move-result-object p0

    return-object p0

    .line 169
    :cond_2
    invoke-static {p0, p1}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->getMemberStringValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;)Ljava/lang/String;

    move-result-object p0

    invoke-static {p0}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->tokenize(Ljava/lang/String;)Ljava/util/List;

    move-result-object p0

    return-object p0
.end method

.method public static getMemberStringValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;)Ljava/lang/String;
    .locals 1

    const/4 v0, 0x0

    .line 125
    invoke-static {p0, p1, v0}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->getMemberStringValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method public static getMemberStringValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;
    .locals 0

    .line 115
    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/ast/AnnotationNode;->getMember(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p0

    .line 116
    instance-of p1, p0, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    if-eqz p1, :cond_1

    .line 117
    check-cast p0, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/expr/ConstantExpression;->getValue()Ljava/lang/Object;

    move-result-object p0

    .line 118
    instance-of p1, p0, Ljava/lang/String;

    if-eqz p1, :cond_0

    move-object p1, p0

    check-cast p1, Ljava/lang/String;

    invoke-static {p1}, Lgroovy/transform/Undefined;->isUndefined(Ljava/lang/String;)Z

    move-result p1

    if-eqz p1, :cond_0

    const/4 p0, 0x0

    :cond_0
    if-eqz p0, :cond_1

    .line 119
    invoke-virtual {p0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object p0

    return-object p0

    :cond_1
    return-object p2
.end method

.method private static getTypeList(Lorg/codehaus/groovy/ast/expr/ListExpression;)Ljava/util/List;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/expr/ListExpression;",
            ")",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            ">;"
        }
    .end annotation

    .line 244
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 245
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/expr/ListExpression;->getExpressions()Ljava/util/List;

    move-result-object p0

    invoke-interface {p0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p0

    :cond_0
    :goto_0
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/ast/expr/Expression;

    .line 246
    instance-of v2, v1, Lorg/codehaus/groovy/ast/expr/ClassExpression;

    if-eqz v2, :cond_0

    .line 247
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/expr/Expression;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    if-eqz v1, :cond_0

    .line 248
    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    :cond_1
    return-object v0
.end method

.method private static getValueStringList(Lorg/codehaus/groovy/ast/expr/ListExpression;)Ljava/util/List;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/expr/ListExpression;",
            ")",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .line 199
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 200
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/expr/ListExpression;->getExpressions()Ljava/util/List;

    move-result-object p0

    invoke-interface {p0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p0

    :cond_0
    :goto_0
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/ast/expr/Expression;

    .line 201
    instance-of v2, v1, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    if-eqz v2, :cond_0

    .line 202
    check-cast v1, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/expr/ConstantExpression;->getValue()Ljava/lang/Object;

    move-result-object v1

    if-eqz v1, :cond_0

    .line 203
    invoke-virtual {v1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    :cond_1
    return-object v0
.end method

.method private static isUndefinedMarkerList(Lorg/codehaus/groovy/ast/expr/ListExpression;)Z
    .locals 3

    .line 173
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/expr/ListExpression;->getExpressions()Ljava/util/List;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    const/4 v1, 0x1

    const/4 v2, 0x0

    if-eq v0, v1, :cond_0

    return v2

    .line 174
    :cond_0
    invoke-virtual {p0, v2}, Lorg/codehaus/groovy/ast/expr/ListExpression;->getExpression(I)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p0

    if-nez p0, :cond_1

    return v2

    .line 176
    :cond_1
    instance-of v0, p0, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    if-eqz v0, :cond_2

    .line 177
    check-cast p0, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/expr/ConstantExpression;->getValue()Ljava/lang/Object;

    move-result-object p0

    .line 178
    instance-of v0, p0, Ljava/lang/String;

    if-eqz v0, :cond_3

    check-cast p0, Ljava/lang/String;

    invoke-static {p0}, Lgroovy/transform/Undefined;->isUndefined(Ljava/lang/String;)Z

    move-result p0

    if-eqz p0, :cond_3

    return v1

    .line 179
    :cond_2
    instance-of v0, p0, Lorg/codehaus/groovy/ast/expr/ClassExpression;

    if-eqz v0, :cond_3

    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/expr/Expression;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p0

    invoke-static {p0}, Lgroovy/transform/Undefined;->isUndefined(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result p0

    if-eqz p0, :cond_3

    return v1

    :cond_3
    return v2
.end method

.method public static nonGeneric(Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/ClassNode;
    .locals 0
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    .line 495
    invoke-static {p0}, Lorg/codehaus/groovy/ast/tools/GenericsUtils;->nonGeneric(Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p0

    return-object p0
.end method

.method public static shouldSkip(Ljava/lang/String;Ljava/util/List;Ljava/util/List;)Z
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;)Z"
        }
    .end annotation

    const/4 v0, 0x0

    .line 290
    invoke-static {p0, p1, p2, v0}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->shouldSkip(Ljava/lang/String;Ljava/util/List;Ljava/util/List;Z)Z

    move-result p0

    return p0
.end method

.method public static shouldSkip(Ljava/lang/String;Ljava/util/List;Ljava/util/List;Z)Z
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;Z)Z"
        }
    .end annotation

    if-eqz p1, :cond_0

    .line 294
    invoke-interface {p1, p0}, Ljava/util/List;->contains(Ljava/lang/Object;)Z

    move-result p1

    if-nez p1, :cond_2

    :cond_0
    if-nez p3, :cond_1

    .line 295
    invoke-static {p0}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->deemedInternalName(Ljava/lang/String;)Z

    move-result p1

    if-nez p1, :cond_2

    :cond_1
    if-eqz p2, :cond_3

    .line 296
    invoke-interface {p2}, Ljava/util/List;->isEmpty()Z

    move-result p1

    if-nez p1, :cond_3

    invoke-interface {p2, p0}, Ljava/util/List;->contains(Ljava/lang/Object;)Z

    move-result p0

    if-nez p0, :cond_3

    :cond_2
    const/4 p0, 0x1

    goto :goto_0

    :cond_3
    const/4 p0, 0x0

    :goto_0
    return p0
.end method

.method public static shouldSkipOnDescriptor(ZLjava/util/Map;Lorg/codehaus/groovy/ast/MethodNode;Ljava/util/List;Ljava/util/List;)Z
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z",
            "Ljava/util/Map;",
            "Lorg/codehaus/groovy/ast/MethodNode;",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            ">;",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            ">;)Z"
        }
    .end annotation

    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    .line 301
    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/MethodNode;->getTypeDescriptor()Ljava/lang/String;

    move-result-object v0

    .line 302
    invoke-static {p2}, Lorg/apache/groovy/ast/tools/MethodNodeUtils;->methodDescriptorWithoutReturnType(Lorg/codehaus/groovy/ast/MethodNode;)Ljava/lang/String;

    move-result-object p2

    .line 303
    invoke-interface {p3}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p3

    :cond_0
    invoke-interface {p3}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    const/4 v2, 0x1

    const/4 v3, 0x0

    if-eqz v1, :cond_5

    invoke-interface {p3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/ast/ClassNode;

    .line 304
    new-instance v4, Ljava/util/LinkedList;

    invoke-direct {v4}, Ljava/util/LinkedList;-><init>()V

    .line 305
    invoke-interface {v4, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 306
    new-instance v1, Ljava/util/HashMap;

    invoke-direct {v1, p1}, Ljava/util/HashMap;-><init>(Ljava/util/Map;)V

    .line 307
    :cond_1
    :goto_0
    invoke-interface {v4}, Ljava/util/List;->isEmpty()Z

    move-result v5

    if-nez v5, :cond_0

    .line 308
    invoke-interface {v4, v3}, Ljava/util/List;->remove(I)Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lorg/codehaus/groovy/ast/ClassNode;

    .line 309
    sget-object v6, Lorg/codehaus/groovy/ast/ClassHelper;->OBJECT_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v5, v6}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result v6

    if-nez v6, :cond_1

    .line 310
    invoke-static {v5, v1}, Lorg/codehaus/groovy/ast/tools/GenericsUtils;->createGenericsSpec(Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/Map;)Ljava/util/Map;

    move-result-object v1

    .line 311
    invoke-virtual {v5}, Lorg/codehaus/groovy/ast/ClassNode;->getMethods()Ljava/util/List;

    move-result-object v6

    invoke-interface {v6}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v6

    :cond_2
    invoke-interface {v6}, Ljava/util/Iterator;->hasNext()Z

    move-result v7

    if-eqz v7, :cond_4

    invoke-interface {v6}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v7

    check-cast v7, Lorg/codehaus/groovy/ast/MethodNode;

    .line 312
    invoke-static {v1, v7}, Lorg/codehaus/groovy/ast/tools/GenericsUtils;->correctToGenericsSpec(Ljava/util/Map;Lorg/codehaus/groovy/ast/MethodNode;)Lorg/codehaus/groovy/ast/MethodNode;

    move-result-object v7

    if-eqz p0, :cond_3

    .line 314
    invoke-virtual {v7}, Lorg/codehaus/groovy/ast/MethodNode;->getTypeDescriptor()Ljava/lang/String;

    move-result-object v7

    .line 315
    invoke-virtual {v7, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v7

    if-eqz v7, :cond_2

    return v2

    .line 317
    :cond_3
    invoke-static {v7}, Lorg/apache/groovy/ast/tools/MethodNodeUtils;->methodDescriptorWithoutReturnType(Lorg/codehaus/groovy/ast/MethodNode;)Ljava/lang/String;

    move-result-object v7

    .line 318
    invoke-virtual {v7, p2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v7

    if-eqz v7, :cond_2

    return v2

    .line 321
    :cond_4
    invoke-virtual {v5}, Lorg/codehaus/groovy/ast/ClassNode;->getInterfaces()[Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v5

    invoke-static {v5}, Ljava/util/Arrays;->asList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v5

    invoke-interface {v4, v5}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    goto :goto_0

    .line 325
    :cond_5
    invoke-interface {p4}, Ljava/util/List;->isEmpty()Z

    move-result p3

    if-eqz p3, :cond_6

    return v3

    .line 326
    :cond_6
    invoke-interface {p4}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p3

    :cond_7
    invoke-interface {p3}, Ljava/util/Iterator;->hasNext()Z

    move-result p4

    if-eqz p4, :cond_c

    invoke-interface {p3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object p4

    check-cast p4, Lorg/codehaus/groovy/ast/ClassNode;

    .line 327
    new-instance v1, Ljava/util/LinkedList;

    invoke-direct {v1}, Ljava/util/LinkedList;-><init>()V

    .line 328
    invoke-interface {v1, p4}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 329
    new-instance p4, Ljava/util/HashMap;

    invoke-direct {p4, p1}, Ljava/util/HashMap;-><init>(Ljava/util/Map;)V

    .line 330
    :cond_8
    :goto_1
    invoke-interface {v1}, Ljava/util/List;->isEmpty()Z

    move-result v4

    if-nez v4, :cond_7

    .line 331
    invoke-interface {v1, v3}, Ljava/util/List;->remove(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lorg/codehaus/groovy/ast/ClassNode;

    .line 332
    sget-object v5, Lorg/codehaus/groovy/ast/ClassHelper;->OBJECT_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v4, v5}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result v5

    if-nez v5, :cond_8

    .line 333
    invoke-static {v4, p4}, Lorg/codehaus/groovy/ast/tools/GenericsUtils;->createGenericsSpec(Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/Map;)Ljava/util/Map;

    move-result-object p4

    .line 334
    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/ClassNode;->getMethods()Ljava/util/List;

    move-result-object v5

    invoke-interface {v5}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v5

    :cond_9
    invoke-interface {v5}, Ljava/util/Iterator;->hasNext()Z

    move-result v6

    if-eqz v6, :cond_b

    invoke-interface {v5}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Lorg/codehaus/groovy/ast/MethodNode;

    .line 335
    invoke-static {p4, v6}, Lorg/codehaus/groovy/ast/tools/GenericsUtils;->correctToGenericsSpec(Ljava/util/Map;Lorg/codehaus/groovy/ast/MethodNode;)Lorg/codehaus/groovy/ast/MethodNode;

    move-result-object v6

    if-eqz p0, :cond_a

    .line 337
    invoke-virtual {v6}, Lorg/codehaus/groovy/ast/MethodNode;->getTypeDescriptor()Ljava/lang/String;

    move-result-object v6

    .line 338
    invoke-virtual {v6, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v6

    if-eqz v6, :cond_9

    return v3

    .line 340
    :cond_a
    invoke-static {v6}, Lorg/apache/groovy/ast/tools/MethodNodeUtils;->methodDescriptorWithoutReturnType(Lorg/codehaus/groovy/ast/MethodNode;)Ljava/lang/String;

    move-result-object v6

    .line 341
    invoke-virtual {v6, p2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v6

    if-eqz v6, :cond_9

    return v3

    .line 344
    :cond_b
    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/ClassNode;->getInterfaces()[Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v4

    invoke-static {v4}, Ljava/util/Arrays;->asList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v4

    invoke-interface {v1, v4}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    goto :goto_1

    :cond_c
    return v2
.end method

.method public static shouldSkipOnDescriptorUndefinedAware(ZLjava/util/Map;Lorg/codehaus/groovy/ast/MethodNode;Ljava/util/List;Ljava/util/List;)Z
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z",
            "Ljava/util/Map;",
            "Lorg/codehaus/groovy/ast/MethodNode;",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            ">;",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            ">;)Z"
        }
    .end annotation

    .line 352
    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/MethodNode;->getTypeDescriptor()Ljava/lang/String;

    move-result-object v0

    .line 353
    invoke-static {p2}, Lorg/apache/groovy/ast/tools/MethodNodeUtils;->methodDescriptorWithoutReturnType(Lorg/codehaus/groovy/ast/MethodNode;)Ljava/lang/String;

    move-result-object p2

    const/4 v1, 0x1

    const/4 v2, 0x0

    if-eqz p3, :cond_5

    .line 355
    invoke-interface {p3}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p3

    :cond_0
    invoke-interface {p3}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_5

    invoke-interface {p3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lorg/codehaus/groovy/ast/ClassNode;

    .line 356
    new-instance v4, Ljava/util/LinkedList;

    invoke-direct {v4}, Ljava/util/LinkedList;-><init>()V

    .line 357
    invoke-interface {v4, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 358
    new-instance v3, Ljava/util/HashMap;

    invoke-direct {v3, p1}, Ljava/util/HashMap;-><init>(Ljava/util/Map;)V

    .line 359
    :cond_1
    :goto_0
    invoke-interface {v4}, Ljava/util/List;->isEmpty()Z

    move-result v5

    if-nez v5, :cond_0

    .line 360
    invoke-interface {v4, v2}, Ljava/util/List;->remove(I)Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lorg/codehaus/groovy/ast/ClassNode;

    .line 361
    sget-object v6, Lorg/codehaus/groovy/ast/ClassHelper;->OBJECT_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v5, v6}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result v6

    if-nez v6, :cond_1

    .line 362
    invoke-static {v5, v3}, Lorg/codehaus/groovy/ast/tools/GenericsUtils;->createGenericsSpec(Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/Map;)Ljava/util/Map;

    move-result-object v3

    .line 363
    invoke-virtual {v5}, Lorg/codehaus/groovy/ast/ClassNode;->getMethods()Ljava/util/List;

    move-result-object v6

    invoke-interface {v6}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v6

    :cond_2
    invoke-interface {v6}, Ljava/util/Iterator;->hasNext()Z

    move-result v7

    if-eqz v7, :cond_4

    invoke-interface {v6}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v7

    check-cast v7, Lorg/codehaus/groovy/ast/MethodNode;

    .line 364
    invoke-static {v3, v7}, Lorg/codehaus/groovy/ast/tools/GenericsUtils;->correctToGenericsSpec(Ljava/util/Map;Lorg/codehaus/groovy/ast/MethodNode;)Lorg/codehaus/groovy/ast/MethodNode;

    move-result-object v7

    if-eqz p0, :cond_3

    .line 366
    invoke-virtual {v7}, Lorg/codehaus/groovy/ast/MethodNode;->getTypeDescriptor()Ljava/lang/String;

    move-result-object v7

    .line 367
    invoke-virtual {v7, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v7

    if-eqz v7, :cond_2

    return v1

    .line 369
    :cond_3
    invoke-static {v7}, Lorg/apache/groovy/ast/tools/MethodNodeUtils;->methodDescriptorWithoutReturnType(Lorg/codehaus/groovy/ast/MethodNode;)Ljava/lang/String;

    move-result-object v7

    .line 370
    invoke-virtual {v7, p2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v7

    if-eqz v7, :cond_2

    return v1

    .line 373
    :cond_4
    invoke-virtual {v5}, Lorg/codehaus/groovy/ast/ClassNode;->getInterfaces()[Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v5

    invoke-static {v5}, Ljava/util/Arrays;->asList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v5

    invoke-interface {v4, v5}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    goto :goto_0

    :cond_5
    if-nez p4, :cond_6

    return v2

    .line 379
    :cond_6
    invoke-interface {p4}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p3

    :cond_7
    invoke-interface {p3}, Ljava/util/Iterator;->hasNext()Z

    move-result p4

    if-eqz p4, :cond_c

    invoke-interface {p3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object p4

    check-cast p4, Lorg/codehaus/groovy/ast/ClassNode;

    .line 380
    new-instance v3, Ljava/util/LinkedList;

    invoke-direct {v3}, Ljava/util/LinkedList;-><init>()V

    .line 381
    invoke-interface {v3, p4}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 382
    new-instance p4, Ljava/util/HashMap;

    invoke-direct {p4, p1}, Ljava/util/HashMap;-><init>(Ljava/util/Map;)V

    .line 383
    :cond_8
    :goto_1
    invoke-interface {v3}, Ljava/util/List;->isEmpty()Z

    move-result v4

    if-nez v4, :cond_7

    .line 384
    invoke-interface {v3, v2}, Ljava/util/List;->remove(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lorg/codehaus/groovy/ast/ClassNode;

    .line 385
    sget-object v5, Lorg/codehaus/groovy/ast/ClassHelper;->OBJECT_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v4, v5}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result v5

    if-nez v5, :cond_8

    .line 386
    invoke-static {v4, p4}, Lorg/codehaus/groovy/ast/tools/GenericsUtils;->createGenericsSpec(Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/Map;)Ljava/util/Map;

    move-result-object p4

    .line 387
    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/ClassNode;->getMethods()Ljava/util/List;

    move-result-object v5

    invoke-interface {v5}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v5

    :cond_9
    invoke-interface {v5}, Ljava/util/Iterator;->hasNext()Z

    move-result v6

    if-eqz v6, :cond_b

    invoke-interface {v5}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Lorg/codehaus/groovy/ast/MethodNode;

    .line 388
    invoke-static {p4, v6}, Lorg/codehaus/groovy/ast/tools/GenericsUtils;->correctToGenericsSpec(Ljava/util/Map;Lorg/codehaus/groovy/ast/MethodNode;)Lorg/codehaus/groovy/ast/MethodNode;

    move-result-object v6

    if-eqz p0, :cond_a

    .line 390
    invoke-virtual {v6}, Lorg/codehaus/groovy/ast/MethodNode;->getTypeDescriptor()Ljava/lang/String;

    move-result-object v6

    .line 391
    invoke-virtual {v6, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v6

    if-eqz v6, :cond_9

    return v2

    .line 393
    :cond_a
    invoke-static {v6}, Lorg/apache/groovy/ast/tools/MethodNodeUtils;->methodDescriptorWithoutReturnType(Lorg/codehaus/groovy/ast/MethodNode;)Ljava/lang/String;

    move-result-object v6

    .line 394
    invoke-virtual {v6, p2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v6

    if-eqz v6, :cond_9

    return v2

    .line 397
    :cond_b
    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/ClassNode;->getInterfaces()[Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v4

    invoke-static {v4}, Ljava/util/Arrays;->asList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v4

    invoke-interface {v3, v4}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    goto :goto_1

    :cond_c
    return v1
.end method

.method public static shouldSkipUndefinedAware(Ljava/lang/String;Ljava/util/List;Ljava/util/List;)Z
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;)Z"
        }
    .end annotation

    const/4 v0, 0x0

    .line 280
    invoke-static {p0, p1, p2, v0}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->shouldSkipUndefinedAware(Ljava/lang/String;Ljava/util/List;Ljava/util/List;Z)Z

    move-result p0

    return p0
.end method

.method public static shouldSkipUndefinedAware(Ljava/lang/String;Ljava/util/List;Ljava/util/List;Z)Z
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;Z)Z"
        }
    .end annotation

    if-eqz p1, :cond_0

    .line 284
    invoke-interface {p1, p0}, Ljava/util/List;->contains(Ljava/lang/Object;)Z

    move-result p1

    if-nez p1, :cond_2

    :cond_0
    if-nez p3, :cond_1

    .line 285
    invoke-static {p0}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->deemedInternalName(Ljava/lang/String;)Z

    move-result p1

    if-nez p1, :cond_2

    :cond_1
    if-eqz p2, :cond_3

    .line 286
    invoke-interface {p2, p0}, Ljava/util/List;->contains(Ljava/lang/Object;)Z

    move-result p0

    if-nez p0, :cond_3

    :cond_2
    const/4 p0, 0x1

    goto :goto_0

    :cond_3
    const/4 p0, 0x0

    :goto_0
    return p0
.end method

.method public static tokenize(Ljava/lang/String;)Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            ")",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    if-nez p0, :cond_0

    .line 272
    new-instance p0, Ljava/util/ArrayList;

    invoke-direct {p0}, Ljava/util/ArrayList;-><init>()V

    goto :goto_0

    :cond_0
    const-string v0, ", "

    invoke-static {p0, v0}, Lorg/codehaus/groovy/runtime/StringGroovyMethods;->tokenize(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/util/List;

    move-result-object p0

    :goto_0
    return-object p0
.end method


# virtual methods
.method public addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V
    .locals 2

    .line 255
    iget-object v0, p0, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->sourceUnit:Lorg/codehaus/groovy/control/SourceUnit;

    invoke-virtual {v0}, Lorg/codehaus/groovy/control/SourceUnit;->getErrorCollector()Lorg/codehaus/groovy/control/ErrorCollector;

    move-result-object v0

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const/16 v1, 0xa

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    iget-object v1, p0, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->sourceUnit:Lorg/codehaus/groovy/control/SourceUnit;

    invoke-virtual {v0, p1, p2, v1}, Lorg/codehaus/groovy/control/ErrorCollector;->addErrorAndContinue(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;Lorg/codehaus/groovy/control/SourceUnit;)V

    return-void
.end method

.method protected checkIncludeExclude(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/List;Ljava/lang/String;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/AnnotationNode;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            ">;",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            ">;",
            "Ljava/lang/String;",
            ")V"
        }
    .end annotation

    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    const/4 v0, 0x1

    if-eqz p3, :cond_0

    .line 424
    invoke-interface {p3}, Ljava/util/List;->isEmpty()Z

    move-result p3

    if-nez p3, :cond_0

    move p3, v0

    goto :goto_0

    :cond_0
    const/4 p3, 0x0

    :goto_0
    if-eqz p2, :cond_1

    .line 425
    invoke-interface {p2}, Ljava/util/List;->isEmpty()Z

    move-result p2

    if-nez p2, :cond_1

    add-int/lit8 p3, p3, 0x1

    :cond_1
    if-eqz p5, :cond_2

    .line 426
    invoke-interface {p5}, Ljava/util/List;->isEmpty()Z

    move-result p2

    if-nez p2, :cond_2

    add-int/lit8 p3, p3, 0x1

    :cond_2
    if-eqz p4, :cond_3

    .line 427
    invoke-interface {p4}, Ljava/util/List;->isEmpty()Z

    move-result p2

    if-nez p2, :cond_3

    add-int/lit8 p3, p3, 0x1

    :cond_3
    if-le p3, v0, :cond_4

    .line 429
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string p3, "Error during "

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, p6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string p3, " processing: Only one of \'includes\', \'excludes\', \'includeTypes\' and \'excludeTypes\' should be supplied."

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p2, p1}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    :cond_4
    return-void
.end method

.method protected checkIncludeExclude(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/util/List;Ljava/util/List;Ljava/lang/String;)Z
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/AnnotationNode;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;",
            "Ljava/lang/String;",
            ")Z"
        }
    .end annotation

    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    if-eqz p3, :cond_0

    .line 406
    invoke-interface {p3}, Ljava/util/List;->isEmpty()Z

    move-result p3

    if-nez p3, :cond_0

    if-eqz p2, :cond_0

    invoke-interface {p2}, Ljava/util/List;->isEmpty()Z

    move-result p2

    if-nez p2, :cond_0

    .line 407
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string p3, "Error during "

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, p4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string p3, " processing: Only one of \'includes\' and \'excludes\' should be supplied not both."

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p2, p1}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    const/4 p1, 0x0

    return p1

    :cond_0
    const/4 p1, 0x1

    return p1
.end method

.method protected checkIncludeExcludeUndefinedAware(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/List;Ljava/lang/String;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/AnnotationNode;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            ">;",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            ">;",
            "Ljava/lang/String;",
            ")V"
        }
    .end annotation

    const/4 v0, 0x1

    if-eqz p3, :cond_0

    move p3, v0

    goto :goto_0

    :cond_0
    const/4 p3, 0x0

    :goto_0
    if-eqz p2, :cond_1

    .line 437
    invoke-interface {p2}, Ljava/util/List;->isEmpty()Z

    move-result p2

    if-nez p2, :cond_1

    add-int/lit8 p3, p3, 0x1

    :cond_1
    if-eqz p5, :cond_2

    add-int/lit8 p3, p3, 0x1

    :cond_2
    if-eqz p4, :cond_3

    .line 439
    invoke-interface {p4}, Ljava/util/List;->isEmpty()Z

    move-result p2

    if-nez p2, :cond_3

    add-int/lit8 p3, p3, 0x1

    :cond_3
    if-le p3, v0, :cond_4

    .line 441
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string p3, "Error during "

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, p6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string p3, " processing: Only one of \'includes\', \'excludes\', \'includeTypes\' and \'excludeTypes\' should be supplied."

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p2, p1}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    :cond_4
    return-void
.end method

.method protected checkIncludeExcludeUndefinedAware(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/util/List;Ljava/util/List;Ljava/lang/String;)Z
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/AnnotationNode;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;",
            "Ljava/lang/String;",
            ")Z"
        }
    .end annotation

    if-eqz p3, :cond_0

    if-eqz p2, :cond_0

    .line 414
    invoke-interface {p2}, Ljava/util/List;->isEmpty()Z

    move-result p2

    if-nez p2, :cond_0

    .line 415
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string p3, "Error during "

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, p4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string p3, " processing: Only one of \'includes\' and \'excludes\' should be supplied not both."

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p2, p1}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    const/4 p1, 0x0

    return p1

    :cond_0
    const/4 p1, 0x1

    return p1
.end method

.method protected checkNotInterface(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Z
    .locals 2

    .line 259
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->isInterface()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 260
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Error processing interface \'"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "\'. "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string v0, " not allowed for interfaces."

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p2, p1}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    const/4 p1, 0x0

    return p1

    :cond_0
    const/4 p1, 0x1

    return p1
.end method

.method public checkPropertyList(Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/List;Ljava/lang/String;Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;Z)Z
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;",
            "Ljava/lang/String;",
            "Lorg/codehaus/groovy/ast/AnnotationNode;",
            "Ljava/lang/String;",
            "Z)Z"
        }
    .end annotation

    const/4 v7, 0x0

    const/4 v8, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    move-object v5, p5

    move v6, p6

    .line 446
    invoke-virtual/range {v0 .. v8}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->checkPropertyList(Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/List;Ljava/lang/String;Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;ZZZ)Z

    move-result p1

    return p1
.end method

.method public checkPropertyList(Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/List;Ljava/lang/String;Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;ZZZ)Z
    .locals 11
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;",
            "Ljava/lang/String;",
            "Lorg/codehaus/groovy/ast/AnnotationNode;",
            "Ljava/lang/String;",
            "ZZZ)Z"
        }
    .end annotation

    const/4 v9, 0x0

    const/4 v10, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    move-object/from16 v5, p5

    move/from16 v6, p6

    move/from16 v7, p7

    move/from16 v8, p8

    .line 450
    invoke-virtual/range {v0 .. v10}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->checkPropertyList(Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/List;Ljava/lang/String;Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;ZZZZZ)Z

    move-result v0

    return v0
.end method

.method public checkPropertyList(Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/List;Ljava/lang/String;Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;ZZZZZ)Z
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;",
            "Ljava/lang/String;",
            "Lorg/codehaus/groovy/ast/AnnotationNode;",
            "Ljava/lang/String;",
            "ZZZZZ)Z"
        }
    .end annotation

    const/4 v0, 0x1

    if-eqz p2, :cond_8

    .line 454
    invoke-interface {p2}, Ljava/util/List;->isEmpty()Z

    move-result v1

    if-eqz v1, :cond_0

    goto/16 :goto_5

    .line 457
    :cond_0
    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    .line 458
    invoke-static {p1, p7, p10, p8}, Lorg/codehaus/groovy/ast/tools/BeanUtils;->getAllProperties(Lorg/codehaus/groovy/ast/ClassNode;ZZZ)Ljava/util/List;

    move-result-object p7

    invoke-interface {p7}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p7

    :goto_0
    invoke-interface {p7}, Ljava/util/Iterator;->hasNext()Z

    move-result p8

    if-eqz p8, :cond_1

    invoke-interface {p7}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object p8

    check-cast p8, Lorg/codehaus/groovy/ast/PropertyNode;

    .line 459
    invoke-virtual {p8}, Lorg/codehaus/groovy/ast/PropertyNode;->getField()Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object p8

    invoke-virtual {p8}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object p8

    invoke-interface {v1, p8}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    :cond_1
    const/4 p7, 0x0

    const-string p8, "\' does not exist."

    const-string p10, " processing: \'"

    const-string v2, "Error during "

    if-nez p6, :cond_4

    if-eqz p9, :cond_2

    goto :goto_2

    .line 480
    :cond_2
    invoke-interface {p2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_3
    :goto_1
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result p2

    if-eqz p2, :cond_8

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Ljava/lang/String;

    .line 481
    invoke-interface {v1, p2}, Ljava/util/List;->contains(Ljava/lang/Object;)Z

    move-result p6

    if-nez p6, :cond_3

    .line 482
    new-instance p6, Ljava/lang/StringBuilder;

    invoke-direct {p6}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p6, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p6

    invoke-virtual {p6, p5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p6

    invoke-virtual {p6, p10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p6

    invoke-virtual {p6, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p6

    const-string p9, "\' property \'"

    invoke-virtual {p6, p9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p6

    invoke-virtual {p6, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, p8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p2, p4}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    move v0, p7

    goto :goto_1

    .line 463
    :cond_4
    :goto_2
    new-instance v3, Ljava/util/ArrayList;

    invoke-direct {v3}, Ljava/util/ArrayList;-><init>()V

    if-eqz p6, :cond_5

    .line 465
    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->getInstanceNonPropertyFieldNames(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/util/List;

    move-result-object p6

    invoke-interface {v3, p6}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    :cond_5
    if-eqz p9, :cond_6

    .line 468
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getSuperClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->getSuperNonPropertyFields(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/util/List;

    move-result-object p1

    .line 469
    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :goto_3
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result p6

    if-eqz p6, :cond_6

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object p6

    check-cast p6, Lorg/codehaus/groovy/ast/FieldNode;

    .line 470
    invoke-virtual {p6}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object p6

    invoke-interface {v3, p6}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_3

    .line 473
    :cond_6
    invoke-interface {p2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_7
    :goto_4
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result p2

    if-eqz p2, :cond_8

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Ljava/lang/String;

    .line 474
    invoke-interface {v1, p2}, Ljava/util/List;->contains(Ljava/lang/Object;)Z

    move-result p6

    if-nez p6, :cond_7

    invoke-interface {v3, p2}, Ljava/util/List;->contains(Ljava/lang/Object;)Z

    move-result p6

    if-nez p6, :cond_7

    .line 475
    new-instance p6, Ljava/lang/StringBuilder;

    invoke-direct {p6}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p6, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p6

    invoke-virtual {p6, p5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p6

    invoke-virtual {p6, p10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p6

    invoke-virtual {p6, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p6

    const-string p9, "\' property or field \'"

    invoke-virtual {p6, p9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p6

    invoke-virtual {p6, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, p8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p2, p4}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    move v0, p7

    goto :goto_4

    :cond_8
    :goto_5
    return v0
.end method

.method protected copyAnnotatedNodeAnnotations(Lorg/codehaus/groovy/ast/AnnotatedNode;Ljava/lang/String;)Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/AnnotatedNode;",
            "Ljava/lang/String;",
            ")",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/AnnotationNode;",
            ">;"
        }
    .end annotation

    const/4 v0, 0x1

    .line 68
    invoke-virtual {p0, p1, p2, v0}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->copyAnnotatedNodeAnnotations(Lorg/codehaus/groovy/ast/AnnotatedNode;Ljava/lang/String;Z)Ljava/util/List;

    move-result-object p1

    return-object p1
.end method

.method protected copyAnnotatedNodeAnnotations(Lorg/codehaus/groovy/ast/AnnotatedNode;Ljava/lang/String;Z)Ljava/util/List;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/AnnotatedNode;",
            "Ljava/lang/String;",
            "Z)",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/AnnotationNode;",
            ">;"
        }
    .end annotation

    .line 78
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 79
    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    .line 80
    invoke-static {p1, v0, v1, p3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->copyAnnotatedNodeAnnotations(Lorg/codehaus/groovy/ast/AnnotatedNode;Ljava/util/List;Ljava/util/List;Z)V

    .line 81
    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result p3

    if-eqz p3, :cond_0

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object p3

    check-cast p3, Lorg/codehaus/groovy/ast/AnnotationNode;

    .line 82
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v2, " does not support keeping Closure annotation members."

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0, v1, p3}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    goto :goto_0

    :cond_0
    return-object v0
.end method

.method public getAnnotationName()Ljava/lang/String;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public getClassList(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;)Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/AnnotationNode;",
            "Ljava/lang/String;",
            ")",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            ">;"
        }
    .end annotation

    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    .line 211
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 212
    invoke-virtual {p1, p2}, Lorg/codehaus/groovy/ast/AnnotationNode;->getMember(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    .line 213
    instance-of p2, p1, Lorg/codehaus/groovy/ast/expr/ListExpression;

    if-eqz p2, :cond_0

    .line 214
    check-cast p1, Lorg/codehaus/groovy/ast/expr/ListExpression;

    .line 215
    invoke-static {p1}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->getTypeList(Lorg/codehaus/groovy/ast/expr/ListExpression;)Ljava/util/List;

    move-result-object v0

    goto :goto_0

    .line 216
    :cond_0
    instance-of p2, p1, Lorg/codehaus/groovy/ast/expr/ClassExpression;

    if-eqz p2, :cond_1

    .line 217
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/Expression;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    if-eqz p1, :cond_1

    .line 218
    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :cond_1
    :goto_0
    return-object v0
.end method

.method public getMemberClassList(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;)Ljava/util/List;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/AnnotationNode;",
            "Ljava/lang/String;",
            ")",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            ">;"
        }
    .end annotation

    .line 224
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 225
    invoke-virtual {p1, p2}, Lorg/codehaus/groovy/ast/AnnotationNode;->getMember(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    const/4 p2, 0x0

    if-nez p1, :cond_0

    return-object p2

    .line 229
    :cond_0
    instance-of v1, p1, Lorg/codehaus/groovy/ast/expr/ListExpression;

    if-eqz v1, :cond_2

    .line 230
    check-cast p1, Lorg/codehaus/groovy/ast/expr/ListExpression;

    .line 231
    invoke-static {p1}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->isUndefinedMarkerList(Lorg/codehaus/groovy/ast/expr/ListExpression;)Z

    move-result v0

    if-eqz v0, :cond_1

    return-object p2

    .line 234
    :cond_1
    invoke-static {p1}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->getTypeList(Lorg/codehaus/groovy/ast/expr/ListExpression;)Ljava/util/List;

    move-result-object v0

    goto :goto_0

    .line 235
    :cond_2
    instance-of v1, p1, Lorg/codehaus/groovy/ast/expr/ClassExpression;

    if-eqz v1, :cond_4

    .line 236
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/Expression;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    .line 237
    invoke-static {p1}, Lgroovy/transform/Undefined;->isUndefined(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v1

    if-eqz v1, :cond_3

    return-object p2

    :cond_3
    if-eqz p1, :cond_4

    .line 238
    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :cond_4
    :goto_0
    return-object v0
.end method

.method public getMemberClassValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/ClassNode;
    .locals 1

    const/4 v0, 0x0

    .line 137
    invoke-virtual {p0, p1, p2, v0}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->getMemberClassValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    return-object p1
.end method

.method public getMemberClassValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/ClassNode;
    .locals 4

    .line 141
    invoke-virtual {p1, p2}, Lorg/codehaus/groovy/ast/AnnotationNode;->getMember(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    if-eqz v0, :cond_2

    .line 143
    instance-of v1, v0, Lorg/codehaus/groovy/ast/expr/ClassExpression;

    if-eqz v1, :cond_0

    .line 144
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/Expression;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    invoke-static {p1}, Lgroovy/transform/Undefined;->isUndefined(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result p1

    if-nez p1, :cond_2

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/Expression;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    return-object p1

    .line 145
    :cond_0
    instance-of v1, v0, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    const/4 v2, 0x0

    const-string v3, "Error expecting to find class value for \'"

    if-eqz v1, :cond_1

    .line 146
    new-instance p3, Ljava/lang/StringBuilder;

    invoke-direct {p3}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p3, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p3

    invoke-virtual {p3, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string p3, "\' but found variable: "

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/Expression;->getText()Ljava/lang/String;

    move-result-object p3

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string p3, ". Missing import?"

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p2, p1}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    return-object v2

    .line 148
    :cond_1
    instance-of v1, v0, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    if-eqz v1, :cond_2

    .line 149
    new-instance p3, Ljava/lang/StringBuilder;

    invoke-direct {p3}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p3, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p3

    invoke-virtual {p3, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string p3, "\' but found constant: "

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/Expression;->getText()Ljava/lang/String;

    move-result-object p3

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string p3, "!"

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p2, p1}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    return-object v2

    :cond_2
    return-object p3
.end method

.method public getMemberIntValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;)I
    .locals 0

    .line 129
    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->getMemberValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1

    .line 130
    instance-of p2, p1, Ljava/lang/Integer;

    if-eqz p2, :cond_0

    .line 131
    check-cast p1, Ljava/lang/Integer;

    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    move-result p1

    return p1

    :cond_0
    const/4 p1, 0x0

    return p1
.end method

.method public getMemberValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;)Ljava/lang/Object;
    .locals 0

    .line 109
    invoke-virtual {p1, p2}, Lorg/codehaus/groovy/ast/AnnotationNode;->getMember(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    .line 110
    instance-of p2, p1, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    if-eqz p2, :cond_0

    check-cast p1, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/ConstantExpression;->getValue()Ljava/lang/Object;

    move-result-object p1

    return-object p1

    :cond_0
    const/4 p1, 0x0

    return-object p1
.end method

.method public hasAnnotation(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;)Z
    .locals 0

    .line 268
    invoke-static {p1, p2}, Lorg/apache/groovy/ast/tools/AnnotatedNodeUtils;->hasAnnotation(Lorg/codehaus/groovy/ast/AnnotatedNode;Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result p1

    return p1
.end method

.method protected init([Lorg/codehaus/groovy/ast/ASTNode;Lorg/codehaus/groovy/control/SourceUnit;)V
    .locals 2

    if-eqz p1, :cond_1

    .line 97
    array-length v0, p1

    const/4 v1, 0x2

    if-ne v0, v1, :cond_1

    const/4 v0, 0x0

    aget-object v0, p1, v0

    instance-of v0, v0, Lorg/codehaus/groovy/ast/AnnotationNode;

    if-eqz v0, :cond_1

    const/4 v0, 0x1

    aget-object v0, p1, v0

    instance-of v0, v0, Lorg/codehaus/groovy/ast/AnnotatedNode;

    if-nez v0, :cond_0

    goto :goto_0

    .line 100
    :cond_0
    iput-object p2, p0, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->sourceUnit:Lorg/codehaus/groovy/control/SourceUnit;

    return-void

    .line 98
    :cond_1
    :goto_0
    new-instance p2, Lorg/codehaus/groovy/GroovyBugError;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Internal error: expecting [AnnotationNode, AnnotatedNode] but got: "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    if-nez p1, :cond_2

    const/4 p1, 0x0

    goto :goto_1

    :cond_2
    invoke-static {p1}, Ljava/util/Arrays;->asList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object p1

    :goto_1
    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {p2, p1}, Lorg/codehaus/groovy/GroovyBugError;-><init>(Ljava/lang/String;)V

    throw p2
.end method

.method public memberHasValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;Ljava/lang/Object;)Z
    .locals 0

    .line 104
    invoke-virtual {p1, p2}, Lorg/codehaus/groovy/ast/AnnotationNode;->getMember(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    .line 105
    instance-of p2, p1, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    if-eqz p2, :cond_0

    check-cast p1, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/ConstantExpression;->getValue()Ljava/lang/Object;

    move-result-object p1

    invoke-virtual {p1, p3}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method
