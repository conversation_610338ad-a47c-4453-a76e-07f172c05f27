.class public Lorg/codehaus/groovy/transform/AutoFinalASTTransformation;
.super Lorg/codehaus/groovy/transform/AbstractASTTransformation;
.source "AutoFinalASTTransformation.java"


# annotations
.annotation runtime Lorg/codehaus/groovy/transform/GroovyASTTransformation;
    phase = .enum Lorg/codehaus/groovy/control/CompilePhase;->SEMANTIC_ANALYSIS:Lorg/codehaus/groovy/control/CompilePhase;
.end annotation


# static fields
.field private static final MY_CLASS:Ljava/lang/Class;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/lang/Class<",
            "*>;"
        }
    .end annotation
.end field

.field private static final MY_TYPE:Lorg/codehaus/groovy/ast/ClassNode;


# instance fields
.field private target:Lorg/codehaus/groovy/ast/AnnotatedNode;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 48
    const-class v0, Lgroovy/transform/AutoFinal;

    sput-object v0, Lorg/codehaus/groovy/transform/AutoFinalASTTransformation;->MY_CLASS:Ljava/lang/Class;

    .line 49
    invoke-static {v0}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/transform/AutoFinalASTTransformation;->MY_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 46
    invoke-direct {p0}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;-><init>()V

    return-void
.end method

.method static synthetic access$000(Lorg/codehaus/groovy/transform/AutoFinalASTTransformation;)Lorg/codehaus/groovy/ast/AnnotatedNode;
    .locals 0

    .line 46
    iget-object p0, p0, Lorg/codehaus/groovy/transform/AutoFinalASTTransformation;->target:Lorg/codehaus/groovy/ast/AnnotatedNode;

    return-object p0
.end method

.method static synthetic access$100(Lorg/codehaus/groovy/ast/AnnotatedNode;)Z
    .locals 0

    .line 46
    invoke-static {p0}, Lorg/codehaus/groovy/transform/AutoFinalASTTransformation;->hasNoExplicitAutoFinal(Lorg/codehaus/groovy/ast/AnnotatedNode;)Z

    move-result p0

    return p0
.end method

.method private createVisitor()Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;
    .locals 1

    .line 131
    new-instance v0, Lorg/codehaus/groovy/transform/AutoFinalASTTransformation$1;

    invoke-direct {v0, p0}, Lorg/codehaus/groovy/transform/AutoFinalASTTransformation$1;-><init>(Lorg/codehaus/groovy/transform/AutoFinalASTTransformation;)V

    return-object v0
.end method

.method private static hasNoExplicitAutoFinal(Lorg/codehaus/groovy/ast/AnnotatedNode;)Z
    .locals 1

    .line 179
    sget-object v0, Lorg/codehaus/groovy/transform/AutoFinalASTTransformation;->MY_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/ast/AnnotatedNode;->getAnnotations(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/util/List;

    move-result-object p0

    invoke-interface {p0}, Ljava/util/List;->isEmpty()Z

    move-result p0

    return p0
.end method

.method private isEnabled(Lorg/codehaus/groovy/ast/AnnotatedNode;)Z
    .locals 1

    if-eqz p1, :cond_0

    .line 174
    sget-object v0, Lorg/codehaus/groovy/transform/AutoFinalASTTransformation;->MY_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {p1, v0}, Lorg/codehaus/groovy/ast/AnnotatedNode;->getAnnotations(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/util/List;

    move-result-object p1

    invoke-interface {p1}, Ljava/util/List;->stream()Ljava/util/stream/Stream;

    move-result-object p1

    new-instance v0, Lorg/codehaus/groovy/transform/AutoFinalASTTransformation$$ExternalSyntheticLambda0;

    invoke-direct {v0, p0}, Lorg/codehaus/groovy/transform/AutoFinalASTTransformation$$ExternalSyntheticLambda0;-><init>(Lorg/codehaus/groovy/transform/AutoFinalASTTransformation;)V

    .line 175
    invoke-interface {p1, v0}, Ljava/util/stream/Stream;->noneMatch(Ljava/util/function/Predicate;)Z

    move-result p1

    if-eqz p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method private process([Lorg/codehaus/groovy/ast/ASTNode;Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;)V
    .locals 2

    const/4 v0, 0x1

    .line 60
    aget-object v0, p1, v0

    check-cast v0, Lorg/codehaus/groovy/ast/AnnotatedNode;

    iput-object v0, p0, Lorg/codehaus/groovy/transform/AutoFinalASTTransformation;->target:Lorg/codehaus/groovy/ast/AnnotatedNode;

    const/4 v0, 0x0

    .line 61
    aget-object p1, p1, v0

    check-cast p1, Lorg/codehaus/groovy/ast/AnnotationNode;

    .line 62
    sget-object v0, Lorg/codehaus/groovy/transform/AutoFinalASTTransformation;->MY_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/AnnotationNode;->getClassNode()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    invoke-virtual {v0, v1}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_0

    return-void

    .line 63
    :cond_0
    sget-object v0, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    const-string v1, "enabled"

    invoke-virtual {p0, p1, v1, v0}, Lorg/codehaus/groovy/transform/AutoFinalASTTransformation;->memberHasValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_1

    return-void

    .line 65
    :cond_1
    iget-object p1, p0, Lorg/codehaus/groovy/transform/AutoFinalASTTransformation;->target:Lorg/codehaus/groovy/ast/AnnotatedNode;

    instance-of v0, p1, Lorg/codehaus/groovy/ast/ClassNode;

    if-eqz v0, :cond_2

    .line 66
    check-cast p1, Lorg/codehaus/groovy/ast/ClassNode;

    invoke-direct {p0, p1, p2}, Lorg/codehaus/groovy/transform/AutoFinalASTTransformation;->processClass(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;)V

    goto :goto_0

    .line 67
    :cond_2
    instance-of v0, p1, Lorg/codehaus/groovy/ast/FieldNode;

    if-eqz v0, :cond_3

    .line 68
    check-cast p1, Lorg/codehaus/groovy/ast/FieldNode;

    invoke-direct {p0, p1, p2}, Lorg/codehaus/groovy/transform/AutoFinalASTTransformation;->processField(Lorg/codehaus/groovy/ast/FieldNode;Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;)V

    goto :goto_0

    .line 69
    :cond_3
    instance-of v0, p1, Lorg/codehaus/groovy/ast/MethodNode;

    if-eqz v0, :cond_4

    .line 70
    check-cast p1, Lorg/codehaus/groovy/ast/MethodNode;

    invoke-direct {p0, p1, p2}, Lorg/codehaus/groovy/transform/AutoFinalASTTransformation;->processConstructorOrMethod(Lorg/codehaus/groovy/ast/MethodNode;Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;)V

    goto :goto_0

    .line 71
    :cond_4
    instance-of v0, p1, Lorg/codehaus/groovy/ast/expr/DeclarationExpression;

    if-eqz v0, :cond_5

    .line 72
    check-cast p1, Lorg/codehaus/groovy/ast/expr/DeclarationExpression;

    invoke-direct {p0, p1, p2}, Lorg/codehaus/groovy/transform/AutoFinalASTTransformation;->processLocalVariable(Lorg/codehaus/groovy/ast/expr/DeclarationExpression;Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;)V

    :cond_5
    :goto_0
    return-void
.end method

.method private processClass(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;)V
    .locals 3

    .line 77
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/transform/AutoFinalASTTransformation;->isEnabled(Lorg/codehaus/groovy/ast/AnnotatedNode;)Z

    move-result v0

    if-nez v0, :cond_0

    return-void

    .line 79
    :cond_0
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->isInterface()Z

    move-result v0

    if-eqz v0, :cond_1

    .line 80
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "Error processing interface \'"

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string v0, "\'. @"

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    sget-object v0, Lorg/codehaus/groovy/transform/AutoFinalASTTransformation;->MY_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->getNameWithoutPackage()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string v0, " only allowed for classes."

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p2, p1}, Lorg/codehaus/groovy/transform/AutoFinalASTTransformation;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    return-void

    .line 84
    :cond_1
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getDeclaredConstructors()Ljava/util/List;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_2
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_3

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/ast/ConstructorNode;

    .line 85
    invoke-static {v1}, Lorg/codehaus/groovy/transform/AutoFinalASTTransformation;->hasNoExplicitAutoFinal(Lorg/codehaus/groovy/ast/AnnotatedNode;)Z

    move-result v2

    if-eqz v2, :cond_2

    .line 86
    invoke-direct {p0, v1, p2}, Lorg/codehaus/groovy/transform/AutoFinalASTTransformation;->processConstructorOrMethod(Lorg/codehaus/groovy/ast/MethodNode;Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;)V

    goto :goto_0

    .line 90
    :cond_3
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getAllDeclaredMethods()Ljava/util/List;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_4
    :goto_1
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_5

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/ast/MethodNode;

    .line 91
    invoke-static {v1}, Lorg/codehaus/groovy/transform/AutoFinalASTTransformation;->hasNoExplicitAutoFinal(Lorg/codehaus/groovy/ast/AnnotatedNode;)Z

    move-result v2

    if-eqz v2, :cond_4

    .line 92
    invoke-direct {p0, v1, p2}, Lorg/codehaus/groovy/transform/AutoFinalASTTransformation;->processConstructorOrMethod(Lorg/codehaus/groovy/ast/MethodNode;Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;)V

    goto :goto_1

    .line 96
    :cond_5
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getInnerClasses()Ljava/util/Iterator;

    move-result-object v0

    :cond_6
    :goto_2
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_7

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/ast/ClassNode;

    .line 97
    invoke-static {v1}, Lorg/codehaus/groovy/transform/AutoFinalASTTransformation;->hasNoExplicitAutoFinal(Lorg/codehaus/groovy/ast/AnnotatedNode;)Z

    move-result v2

    if-eqz v2, :cond_6

    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/ClassNode;->isInterface()Z

    move-result v2

    if-nez v2, :cond_6

    .line 98
    invoke-direct {p0, v1, p2}, Lorg/codehaus/groovy/transform/AutoFinalASTTransformation;->processClass(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;)V

    goto :goto_2

    .line 102
    :cond_7
    invoke-virtual {p2, p1}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;->visitClass(Lorg/codehaus/groovy/ast/ClassNode;)V

    return-void
.end method

.method private processConstructorOrMethod(Lorg/codehaus/groovy/ast/MethodNode;Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;)V
    .locals 5

    .line 113
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/transform/AutoFinalASTTransformation;->isEnabled(Lorg/codehaus/groovy/ast/AnnotatedNode;)Z

    move-result v0

    if-nez v0, :cond_0

    return-void

    .line 114
    :cond_0
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->isSynthetic()Z

    move-result v0

    if-eqz v0, :cond_1

    return-void

    .line 115
    :cond_1
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v0

    array-length v1, v0

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v1, :cond_2

    aget-object v3, v0, v2

    .line 116
    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/Parameter;->getModifiers()I

    move-result v4

    or-int/lit8 v4, v4, 0x10

    invoke-virtual {v3, v4}, Lorg/codehaus/groovy/ast/Parameter;->setModifiers(I)V

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 118
    :cond_2
    invoke-virtual {p2, p1}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;->visitMethod(Lorg/codehaus/groovy/ast/MethodNode;)V

    return-void
.end method

.method private processField(Lorg/codehaus/groovy/ast/FieldNode;Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;)V
    .locals 1

    .line 106
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/transform/AutoFinalASTTransformation;->isEnabled(Lorg/codehaus/groovy/ast/AnnotatedNode;)Z

    move-result v0

    if-nez v0, :cond_0

    return-void

    .line 107
    :cond_0
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/FieldNode;->hasInitialExpression()Z

    move-result v0

    if-eqz v0, :cond_1

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/FieldNode;->getInitialExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    instance-of v0, v0, Lorg/codehaus/groovy/ast/expr/ClosureExpression;

    if-eqz v0, :cond_1

    .line 108
    invoke-virtual {p2, p1}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;->visitField(Lorg/codehaus/groovy/ast/FieldNode;)V

    :cond_1
    return-void
.end method

.method private processLocalVariable(Lorg/codehaus/groovy/ast/expr/DeclarationExpression;Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;)V
    .locals 1

    .line 122
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/transform/AutoFinalASTTransformation;->isEnabled(Lorg/codehaus/groovy/ast/AnnotatedNode;)Z

    move-result v0

    if-nez v0, :cond_0

    return-void

    .line 123
    :cond_0
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/DeclarationExpression;->getRightExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    instance-of v0, v0, Lorg/codehaus/groovy/ast/expr/ClosureExpression;

    if-eqz v0, :cond_1

    .line 124
    invoke-virtual {p2, p1}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;->visitDeclarationExpression(Lorg/codehaus/groovy/ast/expr/DeclarationExpression;)V

    :cond_1
    return-void
.end method


# virtual methods
.method public synthetic lambda$isEnabled$0$org-codehaus-groovy-transform-AutoFinalASTTransformation(Lorg/codehaus/groovy/ast/AnnotationNode;)Z
    .locals 2

    .line 175
    sget-object v0, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    const-string v1, "enabled"

    invoke-virtual {p0, p1, v1, v0}, Lorg/codehaus/groovy/transform/AutoFinalASTTransformation;->memberHasValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;Ljava/lang/Object;)Z

    move-result p1

    return p1
.end method

.method public visit([Lorg/codehaus/groovy/ast/ASTNode;Lorg/codehaus/groovy/control/SourceUnit;)V
    .locals 0

    .line 55
    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/transform/AutoFinalASTTransformation;->init([Lorg/codehaus/groovy/ast/ASTNode;Lorg/codehaus/groovy/control/SourceUnit;)V

    .line 56
    invoke-direct {p0}, Lorg/codehaus/groovy/transform/AutoFinalASTTransformation;->createVisitor()Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;

    move-result-object p2

    invoke-direct {p0, p1, p2}, Lorg/codehaus/groovy/transform/AutoFinalASTTransformation;->process([Lorg/codehaus/groovy/ast/ASTNode;Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;)V

    return-void
.end method
