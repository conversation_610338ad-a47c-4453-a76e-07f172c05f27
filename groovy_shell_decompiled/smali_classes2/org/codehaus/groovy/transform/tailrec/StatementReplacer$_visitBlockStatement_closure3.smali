.class public final Lorg/codehaus/groovy/transform/tailrec/StatementReplacer$_visitBlockStatement_closure3;
.super Lgroovy/lang/Closure;
.source "StatementReplacer.groovy"

# interfaces
.implements Lorg/codehaus/groovy/runtime/GeneratedClosure;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/codehaus/groovy/transform/tailrec/StatementReplacer;->visitBlockStatement(Lorg/codehaus/groovy/ast/stmt/BlockStatement;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x11
    name = "_visitBlockStatement_closure3"
.end annotation


# static fields
.field private static synthetic $staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

.field public static transient synthetic __$stMC:Z


# instance fields
.field private synthetic block:Lgroovy/lang/Reference;


# direct methods
.method public constructor <init>(Ljava/lang/Object;Ljava/lang/Object;Lgroovy/lang/Reference;)V
    .locals 0

    invoke-direct {p0, p1, p2}, Lgroovy/lang/Closure;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    iput-object p3, p0, Lorg/codehaus/groovy/transform/tailrec/StatementReplacer$_visitBlockStatement_closure3;->block:Lgroovy/lang/Reference;

    return-void
.end method


# virtual methods
.method protected synthetic $getStaticMetaClass()Lgroovy/lang/MetaClass;
    .locals 2

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    const-class v1, Lorg/codehaus/groovy/transform/tailrec/StatementReplacer$_visitBlockStatement_closure3;

    if-eq v0, v1, :cond_0

    invoke-static {p0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->initMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0

    :cond_0
    sget-object v0, Lorg/codehaus/groovy/transform/tailrec/StatementReplacer$_visitBlockStatement_closure3;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    if-nez v0, :cond_1

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getClassInfo(Ljava/lang/Class;)Lorg/codehaus/groovy/reflection/ClassInfo;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/transform/tailrec/StatementReplacer$_visitBlockStatement_closure3;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    :cond_1
    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0
.end method

.method public call(Lorg/codehaus/groovy/ast/stmt/Statement;I)Ljava/lang/Object;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p2

    new-instance v0, Lgroovy/lang/Reference;

    invoke-direct {v0, p2}, Lgroovy/lang/Reference;-><init>(Ljava/lang/Object;)V

    invoke-virtual {v0}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object p2

    invoke-static {p2}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->intUnbox(Ljava/lang/Object;)I

    move-result p2

    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/transform/tailrec/StatementReplacer$_visitBlockStatement_closure3;->doCall(Lorg/codehaus/groovy/ast/stmt/Statement;I)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public doCall(Lorg/codehaus/groovy/ast/stmt/Statement;I)Ljava/lang/Object;
    .locals 4

    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p2

    new-instance v0, Lgroovy/lang/Reference;

    invoke-direct {v0, p2}, Lgroovy/lang/Reference;-><init>(Ljava/lang/Object;)V

    .line 60
    move-object p2, p0

    check-cast p2, Lorg/codehaus/groovy/transform/tailrec/StatementReplacer$_visitBlockStatement_closure3;

    invoke-virtual {p0}, Lorg/codehaus/groovy/transform/tailrec/StatementReplacer$_visitBlockStatement_closure3;->getThisObject()Ljava/lang/Object;

    move-result-object p2

    const-class v1, Lorg/codehaus/groovy/transform/tailrec/StatementReplacer;

    invoke-static {p2, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lorg/codehaus/groovy/transform/tailrec/StatementReplacer;

    new-instance v1, Lorg/codehaus/groovy/transform/tailrec/StatementReplacer$_visitBlockStatement_closure3$_closure9;

    invoke-virtual {p0}, Lgroovy/lang/Closure;->getThisObject()Ljava/lang/Object;

    move-result-object v2

    iget-object v3, p0, Lorg/codehaus/groovy/transform/tailrec/StatementReplacer$_visitBlockStatement_closure3;->block:Lgroovy/lang/Reference;

    invoke-direct {v1, p0, v2, v3, v0}, Lorg/codehaus/groovy/transform/tailrec/StatementReplacer$_visitBlockStatement_closure3$_closure9;-><init>(Ljava/lang/Object;Ljava/lang/Object;Lgroovy/lang/Reference;Lgroovy/lang/Reference;)V

    invoke-static {p2, p1, v1}, Lorg/codehaus/groovy/transform/tailrec/StatementReplacer;->access$0(Lorg/codehaus/groovy/transform/tailrec/StatementReplacer;Lorg/codehaus/groovy/ast/stmt/Statement;Lgroovy/lang/Closure;)V

    const/4 p1, 0x0

    return-object p1
.end method

.method public getBlock()Lorg/codehaus/groovy/ast/stmt/BlockStatement;
    .locals 2
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-object v0, p0, Lorg/codehaus/groovy/transform/tailrec/StatementReplacer$_visitBlockStatement_closure3;->block:Lgroovy/lang/Reference;

    invoke-virtual {v0}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    const-class v1, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    invoke-static {v0, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    return-object v0
.end method
