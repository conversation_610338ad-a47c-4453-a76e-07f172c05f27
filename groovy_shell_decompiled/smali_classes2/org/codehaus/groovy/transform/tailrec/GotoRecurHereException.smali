.class public Lorg/codehaus/groovy/transform/tailrec/GotoRecurHereException;
.super Ljava/lang/Exception;
.source "InWhileLoopWrapper.groovy"

# interfaces
.implements Lgroovy/lang/GroovyObject;


# static fields
.field private static synthetic $const$0:J = 0x0L

.field private static synthetic $staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo; = null

.field private static synthetic $staticClassInfo$:Lorg/codehaus/groovy/reflection/ClassInfo; = null

.field public static transient synthetic __$stMC:Z = false

.field private static final serialVersionUID:J = -0x2ae291bd8f8230aL


# instance fields
.field private transient synthetic metaClass:Lgroovy/lang/MetaClass;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    invoke-static {}, Lorg/codehaus/groovy/transform/tailrec/GotoRecurHereException;->__$swapInit()V

    return-void
.end method

.method public constructor <init>()V
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    invoke-direct {p0}, Ljava/lang/Exception;-><init>()V

    invoke-virtual {p0}, Lorg/codehaus/groovy/transform/tailrec/GotoRecurHereException;->$getStaticMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/transform/tailrec/GotoRecurHereException;->metaClass:Lgroovy/lang/MetaClass;

    return-void
.end method

.method public static synthetic __$swapInit()V
    .locals 2

    const-wide v0, -0x2ae291bd8f8230aL    # -4.557392793628382E295

    sput-wide v0, Lorg/codehaus/groovy/transform/tailrec/GotoRecurHereException;->$const$0:J

    return-void
.end method


# virtual methods
.method protected synthetic $getStaticMetaClass()Lgroovy/lang/MetaClass;
    .locals 2

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    const-class v1, Lorg/codehaus/groovy/transform/tailrec/GotoRecurHereException;

    if-eq v0, v1, :cond_0

    invoke-static {p0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->initMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0

    :cond_0
    sget-object v0, Lorg/codehaus/groovy/transform/tailrec/GotoRecurHereException;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    if-nez v0, :cond_1

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getClassInfo(Ljava/lang/Class;)Lorg/codehaus/groovy/reflection/ClassInfo;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/transform/tailrec/GotoRecurHereException;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    :cond_1
    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0
.end method

.method public getMetaClass()Lgroovy/lang/MetaClass;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    .annotation runtime Lgroovy/transform/Internal;
    .end annotation

    .annotation runtime Lgroovyjarjaropenbeans/Transient;
    .end annotation

    iget-object v0, p0, Lorg/codehaus/groovy/transform/tailrec/GotoRecurHereException;->metaClass:Lgroovy/lang/MetaClass;

    if-eqz v0, :cond_0

    return-object v0

    :cond_0
    invoke-virtual {p0}, Lorg/codehaus/groovy/transform/tailrec/GotoRecurHereException;->$getStaticMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/transform/tailrec/GotoRecurHereException;->metaClass:Lgroovy/lang/MetaClass;

    return-object v0
.end method

.method public setMetaClass(Lgroovy/lang/MetaClass;)V
    .locals 0
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    .annotation runtime Lgroovy/transform/Internal;
    .end annotation

    iput-object p1, p0, Lorg/codehaus/groovy/transform/tailrec/GotoRecurHereException;->metaClass:Lgroovy/lang/MetaClass;

    return-void
.end method
