.class public final Lorg/codehaus/groovy/transform/tailrec/StatementReplacer$_visitDoWhileLoop_closure8;
.super Lgroovy/lang/Closure;
.source "StatementReplacer.groovy"

# interfaces
.implements Lorg/codehaus/groovy/runtime/GeneratedClosure;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/codehaus/groovy/transform/tailrec/StatementReplacer;->visitDoWhileLoop(Lorg/codehaus/groovy/ast/stmt/DoWhileStatement;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x11
    name = "_visitDoWhileLoop_closure8"
.end annotation


# static fields
.field private static synthetic $staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

.field public static transient synthetic __$stMC:Z


# instance fields
.field private synthetic loop:Lgroovy/lang/Reference;


# direct methods
.method public constructor <init>(Ljava/lang/Object;Ljava/lang/Object;Lgroovy/lang/Reference;)V
    .locals 0

    invoke-direct {p0, p1, p2}, Lgroovy/lang/Closure;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    iput-object p3, p0, Lorg/codehaus/groovy/transform/tailrec/StatementReplacer$_visitDoWhileLoop_closure8;->loop:Lgroovy/lang/Reference;

    return-void
.end method


# virtual methods
.method protected synthetic $getStaticMetaClass()Lgroovy/lang/MetaClass;
    .locals 2

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    const-class v1, Lorg/codehaus/groovy/transform/tailrec/StatementReplacer$_visitDoWhileLoop_closure8;

    if-eq v0, v1, :cond_0

    invoke-static {p0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->initMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0

    :cond_0
    sget-object v0, Lorg/codehaus/groovy/transform/tailrec/StatementReplacer$_visitDoWhileLoop_closure8;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    if-nez v0, :cond_1

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getClassInfo(Ljava/lang/Class;)Lorg/codehaus/groovy/reflection/ClassInfo;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/transform/tailrec/StatementReplacer$_visitDoWhileLoop_closure8;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    :cond_1
    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0
.end method

.method public call(Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/Statement;
    .locals 0
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/transform/tailrec/StatementReplacer$_visitDoWhileLoop_closure8;->doCall(Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    return-object p1
.end method

.method public doCall(Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/Statement;
    .locals 1

    .line 82
    iget-object v0, p0, Lorg/codehaus/groovy/transform/tailrec/StatementReplacer$_visitDoWhileLoop_closure8;->loop:Lgroovy/lang/Reference;

    invoke-virtual {v0}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/ast/stmt/DoWhileStatement;

    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/ast/stmt/DoWhileStatement;->setLoopBlock(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    return-object p1
.end method

.method public getLoop()Lorg/codehaus/groovy/ast/stmt/DoWhileStatement;
    .locals 2
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-object v0, p0, Lorg/codehaus/groovy/transform/tailrec/StatementReplacer$_visitDoWhileLoop_closure8;->loop:Lgroovy/lang/Reference;

    invoke-virtual {v0}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    const-class v1, Lorg/codehaus/groovy/ast/stmt/DoWhileStatement;

    invoke-static {v0, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/ast/stmt/DoWhileStatement;

    return-object v0
.end method
