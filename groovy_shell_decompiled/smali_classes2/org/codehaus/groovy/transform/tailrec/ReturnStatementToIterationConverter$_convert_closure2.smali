.class public final Lorg/codehaus/groovy/transform/tailrec/ReturnStatementToIterationConverter$_convert_closure2;
.super Lgroovy/lang/Closure;
.source "ReturnStatementToIterationConverter.groovy"

# interfaces
.implements Lorg/codehaus/groovy/runtime/GeneratedClosure;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/codehaus/groovy/transform/tailrec/ReturnStatementToIterationConverter;->convert(Lorg/codehaus/groovy/ast/stmt/ReturnStatement;Ljava/util/Map;)Lorg/codehaus/groovy/ast/stmt/Statement;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x11
    name = "_convert_closure2"
.end annotation


# static fields
.field private static synthetic $staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

.field public static transient synthetic __$stMC:Z


# instance fields
.field private synthetic argAssignments:Lgroovy/lang/Reference;

.field private synthetic positionMapping:Lgroovy/lang/Reference;

.field private synthetic result:Lgroovy/lang/Reference;


# direct methods
.method public constructor <init>(Ljava/lang/Object;Ljava/lang/Object;Lgroovy/lang/Reference;Lgroovy/lang/Reference;Lgroovy/lang/Reference;)V
    .locals 0

    invoke-direct {p0, p1, p2}, Lgroovy/lang/Closure;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    iput-object p3, p0, Lorg/codehaus/groovy/transform/tailrec/ReturnStatementToIterationConverter$_convert_closure2;->positionMapping:Lgroovy/lang/Reference;

    iput-object p4, p0, Lorg/codehaus/groovy/transform/tailrec/ReturnStatementToIterationConverter$_convert_closure2;->argAssignments:Lgroovy/lang/Reference;

    iput-object p5, p0, Lorg/codehaus/groovy/transform/tailrec/ReturnStatementToIterationConverter$_convert_closure2;->result:Lgroovy/lang/Reference;

    return-void
.end method


# virtual methods
.method protected synthetic $getStaticMetaClass()Lgroovy/lang/MetaClass;
    .locals 2

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    const-class v1, Lorg/codehaus/groovy/transform/tailrec/ReturnStatementToIterationConverter$_convert_closure2;

    if-eq v0, v1, :cond_0

    invoke-static {p0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->initMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0

    :cond_0
    sget-object v0, Lorg/codehaus/groovy/transform/tailrec/ReturnStatementToIterationConverter$_convert_closure2;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    if-nez v0, :cond_1

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getClassInfo(Ljava/lang/Class;)Lorg/codehaus/groovy/reflection/ClassInfo;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/transform/tailrec/ReturnStatementToIterationConverter$_convert_closure2;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    :cond_1
    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0
.end method

.method public call(Lorg/codehaus/groovy/ast/expr/Expression;I)Ljava/lang/Object;
    .locals 0
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/transform/tailrec/ReturnStatementToIterationConverter$_convert_closure2;->doCall(Lorg/codehaus/groovy/ast/expr/Expression;I)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public doCall(Lorg/codehaus/groovy/ast/expr/Expression;I)Ljava/lang/Object;
    .locals 3

    .line 78
    move-object v0, p0

    check-cast v0, Lorg/codehaus/groovy/transform/tailrec/ReturnStatementToIterationConverter$_convert_closure2;

    invoke-virtual {p0}, Lorg/codehaus/groovy/transform/tailrec/ReturnStatementToIterationConverter$_convert_closure2;->getThisObject()Ljava/lang/Object;

    move-result-object v0

    const-class v1, Lorg/codehaus/groovy/transform/tailrec/ReturnStatementToIterationConverter;

    invoke-static {v0, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/transform/tailrec/ReturnStatementToIterationConverter;

    iget-object v1, p0, Lorg/codehaus/groovy/transform/tailrec/ReturnStatementToIterationConverter$_convert_closure2;->positionMapping:Lgroovy/lang/Reference;

    invoke-virtual {v1}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v1

    const-class v2, Ljava/util/Map;

    invoke-static {v1, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/util/Map;

    invoke-static {v0, p1, p2, v1}, Lorg/codehaus/groovy/transform/tailrec/ReturnStatementToIterationConverter;->access$0(Lorg/codehaus/groovy/transform/tailrec/ReturnStatementToIterationConverter;Lorg/codehaus/groovy/ast/expr/Expression;ILjava/util/Map;)Lorg/codehaus/groovy/ast/stmt/ExpressionStatement;

    move-result-object p1

    .line 79
    iget-object p2, p0, Lorg/codehaus/groovy/transform/tailrec/ReturnStatementToIterationConverter$_convert_closure2;->argAssignments:Lgroovy/lang/Reference;

    invoke-virtual {p2}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Ljava/util/ArrayList;

    invoke-virtual {p2, p1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 80
    iget-object p2, p0, Lorg/codehaus/groovy/transform/tailrec/ReturnStatementToIterationConverter$_convert_closure2;->result:Lgroovy/lang/Reference;

    invoke-virtual {p2}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    invoke-virtual {p2, p1}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    const/4 p1, 0x0

    return-object p1
.end method

.method public getArgAssignments()Ljava/util/List;
    .locals 2
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-object v0, p0, Lorg/codehaus/groovy/transform/tailrec/ReturnStatementToIterationConverter$_convert_closure2;->argAssignments:Lgroovy/lang/Reference;

    invoke-virtual {v0}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    const-class v1, Ljava/util/List;

    invoke-static {v0, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/List;

    return-object v0
.end method

.method public getPositionMapping()Ljava/util/Map;
    .locals 2
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-object v0, p0, Lorg/codehaus/groovy/transform/tailrec/ReturnStatementToIterationConverter$_convert_closure2;->positionMapping:Lgroovy/lang/Reference;

    invoke-virtual {v0}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    const-class v1, Ljava/util/Map;

    invoke-static {v0, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/Map;

    return-object v0
.end method

.method public getResult()Lorg/codehaus/groovy/ast/stmt/BlockStatement;
    .locals 2
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-object v0, p0, Lorg/codehaus/groovy/transform/tailrec/ReturnStatementToIterationConverter$_convert_closure2;->result:Lgroovy/lang/Reference;

    invoke-virtual {v0}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    const-class v1, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    invoke-static {v0, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    return-object v0
.end method
