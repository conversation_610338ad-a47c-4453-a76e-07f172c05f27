.class public Lorg/codehaus/groovy/transform/tailrec/InWhileLoopWrapper;
.super Ljava/lang/Object;
.source "InWhileLoopWrapper.groovy"

# interfaces
.implements Lgroovy/lang/GroovyObject;


# static fields
.field private static synthetic $staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo; = null

.field private static synthetic $staticClassInfo$:Lorg/codehaus/groovy/reflection/ClassInfo; = null

.field private static final LOOP_EXCEPTION:Lorg/codehaus/groovy/transform/tailrec/GotoRecurHereException;

.field private static final LOOP_LABEL:Ljava/lang/String; = "_RECUR_HERE_"

.field public static transient synthetic __$stMC:Z


# instance fields
.field private transient synthetic metaClass:Lgroovy/lang/MetaClass;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 51
    new-instance v0, Lorg/codehaus/groovy/transform/tailrec/GotoRecurHereException;

    invoke-direct {v0}, Lorg/codehaus/groovy/transform/tailrec/GotoRecurHereException;-><init>()V

    sput-object v0, Lorg/codehaus/groovy/transform/tailrec/InWhileLoopWrapper;->LOOP_EXCEPTION:Lorg/codehaus/groovy/transform/tailrec/GotoRecurHereException;

    return-void
.end method

.method public constructor <init>()V
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    invoke-virtual {p0}, Lorg/codehaus/groovy/transform/tailrec/InWhileLoopWrapper;->$getStaticMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/transform/tailrec/InWhileLoopWrapper;->metaClass:Lgroovy/lang/MetaClass;

    return-void
.end method

.method public static getLOOP_EXCEPTION()Lorg/codehaus/groovy/transform/tailrec/GotoRecurHereException;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    sget-object v0, Lorg/codehaus/groovy/transform/tailrec/InWhileLoopWrapper;->LOOP_EXCEPTION:Lorg/codehaus/groovy/transform/tailrec/GotoRecurHereException;

    return-object v0
.end method

.method public static getLOOP_LABEL()Ljava/lang/String;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    sget-object v0, Lorg/codehaus/groovy/transform/tailrec/InWhileLoopWrapper;->LOOP_LABEL:Ljava/lang/String;

    return-object v0
.end method


# virtual methods
.method protected synthetic $getStaticMetaClass()Lgroovy/lang/MetaClass;
    .locals 2

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    const-class v1, Lorg/codehaus/groovy/transform/tailrec/InWhileLoopWrapper;

    if-eq v0, v1, :cond_0

    invoke-static {p0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->initMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0

    :cond_0
    sget-object v0, Lorg/codehaus/groovy/transform/tailrec/InWhileLoopWrapper;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    if-nez v0, :cond_1

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getClassInfo(Ljava/lang/Class;)Lorg/codehaus/groovy/reflection/ClassInfo;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/transform/tailrec/InWhileLoopWrapper;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    :cond_1
    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0
.end method

.method public getMetaClass()Lgroovy/lang/MetaClass;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    .annotation runtime Lgroovy/transform/Internal;
    .end annotation

    .annotation runtime Lgroovyjarjaropenbeans/Transient;
    .end annotation

    iget-object v0, p0, Lorg/codehaus/groovy/transform/tailrec/InWhileLoopWrapper;->metaClass:Lgroovy/lang/MetaClass;

    if-eqz v0, :cond_0

    return-object v0

    :cond_0
    invoke-virtual {p0}, Lorg/codehaus/groovy/transform/tailrec/InWhileLoopWrapper;->$getStaticMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/transform/tailrec/InWhileLoopWrapper;->metaClass:Lgroovy/lang/MetaClass;

    return-object v0
.end method

.method public setMetaClass(Lgroovy/lang/MetaClass;)V
    .locals 0
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    .annotation runtime Lgroovy/transform/Internal;
    .end annotation

    iput-object p1, p0, Lorg/codehaus/groovy/transform/tailrec/InWhileLoopWrapper;->metaClass:Lgroovy/lang/MetaClass;

    return-void
.end method

.method public wrap(Lorg/codehaus/groovy/ast/MethodNode;)V
    .locals 7

    .line 54
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getCode()Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v0

    const-class v1, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    invoke-static {v0, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->asType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    .line 55
    new-instance v1, Lorg/codehaus/groovy/ast/stmt/TryCatchStatement;

    sget-object v2, Lorg/codehaus/groovy/ast/stmt/EmptyStatement;->INSTANCE:Lorg/codehaus/groovy/ast/stmt/EmptyStatement;

    invoke-direct {v1, v0, v2}, Lorg/codehaus/groovy/ast/stmt/TryCatchStatement;-><init>(Lorg/codehaus/groovy/ast/stmt/Statement;Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 59
    new-instance v0, Lorg/codehaus/groovy/ast/stmt/CatchStatement;

    .line 60
    const-class v2, Lorg/codehaus/groovy/transform/tailrec/GotoRecurHereException;

    invoke-static {v2}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v2

    const-string v3, "ignore"

    invoke-static {v2, v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->param(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v2

    .line 61
    new-instance v3, Lorg/codehaus/groovy/ast/stmt/ContinueStatement;

    invoke-static {}, Lorg/codehaus/groovy/transform/tailrec/InWhileLoopWrapper;->getLOOP_LABEL()Ljava/lang/String;

    move-result-object v4

    invoke-direct {v3, v4}, Lorg/codehaus/groovy/ast/stmt/ContinueStatement;-><init>(Ljava/lang/String;)V

    invoke-direct {v0, v2, v3}, Lorg/codehaus/groovy/ast/stmt/CatchStatement;-><init>(Lorg/codehaus/groovy/ast/Parameter;Lorg/codehaus/groovy/ast/stmt/Statement;)V

    invoke-virtual {v1, v0}, Lorg/codehaus/groovy/ast/stmt/TryCatchStatement;->addCatch(Lorg/codehaus/groovy/ast/stmt/CatchStatement;)V

    .line 64
    new-instance v0, Lorg/codehaus/groovy/ast/stmt/WhileStatement;

    const/4 v2, 0x1

    .line 65
    invoke-static {v2}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v3

    invoke-static {v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->constX(Ljava/lang/Object;)Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    move-result-object v3

    invoke-static {v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->boolX(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/BooleanExpression;

    move-result-object v3

    .line 66
    new-instance v4, Lorg/codehaus/groovy/ast/VariableScope;

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getVariableScope()Lorg/codehaus/groovy/ast/VariableScope;

    move-result-object v5

    invoke-direct {v4, v5}, Lorg/codehaus/groovy/ast/VariableScope;-><init>(Lorg/codehaus/groovy/ast/VariableScope;)V

    new-array v5, v2, [Lorg/codehaus/groovy/ast/stmt/Statement;

    const/4 v6, 0x0

    aput-object v1, v5, v6

    invoke-static {v4, v5}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->block(Lorg/codehaus/groovy/ast/VariableScope;[Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    move-result-object v1

    invoke-direct {v0, v3, v1}, Lorg/codehaus/groovy/ast/stmt/WhileStatement;-><init>(Lorg/codehaus/groovy/ast/expr/BooleanExpression;Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 68
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/stmt/WhileStatement;->getLoopBlock()Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v1

    const-class v3, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    invoke-static {v1, v3}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->getStatements()Ljava/util/List;

    move-result-object v1

    .line 69
    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v3

    if-lez v3, :cond_0

    goto :goto_0

    :cond_0
    move v2, v6

    :goto_0
    if-eqz v2, :cond_1

    .line 70
    sget-object v2, Lorg/codehaus/groovy/transform/tailrec/InWhileLoopWrapper;->LOOP_LABEL:Ljava/lang/String;

    invoke-static {v1, v6}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->getAt(Ljava/util/List;I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/ast/stmt/Statement;

    invoke-virtual {v1, v2}, Lorg/codehaus/groovy/ast/stmt/Statement;->setStatementLabel(Ljava/lang/String;)V

    .line 71
    :cond_1
    new-instance v1, Lorg/codehaus/groovy/ast/VariableScope;

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getVariableScope()Lorg/codehaus/groovy/ast/VariableScope;

    move-result-object v2

    invoke-direct {v1, v2}, Lorg/codehaus/groovy/ast/VariableScope;-><init>(Lorg/codehaus/groovy/ast/VariableScope;)V

    new-array v2, v6, [Lorg/codehaus/groovy/ast/stmt/Statement;

    invoke-static {v1, v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->block(Lorg/codehaus/groovy/ast/VariableScope;[Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    move-result-object v1

    .line 72
    invoke-virtual {v1, v0}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 73
    invoke-virtual {p1, v1}, Lorg/codehaus/groovy/ast/MethodNode;->setCode(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    return-void
.end method
