.class public final Lorg/codehaus/groovy/transform/tailrec/StatementReplacer$_visitBlockStatement_closure3$_closure9;
.super Lgroovy/lang/Closure;
.source "StatementReplacer.groovy"

# interfaces
.implements Lorg/codehaus/groovy/runtime/GeneratedClosure;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/codehaus/groovy/transform/tailrec/StatementReplacer$_visitBlockStatement_closure3;->doCall(Lorg/codehaus/groovy/ast/stmt/Statement;I)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x11
    name = "_closure9"
.end annotation


# static fields
.field private static synthetic $staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

.field public static transient synthetic __$stMC:Z


# instance fields
.field private synthetic block:Lgroovy/lang/Reference;

.field private synthetic index:Lgroovy/lang/Reference;


# direct methods
.method public constructor <init>(Ljava/lang/Object;Ljava/lang/Object;Lgroovy/lang/Reference;Lgroovy/lang/Reference;)V
    .locals 0

    invoke-direct {p0, p1, p2}, Lgroovy/lang/Closure;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    iput-object p3, p0, Lorg/codehaus/groovy/transform/tailrec/StatementReplacer$_visitBlockStatement_closure3$_closure9;->block:Lgroovy/lang/Reference;

    iput-object p4, p0, Lorg/codehaus/groovy/transform/tailrec/StatementReplacer$_visitBlockStatement_closure3$_closure9;->index:Lgroovy/lang/Reference;

    return-void
.end method


# virtual methods
.method protected synthetic $getStaticMetaClass()Lgroovy/lang/MetaClass;
    .locals 2

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    const-class v1, Lorg/codehaus/groovy/transform/tailrec/StatementReplacer$_visitBlockStatement_closure3$_closure9;

    if-eq v0, v1, :cond_0

    invoke-static {p0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->initMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0

    :cond_0
    sget-object v0, Lorg/codehaus/groovy/transform/tailrec/StatementReplacer$_visitBlockStatement_closure3$_closure9;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    if-nez v0, :cond_1

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getClassInfo(Ljava/lang/Class;)Lorg/codehaus/groovy/reflection/ClassInfo;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/transform/tailrec/StatementReplacer$_visitBlockStatement_closure3$_closure9;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    :cond_1
    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0
.end method

.method public call(Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/Statement;
    .locals 0
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/transform/tailrec/StatementReplacer$_visitBlockStatement_closure3$_closure9;->doCall(Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    return-object p1
.end method

.method public doCall(Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/Statement;
    .locals 6

    .line 60
    const-class v0, Lorg/codehaus/groovy/transform/tailrec/StatementReplacer$_visitBlockStatement_closure3$_closure9;

    iget-object v1, p0, Lorg/codehaus/groovy/transform/tailrec/StatementReplacer$_visitBlockStatement_closure3$_closure9;->block:Lgroovy/lang/Reference;

    invoke-virtual {v1}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->getStatements()Ljava/util/List;

    move-result-object v1

    const-string v2, "putAt"

    move-object v3, v2

    check-cast v3, Ljava/lang/String;

    const/4 v3, 0x2

    new-array v3, v3, [Ljava/lang/Object;

    iget-object v4, p0, Lorg/codehaus/groovy/transform/tailrec/StatementReplacer$_visitBlockStatement_closure3$_closure9;->index:Lgroovy/lang/Reference;

    invoke-virtual {v4}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v4

    const/4 v5, 0x0

    aput-object v4, v3, v5

    const/4 v4, 0x1

    aput-object p1, v3, v4

    invoke-static {v0, v1, v2, v3}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodN(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    return-object p1
.end method

.method public getBlock()Lorg/codehaus/groovy/ast/stmt/BlockStatement;
    .locals 2
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-object v0, p0, Lorg/codehaus/groovy/transform/tailrec/StatementReplacer$_visitBlockStatement_closure3$_closure9;->block:Lgroovy/lang/Reference;

    invoke-virtual {v0}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    const-class v1, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    invoke-static {v0, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    return-object v0
.end method

.method public getIndex()I
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-object v0, p0, Lorg/codehaus/groovy/transform/tailrec/StatementReplacer$_visitBlockStatement_closure3$_closure9;->index:Lgroovy/lang/Reference;

    invoke-virtual {v0}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->intUnbox(Ljava/lang/Object;)I

    move-result v0

    return v0
.end method
