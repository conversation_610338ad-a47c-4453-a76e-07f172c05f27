.class public Lorg/codehaus/groovy/transform/tailrec/CollectRecursiveCalls;
.super Lorg/codehaus/groovy/ast/CodeVisitorSupport;
.source "CollectRecursiveCalls.groovy"

# interfaces
.implements Lgroovy/lang/GroovyObject;


# static fields
.field private static synthetic $staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

.field private static synthetic $staticClassInfo$:Lorg/codehaus/groovy/reflection/ClassInfo;

.field public static transient synthetic __$stMC:Z


# instance fields
.field private transient synthetic metaClass:Lgroovy/lang/MetaClass;

.field private method:Lorg/codehaus/groovy/ast/MethodNode;

.field private recursiveCalls:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/expr/Expression;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>()V
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    invoke-direct {p0}, Lorg/codehaus/groovy/ast/CodeVisitorSupport;-><init>()V

    const/4 v0, 0x0

    new-array v0, v0, [Ljava/lang/Object;

    .line 34
    invoke-static {v0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/transform/tailrec/CollectRecursiveCalls;->recursiveCalls:Ljava/util/List;

    invoke-virtual {p0}, Lorg/codehaus/groovy/transform/tailrec/CollectRecursiveCalls;->$getStaticMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/transform/tailrec/CollectRecursiveCalls;->metaClass:Lgroovy/lang/MetaClass;

    return-void
.end method

.method private isRecursive(Ljava/lang/Object;)Z
    .locals 4

    .line 51
    new-instance v0, Lorg/codehaus/groovy/transform/tailrec/RecursivenessTester;

    invoke-direct {v0}, Lorg/codehaus/groovy/transform/tailrec/RecursivenessTester;-><init>()V

    const/4 v1, 0x4

    new-array v1, v1, [Ljava/lang/Object;

    const/4 v2, 0x0

    const-string v3, "method"

    aput-object v3, v1, v2

    iget-object v2, p0, Lorg/codehaus/groovy/transform/tailrec/CollectRecursiveCalls;->method:Lorg/codehaus/groovy/ast/MethodNode;

    const/4 v3, 0x1

    aput-object v2, v1, v3

    const/4 v2, 0x2

    const-string v3, "call"

    aput-object v3, v1, v2

    const/4 v2, 0x3

    aput-object p1, v1, v2

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createMap([Ljava/lang/Object;)Ljava/util/Map;

    move-result-object p1

    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/transform/tailrec/RecursivenessTester;->isRecursive(Ljava/lang/Object;)Z

    move-result p1

    return p1
.end method


# virtual methods
.method protected synthetic $getStaticMetaClass()Lgroovy/lang/MetaClass;
    .locals 2

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    const-class v1, Lorg/codehaus/groovy/transform/tailrec/CollectRecursiveCalls;

    if-eq v0, v1, :cond_0

    invoke-static {p0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->initMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0

    :cond_0
    sget-object v0, Lorg/codehaus/groovy/transform/tailrec/CollectRecursiveCalls;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    if-nez v0, :cond_1

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getClassInfo(Ljava/lang/Class;)Lorg/codehaus/groovy/reflection/ClassInfo;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/transform/tailrec/CollectRecursiveCalls;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    :cond_1
    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0
.end method

.method public declared-synchronized collect(Lorg/codehaus/groovy/ast/MethodNode;)Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/MethodNode;",
            ")",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/expr/Expression;",
            ">;"
        }
    .end annotation

    monitor-enter p0

    .line 55
    :try_start_0
    iget-object v0, p0, Lorg/codehaus/groovy/transform/tailrec/CollectRecursiveCalls;->recursiveCalls:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->clear()V

    .line 56
    iput-object p1, p0, Lorg/codehaus/groovy/transform/tailrec/CollectRecursiveCalls;->method:Lorg/codehaus/groovy/ast/MethodNode;

    .line 57
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getCode()Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    move-object v0, p1

    check-cast v0, Lorg/codehaus/groovy/ast/stmt/Statement;

    invoke-virtual {p1, p0}, Lorg/codehaus/groovy/ast/stmt/Statement;->visit(Lorg/codehaus/groovy/ast/GroovyCodeVisitor;)V

    .line 58
    iget-object p1, p0, Lorg/codehaus/groovy/transform/tailrec/CollectRecursiveCalls;->recursiveCalls:Ljava/util/List;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit p0

    return-object p1

    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method

.method public getMetaClass()Lgroovy/lang/MetaClass;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    .annotation runtime Lgroovy/transform/Internal;
    .end annotation

    .annotation runtime Lgroovyjarjaropenbeans/Transient;
    .end annotation

    iget-object v0, p0, Lorg/codehaus/groovy/transform/tailrec/CollectRecursiveCalls;->metaClass:Lgroovy/lang/MetaClass;

    if-eqz v0, :cond_0

    return-object v0

    :cond_0
    invoke-virtual {p0}, Lorg/codehaus/groovy/transform/tailrec/CollectRecursiveCalls;->$getStaticMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/transform/tailrec/CollectRecursiveCalls;->metaClass:Lgroovy/lang/MetaClass;

    return-object v0
.end method

.method public getMethod()Lorg/codehaus/groovy/ast/MethodNode;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-object v0, p0, Lorg/codehaus/groovy/transform/tailrec/CollectRecursiveCalls;->method:Lorg/codehaus/groovy/ast/MethodNode;

    return-object v0
.end method

.method public getRecursiveCalls()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/expr/Expression;",
            ">;"
        }
    .end annotation

    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-object v0, p0, Lorg/codehaus/groovy/transform/tailrec/CollectRecursiveCalls;->recursiveCalls:Ljava/util/List;

    return-object v0
.end method

.method public setMetaClass(Lgroovy/lang/MetaClass;)V
    .locals 0
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    .annotation runtime Lgroovy/transform/Internal;
    .end annotation

    iput-object p1, p0, Lorg/codehaus/groovy/transform/tailrec/CollectRecursiveCalls;->metaClass:Lgroovy/lang/MetaClass;

    return-void
.end method

.method public setMethod(Lorg/codehaus/groovy/ast/MethodNode;)V
    .locals 0
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iput-object p1, p0, Lorg/codehaus/groovy/transform/tailrec/CollectRecursiveCalls;->method:Lorg/codehaus/groovy/ast/MethodNode;

    return-void
.end method

.method public setRecursiveCalls(Ljava/util/List;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/expr/Expression;",
            ">;)V"
        }
    .end annotation

    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iput-object p1, p0, Lorg/codehaus/groovy/transform/tailrec/CollectRecursiveCalls;->recursiveCalls:Ljava/util/List;

    return-void
.end method

.method public visitMethodCallExpression(Lorg/codehaus/groovy/ast/expr/MethodCallExpression;)V
    .locals 1

    .line 37
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/transform/tailrec/CollectRecursiveCalls;->isRecursive(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 38
    iget-object v0, p0, Lorg/codehaus/groovy/transform/tailrec/CollectRecursiveCalls;->recursiveCalls:Ljava/util/List;

    invoke-static {v0, p1}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->leftShift(Ljava/util/List;Ljava/lang/Object;)Ljava/util/List;

    .line 40
    :cond_0
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/CodeVisitorSupport;->visitMethodCallExpression(Lorg/codehaus/groovy/ast/expr/MethodCallExpression;)V

    return-void
.end method

.method public visitStaticMethodCallExpression(Lorg/codehaus/groovy/ast/expr/StaticMethodCallExpression;)V
    .locals 1

    .line 44
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/transform/tailrec/CollectRecursiveCalls;->isRecursive(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 45
    iget-object v0, p0, Lorg/codehaus/groovy/transform/tailrec/CollectRecursiveCalls;->recursiveCalls:Ljava/util/List;

    invoke-static {v0, p1}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->leftShift(Ljava/util/List;Ljava/lang/Object;)Ljava/util/List;

    .line 47
    :cond_0
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/CodeVisitorSupport;->visitStaticMethodCallExpression(Lorg/codehaus/groovy/ast/expr/StaticMethodCallExpression;)V

    return-void
.end method
