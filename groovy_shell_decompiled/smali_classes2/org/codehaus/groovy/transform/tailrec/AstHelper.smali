.class public Lorg/codehaus/groovy/transform/tailrec/AstHelper;
.super Ljava/lang/Object;
.source "AstHelper.groovy"

# interfaces
.implements Lgroovy/lang/GroovyObject;


# static fields
.field private static synthetic $callSiteArray:Ljava/lang/ref/SoftReference;

.field private static synthetic $staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

.field private static synthetic $staticClassInfo$:Lorg/codehaus/groovy/reflection/ClassInfo;

.field public static transient synthetic __$stMC:Z


# instance fields
.field private transient synthetic metaClass:Lgroovy/lang/MetaClass;


# direct methods
.method private static synthetic $createCallSiteArray()Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;
    .locals 3

    const/4 v0, 0x0

    new-array v0, v0, [Ljava/lang/String;

    new-instance v1, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;

    const-class v2, Lorg/codehaus/groovy/transform/tailrec/AstHelper;

    invoke-direct {v1, v2, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;-><init>(Ljava/lang/Class;[Ljava/lang/String;)V

    return-object v1
.end method

.method private static synthetic $getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;
    .locals 2

    sget-object v0, Lorg/codehaus/groovy/transform/tailrec/AstHelper;->$callSiteArray:Ljava/lang/ref/SoftReference;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Ljava/lang/ref/SoftReference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;

    if-nez v0, :cond_1

    :cond_0
    invoke-static {}, Lorg/codehaus/groovy/transform/tailrec/AstHelper;->$createCallSiteArray()Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;

    move-result-object v0

    new-instance v1, Ljava/lang/ref/SoftReference;

    invoke-direct {v1, v0}, Ljava/lang/ref/SoftReference;-><init>(Ljava/lang/Object;)V

    sput-object v1, Lorg/codehaus/groovy/transform/tailrec/AstHelper;->$callSiteArray:Ljava/lang/ref/SoftReference;

    :cond_1
    iget-object v0, v0, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;->array:[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    return-object v0
.end method

.method public constructor <init>()V
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    invoke-virtual {p0}, Lorg/codehaus/groovy/transform/tailrec/AstHelper;->$getStaticMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/transform/tailrec/AstHelper;->metaClass:Lgroovy/lang/MetaClass;

    return-void
.end method

.method public static createVariableAlias(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/stmt/ExpressionStatement;
    .locals 0

    .line 51
    invoke-static {p2, p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object p2

    invoke-static {p0, p1, p2}, Lorg/codehaus/groovy/transform/tailrec/AstHelper;->createVariableDefinition(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/ExpressionStatement;

    move-result-object p0

    return-object p0
.end method

.method public static createVariableDefinition(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/ExpressionStatement;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    invoke-static {}, Lorg/codehaus/groovy/transform/tailrec/AstHelper;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-object v0, p0

    check-cast v0, Ljava/lang/String;

    move-object v0, p1

    check-cast v0, Lorg/codehaus/groovy/ast/ClassNode;

    move-object v0, p2

    check-cast v0, Lorg/codehaus/groovy/ast/expr/Expression;

    const/4 v0, 0x0

    invoke-static {p0, p1, p2, v0}, Lorg/codehaus/groovy/transform/tailrec/AstHelper;->createVariableDefinition(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;Z)Lorg/codehaus/groovy/ast/stmt/ExpressionStatement;

    move-result-object p0

    return-object p0
.end method

.method public static createVariableDefinition(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;Z)Lorg/codehaus/groovy/ast/stmt/ExpressionStatement;
    .locals 0

    .line 44
    invoke-static {p0, p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->localVarX(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object p0

    if-eqz p3, :cond_0

    .line 46
    sget p1, Ljava/lang/reflect/Modifier;->FINAL:I

    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->setModifiers(I)V

    .line 47
    :cond_0
    invoke-static {p0, p2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->declS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p0

    const-class p1, Lorg/codehaus/groovy/ast/stmt/ExpressionStatement;

    invoke-static {p0, p1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Lorg/codehaus/groovy/ast/stmt/ExpressionStatement;

    return-object p0
.end method

.method public static createVariableReference(Ljava/util/Map;)Lorg/codehaus/groovy/ast/expr/VariableExpression;
    .locals 2

    const-string v0, "name"

    .line 55
    invoke-interface {p0, v0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v0

    move-object v1, v0

    check-cast v1, Ljava/lang/String;

    const-string v1, "type"

    invoke-interface {p0, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    const-class v1, Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {p0, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {v0, p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object p0

    return-object p0
.end method

.method public static recurByThrowStatement()Lorg/codehaus/groovy/ast/stmt/Statement;
    .locals 2

    .line 73
    const-class v0, Lorg/codehaus/groovy/transform/tailrec/InWhileLoopWrapper;

    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->classX(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/expr/ClassExpression;

    move-result-object v0

    const-string v1, "LOOP_EXCEPTION"

    invoke-static {v0, v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->propX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->throwS(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/ThrowStatement;

    move-result-object v0

    return-object v0
.end method

.method public static recurStatement()Lorg/codehaus/groovy/ast/stmt/Statement;
    .locals 2

    .line 64
    new-instance v0, Lorg/codehaus/groovy/ast/stmt/ContinueStatement;

    invoke-static {}, Lorg/codehaus/groovy/transform/tailrec/InWhileLoopWrapper;->getLOOP_LABEL()Ljava/lang/String;

    move-result-object v1

    invoke-direct {v0, v1}, Lorg/codehaus/groovy/ast/stmt/ContinueStatement;-><init>(Ljava/lang/String;)V

    return-object v0
.end method


# virtual methods
.method protected synthetic $getStaticMetaClass()Lgroovy/lang/MetaClass;
    .locals 2

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    const-class v1, Lorg/codehaus/groovy/transform/tailrec/AstHelper;

    if-eq v0, v1, :cond_0

    invoke-static {p0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->initMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0

    :cond_0
    sget-object v0, Lorg/codehaus/groovy/transform/tailrec/AstHelper;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    if-nez v0, :cond_1

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getClassInfo(Ljava/lang/Class;)Lorg/codehaus/groovy/reflection/ClassInfo;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/transform/tailrec/AstHelper;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    :cond_1
    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0
.end method

.method public getMetaClass()Lgroovy/lang/MetaClass;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    .annotation runtime Lgroovy/transform/Internal;
    .end annotation

    .annotation runtime Lgroovyjarjaropenbeans/Transient;
    .end annotation

    iget-object v0, p0, Lorg/codehaus/groovy/transform/tailrec/AstHelper;->metaClass:Lgroovy/lang/MetaClass;

    if-eqz v0, :cond_0

    return-object v0

    :cond_0
    invoke-virtual {p0}, Lorg/codehaus/groovy/transform/tailrec/AstHelper;->$getStaticMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/transform/tailrec/AstHelper;->metaClass:Lgroovy/lang/MetaClass;

    return-object v0
.end method

.method public setMetaClass(Lgroovy/lang/MetaClass;)V
    .locals 0
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    .annotation runtime Lgroovy/transform/Internal;
    .end annotation

    iput-object p1, p0, Lorg/codehaus/groovy/transform/tailrec/AstHelper;->metaClass:Lgroovy/lang/MetaClass;

    return-void
.end method
