.class public final synthetic Lorg/codehaus/groovy/transform/SortableASTTransformation$$ExternalSyntheticLambda0;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/ToIntFunction;


# instance fields
.field public final synthetic f$0:Ljava/util/List;


# direct methods
.method public synthetic constructor <init>(Ljava/util/List;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/codehaus/groovy/transform/SortableASTTransformation$$ExternalSyntheticLambda0;->f$0:Ljava/util/List;

    return-void
.end method


# virtual methods
.method public final applyAsInt(Ljava/lang/Object;)I
    .locals 1

    iget-object v0, p0, Lorg/codehaus/groovy/transform/SortableASTTransformation$$ExternalSyntheticLambda0;->f$0:Ljava/util/List;

    check-cast p1, Lorg/codehaus/groovy/ast/PropertyNode;

    invoke-static {v0, p1}, Lorg/codehaus/groovy/transform/SortableASTTransformation;->lambda$findProperties$0(Ljava/util/List;Lorg/codehaus/groovy/ast/PropertyNode;)I

    move-result p1

    return p1
.end method
