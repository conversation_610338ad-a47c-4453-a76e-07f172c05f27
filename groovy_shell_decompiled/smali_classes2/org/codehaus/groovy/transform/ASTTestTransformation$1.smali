.class public Lorg/codehaus/groovy/transform/ASTTestTransformation$1;
.super Ljava/lang/Object;
.source "ASTTestTransformation.groovy"

# interfaces
.implements Lorg/codehaus/groovy/control/CompilationUnit$ProgressCallback;
.implements Lgroovy/lang/GroovyObject;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/codehaus/groovy/transform/ASTTestTransformation;->visit([Lorg/codehaus/groovy/ast/ASTNode;Lorg/codehaus/groovy/control/SourceUnit;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/codehaus/groovy/transform/ASTTestTransformation$1$_closure1;
    }
.end annotation


# static fields
.field private static synthetic $callSiteArray:Ljava/lang/ref/SoftReference;

.field private static synthetic $staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

.field public static transient synthetic __$stMC:Z


# instance fields
.field private final binding:Lgroovy/lang/Binding;

.field private transient synthetic metaClass:Lgroovy/lang/MetaClass;

.field public synthetic nodes:Lgroovy/lang/Reference;

.field public synthetic phase:Lgroovy/lang/Reference;

.field public synthetic source:Lgroovy/lang/Reference;

.field final synthetic this$0:Lorg/codehaus/groovy/transform/ASTTestTransformation;


# direct methods
.method private static synthetic $createCallSiteArray()Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;
    .locals 3

    const/16 v0, 0x5f

    new-array v0, v0, [Ljava/lang/String;

    invoke-static {v0}, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->$createCallSiteArray_1([Ljava/lang/String;)V

    new-instance v1, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;

    const-class v2, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;

    invoke-direct {v1, v2, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;-><init>(Ljava/lang/Class;[Ljava/lang/String;)V

    return-object v1
.end method

.method private static synthetic $createCallSiteArray_1([Ljava/lang/String;)V
    .locals 24

    const/4 v0, 0x0

    const-string v1, "<$constructor$>"

    aput-object v1, p0, v0

    const/4 v0, 0x1

    const-string v2, "withDefault"

    aput-object v2, p0, v0

    const/4 v0, 0x2

    const-string v2, "phaseNumber"

    aput-object v2, p0, v0

    const/4 v0, 0x3

    const-string v3, "getNodeMetaData"

    aput-object v3, p0, v0

    const/4 v0, 0x4

    const-string v4, "getAt"

    aput-object v4, p0, v0

    const/4 v0, 0x5

    aput-object v1, p0, v0

    const/4 v0, 0x6

    const-string v5, "lineNumber"

    aput-object v5, p0, v0

    const/4 v0, 0x7

    const-string v6, "lastLineNumber"

    aput-object v6, p0, v0

    const/16 v0, 0x8

    const-string v7, "append"

    aput-object v7, p0, v0

    const/16 v0, 0x9

    aput-object v7, p0, v0

    const/16 v0, 0xa

    const-string v8, "getLine"

    aput-object v8, p0, v0

    const/16 v0, 0xb

    const-string v9, "source"

    aput-object v9, p0, v0

    const/16 v0, 0xc

    aput-object v1, p0, v0

    const/16 v0, 0xd

    const-string v10, "plus"

    aput-object v10, p0, v0

    const/16 v0, 0xe

    aput-object v4, p0, v0

    const/16 v0, 0xf

    const-string v10, "columnNumber"

    aput-object v10, p0, v0

    const/16 v0, 0x10

    const-string v11, "length"

    aput-object v11, p0, v0

    const/16 v0, 0x11

    aput-object v4, p0, v0

    const/16 v0, 0x12

    const-string v12, "lastIndexOf"

    aput-object v12, p0, v0

    const/16 v0, 0x13

    aput-object v4, p0, v0

    const/16 v0, 0x14

    const-string v13, "putAt"

    aput-object v13, p0, v0

    const/16 v0, 0x15

    aput-object v13, p0, v0

    const/16 v0, 0x16

    const-string v14, "compilationUnit"

    aput-object v14, p0, v0

    const/16 v0, 0x17

    aput-object v13, p0, v0

    const/16 v0, 0x18

    const-string v15, "fromPhaseNumber"

    aput-object v15, p0, v0

    const/16 v0, 0x19

    aput-object v13, p0, v0

    const/16 v0, 0x1a

    const-string v16, "curry"

    aput-object v16, p0, v0

    const/16 v0, 0x1b

    aput-object v1, p0, v0

    const/16 v0, 0x1c

    aput-object v4, p0, v0

    const/16 v0, 0x1d

    aput-object v13, p0, v0

    const/16 v0, 0x1e

    aput-object v1, p0, v0

    const/16 v0, 0x1f

    const-string v17, "each"

    aput-object v17, p0, v0

    const/16 v0, 0x20

    const-string v18, "imports"

    aput-object v18, p0, v0

    const/16 v0, 0x21

    const-string v19, "AST"

    aput-object v19, p0, v0

    const/16 v0, 0x22

    aput-object v17, p0, v0

    const/16 v0, 0x23

    const-string v20, "starImports"

    aput-object v20, p0, v0

    const/16 v0, 0x24

    aput-object v19, p0, v0

    const/16 v0, 0x25

    aput-object v17, p0, v0

    const/16 v0, 0x26

    const-string v21, "staticImports"

    aput-object v21, p0, v0

    const/16 v0, 0x27

    aput-object v19, p0, v0

    const/16 v0, 0x28

    aput-object v17, p0, v0

    const/16 v0, 0x29

    const-string v22, "staticStarImports"

    aput-object v22, p0, v0

    const/16 v0, 0x2a

    aput-object v19, p0, v0

    const/16 v0, 0x2b

    aput-object v1, p0, v0

    const/16 v0, 0x2c

    const-string v23, "addCompilationCustomizers"

    aput-object v23, p0, v0

    const/16 v0, 0x2d

    const-string v23, "transformLoader"

    aput-object v23, p0, v0

    const/16 v0, 0x2e

    aput-object v14, p0, v0

    const/16 v0, 0x2f

    const-string v23, "evaluate"

    aput-object v23, p0, v0

    const/16 v0, 0x30

    aput-object v1, p0, v0

    const/16 v0, 0x31

    aput-object v2, p0, v0

    const/16 v0, 0x32

    aput-object v3, p0, v0

    const/16 v0, 0x33

    aput-object v1, p0, v0

    const/16 v0, 0x34

    aput-object v5, p0, v0

    const/16 v0, 0x35

    aput-object v6, p0, v0

    const/16 v0, 0x36

    aput-object v7, p0, v0

    const/16 v0, 0x37

    aput-object v7, p0, v0

    const/16 v0, 0x38

    aput-object v8, p0, v0

    const/16 v0, 0x39

    aput-object v9, p0, v0

    const/16 v0, 0x3a

    aput-object v1, p0, v0

    const/16 v0, 0x3b

    aput-object v4, p0, v0

    const/16 v0, 0x3c

    aput-object v10, p0, v0

    const/16 v0, 0x3d

    aput-object v11, p0, v0

    const/16 v0, 0x3e

    aput-object v4, p0, v0

    const/16 v0, 0x3f

    aput-object v12, p0, v0

    const/16 v0, 0x40

    aput-object v13, p0, v0

    const/16 v0, 0x41

    aput-object v13, p0, v0

    const/16 v0, 0x42

    aput-object v14, p0, v0

    const/16 v0, 0x43

    aput-object v13, p0, v0

    const/16 v0, 0x44

    aput-object v15, p0, v0

    const/16 v0, 0x45

    aput-object v13, p0, v0

    const/16 v0, 0x46

    aput-object v16, p0, v0

    const/16 v0, 0x47

    aput-object v1, p0, v0

    const/16 v0, 0x48

    aput-object v13, p0, v0

    const/16 v0, 0x49

    aput-object v1, p0, v0

    const/16 v0, 0x4a

    aput-object v17, p0, v0

    const/16 v0, 0x4b

    aput-object v18, p0, v0

    const/16 v0, 0x4c

    aput-object v19, p0, v0

    const/16 v0, 0x4d

    aput-object v17, p0, v0

    const/16 v0, 0x4e

    aput-object v20, p0, v0

    const/16 v0, 0x4f

    aput-object v19, p0, v0

    const/16 v0, 0x50

    aput-object v17, p0, v0

    const/16 v0, 0x51

    aput-object v21, p0, v0

    const/16 v0, 0x52

    aput-object v19, p0, v0

    const/16 v0, 0x53

    aput-object v17, p0, v0

    const/16 v0, 0x54

    aput-object v22, p0, v0

    const/16 v0, 0x55

    aput-object v19, p0, v0

    const/16 v0, 0x56

    aput-object v1, p0, v0

    const/16 v0, 0x57

    const-string v2, "addCompilationCustomizers"

    aput-object v2, p0, v0

    const/16 v0, 0x58

    const-string v2, "transformLoader"

    aput-object v2, p0, v0

    const/16 v0, 0x59

    aput-object v14, p0, v0

    const/16 v0, 0x5a

    const-string v2, "evaluate"

    aput-object v2, p0, v0

    const/16 v0, 0x5b

    aput-object v1, p0, v0

    const/16 v0, 0x5c

    aput-object v11, p0, v0

    const/16 v0, 0x5d

    aput-object v4, p0, v0

    const/16 v0, 0x5e

    aput-object v11, p0, v0

    return-void
.end method

.method private static synthetic $getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;
    .locals 2

    sget-object v0, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->$callSiteArray:Ljava/lang/ref/SoftReference;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Ljava/lang/ref/SoftReference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;

    if-nez v0, :cond_1

    :cond_0
    invoke-static {}, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->$createCallSiteArray()Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;

    move-result-object v0

    new-instance v1, Ljava/lang/ref/SoftReference;

    invoke-direct {v1, v0}, Ljava/lang/ref/SoftReference;-><init>(Ljava/lang/Object;)V

    sput-object v1, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->$callSiteArray:Ljava/lang/ref/SoftReference;

    :cond_1
    iget-object v0, v0, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;->array:[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    return-object v0
.end method

.method public static synthetic $static_methodMissing(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 8

    const-class v0, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;

    invoke-static {}, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v1

    instance-of v2, p1, [Ljava/lang/Object;

    const/4 v3, 0x1

    xor-int/2addr v2, v3

    const-string v4, ""

    const/4 v5, 0x0

    if-eqz v2, :cond_0

    const-class v1, Lorg/codehaus/groovy/transform/ASTTestTransformation;

    new-instance v2, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v6, v3, [Ljava/lang/Object;

    aput-object p0, v6, v5

    filled-new-array {v4, v4}, [Ljava/lang/String;

    move-result-object p0

    invoke-direct {v2, v6, p0}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p0

    move-object v2, p0

    check-cast v2, Ljava/lang/String;

    new-array v2, v3, [Ljava/lang/Object;

    aput-object p1, v2, v5

    invoke-static {v0, v1, p0, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodN(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    return-object p0

    :cond_0
    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->isOrigInt()Z

    move-result v2

    if-eqz v2, :cond_1

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->isOrigZ()Z

    move-result v2

    if-eqz v2, :cond_1

    sget-boolean v2, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->__$stMC:Z

    if-nez v2, :cond_1

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->disabledStandardMetaClass()Z

    move-result v2

    if-nez v2, :cond_1

    const/16 v2, 0x5e

    aget-object v1, v1, v2

    const-class v2, [Ljava/lang/Object;

    invoke-static {p1, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, [Ljava/lang/Object;

    invoke-interface {v1, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    invoke-static {v1, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_2

    const-class v1, Lorg/codehaus/groovy/transform/ASTTestTransformation;

    new-instance v2, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v6, v3, [Ljava/lang/Object;

    aput-object p0, v6, v5

    filled-new-array {v4, v4}, [Ljava/lang/String;

    move-result-object p0

    invoke-direct {v2, v6, p0}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p0

    move-object v2, p0

    check-cast v2, Ljava/lang/String;

    new-array v2, v3, [Ljava/lang/Object;

    const-class v3, [Ljava/lang/Object;

    invoke-static {p1, v3}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, [Ljava/lang/Object;

    invoke-static {p1, v5}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->objectArrayGet([Ljava/lang/Object;I)Ljava/lang/Object;

    move-result-object p1

    aput-object p1, v2, v5

    invoke-static {v0, v1, p0, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodN(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    return-object p0

    :cond_1
    const/16 v2, 0x5c

    aget-object v2, v1, v2

    const-class v6, [Ljava/lang/Object;

    invoke-static {p1, v6}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v6

    check-cast v6, [Ljava/lang/Object;

    invoke-interface {v2, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v6

    invoke-static {v2, v6}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_2

    const-class v2, Lorg/codehaus/groovy/transform/ASTTestTransformation;

    new-instance v6, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v7, v3, [Ljava/lang/Object;

    aput-object p0, v7, v5

    filled-new-array {v4, v4}, [Ljava/lang/String;

    move-result-object p0

    invoke-direct {v6, v7, p0}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v6}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p0

    move-object v4, p0

    check-cast v4, Ljava/lang/String;

    new-array v3, v3, [Ljava/lang/Object;

    const/16 v4, 0x5d

    aget-object v1, v1, v4

    const-class v4, [Ljava/lang/Object;

    invoke-static {p1, v4}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, [Ljava/lang/Object;

    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v4

    invoke-interface {v1, p1, v4}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    aput-object p1, v3, v5

    invoke-static {v0, v2, p0, v3}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodN(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    return-object p0

    :cond_2
    const-class v1, Lorg/codehaus/groovy/transform/ASTTestTransformation;

    new-instance v2, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v6, v3, [Ljava/lang/Object;

    aput-object p0, v6, v5

    filled-new-array {v4, v4}, [Ljava/lang/String;

    move-result-object p0

    invoke-direct {v2, v6, p0}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p0

    move-object v2, p0

    check-cast v2, Ljava/lang/String;

    new-array v2, v5, [Ljava/lang/Object;

    new-array v4, v3, [Ljava/lang/Object;

    aput-object p1, v4, v5

    new-array p1, v3, [I

    aput v5, p1, v5

    invoke-static {v2, v4, p1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->despreadList([Ljava/lang/Object;[Ljava/lang/Object;[I)[Ljava/lang/Object;

    move-result-object p1

    invoke-static {v0, v1, p0, p1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodN(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic $static_propertyMissing(Ljava/lang/String;)Ljava/lang/Object;
    .locals 5

    invoke-static {}, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    const-class v0, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;

    const-class v1, Lorg/codehaus/groovy/transform/ASTTestTransformation;

    new-instance v2, Lorg/codehaus/groovy/runtime/GStringImpl;

    const/4 v3, 0x1

    new-array v3, v3, [Ljava/lang/Object;

    const/4 v4, 0x0

    aput-object p0, v3, v4

    const-string p0, ""

    filled-new-array {p0, p0}, [Ljava/lang/String;

    move-result-object p0

    invoke-direct {v2, v3, p0}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p0

    move-object v2, p0

    check-cast v2, Ljava/lang/String;

    invoke-static {v0, v1, p0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->getProperty(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic $static_propertyMissing(Ljava/lang/String;Ljava/lang/Object;)V
    .locals 4

    invoke-static {}, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    const-class v0, Lorg/codehaus/groovy/transform/ASTTestTransformation;

    new-instance v1, Lorg/codehaus/groovy/runtime/GStringImpl;

    const/4 v2, 0x1

    new-array v2, v2, [Ljava/lang/Object;

    const/4 v3, 0x0

    aput-object p0, v2, v3

    const-string p0, ""

    filled-new-array {p0, p0}, [Ljava/lang/String;

    move-result-object p0

    invoke-direct {v1, v2, p0}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p0

    move-object v1, p0

    check-cast v1, Ljava/lang/String;

    const/4 v1, 0x0

    invoke-static {p1, v1, v0, p0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->setProperty(Ljava/lang/Object;Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;)V

    return-void
.end method

.method synthetic constructor <init>(Lorg/codehaus/groovy/transform/ASTTestTransformation;Lgroovy/lang/Reference;Lgroovy/lang/Reference;Lgroovy/lang/Reference;)V
    .locals 1

    invoke-static {}, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v0

    iput-object p1, p0, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->this$0:Lorg/codehaus/groovy/transform/ASTTestTransformation;

    iput-object p2, p0, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->phase:Lgroovy/lang/Reference;

    iput-object p3, p0, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->nodes:Lgroovy/lang/Reference;

    iput-object p4, p0, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->source:Lgroovy/lang/Reference;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 p1, 0x0

    .line 79
    aget-object p2, v0, p1

    const-class p3, Lgroovy/lang/Binding;

    const/4 p4, 0x1

    aget-object p4, v0, p4

    new-array p1, p1, [Ljava/lang/Object;

    invoke-static {p1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createMap([Ljava/lang/Object;)Ljava/util/Map;

    move-result-object p1

    new-instance v0, Lorg/codehaus/groovy/transform/ASTTestTransformation$1$_closure1;

    invoke-direct {v0, p0, p0}, Lorg/codehaus/groovy/transform/ASTTestTransformation$1$_closure1;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    invoke-interface {p4, p1, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    invoke-interface {p2, p3, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    const-class p2, Lgroovy/lang/Binding;

    invoke-static {p1, p2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovy/lang/Binding;

    iput-object p1, p0, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->binding:Lgroovy/lang/Binding;

    invoke-virtual {p0}, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->$getStaticMetaClass()Lgroovy/lang/MetaClass;

    move-result-object p1

    iput-object p1, p0, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->metaClass:Lgroovy/lang/MetaClass;

    return-void
.end method


# virtual methods
.method protected synthetic $getStaticMetaClass()Lgroovy/lang/MetaClass;
    .locals 2

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    const-class v1, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;

    if-eq v0, v1, :cond_0

    invoke-static {p0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->initMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0

    :cond_0
    sget-object v0, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    if-nez v0, :cond_1

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getClassInfo(Ljava/lang/Class;)Lorg/codehaus/groovy/reflection/ClassInfo;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    :cond_1
    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0
.end method

.method public call(Lorg/codehaus/groovy/control/ProcessingUnit;I)V
    .locals 20

    move-object/from16 v0, p0

    invoke-static {}, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v1

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->isOrigInt()Z

    move-result v2

    const-string v3, "\n"

    const-string v4, "compilePhase"

    const-string v5, "compilationUnit"

    const-string v6, "sourceUnit"

    const-string v7, "node"

    const-string v8, "}"

    const/4 v9, 0x0

    const-string v10, "lookup"

    const/4 v11, 0x1

    .line 92
    invoke-static {v11}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v12

    const/4 v13, 0x0

    .line 84
    invoke-static {v13}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v14

    if-eqz v2, :cond_3

    .line 0
    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->isOrigZ()Z

    move-result v2

    if-eqz v2, :cond_3

    sget-boolean v2, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->__$stMC:Z

    if-nez v2, :cond_3

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->disabledStandardMetaClass()Z

    move-result v2

    if-nez v2, :cond_3

    .line 83
    iget-object v2, v0, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->phase:Lgroovy/lang/Reference;

    invoke-virtual {v2}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v2

    invoke-static {v2, v9}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-nez v2, :cond_1

    invoke-static/range {p2 .. p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    const/16 v9, 0x31

    aget-object v9, v1, v9

    iget-object v12, v0, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->phase:Lgroovy/lang/Reference;

    invoke-virtual {v12}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v12

    invoke-interface {v9, v12}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v9

    invoke-static {v2, v9}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_0

    goto :goto_0

    :cond_0
    move v2, v13

    goto :goto_1

    :cond_1
    :goto_0
    move v2, v11

    :goto_1
    if-eqz v2, :cond_7

    const/16 v2, 0x32

    .line 84
    aget-object v2, v1, v2

    iget-object v9, v0, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->nodes:Lgroovy/lang/Reference;

    invoke-virtual {v9}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v9

    const-class v12, [Lorg/codehaus/groovy/ast/ASTNode;

    invoke-static {v9, v12}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v9

    check-cast v9, [Lorg/codehaus/groovy/ast/ASTNode;

    invoke-static {v9, v13}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->objectArrayGet([Ljava/lang/Object;I)Ljava/lang/Object;

    move-result-object v9

    const-class v12, Lorg/codehaus/groovy/transform/ASTTestTransformation;

    invoke-interface {v2, v9, v12}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    const-class v9, Lorg/codehaus/groovy/ast/expr/ClosureExpression;

    invoke-static {v2, v9}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lorg/codehaus/groovy/ast/expr/ClosureExpression;

    const/16 v9, 0x33

    .line 85
    aget-object v9, v1, v9

    const-class v12, Ljava/lang/StringBuilder;

    invoke-interface {v9, v12}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v9

    const-class v12, Ljava/lang/StringBuilder;

    invoke-static {v9, v12}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v9

    check-cast v9, Ljava/lang/StringBuilder;

    const/16 v12, 0x34

    .line 86
    aget-object v12, v1, v12

    invoke-interface {v12, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v12

    invoke-static {v12}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->intUnbox(Ljava/lang/Object;)I

    move-result v12

    :goto_2
    invoke-static {v12}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v15

    const/16 v16, 0x35

    aget-object v11, v1, v16

    invoke-interface {v11, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v11

    invoke-static {v15, v11}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareLessThanEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v11

    if-eqz v11, :cond_2

    const/16 v11, 0x36

    .line 87
    aget-object v11, v1, v11

    const/16 v15, 0x37

    aget-object v15, v1, v15

    const/16 v16, 0x38

    aget-object v13, v1, v16

    const/16 v16, 0x39

    move-object/from16 v17, v10

    aget-object v10, v1, v16

    move-object/from16 v16, v4

    iget-object v4, v0, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->source:Lgroovy/lang/Reference;

    invoke-virtual {v4}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v4

    invoke-interface {v10, v4}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    invoke-static {v12}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v10

    const/16 v18, 0x3a

    move-object/from16 v19, v5

    aget-object v5, v1, v18

    move-object/from16 v18, v6

    const-class v6, Lorg/codehaus/groovy/control/Janitor;

    invoke-interface {v5, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    invoke-interface {v13, v4, v10, v5}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    invoke-interface {v15, v9, v4}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    invoke-interface {v11, v4, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    add-int/lit8 v12, v12, 0x1

    move-object/from16 v4, v16

    move-object/from16 v10, v17

    move-object/from16 v6, v18

    move-object/from16 v5, v19

    const/4 v11, 0x1

    const/4 v13, 0x0

    goto :goto_2

    :cond_2
    move-object/from16 v16, v4

    move-object/from16 v19, v5

    move-object/from16 v18, v6

    move-object/from16 v17, v10

    const/16 v3, 0x3b

    .line 89
    aget-object v3, v1, v3

    const/16 v4, 0x3c

    aget-object v4, v1, v4

    invoke-interface {v4, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    const/16 v4, 0x3d

    aget-object v4, v1, v4

    invoke-interface {v4, v9}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    const/4 v5, 0x0

    invoke-static {v2, v4, v5}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createRange(Ljava/lang/Object;Ljava/lang/Object;Z)Ljava/util/List;

    move-result-object v2

    invoke-interface {v3, v9, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    const/16 v3, 0x3e

    .line 90
    aget-object v3, v1, v3

    const/16 v4, 0x3f

    aget-object v4, v1, v4

    invoke-interface {v4, v2, v8}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    invoke-static {v14, v4, v5}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createRange(Ljava/lang/Object;Ljava/lang/Object;Z)Ljava/util/List;

    move-result-object v4

    invoke-interface {v3, v2, v4}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    .line 92
    iget-object v3, v0, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->nodes:Lgroovy/lang/Reference;

    invoke-virtual {v3}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v3

    const-class v4, [Lorg/codehaus/groovy/ast/ASTNode;

    invoke-static {v3, v4}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, [Lorg/codehaus/groovy/ast/ASTNode;

    const/4 v4, 0x1

    invoke-static {v3, v4}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->objectArrayGet([Ljava/lang/Object;I)Ljava/lang/Object;

    move-result-object v3

    const/16 v4, 0x40

    aget-object v4, v1, v4

    iget-object v5, v0, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->binding:Lgroovy/lang/Binding;

    invoke-interface {v4, v5, v7, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 93
    iget-object v3, v0, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->source:Lgroovy/lang/Reference;

    invoke-virtual {v3}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v3

    const/16 v4, 0x41

    aget-object v4, v1, v4

    iget-object v5, v0, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->binding:Lgroovy/lang/Binding;

    invoke-interface {v4, v5, v6, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v3, 0x42

    .line 94
    aget-object v3, v1, v3

    invoke-interface {v3, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGroovyObjectGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    const/16 v4, 0x43

    aget-object v4, v1, v4

    iget-object v5, v0, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->binding:Lgroovy/lang/Binding;

    move-object/from16 v10, v19

    invoke-interface {v4, v5, v10, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v3, 0x44

    .line 95
    aget-object v3, v1, v3

    const-class v4, Lorg/codehaus/groovy/control/CompilePhase;

    invoke-static/range {p2 .. p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v5

    invoke-interface {v3, v4, v5}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    const/16 v4, 0x45

    aget-object v4, v1, v4

    iget-object v5, v0, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->binding:Lgroovy/lang/Binding;

    move-object/from16 v11, v16

    invoke-interface {v4, v5, v11, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v3, 0x46

    .line 96
    aget-object v3, v1, v3

    const/16 v4, 0x47

    aget-object v4, v1, v4

    const-class v5, Lorg/codehaus/groovy/runtime/MethodClosure;

    const-class v6, Lorg/codehaus/groovy/transform/ASTTestTransformation$LabelFinder;

    move-object/from16 v13, v17

    invoke-interface {v4, v5, v6, v13}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    iget-object v5, v0, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->nodes:Lgroovy/lang/Reference;

    invoke-virtual {v5}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v5

    const-class v6, [Lorg/codehaus/groovy/ast/ASTNode;

    invoke-static {v5, v6}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v5

    check-cast v5, [Lorg/codehaus/groovy/ast/ASTNode;

    const/4 v15, 0x1

    invoke-static {v5, v15}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->objectArrayGet([Ljava/lang/Object;I)Ljava/lang/Object;

    move-result-object v5

    invoke-interface {v3, v4, v5}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    const/16 v4, 0x48

    aget-object v4, v1, v4

    iget-object v5, v0, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->binding:Lgroovy/lang/Binding;

    invoke-interface {v4, v5, v13, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v3, 0x49

    .line 98
    aget-object v3, v1, v3

    const-class v4, Lorg/codehaus/groovy/control/customizers/ImportCustomizer;

    invoke-interface {v3, v4}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    new-instance v4, Lgroovy/lang/Reference;

    invoke-direct {v4, v3}, Lgroovy/lang/Reference;-><init>(Ljava/lang/Object;)V

    const/16 v3, 0x4a

    .line 99
    aget-object v3, v1, v3

    const/16 v5, 0x4b

    aget-object v5, v1, v5

    const/16 v6, 0x4c

    aget-object v6, v1, v6

    iget-object v7, v0, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->source:Lgroovy/lang/Reference;

    invoke-virtual {v7}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v7

    invoke-interface {v6, v7}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    invoke-interface {v5, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    new-instance v6, Lorg/codehaus/groovy/transform/ASTTestTransformation$1$_call_closure2;

    invoke-direct {v6, v0, v0, v4}, Lorg/codehaus/groovy/transform/ASTTestTransformation$1$_call_closure2;-><init>(Ljava/lang/Object;Ljava/lang/Object;Lgroovy/lang/Reference;)V

    invoke-interface {v3, v5, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v3, 0x4d

    .line 102
    aget-object v3, v1, v3

    const/16 v5, 0x4e

    aget-object v5, v1, v5

    const/16 v6, 0x4f

    aget-object v6, v1, v6

    iget-object v7, v0, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->source:Lgroovy/lang/Reference;

    invoke-virtual {v7}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v7

    invoke-interface {v6, v7}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    invoke-interface {v5, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    new-instance v6, Lorg/codehaus/groovy/transform/ASTTestTransformation$1$_call_closure3;

    invoke-direct {v6, v0, v0, v4}, Lorg/codehaus/groovy/transform/ASTTestTransformation$1$_call_closure3;-><init>(Ljava/lang/Object;Ljava/lang/Object;Lgroovy/lang/Reference;)V

    invoke-interface {v3, v5, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v3, 0x50

    .line 105
    aget-object v3, v1, v3

    const/16 v5, 0x51

    aget-object v5, v1, v5

    const/16 v6, 0x52

    aget-object v6, v1, v6

    iget-object v7, v0, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->source:Lgroovy/lang/Reference;

    invoke-virtual {v7}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v7

    invoke-interface {v6, v7}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    invoke-interface {v5, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    new-instance v6, Lorg/codehaus/groovy/transform/ASTTestTransformation$1$_call_closure4;

    invoke-direct {v6, v0, v0, v4}, Lorg/codehaus/groovy/transform/ASTTestTransformation$1$_call_closure4;-><init>(Ljava/lang/Object;Ljava/lang/Object;Lgroovy/lang/Reference;)V

    invoke-interface {v3, v5, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v3, 0x53

    .line 108
    aget-object v3, v1, v3

    const/16 v5, 0x54

    aget-object v5, v1, v5

    const/16 v6, 0x55

    aget-object v6, v1, v6

    iget-object v7, v0, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->source:Lgroovy/lang/Reference;

    invoke-virtual {v7}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v7

    invoke-interface {v6, v7}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    invoke-interface {v5, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    new-instance v6, Lorg/codehaus/groovy/transform/ASTTestTransformation$1$_call_closure5;

    invoke-direct {v6, v0, v0, v4}, Lorg/codehaus/groovy/transform/ASTTestTransformation$1$_call_closure5;-><init>(Ljava/lang/Object;Ljava/lang/Object;Lgroovy/lang/Reference;)V

    invoke-interface {v3, v5, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v3, 0x56

    .line 112
    aget-object v3, v1, v3

    const-class v5, Lorg/codehaus/groovy/control/CompilerConfiguration;

    invoke-interface {v3, v5}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    const/16 v5, 0x57

    .line 113
    aget-object v5, v1, v5

    invoke-virtual {v4}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v4

    invoke-interface {v5, v3, v4}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v4, 0x58

    .line 114
    aget-object v4, v1, v4

    const/16 v5, 0x59

    aget-object v5, v1, v5

    invoke-interface {v5, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGroovyObjectGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    invoke-interface {v4, v5}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    const/16 v5, 0x5a

    .line 115
    aget-object v5, v1, v5

    const/16 v6, 0x5b

    aget-object v1, v1, v6

    :goto_3
    const-class v6, Lgroovy/lang/GroovyShell;

    iget-object v7, v0, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->binding:Lgroovy/lang/Binding;

    invoke-interface {v1, v6, v4, v7, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-interface {v5, v1, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto/16 :goto_6

    :cond_3
    move-object v13, v10

    move v15, v11

    move-object v11, v4

    move-object v10, v5

    .line 83
    iget-object v2, v0, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->phase:Lgroovy/lang/Reference;

    invoke-virtual {v2}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v2

    invoke-static {v2, v9}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-nez v2, :cond_5

    invoke-static/range {p2 .. p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    const/4 v4, 0x2

    aget-object v4, v1, v4

    iget-object v5, v0, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->phase:Lgroovy/lang/Reference;

    invoke-virtual {v5}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v5

    invoke-interface {v4, v5}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    invoke-static {v2, v4}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_4

    goto :goto_4

    :cond_4
    const/4 v15, 0x0

    :cond_5
    :goto_4
    if-eqz v15, :cond_7

    const/4 v2, 0x3

    .line 84
    aget-object v2, v1, v2

    const/4 v4, 0x4

    aget-object v4, v1, v4

    iget-object v5, v0, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->nodes:Lgroovy/lang/Reference;

    invoke-virtual {v5}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v5

    invoke-interface {v4, v5, v14}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    const-class v5, Lorg/codehaus/groovy/transform/ASTTestTransformation;

    invoke-interface {v2, v4, v5}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    const-class v4, Lorg/codehaus/groovy/ast/expr/ClosureExpression;

    invoke-static {v2, v4}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lorg/codehaus/groovy/ast/expr/ClosureExpression;

    const/4 v4, 0x5

    .line 85
    aget-object v4, v1, v4

    const-class v5, Ljava/lang/StringBuilder;

    invoke-interface {v4, v5}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    const-class v5, Ljava/lang/StringBuilder;

    invoke-static {v4, v5}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Ljava/lang/StringBuilder;

    const/4 v5, 0x6

    .line 86
    aget-object v5, v1, v5

    invoke-interface {v5, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    invoke-static {v5}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->intUnbox(Ljava/lang/Object;)I

    move-result v5

    :goto_5
    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v9

    const/4 v15, 0x7

    aget-object v15, v1, v15

    invoke-interface {v15, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v15

    invoke-static {v9, v15}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareLessThanEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v9

    if-eqz v9, :cond_6

    const/16 v9, 0x8

    .line 87
    aget-object v9, v1, v9

    const/16 v15, 0x9

    aget-object v15, v1, v15

    const/16 v16, 0xa

    move-object/from16 v17, v13

    aget-object v13, v1, v16

    const/16 v16, 0xb

    move-object/from16 v18, v11

    aget-object v11, v1, v16

    move-object/from16 v19, v10

    iget-object v10, v0, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->source:Lgroovy/lang/Reference;

    invoke-virtual {v10}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v10

    invoke-interface {v11, v10}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v10

    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v11

    const/16 v16, 0xc

    move-object/from16 p1, v6

    aget-object v6, v1, v16

    move-object/from16 v16, v7

    const-class v7, Lorg/codehaus/groovy/control/Janitor;

    invoke-interface {v6, v7}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    invoke-interface {v13, v10, v11, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    invoke-interface {v15, v4, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    invoke-interface {v9, v6, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v6, 0xd

    .line 86
    aget-object v6, v1, v6

    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v5

    invoke-interface {v6, v5, v12}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    invoke-static {v5}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->intUnbox(Ljava/lang/Object;)I

    move-result v5

    move-object/from16 v6, p1

    move-object/from16 v7, v16

    move-object/from16 v13, v17

    move-object/from16 v11, v18

    move-object/from16 v10, v19

    goto :goto_5

    :cond_6
    move-object/from16 p1, v6

    move-object/from16 v16, v7

    move-object/from16 v19, v10

    move-object/from16 v18, v11

    move-object/from16 v17, v13

    const/16 v3, 0xe

    .line 89
    aget-object v3, v1, v3

    const/16 v5, 0xf

    aget-object v5, v1, v5

    invoke-interface {v5, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    const/16 v5, 0x10

    aget-object v5, v1, v5

    invoke-interface {v5, v4}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    const/4 v6, 0x0

    invoke-static {v2, v5, v6}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createRange(Ljava/lang/Object;Ljava/lang/Object;Z)Ljava/util/List;

    move-result-object v2

    invoke-interface {v3, v4, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    const/16 v3, 0x11

    .line 90
    aget-object v3, v1, v3

    const/16 v4, 0x12

    aget-object v4, v1, v4

    invoke-interface {v4, v2, v8}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    invoke-static {v14, v4, v6}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createRange(Ljava/lang/Object;Ljava/lang/Object;Z)Ljava/util/List;

    move-result-object v4

    invoke-interface {v3, v2, v4}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    const/16 v3, 0x13

    .line 92
    aget-object v3, v1, v3

    iget-object v4, v0, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->nodes:Lgroovy/lang/Reference;

    invoke-virtual {v4}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v4

    invoke-interface {v3, v4, v12}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    const/16 v4, 0x14

    aget-object v4, v1, v4

    iget-object v5, v0, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->binding:Lgroovy/lang/Binding;

    move-object/from16 v6, v16

    invoke-interface {v4, v5, v6, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 93
    iget-object v3, v0, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->source:Lgroovy/lang/Reference;

    invoke-virtual {v3}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v3

    const/16 v4, 0x15

    aget-object v4, v1, v4

    iget-object v5, v0, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->binding:Lgroovy/lang/Binding;

    move-object/from16 v6, p1

    invoke-interface {v4, v5, v6, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v3, 0x16

    .line 94
    aget-object v3, v1, v3

    invoke-interface {v3, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGroovyObjectGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    const/16 v4, 0x17

    aget-object v4, v1, v4

    iget-object v5, v0, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->binding:Lgroovy/lang/Binding;

    move-object/from16 v6, v19

    invoke-interface {v4, v5, v6, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v3, 0x18

    .line 95
    aget-object v3, v1, v3

    const-class v4, Lorg/codehaus/groovy/control/CompilePhase;

    invoke-static/range {p2 .. p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v5

    invoke-interface {v3, v4, v5}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    const/16 v4, 0x19

    aget-object v4, v1, v4

    iget-object v5, v0, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->binding:Lgroovy/lang/Binding;

    move-object/from16 v6, v18

    invoke-interface {v4, v5, v6, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v3, 0x1a

    .line 96
    aget-object v3, v1, v3

    const/16 v4, 0x1b

    aget-object v4, v1, v4

    const-class v5, Lorg/codehaus/groovy/runtime/MethodClosure;

    const-class v6, Lorg/codehaus/groovy/transform/ASTTestTransformation$LabelFinder;

    move-object/from16 v7, v17

    invoke-interface {v4, v5, v6, v7}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    const/16 v5, 0x1c

    aget-object v5, v1, v5

    iget-object v6, v0, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->nodes:Lgroovy/lang/Reference;

    invoke-virtual {v6}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v6

    invoke-interface {v5, v6, v12}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    invoke-interface {v3, v4, v5}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    const/16 v4, 0x1d

    aget-object v4, v1, v4

    iget-object v5, v0, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->binding:Lgroovy/lang/Binding;

    invoke-interface {v4, v5, v7, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v3, 0x1e

    .line 98
    aget-object v3, v1, v3

    const-class v4, Lorg/codehaus/groovy/control/customizers/ImportCustomizer;

    invoke-interface {v3, v4}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    new-instance v4, Lgroovy/lang/Reference;

    invoke-direct {v4, v3}, Lgroovy/lang/Reference;-><init>(Ljava/lang/Object;)V

    const/16 v3, 0x1f

    .line 99
    aget-object v3, v1, v3

    const/16 v5, 0x20

    aget-object v5, v1, v5

    const/16 v6, 0x21

    aget-object v6, v1, v6

    iget-object v7, v0, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->source:Lgroovy/lang/Reference;

    invoke-virtual {v7}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v7

    invoke-interface {v6, v7}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    invoke-interface {v5, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    new-instance v6, Lorg/codehaus/groovy/transform/ASTTestTransformation$1$_call_closure2;

    invoke-direct {v6, v0, v0, v4}, Lorg/codehaus/groovy/transform/ASTTestTransformation$1$_call_closure2;-><init>(Ljava/lang/Object;Ljava/lang/Object;Lgroovy/lang/Reference;)V

    invoke-interface {v3, v5, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v3, 0x22

    .line 102
    aget-object v3, v1, v3

    const/16 v5, 0x23

    aget-object v5, v1, v5

    const/16 v6, 0x24

    aget-object v6, v1, v6

    iget-object v7, v0, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->source:Lgroovy/lang/Reference;

    invoke-virtual {v7}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v7

    invoke-interface {v6, v7}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    invoke-interface {v5, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    new-instance v6, Lorg/codehaus/groovy/transform/ASTTestTransformation$1$_call_closure3;

    invoke-direct {v6, v0, v0, v4}, Lorg/codehaus/groovy/transform/ASTTestTransformation$1$_call_closure3;-><init>(Ljava/lang/Object;Ljava/lang/Object;Lgroovy/lang/Reference;)V

    invoke-interface {v3, v5, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v3, 0x25

    .line 105
    aget-object v3, v1, v3

    const/16 v5, 0x26

    aget-object v5, v1, v5

    const/16 v6, 0x27

    aget-object v6, v1, v6

    iget-object v7, v0, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->source:Lgroovy/lang/Reference;

    invoke-virtual {v7}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v7

    invoke-interface {v6, v7}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    invoke-interface {v5, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    new-instance v6, Lorg/codehaus/groovy/transform/ASTTestTransformation$1$_call_closure4;

    invoke-direct {v6, v0, v0, v4}, Lorg/codehaus/groovy/transform/ASTTestTransformation$1$_call_closure4;-><init>(Ljava/lang/Object;Ljava/lang/Object;Lgroovy/lang/Reference;)V

    invoke-interface {v3, v5, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v3, 0x28

    .line 108
    aget-object v3, v1, v3

    const/16 v5, 0x29

    aget-object v5, v1, v5

    const/16 v6, 0x2a

    aget-object v6, v1, v6

    iget-object v7, v0, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->source:Lgroovy/lang/Reference;

    invoke-virtual {v7}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v7

    invoke-interface {v6, v7}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    invoke-interface {v5, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    new-instance v6, Lorg/codehaus/groovy/transform/ASTTestTransformation$1$_call_closure5;

    invoke-direct {v6, v0, v0, v4}, Lorg/codehaus/groovy/transform/ASTTestTransformation$1$_call_closure5;-><init>(Ljava/lang/Object;Ljava/lang/Object;Lgroovy/lang/Reference;)V

    invoke-interface {v3, v5, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v3, 0x2b

    .line 112
    aget-object v3, v1, v3

    const-class v5, Lorg/codehaus/groovy/control/CompilerConfiguration;

    invoke-interface {v3, v5}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    const/16 v5, 0x2c

    .line 113
    aget-object v5, v1, v5

    invoke-virtual {v4}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v4

    invoke-interface {v5, v3, v4}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v4, 0x2d

    .line 114
    aget-object v4, v1, v4

    const/16 v5, 0x2e

    aget-object v5, v1, v5

    invoke-interface {v5, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGroovyObjectGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    invoke-interface {v4, v5}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    const/16 v5, 0x2f

    .line 115
    aget-object v5, v1, v5

    const/16 v6, 0x30

    aget-object v1, v1, v6

    goto/16 :goto_3

    :cond_7
    :goto_6
    return-void
.end method

.method public getMetaClass()Lgroovy/lang/MetaClass;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    .annotation runtime Lgroovy/transform/Internal;
    .end annotation

    .annotation runtime Lgroovyjarjaropenbeans/Transient;
    .end annotation

    iget-object v0, p0, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->metaClass:Lgroovy/lang/MetaClass;

    if-eqz v0, :cond_0

    return-object v0

    :cond_0
    invoke-virtual {p0}, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->$getStaticMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->metaClass:Lgroovy/lang/MetaClass;

    return-object v0
.end method

.method public synthetic methodMissing(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    invoke-static {}, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    iget-object v0, p0, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->this$0:Lorg/codehaus/groovy/transform/ASTTestTransformation;

    invoke-virtual {v0, p1, p2}, Lorg/codehaus/groovy/transform/ASTTestTransformation;->this$dist$invoke$1(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public synthetic propertyMissing(Ljava/lang/String;)Ljava/lang/Object;
    .locals 1

    invoke-static {}, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    iget-object v0, p0, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->this$0:Lorg/codehaus/groovy/transform/ASTTestTransformation;

    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/transform/ASTTestTransformation;->this$dist$get$1(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public synthetic propertyMissing(Ljava/lang/String;Ljava/lang/Object;)V
    .locals 1

    invoke-static {}, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    iget-object v0, p0, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->this$0:Lorg/codehaus/groovy/transform/ASTTestTransformation;

    invoke-virtual {v0, p1, p2}, Lorg/codehaus/groovy/transform/ASTTestTransformation;->this$dist$set$1(Ljava/lang/String;Ljava/lang/Object;)V

    return-void
.end method

.method public setMetaClass(Lgroovy/lang/MetaClass;)V
    .locals 0
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    .annotation runtime Lgroovy/transform/Internal;
    .end annotation

    iput-object p1, p0, Lorg/codehaus/groovy/transform/ASTTestTransformation$1;->metaClass:Lgroovy/lang/MetaClass;

    return-void
.end method
