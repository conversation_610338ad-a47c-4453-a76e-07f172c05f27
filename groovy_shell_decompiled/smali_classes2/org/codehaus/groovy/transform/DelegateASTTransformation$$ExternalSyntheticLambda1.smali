.class public final synthetic Lorg/codehaus/groovy/transform/DelegateASTTransformation$$ExternalSyntheticLambda1;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Predicate;


# instance fields
.field public final synthetic f$0:Lorg/codehaus/groovy/ast/MethodNode;

.field public final synthetic f$1:[Lorg/codehaus/groovy/ast/Parameter;


# direct methods
.method public synthetic constructor <init>(Lorg/codehaus/groovy/ast/MethodNode;[Lorg/codehaus/groovy/ast/Parameter;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/codehaus/groovy/transform/DelegateASTTransformation$$ExternalSyntheticLambda1;->f$0:Lorg/codehaus/groovy/ast/MethodNode;

    iput-object p2, p0, Lorg/codehaus/groovy/transform/DelegateASTTransformation$$ExternalSyntheticLambda1;->f$1:[Lorg/codehaus/groovy/ast/Parameter;

    return-void
.end method


# virtual methods
.method public final test(Ljava/lang/Object;)Z
    .locals 2

    iget-object v0, p0, Lorg/codehaus/groovy/transform/DelegateASTTransformation$$ExternalSyntheticLambda1;->f$0:Lorg/codehaus/groovy/ast/MethodNode;

    iget-object v1, p0, Lorg/codehaus/groovy/transform/DelegateASTTransformation$$ExternalSyntheticLambda1;->f$1:[Lorg/codehaus/groovy/ast/Parameter;

    check-cast p1, Lorg/codehaus/groovy/ast/MethodNode;

    invoke-static {v0, v1, p1}, Lorg/codehaus/groovy/transform/DelegateASTTransformation;->lambda$collectMethods$1(Lorg/codehaus/groovy/ast/MethodNode;[Lorg/codehaus/groovy/ast/Parameter;Lorg/codehaus/groovy/ast/MethodNode;)Z

    move-result p1

    return p1
.end method
