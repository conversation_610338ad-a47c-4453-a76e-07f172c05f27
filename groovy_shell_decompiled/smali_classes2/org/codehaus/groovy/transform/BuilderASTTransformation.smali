.class public Lorg/codehaus/groovy/transform/BuilderASTTransformation;
.super Lorg/codehaus/groovy/transform/AbstractASTTransformation;
.source "BuilderASTTransformation.java"

# interfaces
.implements Lgroovy/transform/CompilationUnitAware;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/codehaus/groovy/transform/BuilderASTTransformation$BuilderStrategy;,
        Lorg/codehaus/groovy/transform/BuilderASTTransformation$AbstractBuilderStrategy;
    }
.end annotation

.annotation runtime Lorg/codehaus/groovy/transform/GroovyASTTransformation;
    phase = .enum Lorg/codehaus/groovy/control/CompilePhase;->SEMANTIC_ANALYSIS:Lorg/codehaus/groovy/control/CompilePhase;
.end annotation


# static fields
.field private static final MY_CLASS:Ljava/lang/Class;

.field private static final MY_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

.field public static final MY_TYPE_NAME:Ljava/lang/String;

.field public static final NO_EXCEPTIONS:[Lorg/codehaus/groovy/ast/ClassNode;

.field public static final NO_PARAMS:[Lorg/codehaus/groovy/ast/Parameter;


# instance fields
.field private compilationUnit:Lorg/codehaus/groovy/control/CompilationUnit;


# direct methods
.method static constructor <clinit>()V
    .locals 3

    .line 57
    const-class v0, Lgroovy/transform/builder/Builder;

    sput-object v0, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->MY_CLASS:Ljava/lang/Class;

    .line 58
    invoke-static {v0}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->MY_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    .line 59
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "@"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->getNameWithoutPackage()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->MY_TYPE_NAME:Ljava/lang/String;

    .line 60
    sget-object v0, Lorg/codehaus/groovy/ast/ClassNode;->EMPTY_ARRAY:[Lorg/codehaus/groovy/ast/ClassNode;

    sput-object v0, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->NO_EXCEPTIONS:[Lorg/codehaus/groovy/ast/ClassNode;

    .line 61
    sget-object v0, Lorg/codehaus/groovy/ast/Parameter;->EMPTY_ARRAY:[Lorg/codehaus/groovy/ast/Parameter;

    sput-object v0, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->NO_PARAMS:[Lorg/codehaus/groovy/ast/Parameter;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 55
    invoke-direct {p0}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;-><init>()V

    return-void
.end method

.method private checkStatic(Lorg/codehaus/groovy/ast/MethodNode;Ljava/lang/String;)Z
    .locals 2

    .line 240
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->isStatic()Z

    move-result v0

    if-nez v0, :cond_0

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->isStaticConstructor()Z

    move-result v0

    if-nez v0, :cond_0

    instance-of v0, p1, Lorg/codehaus/groovy/ast/ConstructorNode;

    if-nez v0, :cond_0

    .line 241
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Error processing method \'"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "\'. "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string v0, " not allowed for instance methods."

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p2, p1}, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    const/4 p1, 0x0

    return p1

    :cond_0
    const/4 p1, 0x1

    return p1
.end method

.method private createBuilderStrategy(Lorg/codehaus/groovy/ast/AnnotationNode;Lgroovy/lang/GroovyClassLoader;)Lorg/codehaus/groovy/transform/BuilderASTTransformation$BuilderStrategy;
    .locals 5

    .line 249
    const-class v0, Lgroovy/transform/builder/DefaultStrategy;

    invoke-static {v0}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    const-string v1, "builderStrategy"

    invoke-virtual {p0, p1, v1, v0}, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->getMemberClassValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    const/4 v1, 0x0

    if-nez v0, :cond_0

    const-string p2, "Couldn\'t determine builderStrategy class"

    .line 252
    invoke-virtual {p0, p2, p1}, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    return-object v1

    .line 256
    :cond_0
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v2

    .line 258
    :try_start_0
    invoke-virtual {p2, v2}, Lgroovy/lang/GroovyClassLoader;->loadClass(Ljava/lang/String;)Ljava/lang/Class;

    move-result-object p2

    const/4 v3, 0x0

    new-array v4, v3, [Ljava/lang/Class;

    invoke-virtual {p2, v4}, Ljava/lang/Class;->getDeclaredConstructor([Ljava/lang/Class;)Ljava/lang/reflect/Constructor;

    move-result-object p2

    new-array v3, v3, [Ljava/lang/Object;

    invoke-virtual {p2, v3}, Ljava/lang/reflect/Constructor;->newInstance([Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p2

    .line 259
    const-class v3, Lorg/codehaus/groovy/transform/BuilderASTTransformation$BuilderStrategy;

    invoke-virtual {p2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v4

    invoke-virtual {v3, v4}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    move-result v3

    if-nez v3, :cond_1

    .line 260
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "The builderStrategy class \'"

    invoke-virtual {p2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string v0, "\' on "

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    sget-object v0, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->MY_TYPE_NAME:Ljava/lang/String;

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string v0, " is not a builderStrategy"

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p2, p1}, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    return-object v1

    .line 264
    :cond_1
    check-cast p2, Lorg/codehaus/groovy/transform/BuilderASTTransformation$BuilderStrategy;
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return-object p2

    :catch_0
    move-exception p2

    .line 266
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "Can\'t load builderStrategy \'"

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v2, "\' "

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p2, p1}, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    return-object v1
.end method


# virtual methods
.method public setCompilationUnit(Lorg/codehaus/groovy/control/CompilationUnit;)V
    .locals 0

    .line 272
    iput-object p1, p0, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->compilationUnit:Lorg/codehaus/groovy/control/CompilationUnit;

    return-void
.end method

.method public visit([Lorg/codehaus/groovy/ast/ASTNode;Lorg/codehaus/groovy/control/SourceUnit;)V
    .locals 3

    .line 66
    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->init([Lorg/codehaus/groovy/ast/ASTNode;Lorg/codehaus/groovy/control/SourceUnit;)V

    const/4 v0, 0x1

    .line 67
    aget-object v0, p1, v0

    check-cast v0, Lorg/codehaus/groovy/ast/AnnotatedNode;

    const/4 v1, 0x0

    .line 68
    aget-object p1, p1, v1

    check-cast p1, Lorg/codehaus/groovy/ast/AnnotationNode;

    .line 69
    sget-object v1, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->MY_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/AnnotationNode;->getClassNode()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v2

    invoke-virtual {v1, v2}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_0

    return-void

    .line 71
    :cond_0
    instance-of v1, v0, Lorg/codehaus/groovy/ast/ClassNode;

    if-nez v1, :cond_1

    instance-of v2, v0, Lorg/codehaus/groovy/ast/MethodNode;

    if-eqz v2, :cond_6

    :cond_1
    if-eqz v1, :cond_2

    .line 72
    move-object v1, v0

    check-cast v1, Lorg/codehaus/groovy/ast/ClassNode;

    sget-object v2, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->MY_TYPE_NAME:Ljava/lang/String;

    invoke-virtual {p0, v1, v2}, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->checkNotInterface(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Z

    move-result v1

    if-nez v1, :cond_2

    return-void

    .line 73
    :cond_2
    instance-of v1, v0, Lorg/codehaus/groovy/ast/MethodNode;

    if-eqz v1, :cond_3

    move-object v1, v0

    check-cast v1, Lorg/codehaus/groovy/ast/MethodNode;

    sget-object v2, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->MY_TYPE_NAME:Ljava/lang/String;

    invoke-direct {p0, v1, v2}, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->checkStatic(Lorg/codehaus/groovy/ast/MethodNode;Ljava/lang/String;)Z

    move-result v1

    if-nez v1, :cond_3

    return-void

    .line 74
    :cond_3
    iget-object v1, p0, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->compilationUnit:Lorg/codehaus/groovy/control/CompilationUnit;

    if-eqz v1, :cond_4

    invoke-virtual {v1}, Lorg/codehaus/groovy/control/CompilationUnit;->getTransformLoader()Lgroovy/lang/GroovyClassLoader;

    move-result-object p2

    goto :goto_0

    :cond_4
    invoke-virtual {p2}, Lorg/codehaus/groovy/control/SourceUnit;->getClassLoader()Lgroovy/lang/GroovyClassLoader;

    move-result-object p2

    .line 75
    :goto_0
    invoke-direct {p0, p1, p2}, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->createBuilderStrategy(Lorg/codehaus/groovy/ast/AnnotationNode;Lgroovy/lang/GroovyClassLoader;)Lorg/codehaus/groovy/transform/BuilderASTTransformation$BuilderStrategy;

    move-result-object p2

    if-nez p2, :cond_5

    return-void

    .line 77
    :cond_5
    invoke-interface {p2, p0, v0, p1}, Lorg/codehaus/groovy/transform/BuilderASTTransformation$BuilderStrategy;->build(Lorg/codehaus/groovy/transform/BuilderASTTransformation;Lorg/codehaus/groovy/ast/AnnotatedNode;Lorg/codehaus/groovy/ast/AnnotationNode;)V

    :cond_6
    return-void
.end method
