.class public Lorg/codehaus/groovy/transform/ReadWriteLockASTTransformation;
.super Lorg/codehaus/groovy/transform/AbstractASTTransformation;
.source "ReadWriteLockASTTransformation.java"


# annotations
.annotation runtime Lorg/codehaus/groovy/transform/GroovyASTTransformation;
    phase = .enum Lorg/codehaus/groovy/control/CompilePhase;->CANONICALIZATION:Lorg/codehaus/groovy/control/CompilePhase;
.end annotation


# static fields
.field public static final DEFAULT_INSTANCE_LOCKNAME:Ljava/lang/String; = "$reentrantlock"

.field public static final DEFAULT_STATIC_LOCKNAME:Ljava/lang/String; = "$REENTRANTLOCK"

.field private static final LOCK_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

.field private static final READ_LOCK_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

.field private static final WRITE_LOCK_TYPE:Lorg/codehaus/groovy/ast/ClassNode;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 57
    const-class v0, Lgroovy/transform/WithReadLock;

    invoke-static {v0}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/transform/ReadWriteLockASTTransformation;->READ_LOCK_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    .line 58
    const-class v0, Lgroovy/transform/WithWriteLock;

    invoke-static {v0}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/transform/ReadWriteLockASTTransformation;->WRITE_LOCK_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    .line 59
    const-class v0, Ljava/util/concurrent/locks/ReentrantReadWriteLock;

    invoke-static {v0}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/transform/ReadWriteLockASTTransformation;->LOCK_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 55
    invoke-direct {p0}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;-><init>()V

    return-void
.end method

.method private static createLockObject()Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 1

    .line 140
    sget-object v0, Lorg/codehaus/groovy/transform/ReadWriteLockASTTransformation;->LOCK_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ctorX(Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;

    move-result-object v0

    return-object v0
.end method

.method private determineLock(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;ZLjava/lang/String;)Lorg/codehaus/groovy/ast/FieldNode;
    .locals 4

    const/4 v0, 0x0

    const-string v1, "Error during "

    const-string v2, "$reentrantlock"

    if-eqz p1, :cond_3

    .line 105
    invoke-virtual {p1}, Ljava/lang/String;->length()I

    move-result v3

    if-lez v3, :cond_3

    invoke-virtual {p1, v2}, Ljava/lang/String;->equalsIgnoreCase(Ljava/lang/String;)Z

    move-result v3

    if-nez v3, :cond_3

    .line 106
    invoke-virtual {p2, p1}, Lorg/codehaus/groovy/ast/ClassNode;->getDeclaredField(Ljava/lang/String;)Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object v2

    const-string v3, " processing: lock field with name \'"

    if-nez v2, :cond_0

    .line 108
    new-instance p3, Ljava/lang/StringBuilder;

    invoke-direct {p3}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p3

    invoke-virtual {p3, p4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p3

    invoke-virtual {p3, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p3

    invoke-virtual {p3, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string p3, "\' not found in class "

    invoke-virtual {p1, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object p3

    invoke-virtual {p1, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/transform/ReadWriteLockASTTransformation;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    return-object v0

    .line 111
    :cond_0
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/FieldNode;->isStatic()Z

    move-result p2

    if-eq p2, p3, :cond_2

    .line 112
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, p4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string p2, "\' should "

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    if-eqz p3, :cond_1

    const-string p2, ""

    goto :goto_0

    :cond_1
    const-string p2, "not "

    :goto_0
    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string p2, "be static"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1, v2}, Lorg/codehaus/groovy/transform/ReadWriteLockASTTransformation;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    return-object v0

    :cond_2
    return-object v2

    :cond_3
    const-string p1, " processing: "

    if-eqz p3, :cond_6

    const-string p3, "$REENTRANTLOCK"

    .line 118
    invoke-virtual {p2, p3}, Lorg/codehaus/groovy/ast/ClassNode;->getDeclaredField(Ljava/lang/String;)Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object v2

    if-nez v2, :cond_4

    const/16 p1, 0x1a

    .line 121
    sget-object p4, Lorg/codehaus/groovy/transform/ReadWriteLockASTTransformation;->LOCK_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {}, Lorg/codehaus/groovy/transform/ReadWriteLockASTTransformation;->createLockObject()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    invoke-virtual {p2, p3, p1, p4, v0}, Lorg/codehaus/groovy/ast/ClassNode;->addField(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object v2

    goto :goto_1

    .line 122
    :cond_4
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/FieldNode;->isStatic()Z

    move-result p2

    if-nez p2, :cond_5

    .line 123
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, p4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string p2, " field must be static"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1, v2}, Lorg/codehaus/groovy/transform/ReadWriteLockASTTransformation;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    return-object v0

    :cond_5
    :goto_1
    return-object v2

    .line 128
    :cond_6
    invoke-virtual {p2, v2}, Lorg/codehaus/groovy/ast/ClassNode;->getDeclaredField(Ljava/lang/String;)Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object p3

    if-nez p3, :cond_7

    const/16 p1, 0x12

    .line 131
    sget-object p3, Lorg/codehaus/groovy/transform/ReadWriteLockASTTransformation;->LOCK_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {}, Lorg/codehaus/groovy/transform/ReadWriteLockASTTransformation;->createLockObject()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p4

    invoke-virtual {p2, v2, p1, p3, p4}, Lorg/codehaus/groovy/ast/ClassNode;->addField(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object p3

    goto :goto_2

    .line 132
    :cond_7
    invoke-virtual {p3}, Lorg/codehaus/groovy/ast/FieldNode;->isStatic()Z

    move-result p2

    if-eqz p2, :cond_8

    .line 133
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, p4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string p2, " field must not be static"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1, p3}, Lorg/codehaus/groovy/transform/ReadWriteLockASTTransformation;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    return-object v0

    :cond_8
    :goto_2
    return-object p3
.end method


# virtual methods
.method public visit([Lorg/codehaus/groovy/ast/ASTNode;Lorg/codehaus/groovy/control/SourceUnit;)V
    .locals 6

    .line 64
    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/transform/ReadWriteLockASTTransformation;->init([Lorg/codehaus/groovy/ast/ASTNode;Lorg/codehaus/groovy/control/SourceUnit;)V

    const/4 p2, 0x1

    .line 65
    aget-object v0, p1, p2

    check-cast v0, Lorg/codehaus/groovy/ast/AnnotatedNode;

    const/4 v1, 0x0

    .line 66
    aget-object p1, p1, v1

    check-cast p1, Lorg/codehaus/groovy/ast/AnnotationNode;

    .line 68
    sget-object v2, Lorg/codehaus/groovy/transform/ReadWriteLockASTTransformation;->READ_LOCK_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/AnnotationNode;->getClassNode()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v3

    invoke-virtual {v2, v3}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_0

    move v2, v1

    goto :goto_0

    .line 70
    :cond_0
    sget-object v3, Lorg/codehaus/groovy/transform/ReadWriteLockASTTransformation;->WRITE_LOCK_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/AnnotationNode;->getClassNode()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v4

    invoke-virtual {v3, v4}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result v4

    if-eqz v4, :cond_4

    move v2, p2

    .line 76
    :goto_0
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "@"

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/AnnotationNode;->getClassNode()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v4

    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/ClassNode;->getNameWithoutPackage()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    const-string v4, "value"

    .line 78
    invoke-static {p1, v4}, Lorg/codehaus/groovy/transform/ReadWriteLockASTTransformation;->getMemberStringValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    .line 80
    instance-of v4, v0, Lorg/codehaus/groovy/ast/MethodNode;

    if-eqz v4, :cond_3

    .line 81
    check-cast v0, Lorg/codehaus/groovy/ast/MethodNode;

    .line 82
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/MethodNode;->getDeclaringClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v4

    .line 83
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/MethodNode;->isStatic()Z

    move-result v5

    invoke-direct {p0, p1, v4, v5, v3}, Lorg/codehaus/groovy/transform/ReadWriteLockASTTransformation;->determineLock(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;ZLjava/lang/String;)Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object p1

    if-nez p1, :cond_1

    return-void

    .line 87
    :cond_1
    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object p1

    if-eqz v2, :cond_2

    const-string v2, "writeLock"

    goto :goto_1

    :cond_2
    const-string v2, "readLock"

    :goto_1
    invoke-static {p1, v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object p1

    .line 88
    invoke-virtual {p1, v1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setImplicitThis(Z)V

    const-string v2, "lock"

    .line 90
    invoke-static {p1, v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object v2

    .line 91
    invoke-virtual {v2, v1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setImplicitThis(Z)V

    const-string v3, "unlock"

    .line 93
    invoke-static {p1, v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object p1

    .line 94
    invoke-virtual {p1, v1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setImplicitThis(Z)V

    .line 96
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/MethodNode;->getCode()Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v3

    const/4 v4, 0x2

    new-array v4, v4, [Lorg/codehaus/groovy/ast/stmt/Statement;

    .line 99
    invoke-static {v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->stmt(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v2

    aput-object v2, v4, v1

    new-instance v1, Lorg/codehaus/groovy/ast/stmt/TryCatchStatement;

    .line 100
    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->stmt(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    invoke-direct {v1, v3, p1}, Lorg/codehaus/groovy/ast/stmt/TryCatchStatement;-><init>(Lorg/codehaus/groovy/ast/stmt/Statement;Lorg/codehaus/groovy/ast/stmt/Statement;)V

    aput-object v1, v4, p2

    .line 98
    invoke-static {v4}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->block([Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    move-result-object p1

    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/ast/MethodNode;->setCode(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    :cond_3
    return-void

    .line 73
    :cond_4
    new-instance p2, Lorg/codehaus/groovy/GroovyBugError;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Internal error: expecting ["

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ", "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "] but got: "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/AnnotationNode;->getClassNode()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {p2, p1}, Lorg/codehaus/groovy/GroovyBugError;-><init>(Ljava/lang/String;)V

    throw p2
.end method
