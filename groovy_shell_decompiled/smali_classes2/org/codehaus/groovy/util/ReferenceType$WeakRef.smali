.class Lorg/codehaus/groovy/util/ReferenceType$WeakRef;
.super Ljava/lang/ref/WeakReference;
.source "ReferenceType.java"

# interfaces
.implements Lorg/codehaus/groovy/util/Reference;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/codehaus/groovy/util/ReferenceType;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0xa
    name = "WeakRef"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "<TT:",
        "Ljava/lang/Object;",
        "V::",
        "Lorg/codehaus/groovy/util/Finalizable;",
        ">",
        "Ljava/lang/ref/WeakReference<",
        "TTT;>;",
        "Lorg/codehaus/groovy/util/Reference<",
        "TTT;TV;>;"
    }
.end annotation


# instance fields
.field private final handler:Lorg/codehaus/groovy/util/Finalizable;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "TV;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Ljava/lang/Object;Lorg/codehaus/groovy/util/Finalizable;Ljava/lang/ref/ReferenceQueue;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TTT;TV;",
            "Ljava/lang/ref/ReferenceQueue<",
            "-TTT;>;)V"
        }
    .end annotation

    .line 68
    invoke-direct {p0, p1, p3}, Ljava/lang/ref/WeakReference;-><init>(Ljava/lang/Object;Ljava/lang/ref/ReferenceQueue;)V

    .line 69
    iput-object p2, p0, Lorg/codehaus/groovy/util/ReferenceType$WeakRef;->handler:Lorg/codehaus/groovy/util/Finalizable;

    return-void
.end method


# virtual methods
.method public getHandler()Lorg/codehaus/groovy/util/Finalizable;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()TV;"
        }
    .end annotation

    .line 72
    iget-object v0, p0, Lorg/codehaus/groovy/util/ReferenceType$WeakRef;->handler:Lorg/codehaus/groovy/util/Finalizable;

    return-object v0
.end method
