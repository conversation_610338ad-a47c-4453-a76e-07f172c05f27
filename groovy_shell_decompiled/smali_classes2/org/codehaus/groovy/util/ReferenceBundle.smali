.class public Lorg/codehaus/groovy/util/ReferenceBundle;
.super Ljava/lang/Object;
.source "ReferenceBundle.java"


# static fields
.field private static final hardReferences:Lorg/codehaus/groovy/util/ReferenceBundle;

.field private static final phantomReferences:Lorg/codehaus/groovy/util/ReferenceBundle;

.field private static final softReferences:Lorg/codehaus/groovy/util/ReferenceBundle;

.field private static final weakReferences:Lorg/codehaus/groovy/util/ReferenceBundle;


# instance fields
.field private final manager:Lorg/codehaus/groovy/util/ReferenceManager;

.field private final type:Lorg/codehaus/groovy/util/ReferenceType;


# direct methods
.method static constructor <clinit>()V
    .locals 3

    .line 39
    new-instance v0, Ljava/lang/ref/ReferenceQueue;

    invoke-direct {v0}, Ljava/lang/ref/ReferenceQueue;-><init>()V

    .line 40
    invoke-static {v0}, Lorg/codehaus/groovy/util/ReferenceManager;->createCallBackedManager(Ljava/lang/ref/ReferenceQueue;)Lorg/codehaus/groovy/util/ReferenceManager;

    move-result-object v1

    const/16 v2, 0x1f4

    .line 41
    invoke-static {v0, v1, v2}, Lorg/codehaus/groovy/util/ReferenceManager;->createThresholdedIdlingManager(Ljava/lang/ref/ReferenceQueue;Lorg/codehaus/groovy/util/ReferenceManager;I)Lorg/codehaus/groovy/util/ReferenceManager;

    move-result-object v0

    .line 42
    new-instance v1, Lorg/codehaus/groovy/util/ReferenceBundle;

    sget-object v2, Lorg/codehaus/groovy/util/ReferenceType;->SOFT:Lorg/codehaus/groovy/util/ReferenceType;

    invoke-direct {v1, v0, v2}, Lorg/codehaus/groovy/util/ReferenceBundle;-><init>(Lorg/codehaus/groovy/util/ReferenceManager;Lorg/codehaus/groovy/util/ReferenceType;)V

    sput-object v1, Lorg/codehaus/groovy/util/ReferenceBundle;->softReferences:Lorg/codehaus/groovy/util/ReferenceBundle;

    .line 43
    new-instance v1, Lorg/codehaus/groovy/util/ReferenceBundle;

    sget-object v2, Lorg/codehaus/groovy/util/ReferenceType;->WEAK:Lorg/codehaus/groovy/util/ReferenceType;

    invoke-direct {v1, v0, v2}, Lorg/codehaus/groovy/util/ReferenceBundle;-><init>(Lorg/codehaus/groovy/util/ReferenceManager;Lorg/codehaus/groovy/util/ReferenceType;)V

    sput-object v1, Lorg/codehaus/groovy/util/ReferenceBundle;->weakReferences:Lorg/codehaus/groovy/util/ReferenceBundle;

    .line 44
    new-instance v1, Lorg/codehaus/groovy/util/ReferenceBundle;

    sget-object v2, Lorg/codehaus/groovy/util/ReferenceType;->PHANTOM:Lorg/codehaus/groovy/util/ReferenceType;

    invoke-direct {v1, v0, v2}, Lorg/codehaus/groovy/util/ReferenceBundle;-><init>(Lorg/codehaus/groovy/util/ReferenceManager;Lorg/codehaus/groovy/util/ReferenceType;)V

    sput-object v1, Lorg/codehaus/groovy/util/ReferenceBundle;->phantomReferences:Lorg/codehaus/groovy/util/ReferenceBundle;

    .line 45
    new-instance v0, Lorg/codehaus/groovy/util/ReferenceBundle;

    const/4 v1, 0x0

    invoke-static {v1}, Lorg/codehaus/groovy/util/ReferenceManager;->createIdlingManager(Ljava/lang/ref/ReferenceQueue;)Lorg/codehaus/groovy/util/ReferenceManager;

    move-result-object v1

    sget-object v2, Lorg/codehaus/groovy/util/ReferenceType;->HARD:Lorg/codehaus/groovy/util/ReferenceType;

    invoke-direct {v0, v1, v2}, Lorg/codehaus/groovy/util/ReferenceBundle;-><init>(Lorg/codehaus/groovy/util/ReferenceManager;Lorg/codehaus/groovy/util/ReferenceType;)V

    sput-object v0, Lorg/codehaus/groovy/util/ReferenceBundle;->hardReferences:Lorg/codehaus/groovy/util/ReferenceBundle;

    return-void
.end method

.method public constructor <init>(Lorg/codehaus/groovy/util/ReferenceManager;Lorg/codehaus/groovy/util/ReferenceType;)V
    .locals 0

    .line 26
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 27
    iput-object p1, p0, Lorg/codehaus/groovy/util/ReferenceBundle;->manager:Lorg/codehaus/groovy/util/ReferenceManager;

    .line 28
    iput-object p2, p0, Lorg/codehaus/groovy/util/ReferenceBundle;->type:Lorg/codehaus/groovy/util/ReferenceType;

    return-void
.end method

.method public static getHardBundle()Lorg/codehaus/groovy/util/ReferenceBundle;
    .locals 1

    .line 57
    sget-object v0, Lorg/codehaus/groovy/util/ReferenceBundle;->hardReferences:Lorg/codehaus/groovy/util/ReferenceBundle;

    return-object v0
.end method

.method public static getPhantomBundle()Lorg/codehaus/groovy/util/ReferenceBundle;
    .locals 1

    .line 61
    sget-object v0, Lorg/codehaus/groovy/util/ReferenceBundle;->phantomReferences:Lorg/codehaus/groovy/util/ReferenceBundle;

    return-object v0
.end method

.method public static getSoftBundle()Lorg/codehaus/groovy/util/ReferenceBundle;
    .locals 1

    .line 49
    sget-object v0, Lorg/codehaus/groovy/util/ReferenceBundle;->softReferences:Lorg/codehaus/groovy/util/ReferenceBundle;

    return-object v0
.end method

.method public static getWeakBundle()Lorg/codehaus/groovy/util/ReferenceBundle;
    .locals 1

    .line 53
    sget-object v0, Lorg/codehaus/groovy/util/ReferenceBundle;->weakReferences:Lorg/codehaus/groovy/util/ReferenceBundle;

    return-object v0
.end method


# virtual methods
.method public getManager()Lorg/codehaus/groovy/util/ReferenceManager;
    .locals 1

    .line 34
    iget-object v0, p0, Lorg/codehaus/groovy/util/ReferenceBundle;->manager:Lorg/codehaus/groovy/util/ReferenceManager;

    return-object v0
.end method

.method public getType()Lorg/codehaus/groovy/util/ReferenceType;
    .locals 1

    .line 31
    iget-object v0, p0, Lorg/codehaus/groovy/util/ReferenceBundle;->type:Lorg/codehaus/groovy/util/ReferenceType;

    return-object v0
.end method
