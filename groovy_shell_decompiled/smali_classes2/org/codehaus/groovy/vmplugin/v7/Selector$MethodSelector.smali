.class Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;
.super Lorg/codehaus/groovy/vmplugin/v7/Selector;
.source "Selector.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/codehaus/groovy/vmplugin/v7/Selector;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0xa
    name = "MethodSelector"
.end annotation


# static fields
.field private static final SINGLE_NULL_ARRAY:[Ljava/lang/Object;


# instance fields
.field private isCategoryMethod:Z

.field protected mc:Lgroovy/lang/MetaClass;


# direct methods
.method static constructor <clinit>()V
    .locals 3

    const/4 v0, 0x1

    new-array v0, v0, [Ljava/lang/Object;

    const/4 v1, 0x0

    const/4 v2, 0x0

    aput-object v2, v0, v1

    .line 511
    sput-object v0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->SINGLE_NULL_ARRAY:[Ljava/lang/Object;

    return-void
.end method

.method public constructor <init>(Ljava/lang/invoke/MutableCallSite;Ljava/lang/Class;Ljava/lang/String;Lorg/codehaus/groovy/vmplugin/v7/IndyInterface$CallType;Ljava/lang/Boolean;Ljava/lang/Boolean;Ljava/lang/Boolean;[Ljava/lang/Object;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/invoke/MutableCallSite;",
            "Ljava/lang/Class<",
            "*>;",
            "Ljava/lang/String;",
            "Lorg/codehaus/groovy/vmplugin/v7/IndyInterface$CallType;",
            "Ljava/lang/Boolean;",
            "Ljava/lang/Boolean;",
            "Ljava/lang/Boolean;",
            "[",
            "Ljava/lang/Object;",
            ")V"
        }
    .end annotation

    .line 515
    invoke-direct {p0}, Lorg/codehaus/groovy/vmplugin/v7/Selector;-><init>()V

    .line 516
    iput-object p4, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->callType:Lorg/codehaus/groovy/vmplugin/v7/IndyInterface$CallType;

    .line 517
    invoke-virtual {p1}, Ljava/lang/invoke/MutableCallSite;->type()Ljava/lang/invoke/MethodType;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->targetType:Ljava/lang/invoke/MethodType;

    .line 518
    iput-object p3, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->name:Ljava/lang/String;

    .line 519
    iput-object p8, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->originalArguments:[Ljava/lang/Object;

    .line 520
    invoke-virtual {p7}, Ljava/lang/Boolean;->booleanValue()Z

    move-result v0

    invoke-static {p8, v0}, Lorg/codehaus/groovy/vmplugin/v7/Selector;->access$100([Ljava/lang/Object;Z)[Ljava/lang/Object;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->args:[Ljava/lang/Object;

    .line 521
    iput-object p1, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->callSite:Ljava/lang/invoke/MutableCallSite;

    .line 522
    iput-object p2, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->sender:Ljava/lang/Class;

    .line 523
    invoke-virtual {p5}, Ljava/lang/Boolean;->booleanValue()Z

    move-result p1

    iput-boolean p1, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->safeNavigationOrig:Z

    .line 524
    invoke-virtual {p5}, Ljava/lang/Boolean;->booleanValue()Z

    move-result p1

    const/4 v0, 0x1

    const/4 v1, 0x0

    if-eqz p1, :cond_0

    aget-object p1, p8, v1

    if-nez p1, :cond_0

    move p1, v0

    goto :goto_0

    :cond_0
    move p1, v1

    :goto_0
    iput-boolean p1, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->safeNavigation:Z

    .line 525
    invoke-virtual {p6}, Ljava/lang/Boolean;->booleanValue()Z

    move-result p1

    iput-boolean p1, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->thisCall:Z

    .line 526
    invoke-virtual {p7}, Ljava/lang/Boolean;->booleanValue()Z

    move-result p1

    iput-boolean p1, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->spread:Z

    .line 527
    iget-boolean p1, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->spread:Z

    xor-int/2addr p1, v0

    iput-boolean p1, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->cache:Z

    .line 529
    sget-boolean p1, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG_ENABLED:Z

    if-eqz p1, :cond_3

    .line 530
    new-instance p1, Ljava/lang/StringBuilder;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "----------------------------------------------------\n\t\tinvocation of method \'"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p3

    const-string v0, "\'\n\t\tinvocation type: "

    invoke-virtual {p3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p3

    invoke-virtual {p3, p4}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p3

    const-string p4, "\n\t\tsender: "

    invoke-virtual {p3, p4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p3

    invoke-virtual {p3, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string p3, "\n\t\ttargetType: "

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    iget-object p3, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->targetType:Ljava/lang/invoke/MethodType;

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string p3, "\n\t\tsafe navigation: "

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, p5}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string p3, "\n\t\tthisCall: "

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, p6}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string p3, "\n\t\tspreadCall: "

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, p7}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string p3, "\n\t\twith "

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    array-length p3, p8

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string p3, " arguments"

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-direct {p1, p2}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    .line 540
    :goto_1
    array-length p2, p8

    if-ge v1, p2, :cond_2

    const-string p2, "\n\t\t\targument["

    .line 541
    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string p3, "] = "

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 542
    aget-object p2, p8, v1

    if-nez p2, :cond_1

    const-string p2, "null"

    .line 543
    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_2

    .line 545
    :cond_1
    aget-object p2, p8, v1

    invoke-virtual {p2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string p3, "@"

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    aget-object p3, p8, v1

    invoke-static {p3}, Ljava/lang/System;->identityHashCode(Ljava/lang/Object;)I

    move-result p3

    invoke-static {p3}, Ljava/lang/Integer;->toHexString(I)Ljava/lang/String;

    move-result-object p3

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    :goto_2
    add-int/lit8 v1, v1, 0x1

    goto :goto_1

    .line 548
    :cond_2
    sget-object p2, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG:Ljava/util/logging/Logger;

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p2, p1}, Ljava/util/logging/Logger;->info(Ljava/lang/String;)V

    :cond_3
    return-void
.end method

.method private correctClassForNameAndUnReflectOtherwise(Ljava/lang/reflect/Method;)Ljava/lang/invoke/MethodHandle;
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/IllegalAccessException;
        }
    .end annotation

    .line 681
    invoke-virtual {p1}, Ljava/lang/reflect/Method;->getDeclaringClass()Ljava/lang/Class;

    move-result-object v0

    const-class v1, Ljava/lang/Class;

    if-ne v0, v1, :cond_0

    invoke-virtual {p1}, Ljava/lang/reflect/Method;->getName()Ljava/lang/String;

    move-result-object v0

    const-string v1, "forName"

    invoke-virtual {v0, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-virtual {p1}, Ljava/lang/reflect/Method;->getParameterTypes()[Ljava/lang/Class;

    move-result-object v0

    array-length v0, v0

    const/4 v1, 0x1

    if-ne v0, v1, :cond_0

    .line 682
    sget-object p1, Lorg/codehaus/groovy/vmplugin/v7/IndyGuardsFiltersAndSignatures;->CLASS_FOR_NAME:Ljava/lang/invoke/MethodHandle;

    const/4 v0, 0x2

    new-array v0, v0, [Ljava/lang/Object;

    const/4 v2, 0x0

    invoke-static {v1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v3

    aput-object v3, v0, v2

    iget-object v2, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->sender:Ljava/lang/Class;

    invoke-virtual {v2}, Ljava/lang/Class;->getClassLoader()Ljava/lang/ClassLoader;

    move-result-object v2

    aput-object v2, v0, v1

    invoke-static {p1, v1, v0}, Ljava/lang/invoke/MethodHandles;->insertArguments(Ljava/lang/invoke/MethodHandle;I[Ljava/lang/Object;)Ljava/lang/invoke/MethodHandle;

    move-result-object p1

    return-object p1

    .line 684
    :cond_0
    sget-object v0, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOOKUP:Ljava/lang/invoke/MethodHandles$Lookup;

    invoke-virtual {v0, p1}, Ljava/lang/invoke/MethodHandles$Lookup;->unreflect(Ljava/lang/reflect/Method;)Ljava/lang/invoke/MethodHandle;

    move-result-object p1

    return-object p1
.end method

.method private removeWrapper(Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodType;
    .locals 4

    .line 692
    invoke-virtual {p1}, Ljava/lang/invoke/MethodType;->parameterArray()[Ljava/lang/Class;

    move-result-object v0

    const/4 v1, 0x0

    .line 693
    :goto_0
    array-length v2, v0

    if-ge v1, v2, :cond_1

    .line 694
    aget-object v2, v0, v1

    const-class v3, Lorg/codehaus/groovy/runtime/wrappers/Wrapper;

    if-ne v2, v3, :cond_0

    .line 695
    const-class v2, Ljava/lang/Object;

    invoke-virtual {p1, v1, v2}, Ljava/lang/invoke/MethodType;->changeParameterType(ILjava/lang/Class;)Ljava/lang/invoke/MethodType;

    move-result-object p1

    :cond_0
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_1
    return-object p1
.end method


# virtual methods
.method public addExceptionHandler()V
    .locals 4

    .line 868
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    if-eqz v0, :cond_2

    iget-boolean v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->catchException:Z

    if-nez v0, :cond_0

    goto :goto_1

    .line 869
    :cond_0
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    invoke-virtual {v0}, Ljava/lang/invoke/MethodHandle;->type()Ljava/lang/invoke/MethodType;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/invoke/MethodType;->returnType()Ljava/lang/Class;

    move-result-object v0

    .line 870
    const-class v1, Ljava/lang/Object;

    if-eq v0, v1, :cond_1

    .line 871
    const-class v1, Lgroovy/lang/GroovyRuntimeException;

    invoke-static {v0, v1}, Ljava/lang/invoke/MethodType;->methodType(Ljava/lang/Class;Ljava/lang/Class;)Ljava/lang/invoke/MethodType;

    move-result-object v0

    .line 872
    iget-object v1, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    const-class v2, Lgroovy/lang/GroovyRuntimeException;

    sget-object v3, Lorg/codehaus/groovy/vmplugin/v7/IndyGuardsFiltersAndSignatures;->UNWRAP_EXCEPTION:Ljava/lang/invoke/MethodHandle;

    invoke-virtual {v3, v0}, Ljava/lang/invoke/MethodHandle;->asType(Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;

    move-result-object v0

    invoke-static {v1, v2, v0}, Ljava/lang/invoke/MethodHandles;->catchException(Ljava/lang/invoke/MethodHandle;Ljava/lang/Class;Ljava/lang/invoke/MethodHandle;)Ljava/lang/invoke/MethodHandle;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    goto :goto_0

    .line 874
    :cond_1
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    const-class v1, Lgroovy/lang/GroovyRuntimeException;

    sget-object v2, Lorg/codehaus/groovy/vmplugin/v7/IndyGuardsFiltersAndSignatures;->UNWRAP_EXCEPTION:Ljava/lang/invoke/MethodHandle;

    invoke-static {v0, v1, v2}, Ljava/lang/invoke/MethodHandles;->catchException(Ljava/lang/invoke/MethodHandle;Ljava/lang/Class;Ljava/lang/invoke/MethodHandle;)Ljava/lang/invoke/MethodHandle;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    .line 876
    :goto_0
    sget-boolean v0, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG_ENABLED:Z

    if-eqz v0, :cond_2

    sget-object v0, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG:Ljava/util/logging/Logger;

    const-string v1, "added GroovyRuntimeException unwrapper"

    invoke-virtual {v0, v1}, Ljava/util/logging/Logger;->info(Ljava/lang/String;)V

    :cond_2
    :goto_1
    return-void
.end method

.method public chooseMeta(Lgroovy/lang/MetaClassImpl;)V
    .locals 4

    if-nez p1, :cond_0

    return-void

    .line 594
    :cond_0
    invoke-virtual {p0}, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->getCorrectedReceiver()Ljava/lang/Object;

    move-result-object v0

    .line 595
    iget-object v1, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->args:[Ljava/lang/Object;

    invoke-static {v1}, Lorg/codehaus/groovy/vmplugin/v7/Selector;->access$000([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object v1

    .line 596
    instance-of v2, v0, Ljava/lang/Class;

    if-eqz v2, :cond_2

    .line 597
    sget-boolean v0, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG_ENABLED:Z

    if-eqz v0, :cond_1

    sget-object v0, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG:Ljava/util/logging/Logger;

    const-string v2, "receiver is a class"

    invoke-virtual {v0, v2}, Ljava/util/logging/Logger;->info(Ljava/lang/String;)V

    .line 598
    :cond_1
    invoke-virtual {p1}, Lgroovy/lang/MetaClassImpl;->hasCustomStaticInvokeMethod()Z

    move-result v0

    if-nez v0, :cond_4

    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->name:Ljava/lang/String;

    invoke-virtual {p1, v0, v1}, Lgroovy/lang/MetaClassImpl;->retrieveStaticMethod(Ljava/lang/String;[Ljava/lang/Object;)Lgroovy/lang/MetaMethod;

    move-result-object p1

    iput-object p1, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->method:Lgroovy/lang/MetaMethod;

    goto :goto_0

    .line 600
    :cond_2
    iget-object v2, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->name:Ljava/lang/String;

    .line 601
    instance-of v0, v0, Lorg/codehaus/groovy/runtime/GeneratedClosure;

    if-eqz v0, :cond_3

    const-string v0, "call"

    invoke-virtual {v2, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_3

    const-string v2, "doCall"

    .line 604
    :cond_3
    invoke-virtual {p1}, Lgroovy/lang/MetaClassImpl;->hasCustomInvokeMethod()Z

    move-result v0

    if-nez v0, :cond_4

    .line 605
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->selectionBase:Ljava/lang/Class;

    const/4 v3, 0x0

    invoke-virtual {p1, v0, v2, v1, v3}, Lgroovy/lang/MetaClassImpl;->getMethodWithCaching(Ljava/lang/Class;Ljava/lang/String;[Ljava/lang/Object;Z)Lgroovy/lang/MetaMethod;

    move-result-object p1

    iput-object p1, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->method:Lgroovy/lang/MetaMethod;

    .line 607
    :cond_4
    :goto_0
    sget-boolean p1, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG_ENABLED:Z

    if-eqz p1, :cond_5

    sget-object p1, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG:Ljava/util/logging/Logger;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "retrieved method from meta class: "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v1, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->method:Lgroovy/lang/MetaMethod;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v0}, Ljava/util/logging/Logger;->info(Ljava/lang/String;)V

    :cond_5
    return-void
.end method

.method public correctCoerce()V
    .locals 7

    .line 802
    iget-boolean v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->useMetaClass:Z

    if-eqz v0, :cond_0

    return-void

    .line 804
    :cond_0
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    invoke-virtual {v0}, Ljava/lang/invoke/MethodHandle;->type()Ljava/lang/invoke/MethodType;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/invoke/MethodType;->parameterArray()[Ljava/lang/Class;

    move-result-object v0

    .line 805
    iget-object v1, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->currentType:Ljava/lang/invoke/MethodType;

    if-eqz v1, :cond_1

    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->currentType:Ljava/lang/invoke/MethodType;

    invoke-virtual {v0}, Ljava/lang/invoke/MethodType;->parameterArray()[Ljava/lang/Class;

    move-result-object v0

    .line 806
    :cond_1
    iget-object v1, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->args:[Ljava/lang/Object;

    array-length v1, v1

    array-length v2, v0

    if-ne v1, v2, :cond_9

    const/4 v1, 0x0

    .line 809
    :goto_0
    iget-object v2, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->args:[Ljava/lang/Object;

    array-length v2, v2

    if-ge v1, v2, :cond_8

    .line 810
    aget-object v2, v0, v1

    const-class v3, Ljava/lang/Object;

    if-ne v2, v3, :cond_2

    goto :goto_1

    .line 811
    :cond_2
    iget-object v2, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->args:[Ljava/lang/Object;

    aget-object v2, v2, v1

    invoke-static {v2}, Lorg/codehaus/groovy/vmplugin/v7/Selector;->access$300(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    if-nez v2, :cond_3

    goto :goto_1

    .line 824
    :cond_3
    invoke-virtual {v2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v3

    .line 827
    aget-object v4, v0, v1

    if-ne v3, v4, :cond_4

    goto :goto_1

    .line 829
    :cond_4
    aget-object v4, v0, v1

    invoke-static {v4}, Lorg/codehaus/groovy/vmplugin/v7/TypeHelper;->getWrapperClass(Ljava/lang/Class;)Ljava/lang/Class;

    move-result-object v4

    .line 831
    invoke-static {v3}, Lorg/codehaus/groovy/vmplugin/v7/TypeHelper;->getWrapperClass(Ljava/lang/Class;)Ljava/lang/Class;

    move-result-object v5

    if-ne v4, v5, :cond_5

    goto :goto_1

    .line 836
    :cond_5
    aget-object v5, v0, v1

    invoke-virtual {v5, v3}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    move-result v5

    if-eqz v5, :cond_6

    goto :goto_1

    .line 839
    :cond_6
    iget-object v5, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    invoke-static {v5, v1, v2, v4}, Lorg/codehaus/groovy/vmplugin/v7/TypeTransformers;->addTransformer(Ljava/lang/invoke/MethodHandle;ILjava/lang/Object;Ljava/lang/Class;)Ljava/lang/invoke/MethodHandle;

    move-result-object v2

    iput-object v2, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    .line 840
    sget-boolean v2, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG_ENABLED:Z

    if-eqz v2, :cond_7

    .line 841
    sget-object v2, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG:Ljava/util/logging/Logger;

    new-instance v5, Ljava/lang/StringBuilder;

    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    const-string v6, "added transformer at pos "

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    invoke-virtual {v5, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v5

    const-string v6, " for type "

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    invoke-virtual {v5, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v3

    const-string v5, " to type "

    invoke-virtual {v3, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Ljava/util/logging/Logger;->info(Ljava/lang/String;)V

    :cond_7
    :goto_1
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_8
    return-void

    .line 807
    :cond_9
    new-instance v0, Lorg/codehaus/groovy/GroovyBugError;

    const-string v1, "At this point argument array length and parameter array length should be the same"

    invoke-direct {v0, v1}, Lorg/codehaus/groovy/GroovyBugError;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public correctNullReceiver()V
    .locals 4

    .line 851
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->args:[Ljava/lang/Object;

    const/4 v1, 0x0

    aget-object v0, v0, v1

    if-eqz v0, :cond_0

    return-void

    .line 852
    :cond_0
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    invoke-static {}, Lorg/codehaus/groovy/runtime/NullObject;->getNullObject()Lorg/codehaus/groovy/runtime/NullObject;

    move-result-object v2

    invoke-virtual {v0, v2}, Ljava/lang/invoke/MethodHandle;->bindTo(Ljava/lang/Object;)Ljava/lang/invoke/MethodHandle;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    .line 853
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    const/4 v2, 0x1

    new-array v2, v2, [Ljava/lang/Class;

    iget-object v3, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->targetType:Ljava/lang/invoke/MethodType;

    invoke-virtual {v3, v1}, Ljava/lang/invoke/MethodType;->parameterType(I)Ljava/lang/Class;

    move-result-object v3

    aput-object v3, v2, v1

    invoke-static {v0, v1, v2}, Ljava/lang/invoke/MethodHandles;->dropArguments(Ljava/lang/invoke/MethodHandle;I[Ljava/lang/Class;)Ljava/lang/invoke/MethodHandle;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    .line 854
    sget-boolean v0, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG_ENABLED:Z

    if-eqz v0, :cond_1

    sget-object v0, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG:Ljava/util/logging/Logger;

    const-string v1, "binding null object receiver and dropping old receiver"

    invoke-virtual {v0, v1}, Ljava/util/logging/Logger;->info(Ljava/lang/String;)V

    :cond_1
    return-void
.end method

.method public correctParameterLength()V
    .locals 6

    .line 756
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    if-nez v0, :cond_0

    return-void

    .line 758
    :cond_0
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    invoke-virtual {v0}, Ljava/lang/invoke/MethodHandle;->type()Ljava/lang/invoke/MethodType;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/invoke/MethodType;->parameterArray()[Ljava/lang/Class;

    move-result-object v0

    .line 759
    iget-object v1, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->currentType:Ljava/lang/invoke/MethodType;

    if-eqz v1, :cond_1

    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->currentType:Ljava/lang/invoke/MethodType;

    invoke-virtual {v0}, Ljava/lang/invoke/MethodType;->parameterArray()[Ljava/lang/Class;

    move-result-object v0

    .line 760
    :cond_1
    iget-boolean v1, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->isVargs:Z

    const/4 v2, 0x1

    if-nez v1, :cond_4

    .line 761
    iget-boolean v1, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->spread:Z

    if-eqz v1, :cond_2

    iget-boolean v1, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->useMetaClass:Z

    if-eqz v1, :cond_2

    return-void

    .line 762
    :cond_2
    array-length v0, v0

    const/4 v1, 0x2

    if-ne v0, v1, :cond_3

    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->args:[Ljava/lang/Object;

    array-length v0, v0

    if-ne v0, v2, :cond_3

    .line 763
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    sget-object v1, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->SINGLE_NULL_ARRAY:[Ljava/lang/Object;

    invoke-static {v0, v2, v1}, Ljava/lang/invoke/MethodHandles;->insertArguments(Ljava/lang/invoke/MethodHandle;I[Ljava/lang/Object;)Ljava/lang/invoke/MethodHandle;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    :cond_3
    return-void

    .line 768
    :cond_4
    array-length v1, v0

    sub-int/2addr v1, v2

    aget-object v1, v0, v1

    .line 769
    iget-object v3, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->args:[Ljava/lang/Object;

    iget-object v4, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->args:[Ljava/lang/Object;

    array-length v4, v4

    sub-int/2addr v4, v2

    aget-object v3, v3, v4

    invoke-static {v3}, Lorg/codehaus/groovy/vmplugin/v7/Selector;->access$300(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    .line 770
    array-length v4, v0

    iget-object v5, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->args:[Ljava/lang/Object;

    array-length v5, v5

    if-ne v4, v5, :cond_8

    if-nez v3, :cond_5

    return-void

    .line 773
    :cond_5
    invoke-virtual {v1, v3}, Ljava/lang/Class;->isInstance(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_6

    return-void

    .line 774
    :cond_6
    invoke-virtual {v3}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/Class;->isArray()Z

    move-result v0

    if-eqz v0, :cond_7

    return-void

    .line 777
    :cond_7
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    invoke-virtual {v0, v1, v2}, Ljava/lang/invoke/MethodHandle;->asCollector(Ljava/lang/Class;I)Ljava/lang/invoke/MethodHandle;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    goto :goto_0

    .line 778
    :cond_8
    array-length v3, v0

    iget-object v4, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->args:[Ljava/lang/Object;

    array-length v4, v4

    if-le v3, v4, :cond_9

    .line 783
    iget-object v3, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    array-length v0, v0

    sub-int/2addr v0, v2

    new-array v2, v2, [Ljava/lang/Object;

    invoke-virtual {v1}, Ljava/lang/Class;->getComponentType()Ljava/lang/Class;

    move-result-object v1

    const/4 v4, 0x0

    invoke-static {v1, v4}, Ljava/lang/reflect/Array;->newInstance(Ljava/lang/Class;I)Ljava/lang/Object;

    move-result-object v1

    aput-object v1, v2, v4

    invoke-static {v3, v0, v2}, Ljava/lang/invoke/MethodHandles;->insertArguments(Ljava/lang/invoke/MethodHandle;I[Ljava/lang/Object;)Ljava/lang/invoke/MethodHandle;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    .line 784
    sget-boolean v0, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG_ENABLED:Z

    if-eqz v0, :cond_a

    sget-object v0, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG:Ljava/util/logging/Logger;

    const-string v1, "added empty array for missing vargs part"

    invoke-virtual {v0, v1}, Ljava/util/logging/Logger;->info(Ljava/lang/String;)V

    goto :goto_0

    .line 789
    :cond_9
    iget-object v3, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    iget-object v4, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->args:[Ljava/lang/Object;

    array-length v4, v4

    array-length v0, v0

    sub-int/2addr v4, v0

    add-int/2addr v4, v2

    invoke-virtual {v3, v1, v4}, Ljava/lang/invoke/MethodHandle;->asCollector(Ljava/lang/Class;I)Ljava/lang/invoke/MethodHandle;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    .line 792
    sget-boolean v0, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG_ENABLED:Z

    if-eqz v0, :cond_a

    sget-object v0, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG:Ljava/util/logging/Logger;

    const-string v1, "changed surplus arguments to be collected for vargs call"

    invoke-virtual {v0, v1}, Ljava/util/logging/Logger;->info(Ljava/lang/String;)V

    :cond_a
    :goto_0
    return-void
.end method

.method public correctSpreading()V
    .locals 3

    .line 858
    iget-boolean v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->spread:Z

    if-eqz v0, :cond_1

    iget-boolean v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->useMetaClass:Z

    if-nez v0, :cond_1

    iget-boolean v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->skipSpreadCollector:Z

    if-eqz v0, :cond_0

    goto :goto_0

    .line 859
    :cond_0
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    const-class v1, [Ljava/lang/Object;

    iget-object v2, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->args:[Ljava/lang/Object;

    array-length v2, v2

    add-int/lit8 v2, v2, -0x1

    invoke-virtual {v0, v1, v2}, Ljava/lang/invoke/MethodHandle;->asSpreader(Ljava/lang/Class;I)Ljava/lang/invoke/MethodHandle;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    :cond_1
    :goto_0
    return-void
.end method

.method public correctWrapping()V
    .locals 8

    .line 737
    iget-boolean v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->useMetaClass:Z

    if-eqz v0, :cond_0

    return-void

    .line 738
    :cond_0
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    invoke-virtual {v0}, Ljava/lang/invoke/MethodHandle;->type()Ljava/lang/invoke/MethodType;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/invoke/MethodType;->parameterArray()[Ljava/lang/Class;

    move-result-object v0

    .line 739
    iget-object v1, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->currentType:Ljava/lang/invoke/MethodType;

    if-eqz v1, :cond_1

    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->currentType:Ljava/lang/invoke/MethodType;

    invoke-virtual {v0}, Ljava/lang/invoke/MethodType;->parameterArray()[Ljava/lang/Class;

    move-result-object v0

    :cond_1
    const/4 v1, 0x1

    move v2, v1

    .line 740
    :goto_0
    iget-object v3, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->args:[Ljava/lang/Object;

    array-length v3, v3

    if-ge v2, v3, :cond_3

    .line 741
    iget-object v3, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->args:[Ljava/lang/Object;

    aget-object v3, v3, v2

    instance-of v3, v3, Lorg/codehaus/groovy/runtime/wrappers/Wrapper;

    if-eqz v3, :cond_2

    .line 742
    aget-object v3, v0, v2

    .line 743
    const-class v4, Lorg/codehaus/groovy/runtime/wrappers/Wrapper;

    invoke-static {v3, v4}, Ljava/lang/invoke/MethodType;->methodType(Ljava/lang/Class;Ljava/lang/Class;)Ljava/lang/invoke/MethodType;

    move-result-object v3

    .line 744
    iget-object v4, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    new-array v5, v1, [Ljava/lang/invoke/MethodHandle;

    const/4 v6, 0x0

    sget-object v7, Lorg/codehaus/groovy/vmplugin/v7/IndyGuardsFiltersAndSignatures;->UNWRAP_METHOD:Ljava/lang/invoke/MethodHandle;

    invoke-virtual {v7, v3}, Ljava/lang/invoke/MethodHandle;->asType(Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;

    move-result-object v3

    aput-object v3, v5, v6

    invoke-static {v4, v2, v5}, Ljava/lang/invoke/MethodHandles;->filterArguments(Ljava/lang/invoke/MethodHandle;I[Ljava/lang/invoke/MethodHandle;)Ljava/lang/invoke/MethodHandle;

    move-result-object v3

    iput-object v3, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    .line 745
    sget-boolean v3, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG_ENABLED:Z

    if-eqz v3, :cond_2

    sget-object v3, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG:Ljava/util/logging/Logger;

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    const-string v5, "added filter for Wrapper for argument at pos "

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {v4, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3, v4}, Ljava/util/logging/Logger;->info(Ljava/lang/String;)V

    :cond_2
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_3
    return-void
.end method

.method public doCallSiteTargetSet()V
    .locals 2

    .line 958
    sget-boolean v0, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG_ENABLED:Z

    if-eqz v0, :cond_0

    sget-object v0, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG:Ljava/util/logging/Logger;

    const-string v1, "call site stays uncached"

    invoke-virtual {v0, v1}, Ljava/util/logging/Logger;->info(Ljava/lang/String;)V

    :cond_0
    return-void
.end method

.method public getMetaClass()Lgroovy/lang/MetaClass;
    .locals 2

    .line 569
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->args:[Ljava/lang/Object;

    const/4 v1, 0x0

    aget-object v0, v0, v1

    if-nez v0, :cond_0

    .line 571
    invoke-static {}, Lorg/codehaus/groovy/runtime/NullObject;->getNullObject()Lorg/codehaus/groovy/runtime/NullObject;

    move-result-object v0

    invoke-virtual {v0}, Lorg/codehaus/groovy/runtime/NullObject;->getMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->mc:Lgroovy/lang/MetaClass;

    goto :goto_0

    .line 572
    :cond_0
    instance-of v1, v0, Lgroovy/lang/GroovyObject;

    if-eqz v1, :cond_1

    .line 573
    check-cast v0, Lgroovy/lang/GroovyObject;

    invoke-interface {v0}, Lgroovy/lang/GroovyObject;->getMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->mc:Lgroovy/lang/MetaClass;

    goto :goto_0

    .line 574
    :cond_1
    instance-of v1, v0, Ljava/lang/Class;

    if-eqz v1, :cond_2

    .line 575
    check-cast v0, Ljava/lang/Class;

    .line 576
    invoke-static {}, Lgroovy/lang/GroovySystem;->getMetaClassRegistry()Lgroovy/lang/MetaClassRegistry;

    move-result-object v1

    invoke-interface {v1, v0}, Lgroovy/lang/MetaClassRegistry;->getMetaClass(Ljava/lang/Class;)Lgroovy/lang/MetaClass;

    move-result-object v1

    iput-object v1, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->mc:Lgroovy/lang/MetaClass;

    .line 577
    iget-boolean v1, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->cache:Z

    invoke-static {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getClassInfo(Ljava/lang/Class;)Lorg/codehaus/groovy/reflection/ClassInfo;

    move-result-object v0

    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->hasPerInstanceMetaClasses()Z

    move-result v0

    xor-int/lit8 v0, v0, 0x1

    and-int/2addr v0, v1

    iput-boolean v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->cache:Z

    goto :goto_0

    .line 579
    :cond_2
    invoke-static {}, Lgroovy/lang/GroovySystem;->getMetaClassRegistry()Lgroovy/lang/MetaClassRegistry;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;

    invoke-virtual {v1, v0}, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->getMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v1

    iput-object v1, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->mc:Lgroovy/lang/MetaClass;

    .line 580
    iget-boolean v1, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->cache:Z

    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getClassInfo(Ljava/lang/Class;)Lorg/codehaus/groovy/reflection/ClassInfo;

    move-result-object v0

    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->hasPerInstanceMetaClasses()Z

    move-result v0

    xor-int/lit8 v0, v0, 0x1

    and-int/2addr v0, v1

    iput-boolean v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->cache:Z

    .line 582
    :goto_0
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->mc:Lgroovy/lang/MetaClass;

    invoke-interface {v0}, Lgroovy/lang/MetaClass;->initialize()V

    .line 584
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->mc:Lgroovy/lang/MetaClass;

    return-object v0
.end method

.method public setCallSiteTarget()V
    .locals 5

    .line 1004
    invoke-virtual {p0}, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->setNullForSafeNavigation()Z

    move-result v0

    const/4 v1, 0x0

    if-nez v0, :cond_4

    invoke-virtual {p0}, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->setInterceptor()Z

    move-result v0

    if-nez v0, :cond_4

    .line 1005
    invoke-virtual {p0}, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->getMetaClass()Lgroovy/lang/MetaClass;

    .line 1006
    sget-boolean v0, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG_ENABLED:Z

    if-eqz v0, :cond_0

    sget-object v0, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG:Ljava/util/logging/Logger;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "meta class is "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    iget-object v3, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->mc:Lgroovy/lang/MetaClass;

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Ljava/util/logging/Logger;->info(Ljava/lang/String;)V

    .line 1007
    :cond_0
    invoke-virtual {p0}, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->setSelectionBase()V

    .line 1008
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->mc:Lgroovy/lang/MetaClass;

    iget-object v2, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->callType:Lorg/codehaus/groovy/vmplugin/v7/IndyInterface$CallType;

    sget-object v3, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface$CallType;->GET:Lorg/codehaus/groovy/vmplugin/v7/IndyInterface$CallType;

    const/4 v4, 0x1

    if-eq v2, v3, :cond_1

    move v2, v4

    goto :goto_0

    :cond_1
    move v2, v1

    :goto_0
    invoke-static {v0, v2}, Lorg/codehaus/groovy/vmplugin/v7/Selector;->access$400(Lgroovy/lang/MetaClass;Z)Lgroovy/lang/MetaClassImpl;

    move-result-object v0

    .line 1009
    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->chooseMeta(Lgroovy/lang/MetaClassImpl;)V

    .line 1010
    invoke-virtual {p0}, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->setHandleForMetaMethod()V

    if-eqz v0, :cond_2

    goto :goto_1

    :cond_2
    move v4, v1

    .line 1011
    :goto_1
    invoke-virtual {p0, v4}, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->setMetaClassCallHandleIfNeeded(Z)V

    .line 1012
    invoke-virtual {p0}, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->correctParameterLength()V

    .line 1013
    invoke-virtual {p0}, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->correctCoerce()V

    .line 1014
    invoke-virtual {p0}, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->correctWrapping()V

    .line 1015
    invoke-virtual {p0}, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->correctNullReceiver()V

    .line 1016
    invoke-virtual {p0}, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->correctSpreading()V

    .line 1018
    sget-boolean v0, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG_ENABLED:Z

    if-eqz v0, :cond_3

    sget-object v0, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG:Ljava/util/logging/Logger;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "casting explicit from "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    iget-object v3, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    invoke-virtual {v3}, Ljava/lang/invoke/MethodHandle;->type()Ljava/lang/invoke/MethodType;

    move-result-object v3

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v2

    const-string v3, " to "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    iget-object v3, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->targetType:Ljava/lang/invoke/MethodType;

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Ljava/util/logging/Logger;->info(Ljava/lang/String;)V

    .line 1019
    :cond_3
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    iget-object v2, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->targetType:Ljava/lang/invoke/MethodType;

    invoke-static {v0, v2}, Ljava/lang/invoke/MethodHandles;->explicitCastArguments(Ljava/lang/invoke/MethodHandle;Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    .line 1021
    invoke-virtual {p0}, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->addExceptionHandler()V

    .line 1023
    :cond_4
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->args:[Ljava/lang/Object;

    aget-object v0, v0, v1

    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->setGuards(Ljava/lang/Object;)V

    .line 1024
    invoke-virtual {p0}, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->doCallSiteTargetSet()V

    return-void
.end method

.method public setGuards(Ljava/lang/Object;)V
    .locals 7

    .line 883
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    if-nez v0, :cond_0

    return-void

    .line 884
    :cond_0
    iget-boolean v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->cache:Z

    if-nez v0, :cond_1

    return-void

    .line 887
    :cond_1
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->callSite:Ljava/lang/invoke/MutableCallSite;

    instance-of v0, v0, Lorg/codehaus/groovy/vmplugin/v7/CacheableCallSite;

    if-eqz v0, :cond_a

    .line 888
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->callSite:Ljava/lang/invoke/MutableCallSite;

    check-cast v0, Lorg/codehaus/groovy/vmplugin/v7/CacheableCallSite;

    invoke-virtual {v0}, Lorg/codehaus/groovy/vmplugin/v7/CacheableCallSite;->getFallbackTarget()Ljava/lang/invoke/MethodHandle;

    move-result-object v0

    .line 894
    instance-of v1, p1, Lgroovy/lang/GroovyObject;

    const/4 v2, 0x0

    if-eqz v1, :cond_2

    .line 895
    check-cast p1, Lgroovy/lang/GroovyObject;

    .line 896
    invoke-interface {p1}, Lgroovy/lang/GroovyObject;->getMetaClass()Lgroovy/lang/MetaClass;

    move-result-object p1

    .line 897
    sget-object v1, Lorg/codehaus/groovy/vmplugin/v7/IndyGuardsFiltersAndSignatures;->SAME_MC:Ljava/lang/invoke/MethodHandle;

    invoke-virtual {v1, p1}, Ljava/lang/invoke/MethodHandle;->bindTo(Ljava/lang/Object;)Ljava/lang/invoke/MethodHandle;

    move-result-object p1

    .line 899
    sget-object v1, Ljava/lang/Boolean;->TYPE:Ljava/lang/Class;

    iget-object v3, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->targetType:Ljava/lang/invoke/MethodType;

    invoke-virtual {v3, v2}, Ljava/lang/invoke/MethodType;->parameterType(I)Ljava/lang/Class;

    move-result-object v3

    invoke-static {v1, v3}, Ljava/lang/invoke/MethodType;->methodType(Ljava/lang/Class;Ljava/lang/Class;)Ljava/lang/invoke/MethodType;

    move-result-object v1

    invoke-virtual {p1, v1}, Ljava/lang/invoke/MethodHandle;->asType(Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;

    move-result-object p1

    .line 900
    iget-object v1, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    invoke-static {p1, v1, v0}, Ljava/lang/invoke/MethodHandles;->guardWithTest(Ljava/lang/invoke/MethodHandle;Ljava/lang/invoke/MethodHandle;Ljava/lang/invoke/MethodHandle;)Ljava/lang/invoke/MethodHandle;

    move-result-object p1

    iput-object p1, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    .line 901
    sget-boolean p1, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG_ENABLED:Z

    if-eqz p1, :cond_3

    sget-object p1, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG:Ljava/util/logging/Logger;

    const-string v1, "added meta class equality check"

    invoke-virtual {p1, v1}, Ljava/util/logging/Logger;->info(Ljava/lang/String;)V

    goto :goto_0

    .line 902
    :cond_2
    instance-of v1, p1, Ljava/lang/Class;

    if-eqz v1, :cond_3

    .line 903
    sget-object v1, Lorg/codehaus/groovy/vmplugin/v7/IndyGuardsFiltersAndSignatures;->EQUALS:Ljava/lang/invoke/MethodHandle;

    invoke-virtual {v1, p1}, Ljava/lang/invoke/MethodHandle;->bindTo(Ljava/lang/Object;)Ljava/lang/invoke/MethodHandle;

    move-result-object p1

    .line 904
    sget-object v1, Ljava/lang/Boolean;->TYPE:Ljava/lang/Class;

    iget-object v3, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->targetType:Ljava/lang/invoke/MethodType;

    invoke-virtual {v3, v2}, Ljava/lang/invoke/MethodType;->parameterType(I)Ljava/lang/Class;

    move-result-object v3

    invoke-static {v1, v3}, Ljava/lang/invoke/MethodType;->methodType(Ljava/lang/Class;Ljava/lang/Class;)Ljava/lang/invoke/MethodType;

    move-result-object v1

    invoke-virtual {p1, v1}, Ljava/lang/invoke/MethodHandle;->asType(Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;

    move-result-object p1

    .line 905
    iget-object v1, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    invoke-static {p1, v1, v0}, Ljava/lang/invoke/MethodHandles;->guardWithTest(Ljava/lang/invoke/MethodHandle;Ljava/lang/invoke/MethodHandle;Ljava/lang/invoke/MethodHandle;)Ljava/lang/invoke/MethodHandle;

    move-result-object p1

    iput-object p1, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    .line 906
    sget-boolean p1, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG_ENABLED:Z

    if-eqz p1, :cond_3

    sget-object p1, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG:Ljava/util/logging/Logger;

    const-string v1, "added class equality check"

    invoke-virtual {p1, v1}, Ljava/util/logging/Logger;->info(Ljava/lang/String;)V

    .line 909
    :cond_3
    :goto_0
    iget-boolean p1, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->useMetaClass:Z

    if-nez p1, :cond_4

    iget-boolean p1, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->isCategoryMethod:Z

    if-eqz p1, :cond_4

    .line 920
    iget-object p1, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->method:Lgroovy/lang/MetaMethod;

    instance-of p1, p1, Lorg/codehaus/groovy/runtime/metaclass/NewInstanceMetaMethod;

    if-eqz p1, :cond_4

    .line 921
    sget-object p1, Lorg/codehaus/groovy/vmplugin/v7/IndyGuardsFiltersAndSignatures;->HAS_CATEGORY_IN_CURRENT_THREAD_GUARD:Ljava/lang/invoke/MethodHandle;

    iget-object v1, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    invoke-static {p1, v1, v0}, Ljava/lang/invoke/MethodHandles;->guardWithTest(Ljava/lang/invoke/MethodHandle;Ljava/lang/invoke/MethodHandle;Ljava/lang/invoke/MethodHandle;)Ljava/lang/invoke/MethodHandle;

    move-result-object p1

    iput-object p1, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    .line 922
    sget-boolean p1, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG_ENABLED:Z

    if-eqz p1, :cond_4

    sget-object p1, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG:Ljava/util/logging/Logger;

    const-string v1, "added category-in-current-thread-guard for category method"

    invoke-virtual {p1, v1}, Ljava/util/logging/Logger;->info(Ljava/lang/String;)V

    .line 927
    :cond_4
    sget-object p1, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->switchPoint:Ljava/lang/invoke/SwitchPoint;

    iget-object v1, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    invoke-virtual {p1, v1, v0}, Ljava/lang/invoke/SwitchPoint;->guardWithTest(Ljava/lang/invoke/MethodHandle;Ljava/lang/invoke/MethodHandle;)Ljava/lang/invoke/MethodHandle;

    move-result-object p1

    iput-object p1, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    .line 928
    sget-boolean p1, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG_ENABLED:Z

    if-eqz p1, :cond_5

    sget-object p1, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG:Ljava/util/logging/Logger;

    const-string v1, "added switch point guard"

    invoke-virtual {p1, v1}, Ljava/util/logging/Logger;->info(Ljava/lang/String;)V

    .line 931
    :cond_5
    iget-object p1, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    invoke-virtual {p1}, Ljava/lang/invoke/MethodHandle;->type()Ljava/lang/invoke/MethodType;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/invoke/MethodType;->parameterArray()[Ljava/lang/Class;

    move-result-object p1

    move v1, v2

    .line 932
    :goto_1
    iget-object v3, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->args:[Ljava/lang/Object;

    array-length v3, v3

    if-ge v1, v3, :cond_9

    .line 933
    iget-object v3, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->args:[Ljava/lang/Object;

    aget-object v3, v3, v1

    if-nez v3, :cond_6

    .line 936
    sget-object v3, Lorg/codehaus/groovy/vmplugin/v7/IndyGuardsFiltersAndSignatures;->IS_NULL:Ljava/lang/invoke/MethodHandle;

    sget-object v4, Ljava/lang/Boolean;->TYPE:Ljava/lang/Class;

    aget-object v5, p1, v1

    invoke-static {v4, v5}, Ljava/lang/invoke/MethodType;->methodType(Ljava/lang/Class;Ljava/lang/Class;)Ljava/lang/invoke/MethodType;

    move-result-object v4

    invoke-virtual {v3, v4}, Ljava/lang/invoke/MethodHandle;->asType(Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;

    move-result-object v3

    .line 937
    sget-boolean v4, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG_ENABLED:Z

    if-eqz v4, :cond_8

    sget-object v4, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG:Ljava/util/logging/Logger;

    new-instance v5, Ljava/lang/StringBuilder;

    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    const-string v6, "added null argument check at pos "

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    invoke-virtual {v5, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v5

    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v4, v5}, Ljava/util/logging/Logger;->info(Ljava/lang/String;)V

    goto :goto_2

    .line 939
    :cond_6
    invoke-virtual {v3}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v3

    .line 940
    aget-object v4, p1, v1

    invoke-virtual {v4}, Ljava/lang/Class;->isPrimitive()Z

    move-result v4

    if-eqz v4, :cond_7

    goto :goto_3

    .line 942
    :cond_7
    sget-object v4, Lorg/codehaus/groovy/vmplugin/v7/IndyGuardsFiltersAndSignatures;->SAME_CLASS:Ljava/lang/invoke/MethodHandle;

    .line 943
    invoke-virtual {v4, v3}, Ljava/lang/invoke/MethodHandle;->bindTo(Ljava/lang/Object;)Ljava/lang/invoke/MethodHandle;

    move-result-object v3

    sget-object v4, Ljava/lang/Boolean;->TYPE:Ljava/lang/Class;

    aget-object v5, p1, v1

    .line 944
    invoke-static {v4, v5}, Ljava/lang/invoke/MethodType;->methodType(Ljava/lang/Class;Ljava/lang/Class;)Ljava/lang/invoke/MethodType;

    move-result-object v4

    invoke-virtual {v3, v4}, Ljava/lang/invoke/MethodHandle;->asType(Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;

    move-result-object v3

    .line 945
    sget-boolean v4, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG_ENABLED:Z

    if-eqz v4, :cond_8

    sget-object v4, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG:Ljava/util/logging/Logger;

    new-instance v5, Ljava/lang/StringBuilder;

    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    const-string v6, "added same class check at pos "

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    invoke-virtual {v5, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v5

    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v4, v5}, Ljava/util/logging/Logger;->info(Ljava/lang/String;)V

    .line 947
    :cond_8
    :goto_2
    new-array v4, v1, [Ljava/lang/Class;

    .line 948
    invoke-static {p1, v2, v4, v2, v1}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    .line 949
    invoke-static {v3, v2, v4}, Ljava/lang/invoke/MethodHandles;->dropArguments(Ljava/lang/invoke/MethodHandle;I[Ljava/lang/Class;)Ljava/lang/invoke/MethodHandle;

    move-result-object v3

    .line 950
    iget-object v4, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    invoke-static {v3, v4, v0}, Ljava/lang/invoke/MethodHandles;->guardWithTest(Ljava/lang/invoke/MethodHandle;Ljava/lang/invoke/MethodHandle;Ljava/lang/invoke/MethodHandle;)Ljava/lang/invoke/MethodHandle;

    move-result-object v3

    iput-object v3, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    :goto_3
    add-int/lit8 v1, v1, 0x1

    goto/16 :goto_1

    :cond_9
    return-void

    .line 890
    :cond_a
    new-instance p1, Lorg/codehaus/groovy/GroovyBugError;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "CacheableCallSite is expected, but the actual callsite is: "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v1, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->callSite:Ljava/lang/invoke/MutableCallSite;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-direct {p1, v0}, Lorg/codehaus/groovy/GroovyBugError;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public setHandleForMetaMethod()V
    .locals 7

    .line 618
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->method:Lgroovy/lang/MetaMethod;

    .line 619
    iget-object v1, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->method:Lgroovy/lang/MetaMethod;

    instance-of v1, v1, Lorg/codehaus/groovy/runtime/GroovyCategorySupport$CategoryMethod;

    iput-boolean v1, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->isCategoryMethod:Z

    .line 621
    instance-of v1, v0, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMetaMethod;

    const/4 v2, 0x0

    if-nez v1, :cond_0

    iget-object v1, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->method:Lgroovy/lang/MetaMethod;

    instance-of v1, v1, Lorg/codehaus/groovy/reflection/GeneratedMetaMethod;

    if-eqz v1, :cond_3

    iget-object v1, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->name:Ljava/lang/String;

    const-string v3, "next"

    .line 622
    invoke-virtual {v1, v3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_0

    iget-object v1, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->name:Ljava/lang/String;

    const-string v3, "previous"

    invoke-virtual {v1, v3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_3

    .line 623
    :cond_0
    sget-boolean v1, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG_ENABLED:Z

    if-eqz v1, :cond_1

    sget-object v1, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG:Ljava/util/logging/Logger;

    const-string v3, "meta method is number method"

    invoke-virtual {v1, v3}, Ljava/util/logging/Logger;->info(Ljava/lang/String;)V

    .line 624
    :cond_1
    invoke-static {p0, v0}, Lorg/codehaus/groovy/vmplugin/v7/IndyMath;->chooseMathMethod(Lorg/codehaus/groovy/vmplugin/v7/Selector;Lgroovy/lang/MetaMethod;)Z

    move-result v1

    if-eqz v1, :cond_3

    .line 625
    iput-boolean v2, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->catchException:Z

    .line 626
    sget-boolean v0, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG_ENABLED:Z

    if-eqz v0, :cond_2

    sget-object v0, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG:Ljava/util/logging/Logger;

    const-string v1, "indy math successful"

    invoke-virtual {v0, v1}, Ljava/util/logging/Logger;->info(Ljava/lang/String;)V

    :cond_2
    return-void

    .line 631
    :cond_3
    instance-of v1, v0, Lorg/codehaus/groovy/runtime/metaclass/NewInstanceMetaMethod;

    .line 632
    sget-boolean v3, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG_ENABLED:Z

    if-eqz v3, :cond_4

    sget-object v3, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG:Ljava/util/logging/Logger;

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    const-string v5, "meta method is category type method: "

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {v4, v1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3, v4}, Ljava/util/logging/Logger;->info(Ljava/lang/String;)V

    .line 633
    :cond_4
    instance-of v3, v0, Lorg/codehaus/groovy/runtime/metaclass/NewStaticMetaMethod;

    .line 634
    sget-boolean v4, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG_ENABLED:Z

    if-eqz v4, :cond_5

    sget-object v4, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG:Ljava/util/logging/Logger;

    new-instance v5, Ljava/lang/StringBuilder;

    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    const-string v6, "meta method is static category type method: "

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    invoke-virtual {v5, v1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object v5

    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v4, v5}, Ljava/util/logging/Logger;->info(Ljava/lang/String;)V

    .line 636
    :cond_5
    instance-of v4, v0, Lorg/codehaus/groovy/runtime/metaclass/ReflectionMetaMethod;

    if-eqz v4, :cond_7

    .line 637
    sget-boolean v4, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG_ENABLED:Z

    if-eqz v4, :cond_6

    sget-object v4, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG:Ljava/util/logging/Logger;

    const-string v5, "meta method is reflective method"

    invoke-virtual {v4, v5}, Ljava/util/logging/Logger;->info(Ljava/lang/String;)V

    .line 638
    :cond_6
    check-cast v0, Lorg/codehaus/groovy/runtime/metaclass/ReflectionMetaMethod;

    .line 639
    invoke-virtual {v0}, Lorg/codehaus/groovy/runtime/metaclass/ReflectionMetaMethod;->getCachedMethod()Lgroovy/lang/MetaMethod;

    move-result-object v0

    .line 642
    :cond_7
    instance-of v4, v0, Lorg/codehaus/groovy/reflection/CachedMethod;

    const/4 v5, 0x1

    if-eqz v4, :cond_b

    .line 643
    sget-boolean v4, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG_ENABLED:Z

    if-eqz v4, :cond_8

    sget-object v4, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG:Ljava/util/logging/Logger;

    const-string v6, "meta method is CachedMethod instance"

    invoke-virtual {v4, v6}, Ljava/util/logging/Logger;->info(Ljava/lang/String;)V

    .line 644
    :cond_8
    check-cast v0, Lorg/codehaus/groovy/reflection/CachedMethod;

    .line 645
    invoke-static {}, Lorg/codehaus/groovy/vmplugin/VMPluginFactory;->getPlugin()Lorg/codehaus/groovy/vmplugin/VMPlugin;

    move-result-object v4

    invoke-virtual {p0}, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->getMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v6

    invoke-interface {v4, v6, v0}, Lorg/codehaus/groovy/vmplugin/VMPlugin;->transformMetaMethod(Lgroovy/lang/MetaClass;Lgroovy/lang/MetaMethod;)Lgroovy/lang/MetaMethod;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/reflection/CachedMethod;

    .line 646
    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/CachedMethod;->isVargsMethod()Z

    move-result v4

    iput-boolean v4, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->isVargs:Z

    .line 648
    :try_start_0
    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/CachedMethod;->getCachedMethod()Ljava/lang/reflect/Method;

    move-result-object v0

    .line 649
    invoke-direct {p0, v0}, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->correctClassForNameAndUnReflectOtherwise(Ljava/lang/reflect/Method;)Ljava/lang/invoke/MethodHandle;

    move-result-object v4

    iput-object v4, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    .line 650
    sget-boolean v4, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG_ENABLED:Z

    if-eqz v4, :cond_9

    sget-object v4, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG:Ljava/util/logging/Logger;

    const-string v6, "successfully unreflected method"

    invoke-virtual {v4, v6}, Ljava/util/logging/Logger;->info(Ljava/lang/String;)V

    :cond_9
    if-eqz v3, :cond_a

    .line 652
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    new-array v1, v5, [Ljava/lang/Object;

    const/4 v3, 0x0

    aput-object v3, v1, v2

    invoke-static {v0, v2, v1}, Ljava/lang/invoke/MethodHandles;->insertArguments(Ljava/lang/invoke/MethodHandle;I[Ljava/lang/Object;)Ljava/lang/invoke/MethodHandle;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    .line 653
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    new-array v1, v5, [Ljava/lang/Class;

    iget-object v3, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->targetType:Ljava/lang/invoke/MethodType;

    invoke-virtual {v3, v2}, Ljava/lang/invoke/MethodType;->parameterType(I)Ljava/lang/Class;

    move-result-object v3

    aput-object v3, v1, v2

    invoke-static {v0, v2, v1}, Ljava/lang/invoke/MethodHandles;->dropArguments(Ljava/lang/invoke/MethodHandle;I[Ljava/lang/Class;)Ljava/lang/invoke/MethodHandle;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    goto :goto_1

    :cond_a
    if-nez v1, :cond_e

    .line 654
    invoke-static {v0}, Lorg/codehaus/groovy/vmplugin/v7/Selector;->access$200(Ljava/lang/reflect/Method;)Z

    move-result v0

    if-eqz v0, :cond_e

    .line 658
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    new-array v1, v5, [Ljava/lang/Class;

    const-class v3, Ljava/lang/Object;

    aput-object v3, v1, v2

    invoke-static {v0, v2, v1}, Ljava/lang/invoke/MethodHandles;->dropArguments(Ljava/lang/invoke/MethodHandle;I[Ljava/lang/Class;)Ljava/lang/invoke/MethodHandle;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;
    :try_end_0
    .catch Ljava/lang/IllegalAccessException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_1

    :catch_0
    move-exception v0

    .line 661
    new-instance v1, Lorg/codehaus/groovy/GroovyBugError;

    invoke-direct {v1, v0}, Lorg/codehaus/groovy/GroovyBugError;-><init>(Ljava/lang/Exception;)V

    throw v1

    .line 663
    :cond_b
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->method:Lgroovy/lang/MetaMethod;

    if-eqz v0, :cond_e

    .line 664
    sget-boolean v0, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG_ENABLED:Z

    if-eqz v0, :cond_c

    sget-object v0, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG:Ljava/util/logging/Logger;

    const-string v1, "meta method is dgm helper"

    invoke-virtual {v0, v1}, Ljava/util/logging/Logger;->info(Ljava/lang/String;)V

    .line 666
    :cond_c
    sget-object v0, Lorg/codehaus/groovy/vmplugin/v7/IndyGuardsFiltersAndSignatures;->META_METHOD_INVOKER:Ljava/lang/invoke/MethodHandle;

    iput-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    .line 667
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    iget-object v1, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->method:Lgroovy/lang/MetaMethod;

    invoke-virtual {v0, v1}, Ljava/lang/invoke/MethodHandle;->bindTo(Ljava/lang/Object;)Ljava/lang/invoke/MethodHandle;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    .line 668
    iget-boolean v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->spread:Z

    if-eqz v0, :cond_d

    .line 669
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->originalArguments:[Ljava/lang/Object;

    iput-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->args:[Ljava/lang/Object;

    .line 670
    iput-boolean v5, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->skipSpreadCollector:Z

    goto :goto_0

    .line 673
    :cond_d
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    const-class v1, [Ljava/lang/Object;

    iget-object v2, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->targetType:Ljava/lang/invoke/MethodType;

    invoke-virtual {v2}, Ljava/lang/invoke/MethodType;->parameterCount()I

    move-result v2

    sub-int/2addr v2, v5

    invoke-virtual {v0, v1, v2}, Ljava/lang/invoke/MethodHandle;->asCollector(Ljava/lang/Class;I)Ljava/lang/invoke/MethodHandle;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    .line 675
    :goto_0
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->targetType:Ljava/lang/invoke/MethodType;

    invoke-direct {p0, v0}, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->removeWrapper(Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodType;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->currentType:Ljava/lang/invoke/MethodType;

    .line 676
    sget-boolean v0, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG_ENABLED:Z

    if-eqz v0, :cond_e

    sget-object v0, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG:Ljava/util/logging/Logger;

    const-string v1, "bound method name to META_METHOD_INVOKER"

    invoke-virtual {v0, v1}, Ljava/util/logging/Logger;->info(Ljava/lang/String;)V

    :cond_e
    :goto_1
    return-void
.end method

.method public setInterceptor()Z
    .locals 5

    .line 987
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->args:[Ljava/lang/Object;

    const/4 v1, 0x0

    aget-object v0, v0, v1

    instance-of v0, v0, Lgroovy/lang/GroovyInterceptable;

    if-nez v0, :cond_0

    return v1

    .line 988
    :cond_0
    sget-object v0, Lorg/codehaus/groovy/vmplugin/v7/IndyGuardsFiltersAndSignatures;->INTERCEPTABLE_INVOKER:Ljava/lang/invoke/MethodHandle;

    const/4 v2, 0x1

    new-array v3, v2, [Ljava/lang/Object;

    iget-object v4, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->name:Ljava/lang/String;

    aput-object v4, v3, v1

    invoke-static {v0, v2, v3}, Ljava/lang/invoke/MethodHandles;->insertArguments(Ljava/lang/invoke/MethodHandle;I[Ljava/lang/Object;)Ljava/lang/invoke/MethodHandle;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    .line 989
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    const-class v1, [Ljava/lang/Object;

    iget-object v3, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->targetType:Ljava/lang/invoke/MethodType;

    invoke-virtual {v3}, Ljava/lang/invoke/MethodType;->parameterCount()I

    move-result v3

    sub-int/2addr v3, v2

    invoke-virtual {v0, v1, v3}, Ljava/lang/invoke/MethodHandle;->asCollector(Ljava/lang/Class;I)Ljava/lang/invoke/MethodHandle;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    .line 990
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    iget-object v1, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->targetType:Ljava/lang/invoke/MethodType;

    invoke-virtual {v0, v1}, Ljava/lang/invoke/MethodHandle;->asType(Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    return v2
.end method

.method public setMetaClassCallHandleIfNeeded(Z)V
    .locals 4

    .line 707
    iget-object p1, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    if-eqz p1, :cond_0

    return-void

    :cond_0
    const/4 p1, 0x1

    .line 708
    iput-boolean p1, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->useMetaClass:Z

    .line 709
    sget-boolean v0, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG_ENABLED:Z

    if-eqz v0, :cond_1

    sget-object v0, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG:Ljava/util/logging/Logger;

    const-string v1, "set meta class invocation path"

    invoke-virtual {v0, v1}, Ljava/util/logging/Logger;->info(Ljava/lang/String;)V

    .line 710
    :cond_1
    invoke-virtual {p0}, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->getCorrectedReceiver()Ljava/lang/Object;

    move-result-object v0

    .line 711
    instance-of v1, v0, Ljava/lang/Class;

    if-eqz v1, :cond_2

    .line 712
    sget-object v0, Lorg/codehaus/groovy/vmplugin/v7/IndyGuardsFiltersAndSignatures;->META_CLASS_INVOKE_STATIC_METHOD:Ljava/lang/invoke/MethodHandle;

    iget-object v1, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->mc:Lgroovy/lang/MetaClass;

    invoke-virtual {v0, v1}, Ljava/lang/invoke/MethodHandle;->bindTo(Ljava/lang/Object;)Ljava/lang/invoke/MethodHandle;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    .line 713
    sget-boolean v0, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG_ENABLED:Z

    if-eqz v0, :cond_5

    sget-object v0, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG:Ljava/util/logging/Logger;

    const-string v1, "use invokeStaticMethod with bound meta class"

    invoke-virtual {v0, v1}, Ljava/util/logging/Logger;->info(Ljava/lang/String;)V

    goto :goto_0

    .line 715
    :cond_2
    sget-object v1, Lorg/codehaus/groovy/vmplugin/v7/IndyGuardsFiltersAndSignatures;->MOP_INVOKE_METHOD:Ljava/lang/invoke/MethodHandle;

    iget-object v2, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->mc:Lgroovy/lang/MetaClass;

    invoke-virtual {v1, v2}, Ljava/lang/invoke/MethodHandle;->bindTo(Ljava/lang/Object;)Ljava/lang/invoke/MethodHandle;

    move-result-object v1

    iput-object v1, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    .line 716
    sget-boolean v1, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG_ENABLED:Z

    if-eqz v1, :cond_3

    sget-object v1, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG:Ljava/util/logging/Logger;

    const-string v2, "use invokeMethod with bound meta class"

    invoke-virtual {v1, v2}, Ljava/util/logging/Logger;->info(Ljava/lang/String;)V

    .line 718
    :cond_3
    instance-of v0, v0, Lgroovy/lang/GroovyObject;

    if-eqz v0, :cond_5

    .line 721
    sget-boolean v0, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG_ENABLED:Z

    if-eqz v0, :cond_4

    sget-object v0, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG:Ljava/util/logging/Logger;

    const-string v1, "add MissingMethod handler for GrooObject#invokeMethod fallback path"

    invoke-virtual {v0, v1}, Ljava/util/logging/Logger;->info(Ljava/lang/String;)V

    .line 722
    :cond_4
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    const-class v1, Lgroovy/lang/MissingMethodException;

    sget-object v2, Lorg/codehaus/groovy/vmplugin/v7/IndyGuardsFiltersAndSignatures;->GROOVY_OBJECT_INVOKER:Ljava/lang/invoke/MethodHandle;

    invoke-static {v0, v1, v2}, Ljava/lang/invoke/MethodHandles;->catchException(Ljava/lang/invoke/MethodHandle;Ljava/lang/Class;Ljava/lang/invoke/MethodHandle;)Ljava/lang/invoke/MethodHandle;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    .line 725
    :cond_5
    :goto_0
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    new-array v1, p1, [Ljava/lang/Object;

    const/4 v2, 0x0

    iget-object v3, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->name:Ljava/lang/String;

    aput-object v3, v1, v2

    invoke-static {v0, p1, v1}, Ljava/lang/invoke/MethodHandles;->insertArguments(Ljava/lang/invoke/MethodHandle;I[Ljava/lang/Object;)Ljava/lang/invoke/MethodHandle;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    .line 726
    iget-boolean v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->spread:Z

    if-nez v0, :cond_6

    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    const-class v1, [Ljava/lang/Object;

    iget-object v2, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->targetType:Ljava/lang/invoke/MethodType;

    invoke-virtual {v2}, Ljava/lang/invoke/MethodType;->parameterCount()I

    move-result v2

    sub-int/2addr v2, p1

    invoke-virtual {v0, v1, v2}, Ljava/lang/invoke/MethodHandle;->asCollector(Ljava/lang/Class;I)Ljava/lang/invoke/MethodHandle;

    move-result-object p1

    iput-object p1, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    .line 727
    :cond_6
    sget-boolean p1, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG_ENABLED:Z

    if-eqz p1, :cond_7

    sget-object p1, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG:Ljava/util/logging/Logger;

    const-string v0, "bind method name and create collector for arguments"

    invoke-virtual {p1, v0}, Ljava/util/logging/Logger;->info(Ljava/lang/String;)V

    :cond_7
    return-void
.end method

.method public setNullForSafeNavigation()Z
    .locals 3

    .line 559
    iget-boolean v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->safeNavigation:Z

    const/4 v1, 0x0

    if-nez v0, :cond_0

    return v1

    .line 560
    :cond_0
    sget-object v0, Lorg/codehaus/groovy/vmplugin/v7/IndyGuardsFiltersAndSignatures;->NULL_REF:Ljava/lang/invoke/MethodHandle;

    iget-object v2, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->targetType:Ljava/lang/invoke/MethodType;

    invoke-virtual {v2}, Ljava/lang/invoke/MethodType;->parameterArray()[Ljava/lang/Class;

    move-result-object v2

    invoke-static {v0, v1, v2}, Ljava/lang/invoke/MethodHandles;->dropArguments(Ljava/lang/invoke/MethodHandle;I[Ljava/lang/Class;)Ljava/lang/invoke/MethodHandle;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->handle:Ljava/lang/invoke/MethodHandle;

    .line 561
    sget-boolean v0, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG_ENABLED:Z

    if-eqz v0, :cond_1

    sget-object v0, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG:Ljava/util/logging/Logger;

    const-string v1, "set null returning handle for safe navigation"

    invoke-virtual {v0, v1}, Ljava/util/logging/Logger;->info(Ljava/lang/String;)V

    :cond_1
    const/4 v0, 0x1

    return v0
.end method

.method public setSelectionBase()V
    .locals 3

    .line 973
    iget-boolean v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->thisCall:Z

    if-eqz v0, :cond_0

    .line 974
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->sender:Ljava/lang/Class;

    iput-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->selectionBase:Ljava/lang/Class;

    goto :goto_0

    .line 975
    :cond_0
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->args:[Ljava/lang/Object;

    const/4 v1, 0x0

    aget-object v0, v0, v1

    if-nez v0, :cond_1

    .line 976
    const-class v0, Lorg/codehaus/groovy/runtime/NullObject;

    iput-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->selectionBase:Ljava/lang/Class;

    goto :goto_0

    .line 978
    :cond_1
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->mc:Lgroovy/lang/MetaClass;

    invoke-interface {v0}, Lgroovy/lang/MetaClass;->getTheClass()Ljava/lang/Class;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->selectionBase:Ljava/lang/Class;

    .line 980
    :goto_0
    sget-boolean v0, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG_ENABLED:Z

    if-eqz v0, :cond_2

    sget-object v0, Lorg/codehaus/groovy/vmplugin/v7/IndyInterface;->LOG:Ljava/util/logging/Logger;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "selection base set to "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    iget-object v2, p0, Lorg/codehaus/groovy/vmplugin/v7/Selector$MethodSelector;->selectionBase:Ljava/lang/Class;

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/util/logging/Logger;->info(Ljava/lang/String;)V

    :cond_2
    return-void
.end method
