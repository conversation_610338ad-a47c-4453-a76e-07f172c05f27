.class public Lorg/codehaus/groovy/vmplugin/v8/IndyMath;
.super Ljava/lang/Object;
.source "IndyMath.java"


# static fields
.field private static final DD:Ljava/lang/invoke/MethodType;

.field private static final DDD:Ljava/lang/invoke/MethodType;

.field private static final DDV:Ljava/lang/invoke/MethodType;

.field private static final DV:Ljava/lang/invoke/MethodType;

.field private static final GGV:Ljava/lang/invoke/MethodType;

.field private static final GV:Ljava/lang/invoke/MethodType;

.field private static final II:Ljava/lang/invoke/MethodType;

.field private static final III:Ljava/lang/invoke/MethodType;

.field private static final IIV:Ljava/lang/invoke/MethodType;

.field private static final IV:Ljava/lang/invoke/MethodType;

.field private static final LL:Ljava/lang/invoke/MethodType;

.field private static final LLL:Ljava/lang/invoke/MethodType;

.field private static final LLV:Ljava/lang/invoke/MethodType;

.field private static final LV:Ljava/lang/invoke/MethodType;

.field private static final METHODS:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/util/Map<",
            "Ljava/lang/invoke/MethodType;",
            "Ljava/lang/invoke/MethodHandle;",
            ">;>;"
        }
    .end annotation
.end field

.field private static final OOV:Ljava/lang/invoke/MethodType;


# direct methods
.method static constructor <clinit>()V
    .locals 18

    .line 47
    sget-object v0, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    sget-object v1, Ljava/lang/Integer;->TYPE:Ljava/lang/Class;

    invoke-static {v0, v1}, Ljava/lang/invoke/MethodType;->methodType(Ljava/lang/Class;Ljava/lang/Class;)Ljava/lang/invoke/MethodType;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/vmplugin/v8/IndyMath;->IV:Ljava/lang/invoke/MethodType;

    .line 48
    sget-object v1, Ljava/lang/Integer;->TYPE:Ljava/lang/Class;

    sget-object v2, Ljava/lang/Integer;->TYPE:Ljava/lang/Class;

    invoke-static {v1, v2}, Ljava/lang/invoke/MethodType;->methodType(Ljava/lang/Class;Ljava/lang/Class;)Ljava/lang/invoke/MethodType;

    move-result-object v1

    sput-object v1, Lorg/codehaus/groovy/vmplugin/v8/IndyMath;->II:Ljava/lang/invoke/MethodType;

    .line 49
    sget-object v2, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    sget-object v3, Ljava/lang/Integer;->TYPE:Ljava/lang/Class;

    const/4 v4, 0x1

    new-array v5, v4, [Ljava/lang/Class;

    sget-object v6, Ljava/lang/Integer;->TYPE:Ljava/lang/Class;

    const/4 v7, 0x0

    aput-object v6, v5, v7

    invoke-static {v2, v3, v5}, Ljava/lang/invoke/MethodType;->methodType(Ljava/lang/Class;Ljava/lang/Class;[Ljava/lang/Class;)Ljava/lang/invoke/MethodType;

    move-result-object v2

    sput-object v2, Lorg/codehaus/groovy/vmplugin/v8/IndyMath;->IIV:Ljava/lang/invoke/MethodType;

    .line 50
    sget-object v3, Ljava/lang/Integer;->TYPE:Ljava/lang/Class;

    sget-object v5, Ljava/lang/Integer;->TYPE:Ljava/lang/Class;

    new-array v6, v4, [Ljava/lang/Class;

    sget-object v8, Ljava/lang/Integer;->TYPE:Ljava/lang/Class;

    aput-object v8, v6, v7

    invoke-static {v3, v5, v6}, Ljava/lang/invoke/MethodType;->methodType(Ljava/lang/Class;Ljava/lang/Class;[Ljava/lang/Class;)Ljava/lang/invoke/MethodType;

    move-result-object v3

    sput-object v3, Lorg/codehaus/groovy/vmplugin/v8/IndyMath;->III:Ljava/lang/invoke/MethodType;

    .line 51
    sget-object v5, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    sget-object v6, Ljava/lang/Long;->TYPE:Ljava/lang/Class;

    invoke-static {v5, v6}, Ljava/lang/invoke/MethodType;->methodType(Ljava/lang/Class;Ljava/lang/Class;)Ljava/lang/invoke/MethodType;

    move-result-object v5

    sput-object v5, Lorg/codehaus/groovy/vmplugin/v8/IndyMath;->LV:Ljava/lang/invoke/MethodType;

    .line 52
    sget-object v6, Ljava/lang/Long;->TYPE:Ljava/lang/Class;

    sget-object v8, Ljava/lang/Long;->TYPE:Ljava/lang/Class;

    invoke-static {v6, v8}, Ljava/lang/invoke/MethodType;->methodType(Ljava/lang/Class;Ljava/lang/Class;)Ljava/lang/invoke/MethodType;

    move-result-object v6

    sput-object v6, Lorg/codehaus/groovy/vmplugin/v8/IndyMath;->LL:Ljava/lang/invoke/MethodType;

    .line 53
    sget-object v8, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    sget-object v9, Ljava/lang/Long;->TYPE:Ljava/lang/Class;

    new-array v10, v4, [Ljava/lang/Class;

    sget-object v11, Ljava/lang/Long;->TYPE:Ljava/lang/Class;

    aput-object v11, v10, v7

    invoke-static {v8, v9, v10}, Ljava/lang/invoke/MethodType;->methodType(Ljava/lang/Class;Ljava/lang/Class;[Ljava/lang/Class;)Ljava/lang/invoke/MethodType;

    move-result-object v8

    sput-object v8, Lorg/codehaus/groovy/vmplugin/v8/IndyMath;->LLV:Ljava/lang/invoke/MethodType;

    .line 54
    sget-object v9, Ljava/lang/Long;->TYPE:Ljava/lang/Class;

    sget-object v10, Ljava/lang/Long;->TYPE:Ljava/lang/Class;

    new-array v11, v4, [Ljava/lang/Class;

    sget-object v12, Ljava/lang/Long;->TYPE:Ljava/lang/Class;

    aput-object v12, v11, v7

    invoke-static {v9, v10, v11}, Ljava/lang/invoke/MethodType;->methodType(Ljava/lang/Class;Ljava/lang/Class;[Ljava/lang/Class;)Ljava/lang/invoke/MethodType;

    move-result-object v9

    sput-object v9, Lorg/codehaus/groovy/vmplugin/v8/IndyMath;->LLL:Ljava/lang/invoke/MethodType;

    .line 55
    sget-object v10, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    sget-object v11, Ljava/lang/Double;->TYPE:Ljava/lang/Class;

    invoke-static {v10, v11}, Ljava/lang/invoke/MethodType;->methodType(Ljava/lang/Class;Ljava/lang/Class;)Ljava/lang/invoke/MethodType;

    move-result-object v10

    sput-object v10, Lorg/codehaus/groovy/vmplugin/v8/IndyMath;->DV:Ljava/lang/invoke/MethodType;

    .line 56
    sget-object v11, Ljava/lang/Double;->TYPE:Ljava/lang/Class;

    sget-object v12, Ljava/lang/Double;->TYPE:Ljava/lang/Class;

    invoke-static {v11, v12}, Ljava/lang/invoke/MethodType;->methodType(Ljava/lang/Class;Ljava/lang/Class;)Ljava/lang/invoke/MethodType;

    move-result-object v11

    sput-object v11, Lorg/codehaus/groovy/vmplugin/v8/IndyMath;->DD:Ljava/lang/invoke/MethodType;

    .line 57
    sget-object v12, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    sget-object v13, Ljava/lang/Double;->TYPE:Ljava/lang/Class;

    new-array v14, v4, [Ljava/lang/Class;

    sget-object v15, Ljava/lang/Double;->TYPE:Ljava/lang/Class;

    aput-object v15, v14, v7

    invoke-static {v12, v13, v14}, Ljava/lang/invoke/MethodType;->methodType(Ljava/lang/Class;Ljava/lang/Class;[Ljava/lang/Class;)Ljava/lang/invoke/MethodType;

    move-result-object v12

    sput-object v12, Lorg/codehaus/groovy/vmplugin/v8/IndyMath;->DDV:Ljava/lang/invoke/MethodType;

    .line 58
    sget-object v13, Ljava/lang/Double;->TYPE:Ljava/lang/Class;

    sget-object v14, Ljava/lang/Double;->TYPE:Ljava/lang/Class;

    new-array v15, v4, [Ljava/lang/Class;

    sget-object v16, Ljava/lang/Double;->TYPE:Ljava/lang/Class;

    aput-object v16, v15, v7

    invoke-static {v13, v14, v15}, Ljava/lang/invoke/MethodType;->methodType(Ljava/lang/Class;Ljava/lang/Class;[Ljava/lang/Class;)Ljava/lang/invoke/MethodType;

    move-result-object v13

    sput-object v13, Lorg/codehaus/groovy/vmplugin/v8/IndyMath;->DDD:Ljava/lang/invoke/MethodType;

    .line 59
    sget-object v14, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const-class v15, Ljava/math/BigDecimal;

    invoke-static {v14, v15}, Ljava/lang/invoke/MethodType;->methodType(Ljava/lang/Class;Ljava/lang/Class;)Ljava/lang/invoke/MethodType;

    move-result-object v14

    sput-object v14, Lorg/codehaus/groovy/vmplugin/v8/IndyMath;->GV:Ljava/lang/invoke/MethodType;

    .line 60
    sget-object v14, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const-class v15, Ljava/math/BigDecimal;

    move-object/from16 v16, v11

    new-array v11, v4, [Ljava/lang/Class;

    const-class v17, Ljava/math/BigDecimal;

    aput-object v17, v11, v7

    invoke-static {v14, v15, v11}, Ljava/lang/invoke/MethodType;->methodType(Ljava/lang/Class;Ljava/lang/Class;[Ljava/lang/Class;)Ljava/lang/invoke/MethodType;

    move-result-object v11

    sput-object v11, Lorg/codehaus/groovy/vmplugin/v8/IndyMath;->GGV:Ljava/lang/invoke/MethodType;

    .line 61
    sget-object v11, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const-class v14, Ljava/lang/Object;

    new-array v15, v4, [Ljava/lang/Class;

    const-class v17, Ljava/lang/Object;

    aput-object v17, v15, v7

    invoke-static {v11, v14, v15}, Ljava/lang/invoke/MethodType;->methodType(Ljava/lang/Class;Ljava/lang/Class;[Ljava/lang/Class;)Ljava/lang/invoke/MethodType;

    move-result-object v11

    sput-object v11, Lorg/codehaus/groovy/vmplugin/v8/IndyMath;->OOV:Ljava/lang/invoke/MethodType;

    .line 71
    new-instance v11, Ljava/util/HashMap;

    invoke-direct {v11}, Ljava/util/HashMap;-><init>()V

    sput-object v11, Lorg/codehaus/groovy/vmplugin/v8/IndyMath;->METHODS:Ljava/util/Map;

    const/4 v11, 0x3

    :try_start_0
    new-array v14, v11, [Ljava/lang/invoke/MethodType;

    aput-object v2, v14, v7

    aput-object v8, v14, v4

    const/4 v15, 0x2

    aput-object v12, v14, v15

    new-array v15, v11, [Ljava/lang/invoke/MethodType;

    aput-object v3, v15, v7

    aput-object v9, v15, v4

    const/16 v17, 0x2

    aput-object v13, v15, v17

    const-string v11, "minus"

    .line 78
    invoke-static {v11, v14, v15}, Lorg/codehaus/groovy/vmplugin/v8/IndyMath;->makeMapEntry(Ljava/lang/String;[Ljava/lang/invoke/MethodType;[Ljava/lang/invoke/MethodType;)V

    const-string v11, "plus"

    .line 79
    invoke-static {v11, v14, v15}, Lorg/codehaus/groovy/vmplugin/v8/IndyMath;->makeMapEntry(Ljava/lang/String;[Ljava/lang/invoke/MethodType;[Ljava/lang/invoke/MethodType;)V

    const-string v11, "multiply"

    .line 80
    invoke-static {v11, v14, v15}, Lorg/codehaus/groovy/vmplugin/v8/IndyMath;->makeMapEntry(Ljava/lang/String;[Ljava/lang/invoke/MethodType;[Ljava/lang/invoke/MethodType;)V

    new-array v11, v4, [Ljava/lang/invoke/MethodType;

    aput-object v12, v11, v7

    new-array v12, v4, [Ljava/lang/invoke/MethodType;

    aput-object v13, v12, v7

    const-string v13, "div"

    .line 84
    invoke-static {v13, v11, v12}, Lorg/codehaus/groovy/vmplugin/v8/IndyMath;->makeMapEntry(Ljava/lang/String;[Ljava/lang/invoke/MethodType;[Ljava/lang/invoke/MethodType;)V

    const/4 v11, 0x3

    new-array v12, v11, [Ljava/lang/invoke/MethodType;

    aput-object v0, v12, v7

    aput-object v5, v12, v4

    const/4 v0, 0x2

    aput-object v10, v12, v0

    new-array v5, v11, [Ljava/lang/invoke/MethodType;

    aput-object v1, v5, v7

    aput-object v6, v5, v4

    aput-object v16, v5, v0

    const-string v1, "next"

    .line 88
    invoke-static {v1, v12, v5}, Lorg/codehaus/groovy/vmplugin/v8/IndyMath;->makeMapEntry(Ljava/lang/String;[Ljava/lang/invoke/MethodType;[Ljava/lang/invoke/MethodType;)V

    const-string v1, "previous"

    .line 89
    invoke-static {v1, v12, v5}, Lorg/codehaus/groovy/vmplugin/v8/IndyMath;->makeMapEntry(Ljava/lang/String;[Ljava/lang/invoke/MethodType;[Ljava/lang/invoke/MethodType;)V

    new-array v1, v0, [Ljava/lang/invoke/MethodType;

    aput-object v2, v1, v7

    aput-object v8, v1, v4

    new-array v0, v0, [Ljava/lang/invoke/MethodType;

    aput-object v3, v0, v7

    aput-object v9, v0, v4

    const-string v2, "mod"

    .line 93
    invoke-static {v2, v1, v0}, Lorg/codehaus/groovy/vmplugin/v8/IndyMath;->makeMapEntry(Ljava/lang/String;[Ljava/lang/invoke/MethodType;[Ljava/lang/invoke/MethodType;)V

    const-string v2, "or"

    .line 94
    invoke-static {v2, v1, v0}, Lorg/codehaus/groovy/vmplugin/v8/IndyMath;->makeMapEntry(Ljava/lang/String;[Ljava/lang/invoke/MethodType;[Ljava/lang/invoke/MethodType;)V

    const-string v2, "xor"

    .line 95
    invoke-static {v2, v1, v0}, Lorg/codehaus/groovy/vmplugin/v8/IndyMath;->makeMapEntry(Ljava/lang/String;[Ljava/lang/invoke/MethodType;[Ljava/lang/invoke/MethodType;)V

    const-string v2, "and"

    .line 96
    invoke-static {v2, v1, v0}, Lorg/codehaus/groovy/vmplugin/v8/IndyMath;->makeMapEntry(Ljava/lang/String;[Ljava/lang/invoke/MethodType;[Ljava/lang/invoke/MethodType;)V

    const-string v2, "leftShift"

    .line 97
    invoke-static {v2, v1, v0}, Lorg/codehaus/groovy/vmplugin/v8/IndyMath;->makeMapEntry(Ljava/lang/String;[Ljava/lang/invoke/MethodType;[Ljava/lang/invoke/MethodType;)V

    const-string v2, "rightShift"

    .line 98
    invoke-static {v2, v1, v0}, Lorg/codehaus/groovy/vmplugin/v8/IndyMath;->makeMapEntry(Ljava/lang/String;[Ljava/lang/invoke/MethodType;[Ljava/lang/invoke/MethodType;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    :catch_0
    move-exception v0

    .line 101
    new-instance v1, Lorg/codehaus/groovy/GroovyBugError;

    invoke-direct {v1, v0}, Lorg/codehaus/groovy/GroovyBugError;-><init>(Ljava/lang/Exception;)V

    throw v1
.end method

.method public constructor <init>()V
    .locals 0

    .line 44
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static and(II)I
    .locals 0

    and-int/2addr p0, p1

    return p0
.end method

.method public static and(JJ)J
    .locals 0

    and-long/2addr p0, p2

    return-wide p0
.end method

.method public static chooseMathMethod(Lorg/codehaus/groovy/vmplugin/v8/Selector;Lgroovy/lang/MetaMethod;)Z
    .locals 3

    .line 110
    sget-object p1, Lorg/codehaus/groovy/vmplugin/v8/IndyMath;->METHODS:Ljava/util/Map;

    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector;->name:Ljava/lang/String;

    invoke-interface {p1, v0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/util/Map;

    const/4 v0, 0x0

    if-nez p1, :cond_0

    return v0

    .line 113
    :cond_0
    iget-object v1, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector;->args:[Ljava/lang/Object;

    iget-object v2, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector;->targetType:Ljava/lang/invoke/MethodType;

    invoke-static {v1, v2}, Lorg/codehaus/groovy/vmplugin/v8/TypeHelper;->replaceWithMoreSpecificType([Ljava/lang/Object;Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodType;

    move-result-object v1

    .line 114
    invoke-static {v1}, Lorg/codehaus/groovy/vmplugin/v8/IndyMath;->widenOperators(Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodType;

    move-result-object v1

    .line 116
    invoke-interface {p1, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/invoke/MethodHandle;

    if-nez p1, :cond_1

    return v0

    .line 119
    :cond_1
    iput-object p1, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector;->handle:Ljava/lang/invoke/MethodHandle;

    const/4 p0, 0x1

    return p0
.end method

.method public static div(DD)D
    .locals 0

    div-double/2addr p0, p2

    return-wide p0
.end method

.method public static leftShift(II)I
    .locals 0

    shl-int/2addr p0, p1

    return p0
.end method

.method public static leftShift(JJ)J
    .locals 0

    long-to-int p2, p2

    shl-long/2addr p0, p2

    return-wide p0
.end method

.method private static makeMapEntry(Ljava/lang/String;[Ljava/lang/invoke/MethodType;[Ljava/lang/invoke/MethodType;)V
    .locals 6
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/NoSuchMethodException;,
            Ljava/lang/IllegalAccessException;
        }
    .end annotation

    .line 64
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    .line 65
    sget-object v1, Lorg/codehaus/groovy/vmplugin/v8/IndyMath;->METHODS:Ljava/util/Map;

    invoke-interface {v1, p0, v0}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/4 v1, 0x0

    .line 66
    :goto_0
    array-length v2, p1

    if-ge v1, v2, :cond_0

    .line 67
    aget-object v2, p1, v1

    sget-object v3, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->LOOKUP:Ljava/lang/invoke/MethodHandles$Lookup;

    const-class v4, Lorg/codehaus/groovy/vmplugin/v8/IndyMath;

    aget-object v5, p2, v1

    invoke-virtual {v3, v4, p0, v5}, Ljava/lang/invoke/MethodHandles$Lookup;->findStatic(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;

    move-result-object v3

    invoke-interface {v0, v2, v3}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    return-void
.end method

.method public static minus(DD)D
    .locals 0

    sub-double/2addr p0, p2

    return-wide p0
.end method

.method public static minus(II)I
    .locals 0

    sub-int/2addr p0, p1

    return p0
.end method

.method public static minus(JJ)J
    .locals 0

    sub-long/2addr p0, p2

    return-wide p0
.end method

.method public static mod(II)I
    .locals 0

    .line 169
    rem-int/2addr p0, p1

    return p0
.end method

.method public static mod(JJ)J
    .locals 0

    .line 206
    rem-long/2addr p0, p2

    return-wide p0
.end method

.method public static multiply(DD)D
    .locals 0

    mul-double/2addr p0, p2

    return-wide p0
.end method

.method public static multiply(II)I
    .locals 0

    mul-int/2addr p0, p1

    return p0
.end method

.method public static multiply(JJ)J
    .locals 0

    mul-long/2addr p0, p2

    return-wide p0
.end method

.method public static next(D)D
    .locals 2

    const-wide/high16 v0, 0x3ff0000000000000L    # 1.0

    add-double/2addr p0, v0

    return-wide p0
.end method

.method public static next(I)I
    .locals 0

    add-int/lit8 p0, p0, 0x1

    return p0
.end method

.method public static next(J)J
    .locals 2

    const-wide/16 v0, 0x1

    add-long/2addr p0, v0

    return-wide p0
.end method

.method public static or(II)I
    .locals 0

    or-int/2addr p0, p1

    return p0
.end method

.method public static or(JJ)J
    .locals 0

    or-long/2addr p0, p2

    return-wide p0
.end method

.method public static plus(DD)D
    .locals 0

    add-double/2addr p0, p2

    return-wide p0
.end method

.method public static plus(II)I
    .locals 0

    add-int/2addr p0, p1

    return p0
.end method

.method public static plus(JJ)J
    .locals 0

    add-long/2addr p0, p2

    return-wide p0
.end method

.method public static previous(D)D
    .locals 2

    const-wide/high16 v0, 0x3ff0000000000000L    # 1.0

    sub-double/2addr p0, v0

    return-wide p0
.end method

.method public static previous(I)I
    .locals 0

    add-int/lit8 p0, p0, -0x1

    return p0
.end method

.method public static previous(J)J
    .locals 2

    const-wide/16 v0, 0x1

    sub-long/2addr p0, v0

    return-wide p0
.end method

.method public static rightShift(II)I
    .locals 0

    shr-int/2addr p0, p1

    return p0
.end method

.method public static rightShift(JJ)J
    .locals 0

    long-to-int p2, p2

    shr-long/2addr p0, p2

    return-wide p0
.end method

.method private static widenOperators(Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodType;
    .locals 4

    .line 132
    invoke-virtual {p0}, Ljava/lang/invoke/MethodType;->parameterCount()I

    move-result v0

    const/4 v1, 0x0

    const/4 v2, 0x1

    const/4 v3, 0x2

    if-ne v0, v3, :cond_4

    .line 134
    invoke-virtual {p0, v1}, Ljava/lang/invoke/MethodType;->parameterType(I)Ljava/lang/Class;

    move-result-object v0

    .line 135
    invoke-virtual {p0, v2}, Ljava/lang/invoke/MethodType;->parameterType(I)Ljava/lang/Class;

    move-result-object p0

    .line 137
    invoke-static {v0}, Lorg/codehaus/groovy/vmplugin/v8/TypeHelper;->isIntCategory(Ljava/lang/Class;)Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-static {p0}, Lorg/codehaus/groovy/vmplugin/v8/TypeHelper;->isIntCategory(Ljava/lang/Class;)Z

    move-result v1

    if-eqz v1, :cond_0

    sget-object p0, Lorg/codehaus/groovy/vmplugin/v8/IndyMath;->IIV:Ljava/lang/invoke/MethodType;

    return-object p0

    .line 138
    :cond_0
    invoke-static {v0}, Lorg/codehaus/groovy/vmplugin/v8/TypeHelper;->isLongCategory(Ljava/lang/Class;)Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-static {p0}, Lorg/codehaus/groovy/vmplugin/v8/TypeHelper;->isLongCategory(Ljava/lang/Class;)Z

    move-result v1

    if-eqz v1, :cond_1

    sget-object p0, Lorg/codehaus/groovy/vmplugin/v8/IndyMath;->LLV:Ljava/lang/invoke/MethodType;

    return-object p0

    .line 139
    :cond_1
    invoke-static {v0}, Lorg/codehaus/groovy/vmplugin/v8/TypeHelper;->isBigDecCategory(Ljava/lang/Class;)Z

    move-result v1

    if-eqz v1, :cond_2

    invoke-static {p0}, Lorg/codehaus/groovy/vmplugin/v8/TypeHelper;->isBigDecCategory(Ljava/lang/Class;)Z

    move-result v1

    if-eqz v1, :cond_2

    sget-object p0, Lorg/codehaus/groovy/vmplugin/v8/IndyMath;->GGV:Ljava/lang/invoke/MethodType;

    return-object p0

    .line 140
    :cond_2
    invoke-static {v0}, Lorg/codehaus/groovy/vmplugin/v8/TypeHelper;->isDoubleCategory(Ljava/lang/Class;)Z

    move-result v0

    if-eqz v0, :cond_3

    invoke-static {p0}, Lorg/codehaus/groovy/vmplugin/v8/TypeHelper;->isDoubleCategory(Ljava/lang/Class;)Z

    move-result p0

    if-eqz p0, :cond_3

    sget-object p0, Lorg/codehaus/groovy/vmplugin/v8/IndyMath;->DDV:Ljava/lang/invoke/MethodType;

    return-object p0

    .line 142
    :cond_3
    sget-object p0, Lorg/codehaus/groovy/vmplugin/v8/IndyMath;->OOV:Ljava/lang/invoke/MethodType;

    return-object p0

    :cond_4
    if-ne v0, v2, :cond_8

    .line 144
    invoke-virtual {p0, v1}, Ljava/lang/invoke/MethodType;->parameterType(I)Ljava/lang/Class;

    move-result-object v0

    .line 145
    invoke-static {v0}, Lorg/codehaus/groovy/vmplugin/v8/TypeHelper;->isIntCategory(Ljava/lang/Class;)Z

    move-result v1

    if-eqz v1, :cond_5

    sget-object p0, Lorg/codehaus/groovy/vmplugin/v8/IndyMath;->IV:Ljava/lang/invoke/MethodType;

    return-object p0

    .line 146
    :cond_5
    invoke-static {v0}, Lorg/codehaus/groovy/vmplugin/v8/TypeHelper;->isLongCategory(Ljava/lang/Class;)Z

    move-result v1

    if-eqz v1, :cond_6

    sget-object p0, Lorg/codehaus/groovy/vmplugin/v8/IndyMath;->LV:Ljava/lang/invoke/MethodType;

    return-object p0

    .line 147
    :cond_6
    invoke-static {v0}, Lorg/codehaus/groovy/vmplugin/v8/TypeHelper;->isBigDecCategory(Ljava/lang/Class;)Z

    move-result v1

    if-eqz v1, :cond_7

    sget-object p0, Lorg/codehaus/groovy/vmplugin/v8/IndyMath;->GV:Ljava/lang/invoke/MethodType;

    return-object p0

    .line 148
    :cond_7
    invoke-static {v0}, Lorg/codehaus/groovy/vmplugin/v8/TypeHelper;->isDoubleCategory(Ljava/lang/Class;)Z

    move-result v0

    if-eqz v0, :cond_8

    sget-object p0, Lorg/codehaus/groovy/vmplugin/v8/IndyMath;->DV:Ljava/lang/invoke/MethodType;

    :cond_8
    return-object p0
.end method

.method public static xor(II)I
    .locals 0

    xor-int/2addr p0, p1

    return p0
.end method

.method public static xor(JJ)J
    .locals 0

    xor-long/2addr p0, p2

    return-wide p0
.end method
