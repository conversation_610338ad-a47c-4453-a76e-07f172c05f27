.class Lorg/codehaus/groovy/vmplugin/v8/Selector$CastSelector;
.super Lorg/codehaus/groovy/vmplugin/v8/Selector$MethodSelector;
.source "Selector.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/codehaus/groovy/vmplugin/v8/Selector;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0xa
    name = "CastSelector"
.end annotation


# instance fields
.field private final staticSourceType:Ljava/lang/Class;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/lang/Class<",
            "*>;"
        }
    .end annotation
.end field

.field private final staticTargetType:Ljava/lang/Class;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/lang/Class<",
            "*>;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Ljava/lang/invoke/MutableCallSite;[Ljava/lang/Object;)V
    .locals 9

    .line 165
    const-class v2, Lorg/codehaus/groovy/vmplugin/v8/Selector;

    sget-object v4, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface$CallType;->CAST:Lorg/codehaus/groovy/vmplugin/v8/IndyInterface$CallType;

    sget-object v5, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    sget-object v6, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    sget-object v7, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    const-string v3, ""

    move-object v0, p0

    move-object v1, p1

    move-object v8, p2

    invoke-direct/range {v0 .. v8}, Lorg/codehaus/groovy/vmplugin/v8/Selector$MethodSelector;-><init>(Ljava/lang/invoke/MutableCallSite;Ljava/lang/Class;Ljava/lang/String;Lorg/codehaus/groovy/vmplugin/v8/IndyInterface$CallType;Ljava/lang/Boolean;Ljava/lang/Boolean;Ljava/lang/Boolean;[Ljava/lang/Object;)V

    .line 166
    invoke-virtual {p1}, Ljava/lang/invoke/MutableCallSite;->type()Ljava/lang/invoke/MethodType;

    move-result-object p2

    const/4 v0, 0x0

    invoke-virtual {p2, v0}, Ljava/lang/invoke/MethodType;->parameterType(I)Ljava/lang/Class;

    move-result-object p2

    iput-object p2, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$CastSelector;->staticSourceType:Ljava/lang/Class;

    .line 167
    invoke-virtual {p1}, Ljava/lang/invoke/MutableCallSite;->type()Ljava/lang/invoke/MethodType;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/invoke/MethodType;->returnType()Ljava/lang/Class;

    move-result-object p1

    iput-object p1, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$CastSelector;->staticTargetType:Ljava/lang/Class;

    return-void
.end method

.method private castAndSetGuards()V
    .locals 2

    .line 196
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$CastSelector;->handle:Ljava/lang/invoke/MethodHandle;

    iget-object v1, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$CastSelector;->targetType:Ljava/lang/invoke/MethodType;

    invoke-static {v0, v1}, Ljava/lang/invoke/MethodHandles;->explicitCastArguments(Ljava/lang/invoke/MethodHandle;Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$CastSelector;->handle:Ljava/lang/invoke/MethodHandle;

    .line 197
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$CastSelector;->args:[Ljava/lang/Object;

    const/4 v1, 0x0

    aget-object v0, v0, v1

    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/vmplugin/v8/Selector$CastSelector;->setGuards(Ljava/lang/Object;)V

    .line 198
    invoke-virtual {p0}, Lorg/codehaus/groovy/vmplugin/v8/Selector$CastSelector;->doCallSiteTargetSet()V

    return-void
.end method

.method private castToTypeFallBack()V
    .locals 5

    .line 250
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$CastSelector;->handle:Ljava/lang/invoke/MethodHandle;

    if-eqz v0, :cond_0

    return-void

    .line 253
    :cond_0
    sget-object v0, Lorg/codehaus/groovy/vmplugin/v8/IndyGuardsFiltersAndSignatures;->DTT_CAST_TO_TYPE:Ljava/lang/invoke/MethodHandle;

    const/4 v1, 0x1

    new-array v2, v1, [Ljava/lang/Object;

    const/4 v3, 0x0

    iget-object v4, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$CastSelector;->staticTargetType:Ljava/lang/Class;

    aput-object v4, v2, v3

    invoke-static {v0, v1, v2}, Ljava/lang/invoke/MethodHandles;->insertArguments(Ljava/lang/invoke/MethodHandle;I[Ljava/lang/Object;)Ljava/lang/invoke/MethodHandle;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$CastSelector;->handle:Ljava/lang/invoke/MethodHandle;

    return-void
.end method

.method private handleBoolean()V
    .locals 6

    .line 257
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$CastSelector;->handle:Ljava/lang/invoke/MethodHandle;

    if-eqz v0, :cond_0

    return-void

    .line 258
    :cond_0
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$CastSelector;->staticTargetType:Ljava/lang/Class;

    sget-object v1, Ljava/lang/Boolean;->TYPE:Ljava/lang/Class;

    const/4 v2, 0x1

    const/4 v3, 0x0

    if-ne v0, v1, :cond_1

    move v0, v2

    goto :goto_0

    :cond_1
    move v0, v3

    :goto_0
    if-nez v0, :cond_2

    .line 259
    iget-object v1, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$CastSelector;->staticTargetType:Ljava/lang/Class;

    const-class v4, Ljava/lang/Boolean;

    if-eq v1, v4, :cond_2

    return-void

    .line 263
    :cond_2
    sget-object v1, Lorg/codehaus/groovy/vmplugin/v8/IndyGuardsFiltersAndSignatures;->IS_NULL:Ljava/lang/invoke/MethodHandle;

    sget-object v4, Ljava/lang/Boolean;->TYPE:Ljava/lang/Class;

    iget-object v5, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$CastSelector;->staticSourceType:Ljava/lang/Class;

    invoke-static {v4, v5}, Ljava/lang/invoke/MethodType;->methodType(Ljava/lang/Class;Ljava/lang/Class;)Ljava/lang/invoke/MethodType;

    move-result-object v4

    invoke-virtual {v1, v4}, Ljava/lang/invoke/MethodHandle;->asType(Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;

    move-result-object v1

    if-eqz v0, :cond_3

    .line 267
    sget-object v0, Ljava/lang/Boolean;->TYPE:Ljava/lang/Class;

    sget-object v4, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    invoke-static {v0, v4}, Ljava/lang/invoke/MethodHandles;->constant(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/invoke/MethodHandle;

    move-result-object v0

    new-array v2, v2, [Ljava/lang/Class;

    iget-object v4, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$CastSelector;->staticSourceType:Ljava/lang/Class;

    aput-object v4, v2, v3

    invoke-static {v0, v3, v2}, Ljava/lang/invoke/MethodHandles;->dropArguments(Ljava/lang/invoke/MethodHandle;I[Ljava/lang/Class;)Ljava/lang/invoke/MethodHandle;

    move-result-object v0

    goto :goto_1

    .line 269
    :cond_3
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$CastSelector;->staticSourceType:Ljava/lang/Class;

    invoke-static {v0}, Ljava/lang/invoke/MethodHandles;->identity(Ljava/lang/Class;)Ljava/lang/invoke/MethodHandle;

    move-result-object v0

    const-class v2, Ljava/lang/Boolean;

    iget-object v3, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$CastSelector;->staticSourceType:Ljava/lang/Class;

    invoke-static {v2, v3}, Ljava/lang/invoke/MethodType;->methodType(Ljava/lang/Class;Ljava/lang/Class;)Ljava/lang/invoke/MethodType;

    move-result-object v2

    invoke-virtual {v0, v2}, Ljava/lang/invoke/MethodHandle;->asType(Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;

    move-result-object v0

    :goto_1
    const-string v2, "asBoolean"

    .line 272
    iput-object v2, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$CastSelector;->name:Ljava/lang/String;

    .line 273
    invoke-super {p0}, Lorg/codehaus/groovy/vmplugin/v8/Selector$MethodSelector;->setCallSiteTarget()V

    .line 274
    iget-object v2, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$CastSelector;->handle:Ljava/lang/invoke/MethodHandle;

    .line 276
    invoke-static {v1, v0, v2}, Ljava/lang/invoke/MethodHandles;->guardWithTest(Ljava/lang/invoke/MethodHandle;Ljava/lang/invoke/MethodHandle;Ljava/lang/invoke/MethodHandle;)Ljava/lang/invoke/MethodHandle;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$CastSelector;->handle:Ljava/lang/invoke/MethodHandle;

    return-void
.end method

.method private handleCollections()V
    .locals 2

    .line 229
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$CastSelector;->handle:Ljava/lang/invoke/MethodHandle;

    if-eqz v0, :cond_0

    return-void

    .line 231
    :cond_0
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$CastSelector;->args:[Ljava/lang/Object;

    const/4 v1, 0x0

    aget-object v0, v0, v1

    instance-of v0, v0, Ljava/util/Collection;

    if-nez v0, :cond_1

    return-void

    .line 232
    :cond_1
    const-class v0, Ljava/util/HashSet;

    iget-object v1, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$CastSelector;->staticTargetType:Ljava/lang/Class;

    invoke-static {v0, v1}, Lorg/codehaus/groovy/vmplugin/v8/Selector$CastSelector;->isAbstractClassOf(Ljava/lang/Class;Ljava/lang/Class;)Z

    move-result v0

    if-eqz v0, :cond_2

    .line 233
    sget-object v0, Lorg/codehaus/groovy/vmplugin/v8/IndyGuardsFiltersAndSignatures;->HASHSET_CONSTRUCTOR:Ljava/lang/invoke/MethodHandle;

    iput-object v0, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$CastSelector;->handle:Ljava/lang/invoke/MethodHandle;

    goto :goto_0

    .line 234
    :cond_2
    const-class v0, Ljava/util/ArrayList;

    iget-object v1, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$CastSelector;->staticTargetType:Ljava/lang/Class;

    invoke-static {v0, v1}, Lorg/codehaus/groovy/vmplugin/v8/Selector$CastSelector;->isAbstractClassOf(Ljava/lang/Class;Ljava/lang/Class;)Z

    move-result v0

    if-eqz v0, :cond_3

    .line 235
    sget-object v0, Lorg/codehaus/groovy/vmplugin/v8/IndyGuardsFiltersAndSignatures;->ARRAYLIST_CONSTRUCTOR:Ljava/lang/invoke/MethodHandle;

    iput-object v0, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$CastSelector;->handle:Ljava/lang/invoke/MethodHandle;

    :cond_3
    :goto_0
    return-void
.end method

.method private handleInstanceCase()V
    .locals 3

    .line 215
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$CastSelector;->handle:Ljava/lang/invoke/MethodHandle;

    if-eqz v0, :cond_0

    return-void

    .line 217
    :cond_0
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$CastSelector;->staticTargetType:Ljava/lang/Class;

    iget-object v1, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$CastSelector;->args:[Ljava/lang/Object;

    const/4 v2, 0x0

    aget-object v1, v1, v2

    invoke-virtual {v1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    move-result v0

    if-eqz v0, :cond_1

    .line 218
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$CastSelector;->staticSourceType:Ljava/lang/Class;

    invoke-static {v0}, Ljava/lang/invoke/MethodHandles;->identity(Ljava/lang/Class;)Ljava/lang/invoke/MethodHandle;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$CastSelector;->handle:Ljava/lang/invoke/MethodHandle;

    :cond_1
    return-void
.end method

.method private handleNullWithoutBoolean()V
    .locals 5

    .line 202
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$CastSelector;->handle:Ljava/lang/invoke/MethodHandle;

    if-nez v0, :cond_2

    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$CastSelector;->args:[Ljava/lang/Object;

    const/4 v1, 0x0

    aget-object v0, v0, v1

    if-eqz v0, :cond_0

    goto :goto_0

    .line 204
    :cond_0
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$CastSelector;->staticTargetType:Ljava/lang/Class;

    invoke-virtual {v0}, Ljava/lang/Class;->isPrimitive()Z

    move-result v0

    if-eqz v0, :cond_1

    .line 205
    sget-object v0, Lorg/codehaus/groovy/vmplugin/v8/IndyGuardsFiltersAndSignatures;->GROOVY_CAST_EXCEPTION:Ljava/lang/invoke/MethodHandle;

    const/4 v2, 0x1

    new-array v3, v2, [Ljava/lang/Object;

    iget-object v4, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$CastSelector;->staticTargetType:Ljava/lang/Class;

    aput-object v4, v3, v1

    invoke-static {v0, v2, v3}, Ljava/lang/invoke/MethodHandles;->insertArguments(Ljava/lang/invoke/MethodHandle;I[Ljava/lang/Object;)Ljava/lang/invoke/MethodHandle;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$CastSelector;->handle:Ljava/lang/invoke/MethodHandle;

    .line 208
    invoke-direct {p0}, Lorg/codehaus/groovy/vmplugin/v8/Selector$CastSelector;->castAndSetGuards()V

    goto :goto_0

    .line 210
    :cond_1
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$CastSelector;->staticSourceType:Ljava/lang/Class;

    invoke-static {v0}, Ljava/lang/invoke/MethodHandles;->identity(Ljava/lang/Class;)Ljava/lang/invoke/MethodHandle;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$CastSelector;->handle:Ljava/lang/invoke/MethodHandle;

    :cond_2
    :goto_0
    return-void
.end method

.method private handleSAM()V
    .locals 4

    .line 240
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$CastSelector;->handle:Ljava/lang/invoke/MethodHandle;

    if-eqz v0, :cond_0

    return-void

    .line 242
    :cond_0
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$CastSelector;->args:[Ljava/lang/Object;

    const/4 v1, 0x0

    aget-object v0, v0, v1

    instance-of v0, v0, Lgroovy/lang/Closure;

    if-nez v0, :cond_1

    return-void

    .line 243
    :cond_1
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$CastSelector;->staticTargetType:Ljava/lang/Class;

    invoke-static {v0}, Lorg/codehaus/groovy/reflection/stdclasses/CachedSAMClass;->getSAMMethod(Ljava/lang/Class;)Ljava/lang/reflect/Method;

    move-result-object v0

    if-nez v0, :cond_2

    return-void

    .line 246
    :cond_2
    sget-object v2, Lorg/codehaus/groovy/vmplugin/v8/IndyGuardsFiltersAndSignatures;->SAM_CONVERSION:Ljava/lang/invoke/MethodHandle;

    const/4 v3, 0x2

    new-array v3, v3, [Ljava/lang/Object;

    aput-object v0, v3, v1

    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$CastSelector;->staticTargetType:Ljava/lang/Class;

    const/4 v1, 0x1

    aput-object v0, v3, v1

    invoke-static {v2, v1, v3}, Ljava/lang/invoke/MethodHandles;->insertArguments(Ljava/lang/invoke/MethodHandle;I[Ljava/lang/Object;)Ljava/lang/invoke/MethodHandle;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$CastSelector;->handle:Ljava/lang/invoke/MethodHandle;

    return-void
.end method

.method private static isAbstractClassOf(Ljava/lang/Class;Ljava/lang/Class;)Z
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Class<",
            "*>;",
            "Ljava/lang/Class<",
            "*>;)Z"
        }
    .end annotation

    .line 223
    invoke-virtual {p0, p1}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    move-result p0

    if-nez p0, :cond_0

    const/4 p0, 0x0

    return p0

    .line 224
    :cond_0
    invoke-virtual {p1}, Ljava/lang/Class;->isInterface()Z

    move-result p0

    if-eqz p0, :cond_1

    const/4 p0, 0x1

    return p0

    .line 225
    :cond_1
    invoke-virtual {p1}, Ljava/lang/Class;->getModifiers()I

    move-result p0

    invoke-static {p0}, Ljava/lang/reflect/Modifier;->isAbstract(I)Z

    move-result p0

    return p0
.end method


# virtual methods
.method public setCallSiteTarget()V
    .locals 2

    .line 174
    invoke-direct {p0}, Lorg/codehaus/groovy/vmplugin/v8/Selector$CastSelector;->handleBoolean()V

    .line 175
    invoke-direct {p0}, Lorg/codehaus/groovy/vmplugin/v8/Selector$CastSelector;->handleNullWithoutBoolean()V

    .line 178
    invoke-direct {p0}, Lorg/codehaus/groovy/vmplugin/v8/Selector$CastSelector;->handleInstanceCase()V

    .line 182
    invoke-direct {p0}, Lorg/codehaus/groovy/vmplugin/v8/Selector$CastSelector;->handleCollections()V

    .line 183
    invoke-direct {p0}, Lorg/codehaus/groovy/vmplugin/v8/Selector$CastSelector;->handleSAM()V

    .line 190
    invoke-direct {p0}, Lorg/codehaus/groovy/vmplugin/v8/Selector$CastSelector;->castToTypeFallBack()V

    .line 192
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$CastSelector;->handle:Ljava/lang/invoke/MethodHandle;

    invoke-virtual {v0}, Ljava/lang/invoke/MethodHandle;->type()Ljava/lang/invoke/MethodType;

    move-result-object v0

    iget-object v1, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$CastSelector;->callSite:Ljava/lang/invoke/MutableCallSite;

    invoke-virtual {v1}, Ljava/lang/invoke/MutableCallSite;->type()Ljava/lang/invoke/MethodType;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/invoke/MethodType;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_0

    invoke-direct {p0}, Lorg/codehaus/groovy/vmplugin/v8/Selector$CastSelector;->castAndSetGuards()V

    :cond_0
    return-void
.end method
