.class public final synthetic Lorg/codehaus/groovy/vmplugin/v8/PluginDefaultGroovyMethods$$ExternalSyntheticLambda8;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Function;


# instance fields
.field public final synthetic f$0:Ljava/lang/Class;


# direct methods
.method public synthetic constructor <init>(Ljava/lang/Class;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/codehaus/groovy/vmplugin/v8/PluginDefaultGroovyMethods$$ExternalSyntheticLambda8;->f$0:Ljava/lang/Class;

    return-void
.end method


# virtual methods
.method public final apply(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v8/PluginDefaultGroovyMethods$$ExternalSyntheticLambda8;->f$0:Ljava/lang/Class;

    invoke-static {v0, p1}, Lorg/codehaus/groovy/vmplugin/v8/PluginDefaultGroovyMethods;->$r8$lambda$qD38EpryxEVHkutWpHvV2SolY9k(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method
