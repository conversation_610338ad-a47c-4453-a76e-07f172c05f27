.class Lorg/codehaus/groovy/vmplugin/v8/PluginDefaultGroovyMethods$TransformedFuture;
.super Ljava/lang/Object;
.source "PluginDefaultGroovyMethods.java"

# interfaces
.implements Ljava/util/concurrent/Future;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/codehaus/groovy/vmplugin/v8/PluginDefaultGroovyMethods;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0xa
    name = "TransformedFuture"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "<E:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;",
        "Ljava/util/concurrent/Future<",
        "TE;>;"
    }
.end annotation


# instance fields
.field private final delegate:Ljava/util/concurrent/Future;

.field private final transform:Lgroovy/lang/Closure;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lgroovy/lang/Closure<",
            "TE;>;"
        }
    .end annotation
.end field


# direct methods
.method private constructor <init>(Ljava/util/concurrent/Future;Lgroovy/lang/Closure;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/concurrent/Future;",
            "Lgroovy/lang/Closure<",
            "TE;>;)V"
        }
    .end annotation

    .line 480
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 481
    iput-object p1, p0, Lorg/codehaus/groovy/vmplugin/v8/PluginDefaultGroovyMethods$TransformedFuture;->delegate:Ljava/util/concurrent/Future;

    .line 482
    iput-object p2, p0, Lorg/codehaus/groovy/vmplugin/v8/PluginDefaultGroovyMethods$TransformedFuture;->transform:Lgroovy/lang/Closure;

    return-void
.end method

.method synthetic constructor <init>(Ljava/util/concurrent/Future;Lgroovy/lang/Closure;Lorg/codehaus/groovy/vmplugin/v8/PluginDefaultGroovyMethods$1;)V
    .locals 0

    .line 476
    invoke-direct {p0, p1, p2}, Lorg/codehaus/groovy/vmplugin/v8/PluginDefaultGroovyMethods$TransformedFuture;-><init>(Ljava/util/concurrent/Future;Lgroovy/lang/Closure;)V

    return-void
.end method


# virtual methods
.method public cancel(Z)Z
    .locals 1

    .line 487
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v8/PluginDefaultGroovyMethods$TransformedFuture;->delegate:Ljava/util/concurrent/Future;

    invoke-interface {v0, p1}, Ljava/util/concurrent/Future;->cancel(Z)Z

    move-result p1

    return p1
.end method

.method public get()Ljava/lang/Object;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()TE;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/InterruptedException;,
            Ljava/util/concurrent/ExecutionException;
        }
    .end annotation

    .line 502
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v8/PluginDefaultGroovyMethods$TransformedFuture;->transform:Lgroovy/lang/Closure;

    iget-object v1, p0, Lorg/codehaus/groovy/vmplugin/v8/PluginDefaultGroovyMethods$TransformedFuture;->delegate:Ljava/util/concurrent/Future;

    invoke-interface {v1}, Ljava/util/concurrent/Future;->get()Ljava/lang/Object;

    move-result-object v1

    invoke-virtual {v0, v1}, Lgroovy/lang/Closure;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    return-object v0
.end method

.method public get(JLjava/util/concurrent/TimeUnit;)Ljava/lang/Object;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(J",
            "Ljava/util/concurrent/TimeUnit;",
            ")TE;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/InterruptedException;,
            Ljava/util/concurrent/ExecutionException;,
            Ljava/util/concurrent/TimeoutException;
        }
    .end annotation

    .line 507
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v8/PluginDefaultGroovyMethods$TransformedFuture;->transform:Lgroovy/lang/Closure;

    iget-object v1, p0, Lorg/codehaus/groovy/vmplugin/v8/PluginDefaultGroovyMethods$TransformedFuture;->delegate:Ljava/util/concurrent/Future;

    invoke-interface {v1, p1, p2, p3}, Ljava/util/concurrent/Future;->get(JLjava/util/concurrent/TimeUnit;)Ljava/lang/Object;

    move-result-object p1

    invoke-virtual {v0, p1}, Lgroovy/lang/Closure;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public isCancelled()Z
    .locals 1

    .line 492
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v8/PluginDefaultGroovyMethods$TransformedFuture;->delegate:Ljava/util/concurrent/Future;

    invoke-interface {v0}, Ljava/util/concurrent/Future;->isCancelled()Z

    move-result v0

    return v0
.end method

.method public isDone()Z
    .locals 1

    .line 497
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v8/PluginDefaultGroovyMethods$TransformedFuture;->delegate:Ljava/util/concurrent/Future;

    invoke-interface {v0}, Ljava/util/concurrent/Future;->isDone()Z

    move-result v0

    return v0
.end method
