.class Lorg/codehaus/groovy/vmplugin/v8/MethodHandleWrapper$NullMethodHandleWrapper;
.super Lorg/codehaus/groovy/vmplugin/v8/MethodHandleWrapper;
.source "MethodHandleWrapper.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/codehaus/groovy/vmplugin/v8/MethodHandleWrapper;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0xa
    name = "NullMethodHandleWrapper"
.end annotation


# static fields
.field public static final INSTANCE:Lorg/codehaus/groovy/vmplugin/v8/MethodHandleWrapper$NullMethodHandleWrapper;


# direct methods
.method static constructor <clinit>()V
    .locals 3

    .line 70
    new-instance v0, Lorg/codehaus/groovy/vmplugin/v8/MethodHandleWrapper$NullMethodHandleWrapper;

    const/4 v1, 0x0

    const/4 v2, 0x0

    invoke-direct {v0, v1, v1, v2}, Lorg/codehaus/groovy/vmplugin/v8/MethodHandleWrapper$NullMethodHandleWrapper;-><init>(Ljava/lang/invoke/MethodHandle;Ljava/lang/invoke/MethodHandle;Z)V

    sput-object v0, Lorg/codehaus/groovy/vmplugin/v8/MethodHandleWrapper$NullMethodHandleWrapper;->INSTANCE:Lorg/codehaus/groovy/vmplugin/v8/MethodHandleWrapper$NullMethodHandleWrapper;

    return-void
.end method

.method private constructor <init>(Ljava/lang/invoke/MethodHandle;Ljava/lang/invoke/MethodHandle;Z)V
    .locals 0

    .line 73
    invoke-direct {p0, p1, p2, p3}, Lorg/codehaus/groovy/vmplugin/v8/MethodHandleWrapper;-><init>(Ljava/lang/invoke/MethodHandle;Ljava/lang/invoke/MethodHandle;Z)V

    return-void
.end method
