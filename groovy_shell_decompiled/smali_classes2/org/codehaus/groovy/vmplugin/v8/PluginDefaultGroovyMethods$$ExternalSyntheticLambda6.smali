.class public final synthetic Lorg/codehaus/groovy/vmplugin/v8/PluginDefaultGroovyMethods$$ExternalSyntheticLambda6;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Supplier;


# static fields
.field public static final synthetic INSTANCE:Lorg/codehaus/groovy/vmplugin/v8/PluginDefaultGroovyMethods$$ExternalSyntheticLambda6;


# direct methods
.method static synthetic constructor <clinit>()V
    .locals 1

    new-instance v0, Lorg/codehaus/groovy/vmplugin/v8/PluginDefaultGroovyMethods$$ExternalSyntheticLambda6;

    invoke-direct {v0}, Lorg/codehaus/groovy/vmplugin/v8/PluginDefaultGroovyMethods$$ExternalSyntheticLambda6;-><init>()V

    sput-object v0, Lorg/codehaus/groovy/vmplugin/v8/PluginDefaultGroovyMethods$$ExternalSyntheticLambda6;->INSTANCE:Lorg/codehaus/groovy/vmplugin/v8/PluginDefaultGroovyMethods$$ExternalSyntheticLambda6;

    return-void
.end method

.method private synthetic constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final get()Ljava/lang/Object;
    .locals 1

    invoke-static {}, Lorg/codehaus/groovy/vmplugin/v8/PluginDefaultGroovyMethods;->$r8$lambda$cCGlUYZBvl9Zymt4g0wOcziB0gg()Ljava/util/stream/Stream;

    move-result-object v0

    return-object v0
.end method
