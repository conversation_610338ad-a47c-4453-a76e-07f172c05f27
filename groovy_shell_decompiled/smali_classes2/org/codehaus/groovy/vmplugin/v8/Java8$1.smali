.class synthetic Lorg/codehaus/groovy/vmplugin/v8/Java8$1;
.super Ljava/lang/Object;
.source "Java8.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/codehaus/groovy/vmplugin/v8/Java8;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1008
    name = null
.end annotation


# static fields
.field static final synthetic $SwitchMap$java$lang$annotation$ElementType:[I

.field static final synthetic $SwitchMap$java$lang$annotation$RetentionPolicy:[I


# direct methods
.method static constructor <clinit>()V
    .locals 6

    .line 157
    invoke-static {}, Ljava/lang/annotation/ElementType;->values()[Ljava/lang/annotation/ElementType;

    move-result-object v0

    array-length v0, v0

    new-array v0, v0, [I

    sput-object v0, Lorg/codehaus/groovy/vmplugin/v8/Java8$1;->$SwitchMap$java$lang$annotation$ElementType:[I

    const/4 v1, 0x1

    :try_start_0
    sget-object v2, Ljava/lang/annotation/ElementType;->TYPE:Ljava/lang/annotation/ElementType;

    invoke-virtual {v2}, Ljava/lang/annotation/ElementType;->ordinal()I

    move-result v2

    aput v1, v0, v2
    :try_end_0
    .catch Ljava/lang/NoSuchFieldError; {:try_start_0 .. :try_end_0} :catch_0

    :catch_0
    const/4 v0, 0x2

    :try_start_1
    sget-object v2, Lorg/codehaus/groovy/vmplugin/v8/Java8$1;->$SwitchMap$java$lang$annotation$ElementType:[I

    sget-object v3, Ljava/lang/annotation/ElementType;->CONSTRUCTOR:Ljava/lang/annotation/ElementType;

    invoke-virtual {v3}, Ljava/lang/annotation/ElementType;->ordinal()I

    move-result v3

    aput v0, v2, v3
    :try_end_1
    .catch Ljava/lang/NoSuchFieldError; {:try_start_1 .. :try_end_1} :catch_1

    :catch_1
    const/4 v2, 0x3

    :try_start_2
    sget-object v3, Lorg/codehaus/groovy/vmplugin/v8/Java8$1;->$SwitchMap$java$lang$annotation$ElementType:[I

    sget-object v4, Ljava/lang/annotation/ElementType;->METHOD:Ljava/lang/annotation/ElementType;

    invoke-virtual {v4}, Ljava/lang/annotation/ElementType;->ordinal()I

    move-result v4

    aput v2, v3, v4
    :try_end_2
    .catch Ljava/lang/NoSuchFieldError; {:try_start_2 .. :try_end_2} :catch_2

    :catch_2
    :try_start_3
    sget-object v3, Lorg/codehaus/groovy/vmplugin/v8/Java8$1;->$SwitchMap$java$lang$annotation$ElementType:[I

    sget-object v4, Ljava/lang/annotation/ElementType;->FIELD:Ljava/lang/annotation/ElementType;

    invoke-virtual {v4}, Ljava/lang/annotation/ElementType;->ordinal()I

    move-result v4

    const/4 v5, 0x4

    aput v5, v3, v4
    :try_end_3
    .catch Ljava/lang/NoSuchFieldError; {:try_start_3 .. :try_end_3} :catch_3

    :catch_3
    :try_start_4
    sget-object v3, Lorg/codehaus/groovy/vmplugin/v8/Java8$1;->$SwitchMap$java$lang$annotation$ElementType:[I

    sget-object v4, Ljava/lang/annotation/ElementType;->PARAMETER:Ljava/lang/annotation/ElementType;

    invoke-virtual {v4}, Ljava/lang/annotation/ElementType;->ordinal()I

    move-result v4

    const/4 v5, 0x5

    aput v5, v3, v4
    :try_end_4
    .catch Ljava/lang/NoSuchFieldError; {:try_start_4 .. :try_end_4} :catch_4

    :catch_4
    :try_start_5
    sget-object v3, Lorg/codehaus/groovy/vmplugin/v8/Java8$1;->$SwitchMap$java$lang$annotation$ElementType:[I

    sget-object v4, Ljava/lang/annotation/ElementType;->LOCAL_VARIABLE:Ljava/lang/annotation/ElementType;

    invoke-virtual {v4}, Ljava/lang/annotation/ElementType;->ordinal()I

    move-result v4

    const/4 v5, 0x6

    aput v5, v3, v4
    :try_end_5
    .catch Ljava/lang/NoSuchFieldError; {:try_start_5 .. :try_end_5} :catch_5

    :catch_5
    :try_start_6
    sget-object v3, Lorg/codehaus/groovy/vmplugin/v8/Java8$1;->$SwitchMap$java$lang$annotation$ElementType:[I

    sget-object v4, Ljava/lang/annotation/ElementType;->ANNOTATION_TYPE:Ljava/lang/annotation/ElementType;

    invoke-virtual {v4}, Ljava/lang/annotation/ElementType;->ordinal()I

    move-result v4

    const/4 v5, 0x7

    aput v5, v3, v4
    :try_end_6
    .catch Ljava/lang/NoSuchFieldError; {:try_start_6 .. :try_end_6} :catch_6

    :catch_6
    :try_start_7
    sget-object v3, Lorg/codehaus/groovy/vmplugin/v8/Java8$1;->$SwitchMap$java$lang$annotation$ElementType:[I

    sget-object v4, Ljava/lang/annotation/ElementType;->PACKAGE:Ljava/lang/annotation/ElementType;

    invoke-virtual {v4}, Ljava/lang/annotation/ElementType;->ordinal()I

    move-result v4

    const/16 v5, 0x8

    aput v5, v3, v4
    :try_end_7
    .catch Ljava/lang/NoSuchFieldError; {:try_start_7 .. :try_end_7} :catch_7

    :catch_7
    :try_start_8
    sget-object v3, Lorg/codehaus/groovy/vmplugin/v8/Java8$1;->$SwitchMap$java$lang$annotation$ElementType:[I

    sget-object v4, Ljava/lang/annotation/ElementType;->TYPE_PARAMETER:Ljava/lang/annotation/ElementType;

    invoke-virtual {v4}, Ljava/lang/annotation/ElementType;->ordinal()I

    move-result v4

    const/16 v5, 0x9

    aput v5, v3, v4
    :try_end_8
    .catch Ljava/lang/NoSuchFieldError; {:try_start_8 .. :try_end_8} :catch_8

    :catch_8
    :try_start_9
    sget-object v3, Lorg/codehaus/groovy/vmplugin/v8/Java8$1;->$SwitchMap$java$lang$annotation$ElementType:[I

    sget-object v4, Ljava/lang/annotation/ElementType;->TYPE_USE:Ljava/lang/annotation/ElementType;

    invoke-virtual {v4}, Ljava/lang/annotation/ElementType;->ordinal()I

    move-result v4

    const/16 v5, 0xa

    aput v5, v3, v4
    :try_end_9
    .catch Ljava/lang/NoSuchFieldError; {:try_start_9 .. :try_end_9} :catch_9

    .line 120
    :catch_9
    invoke-static {}, Ljava/lang/annotation/RetentionPolicy;->values()[Ljava/lang/annotation/RetentionPolicy;

    move-result-object v3

    array-length v3, v3

    new-array v3, v3, [I

    sput-object v3, Lorg/codehaus/groovy/vmplugin/v8/Java8$1;->$SwitchMap$java$lang$annotation$RetentionPolicy:[I

    :try_start_a
    sget-object v4, Ljava/lang/annotation/RetentionPolicy;->RUNTIME:Ljava/lang/annotation/RetentionPolicy;

    invoke-virtual {v4}, Ljava/lang/annotation/RetentionPolicy;->ordinal()I

    move-result v4

    aput v1, v3, v4
    :try_end_a
    .catch Ljava/lang/NoSuchFieldError; {:try_start_a .. :try_end_a} :catch_a

    :catch_a
    :try_start_b
    sget-object v1, Lorg/codehaus/groovy/vmplugin/v8/Java8$1;->$SwitchMap$java$lang$annotation$RetentionPolicy:[I

    sget-object v3, Ljava/lang/annotation/RetentionPolicy;->SOURCE:Ljava/lang/annotation/RetentionPolicy;

    invoke-virtual {v3}, Ljava/lang/annotation/RetentionPolicy;->ordinal()I

    move-result v3

    aput v0, v1, v3
    :try_end_b
    .catch Ljava/lang/NoSuchFieldError; {:try_start_b .. :try_end_b} :catch_b

    :catch_b
    :try_start_c
    sget-object v0, Lorg/codehaus/groovy/vmplugin/v8/Java8$1;->$SwitchMap$java$lang$annotation$RetentionPolicy:[I

    sget-object v1, Ljava/lang/annotation/RetentionPolicy;->CLASS:Ljava/lang/annotation/RetentionPolicy;

    invoke-virtual {v1}, Ljava/lang/annotation/RetentionPolicy;->ordinal()I

    move-result v1

    aput v2, v0, v1
    :try_end_c
    .catch Ljava/lang/NoSuchFieldError; {:try_start_c .. :try_end_c} :catch_c

    :catch_c
    return-void
.end method
