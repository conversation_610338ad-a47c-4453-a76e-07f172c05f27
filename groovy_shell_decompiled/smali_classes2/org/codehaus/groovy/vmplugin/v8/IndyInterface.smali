.class public Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;
.super Ljava/lang/Object;
.source "IndyInterface.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/codehaus/groovy/vmplugin/v8/IndyInterface$CallType;,
        Lorg/codehaus/groovy/vmplugin/v8/IndyInterface$FallbackSupplier;
    }
.end annotation


# static fields
.field private static final FROM_CACHE_METHOD:Ljava/lang/invoke/MethodHandle;

.field public static final GROOVY_OBJECT:I = 0x4

.field public static final IMPLICIT_THIS:I = 0x8

.field private static final INDY_FALLBACK_THRESHOLD:J

.field private static final INDY_OPTIMIZE_THRESHOLD:J

.field protected static final LOG:Ljava/util/logging/Logger;

.field protected static final LOG_ENABLED:Z

.field public static final LOOKUP:Ljava/lang/invoke/MethodHandles$Lookup;

.field private static final NULL_METHOD_HANDLE_WRAPPER:Lorg/codehaus/groovy/vmplugin/v8/MethodHandleWrapper;

.field public static final SAFE_NAVIGATION:I = 0x1

.field private static final SELECT_METHOD:Ljava/lang/invoke/MethodHandle;

.field public static final SPREAD_CALL:I = 0x10

.field public static final THIS_CALL:I = 0x2

.field public static final UNCACHED_CALL:I = 0x20

.field protected static switchPoint:Ljava/lang/invoke/SwitchPoint;


# direct methods
.method static constructor <clinit>()V
    .locals 15

    .line 50
    const-class v0, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;

    const-wide/16 v1, 0x2710

    invoke-static {v1, v2}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v1

    const-string v2, "groovy.indy.optimize.threshold"

    invoke-static {v2, v1}, Lorg/apache/groovy/util/SystemUtil;->getLongSafe(Ljava/lang/String;Ljava/lang/Long;)Ljava/lang/Long;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/Long;->longValue()J

    move-result-wide v2

    sput-wide v2, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->INDY_OPTIMIZE_THRESHOLD:J

    const-string v2, "groovy.indy.fallback.threshold"

    .line 51
    invoke-static {v2, v1}, Lorg/apache/groovy/util/SystemUtil;->getLongSafe(Ljava/lang/String;Ljava/lang/Long;)Ljava/lang/Long;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/Long;->longValue()J

    move-result-wide v1

    sput-wide v1, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->INDY_FALLBACK_THRESHOLD:J

    .line 60
    invoke-static {}, Lorg/codehaus/groovy/vmplugin/v8/MethodHandleWrapper;->getNullMethodHandleWrapper()Lorg/codehaus/groovy/vmplugin/v8/MethodHandleWrapper;

    move-result-object v1

    sput-object v1, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->NULL_METHOD_HANDLE_WRAPPER:Lorg/codehaus/groovy/vmplugin/v8/MethodHandleWrapper;

    .line 123
    invoke-virtual {v0}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Ljava/util/logging/Logger;->getLogger(Ljava/lang/String;)Ljava/util/logging/Logger;

    move-result-object v1

    sput-object v1, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->LOG:Ljava/util/logging/Logger;

    const/4 v2, 0x1

    const/4 v3, 0x0

    :try_start_0
    const-string v4, "groovy.indy.logging"

    .line 126
    invoke-static {v4}, Ljava/lang/System;->getProperty(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v4

    if-eqz v4, :cond_0

    .line 127
    sget-object v4, Ljava/util/logging/Level;->ALL:Ljava/util/logging/Level;

    invoke-virtual {v1, v4}, Ljava/util/logging/Logger;->setLevel(Ljava/util/logging/Level;)V
    :try_end_0
    .catch Ljava/lang/SecurityException; {:try_start_0 .. :try_end_0} :catch_0

    move v1, v2

    goto :goto_0

    :catch_0
    :cond_0
    move v1, v3

    .line 134
    :goto_0
    sput-boolean v1, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->LOG_ENABLED:Z

    .line 140
    invoke-static {}, Ljava/lang/invoke/MethodHandles;->lookup()Ljava/lang/invoke/MethodHandles$Lookup;

    move-result-object v1

    sput-object v1, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->LOOKUP:Ljava/lang/invoke/MethodHandles$Lookup;

    .line 155
    :try_start_1
    const-class v4, Ljava/lang/Object;

    const-class v5, Ljava/lang/invoke/MutableCallSite;

    const/16 v6, 0x8

    new-array v7, v6, [Ljava/lang/Class;

    const-class v8, Ljava/lang/Class;

    aput-object v8, v7, v3

    const-class v8, Ljava/lang/String;

    aput-object v8, v7, v2

    sget-object v8, Ljava/lang/Integer;->TYPE:Ljava/lang/Class;

    const/4 v9, 0x2

    aput-object v8, v7, v9

    const-class v8, Ljava/lang/Boolean;

    const/4 v10, 0x3

    aput-object v8, v7, v10

    const-class v8, Ljava/lang/Boolean;

    const/4 v11, 0x4

    aput-object v8, v7, v11

    const-class v8, Ljava/lang/Boolean;

    const/4 v12, 0x5

    aput-object v8, v7, v12

    const-class v8, Ljava/lang/Object;

    const/4 v13, 0x6

    aput-object v8, v7, v13

    const-class v8, [Ljava/lang/Object;

    const/4 v14, 0x7

    aput-object v8, v7, v14

    invoke-static {v4, v5, v7}, Ljava/lang/invoke/MethodType;->methodType(Ljava/lang/Class;Ljava/lang/Class;[Ljava/lang/Class;)Ljava/lang/invoke/MethodType;

    move-result-object v4

    const-string v5, "fromCache"

    .line 156
    invoke-virtual {v1, v0, v5, v4}, Ljava/lang/invoke/MethodHandles$Lookup;->findStatic(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;

    move-result-object v4

    sput-object v4, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->FROM_CACHE_METHOD:Ljava/lang/invoke/MethodHandle;
    :try_end_1
    .catch Ljava/lang/Exception; {:try_start_1 .. :try_end_1} :catch_2

    .line 162
    :try_start_2
    const-class v4, Ljava/lang/Object;

    const-class v5, Ljava/lang/invoke/MutableCallSite;

    new-array v6, v6, [Ljava/lang/Class;

    const-class v7, Ljava/lang/Class;

    aput-object v7, v6, v3

    const-class v3, Ljava/lang/String;

    aput-object v3, v6, v2

    sget-object v2, Ljava/lang/Integer;->TYPE:Ljava/lang/Class;

    aput-object v2, v6, v9

    const-class v2, Ljava/lang/Boolean;

    aput-object v2, v6, v10

    const-class v2, Ljava/lang/Boolean;

    aput-object v2, v6, v11

    const-class v2, Ljava/lang/Boolean;

    aput-object v2, v6, v12

    const-class v2, Ljava/lang/Object;

    aput-object v2, v6, v13

    const-class v2, [Ljava/lang/Object;

    aput-object v2, v6, v14

    invoke-static {v4, v5, v6}, Ljava/lang/invoke/MethodType;->methodType(Ljava/lang/Class;Ljava/lang/Class;[Ljava/lang/Class;)Ljava/lang/invoke/MethodType;

    move-result-object v2

    const-string v3, "selectMethod"

    .line 163
    invoke-virtual {v1, v0, v3, v2}, Ljava/lang/invoke/MethodHandles$Lookup;->findStatic(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->SELECT_METHOD:Ljava/lang/invoke/MethodHandle;
    :try_end_2
    .catch Ljava/lang/Exception; {:try_start_2 .. :try_end_2} :catch_1

    .line 169
    new-instance v0, Ljava/lang/invoke/SwitchPoint;

    invoke-direct {v0}, Ljava/lang/invoke/SwitchPoint;-><init>()V

    sput-object v0, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->switchPoint:Ljava/lang/invoke/SwitchPoint;

    .line 172
    invoke-static {}, Lgroovy/lang/GroovySystem;->getMetaClassRegistry()Lgroovy/lang/MetaClassRegistry;

    move-result-object v0

    sget-object v1, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface$$ExternalSyntheticLambda0;->INSTANCE:Lorg/codehaus/groovy/vmplugin/v8/IndyInterface$$ExternalSyntheticLambda0;

    invoke-interface {v0, v1}, Lgroovy/lang/MetaClassRegistry;->addMetaClassRegistryChangeEventListener(Lgroovy/lang/MetaClassRegistryChangeEventListener;)V

    return-void

    :catch_1
    move-exception v0

    .line 165
    new-instance v1, Lorg/codehaus/groovy/GroovyBugError;

    invoke-direct {v1, v0}, Lorg/codehaus/groovy/GroovyBugError;-><init>(Ljava/lang/Exception;)V

    throw v1

    :catch_2
    move-exception v0

    .line 158
    new-instance v1, Lorg/codehaus/groovy/GroovyBugError;

    invoke-direct {v1, v0}, Lorg/codehaus/groovy/GroovyBugError;-><init>(Ljava/lang/Exception;)V

    throw v1
.end method

.method public constructor <init>()V
    .locals 0

    .line 49
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method static synthetic access$000(Ljava/lang/invoke/MutableCallSite;Ljava/lang/Class;Ljava/lang/String;ILjava/lang/Boolean;Ljava/lang/Boolean;Ljava/lang/Boolean;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/vmplugin/v8/MethodHandleWrapper;
    .locals 0

    .line 49
    invoke-static/range {p0 .. p8}, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->fallback(Ljava/lang/invoke/MutableCallSite;Ljava/lang/Class;Ljava/lang/String;ILjava/lang/Boolean;Ljava/lang/Boolean;Ljava/lang/Boolean;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/vmplugin/v8/MethodHandleWrapper;

    move-result-object p0

    return-object p0
.end method

.method public static bootstrap(Ljava/lang/invoke/MethodHandles$Lookup;Ljava/lang/String;Ljava/lang/invoke/MethodType;Ljava/lang/String;I)Ljava/lang/invoke/CallSite;
    .locals 8

    .line 207
    invoke-static {p1}, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface$CallType;->fromCallSiteName(Ljava/lang/String;)Lorg/codehaus/groovy/vmplugin/v8/IndyInterface$CallType;

    move-result-object v0

    if-eqz v0, :cond_3

    .line 210
    invoke-virtual {v0}, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface$CallType;->ordinal()I

    move-result v3

    and-int/lit8 p1, p4, 0x1

    const/4 v0, 0x0

    const/4 v1, 0x1

    if-eqz p1, :cond_0

    move v5, v1

    goto :goto_0

    :cond_0
    move v5, v0

    :goto_0
    and-int/lit8 p1, p4, 0x2

    if-eqz p1, :cond_1

    move v6, v1

    goto :goto_1

    :cond_1
    move v6, v0

    :goto_1
    and-int/lit8 p1, p4, 0x10

    if-eqz p1, :cond_2

    move v7, v1

    goto :goto_2

    :cond_2
    move v7, v0

    :goto_2
    move-object v1, p0

    move-object v2, p3

    move-object v4, p2

    .line 215
    invoke-static/range {v1 .. v7}, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->realBootstrap(Ljava/lang/invoke/MethodHandles$Lookup;Ljava/lang/String;ILjava/lang/invoke/MethodType;ZZZ)Ljava/lang/invoke/CallSite;

    move-result-object p0

    return-object p0

    .line 208
    :cond_3
    new-instance p0, Lorg/codehaus/groovy/GroovyBugError;

    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string p3, "Unknown call type: "

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {p0, p1}, Lorg/codehaus/groovy/GroovyBugError;-><init>(Ljava/lang/String;)V

    throw p0
.end method

.method private static doWithCallSite(Ljava/lang/invoke/MutableCallSite;[Ljava/lang/Object;Ljava/util/function/BiFunction;)Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Ljava/lang/invoke/MutableCallSite;",
            "[",
            "Ljava/lang/Object;",
            "Ljava/util/function/BiFunction<",
            "-",
            "Lorg/codehaus/groovy/vmplugin/v8/CacheableCallSite;",
            "-",
            "Ljava/lang/Object;",
            "+TT;>;)TT;"
        }
    .end annotation

    .line 361
    instance-of v0, p0, Lorg/codehaus/groovy/vmplugin/v8/CacheableCallSite;

    if-eqz v0, :cond_1

    .line 362
    check-cast p0, Lorg/codehaus/groovy/vmplugin/v8/CacheableCallSite;

    const/4 v0, 0x0

    .line 363
    aget-object p1, p1, v0

    if-nez p1, :cond_0

    .line 365
    invoke-static {}, Lorg/codehaus/groovy/runtime/NullObject;->getNullObject()Lorg/codehaus/groovy/runtime/NullObject;

    move-result-object p1

    .line 367
    :cond_0
    invoke-interface {p2, p0, p1}, Ljava/util/function/BiFunction;->apply(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    return-object p0

    .line 370
    :cond_1
    new-instance p1, Lorg/codehaus/groovy/GroovyBugError;

    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "CacheableCallSite is expected, but the actual callsite is: "

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    invoke-direct {p1, p0}, Lorg/codehaus/groovy/GroovyBugError;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method private static fallback(Ljava/lang/invoke/MutableCallSite;Ljava/lang/Class;Ljava/lang/String;ILjava/lang/Boolean;Ljava/lang/Boolean;Ljava/lang/Boolean;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/vmplugin/v8/MethodHandleWrapper;
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/invoke/MutableCallSite;",
            "Ljava/lang/Class<",
            "*>;",
            "Ljava/lang/String;",
            "I",
            "Ljava/lang/Boolean;",
            "Ljava/lang/Boolean;",
            "Ljava/lang/Boolean;",
            "Ljava/lang/Object;",
            "[",
            "Ljava/lang/Object;",
            ")",
            "Lorg/codehaus/groovy/vmplugin/v8/MethodHandleWrapper;"
        }
    .end annotation

    .line 350
    invoke-virtual {p4}, Ljava/lang/Boolean;->booleanValue()Z

    move-result v4

    invoke-virtual {p5}, Ljava/lang/Boolean;->booleanValue()Z

    move-result v5

    invoke-virtual {p6}, Ljava/lang/Boolean;->booleanValue()Z

    move-result v6

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move v3, p3

    move-object/from16 v7, p8

    invoke-static/range {v0 .. v7}, Lorg/codehaus/groovy/vmplugin/v8/Selector;->getSelector(Ljava/lang/invoke/MutableCallSite;Ljava/lang/Class;Ljava/lang/String;IZZZ[Ljava/lang/Object;)Lorg/codehaus/groovy/vmplugin/v8/Selector;

    move-result-object v0

    .line 351
    invoke-virtual {v0}, Lorg/codehaus/groovy/vmplugin/v8/Selector;->setCallSiteTarget()V

    .line 353
    new-instance v1, Lorg/codehaus/groovy/vmplugin/v8/MethodHandleWrapper;

    iget-object v2, v0, Lorg/codehaus/groovy/vmplugin/v8/Selector;->handle:Ljava/lang/invoke/MethodHandle;

    const-class v3, [Ljava/lang/Object;

    move-object/from16 v4, p8

    array-length v4, v4

    .line 354
    invoke-virtual {v2, v3, v4}, Ljava/lang/invoke/MethodHandle;->asSpreader(Ljava/lang/Class;I)Ljava/lang/invoke/MethodHandle;

    move-result-object v2

    const-class v3, Ljava/lang/Object;

    const-class v4, [Ljava/lang/Object;

    invoke-static {v3, v4}, Ljava/lang/invoke/MethodType;->methodType(Ljava/lang/Class;Ljava/lang/Class;)Ljava/lang/invoke/MethodType;

    move-result-object v3

    invoke-virtual {v2, v3}, Ljava/lang/invoke/MethodHandle;->asType(Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;

    move-result-object v2

    iget-object v3, v0, Lorg/codehaus/groovy/vmplugin/v8/Selector;->handle:Ljava/lang/invoke/MethodHandle;

    iget-boolean v0, v0, Lorg/codehaus/groovy/vmplugin/v8/Selector;->cache:Z

    invoke-direct {v1, v2, v3, v0}, Lorg/codehaus/groovy/vmplugin/v8/MethodHandleWrapper;-><init>(Ljava/lang/invoke/MethodHandle;Ljava/lang/invoke/MethodHandle;Z)V

    return-object v1
.end method

.method public static fromCache(Ljava/lang/invoke/MutableCallSite;Ljava/lang/Class;Ljava/lang/String;ILjava/lang/Boolean;Ljava/lang/Boolean;Ljava/lang/Boolean;Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;
    .locals 13
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/invoke/MutableCallSite;",
            "Ljava/lang/Class<",
            "*>;",
            "Ljava/lang/String;",
            "I",
            "Ljava/lang/Boolean;",
            "Ljava/lang/Boolean;",
            "Ljava/lang/Boolean;",
            "Ljava/lang/Object;",
            "[",
            "Ljava/lang/Object;",
            ")",
            "Ljava/lang/Object;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Throwable;
        }
    .end annotation

    move-object v10, p0

    move-object/from16 v11, p8

    .line 292
    new-instance v12, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface$FallbackSupplier;

    move-object v0, v12

    move-object v1, p0

    move-object v2, p1

    move-object v3, p2

    move/from16 v4, p3

    move-object/from16 v5, p4

    move-object/from16 v6, p5

    move-object/from16 v7, p6

    move-object/from16 v8, p7

    move-object/from16 v9, p8

    invoke-direct/range {v0 .. v9}, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface$FallbackSupplier;-><init>(Ljava/lang/invoke/MutableCallSite;Ljava/lang/Class;Ljava/lang/String;ILjava/lang/Boolean;Ljava/lang/Boolean;Ljava/lang/Boolean;Ljava/lang/Object;[Ljava/lang/Object;)V

    .line 294
    new-instance v0, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface$$ExternalSyntheticLambda1;

    invoke-direct {v0, v12}, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface$$ExternalSyntheticLambda1;-><init>(Lorg/codehaus/groovy/vmplugin/v8/IndyInterface$FallbackSupplier;)V

    .line 295
    invoke-static {p0, v11, v0}, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->doWithCallSite(Ljava/lang/invoke/MutableCallSite;[Ljava/lang/Object;Ljava/util/function/BiFunction;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/vmplugin/v8/MethodHandleWrapper;

    .line 307
    sget-object v1, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->NULL_METHOD_HANDLE_WRAPPER:Lorg/codehaus/groovy/vmplugin/v8/MethodHandleWrapper;

    if-ne v1, v0, :cond_0

    .line 308
    invoke-virtual {v12}, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface$FallbackSupplier;->get()Lorg/codehaus/groovy/vmplugin/v8/MethodHandleWrapper;

    move-result-object v0

    .line 311
    :cond_0
    invoke-virtual {v0}, Lorg/codehaus/groovy/vmplugin/v8/MethodHandleWrapper;->isCanSetTarget()Z

    move-result v1

    if-eqz v1, :cond_2

    invoke-virtual {p0}, Ljava/lang/invoke/MutableCallSite;->getTarget()Ljava/lang/invoke/MethodHandle;

    move-result-object v1

    invoke-virtual {v0}, Lorg/codehaus/groovy/vmplugin/v8/MethodHandleWrapper;->getTargetMethodHandle()Ljava/lang/invoke/MethodHandle;

    move-result-object v2

    if-eq v1, v2, :cond_2

    invoke-virtual {v0}, Lorg/codehaus/groovy/vmplugin/v8/MethodHandleWrapper;->getLatestHitCount()J

    move-result-wide v1

    sget-wide v3, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->INDY_OPTIMIZE_THRESHOLD:J

    cmp-long v1, v1, v3

    if-lez v1, :cond_2

    .line 312
    invoke-virtual {v0}, Lorg/codehaus/groovy/vmplugin/v8/MethodHandleWrapper;->getTargetMethodHandle()Ljava/lang/invoke/MethodHandle;

    move-result-object v1

    invoke-virtual {p0, v1}, Ljava/lang/invoke/MutableCallSite;->setTarget(Ljava/lang/invoke/MethodHandle;)V

    .line 313
    sget-boolean v1, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->LOG_ENABLED:Z

    if-eqz v1, :cond_1

    sget-object v1, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->LOG:Ljava/util/logging/Logger;

    const-string v2, "call site target set, preparing outside invocation"

    invoke-virtual {v1, v2}, Ljava/util/logging/Logger;->info(Ljava/lang/String;)V

    .line 315
    :cond_1
    invoke-virtual {v0}, Lorg/codehaus/groovy/vmplugin/v8/MethodHandleWrapper;->resetLatestHitCount()V

    .line 318
    :cond_2
    invoke-virtual {v0}, Lorg/codehaus/groovy/vmplugin/v8/MethodHandleWrapper;->getCachedMethodHandle()Ljava/lang/invoke/MethodHandle;

    move-result-object v0

    invoke-polymorphic {v0, v11}, Ljava/lang/invoke/MethodHandle;->invokeExact([Ljava/lang/Object;)Ljava/lang/Object;, ([Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    return-object v0
.end method

.method protected static invalidateSwitchPoints()V
    .locals 4

    .line 179
    sget-boolean v0, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->LOG_ENABLED:Z

    if-eqz v0, :cond_0

    .line 180
    sget-object v0, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->LOG:Ljava/util/logging/Logger;

    const-string v1, "invalidating switch point"

    invoke-virtual {v0, v1}, Ljava/util/logging/Logger;->info(Ljava/lang/String;)V

    .line 183
    :cond_0
    const-class v0, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;

    monitor-enter v0

    .line 184
    :try_start_0
    sget-object v1, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->switchPoint:Ljava/lang/invoke/SwitchPoint;

    .line 185
    new-instance v2, Ljava/lang/invoke/SwitchPoint;

    invoke-direct {v2}, Ljava/lang/invoke/SwitchPoint;-><init>()V

    sput-object v2, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->switchPoint:Ljava/lang/invoke/SwitchPoint;

    const/4 v2, 0x1

    new-array v2, v2, [Ljava/lang/invoke/SwitchPoint;

    const/4 v3, 0x0

    aput-object v1, v2, v3

    .line 186
    invoke-static {v2}, Ljava/lang/invoke/SwitchPoint;->invalidateAll([Ljava/lang/invoke/SwitchPoint;)V

    .line 187
    monitor-exit v0

    return-void

    :catchall_0
    move-exception v1

    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw v1
.end method

.method static synthetic lambda$fromCache$1(Lorg/codehaus/groovy/vmplugin/v8/IndyInterface$FallbackSupplier;Ljava/lang/String;)Lorg/codehaus/groovy/vmplugin/v8/MethodHandleWrapper;
    .locals 0

    .line 301
    invoke-virtual {p0}, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface$FallbackSupplier;->get()Lorg/codehaus/groovy/vmplugin/v8/MethodHandleWrapper;

    move-result-object p0

    .line 302
    invoke-virtual {p0}, Lorg/codehaus/groovy/vmplugin/v8/MethodHandleWrapper;->isCanSetTarget()Z

    move-result p1

    if-eqz p1, :cond_0

    goto :goto_0

    :cond_0
    sget-object p0, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->NULL_METHOD_HANDLE_WRAPPER:Lorg/codehaus/groovy/vmplugin/v8/MethodHandleWrapper;

    :goto_0
    return-object p0
.end method

.method static synthetic lambda$fromCache$2(Lorg/codehaus/groovy/vmplugin/v8/IndyInterface$FallbackSupplier;Lorg/codehaus/groovy/vmplugin/v8/CacheableCallSite;Ljava/lang/Object;)Lorg/codehaus/groovy/vmplugin/v8/MethodHandleWrapper;
    .locals 1

    .line 299
    invoke-virtual {p2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object p2

    new-instance v0, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface$$ExternalSyntheticLambda3;

    invoke-direct {v0, p0}, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface$$ExternalSyntheticLambda3;-><init>(Lorg/codehaus/groovy/vmplugin/v8/IndyInterface$FallbackSupplier;)V

    .line 298
    invoke-virtual {p1, p2, v0}, Lorg/codehaus/groovy/vmplugin/v8/CacheableCallSite;->getAndPut(Ljava/lang/String;Lorg/codehaus/groovy/runtime/memoize/MemoizeCache$ValueProvider;)Lorg/codehaus/groovy/vmplugin/v8/MethodHandleWrapper;

    move-result-object p0

    return-object p0
.end method

.method static synthetic lambda$selectMethod$3(Lorg/codehaus/groovy/vmplugin/v8/MethodHandleWrapper;Lorg/codehaus/groovy/vmplugin/v8/CacheableCallSite;Ljava/lang/Object;)Lorg/codehaus/groovy/vmplugin/v8/MethodHandleWrapper;
    .locals 0

    .line 342
    invoke-virtual {p2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p1, p2, p0}, Lorg/codehaus/groovy/vmplugin/v8/CacheableCallSite;->put(Ljava/lang/String;Lorg/codehaus/groovy/vmplugin/v8/MethodHandleWrapper;)Lorg/codehaus/groovy/vmplugin/v8/MethodHandleWrapper;

    move-result-object p0

    return-object p0
.end method

.method static synthetic lambda$static$0(Lgroovy/lang/MetaClassRegistryChangeEvent;)V
    .locals 0

    .line 172
    invoke-static {}, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->invalidateSwitchPoints()V

    return-void
.end method

.method private static make(Ljava/lang/invoke/MutableCallSite;Ljava/lang/Class;Ljava/lang/String;ILjava/lang/invoke/MethodType;ZZZLjava/lang/invoke/MethodHandle;)Ljava/lang/invoke/MethodHandle;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/invoke/MutableCallSite;",
            "Ljava/lang/Class<",
            "*>;",
            "Ljava/lang/String;",
            "I",
            "Ljava/lang/invoke/MethodType;",
            "ZZZ",
            "Ljava/lang/invoke/MethodHandle;",
            ")",
            "Ljava/lang/invoke/MethodHandle;"
        }
    .end annotation

    const/16 v0, 0x8

    new-array v0, v0, [Ljava/lang/Object;

    const/4 v1, 0x0

    aput-object p0, v0, v1

    const/4 p0, 0x1

    aput-object p1, v0, p0

    const/4 p1, 0x2

    aput-object p2, v0, p1

    .line 251
    invoke-static {p3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    const/4 p2, 0x3

    aput-object p1, v0, p2

    invoke-static {p5}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object p1

    const/4 p2, 0x4

    aput-object p1, v0, p2

    invoke-static {p6}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object p1

    const/4 p2, 0x5

    aput-object p1, v0, p2

    invoke-static {p7}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object p1

    const/4 p2, 0x6

    aput-object p1, v0, p2

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    const/4 p1, 0x7

    aput-object p0, v0, p1

    invoke-static {p8, v1, v0}, Ljava/lang/invoke/MethodHandles;->insertArguments(Ljava/lang/invoke/MethodHandle;I[Ljava/lang/Object;)Ljava/lang/invoke/MethodHandle;

    move-result-object p0

    .line 252
    const-class p1, [Ljava/lang/Object;

    invoke-virtual {p4}, Ljava/lang/invoke/MethodType;->parameterCount()I

    move-result p2

    invoke-virtual {p0, p1, p2}, Ljava/lang/invoke/MethodHandle;->asCollector(Ljava/lang/Class;I)Ljava/lang/invoke/MethodHandle;

    move-result-object p0

    invoke-virtual {p0, p4}, Ljava/lang/invoke/MethodHandle;->asType(Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;

    move-result-object p0

    return-object p0
.end method

.method private static makeAdapter(Ljava/lang/invoke/MutableCallSite;Ljava/lang/Class;Ljava/lang/String;ILjava/lang/invoke/MethodType;ZZZ)Ljava/lang/invoke/MethodHandle;
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/invoke/MutableCallSite;",
            "Ljava/lang/Class<",
            "*>;",
            "Ljava/lang/String;",
            "I",
            "Ljava/lang/invoke/MethodType;",
            "ZZZ)",
            "Ljava/lang/invoke/MethodHandle;"
        }
    .end annotation

    .line 247
    sget-object v8, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->FROM_CACHE_METHOD:Ljava/lang/invoke/MethodHandle;

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move v3, p3

    move-object v4, p4

    move v5, p5

    move v6, p6

    move/from16 v7, p7

    invoke-static/range {v0 .. v8}, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->make(Ljava/lang/invoke/MutableCallSite;Ljava/lang/Class;Ljava/lang/String;ILjava/lang/invoke/MethodType;ZZZLjava/lang/invoke/MethodHandle;)Ljava/lang/invoke/MethodHandle;

    move-result-object v0

    return-object v0
.end method

.method protected static makeFallBack(Ljava/lang/invoke/MutableCallSite;Ljava/lang/Class;Ljava/lang/String;ILjava/lang/invoke/MethodType;ZZZ)Ljava/lang/invoke/MethodHandle;
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/invoke/MutableCallSite;",
            "Ljava/lang/Class<",
            "*>;",
            "Ljava/lang/String;",
            "I",
            "Ljava/lang/invoke/MethodType;",
            "ZZZ)",
            "Ljava/lang/invoke/MethodHandle;"
        }
    .end annotation

    .line 240
    sget-object v8, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->SELECT_METHOD:Ljava/lang/invoke/MethodHandle;

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move v3, p3

    move-object v4, p4

    move v5, p5

    move v6, p6

    move/from16 v7, p7

    invoke-static/range {v0 .. v8}, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->make(Ljava/lang/invoke/MutableCallSite;Ljava/lang/Class;Ljava/lang/String;ILjava/lang/invoke/MethodType;ZZZLjava/lang/invoke/MethodHandle;)Ljava/lang/invoke/MethodHandle;

    move-result-object v0

    return-object v0
.end method

.method private static realBootstrap(Ljava/lang/invoke/MethodHandles$Lookup;Ljava/lang/String;ILjava/lang/invoke/MethodType;ZZZ)Ljava/lang/invoke/CallSite;
    .locals 9

    .line 226
    new-instance v8, Lorg/codehaus/groovy/vmplugin/v8/CacheableCallSite;

    invoke-direct {v8, p3}, Lorg/codehaus/groovy/vmplugin/v8/CacheableCallSite;-><init>(Ljava/lang/invoke/MethodType;)V

    .line 227
    invoke-virtual {p0}, Ljava/lang/invoke/MethodHandles$Lookup;->lookupClass()Ljava/lang/Class;

    move-result-object p0

    move-object v0, v8

    move-object v1, p0

    move-object v2, p1

    move v3, p2

    move-object v4, p3

    move v5, p4

    move v6, p5

    move v7, p6

    .line 228
    invoke-static/range {v0 .. v7}, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->makeAdapter(Ljava/lang/invoke/MutableCallSite;Ljava/lang/Class;Ljava/lang/String;ILjava/lang/invoke/MethodType;ZZZ)Ljava/lang/invoke/MethodHandle;

    move-result-object v0

    .line 229
    invoke-virtual {v8, v0}, Lorg/codehaus/groovy/vmplugin/v8/CacheableCallSite;->setTarget(Ljava/lang/invoke/MethodHandle;)V

    .line 230
    invoke-virtual {v8, v0}, Lorg/codehaus/groovy/vmplugin/v8/CacheableCallSite;->setDefaultTarget(Ljava/lang/invoke/MethodHandle;)V

    move-object v0, v8

    .line 231
    invoke-static/range {v0 .. v7}, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->makeFallBack(Ljava/lang/invoke/MutableCallSite;Ljava/lang/Class;Ljava/lang/String;ILjava/lang/invoke/MethodType;ZZZ)Ljava/lang/invoke/MethodHandle;

    move-result-object p0

    invoke-virtual {v8, p0}, Lorg/codehaus/groovy/vmplugin/v8/CacheableCallSite;->setFallbackTarget(Ljava/lang/invoke/MethodHandle;)V

    return-object v8
.end method

.method public static selectMethod(Ljava/lang/invoke/MutableCallSite;Ljava/lang/Class;Ljava/lang/String;ILjava/lang/Boolean;Ljava/lang/Boolean;Ljava/lang/Boolean;Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/invoke/MutableCallSite;",
            "Ljava/lang/Class<",
            "*>;",
            "Ljava/lang/String;",
            "I",
            "Ljava/lang/Boolean;",
            "Ljava/lang/Boolean;",
            "Ljava/lang/Boolean;",
            "Ljava/lang/Object;",
            "[",
            "Ljava/lang/Object;",
            ")",
            "Ljava/lang/Object;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Throwable;
        }
    .end annotation

    .line 325
    invoke-static/range {p0 .. p8}, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->fallback(Ljava/lang/invoke/MutableCallSite;Ljava/lang/Class;Ljava/lang/String;ILjava/lang/Boolean;Ljava/lang/Boolean;Ljava/lang/Boolean;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/vmplugin/v8/MethodHandleWrapper;

    move-result-object p1

    .line 327
    instance-of p2, p0, Lorg/codehaus/groovy/vmplugin/v8/CacheableCallSite;

    if-eqz p2, :cond_2

    .line 328
    move-object p2, p0

    check-cast p2, Lorg/codehaus/groovy/vmplugin/v8/CacheableCallSite;

    .line 330
    invoke-virtual {p2}, Lorg/codehaus/groovy/vmplugin/v8/CacheableCallSite;->getDefaultTarget()Ljava/lang/invoke/MethodHandle;

    move-result-object p3

    .line 331
    invoke-virtual {p2}, Lorg/codehaus/groovy/vmplugin/v8/CacheableCallSite;->incrementFallbackCount()J

    move-result-wide p4

    .line 332
    sget-wide p6, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->INDY_FALLBACK_THRESHOLD:J

    cmp-long p4, p4, p6

    if-lez p4, :cond_1

    invoke-virtual {p2}, Lorg/codehaus/groovy/vmplugin/v8/CacheableCallSite;->getTarget()Ljava/lang/invoke/MethodHandle;

    move-result-object p4

    if-eq p4, p3, :cond_1

    .line 333
    invoke-virtual {p2, p3}, Lorg/codehaus/groovy/vmplugin/v8/CacheableCallSite;->setTarget(Ljava/lang/invoke/MethodHandle;)V

    .line 334
    sget-boolean p4, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->LOG_ENABLED:Z

    if-eqz p4, :cond_0

    sget-object p4, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->LOG:Ljava/util/logging/Logger;

    const-string p5, "call site target reset to default, preparing outside invocation"

    invoke-virtual {p4, p5}, Ljava/util/logging/Logger;->info(Ljava/lang/String;)V

    .line 336
    :cond_0
    invoke-virtual {p2}, Lorg/codehaus/groovy/vmplugin/v8/CacheableCallSite;->resetFallbackCount()V

    .line 339
    :cond_1
    invoke-virtual {p2}, Lorg/codehaus/groovy/vmplugin/v8/CacheableCallSite;->getTarget()Ljava/lang/invoke/MethodHandle;

    move-result-object p2

    if-ne p3, p2, :cond_2

    .line 342
    new-instance p2, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface$$ExternalSyntheticLambda2;

    invoke-direct {p2, p1}, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface$$ExternalSyntheticLambda2;-><init>(Lorg/codehaus/groovy/vmplugin/v8/MethodHandleWrapper;)V

    invoke-static {p0, p8, p2}, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->doWithCallSite(Ljava/lang/invoke/MutableCallSite;[Ljava/lang/Object;Ljava/util/function/BiFunction;)Ljava/lang/Object;

    .line 346
    :cond_2
    invoke-virtual {p1}, Lorg/codehaus/groovy/vmplugin/v8/MethodHandleWrapper;->getCachedMethodHandle()Ljava/lang/invoke/MethodHandle;

    move-result-object p0

    invoke-polymorphic {p0, p8}, Ljava/lang/invoke/MethodHandle;->invokeExact([Ljava/lang/Object;)Ljava/lang/Object;, ([Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    return-object p0
.end method

.method public static staticArrayAccess(Ljava/lang/invoke/MethodHandles$Lookup;Ljava/lang/String;Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/CallSite;
    .locals 0

    .line 377
    invoke-virtual {p2}, Ljava/lang/invoke/MethodType;->parameterCount()I

    move-result p0

    const/4 p1, 0x2

    if-ne p0, p1, :cond_0

    .line 378
    new-instance p0, Ljava/lang/invoke/ConstantCallSite;

    invoke-static {p2}, Lorg/codehaus/groovy/vmplugin/v8/IndyArrayAccess;->arrayGet(Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;

    move-result-object p1

    invoke-direct {p0, p1}, Ljava/lang/invoke/ConstantCallSite;-><init>(Ljava/lang/invoke/MethodHandle;)V

    return-object p0

    .line 380
    :cond_0
    new-instance p0, Ljava/lang/invoke/ConstantCallSite;

    invoke-static {p2}, Lorg/codehaus/groovy/vmplugin/v8/IndyArrayAccess;->arraySet(Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;

    move-result-object p1

    invoke-direct {p0, p1}, Ljava/lang/invoke/ConstantCallSite;-><init>(Ljava/lang/invoke/MethodHandle;)V

    return-object p0
.end method
