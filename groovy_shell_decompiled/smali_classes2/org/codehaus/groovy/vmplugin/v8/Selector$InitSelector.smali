.class Lorg/codehaus/groovy/vmplugin/v8/Selector$InitSelector;
.super Lorg/codehaus/groovy/vmplugin/v8/Selector$MethodSelector;
.source "Selector.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/codehaus/groovy/vmplugin/v8/Selector;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0xa
    name = "InitSelector"
.end annotation


# instance fields
.field private beanConstructor:Z


# direct methods
.method public constructor <init>(Ljava/lang/invoke/MutableCallSite;Ljava/lang/Class;Ljava/lang/String;Lorg/codehaus/groovy/vmplugin/v8/IndyInterface$CallType;ZZZ[Ljava/lang/Object;)V
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/invoke/MutableCallSite;",
            "Ljava/lang/Class<",
            "*>;",
            "Ljava/lang/String;",
            "Lorg/codehaus/groovy/vmplugin/v8/IndyInterface$CallType;",
            "ZZZ[",
            "Ljava/lang/Object;",
            ")V"
        }
    .end annotation

    .line 385
    invoke-static {p5}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v5

    invoke-static {p6}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v6

    invoke-static/range {p7 .. p7}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v7

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    move-object/from16 v8, p8

    invoke-direct/range {v0 .. v8}, Lorg/codehaus/groovy/vmplugin/v8/Selector$MethodSelector;-><init>(Ljava/lang/invoke/MutableCallSite;Ljava/lang/Class;Ljava/lang/String;Lorg/codehaus/groovy/vmplugin/v8/IndyInterface$CallType;Ljava/lang/Boolean;Ljava/lang/Boolean;Ljava/lang/Boolean;[Ljava/lang/Object;)V

    return-void
.end method


# virtual methods
.method public chooseMeta(Lgroovy/lang/MetaClassImpl;)V
    .locals 2

    if-nez p1, :cond_0

    return-void

    .line 412
    :cond_0
    sget-boolean v0, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->LOG_ENABLED:Z

    if-eqz v0, :cond_1

    sget-object v0, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->LOG:Ljava/util/logging/Logger;

    const-string v1, "getting constructor"

    invoke-virtual {v0, v1}, Ljava/util/logging/Logger;->info(Ljava/lang/String;)V

    .line 413
    :cond_1
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$InitSelector;->args:[Ljava/lang/Object;

    invoke-static {v0}, Lorg/codehaus/groovy/vmplugin/v8/Selector;->access$000([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object v0

    .line 414
    invoke-virtual {p1, v0}, Lgroovy/lang/MetaClassImpl;->retrieveConstructor([Ljava/lang/Object;)Lgroovy/lang/MetaMethod;

    move-result-object p1

    iput-object p1, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$InitSelector;->method:Lgroovy/lang/MetaMethod;

    .line 415
    iget-object p1, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$InitSelector;->method:Lgroovy/lang/MetaMethod;

    instance-of p1, p1, Lgroovy/lang/MetaClassImpl$MetaConstructor;

    if-eqz p1, :cond_3

    .line 416
    iget-object p1, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$InitSelector;->method:Lgroovy/lang/MetaMethod;

    check-cast p1, Lgroovy/lang/MetaClassImpl$MetaConstructor;

    .line 417
    invoke-virtual {p1}, Lgroovy/lang/MetaClassImpl$MetaConstructor;->isBeanConstructor()Z

    move-result p1

    if-eqz p1, :cond_3

    .line 418
    sget-boolean p1, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->LOG_ENABLED:Z

    if-eqz p1, :cond_2

    sget-object p1, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->LOG:Ljava/util/logging/Logger;

    const-string v0, "do beans constructor"

    invoke-virtual {p1, v0}, Ljava/util/logging/Logger;->info(Ljava/lang/String;)V

    :cond_2
    const/4 p1, 0x1

    .line 419
    iput-boolean p1, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$InitSelector;->beanConstructor:Z

    :cond_3
    return-void
.end method

.method public correctCoerce()V
    .locals 1

    .line 480
    iget-boolean v0, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$InitSelector;->beanConstructor:Z

    if-eqz v0, :cond_0

    return-void

    .line 481
    :cond_0
    invoke-super {p0}, Lorg/codehaus/groovy/vmplugin/v8/Selector$MethodSelector;->correctCoerce()V

    return-void
.end method

.method public correctParameterLength()V
    .locals 1

    .line 470
    iget-boolean v0, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$InitSelector;->beanConstructor:Z

    if-eqz v0, :cond_0

    return-void

    .line 471
    :cond_0
    invoke-super {p0}, Lorg/codehaus/groovy/vmplugin/v8/Selector$MethodSelector;->correctParameterLength()V

    return-void
.end method

.method public getMetaClass()Lgroovy/lang/MetaClass;
    .locals 3

    .line 401
    invoke-static {}, Lgroovy/lang/GroovySystem;->getMetaClassRegistry()Lgroovy/lang/MetaClassRegistry;

    move-result-object v0

    iget-object v1, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$InitSelector;->args:[Ljava/lang/Object;

    const/4 v2, 0x0

    aget-object v1, v1, v2

    check-cast v1, Ljava/lang/Class;

    invoke-interface {v0, v1}, Lgroovy/lang/MetaClassRegistry;->getMetaClass(Ljava/lang/Class;)Lgroovy/lang/MetaClass;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$InitSelector;->mc:Lgroovy/lang/MetaClass;

    .line 402
    sget-boolean v0, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->LOG_ENABLED:Z

    if-eqz v0, :cond_0

    sget-object v0, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->LOG:Ljava/util/logging/Logger;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "meta class is "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    iget-object v2, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$InitSelector;->mc:Lgroovy/lang/MetaClass;

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/util/logging/Logger;->info(Ljava/lang/String;)V

    .line 403
    :cond_0
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$InitSelector;->mc:Lgroovy/lang/MetaClass;

    return-object v0
.end method

.method public setHandleForMetaMethod()V
    .locals 6

    .line 429
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$InitSelector;->method:Lgroovy/lang/MetaMethod;

    if-nez v0, :cond_0

    return-void

    .line 430
    :cond_0
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$InitSelector;->method:Lgroovy/lang/MetaMethod;

    instance-of v0, v0, Lgroovy/lang/MetaClassImpl$MetaConstructor;

    if-eqz v0, :cond_2

    .line 431
    sget-boolean v0, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->LOG_ENABLED:Z

    if-eqz v0, :cond_1

    sget-object v0, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->LOG:Ljava/util/logging/Logger;

    const-string v1, "meta method is MetaConstructor instance"

    invoke-virtual {v0, v1}, Ljava/util/logging/Logger;->info(Ljava/lang/String;)V

    .line 432
    :cond_1
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$InitSelector;->method:Lgroovy/lang/MetaMethod;

    check-cast v0, Lgroovy/lang/MetaClassImpl$MetaConstructor;

    .line 433
    invoke-virtual {v0}, Lgroovy/lang/MetaClassImpl$MetaConstructor;->isVargsMethod()Z

    move-result v1

    iput-boolean v1, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$InitSelector;->isVargs:Z

    .line 434
    invoke-virtual {v0}, Lgroovy/lang/MetaClassImpl$MetaConstructor;->getCachedConstrcutor()Lorg/codehaus/groovy/reflection/CachedConstructor;

    move-result-object v0

    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/CachedConstructor;->getCachedConstructor()Ljava/lang/reflect/Constructor;

    move-result-object v0

    .line 436
    :try_start_0
    sget-object v1, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->LOOKUP:Ljava/lang/invoke/MethodHandles$Lookup;

    invoke-virtual {v1, v0}, Ljava/lang/invoke/MethodHandles$Lookup;->unreflectConstructor(Ljava/lang/reflect/Constructor;)Ljava/lang/invoke/MethodHandle;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$InitSelector;->handle:Ljava/lang/invoke/MethodHandle;

    .line 437
    sget-boolean v0, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->LOG_ENABLED:Z

    if-eqz v0, :cond_3

    sget-object v0, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->LOG:Ljava/util/logging/Logger;

    const-string v1, "successfully unreflected constructor"

    invoke-virtual {v0, v1}, Ljava/util/logging/Logger;->info(Ljava/lang/String;)V
    :try_end_0
    .catch Ljava/lang/IllegalAccessException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception v0

    .line 439
    new-instance v1, Lorg/codehaus/groovy/GroovyBugError;

    invoke-direct {v1, v0}, Lorg/codehaus/groovy/GroovyBugError;-><init>(Ljava/lang/Exception;)V

    throw v1

    .line 442
    :cond_2
    invoke-super {p0}, Lorg/codehaus/groovy/vmplugin/v8/Selector$MethodSelector;->setHandleForMetaMethod()V

    .line 444
    :cond_3
    :goto_0
    iget-boolean v0, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$InitSelector;->beanConstructor:Z

    const/4 v1, 0x0

    const/4 v2, 0x1

    if-eqz v0, :cond_5

    .line 450
    sget-object v0, Lorg/codehaus/groovy/vmplugin/v8/IndyGuardsFiltersAndSignatures;->BEAN_CONSTRUCTOR_PROPERTY_SETTER:Ljava/lang/invoke/MethodHandle;

    iget-object v3, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$InitSelector;->mc:Lgroovy/lang/MetaClass;

    invoke-virtual {v0, v3}, Ljava/lang/invoke/MethodHandle;->bindTo(Ljava/lang/Object;)Ljava/lang/invoke/MethodHandle;

    move-result-object v0

    .line 452
    const-class v3, Ljava/lang/Object;

    invoke-static {v3}, Ljava/lang/invoke/MethodType;->methodType(Ljava/lang/Class;)Ljava/lang/invoke/MethodType;

    move-result-object v3

    .line 453
    iget-object v4, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$InitSelector;->args:[Ljava/lang/Object;

    array-length v4, v4

    const/4 v5, 0x3

    if-ne v4, v5, :cond_4

    new-array v4, v2, [Ljava/lang/Class;

    .line 454
    iget-object v5, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$InitSelector;->targetType:Ljava/lang/invoke/MethodType;

    invoke-virtual {v5, v2}, Ljava/lang/invoke/MethodType;->parameterType(I)Ljava/lang/Class;

    move-result-object v5

    aput-object v5, v4, v1

    invoke-static {v0, v2, v4}, Ljava/lang/invoke/MethodHandles;->dropArguments(Ljava/lang/invoke/MethodHandle;I[Ljava/lang/Class;)Ljava/lang/invoke/MethodHandle;

    move-result-object v0

    new-array v4, v2, [Ljava/lang/Class;

    .line 455
    iget-object v5, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$InitSelector;->targetType:Ljava/lang/invoke/MethodType;

    invoke-virtual {v5, v2}, Ljava/lang/invoke/MethodType;->parameterType(I)Ljava/lang/Class;

    move-result-object v5

    aput-object v5, v4, v1

    invoke-virtual {v3, v1, v4}, Ljava/lang/invoke/MethodType;->insertParameterTypes(I[Ljava/lang/Class;)Ljava/lang/invoke/MethodType;

    move-result-object v3

    .line 457
    :cond_4
    iget-object v4, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$InitSelector;->handle:Ljava/lang/invoke/MethodHandle;

    invoke-virtual {v4, v3}, Ljava/lang/invoke/MethodHandle;->asType(Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;

    move-result-object v3

    invoke-static {v0, v3}, Ljava/lang/invoke/MethodHandles;->foldArguments(Ljava/lang/invoke/MethodHandle;Ljava/lang/invoke/MethodHandle;)Ljava/lang/invoke/MethodHandle;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$InitSelector;->handle:Ljava/lang/invoke/MethodHandle;

    .line 459
    :cond_5
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$InitSelector;->method:Lgroovy/lang/MetaMethod;

    instance-of v0, v0, Lgroovy/lang/MetaClassImpl$MetaConstructor;

    if-eqz v0, :cond_6

    .line 460
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$InitSelector;->handle:Ljava/lang/invoke/MethodHandle;

    new-array v2, v2, [Ljava/lang/Class;

    const-class v3, Ljava/lang/Class;

    aput-object v3, v2, v1

    invoke-static {v0, v1, v2}, Ljava/lang/invoke/MethodHandles;->dropArguments(Ljava/lang/invoke/MethodHandle;I[Ljava/lang/Class;)Ljava/lang/invoke/MethodHandle;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$InitSelector;->handle:Ljava/lang/invoke/MethodHandle;

    :cond_6
    return-void
.end method

.method public setInterceptor()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public setMetaClassCallHandleIfNeeded(Z)V
    .locals 3

    .line 489
    iget-object p1, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$InitSelector;->handle:Ljava/lang/invoke/MethodHandle;

    if-eqz p1, :cond_0

    return-void

    :cond_0
    const/4 p1, 0x1

    .line 490
    iput-boolean p1, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$InitSelector;->useMetaClass:Z

    .line 491
    sget-boolean v0, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->LOG_ENABLED:Z

    if-eqz v0, :cond_1

    sget-object v0, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->LOG:Ljava/util/logging/Logger;

    const-string v1, "set meta class invocation path"

    invoke-virtual {v0, v1}, Ljava/util/logging/Logger;->info(Ljava/lang/String;)V

    .line 492
    :cond_1
    sget-object v0, Lorg/codehaus/groovy/vmplugin/v8/IndyGuardsFiltersAndSignatures;->MOP_INVOKE_CONSTRUCTOR:Ljava/lang/invoke/MethodHandle;

    iget-object v1, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$InitSelector;->mc:Lgroovy/lang/MetaClass;

    invoke-virtual {v0, v1}, Ljava/lang/invoke/MethodHandle;->bindTo(Ljava/lang/Object;)Ljava/lang/invoke/MethodHandle;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$InitSelector;->handle:Ljava/lang/invoke/MethodHandle;

    .line 493
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$InitSelector;->handle:Ljava/lang/invoke/MethodHandle;

    const-class v1, [Ljava/lang/Object;

    iget-object v2, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$InitSelector;->targetType:Ljava/lang/invoke/MethodType;

    invoke-virtual {v2}, Ljava/lang/invoke/MethodType;->parameterCount()I

    move-result v2

    sub-int/2addr v2, p1

    invoke-virtual {v0, v1, v2}, Ljava/lang/invoke/MethodHandle;->asCollector(Ljava/lang/Class;I)Ljava/lang/invoke/MethodHandle;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$InitSelector;->handle:Ljava/lang/invoke/MethodHandle;

    .line 494
    iget-object v0, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$InitSelector;->handle:Ljava/lang/invoke/MethodHandle;

    new-array p1, p1, [Ljava/lang/Class;

    const-class v1, Ljava/lang/Class;

    const/4 v2, 0x0

    aput-object v1, p1, v2

    invoke-static {v0, v2, p1}, Ljava/lang/invoke/MethodHandles;->dropArguments(Ljava/lang/invoke/MethodHandle;I[Ljava/lang/Class;)Ljava/lang/invoke/MethodHandle;

    move-result-object p1

    iput-object p1, p0, Lorg/codehaus/groovy/vmplugin/v8/Selector$InitSelector;->handle:Ljava/lang/invoke/MethodHandle;

    .line 495
    sget-boolean p1, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->LOG_ENABLED:Z

    if-eqz p1, :cond_2

    sget-object p1, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->LOG:Ljava/util/logging/Logger;

    const-string v0, "create collector for arguments"

    invoke-virtual {p1, v0}, Ljava/util/logging/Logger;->info(Ljava/lang/String;)V

    :cond_2
    return-void
.end method
