.class public Lorg/codehaus/groovy/vmplugin/v8/IndyGuardsFiltersAndSignatures;
.super Ljava/lang/Object;
.source "IndyGuardsFiltersAndSignatures.java"


# static fields
.field protected static final ARRAYLIST_CONSTRUCTOR:Ljava/lang/invoke/MethodHandle;

.field protected static final BEAN_CONSTRUCTOR_PROPERTY_SETTER:Ljava/lang/invoke/MethodHandle;

.field protected static final BOOLEAN_IDENTITY:Ljava/lang/invoke/MethodHandle;

.field protected static final CLASS_FOR_NAME:Ljava/lang/invoke/MethodHandle;

.field protected static final DTT_CAST_TO_TYPE:Ljava/lang/invoke/MethodHandle;

.field protected static final EQUALS:Ljava/lang/invoke/MethodHandle;

.field protected static final GROOVY_CAST_EXCEPTION:Ljava/lang/invoke/MethodHandle;

.field protected static final GROOVY_OBJECT_GET_PROPERTY:Ljava/lang/invoke/MethodHandle;

.field protected static final GROOVY_OBJECT_INVOKER:Ljava/lang/invoke/MethodHandle;

.field protected static final HASHSET_CONSTRUCTOR:Ljava/lang/invoke/MethodHandle;

.field protected static final HAS_CATEGORY_IN_CURRENT_THREAD_GUARD:Ljava/lang/invoke/MethodHandle;

.field protected static final INTERCEPTABLE_INVOKER:Ljava/lang/invoke/MethodHandle;

.field private static final INVOKER:Ljava/lang/invoke/MethodType;

.field protected static final IS_NULL:Ljava/lang/invoke/MethodHandle;

.field protected static final META_CLASS_INVOKE_STATIC_METHOD:Ljava/lang/invoke/MethodHandle;

.field protected static final META_METHOD_INVOKER:Ljava/lang/invoke/MethodHandle;

.field protected static final META_PROPERTY_GETTER:Ljava/lang/invoke/MethodHandle;

.field protected static final MOP_GET:Ljava/lang/invoke/MethodHandle;

.field protected static final MOP_INVOKE_CONSTRUCTOR:Ljava/lang/invoke/MethodHandle;

.field protected static final MOP_INVOKE_METHOD:Ljava/lang/invoke/MethodHandle;

.field protected static final NULL_REF:Ljava/lang/invoke/MethodHandle;

.field private static final OBJECT_FILTER:Ljava/lang/invoke/MethodType;

.field private static final OBJECT_GUARD:Ljava/lang/invoke/MethodType;

.field protected static final SAME_CLASS:Ljava/lang/invoke/MethodHandle;

.field protected static final SAME_MC:Ljava/lang/invoke/MethodHandle;

.field protected static final SAM_CONVERSION:Ljava/lang/invoke/MethodHandle;

.field protected static final SLOW_META_CLASS_FIND:Ljava/lang/invoke/MethodHandle;

.field protected static final UNWRAP_EXCEPTION:Ljava/lang/invoke/MethodHandle;

.field protected static final UNWRAP_METHOD:Ljava/lang/invoke/MethodHandle;


# direct methods
.method static constructor <clinit>()V
    .locals 16

    const-string v0, "invokeMethod"

    const-string v1, "unwrap"

    const-string v2, "getProperty"

    .line 57
    const-class v3, Lorg/codehaus/groovy/vmplugin/v8/IndyGuardsFiltersAndSignatures;

    const-class v4, Ljava/lang/Object;

    const-class v5, Ljava/lang/Object;

    invoke-static {v4, v5}, Ljava/lang/invoke/MethodType;->methodType(Ljava/lang/Class;Ljava/lang/Class;)Ljava/lang/invoke/MethodType;

    move-result-object v4

    sput-object v4, Lorg/codehaus/groovy/vmplugin/v8/IndyGuardsFiltersAndSignatures;->OBJECT_FILTER:Ljava/lang/invoke/MethodType;

    .line 58
    sget-object v5, Ljava/lang/Boolean;->TYPE:Ljava/lang/Class;

    const-class v6, Ljava/lang/Object;

    invoke-static {v5, v6}, Ljava/lang/invoke/MethodType;->methodType(Ljava/lang/Class;Ljava/lang/Class;)Ljava/lang/invoke/MethodType;

    move-result-object v5

    sput-object v5, Lorg/codehaus/groovy/vmplugin/v8/IndyGuardsFiltersAndSignatures;->OBJECT_GUARD:Ljava/lang/invoke/MethodType;

    .line 59
    const-class v6, Ljava/lang/Object;

    const-class v7, Ljava/lang/Object;

    const/4 v8, 0x2

    new-array v9, v8, [Ljava/lang/Class;

    const-class v10, Ljava/lang/String;

    const/4 v11, 0x0

    aput-object v10, v9, v11

    const-class v10, [Ljava/lang/Object;

    const/4 v12, 0x1

    aput-object v10, v9, v12

    invoke-static {v6, v7, v9}, Ljava/lang/invoke/MethodType;->methodType(Ljava/lang/Class;Ljava/lang/Class;[Ljava/lang/Class;)Ljava/lang/invoke/MethodType;

    move-result-object v6

    sput-object v6, Lorg/codehaus/groovy/vmplugin/v8/IndyGuardsFiltersAndSignatures;->INVOKER:Ljava/lang/invoke/MethodType;

    .line 80
    :try_start_0
    sget-object v7, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->LOOKUP:Ljava/lang/invoke/MethodHandles$Lookup;

    const-string v9, "sameClass"

    sget-object v10, Ljava/lang/Boolean;->TYPE:Ljava/lang/Class;

    const-class v13, Ljava/lang/Class;

    new-array v14, v12, [Ljava/lang/Class;

    const-class v15, Ljava/lang/Object;

    aput-object v15, v14, v11

    invoke-static {v10, v13, v14}, Ljava/lang/invoke/MethodType;->methodType(Ljava/lang/Class;Ljava/lang/Class;[Ljava/lang/Class;)Ljava/lang/invoke/MethodType;

    move-result-object v10

    invoke-virtual {v7, v3, v9, v10}, Ljava/lang/invoke/MethodHandles$Lookup;->findStatic(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;

    move-result-object v7

    sput-object v7, Lorg/codehaus/groovy/vmplugin/v8/IndyGuardsFiltersAndSignatures;->SAME_CLASS:Ljava/lang/invoke/MethodHandle;

    .line 81
    sget-object v7, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->LOOKUP:Ljava/lang/invoke/MethodHandles$Lookup;

    const-string v9, "isSameMetaClass"

    sget-object v10, Ljava/lang/Boolean;->TYPE:Ljava/lang/Class;

    const-class v13, Lgroovy/lang/MetaClass;

    new-array v14, v12, [Ljava/lang/Class;

    const-class v15, Ljava/lang/Object;

    aput-object v15, v14, v11

    invoke-static {v10, v13, v14}, Ljava/lang/invoke/MethodType;->methodType(Ljava/lang/Class;Ljava/lang/Class;[Ljava/lang/Class;)Ljava/lang/invoke/MethodType;

    move-result-object v10

    invoke-virtual {v7, v3, v9, v10}, Ljava/lang/invoke/MethodHandles$Lookup;->findStatic(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;

    move-result-object v7

    sput-object v7, Lorg/codehaus/groovy/vmplugin/v8/IndyGuardsFiltersAndSignatures;->SAME_MC:Ljava/lang/invoke/MethodHandle;

    .line 82
    sget-object v7, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->LOOKUP:Ljava/lang/invoke/MethodHandles$Lookup;

    const-string v9, "isNull"

    invoke-virtual {v7, v3, v9, v5}, Ljava/lang/invoke/MethodHandles$Lookup;->findStatic(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;

    move-result-object v7

    sput-object v7, Lorg/codehaus/groovy/vmplugin/v8/IndyGuardsFiltersAndSignatures;->IS_NULL:Ljava/lang/invoke/MethodHandle;

    .line 83
    sget-object v7, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->LOOKUP:Ljava/lang/invoke/MethodHandles$Lookup;

    invoke-virtual {v7, v3, v1, v4}, Ljava/lang/invoke/MethodHandles$Lookup;->findStatic(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;

    move-result-object v7

    sput-object v7, Lorg/codehaus/groovy/vmplugin/v8/IndyGuardsFiltersAndSignatures;->UNWRAP_METHOD:Ljava/lang/invoke/MethodHandle;

    .line 84
    sget-object v7, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->LOOKUP:Ljava/lang/invoke/MethodHandles$Lookup;

    const-class v9, Ljava/lang/Object;

    const-class v10, Lgroovy/lang/GroovyRuntimeException;

    invoke-static {v9, v10}, Ljava/lang/invoke/MethodType;->methodType(Ljava/lang/Class;Ljava/lang/Class;)Ljava/lang/invoke/MethodType;

    move-result-object v9

    invoke-virtual {v7, v3, v1, v9}, Ljava/lang/invoke/MethodHandles$Lookup;->findStatic(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;

    move-result-object v1

    sput-object v1, Lorg/codehaus/groovy/vmplugin/v8/IndyGuardsFiltersAndSignatures;->UNWRAP_EXCEPTION:Ljava/lang/invoke/MethodHandle;

    .line 85
    sget-object v1, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->LOOKUP:Ljava/lang/invoke/MethodHandles$Lookup;

    const-class v7, Lorg/codehaus/groovy/runtime/GroovyCategorySupport;

    const-string v9, "hasCategoryInCurrentThread"

    sget-object v10, Ljava/lang/Boolean;->TYPE:Ljava/lang/Class;

    invoke-static {v10}, Ljava/lang/invoke/MethodType;->methodType(Ljava/lang/Class;)Ljava/lang/invoke/MethodType;

    move-result-object v10

    invoke-virtual {v1, v7, v9, v10}, Ljava/lang/invoke/MethodHandles$Lookup;->findStatic(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;

    move-result-object v1

    sput-object v1, Lorg/codehaus/groovy/vmplugin/v8/IndyGuardsFiltersAndSignatures;->HAS_CATEGORY_IN_CURRENT_THREAD_GUARD:Ljava/lang/invoke/MethodHandle;

    .line 86
    sget-object v1, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->LOOKUP:Ljava/lang/invoke/MethodHandles$Lookup;

    const-class v7, Lgroovy/lang/MetaMethod;

    const-string v9, "doMethodInvoke"

    const-class v10, Ljava/lang/Object;

    const-class v13, Ljava/lang/Object;

    new-array v14, v12, [Ljava/lang/Class;

    const-class v15, [Ljava/lang/Object;

    aput-object v15, v14, v11

    invoke-static {v10, v13, v14}, Ljava/lang/invoke/MethodType;->methodType(Ljava/lang/Class;Ljava/lang/Class;[Ljava/lang/Class;)Ljava/lang/invoke/MethodType;

    move-result-object v10

    invoke-virtual {v1, v7, v9, v10}, Ljava/lang/invoke/MethodHandles$Lookup;->findVirtual(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;

    move-result-object v1

    sput-object v1, Lorg/codehaus/groovy/vmplugin/v8/IndyGuardsFiltersAndSignatures;->META_METHOD_INVOKER:Ljava/lang/invoke/MethodHandle;

    .line 87
    sget-object v1, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->LOOKUP:Ljava/lang/invoke/MethodHandles$Lookup;

    const-string v7, "invokeGroovyObjectInvoker"

    new-array v9, v12, [Ljava/lang/Class;

    const-class v10, Lgroovy/lang/MissingMethodException;

    aput-object v10, v9, v11

    invoke-virtual {v6, v11, v9}, Ljava/lang/invoke/MethodType;->insertParameterTypes(I[Ljava/lang/Class;)Ljava/lang/invoke/MethodType;

    move-result-object v9

    invoke-virtual {v1, v3, v7, v9}, Ljava/lang/invoke/MethodHandles$Lookup;->findStatic(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;

    move-result-object v1

    sput-object v1, Lorg/codehaus/groovy/vmplugin/v8/IndyGuardsFiltersAndSignatures;->GROOVY_OBJECT_INVOKER:Ljava/lang/invoke/MethodHandle;

    .line 88
    sget-object v1, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->LOOKUP:Ljava/lang/invoke/MethodHandles$Lookup;

    const-class v7, Lgroovy/lang/GroovyObject;

    const-class v9, Ljava/lang/Object;

    const-class v10, Ljava/lang/String;

    invoke-static {v9, v10}, Ljava/lang/invoke/MethodType;->methodType(Ljava/lang/Class;Ljava/lang/Class;)Ljava/lang/invoke/MethodType;

    move-result-object v9

    invoke-virtual {v1, v7, v2, v9}, Ljava/lang/invoke/MethodHandles$Lookup;->findVirtual(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;

    move-result-object v1

    sput-object v1, Lorg/codehaus/groovy/vmplugin/v8/IndyGuardsFiltersAndSignatures;->GROOVY_OBJECT_GET_PROPERTY:Ljava/lang/invoke/MethodHandle;

    .line 89
    sget-object v1, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->LOOKUP:Ljava/lang/invoke/MethodHandles$Lookup;

    const-class v7, Lgroovy/lang/MetaObjectProtocol;

    const-string v9, "invokeStaticMethod"

    invoke-virtual {v1, v7, v9, v6}, Ljava/lang/invoke/MethodHandles$Lookup;->findVirtual(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;

    move-result-object v1

    sput-object v1, Lorg/codehaus/groovy/vmplugin/v8/IndyGuardsFiltersAndSignatures;->META_CLASS_INVOKE_STATIC_METHOD:Ljava/lang/invoke/MethodHandle;

    .line 90
    sget-object v1, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->LOOKUP:Ljava/lang/invoke/MethodHandles$Lookup;

    const-string v7, "setBeanProperties"

    const-class v9, Ljava/lang/Object;

    const-class v10, Lgroovy/lang/MetaClass;

    new-array v13, v8, [Ljava/lang/Class;

    const-class v14, Ljava/lang/Object;

    aput-object v14, v13, v11

    const-class v14, Ljava/util/Map;

    aput-object v14, v13, v12

    invoke-static {v9, v10, v13}, Ljava/lang/invoke/MethodType;->methodType(Ljava/lang/Class;Ljava/lang/Class;[Ljava/lang/Class;)Ljava/lang/invoke/MethodType;

    move-result-object v9

    invoke-virtual {v1, v3, v7, v9}, Ljava/lang/invoke/MethodHandles$Lookup;->findStatic(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;

    move-result-object v1

    sput-object v1, Lorg/codehaus/groovy/vmplugin/v8/IndyGuardsFiltersAndSignatures;->BEAN_CONSTRUCTOR_PROPERTY_SETTER:Ljava/lang/invoke/MethodHandle;

    .line 91
    sget-object v1, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->LOOKUP:Ljava/lang/invoke/MethodHandles$Lookup;

    const-class v3, Lgroovy/lang/MetaProperty;

    invoke-virtual {v1, v3, v2, v4}, Ljava/lang/invoke/MethodHandles$Lookup;->findVirtual(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;

    move-result-object v1

    sput-object v1, Lorg/codehaus/groovy/vmplugin/v8/IndyGuardsFiltersAndSignatures;->META_PROPERTY_GETTER:Ljava/lang/invoke/MethodHandle;

    .line 92
    sget-object v1, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->LOOKUP:Ljava/lang/invoke/MethodHandles$Lookup;

    const-class v3, Lorg/codehaus/groovy/runtime/InvokerHelper;

    const-string v4, "getMetaClass"

    const-class v7, Lgroovy/lang/MetaClass;

    const-class v9, Ljava/lang/Object;

    invoke-static {v7, v9}, Ljava/lang/invoke/MethodType;->methodType(Ljava/lang/Class;Ljava/lang/Class;)Ljava/lang/invoke/MethodType;

    move-result-object v7

    invoke-virtual {v1, v3, v4, v7}, Ljava/lang/invoke/MethodHandles$Lookup;->findStatic(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;

    move-result-object v1

    sput-object v1, Lorg/codehaus/groovy/vmplugin/v8/IndyGuardsFiltersAndSignatures;->SLOW_META_CLASS_FIND:Ljava/lang/invoke/MethodHandle;

    .line 93
    sget-object v1, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->LOOKUP:Ljava/lang/invoke/MethodHandles$Lookup;

    const-class v3, Lgroovy/lang/MetaObjectProtocol;

    const-class v4, Ljava/lang/Object;

    const-class v7, Ljava/lang/Object;

    new-array v9, v12, [Ljava/lang/Class;

    const-class v10, Ljava/lang/String;

    aput-object v10, v9, v11

    invoke-static {v4, v7, v9}, Ljava/lang/invoke/MethodType;->methodType(Ljava/lang/Class;Ljava/lang/Class;[Ljava/lang/Class;)Ljava/lang/invoke/MethodType;

    move-result-object v4

    invoke-virtual {v1, v3, v2, v4}, Ljava/lang/invoke/MethodHandles$Lookup;->findVirtual(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;

    move-result-object v1

    sput-object v1, Lorg/codehaus/groovy/vmplugin/v8/IndyGuardsFiltersAndSignatures;->MOP_GET:Ljava/lang/invoke/MethodHandle;

    .line 94
    sget-object v1, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->LOOKUP:Ljava/lang/invoke/MethodHandles$Lookup;

    const-class v2, Lgroovy/lang/MetaObjectProtocol;

    const-string v3, "invokeConstructor"

    const-class v4, Ljava/lang/Object;

    const-class v7, [Ljava/lang/Object;

    invoke-static {v4, v7}, Ljava/lang/invoke/MethodType;->methodType(Ljava/lang/Class;Ljava/lang/Class;)Ljava/lang/invoke/MethodType;

    move-result-object v4

    invoke-virtual {v1, v2, v3, v4}, Ljava/lang/invoke/MethodHandles$Lookup;->findVirtual(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;

    move-result-object v1

    sput-object v1, Lorg/codehaus/groovy/vmplugin/v8/IndyGuardsFiltersAndSignatures;->MOP_INVOKE_CONSTRUCTOR:Ljava/lang/invoke/MethodHandle;

    .line 95
    sget-object v1, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->LOOKUP:Ljava/lang/invoke/MethodHandles$Lookup;

    const-class v2, Lgroovy/lang/MetaObjectProtocol;

    invoke-virtual {v1, v2, v0, v6}, Ljava/lang/invoke/MethodHandles$Lookup;->findVirtual(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;

    move-result-object v1

    sput-object v1, Lorg/codehaus/groovy/vmplugin/v8/IndyGuardsFiltersAndSignatures;->MOP_INVOKE_METHOD:Ljava/lang/invoke/MethodHandle;

    .line 96
    sget-object v1, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->LOOKUP:Ljava/lang/invoke/MethodHandles$Lookup;

    const-class v2, Lgroovy/lang/GroovyObject;

    const-class v3, Ljava/lang/Object;

    const-class v4, Ljava/lang/String;

    new-array v6, v12, [Ljava/lang/Class;

    const-class v7, Ljava/lang/Object;

    aput-object v7, v6, v11

    invoke-static {v3, v4, v6}, Ljava/lang/invoke/MethodType;->methodType(Ljava/lang/Class;Ljava/lang/Class;[Ljava/lang/Class;)Ljava/lang/invoke/MethodType;

    move-result-object v3

    invoke-virtual {v1, v2, v0, v3}, Ljava/lang/invoke/MethodHandles$Lookup;->findVirtual(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/vmplugin/v8/IndyGuardsFiltersAndSignatures;->INTERCEPTABLE_INVOKER:Ljava/lang/invoke/MethodHandle;

    .line 98
    const-class v0, Ljava/lang/Boolean;

    invoke-static {v0}, Ljava/lang/invoke/MethodHandles;->identity(Ljava/lang/Class;)Ljava/lang/invoke/MethodHandle;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/vmplugin/v8/IndyGuardsFiltersAndSignatures;->BOOLEAN_IDENTITY:Ljava/lang/invoke/MethodHandle;

    .line 99
    sget-object v0, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->LOOKUP:Ljava/lang/invoke/MethodHandles$Lookup;

    const-class v1, Ljava/lang/Class;

    const-string v2, "forName"

    const-class v3, Ljava/lang/Class;

    const-class v4, Ljava/lang/String;

    new-array v6, v8, [Ljava/lang/Class;

    sget-object v7, Ljava/lang/Boolean;->TYPE:Ljava/lang/Class;

    aput-object v7, v6, v11

    const-class v7, Ljava/lang/ClassLoader;

    aput-object v7, v6, v12

    invoke-static {v3, v4, v6}, Ljava/lang/invoke/MethodType;->methodType(Ljava/lang/Class;Ljava/lang/Class;[Ljava/lang/Class;)Ljava/lang/invoke/MethodType;

    move-result-object v3

    invoke-virtual {v0, v1, v2, v3}, Ljava/lang/invoke/MethodHandles$Lookup;->findStatic(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/vmplugin/v8/IndyGuardsFiltersAndSignatures;->CLASS_FOR_NAME:Ljava/lang/invoke/MethodHandle;

    .line 100
    sget-object v0, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->LOOKUP:Ljava/lang/invoke/MethodHandles$Lookup;

    const-class v1, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;

    const-string v2, "castToType"

    const-class v3, Ljava/lang/Object;

    const-class v4, Ljava/lang/Object;

    new-array v6, v12, [Ljava/lang/Class;

    const-class v7, Ljava/lang/Class;

    aput-object v7, v6, v11

    invoke-static {v3, v4, v6}, Ljava/lang/invoke/MethodType;->methodType(Ljava/lang/Class;Ljava/lang/Class;[Ljava/lang/Class;)Ljava/lang/invoke/MethodType;

    move-result-object v3

    invoke-virtual {v0, v1, v2, v3}, Ljava/lang/invoke/MethodHandles$Lookup;->findStatic(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/vmplugin/v8/IndyGuardsFiltersAndSignatures;->DTT_CAST_TO_TYPE:Ljava/lang/invoke/MethodHandle;

    .line 101
    sget-object v0, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->LOOKUP:Ljava/lang/invoke/MethodHandles$Lookup;

    const-class v1, Lorg/codehaus/groovy/reflection/stdclasses/CachedSAMClass;

    const-string v2, "coerceToSAM"

    const-class v3, Ljava/lang/Object;

    const-class v4, Lgroovy/lang/Closure;

    new-array v6, v8, [Ljava/lang/Class;

    const-class v7, Ljava/lang/reflect/Method;

    aput-object v7, v6, v11

    const-class v7, Ljava/lang/Class;

    aput-object v7, v6, v12

    invoke-static {v3, v4, v6}, Ljava/lang/invoke/MethodType;->methodType(Ljava/lang/Class;Ljava/lang/Class;[Ljava/lang/Class;)Ljava/lang/invoke/MethodType;

    move-result-object v3

    invoke-virtual {v0, v1, v2, v3}, Ljava/lang/invoke/MethodHandles$Lookup;->findStatic(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/vmplugin/v8/IndyGuardsFiltersAndSignatures;->SAM_CONVERSION:Ljava/lang/invoke/MethodHandle;

    .line 102
    sget-object v0, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->LOOKUP:Ljava/lang/invoke/MethodHandles$Lookup;

    const-class v1, Ljava/util/HashSet;

    sget-object v2, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const-class v3, Ljava/util/Collection;

    invoke-static {v2, v3}, Ljava/lang/invoke/MethodType;->methodType(Ljava/lang/Class;Ljava/lang/Class;)Ljava/lang/invoke/MethodType;

    move-result-object v2

    invoke-virtual {v0, v1, v2}, Ljava/lang/invoke/MethodHandles$Lookup;->findConstructor(Ljava/lang/Class;Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/vmplugin/v8/IndyGuardsFiltersAndSignatures;->HASHSET_CONSTRUCTOR:Ljava/lang/invoke/MethodHandle;

    .line 103
    sget-object v0, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->LOOKUP:Ljava/lang/invoke/MethodHandles$Lookup;

    const-class v1, Ljava/util/ArrayList;

    sget-object v2, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const-class v3, Ljava/util/Collection;

    invoke-static {v2, v3}, Ljava/lang/invoke/MethodType;->methodType(Ljava/lang/Class;Ljava/lang/Class;)Ljava/lang/invoke/MethodType;

    move-result-object v2

    invoke-virtual {v0, v1, v2}, Ljava/lang/invoke/MethodHandles$Lookup;->findConstructor(Ljava/lang/Class;Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/vmplugin/v8/IndyGuardsFiltersAndSignatures;->ARRAYLIST_CONSTRUCTOR:Ljava/lang/invoke/MethodHandle;

    .line 104
    sget-object v0, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->LOOKUP:Ljava/lang/invoke/MethodHandles$Lookup;

    const-class v1, Lorg/codehaus/groovy/runtime/typehandling/GroovyCastException;

    sget-object v2, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const-class v3, Ljava/lang/Object;

    new-array v4, v12, [Ljava/lang/Class;

    const-class v6, Ljava/lang/Class;

    aput-object v6, v4, v11

    invoke-static {v2, v3, v4}, Ljava/lang/invoke/MethodType;->methodType(Ljava/lang/Class;Ljava/lang/Class;[Ljava/lang/Class;)Ljava/lang/invoke/MethodType;

    move-result-object v2

    invoke-virtual {v0, v1, v2}, Ljava/lang/invoke/MethodHandles$Lookup;->findConstructor(Ljava/lang/Class;Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/vmplugin/v8/IndyGuardsFiltersAndSignatures;->GROOVY_CAST_EXCEPTION:Ljava/lang/invoke/MethodHandle;

    .line 106
    sget-object v0, Lorg/codehaus/groovy/vmplugin/v8/IndyInterface;->LOOKUP:Ljava/lang/invoke/MethodHandles$Lookup;

    const-class v1, Ljava/lang/Object;

    const-string v2, "equals"

    invoke-virtual {v0, v1, v2, v5}, Ljava/lang/invoke/MethodHandles$Lookup;->findVirtual(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MethodHandle;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/vmplugin/v8/IndyGuardsFiltersAndSignatures;->EQUALS:Ljava/lang/invoke/MethodHandle;
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    .line 112
    const-class v0, Ljava/lang/Object;

    const/4 v1, 0x0

    invoke-static {v0, v1}, Ljava/lang/invoke/MethodHandles;->constant(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/invoke/MethodHandle;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/vmplugin/v8/IndyGuardsFiltersAndSignatures;->NULL_REF:Ljava/lang/invoke/MethodHandle;

    return-void

    :catch_0
    move-exception v0

    .line 108
    new-instance v1, Lorg/codehaus/groovy/GroovyBugError;

    invoke-direct {v1, v0}, Lorg/codehaus/groovy/GroovyBugError;-><init>(Ljava/lang/Exception;)V

    throw v1
.end method

.method public constructor <init>()V
    .locals 0

    .line 54
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static invokeGroovyObjectInvoker(Lgroovy/lang/MissingMethodException;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 141
    instance-of v0, p0, Lorg/codehaus/groovy/runtime/metaclass/MissingMethodExecutionFailed;

    if-nez v0, :cond_1

    .line 143
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-virtual {p0}, Lgroovy/lang/MissingMethodException;->getType()Ljava/lang/Class;

    move-result-object v1

    if-ne v0, v1, :cond_0

    invoke-virtual {p0}, Lgroovy/lang/MissingMethodException;->getMethod()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0, p2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 148
    check-cast p1, Lgroovy/lang/GroovyObject;

    invoke-interface {p1, p2, p3}, Lgroovy/lang/GroovyObject;->invokeMethod(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    return-object p0

    .line 150
    :cond_0
    throw p0

    .line 142
    :cond_1
    invoke-virtual {p0}, Lgroovy/lang/MissingMethodException;->getCause()Ljava/lang/Throwable;

    move-result-object p0

    check-cast p0, Lgroovy/lang/MissingMethodException;

    throw p0
.end method

.method public static isNull(Ljava/lang/Object;)Z
    .locals 0

    if-nez p0, :cond_0

    const/4 p0, 0x1

    goto :goto_0

    :cond_0
    const/4 p0, 0x0

    :goto_0
    return p0
.end method

.method public static isSameMetaClass(Lgroovy/lang/MetaClass;Ljava/lang/Object;)Z
    .locals 1

    .line 168
    instance-of v0, p1, Lgroovy/lang/GroovyObject;

    if-eqz v0, :cond_0

    check-cast p1, Lgroovy/lang/GroovyObject;

    invoke-interface {p1}, Lgroovy/lang/GroovyObject;->getMetaClass()Lgroovy/lang/MetaClass;

    move-result-object p1

    if-ne p0, p1, :cond_0

    const/4 p0, 0x1

    goto :goto_0

    :cond_0
    const/4 p0, 0x0

    :goto_0
    return p0
.end method

.method public static sameClass(Ljava/lang/Class;Ljava/lang/Object;)Z
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Class<",
            "*>;",
            "Ljava/lang/Object;",
            ")Z"
        }
    .end annotation

    const/4 v0, 0x0

    if-nez p1, :cond_0

    return v0

    .line 197
    :cond_0
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object p1

    if-ne p1, p0, :cond_1

    const/4 v0, 0x1

    :cond_1
    return v0
.end method

.method public static setBeanProperties(Lgroovy/lang/MetaClass;Ljava/lang/Object;Ljava/util/Map;)Ljava/lang/Object;
    .locals 2

    .line 119
    invoke-interface {p2}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    move-result-object p2

    invoke-interface {p2}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object p2

    :goto_0
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    .line 120
    check-cast v0, Ljava/util/Map$Entry;

    .line 121
    invoke-interface {v0}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v1

    .line 123
    invoke-interface {v0}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v0

    .line 124
    invoke-interface {p0, p1, v1, v0}, Lgroovy/lang/MetaClass;->setProperty(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;)V

    goto :goto_0

    :cond_0
    return-object p1
.end method

.method public static unwrap(Lgroovy/lang/GroovyRuntimeException;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Throwable;
        }
    .end annotation

    .line 160
    invoke-static {p0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->unwrap(Lgroovy/lang/GroovyRuntimeException;)Ljava/lang/Throwable;

    move-result-object p0

    throw p0
.end method

.method public static unwrap(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 177
    check-cast p0, Lorg/codehaus/groovy/runtime/wrappers/Wrapper;

    .line 178
    invoke-virtual {p0}, Lorg/codehaus/groovy/runtime/wrappers/Wrapper;->unwrap()Ljava/lang/Object;

    move-result-object p0

    return-object p0
.end method
