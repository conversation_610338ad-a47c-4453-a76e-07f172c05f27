.class public Lorg/codehaus/groovy/vmplugin/VMPluginFactory;
.super Ljava/lang/Object;
.source "VMPluginFactory.java"


# static fields
.field private static final JDK8_CLASSNAME_CHECK:Ljava/lang/String; = "java.util.Optional"

.field private static final JDK8_PLUGIN_NAME:Ljava/lang/String; = "org.codehaus.groovy.vmplugin.v8.Java8"

.field private static final JDK9_CLASSNAME_CHECK:Ljava/lang/String; = "java.lang.Module"

.field private static final JDK9_PLUGIN_NAME:Ljava/lang/String; = "org.codehaus.groovy.vmplugin.v9.Java9"

.field private static final LOGGER:Ljava/util/logging/Logger;

.field private static final PLUGIN:Lorg/codehaus/groovy/vmplugin/VMPlugin;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 34
    const-class v0, Lorg/codehaus/groovy/vmplugin/VMPluginFactory;

    invoke-virtual {v0}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Ljava/util/logging/Logger;->getLogger(Ljava/lang/String;)Ljava/util/logging/Logger;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/vmplugin/VMPluginFactory;->LOGGER:Ljava/util/logging/Logger;

    const-string v0, "java.lang.Module"

    const-string v1, "org.codehaus.groovy.vmplugin.v9.Java9"

    .line 45
    invoke-static {v0, v1}, Lorg/codehaus/groovy/vmplugin/VMPluginFactory;->createPlugin(Ljava/lang/String;Ljava/lang/String;)Lorg/codehaus/groovy/vmplugin/VMPlugin;

    move-result-object v0

    if-nez v0, :cond_0

    const-string v0, "java.util.Optional"

    const-string v1, "org.codehaus.groovy.vmplugin.v8.Java8"

    .line 47
    invoke-static {v0, v1}, Lorg/codehaus/groovy/vmplugin/VMPluginFactory;->createPlugin(Ljava/lang/String;Ljava/lang/String;)Lorg/codehaus/groovy/vmplugin/VMPlugin;

    move-result-object v0

    .line 50
    :cond_0
    sput-object v0, Lorg/codehaus/groovy/vmplugin/VMPluginFactory;->PLUGIN:Lorg/codehaus/groovy/vmplugin/VMPlugin;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 33
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method private static createPlugin(Ljava/lang/String;Ljava/lang/String;)Lorg/codehaus/groovy/vmplugin/VMPlugin;
    .locals 1

    .line 58
    new-instance v0, Lorg/codehaus/groovy/vmplugin/VMPluginFactory$$ExternalSyntheticLambda0;

    invoke-direct {v0, p0, p1}, Lorg/codehaus/groovy/vmplugin/VMPluginFactory$$ExternalSyntheticLambda0;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    invoke-static {v0}, Ljava/security/AccessController;->doPrivileged(Ljava/security/PrivilegedAction;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Lorg/codehaus/groovy/vmplugin/VMPlugin;

    return-object p0
.end method

.method public static getPlugin()Lorg/codehaus/groovy/vmplugin/VMPlugin;
    .locals 1

    .line 54
    sget-object v0, Lorg/codehaus/groovy/vmplugin/VMPluginFactory;->PLUGIN:Lorg/codehaus/groovy/vmplugin/VMPlugin;

    return-object v0
.end method

.method static synthetic lambda$createPlugin$0(Ljava/lang/String;Ljava/lang/String;)Lorg/codehaus/groovy/vmplugin/VMPlugin;
    .locals 4

    .line 60
    :try_start_0
    const-class v0, Lorg/codehaus/groovy/vmplugin/VMPluginFactory;

    invoke-virtual {v0}, Ljava/lang/Class;->getClassLoader()Ljava/lang/ClassLoader;

    move-result-object v0

    .line 61
    invoke-virtual {v0, p0}, Ljava/lang/ClassLoader;->loadClass(Ljava/lang/String;)Ljava/lang/Class;

    .line 62
    invoke-virtual {v0, p1}, Ljava/lang/ClassLoader;->loadClass(Ljava/lang/String;)Ljava/lang/Class;

    move-result-object v0

    const/4 v1, 0x0

    new-array v2, v1, [Ljava/lang/Class;

    invoke-virtual {v0, v2}, Ljava/lang/Class;->getDeclaredConstructor([Ljava/lang/Class;)Ljava/lang/reflect/Constructor;

    move-result-object v0

    new-array v1, v1, [Ljava/lang/Object;

    invoke-virtual {v0, v1}, Ljava/lang/reflect/Constructor;->newInstance([Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/vmplugin/VMPlugin;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    return-object v0

    :catchall_0
    move-exception v0

    .line 64
    sget-object v1, Lorg/codehaus/groovy/vmplugin/VMPluginFactory;->LOGGER:Ljava/util/logging/Logger;

    sget-object v2, Ljava/util/logging/Level;->FINE:Ljava/util/logging/Level;

    invoke-virtual {v1, v2}, Ljava/util/logging/Logger;->isLoggable(Ljava/util/logging/Level;)Z

    move-result v2

    if-eqz v2, :cond_0

    .line 65
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "Trying to create VM plugin `"

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v2, "` by checking `"

    invoke-virtual {p1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    const-string p1, "`, but failed:\n"

    invoke-virtual {p0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    .line 66
    invoke-static {v0}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->asString(Ljava/lang/Throwable;)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    .line 65
    invoke-virtual {v1, p0}, Ljava/util/logging/Logger;->fine(Ljava/lang/String;)V

    :cond_0
    const/4 p0, 0x0

    return-object p0
.end method
