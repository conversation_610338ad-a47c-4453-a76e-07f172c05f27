.class public Lorg/codehaus/groovy/syntax/Types;
.super Ljava/lang/Object;
.source "Types.java"


# static fields
.field public static final ANY:I = 0x3e8

.field public static final ANY_END_OF_STATEMENT:I = 0x3eb

.field public static final ARRAY_EXPRESSION:I = 0x771

.field public static final ARRAY_ITEM_TERMINATORS:I = 0x7d1

.field public static final ASSIGN:I = 0x64

.field public static final ASSIGNMENT_OPERATOR:I = 0x44c

.field public static final BITWISE_AND:I = 0x155

.field public static final BITWISE_AND_EQUAL:I = 0x15f

.field public static final BITWISE_NEGATION:I = 0x61

.field public static final BITWISE_OPERATOR:I = 0x453

.field public static final BITWISE_OR:I = 0x154

.field public static final BITWISE_OR_EQUAL:I = 0x15e

.field public static final BITWISE_XOR:I = 0x156

.field public static final BITWISE_XOR_EQUAL:I = 0x160

.field public static final COLON:I = 0x136

.field public static final COMMA:I = 0x12c

.field public static final COMPARE_EQUAL:I = 0x7b

.field public static final COMPARE_GREATER_THAN:I = 0x7e

.field public static final COMPARE_GREATER_THAN_EQUAL:I = 0x7f

.field public static final COMPARE_IDENTICAL:I = 0x79

.field public static final COMPARE_LESS_THAN:I = 0x7c

.field public static final COMPARE_LESS_THAN_EQUAL:I = 0x7d

.field public static final COMPARE_NOT_EQUAL:I = 0x78

.field public static final COMPARE_NOT_IDENTICAL:I = 0x7a

.field public static final COMPARE_NOT_IN:I = 0x81

.field public static final COMPARE_NOT_INSTANCEOF:I = 0x82

.field public static final COMPARE_TO:I = 0x80

.field public static final COMPARISON_OPERATOR:I = 0x44d

.field public static final COMPLEX_EXPRESSION:I = 0x777

.field public static final CREATABLE_PRIMITIVE_TYPE:I = 0x53d

.field public static final CREATABLE_TYPE_NAME:I = 0x596

.field public static final DECIMAL_NUMBER:I = 0x1c3

.field public static final DECLARATION_MODIFIER:I = 0x582

.field public static final DEREFERENCE_OPERATOR:I = 0x452

.field private static final DESCRIPTIONS:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/Integer;",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public static final DIVIDE:I = 0xcb

.field public static final DIVIDE_EQUAL:I = 0xd5

.field public static final DOT:I = 0x46

.field public static final DOT_DOT:I = 0x4b

.field public static final DOT_DOT_DOT:I = 0x4d

.field public static final DOUBLE_PIPE:I = 0xa2

.field public static final ELVIS_EQUAL:I = 0xd9

.field public static final EOF:I = -0x1

.field public static final EQUAL:I = 0x64

.field public static final EQUALS:I = 0x64

.field public static final EXPRESSION:I = 0x76c

.field public static final FIND_REGEX:I = 0x5a

.field public static final GENERAL_END_OF_STATEMENT:I = 0x3ea

.field public static final GSTRING_END:I = 0x386

.field public static final GSTRING_EXPRESSION_END:I = 0x388

.field public static final GSTRING_EXPRESSION_START:I = 0x387

.field public static final GSTRING_START:I = 0x385

.field public static final IDENTIFIER:I = 0x1b8

.field public static final INFIX_OPERATOR:I = 0x4c4

.field public static final INSTANCEOF_OPERATOR:I = 0x454

.field public static final INTDIV:I = 0xcc

.field public static final INTDIV_EQUAL:I = 0xd6

.field public static final INTEGER_NUMBER:I = 0x1c2

.field public static final KEYWORD:I = 0x514

.field private static final KEYWORDS:Ljava/util/Set;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Set<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public static final KEYWORD_ABSTRACT:I = 0x1fe

.field public static final KEYWORD_AS:I = 0x228

.field public static final KEYWORD_ASSERT:I = 0x249

.field public static final KEYWORD_BOOLEAN:I = 0x259

.field public static final KEYWORD_BREAK:I = 0x23e

.field public static final KEYWORD_BYTE:I = 0x25a

.field public static final KEYWORD_CASE:I = 0x241

.field public static final KEYWORD_CATCH:I = 0x245

.field public static final KEYWORD_CHAR:I = 0x260

.field public static final KEYWORD_CLASS:I = 0x213

.field public static final KEYWORD_CONST:I = 0x2bc

.field public static final KEYWORD_CONTINUE:I = 0x23f

.field public static final KEYWORD_DEF:I = 0x212

.field public static final KEYWORD_DEFAULT:I = 0x242

.field public static final KEYWORD_DEFMACRO:I = 0x21b

.field public static final KEYWORD_DO:I = 0x23a

.field public static final KEYWORD_DOUBLE:I = 0x25f

.field public static final KEYWORD_ELSE:I = 0x232

.field public static final KEYWORD_EXPRESSION:I = 0x76f

.field public static final KEYWORD_EXTENDS:I = 0x21d

.field public static final KEYWORD_FALSE:I = 0x263

.field public static final KEYWORD_FINAL:I = 0x1ff

.field public static final KEYWORD_FINALLY:I = 0x246

.field public static final KEYWORD_FLOAT:I = 0x25e

.field public static final KEYWORD_FOR:I = 0x23c

.field public static final KEYWORD_GOTO:I = 0x2bd

.field public static final KEYWORD_IDENTIFIER:I = 0x551

.field public static final KEYWORD_IF:I = 0x231

.field public static final KEYWORD_IMPLEMENTS:I = 0x21c

.field public static final KEYWORD_IMPORT:I = 0x227

.field public static final KEYWORD_IN:I = 0x23d

.field public static final KEYWORD_INSTANCEOF:I = 0x220

.field public static final KEYWORD_INT:I = 0x25c

.field public static final KEYWORD_INTERFACE:I = 0x214

.field public static final KEYWORD_LONG:I = 0x25d

.field public static final KEYWORD_MIXIN:I = 0x215

.field public static final KEYWORD_NATIVE:I = 0x200

.field public static final KEYWORD_NEW:I = 0x222

.field public static final KEYWORD_NULL:I = 0x264

.field public static final KEYWORD_PACKAGE:I = 0x226

.field public static final KEYWORD_PRIVATE:I = 0x1f4

.field public static final KEYWORD_PROPERTY:I = 0x221

.field public static final KEYWORD_PROTECTED:I = 0x1f5

.field public static final KEYWORD_PUBLIC:I = 0x1f6

.field public static final KEYWORD_RETURN:I = 0x230

.field public static final KEYWORD_SHORT:I = 0x25b

.field public static final KEYWORD_STATIC:I = 0x209

.field public static final KEYWORD_SUPER:I = 0x21f

.field public static final KEYWORD_SWITCH:I = 0x240

.field public static final KEYWORD_SYNCHRONIZED:I = 0x208

.field public static final KEYWORD_THIS:I = 0x21e

.field public static final KEYWORD_THROW:I = 0x247

.field public static final KEYWORD_THROWS:I = 0x248

.field public static final KEYWORD_TRANSIENT:I = 0x201

.field public static final KEYWORD_TRUE:I = 0x262

.field public static final KEYWORD_TRY:I = 0x244

.field public static final KEYWORD_VOID:I = 0x258

.field public static final KEYWORD_VOLATILE:I = 0x202

.field public static final KEYWORD_WHILE:I = 0x23b

.field public static final LEFT_CURLY_BRACE:I = 0xa

.field public static final LEFT_OF_MATCHED_CONTAINER:I = 0x5dd

.field public static final LEFT_PARENTHESIS:I = 0x32

.field public static final LEFT_SHIFT:I = 0x118

.field public static final LEFT_SHIFT_EQUAL:I = 0x11d

.field public static final LEFT_SQUARE_BRACKET:I = 0x1e

.field public static final LITERAL:I = 0x51e

.field public static final LITERAL_EXPRESSION:I = 0x770

.field public static final LOGICAL_AND:I = 0xa4

.field public static final LOGICAL_AND_EQUAL:I = 0xa8

.field public static final LOGICAL_OPERATOR:I = 0x44f

.field public static final LOGICAL_OR:I = 0xa2

.field public static final LOGICAL_OR_EQUAL:I = 0xa6

.field private static final LOOKUP:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation
.end field

.field public static final LOOP:I = 0x546

.field public static final MATCHED_CONTAINER:I = 0x5dc

.field public static final MATCH_REGEX:I = 0x5e

.field public static final MATH_OPERATOR:I = 0x44e

.field public static final METHOD_CALL_STARTERS:I = 0x7d6

.field public static final MINUS:I = 0xc9

.field public static final MINUS_EQUAL:I = 0xd3

.field public static final MINUS_MINUS:I = 0x104

.field public static final MOD:I = 0xcd

.field public static final MOD_EQUAL:I = 0xd7

.field public static final MULTIPLY:I = 0xca

.field public static final MULTIPLY_EQUAL:I = 0xd4

.field public static final NAMED_VALUE:I = 0x532

.field public static final NAVIGATE:I = 0x50

.field public static final NEWLINE:I = 0x5

.field public static final NOT:I = 0xa0

.field public static final NOT_EOF:I = 0x3e9

.field public static final NUMBER:I = 0x528

.field public static final OPERATOR_EXPRESSION:I = 0x76d

.field public static final OPTIONAL_DATATYPE_FOLLOWERS:I = 0x7d3

.field public static final PARAMETER_TERMINATORS:I = 0x7d0

.field public static final PIPE:I = 0x154

.field public static final PLUS:I = 0xc8

.field public static final PLUS_EQUAL:I = 0xd2

.field public static final PLUS_PLUS:I = 0xfa

.field public static final POSTFIX_MINUS_MINUS:I = 0x106

.field public static final POSTFIX_OPERATOR:I = 0x4ba

.field public static final POSTFIX_PLUS_PLUS:I = 0xfc

.field public static final POWER:I = 0xce

.field public static final POWER_EQUAL:I = 0xd8

.field public static final PRECLUDES_CAST_OPERATOR:I = 0x7d8

.field public static final PREFIX_MINUS:I = 0x107

.field public static final PREFIX_MINUS_MINUS:I = 0x105

.field public static final PREFIX_OPERATOR:I = 0x4b0

.field public static final PREFIX_OR_INFIX_OPERATOR:I = 0x4ce

.field public static final PREFIX_PLUS:I = 0xfd

.field public static final PREFIX_PLUS_PLUS:I = 0xfb

.field public static final PRIMITIVE_TYPE:I = 0x53c

.field public static final PURE_PREFIX_OPERATOR:I = 0x4d3

.field public static final QUESTION:I = 0x14a

.field public static final RANGE_OPERATOR:I = 0x450

.field public static final REGEX_COMPARISON_OPERATOR:I = 0x451

.field public static final REGEX_PATTERN:I = 0x61

.field public static final RESERVED_KEYWORD:I = 0x550

.field public static final RIGHT_CURLY_BRACE:I = 0x14

.field public static final RIGHT_OF_MATCHED_CONTAINER:I = 0x5de

.field public static final RIGHT_PARENTHESIS:I = 0x3c

.field public static final RIGHT_SHIFT:I = 0x119

.field public static final RIGHT_SHIFT_EQUAL:I = 0x11e

.field public static final RIGHT_SHIFT_UNSIGNED:I = 0x11a

.field public static final RIGHT_SHIFT_UNSIGNED_EQUAL:I = 0x11f

.field public static final RIGHT_SQUARE_BRACKET:I = 0x28

.field public static final SEMICOLON:I = 0x140

.field public static final SIGN:I = 0x52d

.field public static final SIMPLE_EXPRESSION:I = 0x776

.field public static final STAR:I = 0xca

.field public static final STAR_STAR:I = 0xce

.field public static final STRING:I = 0x190

.field public static final SWITCH_BLOCK_TERMINATORS:I = 0x7d4

.field public static final SWITCH_ENTRIES:I = 0x7d5

.field public static final SYMBOL:I = 0x515

.field public static final SYNTHETIC:I = 0x55a

.field public static final SYNTH_BLOCK:I = 0x330

.field public static final SYNTH_CAST:I = 0x32f

.field public static final SYNTH_CLASS:I = 0x321

.field public static final SYNTH_CLOSURE:I = 0x331

.field public static final SYNTH_COMPILATION_UNIT:I = 0x320

.field public static final SYNTH_EXPRESSION:I = 0x76e

.field public static final SYNTH_GSTRING:I = 0x32c

.field public static final SYNTH_INTERFACE:I = 0x322

.field public static final SYNTH_LABEL:I = 0x332

.field public static final SYNTH_LIST:I = 0x32a

.field public static final SYNTH_MAP:I = 0x32b

.field public static final SYNTH_METHOD:I = 0x324

.field public static final SYNTH_METHOD_CALL:I = 0x32e

.field public static final SYNTH_MIXIN:I = 0x323

.field public static final SYNTH_PARAMETER_DECLARATION:I = 0x326

.field public static final SYNTH_PROPERTY:I = 0x325

.field public static final SYNTH_TERNARY:I = 0x333

.field public static final SYNTH_TUPLE:I = 0x334

.field public static final SYNTH_VARIABLE_DECLARATION:I = 0x33e

.field private static final TEXTS:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/Integer;",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public static final TRUTH_VALUE:I = 0x533

.field public static final TYPE_DECLARATION:I = 0x578

.field public static final TYPE_LIST_TERMINATORS:I = 0x7d2

.field public static final TYPE_NAME:I = 0x58c

.field public static final UNKNOWN:I = 0x0

.field public static final UNSAFE_OVER_NEWLINES:I = 0x7d7


# direct methods
.method static constructor <clinit>()V
    .locals 5

    .line 1045
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    sput-object v0, Lorg/codehaus/groovy/syntax/Types;->TEXTS:Ljava/util/Map;

    .line 1046
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    sput-object v0, Lorg/codehaus/groovy/syntax/Types;->LOOKUP:Ljava/util/Map;

    .line 1047
    new-instance v1, Ljava/util/HashSet;

    invoke-direct {v1}, Ljava/util/HashSet;-><init>()V

    sput-object v1, Lorg/codehaus/groovy/syntax/Types;->KEYWORDS:Ljava/util/Set;

    const-string v1, "\n"

    const/4 v2, 0x5

    .line 1129
    invoke-static {v1, v2}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, "{"

    const/16 v3, 0xa

    .line 1131
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, "}"

    const/16 v3, 0x14

    .line 1132
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, "["

    const/16 v3, 0x1e

    .line 1133
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, "]"

    const/16 v3, 0x28

    .line 1134
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, "("

    const/16 v3, 0x32

    .line 1135
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, ")"

    const/16 v3, 0x3c

    .line 1136
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, "."

    const/16 v3, 0x46

    .line 1138
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, ".."

    const/16 v3, 0x4b

    .line 1139
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, "..."

    const/16 v3, 0x4d

    .line 1140
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, "->"

    const/16 v3, 0x50

    .line 1142
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, "=~"

    const/16 v3, 0x5a

    .line 1144
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, "==~"

    const/16 v3, 0x5e

    .line 1145
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, "~"

    const/16 v3, 0x61

    .line 1146
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, "="

    const/16 v3, 0x64

    .line 1148
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, "!="

    const/16 v3, 0x78

    .line 1150
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, "==="

    const/16 v3, 0x79

    .line 1151
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, "!=="

    const/16 v3, 0x7a

    .line 1152
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, "=="

    const/16 v3, 0x7b

    .line 1153
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, "<"

    const/16 v3, 0x7c

    .line 1154
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, "<="

    const/16 v3, 0x7d

    .line 1155
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, ">"

    const/16 v3, 0x7e

    .line 1156
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, ">="

    const/16 v3, 0x7f

    .line 1157
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, "<=>"

    const/16 v3, 0x80

    .line 1158
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, "!in"

    const/16 v3, 0x81

    .line 1159
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, "!instanceof"

    const/16 v3, 0x82

    .line 1160
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, "!"

    const/16 v3, 0xa0

    .line 1162
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, "||"

    const/16 v3, 0xa2

    .line 1163
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, "&&"

    const/16 v3, 0xa4

    .line 1164
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, "||="

    const/16 v3, 0xa6

    .line 1166
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, "&&="

    const/16 v3, 0xa8

    .line 1167
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, "+"

    const/16 v3, 0xc8

    .line 1169
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, "-"

    const/16 v3, 0xc9

    .line 1170
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, "*"

    const/16 v3, 0xca

    .line 1171
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, "/"

    const/16 v3, 0xcb

    .line 1172
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, "\\"

    const/16 v3, 0xcc

    .line 1173
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, "%"

    const/16 v3, 0xcd

    .line 1174
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, "**"

    const/16 v3, 0xce

    .line 1176
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, "+="

    const/16 v3, 0xd2

    .line 1177
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, "-="

    const/16 v3, 0xd3

    .line 1178
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, "*="

    const/16 v3, 0xd4

    .line 1179
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, "/="

    const/16 v3, 0xd5

    .line 1180
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, "\\="

    const/16 v3, 0xd6

    .line 1181
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, "%="

    const/16 v3, 0xd7

    .line 1182
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, "**="

    const/16 v3, 0xd8

    .line 1183
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, "?="

    const/16 v3, 0xd9

    .line 1184
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, "++"

    const/16 v3, 0xfa

    .line 1186
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, "--"

    const/16 v3, 0x104

    .line 1187
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, "<<"

    const/16 v3, 0x118

    .line 1189
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, ">>"

    const/16 v3, 0x119

    .line 1190
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, ">>>"

    const/16 v3, 0x11a

    .line 1191
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, "<<="

    const/16 v3, 0x11d

    .line 1193
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, ">>="

    const/16 v3, 0x11e

    .line 1194
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, ">>>="

    const/16 v3, 0x11f

    .line 1195
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, "&"

    const/16 v3, 0x155

    .line 1197
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, "^"

    const/16 v3, 0x156

    .line 1198
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, "|="

    const/16 v3, 0x15e

    .line 1200
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, "&="

    const/16 v3, 0x15f

    .line 1201
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, "^="

    const/16 v3, 0x160

    .line 1202
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, ","

    const/16 v3, 0x12c

    .line 1204
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, ":"

    const/16 v3, 0x136

    .line 1205
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, ";"

    const/16 v3, 0x140

    .line 1206
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, "?"

    const/16 v3, 0x14a

    .line 1207
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, "|"

    const/16 v3, 0x154

    .line 1208
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, "${}"

    const/16 v3, 0x387

    .line 1210
    invoke-static {v1, v3}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    const-string v1, "abstract"

    const/16 v4, 0x1fe

    .line 1216
    invoke-static {v1, v4}, Lorg/codehaus/groovy/syntax/Types;->addKeyword(Ljava/lang/String;I)V

    const-string v1, "as"

    const/16 v4, 0x228

    .line 1217
    invoke-static {v1, v4}, Lorg/codehaus/groovy/syntax/Types;->addKeyword(Ljava/lang/String;I)V

    const-string v1, "assert"

    const/16 v4, 0x249

    .line 1218
    invoke-static {v1, v4}, Lorg/codehaus/groovy/syntax/Types;->addKeyword(Ljava/lang/String;I)V

    const-string v1, "break"

    const/16 v4, 0x23e

    .line 1219
    invoke-static {v1, v4}, Lorg/codehaus/groovy/syntax/Types;->addKeyword(Ljava/lang/String;I)V

    const-string v1, "case"

    const/16 v4, 0x241

    .line 1220
    invoke-static {v1, v4}, Lorg/codehaus/groovy/syntax/Types;->addKeyword(Ljava/lang/String;I)V

    const-string v1, "catch"

    const/16 v4, 0x245

    .line 1221
    invoke-static {v1, v4}, Lorg/codehaus/groovy/syntax/Types;->addKeyword(Ljava/lang/String;I)V

    const-string v1, "class"

    const/16 v4, 0x213

    .line 1222
    invoke-static {v1, v4}, Lorg/codehaus/groovy/syntax/Types;->addKeyword(Ljava/lang/String;I)V

    const-string v1, "const"

    const/16 v4, 0x2bc

    .line 1223
    invoke-static {v1, v4}, Lorg/codehaus/groovy/syntax/Types;->addKeyword(Ljava/lang/String;I)V

    const-string v1, "continue"

    const/16 v4, 0x23f

    .line 1224
    invoke-static {v1, v4}, Lorg/codehaus/groovy/syntax/Types;->addKeyword(Ljava/lang/String;I)V

    const-string v1, "def"

    const/16 v4, 0x212

    .line 1225
    invoke-static {v1, v4}, Lorg/codehaus/groovy/syntax/Types;->addKeyword(Ljava/lang/String;I)V

    const-string v1, "defmacro"

    .line 1226
    invoke-static {v1, v4}, Lorg/codehaus/groovy/syntax/Types;->addKeyword(Ljava/lang/String;I)V

    const-string v1, "default"

    const/16 v4, 0x242

    .line 1227
    invoke-static {v1, v4}, Lorg/codehaus/groovy/syntax/Types;->addKeyword(Ljava/lang/String;I)V

    const-string v1, "do"

    const/16 v4, 0x23a

    .line 1228
    invoke-static {v1, v4}, Lorg/codehaus/groovy/syntax/Types;->addKeyword(Ljava/lang/String;I)V

    const-string v1, "else"

    const/16 v4, 0x232

    .line 1229
    invoke-static {v1, v4}, Lorg/codehaus/groovy/syntax/Types;->addKeyword(Ljava/lang/String;I)V

    const-string v1, "extends"

    const/16 v4, 0x21d

    .line 1230
    invoke-static {v1, v4}, Lorg/codehaus/groovy/syntax/Types;->addKeyword(Ljava/lang/String;I)V

    const-string v1, "final"

    const/16 v4, 0x1ff

    .line 1231
    invoke-static {v1, v4}, Lorg/codehaus/groovy/syntax/Types;->addKeyword(Ljava/lang/String;I)V

    const-string v1, "finally"

    const/16 v4, 0x246

    .line 1232
    invoke-static {v1, v4}, Lorg/codehaus/groovy/syntax/Types;->addKeyword(Ljava/lang/String;I)V

    const-string v1, "for"

    const/16 v4, 0x23c

    .line 1233
    invoke-static {v1, v4}, Lorg/codehaus/groovy/syntax/Types;->addKeyword(Ljava/lang/String;I)V

    const-string v1, "goto"

    const/16 v4, 0x2bd

    .line 1234
    invoke-static {v1, v4}, Lorg/codehaus/groovy/syntax/Types;->addKeyword(Ljava/lang/String;I)V

    const-string v1, "if"

    const/16 v4, 0x231

    .line 1235
    invoke-static {v1, v4}, Lorg/codehaus/groovy/syntax/Types;->addKeyword(Ljava/lang/String;I)V

    const-string v1, "in"

    const/16 v4, 0x23d

    .line 1236
    invoke-static {v1, v4}, Lorg/codehaus/groovy/syntax/Types;->addKeyword(Ljava/lang/String;I)V

    const-string v1, "implements"

    const/16 v4, 0x21c

    .line 1237
    invoke-static {v1, v4}, Lorg/codehaus/groovy/syntax/Types;->addKeyword(Ljava/lang/String;I)V

    const-string v1, "import"

    const/16 v4, 0x227

    .line 1238
    invoke-static {v1, v4}, Lorg/codehaus/groovy/syntax/Types;->addKeyword(Ljava/lang/String;I)V

    const-string v1, "instanceof"

    const/16 v4, 0x220

    .line 1239
    invoke-static {v1, v4}, Lorg/codehaus/groovy/syntax/Types;->addKeyword(Ljava/lang/String;I)V

    const-string v1, "interface"

    const/16 v4, 0x214

    .line 1240
    invoke-static {v1, v4}, Lorg/codehaus/groovy/syntax/Types;->addKeyword(Ljava/lang/String;I)V

    const-string v1, "mixin"

    const/16 v4, 0x215

    .line 1241
    invoke-static {v1, v4}, Lorg/codehaus/groovy/syntax/Types;->addKeyword(Ljava/lang/String;I)V

    const-string v1, "native"

    const/16 v4, 0x200

    .line 1242
    invoke-static {v1, v4}, Lorg/codehaus/groovy/syntax/Types;->addKeyword(Ljava/lang/String;I)V

    const-string v1, "new"

    const/16 v4, 0x222

    .line 1243
    invoke-static {v1, v4}, Lorg/codehaus/groovy/syntax/Types;->addKeyword(Ljava/lang/String;I)V

    const-string v1, "package"

    const/16 v4, 0x226

    .line 1244
    invoke-static {v1, v4}, Lorg/codehaus/groovy/syntax/Types;->addKeyword(Ljava/lang/String;I)V

    const-string v1, "private"

    const/16 v4, 0x1f4

    .line 1245
    invoke-static {v1, v4}, Lorg/codehaus/groovy/syntax/Types;->addKeyword(Ljava/lang/String;I)V

    const-string v1, "property"

    const/16 v4, 0x221

    .line 1246
    invoke-static {v1, v4}, Lorg/codehaus/groovy/syntax/Types;->addKeyword(Ljava/lang/String;I)V

    const-string v1, "protected"

    const/16 v4, 0x1f5

    .line 1247
    invoke-static {v1, v4}, Lorg/codehaus/groovy/syntax/Types;->addKeyword(Ljava/lang/String;I)V

    const-string v1, "public"

    const/16 v4, 0x1f6

    .line 1248
    invoke-static {v1, v4}, Lorg/codehaus/groovy/syntax/Types;->addKeyword(Ljava/lang/String;I)V

    const-string v1, "return"

    const/16 v4, 0x230

    .line 1249
    invoke-static {v1, v4}, Lorg/codehaus/groovy/syntax/Types;->addKeyword(Ljava/lang/String;I)V

    const-string v1, "static"

    const/16 v4, 0x209

    .line 1250
    invoke-static {v1, v4}, Lorg/codehaus/groovy/syntax/Types;->addKeyword(Ljava/lang/String;I)V

    const-string v1, "super"

    const/16 v4, 0x21f

    .line 1251
    invoke-static {v1, v4}, Lorg/codehaus/groovy/syntax/Types;->addKeyword(Ljava/lang/String;I)V

    const-string v1, "switch"

    const/16 v4, 0x240

    .line 1252
    invoke-static {v1, v4}, Lorg/codehaus/groovy/syntax/Types;->addKeyword(Ljava/lang/String;I)V

    const-string v1, "synchronized"

    const/16 v4, 0x208

    .line 1253
    invoke-static {v1, v4}, Lorg/codehaus/groovy/syntax/Types;->addKeyword(Ljava/lang/String;I)V

    const-string v1, "this"

    const/16 v4, 0x21e

    .line 1254
    invoke-static {v1, v4}, Lorg/codehaus/groovy/syntax/Types;->addKeyword(Ljava/lang/String;I)V

    const-string v1, "throw"

    const/16 v4, 0x247

    .line 1255
    invoke-static {v1, v4}, Lorg/codehaus/groovy/syntax/Types;->addKeyword(Ljava/lang/String;I)V

    const-string v1, "throws"

    const/16 v4, 0x248

    .line 1256
    invoke-static {v1, v4}, Lorg/codehaus/groovy/syntax/Types;->addKeyword(Ljava/lang/String;I)V

    const-string v1, "transient"

    const/16 v4, 0x201

    .line 1257
    invoke-static {v1, v4}, Lorg/codehaus/groovy/syntax/Types;->addKeyword(Ljava/lang/String;I)V

    const-string v1, "try"

    const/16 v4, 0x244

    .line 1258
    invoke-static {v1, v4}, Lorg/codehaus/groovy/syntax/Types;->addKeyword(Ljava/lang/String;I)V

    const-string v1, "volatile"

    const/16 v4, 0x202

    .line 1259
    invoke-static {v1, v4}, Lorg/codehaus/groovy/syntax/Types;->addKeyword(Ljava/lang/String;I)V

    const-string v1, "while"

    const/16 v4, 0x23b

    .line 1260
    invoke-static {v1, v4}, Lorg/codehaus/groovy/syntax/Types;->addKeyword(Ljava/lang/String;I)V

    const-string v1, "true"

    const/16 v4, 0x262

    .line 1261
    invoke-static {v1, v4}, Lorg/codehaus/groovy/syntax/Types;->addKeyword(Ljava/lang/String;I)V

    const-string v1, "false"

    const/16 v4, 0x263

    .line 1262
    invoke-static {v1, v4}, Lorg/codehaus/groovy/syntax/Types;->addKeyword(Ljava/lang/String;I)V

    const-string v1, "null"

    const/16 v4, 0x264

    .line 1263
    invoke-static {v1, v4}, Lorg/codehaus/groovy/syntax/Types;->addKeyword(Ljava/lang/String;I)V

    const-string v1, "void"

    const/16 v4, 0x258

    .line 1264
    invoke-static {v1, v4}, Lorg/codehaus/groovy/syntax/Types;->addKeyword(Ljava/lang/String;I)V

    const-string v1, "boolean"

    const/16 v4, 0x259

    .line 1265
    invoke-static {v1, v4}, Lorg/codehaus/groovy/syntax/Types;->addKeyword(Ljava/lang/String;I)V

    const-string v1, "byte"

    const/16 v4, 0x25a

    .line 1266
    invoke-static {v1, v4}, Lorg/codehaus/groovy/syntax/Types;->addKeyword(Ljava/lang/String;I)V

    const-string v1, "int"

    const/16 v4, 0x25c

    .line 1267
    invoke-static {v1, v4}, Lorg/codehaus/groovy/syntax/Types;->addKeyword(Ljava/lang/String;I)V

    const-string v1, "short"

    const/16 v4, 0x25b

    .line 1268
    invoke-static {v1, v4}, Lorg/codehaus/groovy/syntax/Types;->addKeyword(Ljava/lang/String;I)V

    const-string v1, "long"

    const/16 v4, 0x25d

    .line 1269
    invoke-static {v1, v4}, Lorg/codehaus/groovy/syntax/Types;->addKeyword(Ljava/lang/String;I)V

    const-string v1, "float"

    const/16 v4, 0x25e

    .line 1270
    invoke-static {v1, v4}, Lorg/codehaus/groovy/syntax/Types;->addKeyword(Ljava/lang/String;I)V

    const-string v1, "double"

    const/16 v4, 0x25f

    .line 1271
    invoke-static {v1, v4}, Lorg/codehaus/groovy/syntax/Types;->addKeyword(Ljava/lang/String;I)V

    const-string v1, "char"

    const/16 v4, 0x260

    .line 1272
    invoke-static {v1, v4}, Lorg/codehaus/groovy/syntax/Types;->addKeyword(Ljava/lang/String;I)V

    .line 1280
    new-instance v1, Ljava/util/HashMap;

    invoke-direct {v1}, Ljava/util/HashMap;-><init>()V

    sput-object v1, Lorg/codehaus/groovy/syntax/Types;->DESCRIPTIONS:Ljava/util/Map;

    .line 1308
    invoke-interface {v0}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/util/Map$Entry;

    .line 1309
    invoke-interface {v1}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Ljava/lang/Integer;

    invoke-virtual {v4}, Ljava/lang/Integer;->intValue()I

    move-result v4

    invoke-interface {v1}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/String;

    invoke-static {v4, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    goto :goto_0

    :cond_0
    const-string v0, "<newline>"

    .line 1312
    invoke-static {v2, v0}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0xfb

    const-string v1, "<prefix ++>"

    .line 1313
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0xfc

    const-string v1, "<postfix ++>"

    .line 1314
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0x105

    const-string v1, "<prefix -->"

    .line 1315
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0x106

    const-string v1, "<postfix -->"

    .line 1316
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0xfd

    const-string v1, "<positive>"

    .line 1317
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0x107

    const-string v1, "<negative>"

    .line 1318
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0x190

    const-string v1, "<string literal>"

    .line 1320
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0x1b8

    const-string v1, "<identifier>"

    .line 1321
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0x1c2

    const-string v1, "<integer>"

    .line 1322
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0x1c3

    const-string v1, "<decimal>"

    .line 1323
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0x320

    const-string v1, "<compilation unit>"

    .line 1325
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0x321

    const-string v1, "<class>"

    .line 1326
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0x322

    const-string v1, "<interface>"

    .line 1327
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0x323

    const-string v1, "<mixin>"

    .line 1328
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0x324

    const-string v1, "<method>"

    .line 1329
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0x32e

    const-string v1, "<method call>"

    .line 1330
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0x325

    const-string v1, "<property>"

    .line 1331
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0x326

    const-string v1, "<parameter>"

    .line 1332
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0x32a

    const-string v1, "<list>"

    .line 1333
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0x32b

    const-string v1, "<map>"

    .line 1334
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0x334

    const-string v1, "<tuple>"

    .line 1335
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0x32c

    const-string v1, "<gstring>"

    .line 1336
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0x32f

    const-string v1, "<cast>"

    .line 1337
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0x330

    const-string v1, "<block>"

    .line 1338
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0x331

    const-string v1, "<closure>"

    .line 1339
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0x333

    const-string v1, "<ternary>"

    .line 1340
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0x332

    const-string v1, "<label>"

    .line 1341
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0x33e

    const-string v1, "<variable declaration>"

    .line 1342
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0x385

    const-string v1, "<start of gstring tokens>"

    .line 1344
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0x386

    const-string v1, "<end of gstring tokens>"

    .line 1345
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const-string v0, "<start of gstring expression>"

    .line 1346
    invoke-static {v3, v0}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0x388

    const-string v1, "<end of gstring expression>"

    .line 1347
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0x44c

    const-string v1, "<assignment operator>"

    .line 1349
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0x44d

    const-string v1, "<comparison operator>"

    .line 1350
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0x44e

    const-string v1, "<math operator>"

    .line 1351
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0x44f

    const-string v1, "<logical operator>"

    .line 1352
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0x453

    const-string v1, "<bitwise operator>"

    .line 1353
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0x450

    const-string v1, "<range operator>"

    .line 1354
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0x451

    const-string v1, "<regex comparison operator>"

    .line 1355
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0x452

    const-string v1, "<dereference operator>"

    .line 1356
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0x4b0

    const-string v1, "<prefix operator>"

    .line 1357
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0x4ba

    const-string v1, "<postfix operator>"

    .line 1358
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0x4c4

    const-string v1, "<infix operator>"

    .line 1359
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0x514

    const-string v1, "<keyword>"

    .line 1360
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0x51e

    const-string v1, "<literal>"

    .line 1361
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0x528

    const-string v1, "<number>"

    .line 1362
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0x532

    const-string v1, "<named value>"

    .line 1363
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0x533

    const-string v1, "<truth value>"

    .line 1364
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0x53c

    const-string v1, "<primitive type>"

    .line 1365
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0x53d

    const-string v1, "<creatable primitive type>"

    .line 1366
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0x546

    const-string v1, "<loop>"

    .line 1367
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0x550

    const-string v1, "<reserved keyword>"

    .line 1368
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0x55a

    const-string v1, "<synthetic>"

    .line 1369
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0x578

    const-string v1, "<type declaration>"

    .line 1370
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0x582

    const-string v1, "<declaration modifier>"

    .line 1371
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0x58c

    const-string v1, "<type name>"

    .line 1372
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0x596

    const-string v1, "<creatable type name>"

    .line 1373
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0x5dc

    const-string v1, "<matched container>"

    .line 1374
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0x5dd

    const-string v1, "<left of matched container>"

    .line 1375
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0x5de

    const-string v1, "<right of matched container>"

    .line 1376
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    const/16 v0, 0x7d5

    const-string v1, "<valid in a switch body>"

    .line 1377
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->addDescription(ILjava/lang/String;)V

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 35
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method private static addDescription(ILjava/lang/String;)V
    .locals 3

    const-string v0, "<"

    .line 1299
    invoke-virtual {p1, v0}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_0

    const-string v0, ">"

    invoke-virtual {p1, v0}, Ljava/lang/String;->endsWith(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 1300
    sget-object v0, Lorg/codehaus/groovy/syntax/Types;->DESCRIPTIONS:Ljava/util/Map;

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {v0, p0, p1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    .line 1302
    :cond_0
    sget-object v0, Lorg/codehaus/groovy/syntax/Types;->DESCRIPTIONS:Ljava/util/Map;

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const/16 v2, 0x22

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v2}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-interface {v0, p0, p1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :goto_0
    return-void
.end method

.method private static addKeyword(Ljava/lang/String;I)V
    .locals 1

    .line 1120
    sget-object v0, Lorg/codehaus/groovy/syntax/Types;->KEYWORDS:Ljava/util/Set;

    invoke-interface {v0, p0}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 1121
    invoke-static {p0, p1}, Lorg/codehaus/groovy/syntax/Types;->addTranslation(Ljava/lang/String;I)V

    return-void
.end method

.method private static addTranslation(Ljava/lang/String;I)V
    .locals 2

    .line 1112
    sget-object v0, Lorg/codehaus/groovy/syntax/Types;->TEXTS:Ljava/util/Map;

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    invoke-interface {v0, v1, p0}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1113
    sget-object v0, Lorg/codehaus/groovy/syntax/Types;->LOOKUP:Ljava/util/Map;

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    invoke-interface {v0, p0, p1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method public static canMean(II)Z
    .locals 4

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    :cond_0
    const/16 v1, 0x1b8

    const/4 v2, 0x0

    if-eq p1, v1, :cond_8

    const/16 v3, 0x33e

    if-eq p1, v3, :cond_6

    const/16 v3, 0x32a

    if-eq p1, v3, :cond_4

    const/16 v3, 0x32b

    if-eq p1, v3, :cond_4

    packed-switch p1, :pswitch_data_0

    packed-switch p1, :pswitch_data_1

    goto :goto_5

    :pswitch_0
    const/16 p1, 0x136

    if-ne p0, p1, :cond_1

    goto :goto_0

    :cond_1
    move v0, v2

    :goto_0
    return v0

    :pswitch_1
    const/16 p1, 0xa

    if-ne p0, p1, :cond_2

    goto :goto_1

    :cond_2
    move v0, v2

    :goto_1
    return v0

    :pswitch_2
    const/16 p1, 0x32

    if-ne p0, p1, :cond_3

    goto :goto_2

    :cond_3
    move v0, v2

    :goto_2
    return v0

    :cond_4
    const/16 p1, 0x1e

    if-ne p0, p1, :cond_5

    goto :goto_3

    :cond_5
    move v0, v2

    :goto_3
    return v0

    :cond_6
    :pswitch_3
    if-ne p0, v1, :cond_7

    goto :goto_4

    :cond_7
    move v0, v2

    :goto_4
    return v0

    :cond_8
    :pswitch_4
    if-eq p0, v1, :cond_9

    const/16 p1, 0x21b

    if-eq p0, p1, :cond_9

    packed-switch p0, :pswitch_data_2

    :goto_5
    return v2

    :cond_9
    :pswitch_5
    return v0

    :pswitch_data_0
    .packed-switch 0x321
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_4
    .end packed-switch

    :pswitch_data_1
    .packed-switch 0x32f
        :pswitch_2
        :pswitch_1
        :pswitch_1
        :pswitch_0
    .end packed-switch

    :pswitch_data_2
    .packed-switch 0x212
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
    .end packed-switch
.end method

.method public static getDescription(I)Ljava/lang/String;
    .locals 2

    .line 1287
    sget-object v0, Lorg/codehaus/groovy/syntax/Types;->DESCRIPTIONS:Ljava/util/Map;

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/util/Map;->containsKey(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_0

    .line 1288
    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {v0, p0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Ljava/lang/String;

    return-object p0

    :cond_0
    const-string p0, "<>"

    return-object p0
.end method

.method public static getKeywords()Ljava/util/Collection;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Collection<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .line 1050
    sget-object v0, Lorg/codehaus/groovy/syntax/Types;->KEYWORDS:Ljava/util/Set;

    invoke-static {v0}, Ljava/util/Collections;->unmodifiableSet(Ljava/util/Set;)Ljava/util/Set;

    move-result-object v0

    return-object v0
.end method

.method public static getPrecedence(IZ)I
    .locals 0

    sparse-switch p0, :sswitch_data_0

    packed-switch p0, :pswitch_data_0

    packed-switch p0, :pswitch_data_1

    packed-switch p0, :pswitch_data_2

    packed-switch p0, :pswitch_data_3

    packed-switch p0, :pswitch_data_4

    packed-switch p0, :pswitch_data_5

    packed-switch p0, :pswitch_data_6

    packed-switch p0, :pswitch_data_7

    packed-switch p0, :pswitch_data_8

    if-nez p1, :cond_0

    const/4 p0, -0x1

    return p0

    .line 1035
    :cond_0
    new-instance p0, Lorg/codehaus/groovy/GroovyBugError;

    const-string p1, "precedence requested for non-operator"

    invoke-direct {p0, p1}, Lorg/codehaus/groovy/GroovyBugError;-><init>(Ljava/lang/String;)V

    throw p0

    :pswitch_0
    const/16 p0, 0x16

    return p0

    :pswitch_1
    const/16 p0, 0x23

    return p0

    :pswitch_2
    const/16 p0, 0x46

    return p0

    :pswitch_3
    const/16 p0, 0x41

    return p0

    :pswitch_4
    const/16 p0, 0x48

    return p0

    :pswitch_5
    const/16 p0, 0x2d

    return p0

    :pswitch_6
    const/16 p0, 0x28

    return p0

    :pswitch_7
    const/16 p0, 0x18

    return p0

    :sswitch_0
    const/16 p0, 0x37

    return p0

    :sswitch_1
    const/16 p0, 0x55

    return p0

    :sswitch_2
    const/16 p0, 0xa

    return p0

    :sswitch_3
    const/16 p0, 0x14

    return p0

    :sswitch_4
    const/16 p0, 0xf

    return p0

    :pswitch_8
    :sswitch_5
    const/4 p0, 0x5

    return p0

    :sswitch_6
    const/16 p0, 0x32

    return p0

    :pswitch_9
    :sswitch_7
    const/16 p0, 0x19

    return p0

    :sswitch_8
    const/16 p0, 0x1e

    return p0

    :sswitch_9
    const/16 p0, 0x50

    return p0

    :sswitch_a
    const/4 p0, 0x0

    return p0

    :sswitch_b
    const/16 p0, 0x4b

    return p0

    :sswitch_data_0
    .sparse-switch
        0x1e -> :sswitch_b
        0x32 -> :sswitch_a
        0x46 -> :sswitch_9
        0x4b -> :sswitch_8
        0x4d -> :sswitch_8
        0x50 -> :sswitch_9
        0x5a -> :sswitch_7
        0x5e -> :sswitch_7
        0x61 -> :sswitch_6
        0x64 -> :sswitch_5
        0x82 -> :sswitch_7
        0xa0 -> :sswitch_6
        0xa2 -> :sswitch_4
        0xa4 -> :sswitch_3
        0xa6 -> :sswitch_5
        0xa8 -> :sswitch_5
        0x14a -> :sswitch_2
        0x220 -> :sswitch_7
        0x222 -> :sswitch_1
        0x324 -> :sswitch_b
        0x32f -> :sswitch_0
    .end sparse-switch

    :pswitch_data_0
    .packed-switch 0x78
        :pswitch_9
        :pswitch_7
        :pswitch_7
        :pswitch_9
        :pswitch_9
        :pswitch_9
        :pswitch_9
        :pswitch_9
        :pswitch_9
    .end packed-switch

    :pswitch_data_1
    .packed-switch 0xc8
        :pswitch_6
        :pswitch_6
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_4
    .end packed-switch

    :pswitch_data_2
    .packed-switch 0xd2
        :pswitch_8
        :pswitch_8
        :pswitch_8
        :pswitch_8
        :pswitch_8
        :pswitch_8
        :pswitch_8
        :pswitch_8
    .end packed-switch

    :pswitch_data_3
    .packed-switch 0xfa
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_2
    .end packed-switch

    :pswitch_data_4
    .packed-switch 0x104
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_2
    .end packed-switch

    :pswitch_data_5
    .packed-switch 0x118
        :pswitch_1
        :pswitch_1
        :pswitch_1
    .end packed-switch

    :pswitch_data_6
    .packed-switch 0x11d
        :pswitch_8
        :pswitch_8
        :pswitch_8
    .end packed-switch

    :pswitch_data_7
    .packed-switch 0x154
        :pswitch_0
        :pswitch_0
        :pswitch_0
    .end packed-switch

    :pswitch_data_8
    .packed-switch 0x15e
        :pswitch_8
        :pswitch_8
        :pswitch_8
    .end packed-switch
.end method

.method public static getText(I)Ljava/lang/String;
    .locals 2

    .line 1100
    sget-object v0, Lorg/codehaus/groovy/syntax/Types;->TEXTS:Ljava/util/Map;

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/util/Map;->containsKey(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_0

    .line 1101
    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {v0, p0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Ljava/lang/String;

    goto :goto_0

    :cond_0
    const-string p0, ""

    :goto_0
    return-object p0
.end method

.method public static isAssignment(I)Z
    .locals 1

    const/16 v0, 0x44c

    .line 375
    invoke-static {p0, v0}, Lorg/codehaus/groovy/syntax/Types;->ofType(II)Z

    move-result p0

    return p0
.end method

.method public static isKeyword(Ljava/lang/String;)Z
    .locals 1

    .line 1054
    sget-object v0, Lorg/codehaus/groovy/syntax/Types;->KEYWORDS:Ljava/util/Set;

    invoke-interface {v0, p0}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result p0

    return p0
.end method

.method public static lookup(Ljava/lang/String;I)I
    .locals 3

    .line 1064
    sget-object v0, Lorg/codehaus/groovy/syntax/Types;->LOOKUP:Ljava/util/Map;

    invoke-interface {v0, p0}, Ljava/util/Map;->containsKey(Ljava/lang/Object;)Z

    move-result v1

    const/4 v2, 0x0

    if-eqz v1, :cond_1

    .line 1065
    invoke-interface {v0, p0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Ljava/lang/Integer;

    invoke-virtual {p0}, Ljava/lang/Integer;->intValue()I

    move-result p0

    if-eqz p1, :cond_0

    .line 1066
    invoke-static {p0, p1}, Lorg/codehaus/groovy/syntax/Types;->ofType(II)Z

    move-result p1

    if-nez p1, :cond_0

    goto :goto_0

    :cond_0
    move v2, p0

    :cond_1
    :goto_0
    return v2
.end method

.method public static lookupKeyword(Ljava/lang/String;)I
    .locals 1

    const/16 v0, 0x514

    .line 1080
    invoke-static {p0, v0}, Lorg/codehaus/groovy/syntax/Types;->lookup(Ljava/lang/String;I)I

    move-result p0

    return p0
.end method

.method public static lookupSymbol(Ljava/lang/String;)I
    .locals 1

    const/16 v0, 0x515

    .line 1089
    invoke-static {p0, v0}, Lorg/codehaus/groovy/syntax/Types;->lookup(Ljava/lang/String;I)I

    move-result p0

    return p0
.end method

.method public static makePostfix(Lorg/codehaus/groovy/syntax/CSTNode;Z)V
    .locals 2

    .line 898
    invoke-virtual {p0}, Lorg/codehaus/groovy/syntax/CSTNode;->getMeaning()I

    move-result v0

    const/16 v1, 0xfa

    if-eq v0, v1, :cond_2

    const/16 v1, 0x104

    if-eq v0, v1, :cond_1

    if-nez p1, :cond_0

    goto :goto_0

    .line 909
    :cond_0
    new-instance p1, Lorg/codehaus/groovy/GroovyBugError;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "cannot convert to postfix for type ["

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {p0}, Lorg/codehaus/groovy/syntax/CSTNode;->getMeaning()I

    move-result p0

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p0

    const-string v0, "]"

    invoke-virtual {p0, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    invoke-direct {p1, p0}, Lorg/codehaus/groovy/GroovyBugError;-><init>(Ljava/lang/String;)V

    throw p1

    :cond_1
    const/16 p1, 0x106

    .line 904
    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/syntax/CSTNode;->setMeaning(I)Lorg/codehaus/groovy/syntax/CSTNode;

    goto :goto_0

    :cond_2
    const/16 p1, 0xfc

    .line 900
    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/syntax/CSTNode;->setMeaning(I)Lorg/codehaus/groovy/syntax/CSTNode;

    :goto_0
    return-void
.end method

.method public static makePrefix(Lorg/codehaus/groovy/syntax/CSTNode;Z)V
    .locals 2

    .line 866
    invoke-virtual {p0}, Lorg/codehaus/groovy/syntax/CSTNode;->getMeaning()I

    move-result v0

    const/16 v1, 0xc8

    if-eq v0, v1, :cond_4

    const/16 v1, 0xc9

    if-eq v0, v1, :cond_3

    const/16 v1, 0xfa

    if-eq v0, v1, :cond_2

    const/16 v1, 0x104

    if-eq v0, v1, :cond_1

    if-nez p1, :cond_0

    goto :goto_0

    .line 885
    :cond_0
    new-instance p1, Lorg/codehaus/groovy/GroovyBugError;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "cannot convert to prefix for type ["

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {p0}, Lorg/codehaus/groovy/syntax/CSTNode;->getMeaning()I

    move-result p0

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p0

    const-string v0, "]"

    invoke-virtual {p0, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    invoke-direct {p1, p0}, Lorg/codehaus/groovy/GroovyBugError;-><init>(Ljava/lang/String;)V

    throw p1

    :cond_1
    const/16 p1, 0x105

    .line 880
    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/syntax/CSTNode;->setMeaning(I)Lorg/codehaus/groovy/syntax/CSTNode;

    goto :goto_0

    :cond_2
    const/16 p1, 0xfb

    .line 876
    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/syntax/CSTNode;->setMeaning(I)Lorg/codehaus/groovy/syntax/CSTNode;

    goto :goto_0

    :cond_3
    const/16 p1, 0x107

    .line 872
    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/syntax/CSTNode;->setMeaning(I)Lorg/codehaus/groovy/syntax/CSTNode;

    goto :goto_0

    :cond_4
    const/16 p1, 0xfd

    .line 868
    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/syntax/CSTNode;->setMeaning(I)Lorg/codehaus/groovy/syntax/CSTNode;

    :goto_0
    return-void
.end method

.method public static ofType(II)Z
    .locals 16

    move/from16 v0, p0

    move/from16 v1, p1

    const/4 v2, 0x1

    if-ne v1, v0, :cond_0

    return v2

    :cond_0
    const/16 v3, 0x514

    const/4 v4, 0x0

    if-eq v1, v3, :cond_58

    const/16 v3, 0x515

    const/4 v6, 0x5

    if-eq v1, v3, :cond_56

    const/16 v3, 0x532

    if-eq v1, v3, :cond_54

    const/16 v3, 0x533

    if-eq v1, v3, :cond_51

    const/16 v3, 0x53c

    if-eq v1, v3, :cond_4f

    const/16 v3, 0x53d

    if-eq v1, v3, :cond_4d

    const/16 v3, 0x550

    if-eq v1, v3, :cond_4b

    const/16 v3, 0x551

    if-eq v1, v3, :cond_49

    const/16 v3, 0x776

    if-eq v1, v3, :cond_46

    const/16 v3, 0x777

    if-eq v1, v3, :cond_44

    packed-switch v1, :pswitch_data_0

    const/16 v6, 0x104

    const/16 v10, 0xfa

    const/16 v11, 0x220

    const/16 v13, 0x107

    const/16 v14, 0xfd

    const/16 v15, 0x61

    const/16 v5, 0xa0

    const/16 v12, 0xc9

    const/16 v3, 0xc8

    sparse-switch v1, :sswitch_data_0

    const/16 v9, 0xa

    const/16 v8, 0x32

    const/16 v7, 0x1e

    packed-switch v1, :pswitch_data_1

    packed-switch v1, :pswitch_data_2

    packed-switch v1, :pswitch_data_3

    goto/16 :goto_17

    :pswitch_0
    if-eq v0, v8, :cond_1

    const/16 v1, 0xfb

    if-eq v0, v1, :cond_1

    if-eq v0, v14, :cond_1

    const/16 v1, 0x105

    if-eq v0, v1, :cond_1

    if-eq v0, v13, :cond_1

    if-eq v0, v3, :cond_1

    if-eq v0, v12, :cond_1

    const/16 v1, 0x777

    .line 709
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->ofType(II)Z

    move-result v0

    xor-int/2addr v0, v2

    return v0

    :cond_1
    return v4

    :pswitch_1
    const/16 v1, 0x515

    .line 670
    invoke-static {v0, v1}, Lorg/codehaus/groovy/syntax/Types;->ofType(II)Z

    move-result v1

    if-eqz v1, :cond_3

    if-eq v0, v9, :cond_2

    if-eq v0, v7, :cond_2

    if-eq v0, v8, :cond_2

    if-eq v0, v15, :cond_2

    if-eq v0, v5, :cond_2

    if-eq v0, v10, :cond_2

    if-eq v0, v6, :cond_2

    if-eq v0, v3, :cond_2

    if-eq v0, v12, :cond_2

    return v4

    :cond_2
    return v2

    :cond_3
    if-eq v0, v11, :cond_4

    packed-switch v0, :pswitch_data_4

    return v2

    :cond_4
    :pswitch_2
    return v4

    :pswitch_3
    const/16 v1, 0x190

    if-lt v0, v1, :cond_5

    const/16 v1, 0x1c3

    if-gt v0, v1, :cond_5

    return v2

    :cond_5
    if-eq v0, v8, :cond_6

    const/16 v1, 0x222

    if-eq v0, v1, :cond_6

    const/16 v1, 0x32c

    if-eq v0, v1, :cond_6

    const/16 v1, 0x385

    if-eq v0, v1, :cond_6

    goto/16 :goto_17

    :cond_6
    return v2

    :pswitch_4
    const/16 v1, 0x14

    if-ne v0, v1, :cond_7

    return v2

    :cond_7
    :pswitch_5
    const/16 v1, 0x241

    if-eq v0, v1, :cond_9

    const/16 v1, 0x242

    if-ne v0, v1, :cond_8

    goto :goto_0

    :cond_8
    move v2, v4

    :cond_9
    :goto_0
    return v2

    :pswitch_6
    if-eq v0, v7, :cond_a

    const/16 v1, 0x46

    if-eq v0, v1, :cond_a

    const/16 v1, 0x1b8

    if-eq v0, v1, :cond_a

    goto/16 :goto_17

    :cond_a
    return v2

    :pswitch_7
    if-eq v0, v9, :cond_b

    const/16 v1, 0x12c

    if-eq v0, v1, :cond_b

    const/16 v1, 0x21c

    if-eq v0, v1, :cond_b

    const/16 v1, 0x248

    if-eq v0, v1, :cond_b

    goto/16 :goto_17

    :cond_b
    return v2

    :pswitch_8
    const/16 v1, 0x28

    if-eq v0, v1, :cond_d

    const/16 v1, 0x12c

    if-ne v0, v1, :cond_c

    goto :goto_1

    :cond_c
    move v2, v4

    :cond_d
    :goto_1
    return v2

    :pswitch_9
    const/16 v1, 0x3c

    if-eq v0, v1, :cond_f

    const/16 v1, 0x12c

    if-ne v0, v1, :cond_e

    goto :goto_2

    :cond_e
    move v2, v4

    :cond_f
    :goto_2
    return v2

    :pswitch_a
    if-ne v0, v7, :cond_10

    goto :goto_3

    :cond_10
    move v2, v4

    :goto_3
    return v2

    :pswitch_b
    :sswitch_0
    const/16 v1, 0x1c3

    const/16 v3, 0x190

    goto/16 :goto_9

    :pswitch_c
    const/16 v1, 0x222

    if-eq v0, v1, :cond_11

    packed-switch v0, :pswitch_data_5

    packed-switch v0, :pswitch_data_6

    goto/16 :goto_17

    :cond_11
    :pswitch_d
    return v2

    :pswitch_e
    const/16 v1, 0x32f

    if-eq v0, v1, :cond_12

    const/16 v1, 0x331

    if-eq v0, v1, :cond_12

    const/16 v1, 0x333

    if-eq v0, v1, :cond_12

    goto/16 :goto_17

    :cond_12
    return v2

    :pswitch_f
    const/16 v1, 0x46

    if-lt v0, v1, :cond_13

    const/16 v1, 0x11a

    if-gt v0, v1, :cond_13

    goto :goto_4

    :cond_13
    move v2, v4

    :goto_4
    return v2

    :pswitch_10
    const/16 v1, 0x46

    if-lt v0, v1, :cond_14

    const/16 v1, 0x11a

    if-gt v0, v1, :cond_14

    return v2

    :cond_14
    const/16 v1, 0x190

    if-lt v0, v1, :cond_15

    const/16 v1, 0x1c3

    if-gt v0, v1, :cond_15

    return v2

    :cond_15
    if-eq v0, v7, :cond_16

    const/16 v1, 0x222

    if-eq v0, v1, :cond_16

    const/16 v1, 0x32c

    if-eq v0, v1, :cond_16

    const/16 v1, 0x32f

    if-eq v0, v1, :cond_16

    const/16 v1, 0x331

    if-eq v0, v1, :cond_16

    const/16 v1, 0x333

    if-eq v0, v1, :cond_16

    packed-switch v0, :pswitch_data_7

    packed-switch v0, :pswitch_data_8

    goto/16 :goto_17

    :cond_16
    :pswitch_11
    return v2

    :pswitch_12
    const/16 v1, 0x14

    if-eq v0, v1, :cond_17

    const/16 v1, 0x28

    if-eq v0, v1, :cond_17

    const/16 v1, 0x3c

    if-eq v0, v1, :cond_17

    goto/16 :goto_17

    :cond_17
    return v2

    :pswitch_13
    if-eq v0, v9, :cond_18

    if-eq v0, v7, :cond_18

    if-eq v0, v8, :cond_18

    goto/16 :goto_17

    :cond_18
    return v2

    :pswitch_14
    if-eq v0, v9, :cond_19

    const/16 v1, 0x14

    if-eq v0, v1, :cond_19

    if-eq v0, v7, :cond_19

    const/16 v1, 0x28

    if-eq v0, v1, :cond_19

    if-eq v0, v8, :cond_19

    const/16 v1, 0x3c

    if-eq v0, v1, :cond_19

    goto/16 :goto_17

    :cond_19
    return v2

    :sswitch_1
    const/16 v1, 0x1b8

    if-ne v0, v1, :cond_4f

    return v2

    :sswitch_2
    const/16 v1, 0x1f4

    if-lt v0, v1, :cond_1a

    const/16 v1, 0x209

    if-gt v0, v1, :cond_1a

    goto :goto_5

    :cond_1a
    move v2, v4

    :goto_5
    return v2

    :sswitch_3
    const/16 v1, 0x213

    if-lt v0, v1, :cond_1b

    const/16 v1, 0x215

    if-gt v0, v1, :cond_1b

    goto :goto_6

    :cond_1b
    move v2, v4

    :goto_6
    return v2

    :sswitch_4
    const/16 v1, 0x320

    if-lt v0, v1, :cond_1c

    const/16 v1, 0x33e

    if-gt v0, v1, :cond_1c

    goto :goto_7

    :cond_1c
    move v2, v4

    :goto_7
    return v2

    :sswitch_5
    packed-switch v0, :pswitch_data_9

    goto/16 :goto_17

    :pswitch_15
    return v2

    :sswitch_6
    if-eq v0, v3, :cond_1d

    if-eq v0, v12, :cond_1d

    goto/16 :goto_17

    :cond_1d
    return v2

    :sswitch_7
    const/16 v1, 0x1c2

    if-eq v0, v1, :cond_1f

    const/16 v1, 0x1c3

    if-ne v0, v1, :cond_1e

    goto :goto_8

    :cond_1e
    move v2, v4

    :cond_1f
    :goto_8
    return v2

    :goto_9
    if-lt v0, v3, :cond_20

    if-gt v0, v1, :cond_20

    goto :goto_a

    :cond_20
    move v2, v4

    :goto_a
    return v2

    :sswitch_8
    if-eq v0, v3, :cond_21

    if-eq v0, v12, :cond_21

    const/16 v1, 0xce

    if-eq v0, v1, :cond_21

    if-eq v0, v14, :cond_21

    if-eq v0, v13, :cond_21

    goto/16 :goto_17

    :cond_21
    return v2

    :sswitch_9
    const/16 v1, 0x46

    if-eq v0, v1, :cond_28

    const/16 v1, 0x4b

    if-eq v0, v1, :cond_28

    const/16 v1, 0x4d

    if-eq v0, v1, :cond_28

    const/16 v1, 0x50

    if-eq v0, v1, :cond_28

    const/16 v1, 0x5a

    if-eq v0, v1, :cond_28

    const/16 v1, 0x5e

    if-eq v0, v1, :cond_28

    const/16 v1, 0xa2

    if-eq v0, v1, :cond_28

    const/16 v1, 0xa4

    if-eq v0, v1, :cond_28

    if-eq v0, v11, :cond_28

    packed-switch v0, :pswitch_data_a

    packed-switch v0, :pswitch_data_b

    const/16 v1, 0x78

    if-lt v0, v1, :cond_22

    const/16 v1, 0x80

    if-le v0, v1, :cond_28

    :cond_22
    if-lt v0, v3, :cond_23

    const/16 v1, 0xd7

    if-le v0, v1, :cond_28

    :cond_23
    const/16 v1, 0x64

    if-eq v0, v1, :cond_28

    const/16 v1, 0xd2

    if-lt v0, v1, :cond_24

    const/16 v1, 0xd9

    if-le v0, v1, :cond_28

    :cond_24
    const/16 v1, 0xa6

    if-lt v0, v1, :cond_25

    const/16 v1, 0xa8

    if-le v0, v1, :cond_28

    :cond_25
    const/16 v1, 0x11d

    if-lt v0, v1, :cond_26

    const/16 v1, 0x11f

    if-le v0, v1, :cond_28

    :cond_26
    const/16 v1, 0x15e

    if-lt v0, v1, :cond_27

    const/16 v1, 0x160

    if-gt v0, v1, :cond_27

    goto :goto_b

    :cond_27
    move v2, v4

    :cond_28
    :goto_b
    :pswitch_16
    return v2

    :sswitch_a
    if-eq v0, v10, :cond_29

    const/16 v1, 0xfc

    if-eq v0, v1, :cond_29

    if-eq v0, v6, :cond_29

    const/16 v1, 0x106

    if-eq v0, v1, :cond_29

    goto/16 :goto_17

    :cond_29
    return v2

    :sswitch_b
    if-eq v0, v12, :cond_2a

    if-eq v0, v10, :cond_2a

    if-eq v0, v6, :cond_2a

    :sswitch_c
    if-eq v0, v15, :cond_2a

    if-eq v0, v5, :cond_2a

    const/16 v1, 0xfb

    if-eq v0, v1, :cond_2a

    if-eq v0, v14, :cond_2a

    const/16 v1, 0x105

    if-eq v0, v1, :cond_2a

    if-eq v0, v13, :cond_2a

    const/16 v1, 0x32f

    if-eq v0, v1, :cond_2a

    goto/16 :goto_17

    :cond_2a
    return v2

    :sswitch_d
    if-eq v0, v11, :cond_2c

    const/16 v1, 0x82

    if-ne v0, v1, :cond_2b

    goto :goto_c

    :cond_2b
    move v2, v4

    :cond_2c
    :goto_c
    return v2

    :sswitch_e
    const/16 v1, 0x154

    if-lt v0, v1, :cond_2d

    const/16 v1, 0x156

    if-le v0, v1, :cond_2f

    :cond_2d
    if-ne v0, v15, :cond_2e

    goto :goto_d

    :cond_2e
    move v2, v4

    :cond_2f
    :goto_d
    return v2

    :sswitch_f
    const/16 v1, 0x46

    if-eq v0, v1, :cond_31

    const/16 v1, 0x50

    if-ne v0, v1, :cond_30

    goto :goto_e

    :cond_30
    move v2, v4

    :cond_31
    :goto_e
    return v2

    :sswitch_10
    const/16 v1, 0x5a

    if-eq v0, v1, :cond_33

    const/16 v1, 0x5e

    if-ne v0, v1, :cond_32

    goto :goto_f

    :cond_32
    move v2, v4

    :cond_33
    :goto_f
    return v2

    :sswitch_11
    const/16 v1, 0x4b

    if-eq v0, v1, :cond_35

    const/16 v1, 0x4d

    if-ne v0, v1, :cond_34

    goto :goto_10

    :cond_34
    move v2, v4

    :cond_35
    :goto_10
    return v2

    :sswitch_12
    if-lt v0, v5, :cond_36

    const/16 v1, 0xa4

    if-gt v0, v1, :cond_36

    goto :goto_11

    :cond_36
    move v2, v4

    :goto_11
    return v2

    :sswitch_13
    if-lt v0, v3, :cond_37

    const/16 v1, 0x11a

    if-le v0, v1, :cond_3a

    :cond_37
    if-lt v0, v5, :cond_38

    const/16 v1, 0xa4

    if-le v0, v1, :cond_3a

    :cond_38
    const/16 v1, 0x154

    if-lt v0, v1, :cond_39

    const/16 v1, 0x156

    if-gt v0, v1, :cond_39

    goto :goto_12

    :cond_39
    move v2, v4

    :cond_3a
    :goto_12
    return v2

    :sswitch_14
    const/16 v1, 0x78

    if-lt v0, v1, :cond_3b

    const/16 v1, 0x80

    if-gt v0, v1, :cond_3b

    goto :goto_13

    :cond_3b
    move v2, v4

    :goto_13
    return v2

    :sswitch_15
    const/16 v1, 0x64

    if-eq v0, v1, :cond_40

    const/16 v1, 0xd2

    if-lt v0, v1, :cond_3c

    const/16 v1, 0xd9

    if-le v0, v1, :cond_40

    :cond_3c
    const/16 v1, 0xa6

    if-lt v0, v1, :cond_3d

    const/16 v1, 0xa8

    if-le v0, v1, :cond_40

    :cond_3d
    const/16 v1, 0x11d

    if-lt v0, v1, :cond_3e

    const/16 v1, 0x11f

    if-le v0, v1, :cond_40

    :cond_3e
    const/16 v1, 0x15e

    if-lt v0, v1, :cond_3f

    const/16 v1, 0x160

    if-gt v0, v1, :cond_3f

    goto :goto_14

    :cond_3f
    move v2, v4

    :cond_40
    :goto_14
    return v2

    :pswitch_17
    const/4 v1, -0x1

    if-eq v0, v1, :cond_41

    if-eq v0, v6, :cond_41

    const/16 v1, 0x14

    if-eq v0, v1, :cond_41

    const/16 v1, 0x140

    if-eq v0, v1, :cond_41

    goto :goto_17

    :cond_41
    return v2

    :pswitch_18
    const/4 v1, -0x1

    if-eq v0, v1, :cond_42

    if-eq v0, v6, :cond_42

    const/16 v1, 0x140

    if-eq v0, v1, :cond_42

    goto :goto_17

    :cond_42
    return v2

    :pswitch_19
    if-ltz v0, :cond_43

    const/16 v1, 0x33e

    if-gt v0, v1, :cond_43

    goto :goto_15

    :cond_43
    move v2, v4

    :goto_15
    :pswitch_1a
    return v2

    :cond_44
    const/16 v1, 0x222

    if-eq v0, v1, :cond_45

    const/16 v1, 0x32e

    if-eq v0, v1, :cond_45

    const/16 v1, 0x331

    if-eq v0, v1, :cond_45

    const/16 v1, 0x333

    if-eq v0, v1, :cond_45

    const/16 v1, 0x33e

    if-eq v0, v1, :cond_45

    packed-switch v0, :pswitch_data_c

    goto :goto_16

    :cond_45
    :pswitch_1b
    return v2

    :cond_46
    :goto_16
    const/16 v1, 0x190

    if-lt v0, v1, :cond_47

    const/16 v1, 0x1c3

    if-gt v0, v1, :cond_47

    return v2

    :cond_47
    const/16 v1, 0x21e

    if-eq v0, v1, :cond_48

    const/16 v1, 0x21f

    if-eq v0, v1, :cond_48

    packed-switch v0, :pswitch_data_d

    goto :goto_17

    :cond_48
    :pswitch_1c
    return v2

    :cond_49
    const/16 v1, 0x21b

    if-eq v0, v1, :cond_4a

    const/16 v1, 0x221

    if-eq v0, v1, :cond_4a

    const/16 v1, 0x23d

    if-eq v0, v1, :cond_4a

    packed-switch v0, :pswitch_data_e

    :goto_17
    return v4

    :cond_4a
    :pswitch_1d
    return v2

    :cond_4b
    const/16 v1, 0x2bc

    if-lt v0, v1, :cond_4c

    const/16 v1, 0x2bd

    if-gt v0, v1, :cond_4c

    goto :goto_18

    :cond_4c
    move v2, v4

    :goto_18
    return v2

    :cond_4d
    const/16 v1, 0x259

    if-lt v0, v1, :cond_4e

    const/16 v1, 0x260

    if-gt v0, v1, :cond_4e

    goto :goto_19

    :cond_4e
    move v2, v4

    :goto_19
    return v2

    :cond_4f
    const/16 v1, 0x258

    if-lt v0, v1, :cond_50

    const/16 v1, 0x260

    if-gt v0, v1, :cond_50

    goto :goto_1a

    :cond_50
    move v2, v4

    :goto_1a
    return v2

    :cond_51
    const/16 v1, 0x262

    if-eq v0, v1, :cond_53

    const/16 v1, 0x263

    if-ne v0, v1, :cond_52

    goto :goto_1b

    :cond_52
    move v2, v4

    :cond_53
    :goto_1b
    return v2

    :cond_54
    const/16 v1, 0x262

    if-lt v0, v1, :cond_55

    const/16 v1, 0x264

    if-gt v0, v1, :cond_55

    goto :goto_1c

    :cond_55
    move v2, v4

    :goto_1c
    return v2

    :cond_56
    if-lt v0, v6, :cond_57

    const/16 v1, 0x154

    if-gt v0, v1, :cond_57

    goto :goto_1d

    :cond_57
    move v2, v4

    :goto_1d
    return v2

    :cond_58
    const/16 v1, 0x1f4

    if-lt v0, v1, :cond_59

    const/16 v1, 0x2bd

    if-gt v0, v1, :cond_59

    goto :goto_1e

    :cond_59
    move v2, v4

    :goto_1e
    return v2

    nop

    :pswitch_data_0
    .packed-switch 0x3e8
        :pswitch_1a
        :pswitch_19
        :pswitch_18
        :pswitch_17
    .end packed-switch

    :sswitch_data_0
    .sparse-switch
        0x44c -> :sswitch_15
        0x44d -> :sswitch_14
        0x44e -> :sswitch_13
        0x44f -> :sswitch_12
        0x450 -> :sswitch_11
        0x451 -> :sswitch_10
        0x452 -> :sswitch_f
        0x453 -> :sswitch_e
        0x454 -> :sswitch_d
        0x4b0 -> :sswitch_b
        0x4ba -> :sswitch_a
        0x4c4 -> :sswitch_9
        0x4ce -> :sswitch_8
        0x4d3 -> :sswitch_c
        0x51e -> :sswitch_0
        0x528 -> :sswitch_7
        0x52d -> :sswitch_6
        0x546 -> :sswitch_5
        0x55a -> :sswitch_4
        0x578 -> :sswitch_3
        0x582 -> :sswitch_2
        0x58c -> :sswitch_1
        0x596 -> :sswitch_1
    .end sparse-switch

    :pswitch_data_1
    .packed-switch 0x5dc
        :pswitch_14
        :pswitch_13
        :pswitch_12
    .end packed-switch

    :pswitch_data_2
    .packed-switch 0x76c
        :pswitch_10
        :pswitch_f
        :pswitch_e
        :pswitch_c
        :pswitch_b
        :pswitch_a
    .end packed-switch

    :pswitch_data_3
    .packed-switch 0x7d0
        :pswitch_9
        :pswitch_8
        :pswitch_7
        :pswitch_6
        :pswitch_4
        :pswitch_5
        :pswitch_3
        :pswitch_1
        :pswitch_0
    .end packed-switch

    :pswitch_data_4
    .packed-switch 0x386
        :pswitch_2
        :pswitch_2
        :pswitch_2
    .end packed-switch

    :pswitch_data_5
    .packed-switch 0x21e
        :pswitch_d
        :pswitch_d
        :pswitch_d
    .end packed-switch

    :pswitch_data_6
    .packed-switch 0x262
        :pswitch_d
        :pswitch_d
        :pswitch_d
    .end packed-switch

    :pswitch_data_7
    .packed-switch 0x21e
        :pswitch_11
        :pswitch_11
        :pswitch_11
    .end packed-switch

    :pswitch_data_8
    .packed-switch 0x262
        :pswitch_11
        :pswitch_11
        :pswitch_11
    .end packed-switch

    :pswitch_data_9
    .packed-switch 0x23a
        :pswitch_15
        :pswitch_15
        :pswitch_15
    .end packed-switch

    :pswitch_data_a
    .packed-switch 0x118
        :pswitch_16
        :pswitch_16
        :pswitch_16
    .end packed-switch

    :pswitch_data_b
    .packed-switch 0x154
        :pswitch_16
        :pswitch_16
        :pswitch_16
    .end packed-switch

    :pswitch_data_c
    .packed-switch 0x32a
        :pswitch_1b
        :pswitch_1b
        :pswitch_1b
    .end packed-switch

    :pswitch_data_d
    .packed-switch 0x262
        :pswitch_1c
        :pswitch_1c
        :pswitch_1c
    .end packed-switch

    :pswitch_data_e
    .packed-switch 0x212
        :pswitch_1d
        :pswitch_1d
        :pswitch_1d
        :pswitch_1d
    .end packed-switch
.end method
