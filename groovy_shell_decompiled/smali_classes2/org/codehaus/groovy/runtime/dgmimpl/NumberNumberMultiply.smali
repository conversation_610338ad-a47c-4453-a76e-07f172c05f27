.class public final Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMultiply;
.super Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMetaMethod;
.source "NumberNumberMultiply.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMultiply$IntegerInteger;,
        Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMultiply$IntegerLong;,
        Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMultiply$IntegerFloat;,
        Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMultiply$IntegerDouble;,
        Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMultiply$LongInteger;,
        Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMultiply$LongLong;,
        Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMultiply$LongFloat;,
        Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMultiply$LongDouble;,
        Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMultiply$FloatInteger;,
        Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMultiply$FloatLong;,
        Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMultiply$FloatFloat;,
        Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMultiply$FloatDouble;,
        Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMultiply$DoubleInteger;,
        Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMultiply$DoubleLong;,
        Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMultiply$DoubleFloat;,
        Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMultiply$DoubleDouble;,
        Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMultiply$NumberNumber;
    }
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 26
    invoke-direct {p0}, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMetaMethod;-><init>()V

    return-void
.end method

.method public static multiply(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;
    .locals 0

    .line 45
    invoke-static {p0, p1}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->multiply(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public createDoubleDouble(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;
    .locals 8

    .line 110
    new-instance v7, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMultiply$DoubleDouble;

    move-object v0, v7

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    move-object v5, p5

    move-object v6, p6

    invoke-direct/range {v0 .. v6}, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMultiply$DoubleDouble;-><init>(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)V

    return-object v7
.end method

.method public createDoubleFloat(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;
    .locals 8

    .line 106
    new-instance v7, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMultiply$DoubleFloat;

    move-object v0, v7

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    move-object v5, p5

    move-object v6, p6

    invoke-direct/range {v0 .. v6}, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMultiply$DoubleFloat;-><init>(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)V

    return-object v7
.end method

.method public createDoubleInteger(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;
    .locals 8

    .line 98
    new-instance v7, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMultiply$DoubleInteger;

    move-object v0, v7

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    move-object v5, p5

    move-object v6, p6

    invoke-direct/range {v0 .. v6}, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMultiply$DoubleInteger;-><init>(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)V

    return-object v7
.end method

.method public createDoubleLong(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;
    .locals 8

    .line 102
    new-instance v7, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMultiply$DoubleLong;

    move-object v0, v7

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    move-object v5, p5

    move-object v6, p6

    invoke-direct/range {v0 .. v6}, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMultiply$DoubleLong;-><init>(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)V

    return-object v7
.end method

.method public createFloatDouble(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;
    .locals 8

    .line 94
    new-instance v7, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMultiply$FloatDouble;

    move-object v0, v7

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    move-object v5, p5

    move-object v6, p6

    invoke-direct/range {v0 .. v6}, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMultiply$FloatDouble;-><init>(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)V

    return-object v7
.end method

.method public createFloatFloat(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;
    .locals 8

    .line 90
    new-instance v7, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMultiply$FloatFloat;

    move-object v0, v7

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    move-object v5, p5

    move-object v6, p6

    invoke-direct/range {v0 .. v6}, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMultiply$FloatFloat;-><init>(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)V

    return-object v7
.end method

.method public createFloatInteger(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;
    .locals 8

    .line 82
    new-instance v7, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMultiply$FloatInteger;

    move-object v0, v7

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    move-object v5, p5

    move-object v6, p6

    invoke-direct/range {v0 .. v6}, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMultiply$FloatInteger;-><init>(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)V

    return-object v7
.end method

.method public createFloatLong(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;
    .locals 8

    .line 86
    new-instance v7, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMultiply$FloatLong;

    move-object v0, v7

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    move-object v5, p5

    move-object v6, p6

    invoke-direct/range {v0 .. v6}, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMultiply$FloatLong;-><init>(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)V

    return-object v7
.end method

.method public createIntegerDouble(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;
    .locals 8

    .line 62
    new-instance v7, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMultiply$IntegerDouble;

    move-object v0, v7

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    move-object v5, p5

    move-object v6, p6

    invoke-direct/range {v0 .. v6}, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMultiply$IntegerDouble;-><init>(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)V

    return-object v7
.end method

.method public createIntegerFloat(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;
    .locals 8

    .line 58
    new-instance v7, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMultiply$IntegerFloat;

    move-object v0, v7

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    move-object v5, p5

    move-object v6, p6

    invoke-direct/range {v0 .. v6}, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMultiply$IntegerFloat;-><init>(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)V

    return-object v7
.end method

.method public createIntegerInteger(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;
    .locals 8

    .line 50
    new-instance v7, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMultiply$IntegerInteger;

    move-object v0, v7

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    move-object v5, p5

    move-object v6, p6

    invoke-direct/range {v0 .. v6}, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMultiply$IntegerInteger;-><init>(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)V

    return-object v7
.end method

.method public createIntegerLong(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;
    .locals 8

    .line 54
    new-instance v7, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMultiply$IntegerLong;

    move-object v0, v7

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    move-object v5, p5

    move-object v6, p6

    invoke-direct/range {v0 .. v6}, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMultiply$IntegerLong;-><init>(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)V

    return-object v7
.end method

.method public createLongDouble(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;
    .locals 8

    .line 78
    new-instance v7, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMultiply$LongDouble;

    move-object v0, v7

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    move-object v5, p5

    move-object v6, p6

    invoke-direct/range {v0 .. v6}, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMultiply$LongDouble;-><init>(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)V

    return-object v7
.end method

.method public createLongFloat(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;
    .locals 8

    .line 74
    new-instance v7, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMultiply$LongFloat;

    move-object v0, v7

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    move-object v5, p5

    move-object v6, p6

    invoke-direct/range {v0 .. v6}, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMultiply$LongFloat;-><init>(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)V

    return-object v7
.end method

.method public createLongInteger(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;
    .locals 8

    .line 66
    new-instance v7, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMultiply$LongInteger;

    move-object v0, v7

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    move-object v5, p5

    move-object v6, p6

    invoke-direct/range {v0 .. v6}, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMultiply$LongInteger;-><init>(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)V

    return-object v7
.end method

.method public createLongLong(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;
    .locals 8

    .line 70
    new-instance v7, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMultiply$LongLong;

    move-object v0, v7

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    move-object v5, p5

    move-object v6, p6

    invoke-direct/range {v0 .. v6}, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMultiply$LongLong;-><init>(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)V

    return-object v7
.end method

.method public createNumberNumber(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;
    .locals 8

    .line 114
    new-instance v7, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMultiply$NumberNumber;

    move-object v0, v7

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    move-object v5, p5

    move-object v6, p6

    invoke-direct/range {v0 .. v6}, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMultiply$NumberNumber;-><init>(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)V

    return-object v7
.end method

.method public getName()Ljava/lang/String;
    .locals 1

    const-string v0, "multiply"

    return-object v0
.end method

.method public invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 32
    check-cast p1, Ljava/lang/Number;

    const/4 v0, 0x0

    aget-object p2, p2, v0

    check-cast p2, Ljava/lang/Number;

    invoke-static {p1, p2}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->multiply(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;

    move-result-object p1

    return-object p1
.end method
