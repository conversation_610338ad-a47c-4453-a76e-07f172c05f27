.class Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMultiply$LongFloat;
.super Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMetaMethod$NumberNumberCallSite;
.source "NumberNumberMultiply.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMultiply;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0xa
    name = "LongFloat"
.end annotation


# direct methods
.method public constructor <init>(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)V
    .locals 7

    .line 272
    move-object v5, p5

    check-cast v5, Ljava/lang/Number;

    const/4 p5, 0x0

    aget-object p5, p6, p5

    move-object v6, p5

    check-cast v6, Ljava/lang/Number;

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    invoke-direct/range {v0 .. v6}, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMetaMethod$NumberNumberCallSite;-><init>(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Number;Ljava/lang/Number;)V

    return-void
.end method


# virtual methods
.method public final call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Throwable;
        }
    .end annotation

    .line 277
    :try_start_0
    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMultiply$LongFloat;->checkCall(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 278
    move-object v0, p1

    check-cast v0, Ljava/lang/Long;

    invoke-virtual {v0}, Ljava/lang/Long;->doubleValue()D

    move-result-wide v0

    move-object v2, p2

    check-cast v2, Ljava/lang/Float;

    invoke-virtual {v2}, Ljava/lang/Float;->doubleValue()D

    move-result-wide v2

    mul-double/2addr v0, v2

    invoke-static {v0, v1}, Ljava/lang/Double;->valueOf(D)Ljava/lang/Double;

    move-result-object p1
    :try_end_0
    .catch Ljava/lang/ClassCastException; {:try_start_0 .. :try_end_0} :catch_0

    return-object p1

    .line 283
    :catch_0
    :cond_0
    invoke-super {p0, p1, p2}, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMetaMethod$NumberNumberCallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method
