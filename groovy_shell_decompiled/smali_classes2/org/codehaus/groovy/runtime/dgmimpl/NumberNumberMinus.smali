.class public final Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMinus;
.super Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMetaMethod;
.source "NumberNumberMinus.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMinus$IntegerInteger;,
        Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMinus$IntegerLong;,
        Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMinus$IntegerFloat;,
        Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMinus$IntegerDouble;,
        Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMinus$LongInteger;,
        Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMinus$LongLong;,
        Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMinus$LongFloat;,
        Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMinus$LongDouble;,
        Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMinus$FloatInteger;,
        Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMinus$FloatLong;,
        Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMinus$FloatFloat;,
        Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMinus$FloatDouble;,
        Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMinus$DoubleInteger;,
        Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMinus$DoubleLong;,
        Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMinus$DoubleFloat;,
        Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMinus$DoubleDouble;,
        Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMinus$NumberNumber;
    }
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 26
    invoke-direct {p0}, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMetaMethod;-><init>()V

    return-void
.end method

.method public static minus(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;
    .locals 0

    .line 43
    invoke-static {p0, p1}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->subtract(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public createDoubleDouble(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;
    .locals 8

    .line 108
    new-instance v7, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMinus$DoubleDouble;

    move-object v0, v7

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    move-object v5, p5

    move-object v6, p6

    invoke-direct/range {v0 .. v6}, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMinus$DoubleDouble;-><init>(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)V

    return-object v7
.end method

.method public createDoubleFloat(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;
    .locals 8

    .line 104
    new-instance v7, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMinus$DoubleFloat;

    move-object v0, v7

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    move-object v5, p5

    move-object v6, p6

    invoke-direct/range {v0 .. v6}, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMinus$DoubleFloat;-><init>(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)V

    return-object v7
.end method

.method public createDoubleInteger(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;
    .locals 8

    .line 96
    new-instance v7, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMinus$DoubleInteger;

    move-object v0, v7

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    move-object v5, p5

    move-object v6, p6

    invoke-direct/range {v0 .. v6}, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMinus$DoubleInteger;-><init>(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)V

    return-object v7
.end method

.method public createDoubleLong(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;
    .locals 8

    .line 100
    new-instance v7, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMinus$DoubleLong;

    move-object v0, v7

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    move-object v5, p5

    move-object v6, p6

    invoke-direct/range {v0 .. v6}, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMinus$DoubleLong;-><init>(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)V

    return-object v7
.end method

.method public createFloatDouble(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;
    .locals 8

    .line 92
    new-instance v7, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMinus$FloatDouble;

    move-object v0, v7

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    move-object v5, p5

    move-object v6, p6

    invoke-direct/range {v0 .. v6}, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMinus$FloatDouble;-><init>(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)V

    return-object v7
.end method

.method public createFloatFloat(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;
    .locals 8

    .line 88
    new-instance v7, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMinus$FloatFloat;

    move-object v0, v7

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    move-object v5, p5

    move-object v6, p6

    invoke-direct/range {v0 .. v6}, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMinus$FloatFloat;-><init>(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)V

    return-object v7
.end method

.method public createFloatInteger(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;
    .locals 8

    .line 80
    new-instance v7, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMinus$FloatInteger;

    move-object v0, v7

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    move-object v5, p5

    move-object v6, p6

    invoke-direct/range {v0 .. v6}, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMinus$FloatInteger;-><init>(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)V

    return-object v7
.end method

.method public createFloatLong(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;
    .locals 8

    .line 84
    new-instance v7, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMinus$FloatLong;

    move-object v0, v7

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    move-object v5, p5

    move-object v6, p6

    invoke-direct/range {v0 .. v6}, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMinus$FloatLong;-><init>(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)V

    return-object v7
.end method

.method public createIntegerDouble(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;
    .locals 8

    .line 60
    new-instance v7, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMinus$IntegerDouble;

    move-object v0, v7

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    move-object v5, p5

    move-object v6, p6

    invoke-direct/range {v0 .. v6}, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMinus$IntegerDouble;-><init>(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)V

    return-object v7
.end method

.method public createIntegerFloat(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;
    .locals 8

    .line 56
    new-instance v7, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMinus$IntegerFloat;

    move-object v0, v7

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    move-object v5, p5

    move-object v6, p6

    invoke-direct/range {v0 .. v6}, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMinus$IntegerFloat;-><init>(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)V

    return-object v7
.end method

.method public createIntegerInteger(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;
    .locals 8

    .line 48
    new-instance v7, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMinus$IntegerInteger;

    move-object v0, v7

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    move-object v5, p5

    move-object v6, p6

    invoke-direct/range {v0 .. v6}, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMinus$IntegerInteger;-><init>(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)V

    return-object v7
.end method

.method public createIntegerLong(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;
    .locals 8

    .line 52
    new-instance v7, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMinus$IntegerLong;

    move-object v0, v7

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    move-object v5, p5

    move-object v6, p6

    invoke-direct/range {v0 .. v6}, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMinus$IntegerLong;-><init>(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)V

    return-object v7
.end method

.method public createLongDouble(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;
    .locals 8

    .line 76
    new-instance v7, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMinus$LongDouble;

    move-object v0, v7

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    move-object v5, p5

    move-object v6, p6

    invoke-direct/range {v0 .. v6}, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMinus$LongDouble;-><init>(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)V

    return-object v7
.end method

.method public createLongFloat(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;
    .locals 8

    .line 72
    new-instance v7, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMinus$LongFloat;

    move-object v0, v7

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    move-object v5, p5

    move-object v6, p6

    invoke-direct/range {v0 .. v6}, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMinus$LongFloat;-><init>(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)V

    return-object v7
.end method

.method public createLongInteger(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;
    .locals 8

    .line 64
    new-instance v7, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMinus$LongInteger;

    move-object v0, v7

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    move-object v5, p5

    move-object v6, p6

    invoke-direct/range {v0 .. v6}, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMinus$LongInteger;-><init>(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)V

    return-object v7
.end method

.method public createLongLong(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;
    .locals 8

    .line 68
    new-instance v7, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMinus$LongLong;

    move-object v0, v7

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    move-object v5, p5

    move-object v6, p6

    invoke-direct/range {v0 .. v6}, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMinus$LongLong;-><init>(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)V

    return-object v7
.end method

.method public createNumberNumber(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;
    .locals 8

    .line 112
    new-instance v7, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMinus$NumberNumber;

    move-object v0, v7

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    move-object v5, p5

    move-object v6, p6

    invoke-direct/range {v0 .. v6}, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMinus$NumberNumber;-><init>(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)V

    return-object v7
.end method

.method public getName()Ljava/lang/String;
    .locals 1

    const-string v0, "minus"

    return-object v0
.end method

.method public invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 32
    check-cast p1, Ljava/lang/Number;

    const/4 v0, 0x0

    aget-object p2, p2, v0

    check-cast p2, Ljava/lang/Number;

    invoke-static {p1, p2}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->subtract(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;

    move-result-object p1

    return-object p1
.end method
