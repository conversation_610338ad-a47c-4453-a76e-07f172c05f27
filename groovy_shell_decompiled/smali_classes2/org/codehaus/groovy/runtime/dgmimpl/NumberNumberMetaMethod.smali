.class public abstract Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMetaMethod;
.super Lorg/codehaus/groovy/runtime/callsite/CallSiteAwareMetaMethod;
.source "NumberNumberMetaMethod.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMetaMethod$NumberNumberCallSite;
    }
.end annotation


# static fields
.field private static final NUMBER_CLASS:Lorg/codehaus/groovy/reflection/CachedClass;

.field private static final NUMBER_CLASS_ARR:[Lorg/codehaus/groovy/reflection/CachedClass;


# direct methods
.method static constructor <clinit>()V
    .locals 3

    .line 33
    const-class v0, Ljava/lang/Number;

    invoke-static {v0}, Lorg/codehaus/groovy/reflection/ReflectionCache;->getCachedClass(Ljava/lang/Class;)Lorg/codehaus/groovy/reflection/CachedClass;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMetaMethod;->NUMBER_CLASS:Lorg/codehaus/groovy/reflection/CachedClass;

    const/4 v1, 0x1

    new-array v1, v1, [Lorg/codehaus/groovy/reflection/CachedClass;

    const/4 v2, 0x0

    aput-object v0, v1, v2

    .line 34
    sput-object v1, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMetaMethod;->NUMBER_CLASS_ARR:[Lorg/codehaus/groovy/reflection/CachedClass;

    return-void
.end method

.method protected constructor <init>()V
    .locals 1

    .line 36
    invoke-direct {p0}, Lorg/codehaus/groovy/runtime/callsite/CallSiteAwareMetaMethod;-><init>()V

    .line 37
    sget-object v0, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMetaMethod;->NUMBER_CLASS_ARR:[Lorg/codehaus/groovy/reflection/CachedClass;

    iput-object v0, p0, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMetaMethod;->parameterTypes:[Lorg/codehaus/groovy/reflection/CachedClass;

    return-void
.end method


# virtual methods
.method public abstract createDoubleDouble(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;
.end method

.method public abstract createDoubleFloat(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;
.end method

.method public abstract createDoubleInteger(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;
.end method

.method public abstract createDoubleLong(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;
.end method

.method public abstract createFloatDouble(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;
.end method

.method public abstract createFloatFloat(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;
.end method

.method public abstract createFloatInteger(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;
.end method

.method public abstract createFloatLong(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;
.end method

.method public abstract createIntegerDouble(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;
.end method

.method public abstract createIntegerFloat(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;
.end method

.method public abstract createIntegerInteger(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;
.end method

.method public abstract createIntegerLong(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;
.end method

.method public abstract createLongDouble(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;
.end method

.method public abstract createLongFloat(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;
.end method

.method public abstract createLongInteger(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;
.end method

.method public abstract createLongLong(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;
.end method

.method public abstract createNumberNumber(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;
.end method

.method public createPojoCallSite(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;
    .locals 2

    const/4 v0, 0x0

    .line 63
    aget-object v0, p6, v0

    .line 65
    instance-of v1, p5, Ljava/lang/Integer;

    if-eqz v1, :cond_3

    .line 66
    instance-of v1, v0, Ljava/lang/Integer;

    if-eqz v1, :cond_0

    .line 67
    invoke-virtual/range {p0 .. p6}, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMetaMethod;->createIntegerInteger(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object p1

    return-object p1

    .line 69
    :cond_0
    instance-of v1, v0, Ljava/lang/Long;

    if-eqz v1, :cond_1

    .line 70
    invoke-virtual/range {p0 .. p6}, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMetaMethod;->createIntegerLong(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object p1

    return-object p1

    .line 72
    :cond_1
    instance-of v1, v0, Ljava/lang/Float;

    if-eqz v1, :cond_2

    .line 73
    invoke-virtual/range {p0 .. p6}, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMetaMethod;->createIntegerFloat(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object p1

    return-object p1

    .line 75
    :cond_2
    instance-of v1, v0, Ljava/lang/Double;

    if-eqz v1, :cond_3

    .line 76
    invoke-virtual/range {p0 .. p6}, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMetaMethod;->createIntegerDouble(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object p1

    return-object p1

    .line 79
    :cond_3
    instance-of v1, p5, Ljava/lang/Long;

    if-eqz v1, :cond_7

    .line 80
    instance-of v1, v0, Ljava/lang/Integer;

    if-eqz v1, :cond_4

    .line 81
    invoke-virtual/range {p0 .. p6}, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMetaMethod;->createLongInteger(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object p1

    return-object p1

    .line 83
    :cond_4
    instance-of v1, v0, Ljava/lang/Long;

    if-eqz v1, :cond_5

    .line 84
    invoke-virtual/range {p0 .. p6}, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMetaMethod;->createLongLong(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object p1

    return-object p1

    .line 86
    :cond_5
    instance-of v1, v0, Ljava/lang/Float;

    if-eqz v1, :cond_6

    .line 87
    invoke-virtual/range {p0 .. p6}, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMetaMethod;->createLongFloat(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object p1

    return-object p1

    .line 89
    :cond_6
    instance-of v1, v0, Ljava/lang/Double;

    if-eqz v1, :cond_7

    .line 90
    invoke-virtual/range {p0 .. p6}, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMetaMethod;->createLongDouble(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object p1

    return-object p1

    .line 93
    :cond_7
    instance-of v1, p5, Ljava/lang/Float;

    if-eqz v1, :cond_b

    .line 94
    instance-of v1, v0, Ljava/lang/Integer;

    if-eqz v1, :cond_8

    .line 95
    invoke-virtual/range {p0 .. p6}, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMetaMethod;->createFloatInteger(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object p1

    return-object p1

    .line 97
    :cond_8
    instance-of v1, v0, Ljava/lang/Long;

    if-eqz v1, :cond_9

    .line 98
    invoke-virtual/range {p0 .. p6}, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMetaMethod;->createFloatLong(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object p1

    return-object p1

    .line 100
    :cond_9
    instance-of v1, v0, Ljava/lang/Float;

    if-eqz v1, :cond_a

    .line 101
    invoke-virtual/range {p0 .. p6}, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMetaMethod;->createFloatFloat(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object p1

    return-object p1

    .line 103
    :cond_a
    instance-of v1, v0, Ljava/lang/Double;

    if-eqz v1, :cond_b

    .line 104
    invoke-virtual/range {p0 .. p6}, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMetaMethod;->createFloatDouble(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object p1

    return-object p1

    .line 107
    :cond_b
    instance-of v1, p5, Ljava/lang/Double;

    if-eqz v1, :cond_f

    .line 108
    instance-of v1, v0, Ljava/lang/Integer;

    if-eqz v1, :cond_c

    .line 109
    invoke-virtual/range {p0 .. p6}, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMetaMethod;->createDoubleInteger(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object p1

    return-object p1

    .line 111
    :cond_c
    instance-of v1, v0, Ljava/lang/Long;

    if-eqz v1, :cond_d

    .line 112
    invoke-virtual/range {p0 .. p6}, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMetaMethod;->createDoubleLong(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object p1

    return-object p1

    .line 114
    :cond_d
    instance-of v1, v0, Ljava/lang/Float;

    if-eqz v1, :cond_e

    .line 115
    invoke-virtual/range {p0 .. p6}, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMetaMethod;->createDoubleFloat(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object p1

    return-object p1

    .line 117
    :cond_e
    instance-of v0, v0, Ljava/lang/Double;

    if-eqz v0, :cond_f

    .line 118
    invoke-virtual/range {p0 .. p6}, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMetaMethod;->createDoubleDouble(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object p1

    return-object p1

    .line 121
    :cond_f
    invoke-virtual/range {p0 .. p6}, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMetaMethod;->createNumberNumber(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object p1

    return-object p1
.end method

.method public final getDeclaringClass()Lorg/codehaus/groovy/reflection/CachedClass;
    .locals 1

    .line 49
    sget-object v0, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMetaMethod;->NUMBER_CLASS:Lorg/codehaus/groovy/reflection/CachedClass;

    return-object v0
.end method

.method public getModifiers()I
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public getReturnType()Ljava/lang/Class;
    .locals 1

    .line 45
    sget-object v0, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMetaMethod;->NUMBER_CLASS:Lorg/codehaus/groovy/reflection/CachedClass;

    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/CachedClass;->getTheClass()Ljava/lang/Class;

    move-result-object v0

    return-object v0
.end method
