.class public abstract Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMetaMethod$NumberNumberCallSite;
.super Lorg/codehaus/groovy/runtime/callsite/PojoMetaMethodSite;
.source "NumberNumberMetaMethod.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMetaMethod;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x409
    name = "NumberNumberCallSite"
.end annotation


# instance fields
.field final math:Lorg/codehaus/groovy/runtime/typehandling/NumberMath;


# direct methods
.method public constructor <init>(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Number;Ljava/lang/Number;)V
    .locals 0

    .line 57
    invoke-direct {p0, p1, p2, p3, p4}, Lorg/codehaus/groovy/runtime/callsite/PojoMetaMethodSite;-><init>(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;)V

    .line 58
    invoke-static {p5, p6}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->getMath(Ljava/lang/Number;Ljava/lang/Number;)Lorg/codehaus/groovy/runtime/typehandling/NumberMath;

    move-result-object p1

    iput-object p1, p0, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMetaMethod$NumberNumberCallSite;->math:Lorg/codehaus/groovy/runtime/typehandling/NumberMath;

    return-void
.end method
