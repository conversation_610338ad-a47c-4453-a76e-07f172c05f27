.class Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberPlus$1;
.super Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMetaMethod$NumberNumberCallSite;
.source "NumberNumberPlus.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberPlus;->createNumberNumber(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberPlus;


# direct methods
.method constructor <init>(Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberPlus;Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Number;Ljava/lang/Number;)V
    .locals 7

    .line 111
    iput-object p1, p0, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberPlus$1;->this$0:Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberPlus;

    move-object v0, p0

    move-object v1, p2

    move-object v2, p3

    move-object v3, p4

    move-object v4, p5

    move-object v5, p6

    move-object v6, p7

    invoke-direct/range {v0 .. v6}, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMetaMethod$NumberNumberCallSite;-><init>(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Number;Ljava/lang/Number;)V

    return-void
.end method


# virtual methods
.method public invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 117
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberPlus$1;->math:Lorg/codehaus/groovy/runtime/typehandling/NumberMath;

    check-cast p1, Ljava/lang/Number;

    check-cast p2, Ljava/lang/Number;

    invoke-virtual {v0, p1, p2}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->addImpl(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;

    move-result-object p1

    return-object p1
.end method

.method public invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 113
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberPlus$1;->math:Lorg/codehaus/groovy/runtime/typehandling/NumberMath;

    check-cast p1, Ljava/lang/Number;

    const/4 v1, 0x0

    aget-object p2, p2, v1

    check-cast p2, Ljava/lang/Number;

    invoke-virtual {v0, p1, p2}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->addImpl(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;

    move-result-object p1

    return-object p1
.end method
