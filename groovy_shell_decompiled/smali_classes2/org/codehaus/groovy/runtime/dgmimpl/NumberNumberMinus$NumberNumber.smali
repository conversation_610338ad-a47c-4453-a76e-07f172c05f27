.class Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMinus$NumberNumber;
.super Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMetaMethod$NumberNumberCallSite;
.source "NumberNumberMinus.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMinus;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0xa
    name = "NumberNumber"
.end annotation


# direct methods
.method public constructor <init>(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Object;[Ljava/lang/Object;)V
    .locals 7

    .line 389
    move-object v5, p5

    check-cast v5, Ljava/lang/Number;

    const/4 p5, 0x0

    aget-object p5, p6, p5

    move-object v6, p5

    check-cast v6, Ljava/lang/Number;

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    invoke-direct/range {v0 .. v6}, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMetaMethod$NumberNumberCallSite;-><init>(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;Ljava/lang/Number;Ljava/lang/Number;)V

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 397
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMinus$NumberNumber;->math:Lorg/codehaus/groovy/runtime/typehandling/NumberMath;

    check-cast p1, Ljava/lang/Number;

    check-cast p2, Ljava/lang/Number;

    invoke-virtual {v0, p1, p2}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->subtractImpl(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 393
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/dgmimpl/NumberNumberMinus$NumberNumber;->math:Lorg/codehaus/groovy/runtime/typehandling/NumberMath;

    check-cast p1, Ljava/lang/Number;

    const/4 v1, 0x0

    aget-object p2, p2, v1

    check-cast p2, Ljava/lang/Number;

    invoke-virtual {v0, p1, p2}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->subtractImpl(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;

    move-result-object p1

    return-object p1
.end method
