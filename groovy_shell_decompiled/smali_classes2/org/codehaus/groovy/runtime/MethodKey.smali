.class public abstract Lorg/codehaus/groovy/runtime/MethodKey;
.super Ljava/lang/Object;
.source "MethodKey.java"


# instance fields
.field private hash:I

.field private final isCallToSuper:Z

.field private final name:Ljava/lang/String;

.field private final sender:L<PERSON><PERSON>/lang/Class;


# direct methods
.method public constructor <init>(Ljava/lang/Class;Ljava/lang/String;Z)V
    .locals 0

    .line 37
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 38
    iput-object p1, p0, Lorg/codehaus/groovy/runtime/MethodKey;->sender:Ljava/lang/Class;

    .line 39
    iput-object p2, p0, Lorg/codehaus/groovy/runtime/MethodKey;->name:Ljava/lang/String;

    .line 40
    iput-boolean p3, p0, Lorg/codehaus/groovy/runtime/MethodKey;->isCallToSuper:Z

    return-void
.end method


# virtual methods
.method public createCopy()Lorg/codehaus/groovy/runtime/MethodKey;
    .locals 5

    .line 47
    invoke-virtual {p0}, Lorg/codehaus/groovy/runtime/MethodKey;->getParameterCount()I

    move-result v0

    .line 48
    new-array v1, v0, [Ljava/lang/Class;

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v0, :cond_0

    .line 50
    invoke-virtual {p0, v2}, Lorg/codehaus/groovy/runtime/MethodKey;->getParameterType(I)Ljava/lang/Class;

    move-result-object v3

    aput-object v3, v1, v2

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 52
    :cond_0
    new-instance v0, Lorg/codehaus/groovy/runtime/DefaultMethodKey;

    iget-object v2, p0, Lorg/codehaus/groovy/runtime/MethodKey;->sender:Ljava/lang/Class;

    iget-object v3, p0, Lorg/codehaus/groovy/runtime/MethodKey;->name:Ljava/lang/String;

    iget-boolean v4, p0, Lorg/codehaus/groovy/runtime/MethodKey;->isCallToSuper:Z

    invoke-direct {v0, v2, v3, v1, v4}, Lorg/codehaus/groovy/runtime/DefaultMethodKey;-><init>(Ljava/lang/Class;Ljava/lang/String;[Ljava/lang/Class;Z)V

    return-object v0
.end method

.method protected createHashCode()I
    .locals 4

    .line 114
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/MethodKey;->name:Ljava/lang/String;

    invoke-virtual {v0}, Ljava/lang/String;->hashCode()I

    move-result v0

    .line 115
    invoke-virtual {p0}, Lorg/codehaus/groovy/runtime/MethodKey;->getParameterCount()I

    move-result v1

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v1, :cond_0

    mul-int/lit8 v0, v0, 0x25

    .line 123
    invoke-virtual {p0, v2}, Lorg/codehaus/groovy/runtime/MethodKey;->getParameterType(I)Ljava/lang/Class;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/Object;->hashCode()I

    move-result v3

    add-int/lit8 v3, v3, 0x1

    add-int/2addr v0, v3

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_0
    mul-int/lit8 v0, v0, 0x25

    .line 126
    iget-boolean v1, p0, Lorg/codehaus/groovy/runtime/MethodKey;->isCallToSuper:Z

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x25

    .line 128
    iget-object v1, p0, Lorg/codehaus/groovy/runtime/MethodKey;->sender:Ljava/lang/Class;

    invoke-virtual {v1}, Ljava/lang/Object;->hashCode()I

    move-result v1

    add-int/lit8 v1, v1, 0x1

    add-int/2addr v0, v1

    return v0
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 1

    if-ne p0, p1, :cond_0

    const/4 p1, 0x1

    return p1

    .line 59
    :cond_0
    instance-of v0, p1, Lorg/codehaus/groovy/runtime/MethodKey;

    if-eqz v0, :cond_1

    .line 60
    check-cast p1, Lorg/codehaus/groovy/runtime/MethodKey;

    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/runtime/MethodKey;->equals(Lorg/codehaus/groovy/runtime/MethodKey;)Z

    move-result p1

    return p1

    :cond_1
    const/4 p1, 0x0

    return p1
.end method

.method public equals(Lorg/codehaus/groovy/runtime/MethodKey;)Z
    .locals 5

    .line 67
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/MethodKey;->sender:Ljava/lang/Class;

    iget-object v1, p1, Lorg/codehaus/groovy/runtime/MethodKey;->sender:Ljava/lang/Class;

    const/4 v2, 0x0

    if-eq v0, v1, :cond_0

    return v2

    .line 68
    :cond_0
    iget-boolean v0, p0, Lorg/codehaus/groovy/runtime/MethodKey;->isCallToSuper:Z

    iget-boolean v1, p1, Lorg/codehaus/groovy/runtime/MethodKey;->isCallToSuper:Z

    if-eq v0, v1, :cond_1

    return v2

    .line 69
    :cond_1
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/MethodKey;->name:Ljava/lang/String;

    iget-object v1, p1, Lorg/codehaus/groovy/runtime/MethodKey;->name:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_2

    return v2

    .line 70
    :cond_2
    invoke-virtual {p0}, Lorg/codehaus/groovy/runtime/MethodKey;->getParameterCount()I

    move-result v0

    invoke-virtual {p1}, Lorg/codehaus/groovy/runtime/MethodKey;->getParameterCount()I

    move-result v1

    if-eq v0, v1, :cond_3

    return v2

    :cond_3
    move v1, v2

    :goto_0
    if-ge v1, v0, :cond_5

    .line 73
    invoke-virtual {p0, v1}, Lorg/codehaus/groovy/runtime/MethodKey;->getParameterType(I)Ljava/lang/Class;

    move-result-object v3

    invoke-virtual {p1, v1}, Lorg/codehaus/groovy/runtime/MethodKey;->getParameterType(I)Ljava/lang/Class;

    move-result-object v4

    if-eq v3, v4, :cond_4

    return v2

    :cond_4
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_5
    const/4 p1, 0x1

    return p1
.end method

.method public getName()Ljava/lang/String;
    .locals 1

    .line 95
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/MethodKey;->name:Ljava/lang/String;

    return-object v0
.end method

.method public abstract getParameterCount()I
.end method

.method public abstract getParameterType(I)Ljava/lang/Class;
.end method

.method public getParamterTypes()Ljava/util/List;
    .locals 4

    .line 99
    invoke-virtual {p0}, Lorg/codehaus/groovy/runtime/MethodKey;->getParameterCount()I

    move-result v0

    if-gtz v0, :cond_0

    .line 101
    sget-object v0, Ljava/util/Collections;->EMPTY_LIST:Ljava/util/List;

    return-object v0

    .line 103
    :cond_0
    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1, v0}, Ljava/util/ArrayList;-><init>(I)V

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v0, :cond_1

    .line 105
    invoke-virtual {p0, v2}, Lorg/codehaus/groovy/runtime/MethodKey;->getParameterType(I)Ljava/lang/Class;

    move-result-object v3

    invoke-interface {v1, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_1
    return-object v1
.end method

.method public hashCode()I
    .locals 1

    .line 81
    iget v0, p0, Lorg/codehaus/groovy/runtime/MethodKey;->hash:I

    if-nez v0, :cond_0

    .line 82
    invoke-virtual {p0}, Lorg/codehaus/groovy/runtime/MethodKey;->createHashCode()I

    move-result v0

    iput v0, p0, Lorg/codehaus/groovy/runtime/MethodKey;->hash:I

    if-nez v0, :cond_0

    const v0, -0x35014542    # -8346975.0f

    .line 84
    iput v0, p0, Lorg/codehaus/groovy/runtime/MethodKey;->hash:I

    .line 87
    :cond_0
    iget v0, p0, Lorg/codehaus/groovy/runtime/MethodKey;->hash:I

    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 2

    .line 91
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-super {p0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "[name:"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v1, p0, Lorg/codehaus/groovy/runtime/MethodKey;->name:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "; params:"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {p0}, Lorg/codehaus/groovy/runtime/MethodKey;->getParamterTypes()Ljava/util/List;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
