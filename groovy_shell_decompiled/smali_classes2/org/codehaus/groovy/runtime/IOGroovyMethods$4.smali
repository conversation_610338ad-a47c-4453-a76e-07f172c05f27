.class Lorg/codehaus/groovy/runtime/IOGroovyMethods$4;
.super Ljava/lang/Object;
.source "IOGroovyMethods.java"

# interfaces
.implements Lgroovy/lang/Writable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/codehaus/groovy/runtime/IOGroovyMethods;->filterLine(Ljava/io/Reader;Lgroovy/lang/Closure;)Lgroovy/lang/Writable;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic val$br:Ljava/io/BufferedReader;

.field final synthetic val$closure:Lgroovy/lang/Closure;


# direct methods
.method constructor <init>(Lgroovy/lang/Closure;Ljava/io/BufferedReader;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .line 1493
    iput-object p1, p0, Lorg/codehaus/groovy/runtime/IOGroovyMethods$4;->val$closure:Lgroovy/lang/Closure;

    iput-object p2, p0, Lorg/codehaus/groovy/runtime/IOGroovyMethods$4;->val$br:Ljava/io/BufferedReader;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public toString()Ljava/lang/String;
    .locals 2

    .line 1509
    new-instance v0, Lorg/apache/groovy/io/StringBuilderWriter;

    invoke-direct {v0}, Lorg/apache/groovy/io/StringBuilderWriter;-><init>()V

    .line 1511
    :try_start_0
    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/runtime/IOGroovyMethods$4;->writeTo(Ljava/io/Writer;)Ljava/io/Writer;
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    .line 1515
    invoke-virtual {v0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0

    :catch_0
    move-exception v0

    .line 1513
    new-instance v1, Lgroovy/lang/StringWriterIOException;

    invoke-direct {v1, v0}, Lgroovy/lang/StringWriterIOException;-><init>(Ljava/io/IOException;)V

    throw v1
.end method

.method public writeTo(Ljava/io/Writer;)Ljava/io/Writer;
    .locals 5
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 1495
    new-instance v0, Ljava/io/BufferedWriter;

    invoke-direct {v0, p1}, Ljava/io/BufferedWriter;-><init>(Ljava/io/Writer;)V

    .line 1497
    new-instance v1, Lorg/codehaus/groovy/runtime/callsite/BooleanClosureWrapper;

    iget-object v2, p0, Lorg/codehaus/groovy/runtime/IOGroovyMethods$4;->val$closure:Lgroovy/lang/Closure;

    invoke-direct {v1, v2}, Lorg/codehaus/groovy/runtime/callsite/BooleanClosureWrapper;-><init>(Lgroovy/lang/Closure;)V

    .line 1498
    :cond_0
    :goto_0
    iget-object v2, p0, Lorg/codehaus/groovy/runtime/IOGroovyMethods$4;->val$br:Ljava/io/BufferedReader;

    invoke-virtual {v2}, Ljava/io/BufferedReader;->readLine()Ljava/lang/String;

    move-result-object v2

    if-eqz v2, :cond_1

    const/4 v3, 0x1

    new-array v3, v3, [Ljava/lang/Object;

    const/4 v4, 0x0

    aput-object v2, v3, v4

    .line 1499
    invoke-virtual {v1, v3}, Lorg/codehaus/groovy/runtime/callsite/BooleanClosureWrapper;->call([Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_0

    .line 1500
    invoke-virtual {v0, v2}, Ljava/io/BufferedWriter;->write(Ljava/lang/String;)V

    .line 1501
    invoke-virtual {v0}, Ljava/io/BufferedWriter;->newLine()V

    goto :goto_0

    .line 1504
    :cond_1
    invoke-virtual {v0}, Ljava/io/BufferedWriter;->flush()V

    return-object p1
.end method
