.class public Lorg/codehaus/groovy/runtime/RegexSupport;
.super Ljava/lang/Object;
.source "RegexSupport.java"


# static fields
.field private static final CURRENT_MATCHER:Ljava/lang/ThreadLocal;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 28
    new-instance v0, Ljava/lang/ThreadLocal;

    invoke-direct {v0}, Ljava/lang/ThreadLocal;-><init>()V

    sput-object v0, Lorg/codehaus/groovy/runtime/RegexSupport;->CURRENT_MATCHER:Ljava/lang/ThreadLocal;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 26
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static getLastMatcher()Ljava/util/regex/Matcher;
    .locals 1

    .line 31
    sget-object v0, Lorg/codehaus/groovy/runtime/RegexSupport;->CURRENT_MATCHER:Ljava/lang/ThreadLocal;

    invoke-virtual {v0}, Ljava/lang/ThreadLocal;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/regex/Matcher;

    return-object v0
.end method

.method public static setLastMatcher(Ljava/util/regex/Matcher;)V
    .locals 1

    .line 35
    sget-object v0, Lorg/codehaus/groovy/runtime/RegexSupport;->CURRENT_MATCHER:Ljava/lang/ThreadLocal;

    invoke-virtual {v0, p0}, Ljava/lang/ThreadLocal;->set(Ljava/lang/Object;)V

    return-void
.end method
