.class public Lorg/codehaus/groovy/runtime/MetaClassHelper;
.super Ljava/lang/Object;
.source "MetaClassHelper.java"


# static fields
.field public static final ARRAY_WITH_NULL:[Ljava/lang/Object;

.field public static final EMPTY_ARRAY:[Ljava/lang/Object;

.field public static final EMPTY_CLASS_ARRAY:[Ljava/lang/Class;

.field public static final EMPTY_TYPE_ARRAY:[Ljava/lang/Class;

.field private static final INTERFACE_SHIFT:I = 0x0

.field protected static final LOG:Ljava/util/logging/Logger;

.field private static final MAX_ARG_LEN:I = 0xc

.field private static final OBJECT_SHIFT:I = 0x17

.field private static final PRIMITIVES:[Ljava/lang/Class;

.field private static final PRIMITIVE_DISTANCE_TABLE:[[I

.field private static final PRIMITIVE_SHIFT:I = 0x15

.field private static final VARGS_SHIFT:I = 0x2c


# direct methods
.method static constructor <clinit>()V
    .locals 23

    const/4 v0, 0x0

    new-array v1, v0, [Ljava/lang/Object;

    .line 48
    sput-object v1, Lorg/codehaus/groovy/runtime/MetaClassHelper;->EMPTY_ARRAY:[Ljava/lang/Object;

    new-array v1, v0, [Ljava/lang/Class;

    .line 49
    sput-object v1, Lorg/codehaus/groovy/runtime/MetaClassHelper;->EMPTY_TYPE_ARRAY:[Ljava/lang/Class;

    const/4 v1, 0x1

    new-array v2, v1, [Ljava/lang/Object;

    const/4 v3, 0x0

    aput-object v3, v2, v0

    .line 50
    sput-object v2, Lorg/codehaus/groovy/runtime/MetaClassHelper;->ARRAY_WITH_NULL:[Ljava/lang/Object;

    .line 51
    const-class v2, Lorg/codehaus/groovy/runtime/MetaClassHelper;

    invoke-virtual {v2}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v2

    invoke-static {v2}, Ljava/util/logging/Logger;->getLogger(Ljava/lang/String;)Ljava/util/logging/Logger;

    move-result-object v2

    sput-object v2, Lorg/codehaus/groovy/runtime/MetaClassHelper;->LOG:Ljava/util/logging/Logger;

    new-array v2, v0, [Ljava/lang/Class;

    .line 63
    sput-object v2, Lorg/codehaus/groovy/runtime/MetaClassHelper;->EMPTY_CLASS_ARRAY:[Ljava/lang/Class;

    const/16 v2, 0x14

    new-array v3, v2, [Ljava/lang/Class;

    .line 207
    sget-object v4, Ljava/lang/Boolean;->TYPE:Ljava/lang/Class;

    aput-object v4, v3, v0

    const-class v4, Ljava/lang/Boolean;

    aput-object v4, v3, v1

    sget-object v4, Ljava/lang/Byte;->TYPE:Ljava/lang/Class;

    const/4 v5, 0x2

    aput-object v4, v3, v5

    const-class v4, Ljava/lang/Byte;

    const/4 v6, 0x3

    aput-object v4, v3, v6

    sget-object v4, Ljava/lang/Short;->TYPE:Ljava/lang/Class;

    const/4 v7, 0x4

    aput-object v4, v3, v7

    const-class v4, Ljava/lang/Short;

    const/4 v8, 0x5

    aput-object v4, v3, v8

    sget-object v4, Ljava/lang/Character;->TYPE:Ljava/lang/Class;

    const/4 v9, 0x6

    aput-object v4, v3, v9

    const-class v4, Ljava/lang/Character;

    const/4 v10, 0x7

    aput-object v4, v3, v10

    sget-object v4, Ljava/lang/Integer;->TYPE:Ljava/lang/Class;

    const/16 v11, 0x8

    aput-object v4, v3, v11

    const-class v4, Ljava/lang/Integer;

    const/16 v12, 0x9

    aput-object v4, v3, v12

    sget-object v4, Ljava/lang/Long;->TYPE:Ljava/lang/Class;

    const/16 v13, 0xa

    aput-object v4, v3, v13

    const-class v4, Ljava/lang/Long;

    const/16 v14, 0xb

    aput-object v4, v3, v14

    const-class v4, Ljava/math/BigInteger;

    const/16 v15, 0xc

    aput-object v4, v3, v15

    sget-object v4, Ljava/lang/Float;->TYPE:Ljava/lang/Class;

    const/16 v16, 0xd

    aput-object v4, v3, v16

    const-class v4, Ljava/lang/Float;

    const/16 v17, 0xe

    aput-object v4, v3, v17

    sget-object v4, Ljava/lang/Double;->TYPE:Ljava/lang/Class;

    const/16 v18, 0xf

    aput-object v4, v3, v18

    const-class v4, Ljava/lang/Double;

    const/16 v19, 0x10

    aput-object v4, v3, v19

    const-class v4, Ljava/math/BigDecimal;

    const/16 v20, 0x11

    aput-object v4, v3, v20

    const-class v4, Ljava/lang/Number;

    const/16 v21, 0x12

    aput-object v4, v3, v21

    const-class v4, Ljava/lang/Object;

    const/16 v22, 0x13

    aput-object v4, v3, v22

    sput-object v3, Lorg/codehaus/groovy/runtime/MetaClassHelper;->PRIMITIVES:[Ljava/lang/Class;

    new-array v3, v2, [[I

    new-array v4, v2, [I

    .line 230
    fill-array-data v4, :array_0

    aput-object v4, v3, v0

    new-array v0, v2, [I

    fill-array-data v0, :array_1

    aput-object v0, v3, v1

    new-array v0, v2, [I

    fill-array-data v0, :array_2

    aput-object v0, v3, v5

    new-array v0, v2, [I

    fill-array-data v0, :array_3

    aput-object v0, v3, v6

    new-array v0, v2, [I

    fill-array-data v0, :array_4

    aput-object v0, v3, v7

    new-array v0, v2, [I

    fill-array-data v0, :array_5

    aput-object v0, v3, v8

    new-array v0, v2, [I

    fill-array-data v0, :array_6

    aput-object v0, v3, v9

    new-array v0, v2, [I

    fill-array-data v0, :array_7

    aput-object v0, v3, v10

    new-array v0, v2, [I

    fill-array-data v0, :array_8

    aput-object v0, v3, v11

    new-array v0, v2, [I

    fill-array-data v0, :array_9

    aput-object v0, v3, v12

    new-array v0, v2, [I

    fill-array-data v0, :array_a

    aput-object v0, v3, v13

    new-array v0, v2, [I

    fill-array-data v0, :array_b

    aput-object v0, v3, v14

    new-array v0, v2, [I

    fill-array-data v0, :array_c

    aput-object v0, v3, v15

    new-array v0, v2, [I

    fill-array-data v0, :array_d

    aput-object v0, v3, v16

    new-array v0, v2, [I

    fill-array-data v0, :array_e

    aput-object v0, v3, v17

    new-array v0, v2, [I

    fill-array-data v0, :array_f

    aput-object v0, v3, v18

    new-array v0, v2, [I

    fill-array-data v0, :array_10

    aput-object v0, v3, v19

    new-array v0, v2, [I

    fill-array-data v0, :array_11

    aput-object v0, v3, v20

    new-array v0, v2, [I

    fill-array-data v0, :array_12

    aput-object v0, v3, v21

    new-array v0, v2, [I

    fill-array-data v0, :array_13

    aput-object v0, v3, v22

    sput-object v3, Lorg/codehaus/groovy/runtime/MetaClassHelper;->PRIMITIVE_DISTANCE_TABLE:[[I

    return-void

    :array_0
    .array-data 4
        0x0
        0x1
        0x3
        0x4
        0x5
        0x6
        0x7
        0x8
        0x9
        0xa
        0xb
        0xc
        0xd
        0xe
        0xf
        0x10
        0x11
        0x12
        0x13
        0x2
    .end array-data

    :array_1
    .array-data 4
        0x1
        0x0
        0x3
        0x4
        0x5
        0x6
        0x7
        0x8
        0x9
        0xa
        0xb
        0xc
        0xd
        0xe
        0xf
        0x10
        0x11
        0x12
        0x13
        0x2
    .end array-data

    :array_2
    .array-data 4
        0x12
        0x13
        0x0
        0x1
        0x2
        0x3
        0x10
        0x11
        0x4
        0x5
        0x6
        0x7
        0x8
        0x9
        0xa
        0xb
        0xc
        0xd
        0xe
        0xf
    .end array-data

    :array_3
    .array-data 4
        0x12
        0x13
        0x1
        0x0
        0x2
        0x3
        0x10
        0x11
        0x4
        0x5
        0x6
        0x7
        0x8
        0x9
        0xa
        0xb
        0xc
        0xd
        0xe
        0xf
    .end array-data

    :array_4
    .array-data 4
        0x12
        0x13
        0xe
        0xf
        0x0
        0x1
        0x10
        0x11
        0x2
        0x3
        0x4
        0x5
        0x6
        0x7
        0x8
        0x9
        0xa
        0xb
        0xc
        0xd
    .end array-data

    :array_5
    .array-data 4
        0x12
        0x13
        0xe
        0xf
        0x1
        0x0
        0x10
        0x11
        0x2
        0x3
        0x4
        0x5
        0x6
        0x7
        0x8
        0x9
        0xa
        0xb
        0xc
        0xd
    .end array-data

    :array_6
    .array-data 4
        0x12
        0x13
        0x10
        0x11
        0xe
        0xf
        0x0
        0x1
        0x2
        0x3
        0x4
        0x5
        0x6
        0x7
        0x8
        0x9
        0xa
        0xb
        0xc
        0xd
    .end array-data

    :array_7
    .array-data 4
        0x12
        0x13
        0x10
        0x11
        0xe
        0xf
        0x1
        0x0
        0x2
        0x3
        0x4
        0x5
        0x6
        0x7
        0x8
        0x9
        0xa
        0xb
        0xc
        0xd
    .end array-data

    :array_8
    .array-data 4
        0x12
        0x13
        0xe
        0xf
        0xc
        0xd
        0x10
        0x11
        0x0
        0x1
        0x2
        0x3
        0x4
        0x5
        0x6
        0x7
        0x8
        0x9
        0xa
        0xb
    .end array-data

    :array_9
    .array-data 4
        0x12
        0x13
        0xe
        0xf
        0xc
        0xd
        0x10
        0x11
        0x1
        0x0
        0x2
        0x3
        0x4
        0x5
        0x6
        0x7
        0x8
        0x9
        0xa
        0xb
    .end array-data

    :array_a
    .array-data 4
        0x12
        0x13
        0xe
        0xf
        0xc
        0xd
        0x10
        0x11
        0xa
        0xb
        0x0
        0x1
        0x2
        0x3
        0x4
        0x5
        0x6
        0x7
        0x8
        0x9
    .end array-data

    :array_b
    .array-data 4
        0x12
        0x13
        0xe
        0xf
        0xc
        0xd
        0x10
        0x11
        0xa
        0xb
        0x1
        0x0
        0x2
        0x3
        0x4
        0x5
        0x6
        0x7
        0x8
        0x9
    .end array-data

    :array_c
    .array-data 4
        0x12
        0x13
        0x9
        0xa
        0x7
        0x8
        0x10
        0x11
        0x5
        0x6
        0x3
        0x4
        0x0
        0xe
        0xf
        0xc
        0xd
        0xb
        0x1
        0x2
    .end array-data

    :array_d
    .array-data 4
        0x12
        0x13
        0xe
        0xf
        0xc
        0xd
        0x10
        0x11
        0xa
        0xb
        0x8
        0x9
        0x7
        0x0
        0x1
        0x2
        0x3
        0x4
        0x5
        0x6
    .end array-data

    :array_e
    .array-data 4
        0x12
        0x13
        0xe
        0xf
        0xc
        0xd
        0x10
        0x11
        0xa
        0xb
        0x8
        0x9
        0x7
        0x1
        0x0
        0x2
        0x3
        0x4
        0x5
        0x6
    .end array-data

    :array_f
    .array-data 4
        0x12
        0x13
        0xe
        0xf
        0xc
        0xd
        0x10
        0x11
        0xa
        0xb
        0x8
        0x9
        0x7
        0x5
        0x6
        0x0
        0x1
        0x2
        0x3
        0x4
    .end array-data

    :array_10
    .array-data 4
        0x12
        0x13
        0xe
        0xf
        0xc
        0xd
        0x10
        0x11
        0xa
        0xb
        0x8
        0x9
        0x7
        0x5
        0x6
        0x1
        0x0
        0x2
        0x3
        0x4
    .end array-data

    :array_11
    .array-data 4
        0x12
        0x13
        0xe
        0xf
        0xc
        0xd
        0x10
        0x11
        0xa
        0xb
        0x8
        0x9
        0x7
        0x5
        0x6
        0x3
        0x4
        0x0
        0x1
        0x2
    .end array-data

    :array_12
    .array-data 4
        0x12
        0x13
        0xe
        0xf
        0xc
        0xd
        0x10
        0x11
        0xa
        0xb
        0x8
        0x9
        0x7
        0x5
        0x6
        0x3
        0x4
        0x2
        0x0
        0x1
    .end array-data

    :array_13
    .array-data 4
        0x12
        0x13
        0xe
        0xf
        0xc
        0xd
        0x10
        0x11
        0xa
        0xb
        0x8
        0x9
        0x7
        0x5
        0x6
        0x3
        0x4
        0x2
        0x1
        0x0
    .end array-data
.end method

.method public constructor <init>()V
    .locals 0

    .line 46
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static accessibleToConstructor(Ljava/lang/Class;Ljava/lang/reflect/Constructor;)Z
    .locals 4

    .line 67
    invoke-virtual {p1}, Ljava/lang/reflect/Constructor;->getModifiers()I

    move-result v0

    .line 68
    invoke-static {v0}, Ljava/lang/reflect/Modifier;->isPublic(I)Z

    move-result v1

    const/4 v2, 0x1

    const/4 v3, 0x0

    if-eqz v1, :cond_0

    goto :goto_2

    .line 70
    :cond_0
    invoke-static {v0}, Ljava/lang/reflect/Modifier;->isPrivate(I)Z

    move-result v1

    if-eqz v1, :cond_1

    .line 71
    invoke-virtual {p0}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object p0

    invoke-virtual {p1}, Ljava/lang/reflect/Constructor;->getName()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v2

    goto :goto_2

    .line 72
    :cond_1
    invoke-static {v0}, Ljava/lang/reflect/Modifier;->isProtected(I)Z

    move-result v0

    if-eqz v0, :cond_5

    .line 73
    invoke-static {p0, p1}, Lorg/codehaus/groovy/runtime/MetaClassHelper;->checkCompatiblePackages(Ljava/lang/Class;Ljava/lang/reflect/Constructor;)Ljava/lang/Boolean;

    move-result-object v0

    if-eqz v0, :cond_2

    .line 75
    invoke-virtual {v0}, Ljava/lang/Boolean;->booleanValue()Z

    move-result p0

    move v2, p0

    goto :goto_2

    :cond_2
    :goto_0
    if-eqz p0, :cond_6

    .line 80
    invoke-virtual {p1}, Ljava/lang/reflect/Constructor;->getDeclaringClass()Ljava/lang/Class;

    move-result-object v0

    invoke-virtual {p0, v0}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_3

    goto :goto_2

    .line 84
    :cond_3
    const-class v0, Ljava/lang/Object;

    invoke-virtual {p0, v0}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_4

    goto :goto_1

    .line 87
    :cond_4
    invoke-virtual {p0}, Ljava/lang/Class;->getSuperclass()Ljava/lang/Class;

    move-result-object p0

    goto :goto_0

    .line 92
    :cond_5
    invoke-static {p0, p1}, Lorg/codehaus/groovy/runtime/MetaClassHelper;->checkCompatiblePackages(Ljava/lang/Class;Ljava/lang/reflect/Constructor;)Ljava/lang/Boolean;

    move-result-object p0

    if-eqz p0, :cond_6

    .line 94
    invoke-virtual {p0}, Ljava/lang/Boolean;->booleanValue()Z

    move-result v2

    goto :goto_2

    :cond_6
    :goto_1
    move v2, v3

    :goto_2
    return v2
.end method

.method public static asPrimitiveArray(Ljava/util/List;Ljava/lang/Class;)Ljava/lang/Object;
    .locals 4

    .line 178
    invoke-virtual {p1}, Ljava/lang/Class;->getComponentType()Ljava/lang/Class;

    move-result-object p1

    .line 179
    invoke-interface {p0}, Ljava/util/List;->size()I

    move-result v0

    invoke-static {p1, v0}, Ljava/lang/reflect/Array;->newInstance(Ljava/lang/Class;I)Ljava/lang/Object;

    move-result-object v0

    const/4 v1, 0x0

    .line 180
    :goto_0
    invoke-interface {p0}, Ljava/util/List;->size()I

    move-result v2

    if-ge v1, v2, :cond_9

    .line 181
    invoke-interface {p0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    .line 182
    invoke-virtual {p1}, Ljava/lang/Class;->isPrimitive()Z

    move-result v3

    if-eqz v3, :cond_7

    .line 183
    instance-of v3, v2, Ljava/lang/Integer;

    if-eqz v3, :cond_0

    .line 184
    check-cast v2, Ljava/lang/Integer;

    invoke-virtual {v2}, Ljava/lang/Integer;->intValue()I

    move-result v2

    invoke-static {v0, v1, v2}, Ljava/lang/reflect/Array;->setInt(Ljava/lang/Object;II)V

    goto :goto_1

    .line 185
    :cond_0
    instance-of v3, v2, Ljava/lang/Double;

    if-eqz v3, :cond_1

    .line 186
    check-cast v2, Ljava/lang/Double;

    invoke-virtual {v2}, Ljava/lang/Double;->doubleValue()D

    move-result-wide v2

    invoke-static {v0, v1, v2, v3}, Ljava/lang/reflect/Array;->setDouble(Ljava/lang/Object;ID)V

    goto :goto_1

    .line 187
    :cond_1
    instance-of v3, v2, Ljava/lang/Boolean;

    if-eqz v3, :cond_2

    .line 188
    check-cast v2, Ljava/lang/Boolean;

    invoke-virtual {v2}, Ljava/lang/Boolean;->booleanValue()Z

    move-result v2

    invoke-static {v0, v1, v2}, Ljava/lang/reflect/Array;->setBoolean(Ljava/lang/Object;IZ)V

    goto :goto_1

    .line 189
    :cond_2
    instance-of v3, v2, Ljava/lang/Long;

    if-eqz v3, :cond_3

    .line 190
    check-cast v2, Ljava/lang/Long;

    invoke-virtual {v2}, Ljava/lang/Long;->longValue()J

    move-result-wide v2

    invoke-static {v0, v1, v2, v3}, Ljava/lang/reflect/Array;->setLong(Ljava/lang/Object;IJ)V

    goto :goto_1

    .line 191
    :cond_3
    instance-of v3, v2, Ljava/lang/Float;

    if-eqz v3, :cond_4

    .line 192
    check-cast v2, Ljava/lang/Float;

    invoke-virtual {v2}, Ljava/lang/Float;->floatValue()F

    move-result v2

    invoke-static {v0, v1, v2}, Ljava/lang/reflect/Array;->setFloat(Ljava/lang/Object;IF)V

    goto :goto_1

    .line 193
    :cond_4
    instance-of v3, v2, Ljava/lang/Character;

    if-eqz v3, :cond_5

    .line 194
    check-cast v2, Ljava/lang/Character;

    invoke-virtual {v2}, Ljava/lang/Character;->charValue()C

    move-result v2

    invoke-static {v0, v1, v2}, Ljava/lang/reflect/Array;->setChar(Ljava/lang/Object;IC)V

    goto :goto_1

    .line 195
    :cond_5
    instance-of v3, v2, Ljava/lang/Byte;

    if-eqz v3, :cond_6

    .line 196
    check-cast v2, Ljava/lang/Byte;

    invoke-virtual {v2}, Ljava/lang/Byte;->byteValue()B

    move-result v2

    invoke-static {v0, v1, v2}, Ljava/lang/reflect/Array;->setByte(Ljava/lang/Object;IB)V

    goto :goto_1

    .line 197
    :cond_6
    instance-of v3, v2, Ljava/lang/Short;

    if-eqz v3, :cond_8

    .line 198
    check-cast v2, Ljava/lang/Short;

    invoke-virtual {v2}, Ljava/lang/Short;->shortValue()S

    move-result v2

    invoke-static {v0, v1, v2}, Ljava/lang/reflect/Array;->setShort(Ljava/lang/Object;IS)V

    goto :goto_1

    .line 201
    :cond_7
    invoke-static {v0, v1, v2}, Ljava/lang/reflect/Array;->set(Ljava/lang/Object;ILjava/lang/Object;)V

    :cond_8
    :goto_1
    add-int/lit8 v1, v1, 0x1

    goto/16 :goto_0

    :cond_9
    return-object v0
.end method

.method public static asWrapperArray(Ljava/lang/Object;Ljava/lang/Class;)[Ljava/lang/Object;
    .locals 4

    .line 118
    sget-object v0, Ljava/lang/Boolean;->TYPE:Ljava/lang/Class;

    const/4 v1, 0x0

    if-ne p1, v0, :cond_0

    .line 119
    check-cast p0, [Z

    .line 120
    array-length p1, p0

    new-array p1, p1, [Ljava/lang/Object;

    .line 121
    :goto_0
    array-length v0, p0

    if-ge v1, v0, :cond_8

    .line 122
    aget-boolean v0, p0, v1

    invoke-static {v0}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v0

    aput-object v0, p1, v1

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 124
    :cond_0
    sget-object v0, Ljava/lang/Character;->TYPE:Ljava/lang/Class;

    if-ne p1, v0, :cond_1

    .line 125
    check-cast p0, [C

    .line 126
    array-length p1, p0

    new-array p1, p1, [Ljava/lang/Object;

    .line 127
    :goto_1
    array-length v0, p0

    if-ge v1, v0, :cond_8

    .line 128
    aget-char v0, p0, v1

    invoke-static {v0}, Ljava/lang/Character;->valueOf(C)Ljava/lang/Character;

    move-result-object v0

    aput-object v0, p1, v1

    add-int/lit8 v1, v1, 0x1

    goto :goto_1

    .line 130
    :cond_1
    sget-object v0, Ljava/lang/Byte;->TYPE:Ljava/lang/Class;

    if-ne p1, v0, :cond_2

    .line 131
    check-cast p0, [B

    .line 132
    array-length p1, p0

    new-array p1, p1, [Ljava/lang/Object;

    .line 133
    :goto_2
    array-length v0, p0

    if-ge v1, v0, :cond_8

    .line 134
    aget-byte v0, p0, v1

    invoke-static {v0}, Ljava/lang/Byte;->valueOf(B)Ljava/lang/Byte;

    move-result-object v0

    aput-object v0, p1, v1

    add-int/lit8 v1, v1, 0x1

    goto :goto_2

    .line 136
    :cond_2
    sget-object v0, Ljava/lang/Integer;->TYPE:Ljava/lang/Class;

    if-ne p1, v0, :cond_3

    .line 137
    check-cast p0, [I

    .line 138
    array-length p1, p0

    new-array p1, p1, [Ljava/lang/Object;

    .line 139
    :goto_3
    array-length v0, p0

    if-ge v1, v0, :cond_8

    .line 140
    aget v0, p0, v1

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    aput-object v0, p1, v1

    add-int/lit8 v1, v1, 0x1

    goto :goto_3

    .line 142
    :cond_3
    sget-object v0, Ljava/lang/Short;->TYPE:Ljava/lang/Class;

    if-ne p1, v0, :cond_4

    .line 143
    check-cast p0, [S

    .line 144
    array-length p1, p0

    new-array p1, p1, [Ljava/lang/Object;

    .line 145
    :goto_4
    array-length v0, p0

    if-ge v1, v0, :cond_8

    .line 146
    aget-short v0, p0, v1

    invoke-static {v0}, Ljava/lang/Short;->valueOf(S)Ljava/lang/Short;

    move-result-object v0

    aput-object v0, p1, v1

    add-int/lit8 v1, v1, 0x1

    goto :goto_4

    .line 148
    :cond_4
    sget-object v0, Ljava/lang/Long;->TYPE:Ljava/lang/Class;

    if-ne p1, v0, :cond_5

    .line 149
    check-cast p0, [J

    .line 150
    array-length p1, p0

    new-array p1, p1, [Ljava/lang/Object;

    .line 151
    :goto_5
    array-length v0, p0

    if-ge v1, v0, :cond_8

    .line 152
    aget-wide v2, p0, v1

    invoke-static {v2, v3}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v0

    aput-object v0, p1, v1

    add-int/lit8 v1, v1, 0x1

    goto :goto_5

    .line 154
    :cond_5
    sget-object v0, Ljava/lang/Double;->TYPE:Ljava/lang/Class;

    if-ne p1, v0, :cond_6

    .line 155
    check-cast p0, [D

    .line 156
    array-length p1, p0

    new-array p1, p1, [Ljava/lang/Object;

    .line 157
    :goto_6
    array-length v0, p0

    if-ge v1, v0, :cond_8

    .line 158
    aget-wide v2, p0, v1

    invoke-static {v2, v3}, Ljava/lang/Double;->valueOf(D)Ljava/lang/Double;

    move-result-object v0

    aput-object v0, p1, v1

    add-int/lit8 v1, v1, 0x1

    goto :goto_6

    .line 160
    :cond_6
    sget-object v0, Ljava/lang/Float;->TYPE:Ljava/lang/Class;

    if-ne p1, v0, :cond_7

    .line 161
    check-cast p0, [F

    .line 162
    array-length p1, p0

    new-array p1, p1, [Ljava/lang/Object;

    .line 163
    :goto_7
    array-length v0, p0

    if-ge v1, v0, :cond_8

    .line 164
    aget v0, p0, v1

    invoke-static {v0}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object v0

    aput-object v0, p1, v1

    add-int/lit8 v1, v1, 0x1

    goto :goto_7

    :cond_7
    const/4 p1, 0x0

    :cond_8
    return-object p1
.end method

.method private static calculateParameterDistance(Ljava/lang/Class;Lorg/codehaus/groovy/reflection/CachedClass;)J
    .locals 9

    .line 300
    invoke-virtual {p1}, Lorg/codehaus/groovy/reflection/CachedClass;->getTheClass()Ljava/lang/Class;

    move-result-object v0

    const-wide/16 v1, 0x0

    if-ne v0, p0, :cond_0

    return-wide v1

    .line 302
    :cond_0
    invoke-virtual {p1}, Lorg/codehaus/groovy/reflection/CachedClass;->isInterface()Z

    move-result v0

    if-eqz v0, :cond_2

    .line 303
    invoke-virtual {p1}, Lorg/codehaus/groovy/reflection/CachedClass;->getTheClass()Ljava/lang/Class;

    move-result-object v0

    invoke-static {p0, v0}, Lorg/codehaus/groovy/runtime/MetaClassHelper;->getMaximumInterfaceDistance(Ljava/lang/Class;Ljava/lang/Class;)I

    move-result v0

    shl-int/lit8 v0, v0, 0x0

    const/4 v3, -0x1

    if-gt v0, v3, :cond_1

    if-eqz p0, :cond_1

    .line 304
    const-class v3, Lgroovy/lang/Closure;

    invoke-virtual {v3, p0}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    move-result v3

    if-nez v3, :cond_2

    :cond_1
    int-to-long p0, v0

    return-wide p0

    :cond_2
    const-wide/16 v3, 0x2

    if-eqz p0, :cond_8

    .line 311
    invoke-virtual {p1}, Lorg/codehaus/groovy/reflection/CachedClass;->getTheClass()Ljava/lang/Class;

    move-result-object v0

    invoke-static {v0, p0}, Lorg/codehaus/groovy/runtime/MetaClassHelper;->getPrimitiveDistance(Ljava/lang/Class;Ljava/lang/Class;)I

    move-result v0

    int-to-long v5, v0

    const-wide/16 v7, -0x1

    cmp-long v0, v5, v7

    if-eqz v0, :cond_3

    const/16 p0, 0x15

    shl-long p0, v5, p0

    return-wide p0

    .line 315
    :cond_3
    sget-object v0, Lorg/codehaus/groovy/runtime/MetaClassHelper;->PRIMITIVES:[Ljava/lang/Class;

    array-length v0, v0

    add-int/lit8 v0, v0, 0x1

    int-to-long v5, v0

    add-long/2addr v5, v1

    .line 320
    invoke-virtual {p0}, Ljava/lang/Class;->isArray()Z

    move-result v0

    if-eqz v0, :cond_4

    iget-boolean v0, p1, Lorg/codehaus/groovy/reflection/CachedClass;->isArray:Z

    if-nez v0, :cond_4

    const-wide/16 v0, 0x4

    add-long/2addr v5, v0

    .line 323
    :cond_4
    invoke-static {p0}, Lorg/codehaus/groovy/reflection/ReflectionCache;->autoboxType(Ljava/lang/Class;)Ljava/lang/Class;

    move-result-object p0

    :goto_0
    if-eqz p0, :cond_7

    .line 325
    invoke-virtual {p1}, Lorg/codehaus/groovy/reflection/CachedClass;->getTheClass()Ljava/lang/Class;

    move-result-object v0

    if-ne p0, v0, :cond_5

    goto :goto_1

    .line 326
    :cond_5
    const-class v0, Lgroovy/lang/GString;

    if-ne p0, v0, :cond_6

    invoke-virtual {p1}, Lorg/codehaus/groovy/reflection/CachedClass;->getTheClass()Ljava/lang/Class;

    move-result-object v0

    const-class v1, Ljava/lang/String;

    if-ne v0, v1, :cond_6

    add-long/2addr v5, v3

    goto :goto_1

    .line 330
    :cond_6
    invoke-virtual {p0}, Ljava/lang/Class;->getSuperclass()Ljava/lang/Class;

    move-result-object p0

    const-wide/16 v0, 0x3

    add-long/2addr v5, v0

    goto :goto_0

    :cond_7
    :goto_1
    move-wide v3, v5

    goto :goto_3

    .line 337
    :cond_8
    invoke-virtual {p1}, Lorg/codehaus/groovy/reflection/CachedClass;->getTheClass()Ljava/lang/Class;

    move-result-object p0

    .line 338
    invoke-virtual {p0}, Ljava/lang/Class;->isPrimitive()Z

    move-result p1

    if-eqz p1, :cond_9

    goto :goto_3

    .line 341
    :cond_9
    :goto_2
    const-class p1, Ljava/lang/Object;

    if-eq p0, p1, :cond_a

    if-eqz p0, :cond_a

    .line 342
    invoke-virtual {p0}, Ljava/lang/Class;->getSuperclass()Ljava/lang/Class;

    move-result-object p0

    add-long/2addr v1, v3

    goto :goto_2

    :cond_a
    move-wide v3, v1

    :goto_3
    const/16 p0, 0x17

    shl-long p0, v3, p0

    return-wide p0
.end method

.method public static calculateParameterDistance([Ljava/lang/Class;Lorg/codehaus/groovy/reflection/ParameterTypes;)J
    .locals 9

    .line 351
    invoke-virtual {p1}, Lorg/codehaus/groovy/reflection/ParameterTypes;->getParameterTypes()[Lorg/codehaus/groovy/reflection/CachedClass;

    move-result-object p1

    .line 352
    array-length v0, p1

    const-wide/16 v1, 0x0

    if-nez v0, :cond_0

    return-wide v1

    :cond_0
    add-int/lit8 v3, v0, -0x1

    const/4 v4, 0x0

    :goto_0
    if-ge v4, v3, :cond_1

    .line 445
    aget-object v5, p0, v4

    aget-object v6, p1, v4

    invoke-static {v5, v6}, Lorg/codehaus/groovy/runtime/MetaClassHelper;->calculateParameterDistance(Ljava/lang/Class;Lorg/codehaus/groovy/reflection/CachedClass;)J

    move-result-wide v5

    add-long/2addr v1, v5

    add-int/lit8 v4, v4, 0x1

    goto :goto_0

    .line 448
    :cond_1
    array-length v4, p0

    if-ne v4, v0, :cond_3

    .line 452
    aget-object v0, p1, v3

    .line 453
    aget-object p1, p1, v3

    aget-object v4, p0, v3

    invoke-virtual {p1, v4}, Lorg/codehaus/groovy/reflection/CachedClass;->isAssignableFrom(Ljava/lang/Class;)Z

    move-result p1

    if-nez p1, :cond_2

    .line 454
    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/CachedClass;->getTheClass()Ljava/lang/Class;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/Class;->getComponentType()Ljava/lang/Class;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/reflection/ReflectionCache;->getCachedClass(Ljava/lang/Class;)Lorg/codehaus/groovy/reflection/CachedClass;

    move-result-object v0

    const-wide v4, 0x200000000000L

    add-long/2addr v1, v4

    .line 457
    :cond_2
    aget-object p0, p0, v3

    invoke-static {p0, v0}, Lorg/codehaus/groovy/runtime/MetaClassHelper;->calculateParameterDistance(Ljava/lang/Class;Lorg/codehaus/groovy/reflection/CachedClass;)J

    move-result-wide p0

    goto :goto_2

    :cond_3
    if-le v4, v0, :cond_4

    const-wide/16 v5, 0x2

    int-to-long v7, v4

    add-long/2addr v7, v5

    int-to-long v5, v0

    sub-long/2addr v7, v5

    const/16 v0, 0x2c

    shl-long v5, v7, v0

    add-long/2addr v1, v5

    .line 463
    aget-object p1, p1, v3

    invoke-virtual {p1}, Lorg/codehaus/groovy/reflection/CachedClass;->getTheClass()Ljava/lang/Class;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/Class;->getComponentType()Ljava/lang/Class;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/reflection/ReflectionCache;->getCachedClass(Ljava/lang/Class;)Lorg/codehaus/groovy/reflection/CachedClass;

    move-result-object p1

    :goto_1
    if-ge v3, v4, :cond_5

    .line 465
    aget-object v0, p0, v3

    invoke-static {v0, p1}, Lorg/codehaus/groovy/runtime/MetaClassHelper;->calculateParameterDistance(Ljava/lang/Class;Lorg/codehaus/groovy/reflection/CachedClass;)J

    move-result-wide v5

    add-long/2addr v1, v5

    add-int/lit8 v3, v3, 0x1

    goto :goto_1

    :cond_4
    const-wide p0, 0x100000000000L

    :goto_2
    add-long/2addr v1, p0

    :cond_5
    return-wide v1
.end method

.method public static capitalize(Ljava/lang/String;)Ljava/lang/String;
    .locals 0
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    .line 482
    invoke-static {p0}, Lorg/apache/groovy/util/BeanUtils;->capitalize(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method public static castArgumentsToClassArray([Ljava/lang/Object;)[Ljava/lang/Class;
    .locals 4

    if-nez p0, :cond_0

    .line 955
    sget-object p0, Lorg/codehaus/groovy/runtime/MetaClassHelper;->EMPTY_CLASS_ARRAY:[Ljava/lang/Class;

    return-object p0

    .line 956
    :cond_0
    array-length v0, p0

    new-array v0, v0, [Ljava/lang/Class;

    const/4 v1, 0x0

    .line 957
    :goto_0
    array-length v2, p0

    if-ge v1, v2, :cond_3

    .line 958
    aget-object v2, p0, v1

    .line 959
    instance-of v3, v2, Ljava/lang/Class;

    if-eqz v3, :cond_1

    .line 960
    check-cast v2, Ljava/lang/Class;

    aput-object v2, v0, v1

    goto :goto_1

    :cond_1
    if-nez v2, :cond_2

    const/4 v2, 0x0

    .line 962
    aput-object v2, v0, v1

    goto :goto_1

    .line 965
    :cond_2
    invoke-virtual {v2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v2

    aput-object v2, v0, v1

    :goto_1
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_3
    return-object v0
.end method

.method private static checkCompatiblePackages(Ljava/lang/Class;Ljava/lang/reflect/Constructor;)Ljava/lang/Boolean;
    .locals 1

    .line 101
    invoke-virtual {p0}, Ljava/lang/Class;->getPackage()Ljava/lang/Package;

    move-result-object v0

    if-nez v0, :cond_0

    invoke-virtual {p1}, Ljava/lang/reflect/Constructor;->getDeclaringClass()Ljava/lang/Class;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/Class;->getPackage()Ljava/lang/Package;

    move-result-object v0

    if-nez v0, :cond_0

    .line 102
    sget-object p0, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    return-object p0

    .line 104
    :cond_0
    invoke-virtual {p0}, Ljava/lang/Class;->getPackage()Ljava/lang/Package;

    move-result-object v0

    if-nez v0, :cond_1

    invoke-virtual {p1}, Ljava/lang/reflect/Constructor;->getDeclaringClass()Ljava/lang/Class;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/Class;->getPackage()Ljava/lang/Package;

    move-result-object v0

    if-eqz v0, :cond_1

    .line 105
    sget-object p0, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    return-object p0

    .line 107
    :cond_1
    invoke-virtual {p0}, Ljava/lang/Class;->getPackage()Ljava/lang/Package;

    move-result-object v0

    if-eqz v0, :cond_2

    invoke-virtual {p1}, Ljava/lang/reflect/Constructor;->getDeclaringClass()Ljava/lang/Class;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/Class;->getPackage()Ljava/lang/Package;

    move-result-object v0

    if-nez v0, :cond_2

    .line 108
    sget-object p0, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    return-object p0

    .line 110
    :cond_2
    invoke-virtual {p0}, Ljava/lang/Class;->getPackage()Ljava/lang/Package;

    move-result-object p0

    invoke-virtual {p1}, Ljava/lang/reflect/Constructor;->getDeclaringClass()Ljava/lang/Class;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/Class;->getPackage()Ljava/lang/Package;

    move-result-object p1

    invoke-virtual {p0, p1}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result p0

    if-eqz p0, :cond_3

    .line 111
    sget-object p0, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    return-object p0

    :cond_3
    const/4 p0, 0x0

    return-object p0
.end method

.method public static chooseEmptyMethodParams(Lorg/codehaus/groovy/util/FastArray;)Ljava/lang/Object;
    .locals 7

    .line 492
    invoke-virtual {p0}, Lorg/codehaus/groovy/util/FastArray;->size()I

    move-result v0

    .line 493
    invoke-virtual {p0}, Lorg/codehaus/groovy/util/FastArray;->getArray()[Ljava/lang/Object;

    move-result-object p0

    const/4 v1, 0x0

    const/4 v2, 0x0

    :goto_0
    if-eq v2, v0, :cond_2

    .line 495
    aget-object v3, p0, v2

    .line 496
    move-object v4, v3

    check-cast v4, Lorg/codehaus/groovy/reflection/ParameterTypes;

    .line 497
    invoke-virtual {v4}, Lorg/codehaus/groovy/reflection/ParameterTypes;->getParameterTypes()[Lorg/codehaus/groovy/reflection/CachedClass;

    move-result-object v5

    .line 498
    array-length v5, v5

    if-nez v5, :cond_0

    return-object v3

    :cond_0
    const/4 v6, 0x1

    if-ne v5, v6, :cond_1

    .line 501
    sget-object v5, Lorg/codehaus/groovy/runtime/MetaClassHelper;->EMPTY_ARRAY:[Ljava/lang/Object;

    invoke-virtual {v4, v5}, Lorg/codehaus/groovy/reflection/ParameterTypes;->isVargsMethod([Ljava/lang/Object;)Z

    move-result v4

    if-eqz v4, :cond_1

    move-object v1, v3

    :cond_1
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_2
    return-object v1
.end method

.method public static chooseMostGeneralMethodWith1NullParam(Lorg/codehaus/groovy/util/FastArray;)Ljava/lang/Object;
    .locals 16
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    .line 524
    invoke-virtual/range {p0 .. p0}, Lorg/codehaus/groovy/util/FastArray;->size()I

    move-result v0

    const/4 v1, 0x0

    const/4 v2, -0x1

    const/4 v3, 0x0

    move v4, v1

    move v7, v2

    move-object v5, v3

    move-object v6, v5

    move-object v8, v6

    :goto_0
    if-eq v4, v0, :cond_b

    .line 526
    invoke-virtual/range {p0 .. p0}, Lorg/codehaus/groovy/util/FastArray;->getArray()[Ljava/lang/Object;

    move-result-object v9

    .line 527
    aget-object v9, v9, v4

    .line 528
    move-object v10, v9

    check-cast v10, Lorg/codehaus/groovy/reflection/ParameterTypes;

    .line 529
    invoke-virtual {v10}, Lorg/codehaus/groovy/reflection/ParameterTypes;->getParameterTypes()[Lorg/codehaus/groovy/reflection/CachedClass;

    move-result-object v11

    .line 530
    array-length v12, v11

    if-eqz v12, :cond_a

    const/4 v13, 0x2

    if-le v12, v13, :cond_0

    goto/16 :goto_4

    .line 533
    :cond_0
    aget-object v14, v11, v1

    .line 534
    iget-boolean v15, v14, Lorg/codehaus/groovy/reflection/CachedClass;->isPrimitive:Z

    if-eqz v15, :cond_1

    goto/16 :goto_4

    :cond_1
    if-ne v12, v13, :cond_6

    .line 537
    sget-object v12, Lorg/codehaus/groovy/runtime/MetaClassHelper;->ARRAY_WITH_NULL:[Ljava/lang/Object;

    invoke-virtual {v10, v12}, Lorg/codehaus/groovy/reflection/ParameterTypes;->isVargsMethod([Ljava/lang/Object;)Z

    move-result v10

    if-nez v10, :cond_2

    goto/16 :goto_4

    :cond_2
    const/4 v10, 0x1

    if-nez v6, :cond_3

    .line 539
    aget-object v8, v11, v10

    goto :goto_2

    .line 542
    :cond_3
    invoke-virtual {v6}, Lorg/codehaus/groovy/reflection/CachedClass;->getTheClass()Ljava/lang/Class;

    move-result-object v12

    invoke-virtual {v14}, Lorg/codehaus/groovy/reflection/CachedClass;->getTheClass()Ljava/lang/Class;

    move-result-object v13

    if-ne v12, v13, :cond_5

    if-nez v8, :cond_4

    goto :goto_4

    .line 544
    :cond_4
    aget-object v10, v11, v10

    .line 545
    invoke-virtual {v10}, Lorg/codehaus/groovy/reflection/CachedClass;->getTheClass()Ljava/lang/Class;

    move-result-object v11

    invoke-virtual {v8}, Lorg/codehaus/groovy/reflection/CachedClass;->getTheClass()Ljava/lang/Class;

    move-result-object v12

    invoke-static {v11, v12}, Lorg/codehaus/groovy/runtime/MetaClassHelper;->isAssignableFrom(Ljava/lang/Class;Ljava/lang/Class;)Z

    move-result v11

    if-eqz v11, :cond_a

    move-object v5, v9

    move-object v8, v10

    goto :goto_4

    .line 549
    :cond_5
    invoke-virtual {v14}, Lorg/codehaus/groovy/reflection/CachedClass;->getTheClass()Ljava/lang/Class;

    move-result-object v12

    invoke-virtual {v6}, Lorg/codehaus/groovy/reflection/CachedClass;->getTheClass()Ljava/lang/Class;

    move-result-object v13

    invoke-static {v12, v13}, Lorg/codehaus/groovy/runtime/MetaClassHelper;->isAssignableFrom(Ljava/lang/Class;Ljava/lang/Class;)Z

    move-result v12

    if-eqz v12, :cond_a

    .line 550
    aget-object v8, v11, v10

    goto :goto_2

    :cond_6
    if-eqz v6, :cond_9

    .line 555
    invoke-virtual {v14}, Lorg/codehaus/groovy/reflection/CachedClass;->getTheClass()Ljava/lang/Class;

    move-result-object v10

    invoke-virtual {v6}, Lorg/codehaus/groovy/reflection/CachedClass;->getTheClass()Ljava/lang/Class;

    move-result-object v11

    invoke-static {v10, v11}, Lorg/codehaus/groovy/runtime/MetaClassHelper;->isAssignableFrom(Ljava/lang/Class;Ljava/lang/Class;)Z

    move-result v10

    if-eqz v10, :cond_7

    goto :goto_1

    :cond_7
    if-ne v7, v2, :cond_8

    .line 563
    invoke-virtual {v6}, Lorg/codehaus/groovy/reflection/CachedClass;->getSuperClassDistance()I

    move-result v7

    .line 564
    :cond_8
    invoke-virtual {v14}, Lorg/codehaus/groovy/reflection/CachedClass;->getSuperClassDistance()I

    move-result v10

    if-ge v10, v7, :cond_a

    move-object v8, v3

    move-object v5, v9

    move v7, v10

    goto :goto_3

    :cond_9
    :goto_1
    move v7, v2

    move-object v8, v3

    :goto_2
    move-object v5, v9

    :goto_3
    move-object v6, v14

    :cond_a
    :goto_4
    add-int/lit8 v4, v4, 0x1

    goto/16 :goto_0

    :cond_b
    return-object v5
.end method

.method public static containsMatchingMethod(Ljava/util/List;Lgroovy/lang/MetaMethod;)Z
    .locals 7

    .line 586
    invoke-interface {p0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p0

    :cond_0
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    const/4 v1, 0x0

    if-eqz v0, :cond_3

    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    .line 587
    check-cast v0, Lgroovy/lang/MetaMethod;

    .line 588
    invoke-virtual {v0}, Lgroovy/lang/MetaMethod;->getParameterTypes()[Lorg/codehaus/groovy/reflection/CachedClass;

    move-result-object v0

    .line 589
    invoke-virtual {p1}, Lgroovy/lang/MetaMethod;->getParameterTypes()[Lorg/codehaus/groovy/reflection/CachedClass;

    move-result-object v2

    .line 590
    array-length v3, v0

    array-length v4, v2

    if-ne v3, v4, :cond_0

    move v3, v1

    .line 592
    :goto_0
    array-length v4, v0

    const/4 v5, 0x1

    if-ge v3, v4, :cond_2

    .line 593
    aget-object v4, v0, v3

    aget-object v6, v2, v3

    if-eq v4, v6, :cond_1

    goto :goto_1

    :cond_1
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    :cond_2
    move v1, v5

    :goto_1
    if-eqz v1, :cond_0

    return v5

    :cond_3
    return v1
.end method

.method public static convertPropertyName(Ljava/lang/String;)Ljava/lang/String;
    .locals 1

    const/4 v0, 0x0

    .line 1009
    invoke-virtual {p0, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    invoke-static {v0}, Ljava/lang/Character;->isDigit(C)Z

    move-result v0

    if-eqz v0, :cond_0

    return-object p0

    .line 1012
    :cond_0
    invoke-static {p0}, Lorg/apache/groovy/util/BeanUtils;->decapitalize(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method public static convertToTypeArray([Ljava/lang/Object;)[Ljava/lang/Class;
    .locals 4

    if-nez p0, :cond_0

    const/4 p0, 0x0

    return-object p0

    .line 615
    :cond_0
    array-length v0, p0

    .line 616
    new-array v1, v0, [Ljava/lang/Class;

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v0, :cond_1

    .line 618
    aget-object v3, p0, v2

    .line 619
    invoke-static {v3}, Lorg/codehaus/groovy/runtime/MetaClassHelper;->getClassWithNullAndWrapper(Ljava/lang/Object;)Ljava/lang/Class;

    move-result-object v3

    aput-object v3, v1, v2

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_1
    return-object v1
.end method

.method public static createExceptionText(Ljava/lang/String;Lgroovy/lang/MetaMethod;Ljava/lang/Object;[Ljava/lang/Object;Ljava/lang/Throwable;Z)Lgroovy/lang/GroovyRuntimeException;
    .locals 2

    .line 683
    new-instance v0, Lgroovy/lang/GroovyRuntimeException;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p0

    const-string p1, " on: "

    invoke-virtual {p0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p0

    const-string p1, " with arguments: "

    invoke-virtual {p0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    .line 689
    invoke-static {p3}, Lorg/codehaus/groovy/runtime/InvokerHelper;->toString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    const-string p1, " reason: "

    invoke-virtual {p0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0, p4}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    if-eqz p5, :cond_0

    goto :goto_0

    :cond_0
    const/4 p4, 0x0

    .line 692
    :goto_0
    invoke-direct {v0, p0, p4}, Lgroovy/lang/GroovyRuntimeException;-><init>(Ljava/lang/String;Ljava/lang/Throwable;)V

    return-object v0
.end method

.method public static doSetMetaClass(Ljava/lang/Object;Lgroovy/lang/MetaClass;)V
    .locals 1

    .line 995
    instance-of v0, p0, Lgroovy/lang/GroovyObject;

    if-eqz v0, :cond_0

    .line 996
    check-cast p0, Lgroovy/lang/GroovyObject;

    invoke-static {p0, p1}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->setMetaClass(Lgroovy/lang/GroovyObject;Lgroovy/lang/MetaClass;)V

    goto :goto_0

    .line 998
    :cond_0
    invoke-static {p0, p1}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->setMetaClass(Ljava/lang/Object;Lgroovy/lang/MetaClass;)V

    :goto_0
    return-void
.end method

.method protected static getClassName(Ljava/lang/Object;)Ljava/lang/String;
    .locals 1

    if-nez p0, :cond_0

    const/4 p0, 0x0

    return-object p0

    .line 697
    :cond_0
    instance-of v0, p0, Ljava/lang/Class;

    if-eqz v0, :cond_1

    check-cast p0, Ljava/lang/Class;

    goto :goto_0

    :cond_1
    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object p0

    :goto_0
    invoke-virtual {p0}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method private static getClassWithNullAndWrapper(Ljava/lang/Object;)Ljava/lang/Class;
    .locals 1

    if-nez p0, :cond_0

    const/4 p0, 0x0

    return-object p0

    .line 884
    :cond_0
    instance-of v0, p0, Lorg/codehaus/groovy/runtime/wrappers/Wrapper;

    if-eqz v0, :cond_1

    .line 885
    check-cast p0, Lorg/codehaus/groovy/runtime/wrappers/Wrapper;

    .line 886
    invoke-virtual {p0}, Lorg/codehaus/groovy/runtime/wrappers/Wrapper;->getType()Ljava/lang/Class;

    move-result-object p0

    return-object p0

    .line 888
    :cond_1
    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object p0

    return-object p0
.end method

.method private static getMaximumInterfaceDistance(Ljava/lang/Class;Ljava/lang/Class;)I
    .locals 6

    const/4 v0, -0x1

    if-nez p0, :cond_0

    return v0

    :cond_0
    const/4 v1, 0x0

    if-ne p0, p1, :cond_1

    return v1

    .line 275
    :cond_1
    invoke-virtual {p0}, Ljava/lang/Class;->getInterfaces()[Ljava/lang/Class;

    move-result-object v2

    .line 277
    array-length v3, v2

    move v4, v0

    :goto_0
    if-ge v1, v3, :cond_3

    aget-object v5, v2, v1

    .line 278
    invoke-static {v5, p1}, Lorg/codehaus/groovy/runtime/MetaClassHelper;->getMaximumInterfaceDistance(Ljava/lang/Class;Ljava/lang/Class;)I

    move-result v5

    if-eq v5, v0, :cond_2

    add-int/lit8 v5, v5, 0x1

    .line 285
    :cond_2
    invoke-static {v4, v5}, Ljava/lang/Math;->max(II)I

    move-result v4

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 288
    :cond_3
    invoke-virtual {p0}, Ljava/lang/Class;->getSuperclass()Ljava/lang/Class;

    move-result-object p0

    invoke-static {p0, p1}, Lorg/codehaus/groovy/runtime/MetaClassHelper;->getMaximumInterfaceDistance(Ljava/lang/Class;Ljava/lang/Class;)I

    move-result p0

    if-eq p0, v0, :cond_4

    add-int/lit8 p0, p0, 0x1

    .line 290
    :cond_4
    invoke-static {v4, p0}, Ljava/lang/Math;->max(II)I

    move-result p0

    return p0
.end method

.method public static getMethodPointer(Ljava/lang/Object;Ljava/lang/String;)Lgroovy/lang/Closure;
    .locals 1

    .line 710
    new-instance v0, Lorg/codehaus/groovy/runtime/MethodClosure;

    invoke-direct {v0, p0, p1}, Lorg/codehaus/groovy/runtime/MethodClosure;-><init>(Ljava/lang/Object;Ljava/lang/String;)V

    return-object v0
.end method

.method private static getPrimitiveDistance(Ljava/lang/Class;Ljava/lang/Class;)I
    .locals 1

    .line 264
    invoke-static {p0}, Lorg/codehaus/groovy/runtime/MetaClassHelper;->getPrimitiveIndex(Ljava/lang/Class;)I

    move-result p0

    .line 265
    invoke-static {p1}, Lorg/codehaus/groovy/runtime/MetaClassHelper;->getPrimitiveIndex(Ljava/lang/Class;)I

    move-result p1

    const/4 v0, -0x1

    if-eq p0, v0, :cond_1

    if-ne p1, v0, :cond_0

    goto :goto_0

    .line 267
    :cond_0
    sget-object v0, Lorg/codehaus/groovy/runtime/MetaClassHelper;->PRIMITIVE_DISTANCE_TABLE:[[I

    aget-object p1, v0, p1

    aget p0, p1, p0

    return p0

    :cond_1
    :goto_0
    return v0
.end method

.method private static getPrimitiveIndex(Ljava/lang/Class;)I
    .locals 3

    const/4 v0, 0x0

    .line 255
    :goto_0
    sget-object v1, Lorg/codehaus/groovy/runtime/MetaClassHelper;->PRIMITIVES:[Ljava/lang/Class;

    array-length v2, v1

    if-ge v0, v2, :cond_1

    .line 256
    aget-object v1, v1, v0

    if-ne v1, p0, :cond_0

    return v0

    :cond_0
    add-int/lit8 v0, v0, 0x1

    int-to-byte v0, v0

    goto :goto_0

    :cond_1
    const/4 p0, -0x1

    return p0
.end method

.method public static isAssignableFrom(Ljava/lang/Class;Ljava/lang/Class;)Z
    .locals 2

    const/4 v0, 0x1

    if-eq p0, p1, :cond_e

    if-eqz p1, :cond_e

    .line 714
    const-class v1, Ljava/lang/Object;

    if-ne p0, v1, :cond_0

    goto/16 :goto_0

    .line 720
    :cond_0
    invoke-static {p0}, Lorg/codehaus/groovy/reflection/ReflectionCache;->autoboxType(Ljava/lang/Class;)Ljava/lang/Class;

    move-result-object p0

    .line 721
    invoke-static {p1}, Lorg/codehaus/groovy/reflection/ReflectionCache;->autoboxType(Ljava/lang/Class;)Ljava/lang/Class;

    move-result-object p1

    if-ne p0, p1, :cond_1

    return v0

    .line 725
    :cond_1
    const-class v1, Ljava/lang/Integer;

    if-ne p0, v1, :cond_3

    .line 726
    const-class v1, Ljava/lang/Short;

    if-eq p1, v1, :cond_2

    const-class v1, Ljava/lang/Byte;

    if-eq p1, v1, :cond_2

    const-class v1, Ljava/math/BigInteger;

    if-ne p1, v1, :cond_d

    :cond_2
    return v0

    .line 730
    :cond_3
    const-class v1, Ljava/lang/Double;

    if-ne p0, v1, :cond_5

    .line 731
    const-class v1, Ljava/lang/Integer;

    if-eq p1, v1, :cond_4

    const-class v1, Ljava/lang/Long;

    if-eq p1, v1, :cond_4

    const-class v1, Ljava/lang/Short;

    if-eq p1, v1, :cond_4

    const-class v1, Ljava/lang/Byte;

    if-eq p1, v1, :cond_4

    const-class v1, Ljava/lang/Float;

    if-eq p1, v1, :cond_4

    const-class v1, Ljava/math/BigDecimal;

    if-eq p1, v1, :cond_4

    const-class v1, Ljava/math/BigInteger;

    if-ne p1, v1, :cond_d

    :cond_4
    return v0

    .line 739
    :cond_5
    const-class v1, Ljava/math/BigDecimal;

    if-ne p0, v1, :cond_7

    .line 740
    const-class v1, Ljava/lang/Double;

    if-eq p1, v1, :cond_6

    const-class v1, Ljava/lang/Integer;

    if-eq p1, v1, :cond_6

    const-class v1, Ljava/lang/Long;

    if-eq p1, v1, :cond_6

    const-class v1, Ljava/lang/Short;

    if-eq p1, v1, :cond_6

    const-class v1, Ljava/lang/Byte;

    if-eq p1, v1, :cond_6

    const-class v1, Ljava/lang/Float;

    if-eq p1, v1, :cond_6

    const-class v1, Ljava/math/BigInteger;

    if-ne p1, v1, :cond_d

    :cond_6
    return v0

    .line 748
    :cond_7
    const-class v1, Ljava/math/BigInteger;

    if-ne p0, v1, :cond_8

    .line 749
    invoke-static {p1}, Lorg/codehaus/groovy/runtime/MetaClassHelper;->isIntegerLongShortByte(Ljava/lang/Class;)Z

    move-result v1

    if-eqz v1, :cond_d

    return v0

    .line 751
    :cond_8
    const-class v1, Ljava/lang/Long;

    if-ne p0, v1, :cond_a

    .line 752
    const-class v1, Ljava/lang/Integer;

    if-eq p1, v1, :cond_9

    const-class v1, Ljava/lang/Short;

    if-eq p1, v1, :cond_9

    const-class v1, Ljava/lang/Byte;

    if-ne p1, v1, :cond_d

    :cond_9
    return v0

    .line 756
    :cond_a
    const-class v1, Ljava/lang/Float;

    if-ne p0, v1, :cond_b

    .line 757
    invoke-static {p1}, Lorg/codehaus/groovy/runtime/MetaClassHelper;->isIntegerLongShortByte(Ljava/lang/Class;)Z

    move-result v1

    if-eqz v1, :cond_d

    return v0

    .line 759
    :cond_b
    const-class v1, Ljava/lang/Short;

    if-ne p0, v1, :cond_c

    .line 760
    const-class v1, Ljava/lang/Byte;

    if-ne p1, v1, :cond_d

    return v0

    .line 762
    :cond_c
    const-class v1, Ljava/lang/String;

    if-ne p0, v1, :cond_d

    .line 763
    const-class v1, Lgroovy/lang/GString;

    invoke-virtual {v1, p1}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    move-result v1

    if-eqz v1, :cond_d

    return v0

    .line 768
    :cond_d
    invoke-static {p0, p1}, Lorg/codehaus/groovy/reflection/ReflectionCache;->isAssignableFrom(Ljava/lang/Class;Ljava/lang/Class;)Z

    move-result p0

    return p0

    :cond_e
    :goto_0
    return v0
.end method

.method public static isGenericSetMethod(Lgroovy/lang/MetaMethod;)Z
    .locals 2

    .line 779
    invoke-virtual {p0}, Lgroovy/lang/MetaMethod;->getName()Ljava/lang/String;

    move-result-object v0

    const-string v1, "set"

    invoke-virtual {v0, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 780
    invoke-virtual {p0}, Lgroovy/lang/MetaMethod;->getParameterTypes()[Lorg/codehaus/groovy/reflection/CachedClass;

    move-result-object p0

    array-length p0, p0

    const/4 v0, 0x2

    if-ne p0, v0, :cond_0

    const/4 p0, 0x1

    goto :goto_0

    :cond_0
    const/4 p0, 0x0

    :goto_0
    return p0
.end method

.method private static isIntegerLongShortByte(Ljava/lang/Class;)Z
    .locals 1

    .line 772
    const-class v0, Ljava/lang/Integer;

    if-eq p0, v0, :cond_1

    const-class v0, Ljava/lang/Long;

    if-eq p0, v0, :cond_1

    const-class v0, Ljava/lang/Short;

    if-eq p0, v0, :cond_1

    const-class v0, Ljava/lang/Byte;

    if-ne p0, v0, :cond_0

    goto :goto_0

    :cond_0
    const/4 p0, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 p0, 0x1

    :goto_1
    return p0
.end method

.method protected static isSuperclass(Ljava/lang/Class;Ljava/lang/Class;)Z
    .locals 0

    :goto_0
    if-eqz p0, :cond_1

    if-ne p0, p1, :cond_0

    const/4 p0, 0x1

    return p0

    .line 786
    :cond_0
    invoke-virtual {p0}, Ljava/lang/Class;->getSuperclass()Ljava/lang/Class;

    move-result-object p0

    goto :goto_0

    :cond_1
    const/4 p0, 0x0

    return p0
.end method

.method public static logMethodCall(Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)V
    .locals 3

    .line 801
    invoke-static {p0}, Lorg/codehaus/groovy/runtime/MetaClassHelper;->getClassName(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p0

    .line 802
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "methodCalls."

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "."

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    .line 803
    invoke-static {v0}, Ljava/util/logging/Logger;->getLogger(Ljava/lang/String;)Ljava/util/logging/Logger;

    move-result-object v0

    .line 804
    sget-object v1, Ljava/util/logging/Level;->FINER:Ljava/util/logging/Level;

    invoke-virtual {v0, v1}, Ljava/util/logging/Logger;->isLoggable(Ljava/util/logging/Level;)Z

    move-result v1

    if-nez v1, :cond_0

    return-void

    .line 805
    :cond_0
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1, p1}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    const-string p1, "("

    .line 806
    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    if-eqz p2, :cond_2

    const/4 p1, 0x0

    .line 808
    :cond_1
    :goto_0
    array-length v2, p2

    if-ge p1, v2, :cond_2

    .line 809
    aget-object v2, p2, p1

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/MetaClassHelper;->normalizedValue(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    add-int/lit8 p1, p1, 0x1

    .line 810
    array-length v2, p2

    if-ge p1, v2, :cond_1

    const-string v2, ","

    .line 811
    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_0

    :cond_2
    const-string p1, ")"

    .line 815
    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 816
    sget-object p1, Ljava/util/logging/Level;->FINER:Ljava/util/logging/Level;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    const-string v1, "called from MetaClass.invokeMethod"

    invoke-virtual {v0, p1, p0, p2, v1}, Ljava/util/logging/Logger;->logp(Ljava/util/logging/Level;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public static makeArray(Ljava/lang/Object;Ljava/lang/Class;I)Ljava/lang/Object;
    .locals 0

    if-eqz p0, :cond_0

    .line 674
    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object p1

    .line 679
    :cond_0
    invoke-static {p1, p2}, Ljava/lang/reflect/Array;->newInstance(Ljava/lang/Class;I)Ljava/lang/Object;

    move-result-object p0

    return-object p0
.end method

.method public static makeCommonArray([Ljava/lang/Object;ILjava/lang/Class;)Ljava/lang/Object;
    .locals 8

    const/4 v0, 0x0

    move v1, p1

    move-object v2, v0

    .line 627
    :goto_0
    array-length v3, p0

    if-ge v1, v3, :cond_4

    .line 628
    aget-object v3, p0, v1

    if-nez v3, :cond_0

    goto :goto_2

    .line 629
    :cond_0
    aget-object v3, p0, v1

    invoke-virtual {v3}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v3

    if-nez v2, :cond_1

    move-object v2, v3

    goto :goto_2

    .line 633
    :cond_1
    :goto_1
    const-class v4, Ljava/lang/Object;

    if-eq v2, v4, :cond_3

    .line 634
    invoke-virtual {v2, v3}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    move-result v4

    if-eqz v4, :cond_2

    goto :goto_2

    .line 633
    :cond_2
    invoke-virtual {v2}, Ljava/lang/Class;->getSuperclass()Ljava/lang/Class;

    move-result-object v2

    goto :goto_1

    :cond_3
    :goto_2
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_4
    if-nez v2, :cond_5

    move-object v2, p2

    .line 646
    :cond_5
    const-class v1, Ljava/lang/Object;

    const/4 v3, 0x0

    if-ne v2, v1, :cond_9

    invoke-virtual {p2}, Ljava/lang/Class;->isInterface()Z

    move-result v1

    if-eqz v1, :cond_9

    move v1, p1

    move v4, v3

    .line 648
    :goto_3
    array-length v5, p0

    if-ge v1, v5, :cond_8

    .line 649
    aget-object v5, p0, v1

    if-eqz v5, :cond_7

    .line 651
    new-instance v5, Ljava/util/HashSet;

    invoke-direct {v5}, Ljava/util/HashSet;-><init>()V

    .line 652
    aget-object v6, p0, v1

    invoke-virtual {v6}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v6

    .line 653
    :goto_4
    const-class v7, Ljava/lang/Object;

    if-eq v6, v7, :cond_6

    .line 654
    invoke-virtual {v6}, Ljava/lang/Class;->getInterfaces()[Ljava/lang/Class;

    move-result-object v7

    invoke-static {v7}, Ljava/util/Arrays;->asList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v7

    invoke-interface {v5, v7}, Ljava/util/Set;->addAll(Ljava/util/Collection;)Z

    .line 653
    invoke-virtual {v6}, Ljava/lang/Class;->getSuperclass()Ljava/lang/Class;

    move-result-object v6

    goto :goto_4

    .line 656
    :cond_6
    invoke-interface {v5, p2}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result v5

    if-eqz v5, :cond_7

    add-int/lit8 v4, v4, 0x1

    :cond_7
    add-int/lit8 v1, v1, 0x1

    goto :goto_3

    .line 662
    :cond_8
    array-length v1, p0

    sub-int/2addr v1, p1

    if-ne v4, v1, :cond_9

    goto :goto_5

    :cond_9
    move-object p2, v2

    .line 666
    :goto_5
    array-length v1, p0

    sub-int/2addr v1, p1

    invoke-static {v0, p2, v1}, Lorg/codehaus/groovy/runtime/MetaClassHelper;->makeArray(Ljava/lang/Object;Ljava/lang/Class;I)Ljava/lang/Object;

    move-result-object p2

    .line 667
    array-length v0, p0

    sub-int/2addr v0, p1

    invoke-static {p0, p1, p2, v3, v0}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    return-object p2
.end method

.method protected static normalizedValue(Ljava/lang/Object;)Ljava/lang/String;
    .locals 5

    const-string v0, "\'"

    .line 822
    :try_start_0
    invoke-virtual {p0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v1

    .line 823
    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v2

    const/16 v3, 0xc

    if-le v2, v3, :cond_0

    .line 824
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const/4 v3, 0x0

    const/16 v4, 0xa

    invoke-virtual {v1, v3, v4}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v2, ".."

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    .line 826
    :cond_0
    instance-of v2, p0, Ljava/lang/String;

    if-eqz v2, :cond_1

    .line 827
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    .line 830
    :catch_0
    invoke-static {p0}, Lorg/codehaus/groovy/runtime/MetaClassHelper;->shortName(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v1

    :cond_1
    :goto_0
    return-object v1
.end method

.method public static parametersAreCompatible([Ljava/lang/Class;[Ljava/lang/Class;)Z
    .locals 5

    .line 792
    array-length v0, p0

    .line 793
    array-length v1, p1

    const/4 v2, 0x0

    if-eq v0, v1, :cond_0

    return v2

    :cond_0
    move v1, v2

    :goto_0
    if-ge v1, v0, :cond_2

    .line 795
    aget-object v3, p1, v1

    aget-object v4, p0, v1

    invoke-static {v3, v4}, Lorg/codehaus/groovy/runtime/MetaClassHelper;->isAssignableFrom(Ljava/lang/Class;Ljava/lang/Class;)Z

    move-result v3

    if-nez v3, :cond_1

    return v2

    :cond_1
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_2
    const/4 p0, 0x1

    return p0
.end method

.method public static sameClass([Ljava/lang/Class;Ljava/lang/Object;)Z
    .locals 1

    const/4 v0, 0x0

    .line 950
    aget-object p0, p0, v0

    invoke-static {p1}, Lorg/codehaus/groovy/runtime/MetaClassHelper;->getClassWithNullAndWrapper(Ljava/lang/Object;)Ljava/lang/Class;

    move-result-object p1

    if-ne p0, p1, :cond_0

    const/4 v0, 0x1

    :cond_0
    return v0
.end method

.method public static sameClasses([Ljava/lang/Class;)Z
    .locals 0

    .line 910
    array-length p0, p0

    if-nez p0, :cond_0

    const/4 p0, 0x1

    goto :goto_0

    :cond_0
    const/4 p0, 0x0

    :goto_0
    return p0
.end method

.method public static sameClasses([Ljava/lang/Class;Ljava/lang/Object;)Z
    .locals 3

    .line 914
    array-length v0, p0

    const/4 v1, 0x1

    const/4 v2, 0x0

    if-eq v0, v1, :cond_0

    return v2

    .line 917
    :cond_0
    aget-object p0, p0, v2

    invoke-static {p1}, Lorg/codehaus/groovy/runtime/MetaClassHelper;->getClassWithNullAndWrapper(Ljava/lang/Object;)Ljava/lang/Class;

    move-result-object p1

    if-eq p0, p1, :cond_1

    return v2

    :cond_1
    return v1
.end method

.method public static sameClasses([Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;)Z
    .locals 3

    .line 923
    array-length v0, p0

    const/4 v1, 0x0

    const/4 v2, 0x2

    if-eq v0, v2, :cond_0

    return v1

    .line 926
    :cond_0
    aget-object v0, p0, v1

    invoke-static {p1}, Lorg/codehaus/groovy/runtime/MetaClassHelper;->getClassWithNullAndWrapper(Ljava/lang/Object;)Ljava/lang/Class;

    move-result-object p1

    if-eq v0, p1, :cond_1

    return v1

    :cond_1
    const/4 p1, 0x1

    .line 927
    aget-object p0, p0, p1

    invoke-static {p2}, Lorg/codehaus/groovy/runtime/MetaClassHelper;->getClassWithNullAndWrapper(Ljava/lang/Object;)Ljava/lang/Class;

    move-result-object p2

    if-ne p0, p2, :cond_2

    move v1, p1

    :cond_2
    return v1
.end method

.method public static sameClasses([Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Z
    .locals 3

    .line 931
    array-length v0, p0

    const/4 v1, 0x0

    const/4 v2, 0x3

    if-eq v0, v2, :cond_0

    return v1

    .line 934
    :cond_0
    aget-object v0, p0, v1

    invoke-static {p1}, Lorg/codehaus/groovy/runtime/MetaClassHelper;->getClassWithNullAndWrapper(Ljava/lang/Object;)Ljava/lang/Class;

    move-result-object p1

    if-eq v0, p1, :cond_1

    return v1

    :cond_1
    const/4 p1, 0x1

    .line 935
    aget-object v0, p0, p1

    invoke-static {p2}, Lorg/codehaus/groovy/runtime/MetaClassHelper;->getClassWithNullAndWrapper(Ljava/lang/Object;)Ljava/lang/Class;

    move-result-object p2

    if-eq v0, p2, :cond_2

    return v1

    :cond_2
    const/4 p2, 0x2

    .line 936
    aget-object p0, p0, p2

    invoke-static {p3}, Lorg/codehaus/groovy/runtime/MetaClassHelper;->getClassWithNullAndWrapper(Ljava/lang/Object;)Ljava/lang/Class;

    move-result-object p2

    if-ne p0, p2, :cond_3

    move v1, p1

    :cond_3
    return v1
.end method

.method public static sameClasses([Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Z
    .locals 3

    .line 940
    array-length v0, p0

    const/4 v1, 0x0

    const/4 v2, 0x4

    if-eq v0, v2, :cond_0

    return v1

    .line 943
    :cond_0
    aget-object v0, p0, v1

    invoke-static {p1}, Lorg/codehaus/groovy/runtime/MetaClassHelper;->getClassWithNullAndWrapper(Ljava/lang/Object;)Ljava/lang/Class;

    move-result-object p1

    if-eq v0, p1, :cond_1

    return v1

    :cond_1
    const/4 p1, 0x1

    .line 944
    aget-object v0, p0, p1

    invoke-static {p2}, Lorg/codehaus/groovy/runtime/MetaClassHelper;->getClassWithNullAndWrapper(Ljava/lang/Object;)Ljava/lang/Class;

    move-result-object p2

    if-eq v0, p2, :cond_2

    return v1

    :cond_2
    const/4 p2, 0x2

    .line 945
    aget-object p2, p0, p2

    invoke-static {p3}, Lorg/codehaus/groovy/runtime/MetaClassHelper;->getClassWithNullAndWrapper(Ljava/lang/Object;)Ljava/lang/Class;

    move-result-object p3

    if-eq p2, p3, :cond_3

    return v1

    :cond_3
    const/4 p2, 0x3

    .line 946
    aget-object p0, p0, p2

    invoke-static {p4}, Lorg/codehaus/groovy/runtime/MetaClassHelper;->getClassWithNullAndWrapper(Ljava/lang/Object;)Ljava/lang/Class;

    move-result-object p2

    if-ne p0, p2, :cond_4

    move v1, p1

    :cond_4
    return v1
.end method

.method public static sameClasses([Ljava/lang/Class;[Ljava/lang/Object;)Z
    .locals 5

    .line 892
    array-length v0, p0

    array-length v1, p1

    const/4 v2, 0x0

    if-eq v0, v1, :cond_0

    return v2

    .line 895
    :cond_0
    array-length v0, p0

    const/4 v1, 0x1

    sub-int/2addr v0, v1

    :goto_0
    if-ltz v0, :cond_3

    .line 896
    aget-object v3, p1, v0

    if-nez v3, :cond_1

    .line 898
    aget-object v3, p0, v0

    if-eqz v3, :cond_2

    return v2

    .line 901
    :cond_1
    aget-object v4, p0, v0

    invoke-static {v3}, Lorg/codehaus/groovy/runtime/MetaClassHelper;->getClassWithNullAndWrapper(Ljava/lang/Object;)Ljava/lang/Class;

    move-result-object v3

    if-eq v4, v3, :cond_2

    return v2

    :cond_2
    add-int/lit8 v0, v0, -0x1

    goto :goto_0

    :cond_3
    return v1
.end method

.method public static sameClasses([Ljava/lang/Class;[Ljava/lang/Object;Z)Z
    .locals 4

    .line 870
    array-length p2, p0

    array-length v0, p1

    const/4 v1, 0x0

    if-eq p2, v0, :cond_0

    return v1

    .line 873
    :cond_0
    array-length p2, p0

    const/4 v0, 0x1

    sub-int/2addr p2, v0

    :goto_0
    if-ltz p2, :cond_2

    .line 874
    aget-object v2, p1, p2

    .line 875
    invoke-static {v2}, Lorg/codehaus/groovy/runtime/MetaClassHelper;->getClassWithNullAndWrapper(Ljava/lang/Object;)Ljava/lang/Class;

    move-result-object v2

    .line 876
    aget-object v3, p0, p2

    if-eq v3, v2, :cond_1

    return v1

    :cond_1
    add-int/lit8 p2, p2, -0x1

    goto :goto_0

    :cond_2
    return v0
.end method

.method protected static shortName(Ljava/lang/Object;)Ljava/lang/String;
    .locals 2

    if-eqz p0, :cond_4

    .line 836
    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    if-nez v0, :cond_0

    goto :goto_1

    .line 837
    :cond_0
    invoke-static {p0}, Lorg/codehaus/groovy/runtime/MetaClassHelper;->getClassName(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p0

    if-nez p0, :cond_1

    const-string p0, "unknownClassName"

    return-object p0

    :cond_1
    const/16 v0, 0x2e

    .line 839
    invoke-virtual {p0, v0}, Ljava/lang/String;->lastIndexOf(I)I

    move-result v0

    if-ltz v0, :cond_3

    .line 840
    invoke-virtual {p0}, Ljava/lang/String;->length()I

    move-result v1

    add-int/lit8 v1, v1, -0x1

    if-lt v0, v1, :cond_2

    goto :goto_0

    :cond_2
    add-int/lit8 v0, v0, 0x1

    .line 841
    invoke-virtual {p0, v0}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object p0

    :cond_3
    :goto_0
    return-object p0

    :cond_4
    :goto_1
    const-string p0, "unknownClass"

    return-object p0
.end method

.method public static unwrap([Ljava/lang/Object;)V
    .locals 2

    const/4 v0, 0x0

    .line 976
    :goto_0
    array-length v1, p0

    if-eq v0, v1, :cond_1

    .line 977
    aget-object v1, p0, v0

    instance-of v1, v1, Lorg/codehaus/groovy/runtime/wrappers/Wrapper;

    if-eqz v1, :cond_0

    .line 978
    aget-object v1, p0, v0

    check-cast v1, Lorg/codehaus/groovy/runtime/wrappers/Wrapper;

    invoke-virtual {v1}, Lorg/codehaus/groovy/runtime/wrappers/Wrapper;->unwrap()Ljava/lang/Object;

    move-result-object v1

    aput-object v1, p0, v0

    :cond_0
    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_1
    return-void
.end method

.method public static wrap([Ljava/lang/Class;)[Ljava/lang/Class;
    .locals 5

    .line 845
    array-length v0, p0

    new-array v1, v0, [Ljava/lang/Class;

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v0, :cond_7

    .line 847
    aget-object v3, p0, v2

    if-nez v3, :cond_0

    goto :goto_2

    .line 849
    :cond_0
    invoke-virtual {v3}, Ljava/lang/Class;->isPrimitive()Z

    move-result v4

    if-eqz v4, :cond_5

    .line 850
    sget-object v4, Ljava/lang/Integer;->TYPE:Ljava/lang/Class;

    if-ne v3, v4, :cond_1

    .line 851
    const-class v3, Ljava/lang/Integer;

    goto :goto_1

    .line 852
    :cond_1
    sget-object v4, Ljava/lang/Byte;->TYPE:Ljava/lang/Class;

    if-ne v3, v4, :cond_2

    .line 853
    const-class v3, Ljava/lang/Byte;

    goto :goto_1

    .line 854
    :cond_2
    sget-object v4, Ljava/lang/Long;->TYPE:Ljava/lang/Class;

    if-ne v3, v4, :cond_3

    .line 855
    const-class v3, Ljava/lang/Long;

    goto :goto_1

    .line 856
    :cond_3
    sget-object v4, Ljava/lang/Double;->TYPE:Ljava/lang/Class;

    if-ne v3, v4, :cond_4

    .line 857
    const-class v3, Ljava/lang/Double;

    goto :goto_1

    .line 858
    :cond_4
    sget-object v4, Ljava/lang/Float;->TYPE:Ljava/lang/Class;

    if-ne v3, v4, :cond_6

    .line 859
    const-class v3, Ljava/lang/Float;

    goto :goto_1

    .line 861
    :cond_5
    const-class v4, Lgroovy/lang/GString;

    invoke-static {v3, v4}, Lorg/codehaus/groovy/runtime/MetaClassHelper;->isSuperclass(Ljava/lang/Class;Ljava/lang/Class;)Z

    move-result v4

    if-eqz v4, :cond_6

    .line 862
    const-class v3, Ljava/lang/String;

    .line 864
    :cond_6
    :goto_1
    aput-object v3, v1, v2

    :goto_2
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_7
    return-object v1
.end method
