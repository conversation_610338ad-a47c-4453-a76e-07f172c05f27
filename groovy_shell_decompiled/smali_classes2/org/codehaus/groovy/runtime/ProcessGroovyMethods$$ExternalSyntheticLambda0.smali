.class public final synthetic Lorg/codehaus/groovy/runtime/ProcessGroovyMethods$$ExternalSyntheticLambda0;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic f$0:Ljava/lang/Process;

.field public final synthetic f$1:Lgroovy/lang/Closure;


# direct methods
.method public synthetic constructor <init>(Ljava/lang/Process;Lgroovy/lang/Closure;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/codehaus/groovy/runtime/ProcessGroovyMethods$$ExternalSyntheticLambda0;->f$0:Ljava/lang/Process;

    iput-object p2, p0, Lorg/codehaus/groovy/runtime/ProcessGroovyMethods$$ExternalSyntheticLambda0;->f$1:Lgroovy/lang/Closure;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 2

    iget-object v0, p0, Lorg/codehaus/groovy/runtime/ProcessGroovyMethods$$ExternalSyntheticLambda0;->f$0:Ljava/lang/Process;

    iget-object v1, p0, Lorg/codehaus/groovy/runtime/ProcessGroovyMethods$$ExternalSyntheticLambda0;->f$1:Lgroovy/lang/Closure;

    invoke-static {v0, v1}, Lorg/codehaus/groovy/runtime/ProcessGroovyMethods;->lambda$withOutputStream$1(Ljava/lang/Process;Lgroovy/lang/Closure;)V

    return-void
.end method
