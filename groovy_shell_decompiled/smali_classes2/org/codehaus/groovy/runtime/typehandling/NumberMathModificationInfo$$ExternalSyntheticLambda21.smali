.class public final synthetic Lorg/codehaus/groovy/runtime/typehandling/NumberMathModificationInfo$$ExternalSyntheticLambda21;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Consumer;


# instance fields
.field public final synthetic f$0:Lorg/codehaus/groovy/runtime/typehandling/NumberMathModificationInfo;


# direct methods
.method public synthetic constructor <init>(Lorg/codehaus/groovy/runtime/typehandling/NumberMathModificationInfo;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/codehaus/groovy/runtime/typehandling/NumberMathModificationInfo$$ExternalSyntheticLambda21;->f$0:Lorg/codehaus/groovy/runtime/typehandling/NumberMathModificationInfo;

    return-void
.end method


# virtual methods
.method public final accept(Ljava/lang/Object;)V
    .locals 1

    iget-object v0, p0, Lorg/codehaus/groovy/runtime/typehandling/NumberMathModificationInfo$$ExternalSyntheticLambda21;->f$0:Lorg/codehaus/groovy/runtime/typehandling/NumberMathModificationInfo;

    check-cast p1, Ljava/lang/Class;

    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/runtime/typehandling/NumberMathModificationInfo;->lambda$checkNumberOps$35$org-codehaus-groovy-runtime-typehandling-NumberMathModificationInfo(Ljava/lang/Class;)V

    return-void
.end method
