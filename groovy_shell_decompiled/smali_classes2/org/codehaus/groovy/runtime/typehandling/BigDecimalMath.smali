.class public final Lorg/codehaus/groovy/runtime/typehandling/BigDecimalMath;
.super Lorg/codehaus/groovy/runtime/typehandling/NumberMath;
.source "BigDecimalMath.java"


# static fields
.field public static final DIVISION_EXTRA_PRECISION:I

.field public static final DIVISION_MIN_SCALE:I

.field public static final INSTANCE:Lorg/codehaus/groovy/runtime/typehandling/BigDecimalMath;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    const/16 v0, 0xa

    .line 33
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    const-string v1, "groovy.division.extra.precision"

    invoke-static {v1, v0}, Lorg/apache/groovy/util/SystemUtil;->getIntegerSafe(Ljava/lang/String;Ljava/lang/Integer;)Ljava/lang/Integer;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/Integer;->intValue()I

    move-result v1

    sput v1, Lorg/codehaus/groovy/runtime/typehandling/BigDecimalMath;->DIVISION_EXTRA_PRECISION:I

    const-string v1, "groovy.division.min.scale"

    .line 37
    invoke-static {v1, v0}, Lorg/apache/groovy/util/SystemUtil;->getIntegerSafe(Ljava/lang/String;Ljava/lang/Integer;)Ljava/lang/Integer;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    move-result v0

    sput v0, Lorg/codehaus/groovy/runtime/typehandling/BigDecimalMath;->DIVISION_MIN_SCALE:I

    .line 39
    new-instance v0, Lorg/codehaus/groovy/runtime/typehandling/BigDecimalMath;

    invoke-direct {v0}, Lorg/codehaus/groovy/runtime/typehandling/BigDecimalMath;-><init>()V

    sput-object v0, Lorg/codehaus/groovy/runtime/typehandling/BigDecimalMath;->INSTANCE:Lorg/codehaus/groovy/runtime/typehandling/BigDecimalMath;

    return-void
.end method

.method private constructor <init>()V
    .locals 0

    .line 41
    invoke-direct {p0}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;-><init>()V

    return-void
.end method


# virtual methods
.method protected absImpl(Ljava/lang/Number;)Ljava/lang/Number;
    .locals 0

    .line 44
    invoke-static {p1}, Lorg/codehaus/groovy/runtime/typehandling/BigDecimalMath;->toBigDecimal(Ljava/lang/Number;)Ljava/math/BigDecimal;

    move-result-object p1

    invoke-virtual {p1}, Ljava/math/BigDecimal;->abs()Ljava/math/BigDecimal;

    move-result-object p1

    return-object p1
.end method

.method public addImpl(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;
    .locals 0

    .line 48
    invoke-static {p1}, Lorg/codehaus/groovy/runtime/typehandling/BigDecimalMath;->toBigDecimal(Ljava/lang/Number;)Ljava/math/BigDecimal;

    move-result-object p1

    invoke-static {p2}, Lorg/codehaus/groovy/runtime/typehandling/BigDecimalMath;->toBigDecimal(Ljava/lang/Number;)Ljava/math/BigDecimal;

    move-result-object p2

    invoke-virtual {p1, p2}, Ljava/math/BigDecimal;->add(Ljava/math/BigDecimal;)Ljava/math/BigDecimal;

    move-result-object p1

    return-object p1
.end method

.method public compareToImpl(Ljava/lang/Number;Ljava/lang/Number;)I
    .locals 0

    .line 75
    invoke-static {p1}, Lorg/codehaus/groovy/runtime/typehandling/BigDecimalMath;->toBigDecimal(Ljava/lang/Number;)Ljava/math/BigDecimal;

    move-result-object p1

    invoke-static {p2}, Lorg/codehaus/groovy/runtime/typehandling/BigDecimalMath;->toBigDecimal(Ljava/lang/Number;)Ljava/math/BigDecimal;

    move-result-object p2

    invoke-virtual {p1, p2}, Ljava/math/BigDecimal;->compareTo(Ljava/math/BigDecimal;)I

    move-result p1

    return p1
.end method

.method public divideImpl(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;
    .locals 2

    .line 60
    invoke-static {p1}, Lorg/codehaus/groovy/runtime/typehandling/BigDecimalMath;->toBigDecimal(Ljava/lang/Number;)Ljava/math/BigDecimal;

    move-result-object p1

    .line 61
    invoke-static {p2}, Lorg/codehaus/groovy/runtime/typehandling/BigDecimalMath;->toBigDecimal(Ljava/lang/Number;)Ljava/math/BigDecimal;

    move-result-object p2

    .line 63
    :try_start_0
    invoke-virtual {p1, p2}, Ljava/math/BigDecimal;->divide(Ljava/math/BigDecimal;)Ljava/math/BigDecimal;

    move-result-object p1
    :try_end_0
    .catch Ljava/lang/ArithmeticException; {:try_start_0 .. :try_end_0} :catch_0

    return-object p1

    .line 66
    :catch_0
    invoke-virtual {p1}, Ljava/math/BigDecimal;->precision()I

    move-result v0

    invoke-virtual {p2}, Ljava/math/BigDecimal;->precision()I

    move-result v1

    invoke-static {v0, v1}, Ljava/lang/Math;->max(II)I

    move-result v0

    sget v1, Lorg/codehaus/groovy/runtime/typehandling/BigDecimalMath;->DIVISION_EXTRA_PRECISION:I

    add-int/2addr v0, v1

    .line 67
    new-instance v1, Ljava/math/MathContext;

    invoke-direct {v1, v0}, Ljava/math/MathContext;-><init>(I)V

    invoke-virtual {p1, p2, v1}, Ljava/math/BigDecimal;->divide(Ljava/math/BigDecimal;Ljava/math/MathContext;)Ljava/math/BigDecimal;

    move-result-object v0

    .line 68
    invoke-virtual {p1}, Ljava/math/BigDecimal;->scale()I

    move-result p1

    invoke-virtual {p2}, Ljava/math/BigDecimal;->scale()I

    move-result p2

    invoke-static {p1, p2}, Ljava/lang/Math;->max(II)I

    move-result p1

    sget p2, Lorg/codehaus/groovy/runtime/typehandling/BigDecimalMath;->DIVISION_MIN_SCALE:I

    invoke-static {p1, p2}, Ljava/lang/Math;->max(II)I

    move-result p1

    .line 69
    invoke-virtual {v0}, Ljava/math/BigDecimal;->scale()I

    move-result p2

    if-le p2, p1, :cond_0

    const/4 p2, 0x4

    invoke-virtual {v0, p1, p2}, Ljava/math/BigDecimal;->setScale(II)Ljava/math/BigDecimal;

    move-result-object v0

    :cond_0
    return-object v0
.end method

.method public multiplyImpl(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;
    .locals 0

    .line 56
    invoke-static {p1}, Lorg/codehaus/groovy/runtime/typehandling/BigDecimalMath;->toBigDecimal(Ljava/lang/Number;)Ljava/math/BigDecimal;

    move-result-object p1

    invoke-static {p2}, Lorg/codehaus/groovy/runtime/typehandling/BigDecimalMath;->toBigDecimal(Ljava/lang/Number;)Ljava/math/BigDecimal;

    move-result-object p2

    invoke-virtual {p1, p2}, Ljava/math/BigDecimal;->multiply(Ljava/math/BigDecimal;)Ljava/math/BigDecimal;

    move-result-object p1

    return-object p1
.end method

.method public subtractImpl(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;
    .locals 0

    .line 52
    invoke-static {p1}, Lorg/codehaus/groovy/runtime/typehandling/BigDecimalMath;->toBigDecimal(Ljava/lang/Number;)Ljava/math/BigDecimal;

    move-result-object p1

    invoke-static {p2}, Lorg/codehaus/groovy/runtime/typehandling/BigDecimalMath;->toBigDecimal(Ljava/lang/Number;)Ljava/math/BigDecimal;

    move-result-object p2

    invoke-virtual {p1, p2}, Ljava/math/BigDecimal;->subtract(Ljava/math/BigDecimal;)Ljava/math/BigDecimal;

    move-result-object p1

    return-object p1
.end method

.method protected unaryMinusImpl(Ljava/lang/Number;)Ljava/lang/Number;
    .locals 0

    .line 79
    invoke-static {p1}, Lorg/codehaus/groovy/runtime/typehandling/BigDecimalMath;->toBigDecimal(Ljava/lang/Number;)Ljava/math/BigDecimal;

    move-result-object p1

    invoke-virtual {p1}, Ljava/math/BigDecimal;->negate()Ljava/math/BigDecimal;

    move-result-object p1

    return-object p1
.end method

.method protected unaryPlusImpl(Ljava/lang/Number;)Ljava/lang/Number;
    .locals 0

    .line 83
    invoke-static {p1}, Lorg/codehaus/groovy/runtime/typehandling/BigDecimalMath;->toBigDecimal(Ljava/lang/Number;)Ljava/math/BigDecimal;

    move-result-object p1

    return-object p1
.end method
