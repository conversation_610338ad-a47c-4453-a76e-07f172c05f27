.class public abstract Lorg/codehaus/groovy/runtime/typehandling/NumberMath;
.super Ljava/lang/Object;
.source "NumberMath.java"


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 45
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static abs(Ljava/lang/Number;)Ljava/lang/Number;
    .locals 1

    .line 48
    invoke-static {p0}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->getMath(Ljava/lang/Number;)Lorg/codehaus/groovy/runtime/typehandling/NumberMath;

    move-result-object v0

    invoke-virtual {v0, p0}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->absImpl(Ljava/lang/Number;)Ljava/lang/Number;

    move-result-object p0

    return-object p0
.end method

.method public static add(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;
    .locals 1

    .line 52
    invoke-static {p0, p1}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->getMath(Ljava/lang/Number;Ljava/lang/Number;)Lorg/codehaus/groovy/runtime/typehandling/NumberMath;

    move-result-object v0

    invoke-virtual {v0, p0, p1}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->addImpl(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;

    move-result-object p0

    return-object p0
.end method

.method public static and(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;
    .locals 1

    .line 76
    invoke-static {p0, p1}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->getMath(Ljava/lang/Number;Ljava/lang/Number;)Lorg/codehaus/groovy/runtime/typehandling/NumberMath;

    move-result-object v0

    invoke-virtual {v0, p0, p1}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->andImpl(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;

    move-result-object p0

    return-object p0
.end method

.method public static bitwiseNegate(Ljava/lang/Number;)Ljava/lang/Number;
    .locals 1

    .line 131
    invoke-static {p0}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->getMath(Ljava/lang/Number;)Lorg/codehaus/groovy/runtime/typehandling/NumberMath;

    move-result-object v0

    invoke-virtual {v0, p0}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->bitwiseNegateImpl(Ljava/lang/Number;)Ljava/lang/Number;

    move-result-object p0

    return-object p0
.end method

.method public static compareTo(Ljava/lang/Number;Ljava/lang/Number;)I
    .locals 1

    .line 68
    invoke-static {p0, p1}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->getMath(Ljava/lang/Number;Ljava/lang/Number;)Lorg/codehaus/groovy/runtime/typehandling/NumberMath;

    move-result-object v0

    invoke-virtual {v0, p0, p1}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->compareToImpl(Ljava/lang/Number;Ljava/lang/Number;)I

    move-result p0

    return p0
.end method

.method public static divide(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;
    .locals 1

    .line 64
    invoke-static {p0, p1}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->getMath(Ljava/lang/Number;Ljava/lang/Number;)Lorg/codehaus/groovy/runtime/typehandling/NumberMath;

    move-result-object v0

    invoke-virtual {v0, p0, p1}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->divideImpl(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;

    move-result-object p0

    return-object p0
.end method

.method static getMath(Ljava/lang/Number;)Lorg/codehaus/groovy/runtime/typehandling/NumberMath;
    .locals 1

    .line 243
    invoke-static {p0}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->isLong(Ljava/lang/Number;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 244
    sget-object p0, Lorg/codehaus/groovy/runtime/typehandling/LongMath;->INSTANCE:Lorg/codehaus/groovy/runtime/typehandling/LongMath;

    return-object p0

    .line 246
    :cond_0
    invoke-static {p0}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->isFloatingPoint(Ljava/lang/Number;)Z

    move-result v0

    if-eqz v0, :cond_1

    .line 247
    sget-object p0, Lorg/codehaus/groovy/runtime/typehandling/FloatingPointMath;->INSTANCE:Lorg/codehaus/groovy/runtime/typehandling/FloatingPointMath;

    return-object p0

    .line 249
    :cond_1
    invoke-static {p0}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->isBigDecimal(Ljava/lang/Number;)Z

    move-result v0

    if-eqz v0, :cond_2

    .line 250
    sget-object p0, Lorg/codehaus/groovy/runtime/typehandling/BigDecimalMath;->INSTANCE:Lorg/codehaus/groovy/runtime/typehandling/BigDecimalMath;

    return-object p0

    .line 252
    :cond_2
    invoke-static {p0}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->isBigInteger(Ljava/lang/Number;)Z

    move-result v0

    if-eqz v0, :cond_3

    .line 253
    sget-object p0, Lorg/codehaus/groovy/runtime/typehandling/BigIntegerMath;->INSTANCE:Lorg/codehaus/groovy/runtime/typehandling/BigIntegerMath;

    return-object p0

    .line 255
    :cond_3
    invoke-static {p0}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->isInteger(Ljava/lang/Number;)Z

    move-result v0

    if-nez v0, :cond_5

    invoke-static {p0}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->isShort(Ljava/lang/Number;)Z

    move-result v0

    if-nez v0, :cond_5

    invoke-static {p0}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->isByte(Ljava/lang/Number;)Z

    move-result p0

    if-eqz p0, :cond_4

    goto :goto_0

    .line 259
    :cond_4
    sget-object p0, Lorg/codehaus/groovy/runtime/typehandling/BigDecimalMath;->INSTANCE:Lorg/codehaus/groovy/runtime/typehandling/BigDecimalMath;

    return-object p0

    .line 256
    :cond_5
    :goto_0
    sget-object p0, Lorg/codehaus/groovy/runtime/typehandling/IntegerMath;->INSTANCE:Lorg/codehaus/groovy/runtime/typehandling/IntegerMath;

    return-object p0
.end method

.method public static getMath(Ljava/lang/Number;Ljava/lang/Number;)Lorg/codehaus/groovy/runtime/typehandling/NumberMath;
    .locals 1

    .line 220
    invoke-static {p0}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->isFloatingPoint(Ljava/lang/Number;)Z

    move-result v0

    if-nez v0, :cond_9

    invoke-static {p1}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->isFloatingPoint(Ljava/lang/Number;)Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_4

    .line 223
    :cond_0
    invoke-static {p0}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->getMath(Ljava/lang/Number;)Lorg/codehaus/groovy/runtime/typehandling/NumberMath;

    move-result-object p0

    .line 224
    invoke-static {p1}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->getMath(Ljava/lang/Number;)Lorg/codehaus/groovy/runtime/typehandling/NumberMath;

    move-result-object p1

    .line 226
    sget-object v0, Lorg/codehaus/groovy/runtime/typehandling/BigDecimalMath;->INSTANCE:Lorg/codehaus/groovy/runtime/typehandling/BigDecimalMath;

    if-eq p0, v0, :cond_8

    sget-object v0, Lorg/codehaus/groovy/runtime/typehandling/BigDecimalMath;->INSTANCE:Lorg/codehaus/groovy/runtime/typehandling/BigDecimalMath;

    if-ne p1, v0, :cond_1

    goto :goto_3

    .line 229
    :cond_1
    sget-object v0, Lorg/codehaus/groovy/runtime/typehandling/BigIntegerMath;->INSTANCE:Lorg/codehaus/groovy/runtime/typehandling/BigIntegerMath;

    if-eq p0, v0, :cond_7

    sget-object v0, Lorg/codehaus/groovy/runtime/typehandling/BigIntegerMath;->INSTANCE:Lorg/codehaus/groovy/runtime/typehandling/BigIntegerMath;

    if-ne p1, v0, :cond_2

    goto :goto_2

    .line 232
    :cond_2
    sget-object v0, Lorg/codehaus/groovy/runtime/typehandling/LongMath;->INSTANCE:Lorg/codehaus/groovy/runtime/typehandling/LongMath;

    if-eq p0, v0, :cond_6

    sget-object v0, Lorg/codehaus/groovy/runtime/typehandling/LongMath;->INSTANCE:Lorg/codehaus/groovy/runtime/typehandling/LongMath;

    if-ne p1, v0, :cond_3

    goto :goto_1

    .line 235
    :cond_3
    sget-object v0, Lorg/codehaus/groovy/runtime/typehandling/IntegerMath;->INSTANCE:Lorg/codehaus/groovy/runtime/typehandling/IntegerMath;

    if-eq p0, v0, :cond_5

    sget-object p0, Lorg/codehaus/groovy/runtime/typehandling/IntegerMath;->INSTANCE:Lorg/codehaus/groovy/runtime/typehandling/IntegerMath;

    if-ne p1, p0, :cond_4

    goto :goto_0

    .line 239
    :cond_4
    sget-object p0, Lorg/codehaus/groovy/runtime/typehandling/BigDecimalMath;->INSTANCE:Lorg/codehaus/groovy/runtime/typehandling/BigDecimalMath;

    return-object p0

    .line 236
    :cond_5
    :goto_0
    sget-object p0, Lorg/codehaus/groovy/runtime/typehandling/IntegerMath;->INSTANCE:Lorg/codehaus/groovy/runtime/typehandling/IntegerMath;

    return-object p0

    .line 233
    :cond_6
    :goto_1
    sget-object p0, Lorg/codehaus/groovy/runtime/typehandling/LongMath;->INSTANCE:Lorg/codehaus/groovy/runtime/typehandling/LongMath;

    return-object p0

    .line 230
    :cond_7
    :goto_2
    sget-object p0, Lorg/codehaus/groovy/runtime/typehandling/BigIntegerMath;->INSTANCE:Lorg/codehaus/groovy/runtime/typehandling/BigIntegerMath;

    return-object p0

    .line 227
    :cond_8
    :goto_3
    sget-object p0, Lorg/codehaus/groovy/runtime/typehandling/BigDecimalMath;->INSTANCE:Lorg/codehaus/groovy/runtime/typehandling/BigDecimalMath;

    return-object p0

    .line 221
    :cond_9
    :goto_4
    sget-object p0, Lorg/codehaus/groovy/runtime/typehandling/FloatingPointMath;->INSTANCE:Lorg/codehaus/groovy/runtime/typehandling/FloatingPointMath;

    return-object p0
.end method

.method public static intdiv(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;
    .locals 1

    .line 84
    invoke-static {p0, p1}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->getMath(Ljava/lang/Number;Ljava/lang/Number;)Lorg/codehaus/groovy/runtime/typehandling/NumberMath;

    move-result-object v0

    invoke-virtual {v0, p0, p1}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->intdivImpl(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;

    move-result-object p0

    return-object p0
.end method

.method public static isBigDecimal(Ljava/lang/Number;)Z
    .locals 0

    .line 163
    instance-of p0, p0, Ljava/math/BigDecimal;

    return p0
.end method

.method public static isBigInteger(Ljava/lang/Number;)Z
    .locals 0

    .line 167
    instance-of p0, p0, Ljava/math/BigInteger;

    return p0
.end method

.method public static isByte(Ljava/lang/Number;)Z
    .locals 0

    .line 155
    instance-of p0, p0, Ljava/lang/Byte;

    return p0
.end method

.method public static isFloatingPoint(Ljava/lang/Number;)Z
    .locals 1

    .line 143
    instance-of v0, p0, Ljava/lang/Double;

    if-nez v0, :cond_1

    instance-of p0, p0, Ljava/lang/Float;

    if-eqz p0, :cond_0

    goto :goto_0

    :cond_0
    const/4 p0, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 p0, 0x1

    :goto_1
    return p0
.end method

.method public static isInteger(Ljava/lang/Number;)Z
    .locals 0

    .line 147
    instance-of p0, p0, Ljava/lang/Integer;

    return p0
.end method

.method public static isLong(Ljava/lang/Number;)Z
    .locals 0

    .line 159
    instance-of p0, p0, Ljava/lang/Long;

    return p0
.end method

.method public static isShort(Ljava/lang/Number;)Z
    .locals 0

    .line 151
    instance-of p0, p0, Ljava/lang/Short;

    return p0
.end method

.method public static leftShift(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;
    .locals 2

    .line 98
    invoke-static {p1}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->isFloatingPoint(Ljava/lang/Number;)Z

    move-result v0

    if-nez v0, :cond_0

    invoke-static {p1}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->isBigDecimal(Ljava/lang/Number;)Z

    move-result v0

    if-nez v0, :cond_0

    .line 101
    invoke-static {p0}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->getMath(Ljava/lang/Number;)Lorg/codehaus/groovy/runtime/typehandling/NumberMath;

    move-result-object v0

    invoke-virtual {v0, p0, p1}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->leftShiftImpl(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;

    move-result-object p0

    return-object p0

    .line 99
    :cond_0
    new-instance p0, Ljava/lang/UnsupportedOperationException;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Shift distance must be an integral type, but "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, " ("

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, ") was supplied"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {p0, p1}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw p0
.end method

.method public static mod(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;
    .locals 1

    .line 88
    invoke-static {p0, p1}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->getMath(Ljava/lang/Number;Ljava/lang/Number;)Lorg/codehaus/groovy/runtime/typehandling/NumberMath;

    move-result-object v0

    invoke-virtual {v0, p0, p1}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->modImpl(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;

    move-result-object p0

    return-object p0
.end method

.method public static multiply(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;
    .locals 1

    .line 60
    invoke-static {p0, p1}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->getMath(Ljava/lang/Number;Ljava/lang/Number;)Lorg/codehaus/groovy/runtime/typehandling/NumberMath;

    move-result-object v0

    invoke-virtual {v0, p0, p1}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->multiplyImpl(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;

    move-result-object p0

    return-object p0
.end method

.method public static or(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;
    .locals 1

    .line 72
    invoke-static {p0, p1}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->getMath(Ljava/lang/Number;Ljava/lang/Number;)Lorg/codehaus/groovy/runtime/typehandling/NumberMath;

    move-result-object v0

    invoke-virtual {v0, p0, p1}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->orImpl(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;

    move-result-object p0

    return-object p0
.end method

.method public static rightShift(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;
    .locals 2

    .line 111
    invoke-static {p1}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->isFloatingPoint(Ljava/lang/Number;)Z

    move-result v0

    if-nez v0, :cond_0

    invoke-static {p1}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->isBigDecimal(Ljava/lang/Number;)Z

    move-result v0

    if-nez v0, :cond_0

    .line 114
    invoke-static {p0}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->getMath(Ljava/lang/Number;)Lorg/codehaus/groovy/runtime/typehandling/NumberMath;

    move-result-object v0

    invoke-virtual {v0, p0, p1}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->rightShiftImpl(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;

    move-result-object p0

    return-object p0

    .line 112
    :cond_0
    new-instance p0, Ljava/lang/UnsupportedOperationException;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Shift distance must be an integral type, but "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, " ("

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, ") was supplied"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {p0, p1}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw p0
.end method

.method public static rightShiftUnsigned(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;
    .locals 2

    .line 124
    invoke-static {p1}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->isFloatingPoint(Ljava/lang/Number;)Z

    move-result v0

    if-nez v0, :cond_0

    invoke-static {p1}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->isBigDecimal(Ljava/lang/Number;)Z

    move-result v0

    if-nez v0, :cond_0

    .line 127
    invoke-static {p0}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->getMath(Ljava/lang/Number;)Lorg/codehaus/groovy/runtime/typehandling/NumberMath;

    move-result-object v0

    invoke-virtual {v0, p0, p1}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->rightShiftUnsignedImpl(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;

    move-result-object p0

    return-object p0

    .line 125
    :cond_0
    new-instance p0, Ljava/lang/UnsupportedOperationException;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Shift distance must be an integral type, but "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, " ("

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, ") was supplied"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {p0, p1}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw p0
.end method

.method public static subtract(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;
    .locals 1

    .line 56
    invoke-static {p0, p1}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->getMath(Ljava/lang/Number;Ljava/lang/Number;)Lorg/codehaus/groovy/runtime/typehandling/NumberMath;

    move-result-object v0

    invoke-virtual {v0, p0, p1}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->subtractImpl(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;

    move-result-object p0

    return-object p0
.end method

.method public static toBigDecimal(Ljava/lang/Number;)Ljava/math/BigDecimal;
    .locals 2

    .line 171
    instance-of v0, p0, Ljava/math/BigDecimal;

    if-eqz v0, :cond_0

    .line 172
    check-cast p0, Ljava/math/BigDecimal;

    return-object p0

    .line 174
    :cond_0
    instance-of v0, p0, Ljava/math/BigInteger;

    if-eqz v0, :cond_1

    .line 175
    new-instance v0, Ljava/math/BigDecimal;

    check-cast p0, Ljava/math/BigInteger;

    invoke-direct {v0, p0}, Ljava/math/BigDecimal;-><init>(Ljava/math/BigInteger;)V

    return-object v0

    .line 177
    :cond_1
    instance-of v0, p0, Ljava/lang/Integer;

    if-nez v0, :cond_3

    instance-of v0, p0, Ljava/lang/Long;

    if-nez v0, :cond_3

    instance-of v0, p0, Ljava/lang/Byte;

    if-nez v0, :cond_3

    instance-of v0, p0, Ljava/lang/Short;

    if-eqz v0, :cond_2

    goto :goto_0

    .line 180
    :cond_2
    new-instance v0, Ljava/math/BigDecimal;

    invoke-virtual {p0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object p0

    invoke-direct {v0, p0}, Ljava/math/BigDecimal;-><init>(Ljava/lang/String;)V

    return-object v0

    .line 178
    :cond_3
    :goto_0
    invoke-virtual {p0}, Ljava/lang/Number;->longValue()J

    move-result-wide v0

    invoke-static {v0, v1}, Ljava/math/BigDecimal;->valueOf(J)Ljava/math/BigDecimal;

    move-result-object p0

    return-object p0
.end method

.method public static toBigInteger(Ljava/lang/Number;)Ljava/math/BigInteger;
    .locals 2

    .line 184
    instance-of v0, p0, Ljava/math/BigInteger;

    if-eqz v0, :cond_0

    .line 185
    check-cast p0, Ljava/math/BigInteger;

    return-object p0

    .line 187
    :cond_0
    instance-of v0, p0, Ljava/lang/Integer;

    if-nez v0, :cond_5

    instance-of v0, p0, Ljava/lang/Long;

    if-nez v0, :cond_5

    instance-of v0, p0, Ljava/lang/Byte;

    if-nez v0, :cond_5

    instance-of v0, p0, Ljava/lang/Short;

    if-eqz v0, :cond_1

    goto :goto_1

    .line 191
    :cond_1
    instance-of v0, p0, Ljava/lang/Float;

    if-nez v0, :cond_4

    instance-of v0, p0, Ljava/lang/Double;

    if-eqz v0, :cond_2

    goto :goto_0

    .line 195
    :cond_2
    instance-of v0, p0, Ljava/math/BigDecimal;

    if-eqz v0, :cond_3

    .line 196
    check-cast p0, Ljava/math/BigDecimal;

    invoke-virtual {p0}, Ljava/math/BigDecimal;->toBigInteger()Ljava/math/BigInteger;

    move-result-object p0

    return-object p0

    .line 199
    :cond_3
    new-instance v0, Ljava/math/BigInteger;

    invoke-virtual {p0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object p0

    invoke-direct {v0, p0}, Ljava/math/BigInteger;-><init>(Ljava/lang/String;)V

    return-object v0

    .line 192
    :cond_4
    :goto_0
    new-instance v0, Ljava/math/BigDecimal;

    invoke-virtual {p0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object p0

    invoke-direct {v0, p0}, Ljava/math/BigDecimal;-><init>(Ljava/lang/String;)V

    .line 193
    invoke-virtual {v0}, Ljava/math/BigDecimal;->toBigInteger()Ljava/math/BigInteger;

    move-result-object p0

    return-object p0

    .line 188
    :cond_5
    :goto_1
    invoke-virtual {p0}, Ljava/lang/Number;->longValue()J

    move-result-wide v0

    invoke-static {v0, v1}, Ljava/math/BigInteger;->valueOf(J)Ljava/math/BigInteger;

    move-result-object p0

    return-object p0
.end method

.method public static unaryMinus(Ljava/lang/Number;)Ljava/lang/Number;
    .locals 1

    .line 135
    invoke-static {p0}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->getMath(Ljava/lang/Number;)Lorg/codehaus/groovy/runtime/typehandling/NumberMath;

    move-result-object v0

    invoke-virtual {v0, p0}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->unaryMinusImpl(Ljava/lang/Number;)Ljava/lang/Number;

    move-result-object p0

    return-object p0
.end method

.method public static unaryPlus(Ljava/lang/Number;)Ljava/lang/Number;
    .locals 1

    .line 139
    invoke-static {p0}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->getMath(Ljava/lang/Number;)Lorg/codehaus/groovy/runtime/typehandling/NumberMath;

    move-result-object v0

    invoke-virtual {v0, p0}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->unaryPlusImpl(Ljava/lang/Number;)Ljava/lang/Number;

    move-result-object p0

    return-object p0
.end method

.method public static xor(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;
    .locals 1

    .line 80
    invoke-static {p0, p1}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->getMath(Ljava/lang/Number;Ljava/lang/Number;)Lorg/codehaus/groovy/runtime/typehandling/NumberMath;

    move-result-object v0

    invoke-virtual {v0, p0, p1}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->xorImpl(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method protected abstract absImpl(Ljava/lang/Number;)Ljava/lang/Number;
.end method

.method public abstract addImpl(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;
.end method

.method protected andImpl(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;
    .locals 0

    const-string p2, "and()"

    .line 288
    invoke-virtual {p0, p2, p1}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->createUnsupportedException(Ljava/lang/String;Ljava/lang/Number;)Ljava/lang/UnsupportedOperationException;

    move-result-object p1

    throw p1
.end method

.method protected bitwiseNegateImpl(Ljava/lang/Number;)Ljava/lang/Number;
    .locals 1

    const-string v0, "bitwiseNegate()"

    .line 280
    invoke-virtual {p0, v0, p1}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->createUnsupportedException(Ljava/lang/String;Ljava/lang/Number;)Ljava/lang/UnsupportedOperationException;

    move-result-object p1

    throw p1
.end method

.method public abstract compareToImpl(Ljava/lang/Number;Ljava/lang/Number;)I
.end method

.method protected createUnsupportedException(Ljava/lang/String;Ljava/lang/Number;)Ljava/lang/UnsupportedOperationException;
    .locals 3

    .line 316
    new-instance v0, Ljava/lang/UnsupportedOperationException;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "Cannot use "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v1, " on this number type: "

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v1, " with value: "

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {v0, p1}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    return-object v0
.end method

.method public abstract divideImpl(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;
.end method

.method protected intdivImpl(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;
    .locals 0

    const-string p2, "intdiv()"

    .line 300
    invoke-virtual {p0, p2, p1}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->createUnsupportedException(Ljava/lang/String;Ljava/lang/Number;)Ljava/lang/UnsupportedOperationException;

    move-result-object p1

    throw p1
.end method

.method protected leftShiftImpl(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;
    .locals 0

    const-string p2, "leftShift()"

    .line 304
    invoke-virtual {p0, p2, p1}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->createUnsupportedException(Ljava/lang/String;Ljava/lang/Number;)Ljava/lang/UnsupportedOperationException;

    move-result-object p1

    throw p1
.end method

.method protected modImpl(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;
    .locals 0

    const-string p2, "mod()"

    .line 296
    invoke-virtual {p0, p2, p1}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->createUnsupportedException(Ljava/lang/String;Ljava/lang/Number;)Ljava/lang/UnsupportedOperationException;

    move-result-object p1

    throw p1
.end method

.method public abstract multiplyImpl(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;
.end method

.method protected orImpl(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;
    .locals 0

    const-string p2, "or()"

    .line 284
    invoke-virtual {p0, p2, p1}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->createUnsupportedException(Ljava/lang/String;Ljava/lang/Number;)Ljava/lang/UnsupportedOperationException;

    move-result-object p1

    throw p1
.end method

.method protected rightShiftImpl(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;
    .locals 0

    const-string p2, "rightShift()"

    .line 308
    invoke-virtual {p0, p2, p1}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->createUnsupportedException(Ljava/lang/String;Ljava/lang/Number;)Ljava/lang/UnsupportedOperationException;

    move-result-object p1

    throw p1
.end method

.method protected rightShiftUnsignedImpl(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;
    .locals 0

    const-string p2, "rightShiftUnsigned()"

    .line 312
    invoke-virtual {p0, p2, p1}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->createUnsupportedException(Ljava/lang/String;Ljava/lang/Number;)Ljava/lang/UnsupportedOperationException;

    move-result-object p1

    throw p1
.end method

.method public abstract subtractImpl(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;
.end method

.method protected abstract unaryMinusImpl(Ljava/lang/Number;)Ljava/lang/Number;
.end method

.method protected abstract unaryPlusImpl(Ljava/lang/Number;)Ljava/lang/Number;
.end method

.method protected xorImpl(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;
    .locals 0

    const-string p2, "xor()"

    .line 292
    invoke-virtual {p0, p2, p1}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->createUnsupportedException(Ljava/lang/String;Ljava/lang/Number;)Ljava/lang/UnsupportedOperationException;

    move-result-object p1

    throw p1
.end method
