.class public final Lorg/codehaus/groovy/runtime/typehandling/BigIntegerMath;
.super Lorg/codehaus/groovy/runtime/typehandling/NumberMath;
.source "BigIntegerMath.java"


# static fields
.field public static final INSTANCE:Lorg/codehaus/groovy/runtime/typehandling/BigIntegerMath;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 26
    new-instance v0, Lorg/codehaus/groovy/runtime/typehandling/BigIntegerMath;

    invoke-direct {v0}, Lorg/codehaus/groovy/runtime/typehandling/BigIntegerMath;-><init>()V

    sput-object v0, Lorg/codehaus/groovy/runtime/typehandling/BigIntegerMath;->INSTANCE:Lorg/codehaus/groovy/runtime/typehandling/BigIntegerMath;

    return-void
.end method

.method private constructor <init>()V
    .locals 0

    .line 28
    invoke-direct {p0}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;-><init>()V

    return-void
.end method


# virtual methods
.method protected absImpl(Ljava/lang/Number;)Ljava/lang/Number;
    .locals 0

    .line 31
    invoke-static {p1}, Lorg/codehaus/groovy/runtime/typehandling/BigIntegerMath;->toBigInteger(Ljava/lang/Number;)Ljava/math/BigInteger;

    move-result-object p1

    invoke-virtual {p1}, Ljava/math/BigInteger;->abs()Ljava/math/BigInteger;

    move-result-object p1

    return-object p1
.end method

.method public addImpl(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;
    .locals 0

    .line 35
    invoke-static {p1}, Lorg/codehaus/groovy/runtime/typehandling/BigIntegerMath;->toBigInteger(Ljava/lang/Number;)Ljava/math/BigInteger;

    move-result-object p1

    invoke-static {p2}, Lorg/codehaus/groovy/runtime/typehandling/BigIntegerMath;->toBigInteger(Ljava/lang/Number;)Ljava/math/BigInteger;

    move-result-object p2

    invoke-virtual {p1, p2}, Ljava/math/BigInteger;->add(Ljava/math/BigInteger;)Ljava/math/BigInteger;

    move-result-object p1

    return-object p1
.end method

.method protected andImpl(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;
    .locals 0

    .line 78
    invoke-static {p1}, Lorg/codehaus/groovy/runtime/typehandling/BigIntegerMath;->toBigInteger(Ljava/lang/Number;)Ljava/math/BigInteger;

    move-result-object p1

    invoke-static {p2}, Lorg/codehaus/groovy/runtime/typehandling/BigIntegerMath;->toBigInteger(Ljava/lang/Number;)Ljava/math/BigInteger;

    move-result-object p2

    invoke-virtual {p1, p2}, Ljava/math/BigInteger;->and(Ljava/math/BigInteger;)Ljava/math/BigInteger;

    move-result-object p1

    return-object p1
.end method

.method protected bitwiseNegateImpl(Ljava/lang/Number;)Ljava/lang/Number;
    .locals 0

    .line 70
    invoke-static {p1}, Lorg/codehaus/groovy/runtime/typehandling/BigIntegerMath;->toBigInteger(Ljava/lang/Number;)Ljava/math/BigInteger;

    move-result-object p1

    invoke-virtual {p1}, Ljava/math/BigInteger;->not()Ljava/math/BigInteger;

    move-result-object p1

    return-object p1
.end method

.method public compareToImpl(Ljava/lang/Number;Ljava/lang/Number;)I
    .locals 0

    .line 50
    invoke-static {p1}, Lorg/codehaus/groovy/runtime/typehandling/BigIntegerMath;->toBigInteger(Ljava/lang/Number;)Ljava/math/BigInteger;

    move-result-object p1

    invoke-static {p2}, Lorg/codehaus/groovy/runtime/typehandling/BigIntegerMath;->toBigInteger(Ljava/lang/Number;)Ljava/math/BigInteger;

    move-result-object p2

    invoke-virtual {p1, p2}, Ljava/math/BigInteger;->compareTo(Ljava/math/BigInteger;)I

    move-result p1

    return p1
.end method

.method public divideImpl(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;
    .locals 1

    .line 46
    sget-object v0, Lorg/codehaus/groovy/runtime/typehandling/BigDecimalMath;->INSTANCE:Lorg/codehaus/groovy/runtime/typehandling/BigDecimalMath;

    invoke-virtual {v0, p1, p2}, Lorg/codehaus/groovy/runtime/typehandling/BigDecimalMath;->divideImpl(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;

    move-result-object p1

    return-object p1
.end method

.method protected intdivImpl(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;
    .locals 0

    .line 54
    invoke-static {p1}, Lorg/codehaus/groovy/runtime/typehandling/BigIntegerMath;->toBigInteger(Ljava/lang/Number;)Ljava/math/BigInteger;

    move-result-object p1

    invoke-static {p2}, Lorg/codehaus/groovy/runtime/typehandling/BigIntegerMath;->toBigInteger(Ljava/lang/Number;)Ljava/math/BigInteger;

    move-result-object p2

    invoke-virtual {p1, p2}, Ljava/math/BigInteger;->divide(Ljava/math/BigInteger;)Ljava/math/BigInteger;

    move-result-object p1

    return-object p1
.end method

.method protected leftShiftImpl(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;
    .locals 0

    .line 86
    invoke-static {p1}, Lorg/codehaus/groovy/runtime/typehandling/BigIntegerMath;->toBigInteger(Ljava/lang/Number;)Ljava/math/BigInteger;

    move-result-object p1

    invoke-virtual {p2}, Ljava/lang/Number;->intValue()I

    move-result p2

    invoke-virtual {p1, p2}, Ljava/math/BigInteger;->shiftLeft(I)Ljava/math/BigInteger;

    move-result-object p1

    return-object p1
.end method

.method protected modImpl(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;
    .locals 0

    .line 58
    invoke-static {p1}, Lorg/codehaus/groovy/runtime/typehandling/BigIntegerMath;->toBigInteger(Ljava/lang/Number;)Ljava/math/BigInteger;

    move-result-object p1

    invoke-static {p2}, Lorg/codehaus/groovy/runtime/typehandling/BigIntegerMath;->toBigInteger(Ljava/lang/Number;)Ljava/math/BigInteger;

    move-result-object p2

    invoke-virtual {p1, p2}, Ljava/math/BigInteger;->mod(Ljava/math/BigInteger;)Ljava/math/BigInteger;

    move-result-object p1

    return-object p1
.end method

.method public multiplyImpl(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;
    .locals 0

    .line 42
    invoke-static {p1}, Lorg/codehaus/groovy/runtime/typehandling/BigIntegerMath;->toBigInteger(Ljava/lang/Number;)Ljava/math/BigInteger;

    move-result-object p1

    invoke-static {p2}, Lorg/codehaus/groovy/runtime/typehandling/BigIntegerMath;->toBigInteger(Ljava/lang/Number;)Ljava/math/BigInteger;

    move-result-object p2

    invoke-virtual {p1, p2}, Ljava/math/BigInteger;->multiply(Ljava/math/BigInteger;)Ljava/math/BigInteger;

    move-result-object p1

    return-object p1
.end method

.method protected orImpl(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;
    .locals 0

    .line 74
    invoke-static {p1}, Lorg/codehaus/groovy/runtime/typehandling/BigIntegerMath;->toBigInteger(Ljava/lang/Number;)Ljava/math/BigInteger;

    move-result-object p1

    invoke-static {p2}, Lorg/codehaus/groovy/runtime/typehandling/BigIntegerMath;->toBigInteger(Ljava/lang/Number;)Ljava/math/BigInteger;

    move-result-object p2

    invoke-virtual {p1, p2}, Ljava/math/BigInteger;->or(Ljava/math/BigInteger;)Ljava/math/BigInteger;

    move-result-object p1

    return-object p1
.end method

.method protected rightShiftImpl(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;
    .locals 0

    .line 90
    invoke-static {p1}, Lorg/codehaus/groovy/runtime/typehandling/BigIntegerMath;->toBigInteger(Ljava/lang/Number;)Ljava/math/BigInteger;

    move-result-object p1

    invoke-virtual {p2}, Ljava/lang/Number;->intValue()I

    move-result p2

    invoke-virtual {p1, p2}, Ljava/math/BigInteger;->shiftRight(I)Ljava/math/BigInteger;

    move-result-object p1

    return-object p1
.end method

.method public subtractImpl(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;
    .locals 0

    .line 38
    invoke-static {p1}, Lorg/codehaus/groovy/runtime/typehandling/BigIntegerMath;->toBigInteger(Ljava/lang/Number;)Ljava/math/BigInteger;

    move-result-object p1

    invoke-static {p2}, Lorg/codehaus/groovy/runtime/typehandling/BigIntegerMath;->toBigInteger(Ljava/lang/Number;)Ljava/math/BigInteger;

    move-result-object p2

    invoke-virtual {p1, p2}, Ljava/math/BigInteger;->subtract(Ljava/math/BigInteger;)Ljava/math/BigInteger;

    move-result-object p1

    return-object p1
.end method

.method protected unaryMinusImpl(Ljava/lang/Number;)Ljava/lang/Number;
    .locals 0

    .line 62
    invoke-static {p1}, Lorg/codehaus/groovy/runtime/typehandling/BigIntegerMath;->toBigInteger(Ljava/lang/Number;)Ljava/math/BigInteger;

    move-result-object p1

    invoke-virtual {p1}, Ljava/math/BigInteger;->negate()Ljava/math/BigInteger;

    move-result-object p1

    return-object p1
.end method

.method protected unaryPlusImpl(Ljava/lang/Number;)Ljava/lang/Number;
    .locals 0

    .line 66
    invoke-static {p1}, Lorg/codehaus/groovy/runtime/typehandling/BigIntegerMath;->toBigInteger(Ljava/lang/Number;)Ljava/math/BigInteger;

    move-result-object p1

    return-object p1
.end method

.method protected xorImpl(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;
    .locals 0

    .line 82
    invoke-static {p1}, Lorg/codehaus/groovy/runtime/typehandling/BigIntegerMath;->toBigInteger(Ljava/lang/Number;)Ljava/math/BigInteger;

    move-result-object p1

    invoke-static {p2}, Lorg/codehaus/groovy/runtime/typehandling/BigIntegerMath;->toBigInteger(Ljava/lang/Number;)Ljava/math/BigInteger;

    move-result-object p2

    invoke-virtual {p1, p2}, Ljava/math/BigInteger;->xor(Ljava/math/BigInteger;)Ljava/math/BigInteger;

    move-result-object p1

    return-object p1
.end method
