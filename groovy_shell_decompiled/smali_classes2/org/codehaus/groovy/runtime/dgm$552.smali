.class public Lorg/codehaus/groovy/runtime/dgm$552;
.super Lorg/codehaus/groovy/reflection/GeneratedMetaMethod;


# direct methods
.method public constructor <init>(Ljava/lang/String;Lorg/codehaus/groovy/reflection/CachedClass;Ljava/lang/Class;[Ljava/lang/Class;)V
    .locals 0

    invoke-direct {p0, p1, p2, p3, p4}, Lorg/codehaus/groovy/reflection/GeneratedMetaMethod;-><init>(Ljava/lang/String;Lorg/codehaus/groovy/reflection/CachedClass;Ljava/lang/Class;[Ljava/lang/Class;)V

    return-void
.end method


# virtual methods
.method public final doMethodInvoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    invoke-virtual {p0, p2}, Lorg/codehaus/groovy/runtime/dgm$552;->coerceArgumentsToClasses([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object p2

    check-cast p1, <PERSON>ja<PERSON>/lang/Boolean;

    const/4 v0, 0x0

    aget-object p2, p2, v0

    check-cast p2, Ljava/lang/Boolean;

    invoke-static {p1, p2}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->or(Ljava/lang/Boolean;Ljava/lang/Boolean;)Ljava/lang/Boolean;

    move-result-object p1

    return-object p1
.end method

.method public invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    check-cast p1, Ljava/lang/Boolean;

    const/4 v0, 0x0

    aget-object p2, p2, v0

    check-cast p2, Ljava/lang/Boolean;

    invoke-static {p1, p2}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->or(Ljava/lang/Boolean;Ljava/lang/Boolean;)Ljava/lang/Boolean;

    move-result-object p1

    return-object p1
.end method
