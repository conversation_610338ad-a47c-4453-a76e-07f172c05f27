.class public final synthetic Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter$$ExternalSyntheticLambda0;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/security/PrivilegedAction;


# instance fields
.field public final synthetic f$0:Ljava/lang/ClassLoader;

.field public final synthetic f$1:[Ljava/lang/Class;


# direct methods
.method public synthetic constructor <init>(Ljava/lang/ClassLoader;[Ljava/lang/Class;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter$$ExternalSyntheticLambda0;->f$0:Ljava/lang/ClassLoader;

    iput-object p2, p0, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter$$ExternalSyntheticLambda0;->f$1:[Ljava/lang/Class;

    return-void
.end method


# virtual methods
.method public final run()Ljava/lang/Object;
    .locals 2

    iget-object v0, p0, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter$$ExternalSyntheticLambda0;->f$0:Ljava/lang/ClassLoader;

    iget-object v1, p0, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter$$ExternalSyntheticLambda0;->f$1:[Ljava/lang/Class;

    invoke-static {v0, v1}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->lambda$createInnerLoader$0(Ljava/lang/ClassLoader;[Ljava/lang/Class;)Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter$InnerLoader;

    move-result-object v0

    return-object v0
.end method
