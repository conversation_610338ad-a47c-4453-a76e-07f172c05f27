.class public final synthetic Lorg/codehaus/groovy/runtime/ProcessGroovyMethods$$ExternalSyntheticLambda2;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic f$0:Ljava/lang/Process;

.field public final synthetic f$1:Ljava/lang/Process;


# direct methods
.method public synthetic constructor <init>(Ljava/lang/Process;Ljava/lang/Process;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/codehaus/groovy/runtime/ProcessGroovyMethods$$ExternalSyntheticLambda2;->f$0:Ljava/lang/Process;

    iput-object p2, p0, Lorg/codehaus/groovy/runtime/ProcessGroovyMethods$$ExternalSyntheticLambda2;->f$1:Ljava/lang/Process;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 2

    iget-object v0, p0, Lorg/codehaus/groovy/runtime/ProcessGroovyMethods$$ExternalSyntheticLambda2;->f$0:Ljava/lang/Process;

    iget-object v1, p0, Lorg/codehaus/groovy/runtime/ProcessGroovyMethods$$ExternalSyntheticLambda2;->f$1:Ljava/lang/Process;

    invoke-static {v0, v1}, Lorg/codehaus/groovy/runtime/ProcessGroovyMethods;->lambda$pipeTo$2(Ljava/lang/Process;Ljava/lang/Process;)V

    return-void
.end method
