.class public abstract Lorg/codehaus/groovy/runtime/m12n/SimpleExtensionModule;
.super Lorg/codehaus/groovy/runtime/m12n/ExtensionModule;
.source "SimpleExtensionModule.java"


# static fields
.field private static final LOG:Ljava/util/logging/Logger;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 88
    const-class v0, Lorg/codehaus/groovy/runtime/m12n/SimpleExtensionModule;

    invoke-virtual {v0}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Ljava/util/logging/Logger;->getLogger(Ljava/lang/String;)Ljava/util/logging/Logger;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/runtime/m12n/SimpleExtensionModule;->LOG:Ljava/util/logging/Logger;

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;Ljava/lang/String;)V
    .locals 0

    .line 91
    invoke-direct {p0, p1, p2}, Lorg/codehaus/groovy/runtime/m12n/ExtensionModule;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method private static createMetaMethods(Ljava/lang/Class;Ljava/util/List;Z)V
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Class;",
            "Ljava/util/List<",
            "Lgroovy/lang/MetaMethod;",
            ">;Z)V"
        }
    .end annotation

    .line 118
    invoke-static {p0}, Lorg/codehaus/groovy/reflection/ReflectionCache;->getCachedClass(Ljava/lang/Class;)Lorg/codehaus/groovy/reflection/CachedClass;

    move-result-object p0

    .line 119
    invoke-virtual {p0}, Lorg/codehaus/groovy/reflection/CachedClass;->getMethods()[Lorg/codehaus/groovy/reflection/CachedMethod;

    move-result-object p0

    .line 120
    array-length v0, p0

    const/4 v1, 0x0

    :goto_0
    if-ge v1, v0, :cond_2

    aget-object v2, p0, v1

    .line 121
    invoke-virtual {v2}, Lorg/codehaus/groovy/reflection/CachedMethod;->isStatic()Z

    move-result v3

    if-eqz v3, :cond_1

    invoke-virtual {v2}, Lorg/codehaus/groovy/reflection/CachedMethod;->isPublic()Z

    move-result v3

    if-eqz v3, :cond_1

    invoke-virtual {v2}, Lorg/codehaus/groovy/reflection/CachedMethod;->getParamsCount()I

    move-result v3

    if-lez v3, :cond_1

    if-eqz p2, :cond_0

    .line 123
    new-instance v3, Lorg/codehaus/groovy/runtime/metaclass/NewStaticMetaMethod;

    invoke-direct {v3, v2}, Lorg/codehaus/groovy/runtime/metaclass/NewStaticMetaMethod;-><init>(Lorg/codehaus/groovy/reflection/CachedMethod;)V

    goto :goto_1

    :cond_0
    new-instance v3, Lorg/codehaus/groovy/runtime/metaclass/NewInstanceMetaMethod;

    invoke-direct {v3, v2}, Lorg/codehaus/groovy/runtime/metaclass/NewInstanceMetaMethod;-><init>(Lorg/codehaus/groovy/reflection/CachedMethod;)V

    :goto_1
    invoke-interface {p1, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :cond_1
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_2
    return-void
.end method


# virtual methods
.method public abstract getInstanceMethodsExtensionClasses()Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Ljava/lang/Class;",
            ">;"
        }
    .end annotation
.end method

.method public getMetaMethods()Ljava/util/List;
    .locals 11
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lgroovy/lang/MetaMethod;",
            ">;"
        }
    .end annotation

    .line 97
    new-instance v0, Ljava/util/LinkedList;

    invoke-direct {v0}, Ljava/util/LinkedList;-><init>()V

    .line 98
    invoke-virtual {p0}, Lorg/codehaus/groovy/runtime/m12n/SimpleExtensionModule;->getInstanceMethodsExtensionClasses()Ljava/util/List;

    move-result-object v1

    .line 99
    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    const-string v3, "]. Maybe this module is not supported by your JVM version."

    const-string v4, "] due to ["

    const-string v5, "] - Unable to load extension class ["

    const-string v6, "Module ["

    if-eqz v2, :cond_0

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/lang/Class;

    const/4 v7, 0x0

    .line 101
    :try_start_0
    invoke-static {v2, v0, v7}, Lorg/codehaus/groovy/runtime/m12n/SimpleExtensionModule;->createMetaMethods(Ljava/lang/Class;Ljava/util/List;Z)V
    :try_end_0
    .catch Ljava/lang/LinkageError; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception v7

    .line 103
    sget-object v8, Lorg/codehaus/groovy/runtime/m12n/SimpleExtensionModule;->LOG:Ljava/util/logging/Logger;

    new-instance v9, Ljava/lang/StringBuilder;

    invoke-direct {v9}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v9, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v6

    invoke-virtual {p0}, Lorg/codehaus/groovy/runtime/m12n/SimpleExtensionModule;->getName()Ljava/lang/String;

    move-result-object v9

    invoke-virtual {v6, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v6

    invoke-virtual {v6, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    invoke-virtual {v5, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v7}, Ljava/lang/LinkageError;->getMessage()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v2, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v8, v2}, Ljava/util/logging/Logger;->warning(Ljava/lang/String;)V

    goto :goto_0

    .line 106
    :cond_0
    invoke-virtual {p0}, Lorg/codehaus/groovy/runtime/m12n/SimpleExtensionModule;->getStaticMethodsExtensionClasses()Ljava/util/List;

    move-result-object v1

    .line 107
    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_1
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_1

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/lang/Class;

    const/4 v7, 0x1

    .line 109
    :try_start_1
    invoke-static {v2, v0, v7}, Lorg/codehaus/groovy/runtime/m12n/SimpleExtensionModule;->createMetaMethods(Ljava/lang/Class;Ljava/util/List;Z)V
    :try_end_1
    .catch Ljava/lang/LinkageError; {:try_start_1 .. :try_end_1} :catch_1

    goto :goto_1

    :catch_1
    move-exception v7

    .line 111
    sget-object v8, Lorg/codehaus/groovy/runtime/m12n/SimpleExtensionModule;->LOG:Ljava/util/logging/Logger;

    new-instance v9, Ljava/lang/StringBuilder;

    invoke-direct {v9}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v9, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v9

    invoke-virtual {p0}, Lorg/codehaus/groovy/runtime/m12n/SimpleExtensionModule;->getName()Ljava/lang/String;

    move-result-object v10

    invoke-virtual {v9, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v9

    invoke-virtual {v9, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v9

    invoke-virtual {v9, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v7}, Ljava/lang/LinkageError;->getMessage()Ljava/lang/String;

    move-result-object v7

    invoke-virtual {v2, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v8, v2}, Ljava/util/logging/Logger;->warning(Ljava/lang/String;)V

    goto :goto_1

    :cond_1
    return-object v0
.end method

.method public abstract getStaticMethodsExtensionClasses()Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Ljava/lang/Class;",
            ">;"
        }
    .end annotation
.end method
