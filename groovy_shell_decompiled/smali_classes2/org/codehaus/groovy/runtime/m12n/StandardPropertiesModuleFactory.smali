.class public Lorg/codehaus/groovy/runtime/m12n/StandardPropertiesModuleFactory;
.super Lorg/codehaus/groovy/runtime/m12n/PropertiesModuleFactory;
.source "StandardPropertiesModuleFactory.java"


# static fields
.field public static final MODULE_FACTORY_KEY:Ljava/lang/String; = "moduleFactory"


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 32
    invoke-direct {p0}, Lorg/codehaus/groovy/runtime/m12n/PropertiesModuleFactory;-><init>()V

    return-void
.end method


# virtual methods
.method public newModule(Ljava/util/Properties;Ljava/lang/ClassLoader;)Lorg/codehaus/groovy/runtime/m12n/ExtensionModule;
    .locals 5

    const-string v0, "]"

    const-string v1, "moduleFactory"

    .line 38
    invoke-virtual {p1, v1}, Ljava/util/Properties;->getProperty(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    if-eqz v1, :cond_0

    .line 41
    :try_start_0
    invoke-virtual {p2, v1}, Ljava/lang/ClassLoader;->loadClass(Ljava/lang/String;)Ljava/lang/Class;

    move-result-object v2

    const/4 v3, 0x0

    new-array v4, v3, [Ljava/lang/Class;

    .line 42
    invoke-virtual {v2, v4}, Ljava/lang/Class;->getDeclaredConstructor([Ljava/lang/Class;)Ljava/lang/reflect/Constructor;

    move-result-object v2

    new-array v3, v3, [Ljava/lang/Object;

    invoke-virtual {v2, v3}, Ljava/lang/reflect/Constructor;->newInstance([Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lorg/codehaus/groovy/runtime/m12n/PropertiesModuleFactory;

    .line 43
    invoke-virtual {v2, p1, p2}, Lorg/codehaus/groovy/runtime/m12n/PropertiesModuleFactory;->newModule(Ljava/util/Properties;Ljava/lang/ClassLoader;)Lorg/codehaus/groovy/runtime/m12n/ExtensionModule;

    move-result-object p1
    :try_end_0
    .catch Ljava/lang/ClassNotFoundException; {:try_start_0 .. :try_end_0} :catch_4
    .catch Ljava/lang/NoSuchMethodException; {:try_start_0 .. :try_end_0} :catch_3
    .catch Ljava/lang/InstantiationException; {:try_start_0 .. :try_end_0} :catch_2
    .catch Ljava/lang/IllegalAccessException; {:try_start_0 .. :try_end_0} :catch_1
    .catch Ljava/lang/reflect/InvocationTargetException; {:try_start_0 .. :try_end_0} :catch_0

    return-object p1

    :catch_0
    move-exception p1

    goto :goto_0

    :catch_1
    move-exception p1

    goto :goto_0

    :catch_2
    move-exception p1

    .line 47
    :goto_0
    new-instance p2, Lgroovy/lang/GroovyRuntimeException;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "Unable to instantiate module factory ["

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-direct {p2, v0, p1}, Lgroovy/lang/GroovyRuntimeException;-><init>(Ljava/lang/String;Ljava/lang/Throwable;)V

    throw p2

    :catch_3
    move-exception p1

    goto :goto_1

    :catch_4
    move-exception p1

    .line 45
    :goto_1
    new-instance p2, Lgroovy/lang/GroovyRuntimeException;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "Unable to load module factory ["

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-direct {p2, v0, p1}, Lgroovy/lang/GroovyRuntimeException;-><init>(Ljava/lang/String;Ljava/lang/Throwable;)V

    throw p2

    .line 50
    :cond_0
    invoke-static {p1, p2}, Lorg/codehaus/groovy/runtime/m12n/MetaInfExtensionModule;->newModule(Ljava/util/Properties;Ljava/lang/ClassLoader;)Lorg/codehaus/groovy/runtime/m12n/MetaInfExtensionModule;

    move-result-object p1

    return-object p1
.end method
