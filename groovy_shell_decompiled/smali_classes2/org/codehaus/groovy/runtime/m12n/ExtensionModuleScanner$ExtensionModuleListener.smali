.class public interface abstract Lorg/codehaus/groovy/runtime/m12n/ExtensionModuleScanner$ExtensionModuleListener;
.super Ljava/lang/Object;
.source "ExtensionModuleScanner.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/codehaus/groovy/runtime/m12n/ExtensionModuleScanner;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "ExtensionModuleListener"
.end annotation


# virtual methods
.method public abstract onModule(Lorg/codehaus/groovy/runtime/m12n/ExtensionModule;)V
.end method
