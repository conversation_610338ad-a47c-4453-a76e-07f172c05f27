.class public Lorg/codehaus/groovy/runtime/m12n/MetaInfExtensionModule;
.super Lorg/codehaus/groovy/runtime/m12n/SimpleExtensionModule;
.source "MetaInfExtensionModule.java"


# static fields
.field private static final LOG:Ljava/util/logging/Logger;

.field public static final MODULE_INSTANCE_CLASSES_KEY:Ljava/lang/String; = "extensionClasses"

.field public static final MODULE_STATIC_CLASSES_KEY:Ljava/lang/String; = "staticExtensionClasses"


# instance fields
.field private final instanceExtensionClasses:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ljava/lang/Class;",
            ">;"
        }
    .end annotation
.end field

.field private final staticExtensionClasses:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ljava/lang/Class;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 36
    const-class v0, Lorg/codehaus/groovy/runtime/m12n/MetaInfExtensionModule;

    invoke-virtual {v0}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Ljava/util/logging/Logger;->getLogger(Ljava/lang/String;)Ljava/util/logging/Logger;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/runtime/m12n/MetaInfExtensionModule;->LOG:Ljava/util/logging/Logger;

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;Ljava/lang/String;Ljava/util/List;Ljava/util/List;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Ljava/lang/Class;",
            ">;",
            "Ljava/util/List<",
            "Ljava/lang/Class;",
            ">;)V"
        }
    .end annotation

    .line 55
    invoke-direct {p0, p1, p2}, Lorg/codehaus/groovy/runtime/m12n/SimpleExtensionModule;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    .line 56
    iput-object p3, p0, Lorg/codehaus/groovy/runtime/m12n/MetaInfExtensionModule;->instanceExtensionClasses:Ljava/util/List;

    .line 57
    iput-object p4, p0, Lorg/codehaus/groovy/runtime/m12n/MetaInfExtensionModule;->staticExtensionClasses:Ljava/util/List;

    return-void
.end method

.method private static loadExtensionClass(Ljava/lang/ClassLoader;[Ljava/lang/String;Ljava/util/List;Ljava/util/List;)V
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/ClassLoader;",
            "[",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Ljava/lang/Class;",
            ">;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;)V"
        }
    .end annotation

    .line 83
    array-length v0, p1

    const/4 v1, 0x0

    :goto_0
    if-ge v1, v0, :cond_1

    aget-object v2, p1, v1

    .line 85
    :try_start_0
    invoke-virtual {v2}, Ljava/lang/String;->trim()Ljava/lang/String;

    move-result-object v2

    .line 86
    invoke-virtual {v2}, Ljava/lang/String;->length()I

    move-result v3

    if-lez v3, :cond_0

    .line 87
    invoke-virtual {p0, v2}, Ljava/lang/ClassLoader;->loadClass(Ljava/lang/String;)Ljava/lang/Class;

    move-result-object v3

    invoke-interface {p2, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z
    :try_end_0
    .catch Ljava/lang/ClassNotFoundException; {:try_start_0 .. :try_end_0} :catch_0
    .catch Ljava/lang/NoClassDefFoundError; {:try_start_0 .. :try_end_0} :catch_0
    .catch Ljava/lang/UnsupportedClassVersionError; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_1

    .line 90
    :catch_0
    invoke-interface {p3, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :cond_0
    :goto_1
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_1
    return-void
.end method

.method public static newModule(Ljava/util/Properties;Ljava/lang/ClassLoader;)Lorg/codehaus/groovy/runtime/m12n/MetaInfExtensionModule;
    .locals 7

    const-string v0, "moduleName"

    .line 61
    invoke-virtual {p0, v0}, Ljava/util/Properties;->getProperty(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    if-eqz v0, :cond_2

    const-string v1, "moduleVersion"

    .line 64
    invoke-virtual {p0, v1}, Ljava/util/Properties;->getProperty(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    if-eqz v1, :cond_1

    const-string v2, "extensionClasses"

    const-string v3, ""

    .line 67
    invoke-virtual {p0, v2, v3}, Ljava/util/Properties;->getProperty(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/String;->trim()Ljava/lang/String;

    move-result-object v2

    const-string v4, "[,; ]"

    invoke-virtual {v2, v4}, Ljava/lang/String;->split(Ljava/lang/String;)[Ljava/lang/String;

    move-result-object v2

    const-string v5, "staticExtensionClasses"

    .line 68
    invoke-virtual {p0, v5, v3}, Ljava/util/Properties;->getProperty(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/String;->trim()Ljava/lang/String;

    move-result-object p0

    invoke-virtual {p0, v4}, Ljava/lang/String;->split(Ljava/lang/String;)[Ljava/lang/String;

    move-result-object p0

    .line 69
    new-instance v3, Ljava/util/ArrayList;

    array-length v4, v2

    invoke-direct {v3, v4}, Ljava/util/ArrayList;-><init>(I)V

    .line 70
    new-instance v4, Ljava/util/ArrayList;

    array-length v5, p0

    invoke-direct {v4, v5}, Ljava/util/ArrayList;-><init>(I)V

    .line 71
    new-instance v5, Ljava/util/LinkedList;

    invoke-direct {v5}, Ljava/util/LinkedList;-><init>()V

    .line 72
    invoke-static {p1, v2, v3, v5}, Lorg/codehaus/groovy/runtime/m12n/MetaInfExtensionModule;->loadExtensionClass(Ljava/lang/ClassLoader;[Ljava/lang/String;Ljava/util/List;Ljava/util/List;)V

    .line 73
    invoke-static {p1, p0, v4, v5}, Lorg/codehaus/groovy/runtime/m12n/MetaInfExtensionModule;->loadExtensionClass(Ljava/lang/ClassLoader;[Ljava/lang/String;Ljava/util/List;Ljava/util/List;)V

    .line 74
    invoke-interface {v5}, Ljava/util/List;->isEmpty()Z

    move-result p0

    if-nez p0, :cond_0

    .line 75
    invoke-interface {v5}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p0

    :goto_0
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    move-result p1

    if-eqz p1, :cond_0

    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/String;

    .line 76
    sget-object v2, Lorg/codehaus/groovy/runtime/m12n/MetaInfExtensionModule;->LOG:Ljava/util/logging/Logger;

    new-instance v5, Ljava/lang/StringBuilder;

    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    const-string v6, "Module ["

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    invoke-virtual {v5, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    const-string v6, "] - Unable to load extension class ["

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    invoke-virtual {v5, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v5, "]"

    invoke-virtual {p1, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v2, p1}, Ljava/util/logging/Logger;->warning(Ljava/lang/String;)V

    goto :goto_0

    .line 79
    :cond_0
    new-instance p0, Lorg/codehaus/groovy/runtime/m12n/MetaInfExtensionModule;

    invoke-direct {p0, v0, v1, v3, v4}, Lorg/codehaus/groovy/runtime/m12n/MetaInfExtensionModule;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/util/List;Ljava/util/List;)V

    return-object p0

    .line 66
    :cond_1
    new-instance p0, Lgroovy/lang/GroovyRuntimeException;

    const-string p1, "Module file hasn\'t set the module version using key [moduleVersion]"

    invoke-direct {p0, p1}, Lgroovy/lang/GroovyRuntimeException;-><init>(Ljava/lang/String;)V

    throw p0

    .line 63
    :cond_2
    new-instance p0, Lgroovy/lang/GroovyRuntimeException;

    const-string p1, "Module file hasn\'t set the module name using key [moduleName]"

    invoke-direct {p0, p1}, Lgroovy/lang/GroovyRuntimeException;-><init>(Ljava/lang/String;)V

    throw p0
.end method


# virtual methods
.method public getInstanceMethodsExtensionClasses()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Ljava/lang/Class;",
            ">;"
        }
    .end annotation

    .line 46
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/m12n/MetaInfExtensionModule;->instanceExtensionClasses:Ljava/util/List;

    return-object v0
.end method

.method public getStaticMethodsExtensionClasses()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Ljava/lang/Class;",
            ">;"
        }
    .end annotation

    .line 51
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/m12n/MetaInfExtensionModule;->staticExtensionClasses:Ljava/util/List;

    return-object v0
.end method
