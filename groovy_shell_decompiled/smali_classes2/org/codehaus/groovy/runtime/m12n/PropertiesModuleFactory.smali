.class public abstract Lorg/codehaus/groovy/runtime/m12n/PropertiesModuleFactory;
.super Ljava/lang/Object;
.source "PropertiesModuleFactory.java"


# static fields
.field public static final MODULE_NAME_KEY:Ljava/lang/String; = "moduleName"

.field public static final MODULE_VERSION_KEY:Ljava/lang/String; = "moduleVersion"


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 29
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public abstract newModule(Ljava/util/Properties;Ljava/lang/ClassLoader;)Lorg/codehaus/groovy/runtime/m12n/ExtensionModule;
.end method
