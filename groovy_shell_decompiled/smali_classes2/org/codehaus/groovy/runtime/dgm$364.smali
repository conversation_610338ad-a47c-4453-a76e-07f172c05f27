.class public Lorg/codehaus/groovy/runtime/dgm$364;
.super Lorg/codehaus/groovy/reflection/GeneratedMetaMethod;


# direct methods
.method public constructor <init>(Ljava/lang/String;Lorg/codehaus/groovy/reflection/CachedClass;Ljava/lang/Class;[Ljava/lang/Class;)V
    .locals 0

    invoke-direct {p0, p1, p2, p3, p4}, Lorg/codehaus/groovy/reflection/GeneratedMetaMethod;-><init>(Ljava/lang/String;Lorg/codehaus/groovy/reflection/CachedClass;Ljava/lang/Class;[Ljava/lang/Class;)V

    return-void
.end method


# virtual methods
.method public final doMethodInvoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    invoke-virtual {p0, p2}, Lorg/codehaus/groovy/runtime/dgm$364;->coerceArgumentsToClasses([Ljava/lang/Object;)[Ljava/lang/Object;

    check-cast p1, [I

    invoke-static {p1}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->getIndices([I)Lgroovy/lang/IntRange;

    move-result-object p1

    return-object p1
.end method

.method public invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    check-cast p1, [I

    invoke-static {p1}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->getIndices([I)Lgroovy/lang/IntRange;

    move-result-object p1

    return-object p1
.end method
