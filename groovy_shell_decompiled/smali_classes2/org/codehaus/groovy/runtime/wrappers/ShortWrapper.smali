.class public Lorg/codehaus/groovy/runtime/wrappers/ShortWrapper;
.super Lorg/codehaus/groovy/runtime/wrappers/PojoWrapper;
.source "ShortWrapper.java"


# direct methods
.method public constructor <init>(S)V
    .locals 1

    .line 23
    invoke-static {p1}, Ljava/lang/Short;->valueOf(S)Ljava/lang/Short;

    move-result-object p1

    sget-object v0, Ljava/lang/Short;->TYPE:Ljava/lang/Class;

    invoke-direct {p0, p1, v0}, Lorg/codehaus/groovy/runtime/wrappers/PojoWrapper;-><init>(Ljava/lang/Object;Ljava/lang/Class;)V

    return-void
.end method
