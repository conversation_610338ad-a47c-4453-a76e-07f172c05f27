.class public Lorg/codehaus/groovy/runtime/NumberAwareComparator;
.super Ljava/lang/Object;
.source "NumberAwareComparator.java"

# interfaces
.implements Ljava/util/Comparator;
.implements Ljava/io/Serializable;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;",
        "Ljava/util/Comparator<",
        "TT;>;",
        "Ljava/io/Serializable;"
    }
.end annotation


# static fields
.field private static final serialVersionUID:J = 0x7d252786dda0368cL


# instance fields
.field private ignoreZeroSign:Z


# direct methods
.method public constructor <init>()V
    .locals 1

    const/4 v0, 0x0

    .line 37
    invoke-direct {p0, v0}, Lorg/codehaus/groovy/runtime/NumberAwareComparator;-><init>(Z)V

    return-void
.end method

.method public constructor <init>(Z)V
    .locals 0

    .line 43
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 44
    iput-boolean p1, p0, Lorg/codehaus/groovy/runtime/NumberAwareComparator;->ignoreZeroSign:Z

    return-void
.end method


# virtual methods
.method public compare(Ljava/lang/Object;Ljava/lang/Object;)I
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TT;TT;)I"
        }
    .end annotation

    const/4 v0, 0x0

    .line 49
    :try_start_0
    iget-boolean v1, p0, Lorg/codehaus/groovy/runtime/NumberAwareComparator;->ignoreZeroSign:Z

    if-eqz v1, :cond_1

    .line 50
    instance-of v1, p1, Ljava/lang/Float;

    if-eqz v1, :cond_0

    move-object v1, p1

    check-cast v1, Ljava/lang/Float;

    invoke-virtual {v1}, Ljava/lang/Float;->floatValue()F

    move-result v1

    const/4 v2, 0x0

    cmpl-float v1, v2, v1

    if-nez v1, :cond_0

    instance-of v1, p2, Ljava/lang/Float;

    if-eqz v1, :cond_0

    move-object v1, p2

    check-cast v1, Ljava/lang/Float;

    invoke-virtual {v1}, Ljava/lang/Float;->floatValue()F

    move-result v1

    cmpl-float v1, v2, v1

    if-nez v1, :cond_0

    return v0

    .line 53
    :cond_0
    instance-of v1, p1, Ljava/lang/Double;

    if-eqz v1, :cond_1

    move-object v1, p1

    check-cast v1, Ljava/lang/Double;

    invoke-virtual {v1}, Ljava/lang/Double;->doubleValue()D

    move-result-wide v1

    const-wide/16 v3, 0x0

    cmpl-double v1, v3, v1

    if-nez v1, :cond_1

    instance-of v1, p2, Ljava/lang/Double;

    if-eqz v1, :cond_1

    move-object v1, p2

    check-cast v1, Ljava/lang/Double;

    invoke-virtual {v1}, Ljava/lang/Double;->doubleValue()D

    move-result-wide v1

    cmpl-double v1, v3, v1

    if-nez v1, :cond_1

    return v0

    .line 57
    :cond_1
    invoke-static {p1, p2}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->compareTo(Ljava/lang/Object;Ljava/lang/Object;)I

    move-result p1
    :try_end_0
    .catch Ljava/lang/ClassCastException; {:try_start_0 .. :try_end_0} :catch_0
    .catch Ljava/lang/IllegalArgumentException; {:try_start_0 .. :try_end_0} :catch_0
    .catch Lgroovy/lang/GroovyRuntimeException; {:try_start_0 .. :try_end_0} :catch_0

    return p1

    .line 70
    :catch_0
    invoke-virtual {p1}, Ljava/lang/Object;->hashCode()I

    move-result v1

    .line 71
    invoke-virtual {p2}, Ljava/lang/Object;->hashCode()I

    move-result v2

    if-ne v1, v2, :cond_2

    .line 72
    invoke-virtual {p1, p2}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_2

    return v0

    :cond_2
    if-le v1, v2, :cond_3

    const/4 p1, 0x1

    return p1

    :cond_3
    const/4 p1, -0x1

    return p1
.end method
