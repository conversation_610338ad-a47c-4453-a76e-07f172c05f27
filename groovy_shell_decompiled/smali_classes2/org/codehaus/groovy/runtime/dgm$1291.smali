.class public Lorg/codehaus/groovy/runtime/dgm$1291;
.super Lorg/codehaus/groovy/reflection/GeneratedMetaMethod;


# direct methods
.method public constructor <init>(Ljava/lang/String;Lorg/codehaus/groovy/reflection/CachedClass;Ljava/lang/Class;[Ljava/lang/Class;)V
    .locals 0

    invoke-direct {p0, p1, p2, p3, p4}, Lorg/codehaus/groovy/reflection/GeneratedMetaMethod;-><init>(Ljava/lang/String;Lorg/codehaus/groovy/reflection/CachedClass;Ljava/lang/Class;[Ljava/lang/Class;)V

    return-void
.end method


# virtual methods
.method public final doMethodInvoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    invoke-virtual {p0, p2}, Lorg/codehaus/groovy/runtime/dgm$1291;->coerceArgumentsToClasses([Ljava/lang/Object;)[Lja<PERSON>/lang/Object;

    move-result-object p2

    check-cast p1, Ljava/lang/String;

    const/4 v0, 0x0

    aget-object v0, p2, v0

    check-cast v0, Ljava/lang/CharSequence;

    const/4 v1, 0x1

    aget-object p2, p2, v1

    check-cast p2, Ljava/lang/CharSequence;

    invoke-static {p1, v0, p2}, Lorg/codehaus/groovy/runtime/StringGroovyMethods;->takeBetween(Ljava/lang/String;Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    check-cast p1, Ljava/lang/String;

    const/4 v0, 0x0

    aget-object v0, p2, v0

    check-cast v0, Ljava/lang/CharSequence;

    const/4 v1, 0x1

    aget-object p2, p2, v1

    check-cast p2, Ljava/lang/CharSequence;

    invoke-static {p1, v0, p2}, Lorg/codehaus/groovy/runtime/StringGroovyMethods;->takeBetween(Ljava/lang/String;Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method
