.class final Lorg/codehaus/groovy/runtime/StringGroovyMethods$LineIterable;
.super Ljava/lang/Object;
.source "StringGroovyMethods.java"

# interfaces
.implements Ljava/lang/Iterable;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/codehaus/groovy/runtime/StringGroovyMethods;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1a
    name = "LineIterable"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ljava/lang/Iterable<",
        "Ljava/lang/String;",
        ">;"
    }
.end annotation


# instance fields
.field private final delegate:Ljava/lang/CharSequence;


# direct methods
.method public constructor <init>(Ljava/lang/CharSequence;)V
    .locals 1

    .line 600
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 603
    instance-of v0, p1, Lgroovy/lang/GString;

    if-eqz v0, :cond_0

    invoke-interface {p1}, Ljava/lang/CharSequence;->toString()Ljava/lang/String;

    move-result-object p1

    :cond_0
    iput-object p1, p0, Lorg/codehaus/groovy/runtime/StringGroovyMethods$LineIterable;->delegate:Ljava/lang/CharSequence;

    return-void
.end method


# virtual methods
.method public iterator()Ljava/util/Iterator;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Iterator<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .line 608
    new-instance v0, Lorg/codehaus/groovy/util/CharSequenceReader;

    iget-object v1, p0, Lorg/codehaus/groovy/runtime/StringGroovyMethods$LineIterable;->delegate:Ljava/lang/CharSequence;

    invoke-direct {v0, v1}, Lorg/codehaus/groovy/util/CharSequenceReader;-><init>(Ljava/lang/CharSequence;)V

    invoke-static {v0}, Lorg/codehaus/groovy/runtime/IOGroovyMethods;->iterator(Ljava/io/Reader;)Ljava/util/Iterator;

    move-result-object v0

    return-object v0
.end method
