.class Lorg/codehaus/groovy/runtime/ProcessGroovyMethods$TextDumper;
.super Ljava/lang/Object;
.source "ProcessGroovyMethods.java"

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/codehaus/groovy/runtime/ProcessGroovyMethods;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0xa
    name = "TextDumper"
.end annotation


# instance fields
.field final app:Ljava/lang/Appendable;

.field final in:Ljava/io/InputStream;


# direct methods
.method public constructor <init>(Ljava/io/InputStream;Ljava/lang/Appendable;)V
    .locals 0

    .line 483
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 484
    iput-object p1, p0, Lorg/codehaus/groovy/runtime/ProcessGroovyMethods$TextDumper;->in:Ljava/io/InputStream;

    .line 485
    iput-object p2, p0, Lorg/codehaus/groovy/runtime/ProcessGroovyMethods$TextDumper;->app:Ljava/lang/Appendable;

    return-void
.end method


# virtual methods
.method public run()V
    .locals 3

    .line 489
    new-instance v0, Ljava/io/InputStreamReader;

    iget-object v1, p0, Lorg/codehaus/groovy/runtime/ProcessGroovyMethods$TextDumper;->in:Ljava/io/InputStream;

    invoke-direct {v0, v1}, Ljava/io/InputStreamReader;-><init>(Ljava/io/InputStream;)V

    .line 490
    new-instance v1, Ljava/io/BufferedReader;

    invoke-direct {v1, v0}, Ljava/io/BufferedReader;-><init>(Ljava/io/Reader;)V

    .line 493
    :cond_0
    :goto_0
    :try_start_0
    invoke-virtual {v1}, Ljava/io/BufferedReader;->readLine()Ljava/lang/String;

    move-result-object v0

    if-eqz v0, :cond_1

    .line 494
    iget-object v2, p0, Lorg/codehaus/groovy/runtime/ProcessGroovyMethods$TextDumper;->app:Ljava/lang/Appendable;

    if-eqz v2, :cond_0

    .line 495
    invoke-interface {v2, v0}, Ljava/lang/Appendable;->append(Ljava/lang/CharSequence;)Ljava/lang/Appendable;

    .line 496
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/ProcessGroovyMethods$TextDumper;->app:Ljava/lang/Appendable;

    const-string v2, "\n"

    invoke-interface {v0, v2}, Ljava/lang/Appendable;->append(Ljava/lang/CharSequence;)Ljava/lang/Appendable;
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :cond_1
    return-void

    :catch_0
    move-exception v0

    .line 500
    new-instance v1, Lgroovy/lang/GroovyRuntimeException;

    const-string v2, "exception while reading process stream"

    invoke-direct {v1, v2, v0}, Lgroovy/lang/GroovyRuntimeException;-><init>(Ljava/lang/String;Ljava/lang/Throwable;)V

    throw v1
.end method
