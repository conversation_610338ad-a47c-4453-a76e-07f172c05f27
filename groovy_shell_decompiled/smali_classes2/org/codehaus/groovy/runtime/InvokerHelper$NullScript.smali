.class Lorg/codehaus/groovy/runtime/InvokerHelper$NullScript;
.super Lgroovy/lang/Script;
.source "InvokerHelper.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/codehaus/groovy/runtime/InvokerHelper;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x8
    name = "NullScript"
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 445
    new-instance v0, Lgroovy/lang/Binding;

    invoke-direct {v0}, Lgroovy/lang/Binding;-><init>()V

    invoke-direct {p0, v0}, Lorg/codehaus/groovy/runtime/InvokerHelper$NullScript;-><init>(Lgroovy/lang/Binding;)V

    return-void
.end method

.method public constructor <init>(Lgroovy/lang/Binding;)V
    .locals 0

    .line 444
    invoke-direct {p0, p1}, Lgroovy/lang/Script;-><init>(Lgroovy/lang/Binding;)V

    return-void
.end method


# virtual methods
.method public run()Ljava/lang/Object;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method
