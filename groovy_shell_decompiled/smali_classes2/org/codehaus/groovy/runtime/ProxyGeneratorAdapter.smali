.class public Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;
.super Lgroovyjarjarasm/asm/ClassVisitor;
.source "ProxyGeneratorAdapter.java"

# interfaces
.implements Lgroovyjarjarasm/asm/Opcodes;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter$InnerLoader;,
        Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter$ReturnValueWrappingClosure;
    }
.end annotation


# static fields
.field static final synthetic $assertionsDisabled:Z = false

.field private static final CLOSURES_MAP_FIELD:Ljava/lang/String; = "$closures$delegate$map"

.field private static final DELEGATE_OBJECT_FIELD:Ljava/lang/String; = "$delegate"

.field private static final EMPTY_ARGS:[Ljava/lang/Object;

.field private static final EMPTY_DELEGATECLOSURE_MAP:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation
.end field

.field private static GROOVYOBJECT_METHODS:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ljava/lang/reflect/Method;",
            ">;"
        }
    .end annotation
.end field

.field private static final GROOVYOBJECT_METHOD_NAMES:Ljava/util/Set;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Set<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field private static OBJECT_METHODS:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ljava/lang/reflect/Method;",
            ">;"
        }
    .end annotation
.end field

.field private static final proxyCounter:Ljava/util/concurrent/atomic/AtomicLong;


# instance fields
.field private final cachedClass:Ljava/lang/Class;

.field private final cachedNoArgConstructor:Ljava/lang/reflect/Constructor;

.field private final delegateClass:Ljava/lang/Class;

.field private final delegatedClosures:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation
.end field

.field private final emptyBody:Z

.field private final generateDelegateField:Z

.field private final hasWildcard:Z

.field private final implClasses:Ljava/util/Set;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Set<",
            "Ljava/lang/Class;",
            ">;"
        }
    .end annotation
.end field

.field private final innerLoader:Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter$InnerLoader;

.field private final objectDelegateMethods:Ljava/util/Set;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Set<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field private final proxyName:Ljava/lang/String;

.field private final superClass:Ljava/lang/Class;

.field private final visitedMethods:Ljava/util/Set;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Set<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public static synthetic $r8$lambda$5SleMaZh-8lqdcvcYVMXUDVdy4s(Ljava/lang/reflect/Method;)I
    .locals 0

    invoke-virtual {p0}, Ljava/lang/reflect/Method;->getModifiers()I

    move-result p0

    return p0
.end method

.method public static synthetic $r8$lambda$GLDyV_TBXMNwEIZuOb4wIrbcp18(I)Z
    .locals 0

    invoke-static {p0}, Ljava/lang/reflect/Modifier;->isAbstract(I)Z

    move-result p0

    return p0
.end method

.method static constructor <clinit>()V
    .locals 5

    .line 87
    invoke-static {}, Ljava/util/Collections;->emptyMap()Ljava/util/Map;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->EMPTY_DELEGATECLOSURE_MAP:Ljava/util/Map;

    const/4 v0, 0x0

    new-array v1, v0, [Ljava/lang/Object;

    .line 88
    sput-object v1, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->EMPTY_ARGS:[Ljava/lang/Object;

    .line 92
    new-instance v1, Ljava/util/concurrent/atomic/AtomicLong;

    invoke-direct {v1}, Ljava/util/concurrent/atomic/AtomicLong;-><init>()V

    sput-object v1, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->proxyCounter:Ljava/util/concurrent/atomic/AtomicLong;

    .line 94
    const-class v1, Ljava/lang/Object;

    new-instance v2, Ljava/util/ArrayList;

    invoke-direct {v2}, Ljava/util/ArrayList;-><init>()V

    invoke-static {v1, v2}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->getInheritedMethods(Ljava/lang/Class;Ljava/util/List;)Ljava/util/List;

    move-result-object v1

    sput-object v1, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->OBJECT_METHODS:Ljava/util/List;

    .line 95
    const-class v1, Lgroovy/lang/GroovyObject;

    new-instance v2, Ljava/util/ArrayList;

    invoke-direct {v2}, Ljava/util/ArrayList;-><init>()V

    invoke-static {v1, v2}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->getInheritedMethods(Ljava/lang/Class;Ljava/util/List;)Ljava/util/List;

    move-result-object v1

    sput-object v1, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->GROOVYOBJECT_METHODS:Ljava/util/List;

    .line 98
    new-instance v1, Ljava/util/HashSet;

    invoke-direct {v1}, Ljava/util/HashSet;-><init>()V

    .line 99
    const-class v2, Lgroovy/lang/GroovyObject;

    invoke-virtual {v2}, Ljava/lang/Class;->getMethods()[Ljava/lang/reflect/Method;

    move-result-object v2

    array-length v3, v2

    :goto_0
    if-ge v0, v3, :cond_0

    aget-object v4, v2, v0

    .line 100
    invoke-virtual {v4}, Ljava/lang/reflect/Method;->getName()Ljava/lang/String;

    move-result-object v4

    invoke-interface {v1, v4}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    .line 102
    :cond_0
    invoke-static {v1}, Ljava/util/Collections;->unmodifiableSet(Ljava/util/Set;)Ljava/util/Set;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->GROOVYOBJECT_METHOD_NAMES:Ljava/util/Set;

    return-void
.end method

.method public constructor <init>(Ljava/util/Map;Ljava/lang/Class;[Ljava/lang/Class;Ljava/lang/ClassLoader;ZLjava/lang/Class;)V
    .locals 13
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/Object;",
            "Ljava/lang/Object;",
            ">;",
            "Ljava/lang/Class<",
            "*>;[",
            "Ljava/lang/Class;",
            "Ljava/lang/ClassLoader;",
            "Z",
            "Ljava/lang/Class<",
            "*>;)V"
        }
    .end annotation

    move-object v7, p0

    move-object v0, p2

    move-object/from16 v1, p3

    move-object/from16 v2, p4

    move-object/from16 v8, p6

    .line 141
    new-instance v3, Lgroovyjarjarasm/asm/ClassWriter;

    const/4 v9, 0x2

    invoke-direct {v3, v9}, Lgroovyjarjarasm/asm/ClassWriter;-><init>(I)V

    const/high16 v4, 0x90000

    invoke-direct {p0, v4, v3}, Lgroovyjarjarasm/asm/ClassVisitor;-><init>(ILgroovyjarjarasm/asm/ClassVisitor;)V

    if-eqz v2, :cond_0

    .line 142
    invoke-static {v2, v1}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->createInnerLoader(Ljava/lang/ClassLoader;[Ljava/lang/Class;)Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter$InnerLoader;

    move-result-object v2

    goto :goto_0

    :cond_0
    invoke-direct {p0, p2, v1}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->findClassLoader(Ljava/lang/Class;[Ljava/lang/Class;)Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter$InnerLoader;

    move-result-object v2

    :goto_0
    iput-object v2, v7, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->innerLoader:Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter$InnerLoader;

    .line 143
    new-instance v2, Ljava/util/LinkedHashSet;

    invoke-direct {v2}, Ljava/util/LinkedHashSet;-><init>()V

    iput-object v2, v7, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->visitedMethods:Ljava/util/Set;

    .line 144
    invoke-interface {p1}, Ljava/util/Map;->isEmpty()Z

    move-result v2

    if-eqz v2, :cond_1

    sget-object v2, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->EMPTY_DELEGATECLOSURE_MAP:Ljava/util/Map;

    goto :goto_1

    :cond_1
    new-instance v2, Ljava/util/HashMap;

    invoke-direct {v2}, Ljava/util/HashMap;-><init>()V

    :goto_1
    iput-object v2, v7, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->delegatedClosures:Ljava/util/Map;

    .line 146
    invoke-interface {p1}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    move-result-object v2

    invoke-interface {v2}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v2

    const/4 v10, 0x0

    move v3, v10

    :goto_2
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v4

    const/4 v11, 0x1

    if-eqz v4, :cond_3

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Ljava/util/Map$Entry;

    .line 147
    invoke-interface {v4}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v4

    invoke-virtual {v4}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v4

    const-string v5, "*"

    .line 148
    invoke-virtual {v5, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v5

    if-eqz v5, :cond_2

    move v3, v11

    .line 151
    :cond_2
    iget-object v5, v7, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->delegatedClosures:Ljava/util/Map;

    sget-object v6, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    invoke-interface {v5, v4, v6}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_2

    .line 153
    :cond_3
    iput-boolean v3, v7, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->hasWildcard:Z

    .line 155
    invoke-direct {p0, p2, v1}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->adjustSuperClass(Ljava/lang/Class;[Ljava/lang/Class;)Ljava/lang/Class;

    move-result-object v2

    if-eqz v8, :cond_4

    move v3, v11

    goto :goto_3

    :cond_4
    move v3, v10

    .line 158
    :goto_3
    iput-boolean v3, v7, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->generateDelegateField:Z

    if-eqz v3, :cond_5

    .line 159
    invoke-static {v2, v8, v1}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->createDelegateMethodList(Ljava/lang/Class;Ljava/lang/Class;[Ljava/lang/Class;)Ljava/util/Set;

    move-result-object v4

    goto :goto_4

    :cond_5
    invoke-static {}, Ljava/util/Collections;->emptySet()Ljava/util/Set;

    move-result-object v4

    :goto_4
    iput-object v4, v7, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->objectDelegateMethods:Ljava/util/Set;

    .line 160
    iput-object v8, v7, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->delegateClass:Ljava/lang/Class;

    .line 165
    iput-object v2, v7, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->superClass:Ljava/lang/Class;

    .line 168
    new-instance v2, Ljava/util/LinkedHashSet;

    invoke-direct {v2}, Ljava/util/LinkedHashSet;-><init>()V

    iput-object v2, v7, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->implClasses:Ljava/util/Set;

    .line 169
    invoke-interface {v2, p2}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    if-eqz v3, :cond_7

    .line 171
    invoke-interface {v2, v8}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 172
    invoke-virtual/range {p6 .. p6}, Ljava/lang/Class;->getInterfaces()[Ljava/lang/Class;

    move-result-object v0

    array-length v2, v0

    move v3, v10

    :goto_5
    if-ge v3, v2, :cond_7

    aget-object v4, v0, v3

    .line 173
    invoke-static {v4}, Lorg/codehaus/groovy/reflection/ReflectionUtils;->isSealed(Ljava/lang/Class;)Z

    move-result v5

    if-nez v5, :cond_6

    iget-object v5, v7, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->implClasses:Ljava/util/Set;

    invoke-interface {v5, v4}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    :cond_6
    add-int/lit8 v3, v3, 0x1

    goto :goto_5

    :cond_7
    if-eqz v1, :cond_8

    .line 177
    iget-object v0, v7, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->implClasses:Ljava/util/Set;

    invoke-static {v0, v1}, Ljava/util/Collections;->addAll(Ljava/util/Collection;[Ljava/lang/Object;)Z

    .line 179
    :cond_8
    invoke-direct {p0}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->proxyName()Ljava/lang/String;

    move-result-object v12

    iput-object v12, v7, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->proxyName:Ljava/lang/String;

    move/from16 v0, p5

    .line 180
    iput-boolean v0, v7, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->emptyBody:Z

    .line 183
    sget-object v0, Lorg/codehaus/groovy/control/CompilerConfiguration;->JDK_TO_BYTECODE_VERSION_MAP:Ljava/util/Map;

    sget-object v1, Lorg/codehaus/groovy/control/CompilerConfiguration;->DEFAULT:Lorg/codehaus/groovy/control/CompilerConfiguration;

    invoke-virtual {v1}, Lorg/codehaus/groovy/control/CompilerConfiguration;->getTargetBytecode()Ljava/lang/String;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/Integer;

    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    move-result v1

    const/4 v2, 0x1

    const/4 v4, 0x0

    const/4 v5, 0x0

    const/4 v6, 0x0

    move-object v0, p0

    move-object v3, v12

    .line 184
    invoke-virtual/range {v0 .. v6}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->visit(IILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)V

    .line 185
    iget-object v0, v7, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->cv:Lgroovyjarjarasm/asm/ClassVisitor;

    check-cast v0, Lgroovyjarjarasm/asm/ClassWriter;

    invoke-virtual {v0}, Lgroovyjarjarasm/asm/ClassWriter;->toByteArray()[B

    move-result-object v0

    .line 186
    iget-object v1, v7, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->innerLoader:Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter$InnerLoader;

    const/16 v2, 0x2f

    const/16 v3, 0x2e

    invoke-virtual {v12, v2, v3}, Ljava/lang/String;->replace(CC)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2, v0}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter$InnerLoader;->defineClass(Ljava/lang/String;[B)Ljava/lang/Class;

    move-result-object v0

    iput-object v0, v7, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->cachedClass:Ljava/lang/Class;

    .line 188
    iget-boolean v1, v7, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->generateDelegateField:Z

    if-eqz v1, :cond_9

    new-array v1, v9, [Ljava/lang/Class;

    const-class v2, Ljava/util/Map;

    aput-object v2, v1, v10

    aput-object v8, v1, v11

    goto :goto_6

    :cond_9
    new-array v1, v11, [Ljava/lang/Class;

    const-class v2, Ljava/util/Map;

    aput-object v2, v1, v10

    .line 191
    :goto_6
    :try_start_0
    invoke-virtual {v0, v1}, Ljava/lang/Class;->getConstructor([Ljava/lang/Class;)Ljava/lang/reflect/Constructor;

    move-result-object v0
    :try_end_0
    .catch Ljava/lang/NoSuchMethodException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_7

    :catch_0
    const/4 v0, 0x0

    .line 195
    :goto_7
    iput-object v0, v7, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->cachedNoArgConstructor:Ljava/lang/reflect/Constructor;

    return-void
.end method

.method private addDelegateFields()V
    .locals 7

    const/16 v1, 0x12

    const-string v2, "$closures$delegate$map"

    const-string v3, "Ljava/util/Map;"

    const/4 v4, 0x0

    const/4 v5, 0x0

    move-object v0, p0

    .line 461
    invoke-virtual/range {v0 .. v5}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->visitField(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Object;)Lgroovyjarjarasm/asm/FieldVisitor;

    .line 462
    iget-boolean v0, p0, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->generateDelegateField:Z

    if-eqz v0, :cond_0

    const/16 v2, 0x12

    .line 463
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->delegateClass:Ljava/lang/Class;

    invoke-static {v0}, Lorg/codehaus/groovy/classgen/asm/BytecodeHelper;->getTypeDescription(Ljava/lang/Class;)Ljava/lang/String;

    move-result-object v4

    const/4 v5, 0x0

    const/4 v6, 0x0

    const-string v3, "$delegate"

    move-object v1, p0

    invoke-virtual/range {v1 .. v6}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->visitField(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Object;)Lgroovyjarjarasm/asm/FieldVisitor;

    :cond_0
    return-void
.end method

.method private adjustSuperClass(Ljava/lang/Class;[Ljava/lang/Class;)Ljava/lang/Class;
    .locals 13

    .line 199
    invoke-virtual {p1}, Ljava/lang/Class;->isInterface()Z

    move-result v0

    if-nez v0, :cond_0

    return-object p1

    :cond_0
    if-eqz p2, :cond_1

    .line 202
    array-length v0, p2

    if-nez v0, :cond_2

    :cond_1
    const/4 p2, 0x1

    new-array p2, p2, [Ljava/lang/Class;

    const/4 v0, 0x0

    aput-object p1, p2, v0

    .line 207
    :cond_2
    invoke-static {p2}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->collectTraits([Ljava/lang/Class;)Ljava/util/Set;

    move-result-object p2

    .line 208
    invoke-interface {p2}, Ljava/util/Set;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_4

    .line 209
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p1}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, "$TraitAdapter"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    .line 210
    new-instance v6, Lorg/codehaus/groovy/ast/ClassNode;

    const/16 v2, 0x401

    sget-object v3, Lorg/codehaus/groovy/ast/ClassHelper;->OBJECT_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    sget-object v0, Lorg/codehaus/groovy/ast/ClassNode;->EMPTY_ARRAY:[Lorg/codehaus/groovy/ast/ClassNode;

    invoke-interface {p2, v0}, Ljava/util/Set;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object p2

    move-object v4, p2

    check-cast v4, [Lorg/codehaus/groovy/ast/ClassNode;

    const/4 v5, 0x0

    move-object v0, v6

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/codehaus/groovy/ast/ClassNode;-><init>(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/MixinNode;)V

    .line 211
    new-instance p2, Lorg/codehaus/groovy/control/CompilationUnit;

    iget-object v0, p0, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->innerLoader:Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter$InnerLoader;

    invoke-direct {p2, v0}, Lorg/codehaus/groovy/control/CompilationUnit;-><init>(Lgroovy/lang/GroovyClassLoader;)V

    .line 212
    new-instance v10, Lorg/codehaus/groovy/control/CompilerConfiguration;

    invoke-direct {v10}, Lorg/codehaus/groovy/control/CompilerConfiguration;-><init>()V

    .line 213
    new-instance v0, Lorg/codehaus/groovy/control/SourceUnit;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v2, "wrapper"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v8

    iget-object v11, p0, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->innerLoader:Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter$InnerLoader;

    new-instance v12, Lorg/codehaus/groovy/control/ErrorCollector;

    invoke-direct {v12, v10}, Lorg/codehaus/groovy/control/ErrorCollector;-><init>(Lorg/codehaus/groovy/control/CompilerConfiguration;)V

    const-string v9, ""

    move-object v7, v0

    invoke-direct/range {v7 .. v12}, Lorg/codehaus/groovy/control/SourceUnit;-><init>(Ljava/lang/String;Ljava/lang/String;Lorg/codehaus/groovy/control/CompilerConfiguration;Lgroovy/lang/GroovyClassLoader;Lorg/codehaus/groovy/control/ErrorCollector;)V

    .line 214
    invoke-virtual {p2, v0}, Lorg/codehaus/groovy/control/CompilationUnit;->addSource(Lorg/codehaus/groovy/control/SourceUnit;)Lorg/codehaus/groovy/control/SourceUnit;

    const/4 v1, 0x3

    .line 215
    invoke-virtual {p2, v1}, Lorg/codehaus/groovy/control/CompilationUnit;->compile(I)V

    .line 216
    invoke-virtual {v0}, Lorg/codehaus/groovy/control/SourceUnit;->getAST()Lorg/codehaus/groovy/ast/ModuleNode;

    move-result-object v0

    invoke-virtual {v0, v6}, Lorg/codehaus/groovy/ast/ModuleNode;->addClass(Lorg/codehaus/groovy/ast/ClassNode;)V

    const/4 v0, 0x7

    .line 217
    invoke-virtual {p2, v0}, Lorg/codehaus/groovy/control/CompilationUnit;->compile(I)V

    .line 218
    invoke-virtual {p2}, Lorg/codehaus/groovy/control/CompilationUnit;->getClasses()Ljava/util/List;

    move-result-object p2

    .line 219
    invoke-interface {p2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p2

    :cond_3
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_4

    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/tools/GroovyClass;

    .line 220
    invoke-virtual {v0}, Lorg/codehaus/groovy/tools/GroovyClass;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v1, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_3

    .line 221
    iget-object p2, p0, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->innerLoader:Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter$InnerLoader;

    invoke-virtual {v0}, Lorg/codehaus/groovy/tools/GroovyClass;->getBytes()[B

    move-result-object v0

    invoke-virtual {p2, p1, v0}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter$InnerLoader;->defineClass(Ljava/lang/String;[B)Ljava/lang/Class;

    move-result-object p1

    return-object p1

    .line 226
    :cond_4
    const-class p1, Ljava/lang/Object;

    return-object p1
.end method

.method private boxPrimitiveType(Lgroovyjarjarasm/asm/MethodVisitor;ILgroovyjarjarasm/asm/Type;)V
    .locals 7

    .line 731
    invoke-static {p3}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->isPrimitive(Lgroovyjarjarasm/asm/Type;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 732
    invoke-static {p3}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->getLoadInsn(Lgroovyjarjarasm/asm/Type;)I

    move-result v0

    invoke-virtual {p1, v0, p2}, Lgroovyjarjarasm/asm/MethodVisitor;->visitIntInsn(II)V

    .line 733
    invoke-static {p3}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->getWrappedClassDescriptor(Lgroovyjarjarasm/asm/Type;)Ljava/lang/String;

    move-result-object v3

    const/16 v2, 0xb8

    .line 734
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "("

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p3}, Lgroovyjarjarasm/asm/Type;->getDescriptor()Ljava/lang/String;

    move-result-object p3

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string p3, ")L"

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string p3, ";"

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v5

    const/4 v6, 0x0

    const-string v4, "valueOf"

    move-object v1, p1

    invoke-virtual/range {v1 .. v6}, Lgroovyjarjarasm/asm/MethodVisitor;->visitMethodInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V

    goto :goto_0

    :cond_0
    const/16 p3, 0x19

    .line 736
    invoke-virtual {p1, p3, p2}, Lgroovyjarjarasm/asm/MethodVisitor;->visitVarInsn(II)V

    :goto_0
    return-void
.end method

.method private static collectTraits([Ljava/lang/Class;)Ljava/util/Set;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "([",
            "Ljava/lang/Class;",
            ")",
            "Ljava/util/Set<",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            ">;"
        }
    .end annotation

    .line 230
    new-instance v0, Ljava/util/LinkedHashSet;

    invoke-direct {v0}, Ljava/util/LinkedHashSet;-><init>()V

    .line 231
    array-length v1, p0

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v1, :cond_3

    aget-object v3, p0, v2

    .line 232
    invoke-static {v3}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v3

    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/ClassNode;->getAllInterfaces()Ljava/util/Set;

    move-result-object v3

    invoke-interface {v3}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v3

    :cond_0
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    move-result v4

    if-eqz v4, :cond_2

    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lorg/codehaus/groovy/ast/ClassNode;

    .line 233
    invoke-static {v4}, Lorg/codehaus/groovy/transform/trait/Traits;->isTrait(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v5

    if-eqz v5, :cond_0

    .line 234
    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/ClassNode;->getPlainNodeReference()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v5

    invoke-interface {v0, v5}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 236
    new-instance v5, Ljava/util/LinkedHashSet;

    invoke-direct {v5}, Ljava/util/LinkedHashSet;-><init>()V

    const/4 v6, 0x1

    .line 237
    invoke-static {v4, v5, v6, v6}, Lorg/codehaus/groovy/transform/trait/Traits;->collectSelfTypes(Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/LinkedHashSet;ZZ)Ljava/util/LinkedHashSet;

    .line 238
    invoke-virtual {v5}, Ljava/util/LinkedHashSet;->iterator()Ljava/util/Iterator;

    move-result-object v4

    :cond_1
    :goto_1
    invoke-interface {v4}, Ljava/util/Iterator;->hasNext()Z

    move-result v5

    if-eqz v5, :cond_0

    invoke-interface {v4}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lorg/codehaus/groovy/ast/ClassNode;

    .line 239
    invoke-static {v5}, Lorg/codehaus/groovy/transform/trait/Traits;->isTrait(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v6

    if-eqz v6, :cond_1

    .line 240
    invoke-virtual {v5}, Lorg/codehaus/groovy/ast/ClassNode;->getPlainNodeReference()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v5

    invoke-interface {v0, v5}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    goto :goto_1

    :cond_2
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_3
    return-object v0
.end method

.method private static containsEquivalentMethod(Ljava/util/Collection;Ljava/lang/reflect/Method;)Z
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Collection<",
            "Ljava/lang/reflect/Method;",
            ">;",
            "Ljava/lang/reflect/Method;",
            ")Z"
        }
    .end annotation

    .line 304
    invoke-interface {p0}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object p0

    :cond_0
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_1

    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/reflect/Method;

    .line 305
    invoke-virtual {p1}, Ljava/lang/reflect/Method;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0}, Ljava/lang/reflect/Method;->getName()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_0

    .line 306
    invoke-virtual {p1}, Ljava/lang/reflect/Method;->getReturnType()Ljava/lang/Class;

    move-result-object v1

    invoke-virtual {v0}, Ljava/lang/reflect/Method;->getReturnType()Ljava/lang/Class;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_0

    .line 307
    invoke-static {v0, p1}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->hasMatchingParameterTypes(Ljava/lang/reflect/Method;Ljava/lang/reflect/Method;)Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 p0, 0x1

    return p0

    :cond_1
    const/4 p0, 0x0

    return p0
.end method

.method private createConstructor(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lgroovyjarjarasm/asm/MethodVisitor;
    .locals 15

    move-object v6, p0

    .line 588
    invoke-static/range {p3 .. p3}, Lgroovyjarjarasm/asm/Type;->getArgumentTypes(Ljava/lang/String;)[Lgroovyjarjarasm/asm/Type;

    move-result-object v7

    .line 589
    new-instance v0, Ljava/lang/StringBuilder;

    const-string v1, "("

    invoke-direct {v0, v1}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    .line 590
    array-length v1, v7

    const/4 v8, 0x0

    move v2, v8

    :goto_0
    if-ge v2, v1, :cond_0

    aget-object v3, v7, v2

    .line 591
    invoke-virtual {v3}, Lgroovyjarjarasm/asm/Type;->getDescriptor()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_0
    const-string v1, "Ljava/util/Map;"

    .line 593
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 594
    iget-boolean v1, v6, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->generateDelegateField:Z

    if-eqz v1, :cond_1

    .line 595
    iget-object v1, v6, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->delegateClass:Ljava/lang/Class;

    invoke-static {v1}, Lorg/codehaus/groovy/classgen/asm/BytecodeHelper;->getTypeDescription(Ljava/lang/Class;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    :cond_1
    const-string v1, ")V"

    .line 597
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 598
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    move-object v0, p0

    move/from16 v1, p1

    move-object/from16 v2, p2

    move-object/from16 v4, p4

    move-object/from16 v5, p5

    invoke-super/range {v0 .. v5}, Lgroovyjarjarasm/asm/ClassVisitor;->visitMethod(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lgroovyjarjarasm/asm/MethodVisitor;

    move-result-object v0

    .line 599
    invoke-virtual {v0}, Lgroovyjarjarasm/asm/MethodVisitor;->visitCode()V

    .line 600
    invoke-direct {p0, v0, v7}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->initializeDelegateClosure(Lgroovyjarjarasm/asm/MethodVisitor;[Lgroovyjarjarasm/asm/Type;)V

    .line 601
    iget-boolean v1, v6, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->generateDelegateField:Z

    if-eqz v1, :cond_2

    .line 602
    invoke-direct {p0, v0, v7}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->initializeDelegateObject(Lgroovyjarjarasm/asm/MethodVisitor;[Lgroovyjarjarasm/asm/Type;)V

    :cond_2
    const/16 v1, 0x19

    .line 604
    invoke-virtual {v0, v1, v8}, Lgroovyjarjarasm/asm/MethodVisitor;->visitVarInsn(II)V

    .line 606
    array-length v2, v7

    const/4 v3, 0x1

    move v4, v3

    :goto_1
    if-ge v8, v2, :cond_4

    aget-object v5, v7, v8

    .line 607
    invoke-static {v5}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->isPrimitive(Lgroovyjarjarasm/asm/Type;)Z

    move-result v9

    if-eqz v9, :cond_3

    .line 608
    invoke-static {v5}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->getLoadInsn(Lgroovyjarjarasm/asm/Type;)I

    move-result v9

    invoke-virtual {v0, v9, v4}, Lgroovyjarjarasm/asm/MethodVisitor;->visitIntInsn(II)V

    goto :goto_2

    .line 610
    :cond_3
    invoke-virtual {v0, v1, v4}, Lgroovyjarjarasm/asm/MethodVisitor;->visitVarInsn(II)V

    .line 612
    :goto_2
    invoke-static {v5}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->registerLen(Lgroovyjarjarasm/asm/Type;)I

    move-result v5

    add-int/2addr v4, v5

    add-int/lit8 v8, v8, 0x1

    goto :goto_1

    :cond_4
    const/16 v10, 0xb7

    .line 614
    iget-object v1, v6, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->superClass:Ljava/lang/Class;

    invoke-static {v1}, Lorg/codehaus/groovy/classgen/asm/BytecodeHelper;->getClassInternalName(Ljava/lang/Class;)Ljava/lang/String;

    move-result-object v11

    const/4 v14, 0x0

    const-string v12, "<init>"

    move-object v9, v0

    move-object/from16 v13, p3

    invoke-virtual/range {v9 .. v14}, Lgroovyjarjarasm/asm/MethodVisitor;->visitMethodInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V

    const/16 v1, 0xb1

    .line 615
    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    add-int/2addr v4, v3

    .line 616
    iget-boolean v1, v6, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->generateDelegateField:Z

    add-int/2addr v4, v1

    .line 617
    invoke-virtual {v0, v4, v4}, Lgroovyjarjarasm/asm/MethodVisitor;->visitMaxs(II)V

    .line 618
    invoke-virtual {v0}, Lgroovyjarjarasm/asm/MethodVisitor;->visitEnd()V

    const/4 v0, 0x0

    return-object v0
.end method

.method private static createDelegateMethodList(Ljava/lang/Class;Ljava/lang/Class;[Ljava/lang/Class;)Ljava/util/Set;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Class;",
            "Ljava/lang/Class;",
            "[",
            "Ljava/lang/Class;",
            ")",
            "Ljava/util/Set<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .line 260
    new-instance v0, Ljava/util/HashSet;

    invoke-direct {v0}, Ljava/util/HashSet;-><init>()V

    .line 261
    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    .line 262
    new-instance v2, Ljava/util/ArrayList;

    invoke-direct {v2}, Ljava/util/ArrayList;-><init>()V

    .line 263
    invoke-virtual {p0}, Ljava/lang/Class;->getDeclaredMethods()[Ljava/lang/reflect/Method;

    move-result-object p0

    invoke-static {v2, p0}, Ljava/util/Collections;->addAll(Ljava/util/Collection;[Ljava/lang/Object;)Z

    if-eqz p2, :cond_2

    .line 265
    array-length p0, p2

    const/4 v3, 0x0

    :goto_0
    if-ge v3, p0, :cond_0

    aget-object v4, p2, v3

    .line 266
    invoke-static {v4, v1}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->getInheritedMethods(Ljava/lang/Class;Ljava/util/List;)Ljava/util/List;

    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    .line 268
    :cond_0
    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p0

    :cond_1
    :goto_1
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    move-result p2

    if-eqz p2, :cond_2

    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Ljava/lang/reflect/Method;

    .line 269
    invoke-static {v2, p2}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->containsEquivalentMethod(Ljava/util/Collection;Ljava/lang/reflect/Method;)Z

    move-result v3

    if-nez v3, :cond_1

    .line 270
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p2}, Ljava/lang/reflect/Method;->getName()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-static {p2}, Lgroovyjarjarasm/asm/Type;->getMethodDescriptor(Ljava/lang/reflect/Method;)Ljava/lang/String;

    move-result-object p2

    invoke-virtual {v3, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {v0, p2}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    goto :goto_1

    .line 274
    :cond_2
    new-instance p0, Ljava/util/ArrayList;

    invoke-direct {p0}, Ljava/util/ArrayList;-><init>()V

    invoke-static {p1, p0}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->getInheritedMethods(Ljava/lang/Class;Ljava/util/List;)Ljava/util/List;

    move-result-object p0

    .line 275
    invoke-interface {p0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p0

    :cond_3
    :goto_2
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    move-result p1

    if-eqz p1, :cond_5

    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/reflect/Method;

    .line 276
    invoke-virtual {p1}, Ljava/lang/reflect/Method;->getName()Ljava/lang/String;

    move-result-object p2

    const/16 v2, 0x24

    invoke-virtual {p2, v2}, Ljava/lang/String;->indexOf(I)I

    move-result p2

    const/4 v2, -0x1

    if-eq p2, v2, :cond_4

    goto :goto_2

    .line 278
    :cond_4
    invoke-static {v1, p1}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->containsEquivalentMethod(Ljava/util/Collection;Ljava/lang/reflect/Method;)Z

    move-result p2

    if-nez p2, :cond_3

    sget-object p2, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->OBJECT_METHODS:Ljava/util/List;

    .line 279
    invoke-static {p2, p1}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->containsEquivalentMethod(Ljava/util/Collection;Ljava/lang/reflect/Method;)Z

    move-result p2

    if-nez p2, :cond_3

    sget-object p2, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->GROOVYOBJECT_METHODS:Ljava/util/List;

    .line 280
    invoke-static {p2, p1}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->containsEquivalentMethod(Ljava/util/Collection;Ljava/lang/reflect/Method;)Z

    move-result p2

    if-nez p2, :cond_3

    .line 281
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p1}, Ljava/lang/reflect/Method;->getName()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {p2, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-static {p1}, Lgroovyjarjarasm/asm/Type;->getMethodDescriptor(Ljava/lang/reflect/Method;)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-interface {v0, p1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    goto :goto_2

    :cond_5
    return-object v0
.end method

.method private createGetProxyTargetMethod(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lgroovyjarjarasm/asm/MethodVisitor;
    .locals 6

    const/16 v1, 0x11

    move-object v0, p0

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    move-object v5, p5

    .line 565
    invoke-super/range {v0 .. v5}, Lgroovyjarjarasm/asm/ClassVisitor;->visitMethod(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lgroovyjarjarasm/asm/MethodVisitor;

    move-result-object p1

    .line 566
    invoke-virtual {p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitCode()V

    const/16 p2, 0x19

    const/4 p3, 0x0

    .line 567
    invoke-virtual {p1, p2, p3}, Lgroovyjarjarasm/asm/MethodVisitor;->visitIntInsn(II)V

    .line 568
    iget-object p2, p0, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->proxyName:Ljava/lang/String;

    iget-object p3, p0, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->delegateClass:Ljava/lang/Class;

    invoke-static {p3}, Lorg/codehaus/groovy/classgen/asm/BytecodeHelper;->getTypeDescription(Ljava/lang/Class;)Ljava/lang/String;

    move-result-object p3

    const/16 p4, 0xb4

    const-string p5, "$delegate"

    invoke-virtual {p1, p4, p2, p5, p3}, Lgroovyjarjarasm/asm/MethodVisitor;->visitFieldInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    const/16 p2, 0xb0

    .line 569
    invoke-virtual {p1, p2}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    const/4 p2, 0x1

    .line 570
    invoke-virtual {p1, p2, p2}, Lgroovyjarjarasm/asm/MethodVisitor;->visitMaxs(II)V

    .line 571
    invoke-virtual {p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitEnd()V

    const/4 p1, 0x0

    return-object p1
.end method

.method private createGroovyObjectSupport()V
    .locals 16

    move-object/from16 v6, p0

    const/16 v1, 0x82

    const-string v2, "metaClass"

    const-string v3, "Lgroovy/lang/MetaClass;"

    const/4 v4, 0x0

    const/4 v5, 0x0

    move-object/from16 v0, p0

    .line 410
    invoke-virtual/range {v0 .. v5}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->visitField(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Object;)Lgroovyjarjarasm/asm/FieldVisitor;

    const/4 v1, 0x1

    const-string v2, "getMetaClass"

    const-string v3, "()Lgroovy/lang/MetaClass;"

    .line 415
    invoke-super/range {v0 .. v5}, Lgroovyjarjarasm/asm/ClassVisitor;->visitMethod(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lgroovyjarjarasm/asm/MethodVisitor;

    move-result-object v0

    .line 416
    invoke-virtual {v0}, Lgroovyjarjarasm/asm/MethodVisitor;->visitCode()V

    .line 417
    new-instance v1, Lgroovyjarjarasm/asm/Label;

    invoke-direct {v1}, Lgroovyjarjarasm/asm/Label;-><init>()V

    .line 418
    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitLabel(Lgroovyjarjarasm/asm/Label;)V

    const/16 v13, 0x19

    const/4 v14, 0x0

    .line 419
    invoke-virtual {v0, v13, v14}, Lgroovyjarjarasm/asm/MethodVisitor;->visitVarInsn(II)V

    .line 420
    iget-object v1, v6, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->proxyName:Ljava/lang/String;

    const/16 v2, 0xb4

    const-string v15, "metaClass"

    const-string v5, "Lgroovy/lang/MetaClass;"

    invoke-virtual {v0, v2, v1, v15, v5}, Lgroovyjarjarasm/asm/MethodVisitor;->visitFieldInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    .line 421
    new-instance v1, Lgroovyjarjarasm/asm/Label;

    invoke-direct {v1}, Lgroovyjarjarasm/asm/Label;-><init>()V

    const/16 v3, 0xc7

    .line 422
    invoke-virtual {v0, v3, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitJumpInsn(ILgroovyjarjarasm/asm/Label;)V

    .line 423
    new-instance v3, Lgroovyjarjarasm/asm/Label;

    invoke-direct {v3}, Lgroovyjarjarasm/asm/Label;-><init>()V

    .line 424
    invoke-virtual {v0, v3}, Lgroovyjarjarasm/asm/MethodVisitor;->visitLabel(Lgroovyjarjarasm/asm/Label;)V

    .line 425
    invoke-virtual {v0, v13, v14}, Lgroovyjarjarasm/asm/MethodVisitor;->visitVarInsn(II)V

    .line 426
    invoke-virtual {v0, v13, v14}, Lgroovyjarjarasm/asm/MethodVisitor;->visitVarInsn(II)V

    const/16 v8, 0xb6

    const-string v9, "java/lang/Object"

    const-string v10, "getClass"

    const-string v11, "()Ljava/lang/Class;"

    const/4 v12, 0x0

    move-object v7, v0

    .line 427
    invoke-virtual/range {v7 .. v12}, Lgroovyjarjarasm/asm/MethodVisitor;->visitMethodInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V

    const/16 v8, 0xb8

    const-string v9, "org/codehaus/groovy/runtime/InvokerHelper"

    const-string v10, "getMetaClass"

    const-string v11, "(Ljava/lang/Class;)Lgroovy/lang/MetaClass;"

    .line 428
    invoke-virtual/range {v7 .. v12}, Lgroovyjarjarasm/asm/MethodVisitor;->visitMethodInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V

    .line 429
    iget-object v3, v6, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->proxyName:Ljava/lang/String;

    const/16 v7, 0xb5

    invoke-virtual {v0, v7, v3, v15, v5}, Lgroovyjarjarasm/asm/MethodVisitor;->visitFieldInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    .line 430
    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitLabel(Lgroovyjarjarasm/asm/Label;)V

    .line 431
    invoke-virtual {v0, v13, v14}, Lgroovyjarjarasm/asm/MethodVisitor;->visitVarInsn(II)V

    .line 432
    iget-object v1, v6, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->proxyName:Ljava/lang/String;

    invoke-virtual {v0, v2, v1, v15, v5}, Lgroovyjarjarasm/asm/MethodVisitor;->visitFieldInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    const/16 v1, 0xb0

    .line 433
    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    const/4 v8, 0x2

    const/4 v9, 0x1

    .line 434
    invoke-virtual {v0, v8, v9}, Lgroovyjarjarasm/asm/MethodVisitor;->visitMaxs(II)V

    .line 435
    invoke-virtual {v0}, Lgroovyjarjarasm/asm/MethodVisitor;->visitEnd()V

    const/4 v1, 0x1

    const-string v2, "setMetaClass"

    const-string v3, "(Lgroovy/lang/MetaClass;)V"

    const/4 v10, 0x0

    move-object/from16 v0, p0

    move-object v11, v5

    move-object v5, v10

    .line 440
    invoke-super/range {v0 .. v5}, Lgroovyjarjarasm/asm/ClassVisitor;->visitMethod(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lgroovyjarjarasm/asm/MethodVisitor;

    move-result-object v0

    .line 441
    invoke-virtual {v0}, Lgroovyjarjarasm/asm/MethodVisitor;->visitCode()V

    .line 442
    new-instance v1, Lgroovyjarjarasm/asm/Label;

    invoke-direct {v1}, Lgroovyjarjarasm/asm/Label;-><init>()V

    .line 443
    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitLabel(Lgroovyjarjarasm/asm/Label;)V

    .line 444
    invoke-virtual {v0, v13, v14}, Lgroovyjarjarasm/asm/MethodVisitor;->visitVarInsn(II)V

    .line 445
    invoke-virtual {v0, v13, v9}, Lgroovyjarjarasm/asm/MethodVisitor;->visitVarInsn(II)V

    .line 446
    iget-object v1, v6, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->proxyName:Ljava/lang/String;

    invoke-virtual {v0, v7, v1, v15, v11}, Lgroovyjarjarasm/asm/MethodVisitor;->visitFieldInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    .line 447
    new-instance v1, Lgroovyjarjarasm/asm/Label;

    invoke-direct {v1}, Lgroovyjarjarasm/asm/Label;-><init>()V

    .line 448
    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitLabel(Lgroovyjarjarasm/asm/Label;)V

    const/16 v1, 0xb1

    .line 449
    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    .line 450
    new-instance v1, Lgroovyjarjarasm/asm/Label;

    invoke-direct {v1}, Lgroovyjarjarasm/asm/Label;-><init>()V

    .line 451
    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitLabel(Lgroovyjarjarasm/asm/Label;)V

    .line 452
    invoke-virtual {v0, v8, v8}, Lgroovyjarjarasm/asm/MethodVisitor;->visitMaxs(II)V

    .line 453
    invoke-virtual {v0}, Lgroovyjarjarasm/asm/MethodVisitor;->visitEnd()V

    return-void
.end method

.method private static createInnerLoader(Ljava/lang/ClassLoader;[Ljava/lang/Class;)Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter$InnerLoader;
    .locals 1

    .line 250
    new-instance v0, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter$$ExternalSyntheticLambda0;

    invoke-direct {v0, p0, p1}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter$$ExternalSyntheticLambda0;-><init>(Ljava/lang/ClassLoader;[Ljava/lang/Class;)V

    invoke-static {v0}, Ljava/security/AccessController;->doPrivileged(Ljava/security/PrivilegedAction;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter$InnerLoader;

    return-object p0
.end method

.method public static ensureClosure(Ljava/lang/Object;)Lgroovy/lang/Closure;
    .locals 1

    if-eqz p0, :cond_1

    .line 798
    instance-of v0, p0, Lgroovy/lang/Closure;

    if-eqz v0, :cond_0

    check-cast p0, Lgroovy/lang/Closure;

    return-object p0

    .line 799
    :cond_0
    new-instance v0, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter$ReturnValueWrappingClosure;

    invoke-direct {v0, p0}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter$ReturnValueWrappingClosure;-><init>(Ljava/lang/Object;)V

    return-object v0

    .line 797
    :cond_1
    new-instance p0, Ljava/lang/UnsupportedOperationException;

    invoke-direct {p0}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw p0
.end method

.method private findClassLoader(Ljava/lang/Class;[Ljava/lang/Class;)Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter$InnerLoader;
    .locals 0

    .line 254
    invoke-virtual {p1}, Ljava/lang/Class;->getClassLoader()Ljava/lang/ClassLoader;

    move-result-object p1

    if-nez p1, :cond_0

    .line 255
    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/Class;->getClassLoader()Ljava/lang/ClassLoader;

    move-result-object p1

    .line 256
    :cond_0
    invoke-static {p1, p2}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->createInnerLoader(Ljava/lang/ClassLoader;[Ljava/lang/Class;)Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter$InnerLoader;

    move-result-object p1

    return-object p1
.end method

.method private static getInheritedMethods(Ljava/lang/Class;Ljava/util/List;)Ljava/util/List;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Class;",
            "Ljava/util/List<",
            "Ljava/lang/reflect/Method;",
            ">;)",
            "Ljava/util/List<",
            "Ljava/lang/reflect/Method;",
            ">;"
        }
    .end annotation

    .line 288
    invoke-virtual {p0}, Ljava/lang/Class;->getMethods()[Ljava/lang/reflect/Method;

    move-result-object v0

    invoke-static {p1, v0}, Ljava/util/Collections;->addAll(Ljava/util/Collection;[Ljava/lang/Object;)Z

    :goto_0
    if-eqz p0, :cond_3

    .line 291
    invoke-virtual {p0}, Ljava/lang/Class;->getDeclaredMethods()[Ljava/lang/reflect/Method;

    move-result-object v0

    .line 292
    array-length v1, v0

    const/4 v2, 0x0

    :goto_1
    if-ge v2, v1, :cond_2

    aget-object v3, v0, v2

    .line 293
    invoke-virtual {v3}, Ljava/lang/reflect/Method;->getName()Ljava/lang/String;

    move-result-object v4

    const/16 v5, 0x24

    invoke-virtual {v4, v5}, Ljava/lang/String;->indexOf(I)I

    move-result v4

    const/4 v5, -0x1

    if-eq v4, v5, :cond_0

    goto :goto_2

    .line 295
    :cond_0
    invoke-virtual {v3}, Ljava/lang/reflect/Method;->getModifiers()I

    move-result v4

    invoke-static {v4}, Ljava/lang/reflect/Modifier;->isProtected(I)Z

    move-result v4

    if-eqz v4, :cond_1

    invoke-static {p1, v3}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->containsEquivalentMethod(Ljava/util/Collection;Ljava/lang/reflect/Method;)Z

    move-result v4

    if-nez v4, :cond_1

    .line 296
    invoke-interface {p1, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :cond_1
    :goto_2
    add-int/lit8 v2, v2, 0x1

    goto :goto_1

    .line 298
    :cond_2
    invoke-virtual {p0}, Ljava/lang/Class;->getSuperclass()Ljava/lang/Class;

    move-result-object p0

    goto :goto_0

    :cond_3
    return-object p1
.end method

.method private static getLoadInsn(Lgroovyjarjarasm/asm/Type;)I
    .locals 0

    .line 803
    invoke-static {p0}, Lorg/codehaus/groovy/classgen/asm/util/TypeUtil;->getLoadInsnByType(Lgroovyjarjarasm/asm/Type;)I

    move-result p0

    return p0
.end method

.method private static getReturnInsn(Lgroovyjarjarasm/asm/Type;)I
    .locals 0

    .line 807
    invoke-static {p0}, Lorg/codehaus/groovy/classgen/asm/util/TypeUtil;->getReturnInsnByType(Lgroovyjarjarasm/asm/Type;)I

    move-result p0

    return p0
.end method

.method private static getTypeArgsRegisterLength([Lgroovyjarjarasm/asm/Type;)I
    .locals 4

    .line 641
    array-length v0, p0

    const/4 v1, 0x0

    move v2, v1

    :goto_0
    if-ge v1, v0, :cond_0

    aget-object v3, p0, v1

    .line 642
    invoke-static {v3}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->registerLen(Lgroovyjarjarasm/asm/Type;)I

    move-result v3

    add-int/2addr v2, v3

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    return v2
.end method

.method private static getWrappedClassDescriptor(Lgroovyjarjarasm/asm/Type;)Ljava/lang/String;
    .locals 0

    .line 815
    invoke-static {p0}, Lorg/codehaus/groovy/classgen/asm/util/TypeUtil;->getWrappedClassDescriptor(Lgroovyjarjarasm/asm/Type;)Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method private static hasMatchingParameterTypes(Ljava/lang/reflect/Method;Ljava/lang/reflect/Method;)Z
    .locals 4

    .line 315
    invoke-virtual {p1}, Ljava/lang/reflect/Method;->getParameterTypes()[Ljava/lang/Class;

    move-result-object p1

    .line 316
    invoke-virtual {p0}, Ljava/lang/reflect/Method;->getParameterTypes()[Ljava/lang/Class;

    move-result-object p0

    .line 317
    array-length v0, p1

    array-length v1, p0

    const/4 v2, 0x0

    if-eq v0, v1, :cond_0

    return v2

    :cond_0
    move v0, v2

    .line 318
    :goto_0
    array-length v1, p0

    if-ge v0, v1, :cond_2

    .line 319
    aget-object v1, p1, v0

    aget-object v3, p0, v0

    invoke-virtual {v1, v3}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_1

    return v2

    :cond_1
    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_2
    const/4 p0, 0x1

    return p0
.end method

.method private initializeDelegateClosure(Lgroovyjarjarasm/asm/MethodVisitor;[Lgroovyjarjarasm/asm/Type;)V
    .locals 3

    .line 623
    invoke-static {p2}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->getTypeArgsRegisterLength([Lgroovyjarjarasm/asm/Type;)I

    move-result p2

    add-int/lit8 p2, p2, 0x1

    const/16 v0, 0x19

    const/4 v1, 0x0

    .line 625
    invoke-virtual {p1, v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitIntInsn(II)V

    .line 626
    invoke-virtual {p1, v0, p2}, Lgroovyjarjarasm/asm/MethodVisitor;->visitIntInsn(II)V

    .line 628
    iget-object p2, p0, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->proxyName:Ljava/lang/String;

    const/16 v0, 0xb5

    const-string v1, "$closures$delegate$map"

    const-string v2, "Ljava/util/Map;"

    invoke-virtual {p1, v0, p2, v1, v2}, Lgroovyjarjarasm/asm/MethodVisitor;->visitFieldInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method private initializeDelegateObject(Lgroovyjarjarasm/asm/MethodVisitor;[Lgroovyjarjarasm/asm/Type;)V
    .locals 3

    .line 632
    invoke-static {p2}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->getTypeArgsRegisterLength([Lgroovyjarjarasm/asm/Type;)I

    move-result p2

    add-int/lit8 p2, p2, 0x2

    const/16 v0, 0x19

    const/4 v1, 0x0

    .line 634
    invoke-virtual {p1, v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitIntInsn(II)V

    .line 635
    invoke-virtual {p1, v0, p2}, Lgroovyjarjarasm/asm/MethodVisitor;->visitIntInsn(II)V

    .line 636
    iget-object p2, p0, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->proxyName:Ljava/lang/String;

    iget-object v0, p0, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->delegateClass:Ljava/lang/Class;

    invoke-static {v0}, Lorg/codehaus/groovy/classgen/asm/BytecodeHelper;->getTypeDescription(Ljava/lang/Class;)Ljava/lang/String;

    move-result-object v0

    const/16 v1, 0xb5

    const-string v2, "$delegate"

    invoke-virtual {p1, v1, p2, v2, v0}, Lgroovyjarjarasm/asm/MethodVisitor;->visitFieldInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method private static isImplemented(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;)Z
    .locals 7

    .line 478
    invoke-virtual {p0}, Ljava/lang/Class;->getDeclaredMethods()[Ljava/lang/reflect/Method;

    move-result-object v0

    .line 479
    array-length v1, v0

    const/4 v2, 0x0

    move v3, v2

    :goto_0
    const/4 v4, 0x1

    if-ge v3, v1, :cond_1

    aget-object v5, v0, v3

    .line 480
    invoke-virtual {v5}, Ljava/lang/reflect/Method;->getName()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v6, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v6

    if-eqz v6, :cond_0

    .line 481
    invoke-static {v5}, Lgroovyjarjarasm/asm/Type;->getMethodDescriptor(Ljava/lang/reflect/Method;)Ljava/lang/String;

    move-result-object v6

    invoke-virtual {p2, v6}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v6

    if-eqz v6, :cond_0

    .line 482
    invoke-virtual {v5}, Ljava/lang/reflect/Method;->getModifiers()I

    move-result p0

    invoke-static {p0}, Ljava/lang/reflect/Modifier;->isAbstract(I)Z

    move-result p0

    xor-int/2addr p0, v4

    return p0

    :cond_0
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    .line 486
    :cond_1
    invoke-virtual {p0}, Ljava/lang/Class;->getSuperclass()Ljava/lang/Class;

    move-result-object p0

    if-eqz p0, :cond_2

    .line 487
    invoke-static {p0, p1, p2}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->isImplemented(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;)Z

    move-result p0

    if-eqz p0, :cond_2

    move v2, v4

    :cond_2
    return v2
.end method

.method private static isPrimitive(Lgroovyjarjarasm/asm/Type;)Z
    .locals 0

    .line 811
    invoke-static {p0}, Lorg/codehaus/groovy/classgen/asm/util/TypeUtil;->isPrimitiveType(Lgroovyjarjarasm/asm/Type;)Z

    move-result p0

    return p0
.end method

.method static synthetic lambda$createInnerLoader$0(Ljava/lang/ClassLoader;[Ljava/lang/Class;)Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter$InnerLoader;
    .locals 1

    .line 250
    new-instance v0, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter$InnerLoader;

    invoke-direct {v0, p0, p1}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter$InnerLoader;-><init>(Ljava/lang/ClassLoader;[Ljava/lang/Class;)V

    return-object v0
.end method

.method static synthetic lambda$visitMethod$1(Ljava/lang/String;Ljava/lang/reflect/Method;)Z
    .locals 0

    .line 506
    invoke-virtual {p1}, Ljava/lang/reflect/Method;->getName()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p1, p0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p0

    return p0
.end method

.method private proxyName()Ljava/lang/String;
    .locals 5

    .line 468
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->delegateClass:Ljava/lang/Class;

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->superClass:Ljava/lang/Class;

    :goto_0
    invoke-virtual {v0}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v0

    const-string v1, "["

    .line 469
    invoke-virtual {v0, v1}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v1

    const/4 v2, 0x1

    if-eqz v1, :cond_1

    const-string v1, ";"

    invoke-virtual {v0, v1}, Ljava/lang/String;->endsWith(Ljava/lang/String;)Z

    move-result v1

    if-eqz v1, :cond_1

    .line 470
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v3

    sub-int/2addr v3, v2

    invoke-virtual {v0, v2, v3}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "_array"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    :cond_1
    const/16 v1, 0x2e

    .line 472
    invoke-virtual {v0, v1}, Ljava/lang/String;->lastIndexOf(I)I

    move-result v1

    const/4 v3, -0x1

    const-string v4, "_groovyProxy"

    if-ne v1, v3, :cond_2

    .line 473
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    sget-object v1, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->proxyCounter:Ljava/util/concurrent/atomic/AtomicLong;

    invoke-virtual {v1}, Ljava/util/concurrent/atomic/AtomicLong;->incrementAndGet()J

    move-result-wide v1

    invoke-virtual {v0, v1, v2}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0

    .line 474
    :cond_2
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    add-int/2addr v1, v2

    invoke-virtual {v0, v1}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    sget-object v1, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->proxyCounter:Ljava/util/concurrent/atomic/AtomicLong;

    invoke-virtual {v1}, Ljava/util/concurrent/atomic/AtomicLong;->incrementAndGet()J

    move-result-wide v1

    invoke-virtual {v0, v1, v2}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method private static registerLen(Lgroovyjarjarasm/asm/Type;)I
    .locals 1

    .line 584
    sget-object v0, Lgroovyjarjarasm/asm/Type;->DOUBLE_TYPE:Lgroovyjarjarasm/asm/Type;

    if-eq p0, v0, :cond_1

    sget-object v0, Lgroovyjarjarasm/asm/Type;->LONG_TYPE:Lgroovyjarjarasm/asm/Type;

    if-ne p0, v0, :cond_0

    goto :goto_0

    :cond_0
    const/4 p0, 0x1

    goto :goto_1

    :cond_1
    :goto_0
    const/4 p0, 0x2

    :goto_1
    return p0
.end method

.method private static registerLen([Lgroovyjarjarasm/asm/Type;)I
    .locals 4

    .line 577
    array-length v0, p0

    const/4 v1, 0x0

    move v2, v1

    :goto_0
    if-ge v1, v0, :cond_0

    aget-object v3, p0, v1

    .line 578
    invoke-static {v3}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->registerLen(Lgroovyjarjarasm/asm/Type;)I

    move-result v3

    add-int/2addr v2, v3

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    return v2
.end method

.method private static unwrapResult(Lgroovyjarjarasm/asm/MethodVisitor;Ljava/lang/String;)V
    .locals 2

    .line 741
    invoke-static {p1}, Lgroovyjarjarasm/asm/Type;->getReturnType(Ljava/lang/String;)Lgroovyjarjarasm/asm/Type;

    move-result-object p1

    .line 742
    sget-object v0, Lgroovyjarjarasm/asm/Type;->VOID_TYPE:Lgroovyjarjarasm/asm/Type;

    if-ne p1, v0, :cond_0

    const/16 p1, 0x57

    .line 743
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    const/16 p1, 0xb1

    .line 744
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    goto :goto_1

    .line 746
    :cond_0
    invoke-static {p1}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->isPrimitive(Lgroovyjarjarasm/asm/Type;)Z

    move-result v0

    if-eqz v0, :cond_1

    .line 747
    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Type;->getClassName()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/String;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    invoke-static {p0, v0}, Lorg/codehaus/groovy/classgen/asm/BytecodeHelper;->unbox(Lgroovyjarjarasm/asm/MethodVisitor;Lorg/codehaus/groovy/ast/ClassNode;)V

    goto :goto_0

    :cond_1
    const/16 v0, 0xc0

    .line 749
    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Type;->getInternalName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0, v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitTypeInsn(ILjava/lang/String;)V

    .line 751
    :goto_0
    invoke-static {p1}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->getReturnInsn(Lgroovyjarjarasm/asm/Type;)I

    move-result p1

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    :goto_1
    return-void
.end method

.method private visitClass(Ljava/lang/Class;)V
    .locals 13

    .line 356
    invoke-virtual {p1}, Ljava/lang/Class;->getDeclaredMethods()[Ljava/lang/reflect/Method;

    move-result-object v0

    .line 357
    array-length v1, v0

    const/4 v2, 0x0

    move v3, v2

    :goto_0
    if-ge v3, v1, :cond_1

    aget-object v4, v0, v3

    .line 358
    invoke-virtual {v4}, Ljava/lang/reflect/Method;->getExceptionTypes()[Ljava/lang/Class;

    move-result-object v5

    .line 359
    array-length v6, v5

    new-array v12, v6, [Ljava/lang/String;

    move v7, v2

    :goto_1
    if-ge v7, v6, :cond_0

    .line 361
    aget-object v8, v5, v7

    invoke-static {v8}, Lorg/codehaus/groovy/classgen/asm/BytecodeHelper;->getClassInternalName(Ljava/lang/Class;)Ljava/lang/String;

    move-result-object v8

    aput-object v8, v12, v7

    add-int/lit8 v7, v7, 0x1

    goto :goto_1

    .line 364
    :cond_0
    invoke-virtual {v4}, Ljava/lang/reflect/Method;->getModifiers()I

    move-result v8

    .line 365
    invoke-virtual {v4}, Ljava/lang/reflect/Method;->getName()Ljava/lang/String;

    move-result-object v9

    .line 366
    invoke-virtual {v4}, Ljava/lang/reflect/Method;->getReturnType()Ljava/lang/Class;

    move-result-object v5

    invoke-virtual {v4}, Ljava/lang/reflect/Method;->getParameterTypes()[Ljava/lang/Class;

    move-result-object v4

    invoke-static {v5, v4}, Lorg/codehaus/groovy/classgen/asm/BytecodeHelper;->getMethodDescriptor(Ljava/lang/Class;[Ljava/lang/Class;)Ljava/lang/String;

    move-result-object v10

    const/4 v11, 0x0

    move-object v7, p0

    .line 364
    invoke-virtual/range {v7 .. v12}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->visitMethod(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lgroovyjarjarasm/asm/MethodVisitor;

    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    .line 370
    :cond_1
    invoke-virtual {p1}, Ljava/lang/Class;->getDeclaredConstructors()[Ljava/lang/reflect/Constructor;

    move-result-object v0

    .line 371
    array-length v1, v0

    move v3, v2

    :goto_2
    if-ge v3, v1, :cond_3

    aget-object v4, v0, v3

    .line 372
    invoke-virtual {v4}, Ljava/lang/reflect/Constructor;->getExceptionTypes()[Ljava/lang/Class;

    move-result-object v5

    .line 373
    array-length v6, v5

    new-array v12, v6, [Ljava/lang/String;

    move v7, v2

    :goto_3
    if-ge v7, v6, :cond_2

    .line 375
    aget-object v8, v5, v7

    invoke-static {v8}, Lorg/codehaus/groovy/classgen/asm/BytecodeHelper;->getClassInternalName(Ljava/lang/Class;)Ljava/lang/String;

    move-result-object v8

    aput-object v8, v12, v7

    add-int/lit8 v7, v7, 0x1

    goto :goto_3

    .line 378
    :cond_2
    invoke-virtual {v4}, Ljava/lang/reflect/Constructor;->getModifiers()I

    move-result v8

    sget-object v5, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    .line 380
    invoke-virtual {v4}, Ljava/lang/reflect/Constructor;->getParameterTypes()[Ljava/lang/Class;

    move-result-object v4

    invoke-static {v5, v4}, Lorg/codehaus/groovy/classgen/asm/BytecodeHelper;->getMethodDescriptor(Ljava/lang/Class;[Ljava/lang/Class;)Ljava/lang/String;

    move-result-object v10

    const/4 v11, 0x0

    const-string v9, "<init>"

    move-object v7, p0

    .line 378
    invoke-virtual/range {v7 .. v12}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->visitMethod(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lgroovyjarjarasm/asm/MethodVisitor;

    add-int/lit8 v3, v3, 0x1

    goto :goto_2

    .line 385
    :cond_3
    invoke-virtual {p1}, Ljava/lang/Class;->getInterfaces()[Ljava/lang/Class;

    move-result-object v0

    array-length v1, v0

    :goto_4
    if-ge v2, v1, :cond_4

    aget-object v3, v0, v2

    .line 386
    invoke-direct {p0, v3}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->visitClass(Ljava/lang/Class;)V

    add-int/lit8 v2, v2, 0x1

    goto :goto_4

    .line 388
    :cond_4
    invoke-virtual {p1}, Ljava/lang/Class;->getSuperclass()Ljava/lang/Class;

    move-result-object p1

    if-eqz p1, :cond_5

    .line 389
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->visitClass(Ljava/lang/Class;)V

    .line 393
    :cond_5
    iget-object p1, p0, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->delegatedClosures:Ljava/util/Map;

    invoke-interface {p1}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    move-result-object p1

    invoke-interface {p1}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_6
    :goto_5
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_7

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/Map$Entry;

    .line 394
    invoke-interface {v0}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/Boolean;

    .line 395
    invoke-virtual {v1}, Ljava/lang/Boolean;->booleanValue()Z

    move-result v1

    if-nez v1, :cond_6

    .line 396
    invoke-interface {v0}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v0

    move-object v3, v0

    check-cast v3, Ljava/lang/String;

    const-string v0, "*"

    .line 397
    invoke-virtual {v0, v3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_6

    const/4 v2, 0x1

    const/4 v5, 0x0

    const/4 v6, 0x0

    const-string v4, "([Ljava/lang/Object;)Ljava/lang/Object;"

    move-object v1, p0

    .line 399
    invoke-virtual/range {v1 .. v6}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->visitMethod(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lgroovyjarjarasm/asm/MethodVisitor;

    goto :goto_5

    :cond_7
    return-void
.end method


# virtual methods
.method public varargs delegatingProxy(Ljava/lang/Object;Ljava/util/Map;[Ljava/lang/Object;)Lgroovy/lang/GroovyObject;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Ljava/util/Map<",
            "Ljava/lang/Object;",
            "Ljava/lang/Object;",
            ">;[",
            "Ljava/lang/Object;",
            ")",
            "Lgroovy/lang/GroovyObject;"
        }
    .end annotation

    const/4 v0, 0x1

    const/4 v1, 0x0

    const/4 v2, 0x2

    if-nez p3, :cond_0

    .line 774
    iget-object v3, p0, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->cachedNoArgConstructor:Ljava/lang/reflect/Constructor;

    if-eqz v3, :cond_0

    :try_start_0
    new-array p3, v2, [Ljava/lang/Object;

    aput-object p2, p3, v1

    aput-object p1, p3, v0

    .line 777
    invoke-virtual {v3, p3}, Ljava/lang/reflect/Constructor;->newInstance([Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovy/lang/GroovyObject;
    :try_end_0
    .catch Ljava/lang/InstantiationException; {:try_start_0 .. :try_end_0} :catch_2
    .catch Ljava/lang/reflect/InvocationTargetException; {:try_start_0 .. :try_end_0} :catch_1
    .catch Ljava/lang/IllegalAccessException; {:try_start_0 .. :try_end_0} :catch_0

    return-object p1

    :catch_0
    move-exception p1

    goto :goto_0

    :catch_1
    move-exception p1

    goto :goto_0

    :catch_2
    move-exception p1

    .line 779
    :goto_0
    new-instance p2, Lgroovy/lang/GroovyRuntimeException;

    invoke-direct {p2, p1}, Lgroovy/lang/GroovyRuntimeException;-><init>(Ljava/lang/Throwable;)V

    throw p2

    :cond_0
    if-nez p3, :cond_1

    .line 782
    sget-object p3, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->EMPTY_ARGS:[Ljava/lang/Object;

    .line 783
    :cond_1
    array-length v3, p3

    add-int/2addr v3, v2

    new-array v2, v3, [Ljava/lang/Object;

    .line 784
    array-length v4, p3

    invoke-static {p3, v1, v2, v1, v4}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    add-int/lit8 p3, v3, -0x2

    .line 785
    aput-object p2, v2, p3

    sub-int/2addr v3, v0

    .line 786
    aput-object p1, v2, v3

    .line 787
    iget-object p1, p0, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->cachedClass:Ljava/lang/Class;

    invoke-static {p1, v2}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->newInstance(Ljava/lang/Class;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovy/lang/GroovyObject;

    return-object p1
.end method

.method protected makeDelegateCall(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;I)Lgroovyjarjarasm/asm/MethodVisitor;
    .locals 7

    move-object v0, p0

    move v1, p5

    move-object v2, p1

    move-object v3, p2

    move-object v4, p3

    move-object v5, p4

    .line 651
    invoke-super/range {v0 .. v5}, Lgroovyjarjarasm/asm/ClassVisitor;->visitMethod(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lgroovyjarjarasm/asm/MethodVisitor;

    move-result-object p3

    const/16 p4, 0x19

    const/4 p5, 0x0

    .line 652
    invoke-virtual {p3, p4, p5}, Lgroovyjarjarasm/asm/MethodVisitor;->visitVarInsn(II)V

    .line 653
    iget-object p4, p0, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->proxyName:Ljava/lang/String;

    iget-object v0, p0, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->delegateClass:Ljava/lang/Class;

    invoke-static {v0}, Lorg/codehaus/groovy/classgen/asm/BytecodeHelper;->getTypeDescription(Ljava/lang/Class;)Ljava/lang/String;

    move-result-object v0

    const/16 v1, 0xb4

    const-string v2, "$delegate"

    invoke-virtual {p3, v1, p4, v2, v0}, Lgroovyjarjarasm/asm/MethodVisitor;->visitFieldInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    .line 656
    invoke-virtual {p3, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitLdcInsn(Ljava/lang/Object;)V

    .line 657
    invoke-static {p2}, Lgroovyjarjarasm/asm/Type;->getArgumentTypes(Ljava/lang/String;)[Lgroovyjarjarasm/asm/Type;

    move-result-object p1

    .line 658
    array-length p4, p1

    invoke-static {p3, p4}, Lorg/codehaus/groovy/classgen/asm/BytecodeHelper;->pushConstant(Lgroovyjarjarasm/asm/MethodVisitor;I)V

    const/16 p4, 0xbd

    const-string v0, "java/lang/Object"

    .line 659
    invoke-virtual {p3, p4, v0}, Lgroovyjarjarasm/asm/MethodVisitor;->visitTypeInsn(ILjava/lang/String;)V

    const/4 p4, 0x1

    const/4 v0, 0x6

    move v1, p4

    .line 662
    :goto_0
    array-length v2, p1

    if-ge p5, v2, :cond_0

    .line 663
    aget-object v2, p1, p5

    const/16 v3, 0x59

    .line 664
    invoke-virtual {p3, v3}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    .line 665
    invoke-static {p3, p5}, Lorg/codehaus/groovy/classgen/asm/BytecodeHelper;->pushConstant(Lgroovyjarjarasm/asm/MethodVisitor;I)V

    .line 667
    invoke-direct {p0, p3, v1, v2}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->boxPrimitiveType(Lgroovyjarjarasm/asm/MethodVisitor;ILgroovyjarjarasm/asm/Type;)V

    .line 668
    invoke-static {v2}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->registerLen(Lgroovyjarjarasm/asm/Type;)I

    move-result v3

    add-int/lit8 v3, v3, 0x5

    invoke-static {v0, v3}, Ljava/lang/Math;->max(II)I

    move-result v0

    .line 669
    invoke-static {v2}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->registerLen(Lgroovyjarjarasm/asm/Type;)I

    move-result v2

    add-int/2addr v1, v2

    const/16 v2, 0x53

    .line 670
    invoke-virtual {p3, v2}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    add-int/lit8 p5, p5, 0x1

    goto :goto_0

    :cond_0
    const/16 v2, 0xb8

    const/4 v6, 0x0

    const-string v3, "org/codehaus/groovy/runtime/InvokerHelper"

    const-string v4, "invokeMethod"

    const-string v5, "(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object;"

    move-object v1, p3

    .line 672
    invoke-virtual/range {v1 .. v6}, Lgroovyjarjarasm/asm/MethodVisitor;->visitMethodInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V

    .line 673
    invoke-static {p3, p2}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->unwrapResult(Lgroovyjarjarasm/asm/MethodVisitor;Ljava/lang/String;)V

    .line 674
    invoke-static {p1}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->registerLen([Lgroovyjarjarasm/asm/Type;)I

    move-result p1

    add-int/2addr p1, p4

    invoke-virtual {p3, v0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitMaxs(II)V

    return-object p3
.end method

.method protected makeDelegateToClosureCall(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;I)Lgroovyjarjarasm/asm/MethodVisitor;
    .locals 17

    move-object/from16 v6, p0

    move-object/from16 v0, p0

    move/from16 v1, p5

    move-object/from16 v2, p1

    move-object/from16 v3, p2

    move-object/from16 v4, p3

    move-object/from16 v5, p4

    .line 680
    invoke-super/range {v0 .. v5}, Lgroovyjarjarasm/asm/ClassVisitor;->visitMethod(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lgroovyjarjarasm/asm/MethodVisitor;

    move-result-object v7

    .line 681
    invoke-virtual {v7}, Lgroovyjarjarasm/asm/MethodVisitor;->visitCode()V

    .line 685
    invoke-static/range {p2 .. p2}, Lgroovyjarjarasm/asm/Type;->getArgumentTypes(Ljava/lang/String;)[Lgroovyjarjarasm/asm/Type;

    move-result-object v0

    .line 686
    array-length v1, v0

    const/4 v8, 0x1

    add-int/lit8 v9, v1, 0x1

    .line 687
    array-length v1, v0

    invoke-static {v7, v1}, Lorg/codehaus/groovy/classgen/asm/BytecodeHelper;->pushConstant(Lgroovyjarjarasm/asm/MethodVisitor;I)V

    const/16 v1, 0xbd

    const-string v2, "java/lang/Object"

    .line 688
    invoke-virtual {v7, v1, v2}, Lgroovyjarjarasm/asm/MethodVisitor;->visitTypeInsn(ILjava/lang/String;)V

    const/4 v10, 0x0

    move v2, v8

    move v11, v2

    move v1, v10

    .line 691
    :goto_0
    array-length v3, v0

    if-ge v1, v3, :cond_0

    .line 692
    aget-object v3, v0, v1

    const/16 v4, 0x59

    .line 693
    invoke-virtual {v7, v4}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    .line 694
    invoke-static {v7, v1}, Lorg/codehaus/groovy/classgen/asm/BytecodeHelper;->pushConstant(Lgroovyjarjarasm/asm/MethodVisitor;I)V

    .line 696
    invoke-direct {v6, v7, v2, v3}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->boxPrimitiveType(Lgroovyjarjarasm/asm/MethodVisitor;ILgroovyjarjarasm/asm/Type;)V

    .line 697
    invoke-static {v3}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->registerLen(Lgroovyjarjarasm/asm/Type;)I

    move-result v4

    add-int/2addr v2, v4

    const/4 v4, 0x4

    .line 698
    invoke-static {v3}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->registerLen(Lgroovyjarjarasm/asm/Type;)I

    move-result v3

    add-int/lit8 v3, v3, 0x3

    invoke-static {v4, v3}, Ljava/lang/Math;->max(II)I

    move-result v11

    const/16 v3, 0x53

    .line 699
    invoke-virtual {v7, v3}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    const/16 v12, 0x3a

    .line 701
    invoke-virtual {v7, v12, v9}, Lgroovyjarjarasm/asm/MethodVisitor;->visitVarInsn(II)V

    const/16 v13, 0x19

    .line 703
    invoke-virtual {v7, v13, v10}, Lgroovyjarjarasm/asm/MethodVisitor;->visitVarInsn(II)V

    .line 704
    iget-object v0, v6, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->proxyName:Ljava/lang/String;

    const/16 v14, 0xb4

    const-string v15, "$closures$delegate$map"

    const-string v5, "Ljava/util/Map;"

    invoke-virtual {v7, v14, v0, v15, v5}, Lgroovyjarjarasm/asm/MethodVisitor;->visitFieldInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    move-object/from16 v0, p1

    .line 705
    invoke-virtual {v7, v0}, Lgroovyjarjarasm/asm/MethodVisitor;->visitLdcInsn(Ljava/lang/Object;)V

    const/16 v1, 0xb9

    const/16 v16, 0x1

    const-string v2, "java/util/Map"

    const-string v3, "get"

    const-string v4, "(Ljava/lang/Object;)Ljava/lang/Object;"

    move-object v0, v7

    move-object v8, v5

    move/from16 v5, v16

    .line 706
    invoke-virtual/range {v0 .. v5}, Lgroovyjarjarasm/asm/MethodVisitor;->visitMethodInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V

    add-int/lit8 v5, v9, 0x1

    .line 708
    invoke-virtual {v7, v12, v5}, Lgroovyjarjarasm/asm/MethodVisitor;->visitVarInsn(II)V

    .line 710
    new-instance v4, Lgroovyjarjarasm/asm/Label;

    invoke-direct {v4}, Lgroovyjarjarasm/asm/Label;-><init>()V

    .line 711
    invoke-virtual {v7, v13, v5}, Lgroovyjarjarasm/asm/MethodVisitor;->visitIntInsn(II)V

    const/16 v0, 0xc7

    .line 712
    invoke-virtual {v7, v0, v4}, Lgroovyjarjarasm/asm/MethodVisitor;->visitJumpInsn(ILgroovyjarjarasm/asm/Label;)V

    .line 713
    invoke-virtual {v7, v13, v10}, Lgroovyjarjarasm/asm/MethodVisitor;->visitVarInsn(II)V

    .line 714
    iget-object v0, v6, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->proxyName:Ljava/lang/String;

    invoke-virtual {v7, v14, v0, v15, v8}, Lgroovyjarjarasm/asm/MethodVisitor;->visitFieldInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    const-string v0, "*"

    .line 715
    invoke-virtual {v7, v0}, Lgroovyjarjarasm/asm/MethodVisitor;->visitLdcInsn(Ljava/lang/Object;)V

    const/4 v8, 0x1

    const-string v2, "java/util/Map"

    const-string v3, "get"

    const-string v10, "(Ljava/lang/Object;)Ljava/lang/Object;"

    move-object v0, v7

    move-object v14, v4

    move-object v4, v10

    move v10, v5

    move v5, v8

    .line 716
    invoke-virtual/range {v0 .. v5}, Lgroovyjarjarasm/asm/MethodVisitor;->visitMethodInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V

    .line 717
    invoke-virtual {v7, v12, v10}, Lgroovyjarjarasm/asm/MethodVisitor;->visitVarInsn(II)V

    .line 718
    invoke-virtual {v7, v14}, Lgroovyjarjarasm/asm/MethodVisitor;->visitLabel(Lgroovyjarjarasm/asm/Label;)V

    .line 719
    invoke-virtual {v7, v13, v10}, Lgroovyjarjarasm/asm/MethodVisitor;->visitVarInsn(II)V

    const/16 v1, 0xb8

    .line 720
    invoke-virtual/range {p0 .. p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/classgen/asm/BytecodeHelper;->getClassInternalName(Ljava/lang/Class;)Ljava/lang/String;

    move-result-object v2

    const/4 v5, 0x0

    const-string v3, "ensureClosure"

    const-string v4, "(Ljava/lang/Object;)Lgroovy/lang/Closure;"

    move-object v0, v7

    invoke-virtual/range {v0 .. v5}, Lgroovyjarjarasm/asm/MethodVisitor;->visitMethodInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V

    .line 721
    invoke-virtual {v7, v13, v9}, Lgroovyjarjarasm/asm/MethodVisitor;->visitVarInsn(II)V

    const/4 v0, 0x1

    add-int/2addr v11, v0

    const/16 v1, 0xb6

    const-string v2, "groovy/lang/Closure"

    const-string v3, "call"

    const-string v4, "([Ljava/lang/Object;)Ljava/lang/Object;"

    move-object v0, v7

    .line 723
    invoke-virtual/range {v0 .. v5}, Lgroovyjarjarasm/asm/MethodVisitor;->visitMethodInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V

    move-object/from16 v0, p2

    .line 724
    invoke-static {v7, v0}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->unwrapResult(Lgroovyjarjarasm/asm/MethodVisitor;Ljava/lang/String;)V

    const/4 v0, 0x1

    add-int/lit8 v5, v10, 0x1

    .line 725
    invoke-virtual {v7, v11, v5}, Lgroovyjarjarasm/asm/MethodVisitor;->visitMaxs(II)V

    .line 726
    invoke-virtual {v7}, Lgroovyjarjarasm/asm/MethodVisitor;->visitEnd()V

    const/4 v0, 0x0

    return-object v0
.end method

.method public varargs proxy(Ljava/util/Map;[Ljava/lang/Object;)Lgroovy/lang/GroovyObject;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/Object;",
            "Ljava/lang/Object;",
            ">;[",
            "Ljava/lang/Object;",
            ")",
            "Lgroovy/lang/GroovyObject;"
        }
    .end annotation

    const/4 v0, 0x0

    const/4 v1, 0x1

    if-nez p2, :cond_0

    .line 757
    iget-object v2, p0, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->cachedNoArgConstructor:Ljava/lang/reflect/Constructor;

    if-eqz v2, :cond_0

    :try_start_0
    new-array p2, v1, [Ljava/lang/Object;

    aput-object p1, p2, v0

    .line 760
    invoke-virtual {v2, p2}, Ljava/lang/reflect/Constructor;->newInstance([Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovy/lang/GroovyObject;
    :try_end_0
    .catch Ljava/lang/InstantiationException; {:try_start_0 .. :try_end_0} :catch_2
    .catch Ljava/lang/reflect/InvocationTargetException; {:try_start_0 .. :try_end_0} :catch_1
    .catch Ljava/lang/IllegalAccessException; {:try_start_0 .. :try_end_0} :catch_0

    return-object p1

    :catch_0
    move-exception p1

    goto :goto_0

    :catch_1
    move-exception p1

    goto :goto_0

    :catch_2
    move-exception p1

    .line 762
    :goto_0
    new-instance p2, Lgroovy/lang/GroovyRuntimeException;

    invoke-direct {p2, p1}, Lgroovy/lang/GroovyRuntimeException;-><init>(Ljava/lang/Throwable;)V

    throw p2

    :cond_0
    if-nez p2, :cond_1

    .line 765
    sget-object p2, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->EMPTY_ARGS:[Ljava/lang/Object;

    .line 766
    :cond_1
    array-length v2, p2

    add-int/2addr v2, v1

    new-array v3, v2, [Ljava/lang/Object;

    .line 767
    array-length v4, p2

    invoke-static {p2, v0, v3, v0, v4}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    sub-int/2addr v2, v1

    .line 768
    aput-object p1, v3, v2

    .line 769
    iget-object p1, p0, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->cachedClass:Ljava/lang/Class;

    invoke-static {p1, v3}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->newInstance(Ljava/lang/Class;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovy/lang/GroovyObject;

    return-object p1
.end method

.method public visit(IILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)V
    .locals 8

    .line 326
    new-instance v1, Ljava/util/LinkedHashSet;

    invoke-direct {v1}, Ljava/util/LinkedHashSet;-><init>()V

    if-eqz p6, :cond_0

    .line 327
    invoke-static {v1, p6}, Ljava/util/Collections;->addAll(Ljava/util/Collection;[Ljava/lang/Object;)Z

    .line 328
    :cond_0
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->implClasses:Ljava/util/Set;

    invoke-interface {v0}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_1
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_2

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/lang/Class;

    .line 329
    invoke-virtual {v2}, Ljava/lang/Class;->isInterface()Z

    move-result v3

    if-eqz v3, :cond_1

    invoke-static {v2}, Lorg/codehaus/groovy/classgen/asm/BytecodeHelper;->getClassInternalName(Ljava/lang/Class;)Ljava/lang/String;

    move-result-object v2

    invoke-interface {v1, v2}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    goto :goto_0

    .line 331
    :cond_2
    const-class v0, Lgroovy/lang/GroovyObject;

    iget-object v2, p0, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->superClass:Ljava/lang/Class;

    invoke-virtual {v0, v2}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    move-result v0

    xor-int/lit8 v7, v0, 0x1

    if-eqz v7, :cond_3

    const-string v0, "groovy/lang/GroovyObject"

    .line 332
    invoke-interface {v1, v0}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 333
    :cond_3
    iget-boolean v0, p0, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->generateDelegateField:Z

    if-eqz v0, :cond_4

    .line 334
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->implClasses:Ljava/util/Set;

    const-class v2, Lgroovy/lang/GeneratedGroovyProxy;

    invoke-interface {v0, v2}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    const-string v0, "groovy/lang/GeneratedGroovyProxy"

    .line 335
    invoke-interface {v1, v0}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 337
    :cond_4
    sget-object v0, Lorg/codehaus/groovy/control/CompilerConfiguration;->JDK_TO_BYTECODE_VERSION_MAP:Ljava/util/Map;

    sget-object v2, Lorg/codehaus/groovy/control/CompilerConfiguration;->DEFAULT:Lorg/codehaus/groovy/control/CompilerConfiguration;

    invoke-virtual {v2}, Lorg/codehaus/groovy/control/CompilerConfiguration;->getTargetBytecode()Ljava/lang/String;

    move-result-object v2

    invoke-interface {v0, v2}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/Integer;

    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    move-result v2

    const/4 v3, 0x1

    .line 338
    iget-object v4, p0, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->proxyName:Ljava/lang/String;

    iget-object v0, p0, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->superClass:Ljava/lang/Class;

    invoke-static {v0}, Lorg/codehaus/groovy/classgen/asm/BytecodeHelper;->getClassInternalName(Ljava/lang/Class;)Ljava/lang/String;

    move-result-object v5

    const/4 v0, 0x0

    new-array v0, v0, [Ljava/lang/String;

    invoke-interface {v1, v0}, Ljava/util/Set;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object v0

    move-object v6, v0

    check-cast v6, [Ljava/lang/String;

    move-object v0, p0

    move v1, v2

    move v2, v3

    move-object v3, v4

    move-object v4, p4

    invoke-super/range {v0 .. v6}, Lgroovyjarjarasm/asm/ClassVisitor;->visit(IILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)V

    const/4 v0, 0x1

    const/4 v1, 0x0

    const/4 v2, 0x0

    const-string v3, "<init>"

    const-string v4, "()V"

    move-object p1, p0

    move p2, v0

    move-object p3, v3

    move-object p4, v4

    move-object p5, v1

    move-object p6, v2

    .line 339
    invoke-virtual/range {p1 .. p6}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->visitMethod(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lgroovyjarjarasm/asm/MethodVisitor;

    .line 340
    invoke-direct {p0}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->addDelegateFields()V

    if-eqz v7, :cond_5

    .line 342
    invoke-direct {p0}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->createGroovyObjectSupport()V

    .line 344
    :cond_5
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->implClasses:Ljava/util/Set;

    invoke-interface {v0}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_1
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_6

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/Class;

    .line 345
    invoke-direct {p0, v1}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->visitClass(Ljava/lang/Class;)V

    goto :goto_1

    :cond_6
    return-void
.end method

.method public visitMethod(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lgroovyjarjarasm/asm/MethodVisitor;
    .locals 17

    move-object/from16 v6, p0

    move/from16 v0, p1

    move-object/from16 v2, p2

    move-object/from16 v7, p3

    and-int/lit16 v1, v0, 0x1112

    const/4 v8, 0x0

    if-eqz v1, :cond_0

    return-object v8

    :cond_0
    const/4 v9, 0x2

    new-array v1, v9, [Ljava/lang/String;

    const/4 v3, 0x0

    aput-object v2, v1, v3

    const/4 v10, 0x1

    aput-object v7, v1, v10

    .line 495
    invoke-static {v1}, Ljava/util/Arrays;->asList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v1

    .line 496
    iget-object v4, v6, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->visitedMethods:Ljava/util/Set;

    invoke-interface {v4, v1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_1

    return-object v8

    .line 498
    :cond_1
    iget-object v1, v6, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->objectDelegateMethods:Ljava/util/Set;

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v4, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {v4, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    invoke-interface {v1, v4}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result v1

    .line 499
    iget-object v4, v6, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->delegatedClosures:Ljava/util/Map;

    invoke-interface {v4, v2}, Ljava/util/Map;->containsKey(Ljava/lang/Object;)Z

    move-result v4

    .line 500
    iget-boolean v5, v6, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->hasWildcard:Z

    const-string v11, "<init>"

    if-eqz v5, :cond_2

    invoke-virtual {v11, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v5

    if-nez v5, :cond_2

    move v3, v10

    :cond_2
    if-nez v1, :cond_3

    if-nez v4, :cond_3

    if-eqz v3, :cond_8

    .line 502
    :cond_3
    invoke-static/range {p1 .. p1}, Ljava/lang/reflect/Modifier;->isStatic(I)Z

    move-result v5

    if-nez v5, :cond_8

    .line 503
    sget-object v5, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->GROOVYOBJECT_METHOD_NAMES:Ljava/util/Set;

    invoke-interface {v5, v2}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result v5

    if-nez v5, :cond_e

    iget-object v5, v6, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->superClass:Ljava/lang/Class;

    .line 505
    invoke-virtual {v5}, Ljava/lang/Class;->getModifiers()I

    move-result v5

    invoke-static {v5}, Ljava/lang/reflect/Modifier;->isAbstract(I)Z

    move-result v5

    if-eqz v5, :cond_5

    iget-object v5, v6, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->superClass:Ljava/lang/Class;

    invoke-static {v5, v2, v7}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->isImplemented(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;)Z

    move-result v5

    if-eqz v5, :cond_5

    if-nez v1, :cond_4

    if-eqz v4, :cond_e

    :cond_4
    iget-object v5, v6, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->superClass:Ljava/lang/Class;

    .line 506
    invoke-virtual {v5}, Ljava/lang/Class;->getMethods()[Ljava/lang/reflect/Method;

    move-result-object v5

    invoke-static {v5}, Ljava/util/Arrays;->stream([Ljava/lang/Object;)Ljava/util/stream/Stream;

    move-result-object v5

    new-instance v9, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter$$ExternalSyntheticLambda2;

    invoke-direct {v9, v2}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter$$ExternalSyntheticLambda2;-><init>(Ljava/lang/String;)V

    invoke-interface {v5, v9}, Ljava/util/stream/Stream;->filter(Ljava/util/function/Predicate;)Ljava/util/stream/Stream;

    move-result-object v5

    sget-object v9, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter$$ExternalSyntheticLambda3;->INSTANCE:Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter$$ExternalSyntheticLambda3;

    invoke-interface {v5, v9}, Ljava/util/stream/Stream;->mapToInt(Ljava/util/function/ToIntFunction;)Ljava/util/stream/IntStream;

    move-result-object v5

    sget-object v9, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter$$ExternalSyntheticLambda1;->INSTANCE:Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter$$ExternalSyntheticLambda1;

    invoke-interface {v5, v9}, Ljava/util/stream/IntStream;->noneMatch(Ljava/util/function/IntPredicate;)Z

    move-result v5

    if-eqz v5, :cond_e

    :cond_5
    if-nez v4, :cond_7

    if-nez v3, :cond_7

    if-eqz v1, :cond_7

    .line 508
    iget-boolean v1, v6, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->generateDelegateField:Z

    if-nez v1, :cond_6

    goto :goto_0

    :cond_6
    and-int/lit16 v5, v0, -0x401

    move-object/from16 v0, p0

    move-object/from16 v1, p2

    move-object/from16 v2, p3

    move-object/from16 v3, p4

    move-object/from16 v4, p5

    .line 512
    invoke-virtual/range {v0 .. v5}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->makeDelegateCall(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;I)Lgroovyjarjarasm/asm/MethodVisitor;

    move-result-object v0

    return-object v0

    .line 509
    :cond_7
    :goto_0
    iget-object v1, v6, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->delegatedClosures:Ljava/util/Map;

    sget-object v3, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    invoke-interface {v1, v2, v3}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    and-int/lit16 v5, v0, -0x401

    move-object/from16 v0, p0

    move-object/from16 v1, p2

    move-object/from16 v2, p3

    move-object/from16 v3, p4

    move-object/from16 v4, p5

    .line 510
    invoke-virtual/range {v0 .. v5}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->makeDelegateToClosureCall(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;I)Lgroovyjarjarasm/asm/MethodVisitor;

    move-result-object v0

    return-object v0

    :cond_8
    const-string v1, "getProxyTarget"

    .line 514
    invoke-virtual {v1, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_9

    const-string v1, "()Ljava/lang/Object;"

    invoke-virtual {v1, v7}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_9

    .line 515
    invoke-direct/range {p0 .. p5}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->createGetProxyTargetMethod(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lgroovyjarjarasm/asm/MethodVisitor;

    move-result-object v0

    return-object v0

    .line 517
    :cond_9
    invoke-virtual {v11, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_b

    invoke-static/range {p1 .. p1}, Ljava/lang/reflect/Modifier;->isPublic(I)Z

    move-result v1

    if-nez v1, :cond_a

    invoke-static/range {p1 .. p1}, Ljava/lang/reflect/Modifier;->isProtected(I)Z

    move-result v1

    if-eqz v1, :cond_b

    .line 518
    :cond_a
    invoke-direct/range {p0 .. p5}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->createConstructor(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lgroovyjarjarasm/asm/MethodVisitor;

    move-result-object v0

    return-object v0

    .line 520
    :cond_b
    invoke-static/range {p1 .. p1}, Ljava/lang/reflect/Modifier;->isAbstract(I)Z

    move-result v1

    if-eqz v1, :cond_e

    sget-object v1, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->GROOVYOBJECT_METHOD_NAMES:Ljava/util/Set;

    invoke-interface {v1, v2}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_e

    iget-object v1, v6, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->superClass:Ljava/lang/Class;

    invoke-static {v1, v2, v7}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->isImplemented(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;)Z

    move-result v1

    if-nez v1, :cond_e

    and-int/lit16 v1, v0, -0x401

    move-object/from16 v0, p0

    move-object/from16 v2, p2

    move-object/from16 v3, p3

    move-object/from16 v4, p4

    move-object/from16 v5, p5

    .line 521
    invoke-super/range {v0 .. v5}, Lgroovyjarjarasm/asm/ClassVisitor;->visitMethod(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lgroovyjarjarasm/asm/MethodVisitor;

    move-result-object v0

    .line 522
    invoke-virtual {v0}, Lgroovyjarjarasm/asm/MethodVisitor;->visitCode()V

    .line 523
    invoke-static/range {p3 .. p3}, Lgroovyjarjarasm/asm/Type;->getArgumentTypes(Ljava/lang/String;)[Lgroovyjarjarasm/asm/Type;

    move-result-object v1

    .line 524
    iget-boolean v2, v6, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->emptyBody:Z

    if-eqz v2, :cond_d

    .line 525
    invoke-static/range {p3 .. p3}, Lgroovyjarjarasm/asm/Type;->getReturnType(Ljava/lang/String;)Lgroovyjarjarasm/asm/Type;

    move-result-object v2

    .line 526
    sget-object v3, Lgroovyjarjarasm/asm/Type;->VOID_TYPE:Lgroovyjarjarasm/asm/Type;

    if-ne v2, v3, :cond_c

    const/16 v1, 0xb1

    .line 527
    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    goto :goto_2

    .line 529
    :cond_c
    invoke-static {v2}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->getLoadInsn(Lgroovyjarjarasm/asm/Type;)I

    move-result v3

    packed-switch v3, :pswitch_data_0

    .line 544
    invoke-virtual {v0, v10}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    goto :goto_1

    :pswitch_0
    const/16 v3, 0xe

    .line 541
    invoke-virtual {v0, v3}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    goto :goto_1

    :pswitch_1
    const/16 v3, 0xb

    .line 538
    invoke-virtual {v0, v3}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    goto :goto_1

    :pswitch_2
    const/16 v3, 0x9

    .line 535
    invoke-virtual {v0, v3}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    goto :goto_1

    :pswitch_3
    const/4 v3, 0x3

    .line 532
    invoke-virtual {v0, v3}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    .line 546
    :goto_1
    invoke-static {v2}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->getReturnInsn(Lgroovyjarjarasm/asm/Type;)I

    move-result v2

    invoke-virtual {v0, v2}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    .line 547
    invoke-static {v1}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->registerLen([Lgroovyjarjarasm/asm/Type;)I

    move-result v1

    add-int/2addr v1, v10

    invoke-virtual {v0, v9, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitMaxs(II)V

    goto :goto_2

    :cond_d
    const/16 v2, 0xbb

    const-string v3, "java/lang/UnsupportedOperationException"

    .line 552
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarasm/asm/MethodVisitor;->visitTypeInsn(ILjava/lang/String;)V

    const/16 v2, 0x59

    .line 553
    invoke-virtual {v0, v2}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    const/16 v12, 0xb7

    const/16 v16, 0x0

    const-string v13, "java/lang/UnsupportedOperationException"

    const-string v14, "<init>"

    const-string v15, "()V"

    move-object v11, v0

    .line 554
    invoke-virtual/range {v11 .. v16}, Lgroovyjarjarasm/asm/MethodVisitor;->visitMethodInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V

    const/16 v2, 0xbf

    .line 555
    invoke-virtual {v0, v2}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    .line 556
    invoke-static {v1}, Lorg/codehaus/groovy/runtime/ProxyGeneratorAdapter;->registerLen([Lgroovyjarjarasm/asm/Type;)I

    move-result v1

    add-int/2addr v1, v10

    invoke-virtual {v0, v9, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitMaxs(II)V

    .line 558
    :goto_2
    invoke-virtual {v0}, Lgroovyjarjarasm/asm/MethodVisitor;->visitEnd()V

    :cond_e
    return-object v8

    :pswitch_data_0
    .packed-switch 0x15
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method
