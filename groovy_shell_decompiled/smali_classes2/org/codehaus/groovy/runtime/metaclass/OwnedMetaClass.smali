.class public abstract Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;
.super Lgroovy/lang/DelegatingMetaClass;
.source "OwnedMetaClass.java"


# direct methods
.method public constructor <init>(Lgroovy/lang/MetaClass;)V
    .locals 0

    .line 32
    invoke-direct {p0, p1}, Lgroovy/lang/DelegatingMetaClass;-><init>(Lgroovy/lang/MetaClass;)V

    return-void
.end method


# virtual methods
.method public equals(Ljava/lang/Object;)Z
    .locals 1

    .line 131
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->delegate:Lgroovy/lang/MetaClass;

    invoke-virtual {v0, p1}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result p1

    return p1
.end method

.method public getAttribute(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;Z)Ljava/lang/Object;
    .locals 1

    .line 143
    invoke-virtual {p0}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwner()Ljava/lang/Object;

    move-result-object v0

    .line 144
    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwnerMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    .line 145
    invoke-interface {v0, p1, p2, p3, p4}, Lgroovy/lang/MetaClass;->getAttribute(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;Z)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public getAttribute(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;
    .locals 1

    .line 36
    invoke-virtual {p0}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwner()Ljava/lang/Object;

    move-result-object p1

    .line 37
    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwnerMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    .line 38
    invoke-interface {v0, p1, p2}, Lgroovy/lang/MetaClass;->getAttribute(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public getClassNode()Lorg/codehaus/groovy/ast/ClassNode;
    .locals 1

    .line 44
    invoke-virtual {p0}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwner()Ljava/lang/Object;

    move-result-object v0

    .line 45
    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwnerMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    .line 46
    invoke-interface {v0}, Lgroovy/lang/MetaClass;->getClassNode()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    return-object v0
.end method

.method public getMetaMethod(Ljava/lang/String;[Ljava/lang/Class;)Lgroovy/lang/MetaMethod;
    .locals 1

    .line 179
    invoke-virtual {p0}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwner()Ljava/lang/Object;

    move-result-object v0

    .line 180
    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwnerMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    .line 181
    invoke-interface {v0, p1, p2}, Lgroovy/lang/MetaClass;->getMetaMethod(Ljava/lang/String;[Ljava/lang/Object;)Lgroovy/lang/MetaMethod;

    move-result-object p1

    return-object p1
.end method

.method public getMetaMethod(Ljava/lang/String;[Ljava/lang/Object;)Lgroovy/lang/MetaMethod;
    .locals 1

    .line 173
    invoke-virtual {p0}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwner()Ljava/lang/Object;

    move-result-object v0

    .line 174
    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwnerMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    .line 175
    invoke-interface {v0, p1, p2}, Lgroovy/lang/MetaClass;->getMetaMethod(Ljava/lang/String;[Ljava/lang/Object;)Lgroovy/lang/MetaMethod;

    move-result-object p1

    return-object p1
.end method

.method public getMetaMethods()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lgroovy/lang/MetaMethod;",
            ">;"
        }
    .end annotation

    .line 50
    invoke-virtual {p0}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwner()Ljava/lang/Object;

    move-result-object v0

    .line 51
    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwnerMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    .line 52
    invoke-interface {v0}, Lgroovy/lang/MetaClass;->getMetaMethods()Ljava/util/List;

    move-result-object v0

    return-object v0
.end method

.method public getMetaProperty(Ljava/lang/String;)Lgroovy/lang/MetaProperty;
    .locals 1

    .line 155
    invoke-virtual {p0}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwner()Ljava/lang/Object;

    move-result-object v0

    .line 156
    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwnerMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    .line 157
    invoke-interface {v0, p1}, Lgroovy/lang/MetaClass;->getMetaProperty(Ljava/lang/String;)Lgroovy/lang/MetaProperty;

    move-result-object p1

    return-object p1
.end method

.method public getMethods()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lgroovy/lang/MetaMethod;",
            ">;"
        }
    .end annotation

    .line 57
    invoke-virtual {p0}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwner()Ljava/lang/Object;

    move-result-object v0

    .line 58
    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwnerMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    .line 59
    invoke-interface {v0}, Lgroovy/lang/MetaClass;->getMethods()Ljava/util/List;

    move-result-object v0

    return-object v0
.end method

.method protected abstract getOwner()Ljava/lang/Object;
.end method

.method protected abstract getOwnerMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;
.end method

.method public getProperties()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lgroovy/lang/MetaProperty;",
            ">;"
        }
    .end annotation

    .line 81
    invoke-virtual {p0}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwner()Ljava/lang/Object;

    move-result-object v0

    .line 82
    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwnerMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    .line 83
    invoke-interface {v0}, Lgroovy/lang/MetaClass;->getProperties()Ljava/util/List;

    move-result-object v0

    return-object v0
.end method

.method public getProperty(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;ZZ)Ljava/lang/Object;
    .locals 7

    .line 149
    invoke-virtual {p0}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwner()Ljava/lang/Object;

    move-result-object v0

    .line 150
    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwnerMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v1

    move-object v2, p1

    move-object v3, p2

    move-object v4, p3

    move v5, p4

    move v6, p5

    .line 151
    invoke-interface/range {v1 .. v6}, Lgroovy/lang/MetaClass;->getProperty(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;ZZ)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public getProperty(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;
    .locals 1

    .line 87
    invoke-virtual {p0}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwner()Ljava/lang/Object;

    move-result-object p1

    .line 88
    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwnerMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    .line 89
    invoke-interface {v0, p1, p2}, Lgroovy/lang/MetaClass;->getProperty(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public getStaticMetaMethod(Ljava/lang/String;[Ljava/lang/Class;)Lgroovy/lang/MetaMethod;
    .locals 1

    .line 167
    invoke-virtual {p0}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwner()Ljava/lang/Object;

    move-result-object v0

    .line 168
    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwnerMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    .line 169
    invoke-interface {v0, p1, p2}, Lgroovy/lang/MetaClass;->getStaticMetaMethod(Ljava/lang/String;[Ljava/lang/Object;)Lgroovy/lang/MetaMethod;

    move-result-object p1

    return-object p1
.end method

.method public getStaticMetaMethod(Ljava/lang/String;[Ljava/lang/Object;)Lgroovy/lang/MetaMethod;
    .locals 1

    .line 161
    invoke-virtual {p0}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwner()Ljava/lang/Object;

    move-result-object v0

    .line 162
    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwnerMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    .line 163
    invoke-interface {v0, p1, p2}, Lgroovy/lang/MetaClass;->getStaticMetaMethod(Ljava/lang/String;[Ljava/lang/Object;)Lgroovy/lang/MetaMethod;

    move-result-object p1

    return-object p1
.end method

.method public getTheClass()Ljava/lang/Class;
    .locals 1

    .line 185
    invoke-virtual {p0}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwner()Ljava/lang/Object;

    move-result-object v0

    .line 186
    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwnerMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    .line 187
    invoke-interface {v0}, Lgroovy/lang/MetaClass;->getTheClass()Ljava/lang/Class;

    move-result-object v0

    return-object v0
.end method

.method public hasProperty(Ljava/lang/Object;Ljava/lang/String;)Lgroovy/lang/MetaProperty;
    .locals 1

    .line 75
    invoke-virtual {p0}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwner()Ljava/lang/Object;

    move-result-object p1

    .line 76
    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwnerMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    .line 77
    invoke-interface {v0, p1, p2}, Lgroovy/lang/MetaClass;->hasProperty(Ljava/lang/Object;Ljava/lang/String;)Lgroovy/lang/MetaProperty;

    move-result-object p1

    return-object p1
.end method

.method public hashCode()I
    .locals 1

    .line 135
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->delegate:Lgroovy/lang/MetaClass;

    invoke-virtual {v0}, Ljava/lang/Object;->hashCode()I

    move-result v0

    return v0
.end method

.method public invokeConstructor([Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 93
    invoke-virtual {p0}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwner()Ljava/lang/Object;

    move-result-object v0

    .line 94
    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwnerMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    .line 95
    invoke-interface {v0, p1}, Lgroovy/lang/MetaClass;->invokeConstructor([Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public invokeMethod(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;ZZ)Ljava/lang/Object;
    .locals 7

    .line 191
    invoke-virtual {p0}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwner()Ljava/lang/Object;

    move-result-object v2

    .line 192
    invoke-virtual {p0, v2}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwnerMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    move-object v1, p1

    move-object v3, p3

    move-object v4, p4

    move v5, p5

    move v6, p6

    .line 193
    invoke-interface/range {v0 .. v6}, Lgroovy/lang/MetaClass;->invokeMethod(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;ZZ)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public invokeMethod(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 99
    invoke-virtual {p0}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwner()Ljava/lang/Object;

    move-result-object p1

    .line 100
    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwnerMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    .line 101
    invoke-interface {v0, p1, p2, p3}, Lgroovy/lang/MetaClass;->invokeMethod(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public invokeMethod(Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 105
    invoke-virtual {p0}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwner()Ljava/lang/Object;

    move-result-object p1

    .line 106
    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwnerMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    .line 107
    invoke-interface {v0, p1, p2, p3}, Lgroovy/lang/MetaClass;->invokeMethod(Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public invokeMissingMethod(Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 197
    invoke-virtual {p0}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwner()Ljava/lang/Object;

    move-result-object p1

    .line 198
    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwnerMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    .line 199
    invoke-interface {v0, p1, p2, p3}, Lgroovy/lang/MetaClass;->invokeMissingMethod(Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public invokeMissingProperty(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;Z)Ljava/lang/Object;
    .locals 1

    .line 203
    invoke-virtual {p0}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwner()Ljava/lang/Object;

    move-result-object p1

    .line 204
    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwnerMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    .line 205
    invoke-interface {v0, p1, p2, p3, p4}, Lgroovy/lang/MetaClass;->invokeMissingProperty(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;Z)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public invokeStaticMethod(Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 113
    invoke-virtual {p0}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwner()Ljava/lang/Object;

    move-result-object v0

    .line 114
    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwnerMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    .line 115
    invoke-interface {v0, p1, p2, p3}, Lgroovy/lang/MetaClass;->invokeStaticMethod(Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public isGroovyObject()Z
    .locals 2

    .line 209
    invoke-virtual {p0}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwner()Ljava/lang/Object;

    move-result-object v0

    .line 210
    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwnerMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    .line 211
    const-class v1, Lgroovy/lang/GroovyObject;

    invoke-interface {v0}, Lgroovy/lang/MetaClass;->getTheClass()Ljava/lang/Class;

    move-result-object v0

    invoke-virtual {v1, v0}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    move-result v0

    return v0
.end method

.method public respondsTo(Ljava/lang/Object;Ljava/lang/String;)Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Ljava/lang/String;",
            ")",
            "Ljava/util/List<",
            "Lgroovy/lang/MetaMethod;",
            ">;"
        }
    .end annotation

    .line 69
    invoke-virtual {p0}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwner()Ljava/lang/Object;

    move-result-object p1

    .line 70
    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwnerMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    .line 71
    invoke-interface {v0, p1, p2}, Lgroovy/lang/MetaClass;->respondsTo(Ljava/lang/Object;Ljava/lang/String;)Ljava/util/List;

    move-result-object p1

    return-object p1
.end method

.method public respondsTo(Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Ljava/lang/String;",
            "[",
            "Ljava/lang/Object;",
            ")",
            "Ljava/util/List<",
            "Lgroovy/lang/MetaMethod;",
            ">;"
        }
    .end annotation

    .line 63
    invoke-virtual {p0}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwner()Ljava/lang/Object;

    move-result-object p1

    .line 64
    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwnerMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    .line 65
    invoke-interface {v0, p1, p2, p3}, Lgroovy/lang/MetaClass;->respondsTo(Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/util/List;

    move-result-object p1

    return-object p1
.end method

.method public selectConstructorAndTransformArguments(I[Ljava/lang/Object;)I
    .locals 1

    .line 227
    invoke-virtual {p0}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwner()Ljava/lang/Object;

    move-result-object v0

    .line 228
    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwnerMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    .line 229
    invoke-interface {v0, p1, p2}, Lgroovy/lang/MetaClass;->selectConstructorAndTransformArguments(I[Ljava/lang/Object;)I

    move-result p1

    return p1
.end method

.method public setAttribute(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;ZZ)V
    .locals 7

    .line 215
    invoke-virtual {p0}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwner()Ljava/lang/Object;

    move-result-object v2

    .line 216
    invoke-virtual {p0, v2}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwnerMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    move-object v1, p1

    move-object v3, p3

    move-object v4, p4

    move v5, p5

    move v6, p6

    .line 217
    invoke-interface/range {v0 .. v6}, Lgroovy/lang/MetaClass;->setAttribute(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;ZZ)V

    return-void
.end method

.method public setAttribute(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;)V
    .locals 1

    .line 119
    invoke-virtual {p0}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwner()Ljava/lang/Object;

    move-result-object v0

    .line 120
    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwnerMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    .line 121
    invoke-interface {v0, p1, p2, p3}, Lgroovy/lang/MetaClass;->setAttribute(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;)V

    return-void
.end method

.method public setProperty(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;ZZ)V
    .locals 7

    .line 221
    invoke-virtual {p0}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwner()Ljava/lang/Object;

    move-result-object v2

    .line 222
    invoke-virtual {p0, v2}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwnerMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    move-object v1, p1

    move-object v3, p3

    move-object v4, p4

    move v5, p5

    move v6, p6

    .line 223
    invoke-interface/range {v0 .. v6}, Lgroovy/lang/MetaClass;->setProperty(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;ZZ)V

    return-void
.end method

.method public setProperty(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;)V
    .locals 1

    .line 125
    invoke-virtual {p0}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwner()Ljava/lang/Object;

    move-result-object v0

    .line 126
    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->getOwnerMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    .line 127
    invoke-interface {v0, p1, p2, p3}, Lgroovy/lang/MetaClass;->setProperty(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;)V

    return-void
.end method

.method public toString()Ljava/lang/String;
    .locals 2

    .line 139
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-super {p0}, Lgroovy/lang/DelegatingMetaClass;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "["

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v1, p0, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;->delegate:Lgroovy/lang/MetaClass;

    invoke-virtual {v1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "]"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
