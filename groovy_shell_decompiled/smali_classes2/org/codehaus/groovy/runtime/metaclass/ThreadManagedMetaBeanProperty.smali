.class public Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty;
.super Lgroovy/lang/MetaBeanProperty;
.source "ThreadManagedMetaBeanProperty.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty$ThreadBoundGetter;,
        Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty$ThreadBoundSetter;
    }
.end annotation


# static fields
.field private static final PROPNAME_TO_MAP:Ljava/util/concurrent/ConcurrentHashMap;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/concurrent/ConcurrentHashMap<",
            "Ljava/lang/String;",
            "Lorg/codehaus/groovy/util/ManagedConcurrentMap;",
            ">;"
        }
    .end annotation
.end field

.field private static final SOFT_BUNDLE:Lorg/codehaus/groovy/util/ReferenceBundle;


# instance fields
.field private final declaringClass:Ljava/lang/Class;

.field private final getter:Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty$ThreadBoundGetter;

.field private initialValue:Ljava/lang/Object;

.field private initialValueCreator:Lgroovy/lang/Closure;

.field private final instance2Prop:Lorg/codehaus/groovy/util/ManagedConcurrentMap;

.field private final setter:Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty$ThreadBoundSetter;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 42
    new-instance v0, Ljava/util/concurrent/ConcurrentHashMap;

    invoke-direct {v0}, Ljava/util/concurrent/ConcurrentHashMap;-><init>()V

    sput-object v0, Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty;->PROPNAME_TO_MAP:Ljava/util/concurrent/ConcurrentHashMap;

    .line 52
    invoke-static {}, Lorg/codehaus/groovy/util/ReferenceBundle;->getSoftBundle()Lorg/codehaus/groovy/util/ReferenceBundle;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty;->SOFT_BUNDLE:Lorg/codehaus/groovy/util/ReferenceBundle;

    return-void
.end method

.method public constructor <init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/Class;Lgroovy/lang/Closure;)V
    .locals 1

    const/4 v0, 0x0

    .line 109
    invoke-direct {p0, p2, p3, v0, v0}, Lgroovy/lang/MetaBeanProperty;-><init>(Ljava/lang/String;Ljava/lang/Class;Lgroovy/lang/MetaMethod;Lgroovy/lang/MetaMethod;)V

    .line 110
    iput-object p3, p0, Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty;->type:Ljava/lang/Class;

    .line 111
    iput-object p1, p0, Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty;->declaringClass:Ljava/lang/Class;

    .line 113
    new-instance p1, Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty$ThreadBoundGetter;

    invoke-direct {p1, p0, p2}, Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty$ThreadBoundGetter;-><init>(Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty;Ljava/lang/String;)V

    iput-object p1, p0, Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty;->getter:Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty$ThreadBoundGetter;

    .line 114
    new-instance p1, Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty$ThreadBoundSetter;

    invoke-direct {p1, p0, p2}, Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty$ThreadBoundSetter;-><init>(Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty;Ljava/lang/String;)V

    iput-object p1, p0, Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty;->setter:Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty$ThreadBoundSetter;

    .line 115
    iput-object p4, p0, Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty;->initialValueCreator:Lgroovy/lang/Closure;

    .line 117
    invoke-static {p2}, Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty;->getInstance2PropName(Ljava/lang/String;)Lorg/codehaus/groovy/util/ManagedConcurrentMap;

    move-result-object p1

    iput-object p1, p0, Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty;->instance2Prop:Lorg/codehaus/groovy/util/ManagedConcurrentMap;

    return-void
.end method

.method public constructor <init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/Class;Ljava/lang/Object;)V
    .locals 1

    const/4 v0, 0x0

    .line 89
    invoke-direct {p0, p2, p3, v0, v0}, Lgroovy/lang/MetaBeanProperty;-><init>(Ljava/lang/String;Ljava/lang/Class;Lgroovy/lang/MetaMethod;Lgroovy/lang/MetaMethod;)V

    .line 90
    iput-object p3, p0, Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty;->type:Ljava/lang/Class;

    .line 91
    iput-object p1, p0, Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty;->declaringClass:Ljava/lang/Class;

    .line 93
    new-instance p1, Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty$ThreadBoundGetter;

    invoke-direct {p1, p0, p2}, Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty$ThreadBoundGetter;-><init>(Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty;Ljava/lang/String;)V

    iput-object p1, p0, Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty;->getter:Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty$ThreadBoundGetter;

    .line 94
    new-instance p1, Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty$ThreadBoundSetter;

    invoke-direct {p1, p0, p2}, Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty$ThreadBoundSetter;-><init>(Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty;Ljava/lang/String;)V

    iput-object p1, p0, Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty;->setter:Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty$ThreadBoundSetter;

    .line 95
    iput-object p4, p0, Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty;->initialValue:Ljava/lang/Object;

    .line 97
    invoke-static {p2}, Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty;->getInstance2PropName(Ljava/lang/String;)Lorg/codehaus/groovy/util/ManagedConcurrentMap;

    move-result-object p1

    iput-object p1, p0, Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty;->instance2Prop:Lorg/codehaus/groovy/util/ManagedConcurrentMap;

    return-void
.end method

.method static synthetic access$000(Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty;)Ljava/lang/Class;
    .locals 0

    .line 41
    iget-object p0, p0, Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty;->type:Ljava/lang/Class;

    return-object p0
.end method

.method static synthetic access$100(Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty;)Ljava/lang/Class;
    .locals 0

    .line 41
    iget-object p0, p0, Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty;->type:Ljava/lang/Class;

    return-object p0
.end method

.method static synthetic access$200(Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty;)Ljava/lang/Class;
    .locals 0

    .line 41
    iget-object p0, p0, Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty;->declaringClass:Ljava/lang/Class;

    return-object p0
.end method

.method static synthetic access$300(Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty;)Lorg/codehaus/groovy/util/ManagedConcurrentMap;
    .locals 0

    .line 41
    iget-object p0, p0, Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty;->instance2Prop:Lorg/codehaus/groovy/util/ManagedConcurrentMap;

    return-object p0
.end method

.method static synthetic access$400(Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty;)Ljava/lang/Class;
    .locals 0

    .line 41
    iget-object p0, p0, Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty;->type:Ljava/lang/Class;

    return-object p0
.end method

.method static synthetic access$500(Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty;)Ljava/lang/Class;
    .locals 0

    .line 41
    iget-object p0, p0, Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty;->type:Ljava/lang/Class;

    return-object p0
.end method

.method private static getInstance2PropName(Ljava/lang/String;)Lorg/codehaus/groovy/util/ManagedConcurrentMap;
    .locals 3

    .line 121
    sget-object v0, Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty;->PROPNAME_TO_MAP:Ljava/util/concurrent/ConcurrentHashMap;

    invoke-virtual {v0, p0}, Ljava/util/concurrent/ConcurrentHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/util/ManagedConcurrentMap;

    if-nez v1, :cond_0

    .line 123
    new-instance v1, Lorg/codehaus/groovy/util/ManagedConcurrentMap;

    sget-object v2, Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty;->SOFT_BUNDLE:Lorg/codehaus/groovy/util/ReferenceBundle;

    invoke-direct {v1, v2}, Lorg/codehaus/groovy/util/ManagedConcurrentMap;-><init>(Lorg/codehaus/groovy/util/ReferenceBundle;)V

    .line 124
    invoke-virtual {v0, p0, v1}, Ljava/util/concurrent/ConcurrentHashMap;->putIfAbsent(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Lorg/codehaus/groovy/util/ManagedConcurrentMap;

    if-eqz p0, :cond_0

    return-object p0

    :cond_0
    return-object v1
.end method


# virtual methods
.method public getGetter()Lgroovy/lang/MetaMethod;
    .locals 1

    .line 135
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty;->getter:Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty$ThreadBoundGetter;

    return-object v0
.end method

.method public declared-synchronized getInitialValue()Ljava/lang/Object;
    .locals 1

    monitor-enter p0

    const/4 v0, 0x0

    .line 60
    :try_start_0
    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty;->getInitialValue(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit p0

    return-object v0

    :catchall_0
    move-exception v0

    monitor-exit p0

    throw v0
.end method

.method public declared-synchronized getInitialValue(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    monitor-enter p0

    .line 64
    :try_start_0
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty;->initialValueCreator:Lgroovy/lang/Closure;

    if-eqz v0, :cond_0

    .line 65
    invoke-virtual {v0, p1}, Lgroovy/lang/Closure;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit p0

    return-object p1

    .line 67
    :cond_0
    :try_start_1
    iget-object p1, p0, Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty;->initialValue:Ljava/lang/Object;
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    monitor-exit p0

    return-object p1

    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method

.method public getSetter()Lgroovy/lang/MetaMethod;
    .locals 1

    .line 142
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty;->setter:Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty$ThreadBoundSetter;

    return-object v0
.end method

.method public setInitialValueCreator(Lgroovy/lang/Closure;)V
    .locals 0

    .line 77
    iput-object p1, p0, Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty;->initialValueCreator:Lgroovy/lang/Closure;

    return-void
.end method
