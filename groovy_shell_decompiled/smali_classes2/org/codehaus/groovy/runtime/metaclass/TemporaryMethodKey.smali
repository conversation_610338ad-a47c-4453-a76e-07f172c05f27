.class public Lorg/codehaus/groovy/runtime/metaclass/TemporaryMethodKey;
.super Lorg/codehaus/groovy/runtime/MethodKey;
.source "TemporaryMethodKey.java"


# instance fields
.field private final parameterValues:[Ljava/lang/Object;


# direct methods
.method public constructor <init>(Ljava/lang/Class;Ljava/lang/String;[Ljava/lang/Object;Z)V
    .locals 0

    .line 34
    invoke-direct {p0, p1, p2, p4}, Lorg/codehaus/groovy/runtime/MethodKey;-><init>(Ljava/lang/Class;Ljava/lang/String;Z)V

    if-nez p3, :cond_0

    .line 36
    sget-object p3, Lorg/codehaus/groovy/runtime/MetaClassHelper;->EMPTY_ARRAY:[Ljava/lang/Object;

    .line 38
    :cond_0
    iput-object p3, p0, Lorg/codehaus/groovy/runtime/metaclass/TemporaryMethodKey;->parameterValues:[Ljava/lang/Object;

    return-void
.end method


# virtual methods
.method public getParameterCount()I
    .locals 1

    .line 42
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/metaclass/TemporaryMethodKey;->parameterValues:[Ljava/lang/Object;

    array-length v0, v0

    return v0
.end method

.method public getParameterType(I)Ljava/lang/Class;
    .locals 2

    .line 46
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/metaclass/TemporaryMethodKey;->parameterValues:[Ljava/lang/Object;

    aget-object p1, v0, p1

    if-eqz p1, :cond_1

    .line 49
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    const-class v1, Ljava/lang/Class;

    if-ne v0, v1, :cond_0

    goto :goto_0

    .line 51
    :cond_0
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object p1

    :goto_0
    check-cast p1, Ljava/lang/Class;

    return-object p1

    .line 55
    :cond_1
    const-class p1, Ljava/lang/Object;

    return-object p1
.end method
