.class public Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;
.super Ljava/lang/Object;
.source "MetaMethodIndex.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;,
        Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;,
        Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$CacheEntry;,
        Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$EntryIterator;
    }
.end annotation


# static fields
.field protected static final DEFAULT_CAPACITY:I = 0x20

.field protected static final MAXIMUM_CAPACITY:I = 0x10000000

.field protected static final MINIMUM_CAPACITY:I = 0x4


# instance fields
.field public methodHeaders:Lorg/codehaus/groovy/util/SingleKeyHashMap;

.field protected size:I

.field protected table:[Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;

.field protected transient threshold:I


# direct methods
.method public constructor <init>(Lorg/codehaus/groovy/reflection/CachedClass;)V
    .locals 6

    .line 79
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 32
    new-instance v0, Lorg/codehaus/groovy/util/SingleKeyHashMap;

    invoke-direct {v0}, Lorg/codehaus/groovy/util/SingleKeyHashMap;-><init>()V

    iput-object v0, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->methodHeaders:Lorg/codehaus/groovy/util/SingleKeyHashMap;

    const/16 v0, 0x20

    .line 80
    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->init(I)V

    .line 83
    invoke-virtual {p1}, Lorg/codehaus/groovy/reflection/CachedClass;->isInterface()Z

    move-result v0

    if-nez v0, :cond_1

    const/4 v0, 0x0

    move-object v1, v0

    :goto_0
    if-eqz p1, :cond_2

    .line 85
    iget-object v2, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->methodHeaders:Lorg/codehaus/groovy/util/SingleKeyHashMap;

    invoke-virtual {p1}, Lorg/codehaus/groovy/reflection/CachedClass;->getTheClass()Ljava/lang/Class;

    move-result-object v3

    invoke-virtual {v2, v3}, Lorg/codehaus/groovy/util/SingleKeyHashMap;->getOrPut(Ljava/lang/Object;)Lorg/codehaus/groovy/util/SingleKeyHashMap$Entry;

    move-result-object v2

    .line 86
    new-instance v3, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;

    invoke-virtual {p1}, Lorg/codehaus/groovy/reflection/CachedClass;->getTheClass()Ljava/lang/Class;

    move-result-object v4

    if-nez v1, :cond_0

    move-object v1, v0

    goto :goto_1

    :cond_0
    invoke-virtual {v1}, Lorg/codehaus/groovy/reflection/CachedClass;->getTheClass()Ljava/lang/Class;

    move-result-object v1

    :goto_1
    invoke-direct {v3, v4, v1}, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;-><init>(Ljava/lang/Class;Ljava/lang/Class;)V

    iput-object v3, v2, Lorg/codehaus/groovy/util/SingleKeyHashMap$Entry;->value:Ljava/lang/Object;

    .line 84
    invoke-virtual {p1}, Lorg/codehaus/groovy/reflection/CachedClass;->getCachedSuperClass()Lorg/codehaus/groovy/reflection/CachedClass;

    move-result-object v1

    move-object v5, v1

    move-object v1, p1

    move-object p1, v5

    goto :goto_0

    .line 90
    :cond_1
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->methodHeaders:Lorg/codehaus/groovy/util/SingleKeyHashMap;

    const-class v1, Ljava/lang/Object;

    invoke-virtual {v0, v1}, Lorg/codehaus/groovy/util/SingleKeyHashMap;->getOrPut(Ljava/lang/Object;)Lorg/codehaus/groovy/util/SingleKeyHashMap$Entry;

    move-result-object v0

    .line 91
    new-instance v1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;

    const-class v2, Ljava/lang/Object;

    invoke-virtual {p1}, Lorg/codehaus/groovy/reflection/CachedClass;->getTheClass()Ljava/lang/Class;

    move-result-object p1

    invoke-direct {v1, v2, p1}, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;-><init>(Ljava/lang/Class;Ljava/lang/Class;)V

    iput-object v1, v0, Lorg/codehaus/groovy/util/SingleKeyHashMap$Entry;->value:Ljava/lang/Object;

    :cond_2
    return-void
.end method

.method private copyAllMethods(Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;)V
    .locals 6

    .line 491
    iget-object v0, p1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->methods:Ljava/lang/Object;

    .line 492
    instance-of v1, v0, Lorg/codehaus/groovy/util/FastArray;

    if-eqz v1, :cond_1

    .line 493
    check-cast v0, Lorg/codehaus/groovy/util/FastArray;

    const/4 v1, 0x0

    .line 495
    invoke-virtual {v0}, Lorg/codehaus/groovy/util/FastArray;->size()I

    move-result v2

    .line 496
    invoke-virtual {v0}, Lorg/codehaus/groovy/util/FastArray;->getArray()[Ljava/lang/Object;

    move-result-object v0

    const/4 v3, 0x0

    :goto_0
    if-eq v3, v2, :cond_2

    .line 498
    aget-object v4, v0, v3

    check-cast v4, Lgroovy/lang/MetaMethod;

    if-nez v1, :cond_0

    .line 500
    iget-object v1, p1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->name:Ljava/lang/String;

    invoke-virtual {p0, v1, p2}, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->getOrPutMethods(Ljava/lang/String;Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;)Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;

    move-result-object v1

    .line 501
    :cond_0
    iget-object v5, v1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->methods:Ljava/lang/Object;

    invoke-virtual {p0, v5, v4}, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->addMethodToList(Ljava/lang/Object;Lgroovy/lang/MetaMethod;)Ljava/lang/Object;

    move-result-object v4

    iput-object v4, v1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->methods:Ljava/lang/Object;

    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    .line 504
    :cond_1
    check-cast v0, Lgroovy/lang/MetaMethod;

    .line 505
    invoke-virtual {v0}, Lgroovy/lang/MetaMethod;->isPrivate()Z

    move-result v1

    if-nez v1, :cond_2

    .line 506
    iget-object p1, p1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->name:Ljava/lang/String;

    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->getOrPutMethods(Ljava/lang/String;Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;)Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;

    move-result-object p1

    .line 507
    iget-object p2, p1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->methods:Ljava/lang/Object;

    invoke-virtual {p0, p2, v0}, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->addMethodToList(Ljava/lang/Object;Lgroovy/lang/MetaMethod;)Ljava/lang/Object;

    move-result-object p2

    iput-object p2, p1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->methods:Ljava/lang/Object;

    :cond_2
    return-void
.end method

.method private copyAllMethodsToSuper(Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;)V
    .locals 6

    .line 298
    iget-object v0, p1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->methods:Ljava/lang/Object;

    .line 299
    instance-of v1, v0, Lorg/codehaus/groovy/util/FastArray;

    if-eqz v1, :cond_1

    .line 300
    check-cast v0, Lorg/codehaus/groovy/util/FastArray;

    const/4 v1, 0x0

    .line 302
    invoke-virtual {v0}, Lorg/codehaus/groovy/util/FastArray;->size()I

    move-result v2

    .line 303
    invoke-virtual {v0}, Lorg/codehaus/groovy/util/FastArray;->getArray()[Ljava/lang/Object;

    move-result-object v0

    const/4 v3, 0x0

    :goto_0
    if-eq v3, v2, :cond_2

    .line 305
    aget-object v4, v0, v3

    check-cast v4, Lgroovy/lang/MetaMethod;

    if-nez v1, :cond_0

    .line 307
    iget-object v1, p1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->name:Ljava/lang/String;

    invoke-virtual {p0, v1, p2}, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->getOrPutMethods(Ljava/lang/String;Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;)Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;

    move-result-object v1

    .line 308
    :cond_0
    iget-object v5, v1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->methodsForSuper:Ljava/lang/Object;

    invoke-virtual {p0, v5, v4}, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->addMethodToList(Ljava/lang/Object;Lgroovy/lang/MetaMethod;)Ljava/lang/Object;

    move-result-object v4

    iput-object v4, v1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->methodsForSuper:Ljava/lang/Object;

    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    .line 311
    :cond_1
    check-cast v0, Lgroovy/lang/MetaMethod;

    .line 312
    iget-object p1, p1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->name:Ljava/lang/String;

    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->getOrPutMethods(Ljava/lang/String;Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;)Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;

    move-result-object p1

    .line 313
    iget-object p2, p1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->methodsForSuper:Ljava/lang/Object;

    invoke-virtual {p0, p2, v0}, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->addMethodToList(Ljava/lang/Object;Lgroovy/lang/MetaMethod;)Ljava/lang/Object;

    move-result-object p2

    iput-object p2, p1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->methodsForSuper:Ljava/lang/Object;

    :cond_2
    return-void
.end method

.method private copyNonPrivateMethods(Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;)V
    .locals 6

    .line 275
    iget-object v0, p1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->methods:Ljava/lang/Object;

    .line 276
    instance-of v1, v0, Lorg/codehaus/groovy/util/FastArray;

    if-eqz v1, :cond_2

    .line 277
    check-cast v0, Lorg/codehaus/groovy/util/FastArray;

    const/4 v1, 0x0

    .line 279
    invoke-virtual {v0}, Lorg/codehaus/groovy/util/FastArray;->size()I

    move-result v2

    .line 280
    invoke-virtual {v0}, Lorg/codehaus/groovy/util/FastArray;->getArray()[Ljava/lang/Object;

    move-result-object v0

    const/4 v3, 0x0

    :goto_0
    if-eq v3, v2, :cond_3

    .line 282
    aget-object v4, v0, v3

    check-cast v4, Lgroovy/lang/MetaMethod;

    .line 283
    invoke-virtual {v4}, Lgroovy/lang/MetaMethod;->isPrivate()Z

    move-result v5

    if-eqz v5, :cond_0

    goto :goto_1

    :cond_0
    if-nez v1, :cond_1

    .line 285
    iget-object v1, p1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->name:Ljava/lang/String;

    invoke-virtual {p0, v1, p2}, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->getOrPutMethods(Ljava/lang/String;Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;)Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;

    move-result-object v1

    .line 286
    :cond_1
    iget-object v5, v1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->methods:Ljava/lang/Object;

    invoke-virtual {p0, v5, v4}, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->addMethodToList(Ljava/lang/Object;Lgroovy/lang/MetaMethod;)Ljava/lang/Object;

    move-result-object v4

    iput-object v4, v1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->methods:Ljava/lang/Object;

    :goto_1
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    .line 289
    :cond_2
    check-cast v0, Lgroovy/lang/MetaMethod;

    .line 290
    invoke-virtual {v0}, Lgroovy/lang/MetaMethod;->isPrivate()Z

    move-result v1

    if-nez v1, :cond_3

    .line 291
    iget-object p1, p1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->name:Ljava/lang/String;

    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->getOrPutMethods(Ljava/lang/String;Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;)Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;

    move-result-object p1

    .line 292
    iget-object p2, p1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->methods:Ljava/lang/Object;

    invoke-virtual {p0, p2, v0}, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->addMethodToList(Ljava/lang/Object;Lgroovy/lang/MetaMethod;)Ljava/lang/Object;

    move-result-object p2

    iput-object p2, p1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->methods:Ljava/lang/Object;

    :cond_3
    return-void
.end method

.method private copyNonPrivateMethodsFromSuper(Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;)V
    .locals 5

    .line 318
    iget-object v0, p1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->methodsForSuper:Ljava/lang/Object;

    if-nez v0, :cond_0

    return-void

    .line 323
    :cond_0
    instance-of v1, v0, Lorg/codehaus/groovy/util/FastArray;

    if-eqz v1, :cond_2

    .line 324
    check-cast v0, Lorg/codehaus/groovy/util/FastArray;

    .line 325
    invoke-virtual {v0}, Lorg/codehaus/groovy/util/FastArray;->size()I

    move-result v1

    .line 326
    invoke-virtual {v0}, Lorg/codehaus/groovy/util/FastArray;->getArray()[Ljava/lang/Object;

    move-result-object v0

    const/4 v2, 0x0

    :goto_0
    if-eq v2, v1, :cond_3

    .line 328
    aget-object v3, v0, v2

    check-cast v3, Lgroovy/lang/MetaMethod;

    .line 329
    invoke-virtual {v3}, Lgroovy/lang/MetaMethod;->isPrivate()Z

    move-result v4

    if-eqz v4, :cond_1

    goto :goto_1

    .line 330
    :cond_1
    iget-object v4, p1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->methods:Ljava/lang/Object;

    invoke-virtual {p0, v4, v3}, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->addMethodToList(Ljava/lang/Object;Lgroovy/lang/MetaMethod;)Ljava/lang/Object;

    move-result-object v3

    iput-object v3, p1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->methods:Ljava/lang/Object;

    :goto_1
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 333
    :cond_2
    check-cast v0, Lgroovy/lang/MetaMethod;

    .line 334
    invoke-virtual {v0}, Lgroovy/lang/MetaMethod;->isPrivate()Z

    move-result v1

    if-nez v1, :cond_3

    .line 335
    iget-object v1, p1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->methods:Ljava/lang/Object;

    invoke-virtual {p0, v1, v0}, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->addMethodToList(Ljava/lang/Object;Lgroovy/lang/MetaMethod;)Ljava/lang/Object;

    move-result-object v0

    iput-object v0, p1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->methods:Ljava/lang/Object;

    :cond_3
    return-void
.end method

.method private copyNonPrivateNonNewMetaMethods(Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;)V
    .locals 6

    .line 350
    iget-object v0, p1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->methods:Ljava/lang/Object;

    if-nez v0, :cond_0

    return-void

    .line 355
    :cond_0
    instance-of v1, v0, Lorg/codehaus/groovy/util/FastArray;

    if-eqz v1, :cond_4

    .line 356
    check-cast v0, Lorg/codehaus/groovy/util/FastArray;

    const/4 v1, 0x0

    .line 358
    invoke-virtual {v0}, Lorg/codehaus/groovy/util/FastArray;->size()I

    move-result v2

    .line 359
    invoke-virtual {v0}, Lorg/codehaus/groovy/util/FastArray;->getArray()[Ljava/lang/Object;

    move-result-object v0

    const/4 v3, 0x0

    :goto_0
    if-eq v3, v2, :cond_6

    .line 361
    aget-object v4, v0, v3

    check-cast v4, Lgroovy/lang/MetaMethod;

    .line 362
    instance-of v5, v4, Lorg/codehaus/groovy/runtime/metaclass/NewMetaMethod;

    if-nez v5, :cond_3

    invoke-virtual {v4}, Lgroovy/lang/MetaMethod;->isPrivate()Z

    move-result v5

    if-eqz v5, :cond_1

    goto :goto_1

    :cond_1
    if-nez v1, :cond_2

    .line 364
    iget-object v1, p1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->name:Ljava/lang/String;

    invoke-virtual {p0, v1, p2}, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->getOrPutMethods(Ljava/lang/String;Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;)Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;

    move-result-object v1

    .line 365
    :cond_2
    iget-object v5, v1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->methods:Ljava/lang/Object;

    invoke-virtual {p0, v5, v4}, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->addMethodToList(Ljava/lang/Object;Lgroovy/lang/MetaMethod;)Ljava/lang/Object;

    move-result-object v4

    iput-object v4, v1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->methods:Ljava/lang/Object;

    :cond_3
    :goto_1
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    .line 368
    :cond_4
    check-cast v0, Lgroovy/lang/MetaMethod;

    .line 369
    instance-of v1, v0, Lorg/codehaus/groovy/runtime/metaclass/NewMetaMethod;

    if-nez v1, :cond_6

    invoke-virtual {v0}, Lgroovy/lang/MetaMethod;->isPrivate()Z

    move-result v1

    if-eqz v1, :cond_5

    goto :goto_2

    .line 370
    :cond_5
    iget-object p1, p1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->name:Ljava/lang/String;

    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->getOrPutMethods(Ljava/lang/String;Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;)Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;

    move-result-object p1

    .line 371
    iget-object p2, p1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->methods:Ljava/lang/Object;

    invoke-virtual {p0, p2, v0}, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->addMethodToList(Ljava/lang/Object;Lgroovy/lang/MetaMethod;)Ljava/lang/Object;

    move-result-object p2

    iput-object p2, p1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->methods:Ljava/lang/Object;

    :cond_6
    :goto_2
    return-void
.end method

.method private static findMatchingMethod(Lorg/codehaus/groovy/util/FastArray;Lgroovy/lang/MetaMethod;)I
    .locals 3

    .line 457
    invoke-virtual {p0}, Lorg/codehaus/groovy/util/FastArray;->size()I

    move-result v0

    .line 458
    invoke-virtual {p0}, Lorg/codehaus/groovy/util/FastArray;->getArray()[Ljava/lang/Object;

    move-result-object p0

    const/4 v1, 0x0

    :goto_0
    if-eq v1, v0, :cond_1

    .line 460
    aget-object v2, p0, v1

    check-cast v2, Lgroovy/lang/MetaMethod;

    .line 461
    invoke-static {v2, p1}, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->isMatchingMethod(Lgroovy/lang/MetaMethod;Lgroovy/lang/MetaMethod;)Z

    move-result v2

    if-eqz v2, :cond_0

    return v1

    :cond_0
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_1
    const/4 p0, -0x1

    return p0
.end method

.method public static hash(I)I
    .locals 1

    shl-int/lit8 v0, p0, 0x9

    not-int v0, v0

    add-int/2addr p0, v0

    ushr-int/lit8 v0, p0, 0xe

    xor-int/2addr p0, v0

    shl-int/lit8 v0, p0, 0x4

    add-int/2addr p0, v0

    ushr-int/lit8 v0, p0, 0xa

    xor-int/2addr p0, v0

    return p0
.end method

.method private static isMatchingMethod(Lgroovy/lang/MetaMethod;Lgroovy/lang/MetaMethod;)Z
    .locals 6

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    .line 444
    :cond_0
    invoke-virtual {p0}, Lgroovy/lang/MetaMethod;->getParameterTypes()[Lorg/codehaus/groovy/reflection/CachedClass;

    move-result-object p0

    .line 445
    invoke-virtual {p1}, Lgroovy/lang/MetaMethod;->getParameterTypes()[Lorg/codehaus/groovy/reflection/CachedClass;

    move-result-object p1

    .line 446
    array-length v1, p0

    array-length v2, p1

    const/4 v3, 0x0

    if-eq v1, v2, :cond_1

    return v3

    .line 448
    :cond_1
    array-length v1, p0

    move v2, v3

    :goto_0
    if-ge v2, v1, :cond_3

    .line 449
    aget-object v4, p0, v2

    aget-object v5, p1, v2

    if-eq v4, v5, :cond_2

    return v3

    :cond_2
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_3
    return v0
.end method

.method private static isNonRealMethod(Lgroovy/lang/MetaMethod;)Z
    .locals 1

    .line 434
    instance-of v0, p0, Lorg/codehaus/groovy/runtime/metaclass/NewMetaMethod;

    if-nez v0, :cond_1

    instance-of v0, p0, Lorg/codehaus/groovy/runtime/metaclass/ClosureMetaMethod;

    if-nez v0, :cond_1

    instance-of v0, p0, Lorg/codehaus/groovy/reflection/GeneratedMetaMethod;

    if-nez v0, :cond_1

    instance-of v0, p0, Lorg/codehaus/groovy/runtime/metaclass/ClosureStaticMetaMethod;

    if-nez v0, :cond_1

    instance-of v0, p0, Lorg/codehaus/groovy/runtime/metaclass/MixinInstanceMetaMethod;

    if-nez v0, :cond_1

    instance-of p0, p0, Lorg/codehaus/groovy/runtime/metaclass/ClosureMetaMethod$AnonymousMetaMethod;

    if-eqz p0, :cond_0

    goto :goto_0

    :cond_0
    const/4 p0, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 p0, 0x1

    :goto_1
    return p0
.end method

.method private static isOverridden(Lgroovy/lang/MetaMethod;Lgroovy/lang/MetaMethod;)Z
    .locals 5

    .line 414
    invoke-virtual {p0}, Lgroovy/lang/MetaMethod;->isPrivate()Z

    move-result v0

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    return v1

    .line 416
    :cond_0
    invoke-virtual {p0}, Lgroovy/lang/MetaMethod;->getDeclaringClass()Lorg/codehaus/groovy/reflection/CachedClass;

    move-result-object v0

    .line 417
    invoke-virtual {p1}, Lgroovy/lang/MetaMethod;->getDeclaringClass()Lorg/codehaus/groovy/reflection/CachedClass;

    move-result-object v2

    const/4 v3, 0x1

    if-ne v0, v2, :cond_3

    .line 419
    invoke-static {p1}, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->isNonRealMethod(Lgroovy/lang/MetaMethod;)Z

    move-result p1

    if-nez p1, :cond_1

    invoke-virtual {p0}, Lgroovy/lang/MetaMethod;->getModifiers()I

    move-result p0

    and-int/lit16 p0, p0, 0x1000

    if-eqz p0, :cond_2

    :cond_1
    move v1, v3

    :cond_2
    return v1

    .line 423
    :cond_3
    invoke-virtual {p0}, Lgroovy/lang/MetaMethod;->isStatic()Z

    move-result v4

    if-nez v4, :cond_6

    invoke-virtual {p1}, Lgroovy/lang/MetaMethod;->isStatic()Z

    move-result p1

    if-nez p1, :cond_6

    .line 424
    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/CachedClass;->isInterface()Z

    move-result p1

    invoke-virtual {v2}, Lorg/codehaus/groovy/reflection/CachedClass;->isInterface()Z

    move-result v4

    if-eq p1, v4, :cond_6

    .line 426
    invoke-static {p0}, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->isNonRealMethod(Lgroovy/lang/MetaMethod;)Z

    move-result p0

    if-nez p0, :cond_4

    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/CachedClass;->isInterface()Z

    move-result p0

    if-eqz p0, :cond_4

    invoke-virtual {v2}, Lorg/codehaus/groovy/reflection/CachedClass;->isInterface()Z

    move-result p0

    if-eqz p0, :cond_5

    :cond_4
    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/CachedClass;->getTheClass()Ljava/lang/Class;

    move-result-object p0

    invoke-virtual {v2, p0}, Lorg/codehaus/groovy/reflection/CachedClass;->isAssignableFrom(Ljava/lang/Class;)Z

    move-result p0

    if-nez p0, :cond_5

    move v1, v3

    :cond_5
    return v1

    .line 430
    :cond_6
    invoke-virtual {v2}, Lorg/codehaus/groovy/reflection/CachedClass;->getTheClass()Ljava/lang/Class;

    move-result-object p0

    invoke-virtual {v0, p0}, Lorg/codehaus/groovy/reflection/CachedClass;->isAssignableFrom(Ljava/lang/Class;)Z

    move-result p0

    return p0
.end method


# virtual methods
.method public addMethodToList(Ljava/lang/Object;Lgroovy/lang/MetaMethod;)Ljava/lang/Object;
    .locals 3

    if-nez p1, :cond_0

    return-object p2

    .line 380
    :cond_0
    instance-of v0, p1, Lgroovy/lang/MetaMethod;

    if-eqz v0, :cond_3

    .line 381
    check-cast p1, Lgroovy/lang/MetaMethod;

    .line 382
    invoke-static {p1, p2}, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->isMatchingMethod(Lgroovy/lang/MetaMethod;Lgroovy/lang/MetaMethod;)Z

    move-result v0

    if-nez v0, :cond_1

    .line 383
    new-instance v0, Lorg/codehaus/groovy/util/FastArray;

    const/4 v1, 0x2

    new-array v1, v1, [Ljava/lang/Object;

    const/4 v2, 0x0

    aput-object p1, v1, v2

    const/4 p1, 0x1

    aput-object p2, v1, p1

    invoke-direct {v0, v1}, Lorg/codehaus/groovy/util/FastArray;-><init>([Ljava/lang/Object;)V

    return-object v0

    .line 385
    :cond_1
    invoke-static {p1, p2}, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->isOverridden(Lgroovy/lang/MetaMethod;Lgroovy/lang/MetaMethod;)Z

    move-result v0

    if-nez v0, :cond_2

    move-object p2, p1

    :cond_2
    return-object p2

    .line 388
    :cond_3
    instance-of v0, p1, Lorg/codehaus/groovy/util/FastArray;

    if-eqz v0, :cond_5

    .line 389
    move-object v0, p1

    check-cast v0, Lorg/codehaus/groovy/util/FastArray;

    .line 390
    invoke-static {v0, p2}, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->findMatchingMethod(Lorg/codehaus/groovy/util/FastArray;Lgroovy/lang/MetaMethod;)I

    move-result v1

    const/4 v2, -0x1

    if-ne v1, v2, :cond_4

    .line 392
    invoke-virtual {v0, p2}, Lorg/codehaus/groovy/util/FastArray;->add(Ljava/lang/Object;)V

    goto :goto_0

    .line 394
    :cond_4
    invoke-virtual {v0, v1}, Lorg/codehaus/groovy/util/FastArray;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovy/lang/MetaMethod;

    if-eq v2, p2, :cond_5

    .line 395
    invoke-static {v2, p2}, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->isOverridden(Lgroovy/lang/MetaMethod;Lgroovy/lang/MetaMethod;)Z

    move-result v2

    if-eqz v2, :cond_5

    .line 396
    invoke-virtual {v0, v1, p2}, Lorg/codehaus/groovy/util/FastArray;->set(ILjava/lang/Object;)V

    :cond_5
    :goto_0
    return-object p1
.end method

.method public clear()V
    .locals 2

    .line 121
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->table:[Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;

    const/4 v1, 0x0

    .line 122
    invoke-static {v0, v1}, Ljava/util/Arrays;->fill([Ljava/lang/Object;Ljava/lang/Object;)V

    const/4 v0, 0x0

    .line 123
    iput v0, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->size:I

    return-void
.end method

.method public clearCaches()V
    .locals 3

    const/4 v0, 0x0

    .line 513
    :goto_0
    iget-object v1, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->table:[Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;

    array-length v2, v1

    if-eq v0, v2, :cond_1

    .line 514
    aget-object v1, v1, v0

    :goto_1
    if-eqz v1, :cond_0

    const/4 v2, 0x0

    .line 515
    iput-object v2, v1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->cachedStaticMethod:Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$CacheEntry;

    iput-object v2, v1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->cachedMethodForSuper:Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$CacheEntry;

    iput-object v2, v1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->cachedMethod:Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$CacheEntry;

    .line 514
    iget-object v1, v1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->nextHashEntry:Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;

    goto :goto_1

    :cond_0
    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_1
    return-void
.end method

.method public clearCaches(Ljava/lang/String;)V
    .locals 3

    const/4 v0, 0x0

    .line 520
    :goto_0
    iget-object v1, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->table:[Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;

    array-length v2, v1

    if-eq v0, v2, :cond_2

    .line 521
    aget-object v1, v1, v0

    :goto_1
    if-eqz v1, :cond_1

    .line 522
    iget-object v2, v1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->name:Ljava/lang/String;

    invoke-virtual {v2, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_0

    const/4 v2, 0x0

    .line 523
    iput-object v2, v1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->cachedStaticMethod:Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$CacheEntry;

    iput-object v2, v1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->cachedMethodForSuper:Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$CacheEntry;

    iput-object v2, v1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->cachedMethod:Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$CacheEntry;

    .line 521
    :cond_0
    iget-object v1, v1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->nextHashEntry:Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;

    goto :goto_1

    :cond_1
    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_2
    return-void
.end method

.method public copy(Ljava/lang/Class;Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;)V
    .locals 0

    .line 482
    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->getHeader(Ljava/lang/Class;)Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;

    move-result-object p1

    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->copy(Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;)V

    return-void
.end method

.method public copy(Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;)V
    .locals 0

    .line 486
    iget-object p1, p1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;->head:Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;

    :goto_0
    if-eqz p1, :cond_0

    .line 487
    invoke-direct {p0, p1, p2}, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->copyAllMethods(Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;)V

    .line 486
    iget-object p1, p1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->nextClassEntry:Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;

    goto :goto_0

    :cond_0
    return-void
.end method

.method public copyAllMethodsToSuper(Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;)V
    .locals 0

    .line 265
    iget-object p1, p1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;->head:Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;

    :goto_0
    if-eqz p1, :cond_0

    .line 266
    invoke-direct {p0, p1, p2}, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->copyAllMethodsToSuper(Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;)V

    .line 265
    iget-object p1, p1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->nextClassEntry:Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;

    goto :goto_0

    :cond_0
    return-void
.end method

.method public copyMethodsToSuper()V
    .locals 5

    .line 468
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->table:[Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;

    .line 470
    array-length v1, v0

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v1, :cond_2

    aget-object v3, v0, v2

    :goto_1
    if-eqz v3, :cond_1

    .line 472
    iget-object v4, v3, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->methods:Ljava/lang/Object;

    instance-of v4, v4, Lorg/codehaus/groovy/util/FastArray;

    if-eqz v4, :cond_0

    .line 473
    iget-object v4, v3, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->methods:Ljava/lang/Object;

    check-cast v4, Lorg/codehaus/groovy/util/FastArray;

    invoke-virtual {v4}, Lorg/codehaus/groovy/util/FastArray;->copy()Lorg/codehaus/groovy/util/FastArray;

    move-result-object v4

    iput-object v4, v3, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->methodsForSuper:Ljava/lang/Object;

    goto :goto_2

    .line 475
    :cond_0
    iget-object v4, v3, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->methods:Ljava/lang/Object;

    iput-object v4, v3, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->methodsForSuper:Ljava/lang/Object;

    .line 471
    :goto_2
    iget-object v3, v3, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->nextHashEntry:Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;

    goto :goto_1

    :cond_1
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_2
    return-void
.end method

.method public copyNonPrivateMethods(Ljava/lang/Class;Ljava/lang/Class;)V
    .locals 0

    .line 256
    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->getHeader(Ljava/lang/Class;)Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;

    move-result-object p1

    invoke-virtual {p0, p2}, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->getHeader(Ljava/lang/Class;)Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;

    move-result-object p2

    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->copyNonPrivateMethods(Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;)V

    return-void
.end method

.method public copyNonPrivateMethods(Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;)V
    .locals 0

    .line 260
    iget-object p1, p1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;->head:Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;

    :goto_0
    if-eqz p1, :cond_0

    .line 261
    invoke-direct {p0, p1, p2}, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->copyNonPrivateMethods(Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;)V

    .line 260
    iget-object p1, p1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->nextClassEntry:Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;

    goto :goto_0

    :cond_0
    return-void
.end method

.method public copyNonPrivateMethodsDown(Ljava/lang/Class;Ljava/lang/Class;)V
    .locals 0

    .line 341
    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->getHeader(Ljava/lang/Class;)Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;

    move-result-object p1

    invoke-virtual {p0, p2}, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->getHeader(Ljava/lang/Class;)Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;

    move-result-object p2

    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->copyNonPrivateNonNewMetaMethods(Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;)V

    return-void
.end method

.method public copyNonPrivateMethodsFromSuper(Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;)V
    .locals 0

    .line 270
    iget-object p1, p1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;->head:Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;

    :goto_0
    if-eqz p1, :cond_0

    .line 271
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->copyNonPrivateMethodsFromSuper(Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;)V

    .line 270
    iget-object p1, p1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->nextClassEntry:Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;

    goto :goto_0

    :cond_0
    return-void
.end method

.method public copyNonPrivateNonNewMetaMethods(Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;)V
    .locals 0

    .line 345
    iget-object p1, p1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;->head:Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;

    :goto_0
    if-eqz p1, :cond_0

    .line 346
    invoke-direct {p0, p1, p2}, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->copyNonPrivateNonNewMetaMethods(Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;)V

    .line 345
    iget-object p1, p1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->nextClassEntry:Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;

    goto :goto_0

    :cond_0
    return-void
.end method

.method public getEntrySetIterator()Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$EntryIterator;
    .locals 1

    .line 166
    new-instance v0, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$1;

    invoke-direct {v0, p0}, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$1;-><init>(Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;)V

    return-object v0
.end method

.method public getHeader(Ljava/lang/Class;)Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;
    .locals 2

    .line 247
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->methodHeaders:Lorg/codehaus/groovy/util/SingleKeyHashMap;

    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/util/SingleKeyHashMap;->getOrPut(Ljava/lang/Object;)Lorg/codehaus/groovy/util/SingleKeyHashMap$Entry;

    move-result-object v0

    .line 248
    iget-object v1, v0, Lorg/codehaus/groovy/util/SingleKeyHashMap$Entry;->value:Ljava/lang/Object;

    if-nez v1, :cond_0

    .line 249
    new-instance v1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;

    invoke-direct {v1, p1}, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;-><init>(Ljava/lang/Class;)V

    iput-object v1, v0, Lorg/codehaus/groovy/util/SingleKeyHashMap$Entry;->value:Ljava/lang/Object;

    .line 251
    :cond_0
    iget-object p1, v0, Lorg/codehaus/groovy/util/SingleKeyHashMap$Entry;->value:Ljava/lang/Object;

    check-cast p1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;

    return-object p1
.end method

.method public final getMethods(Ljava/lang/Class;Ljava/lang/String;)Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;
    .locals 3

    .line 210
    invoke-virtual {p1}, Ljava/lang/Object;->hashCode()I

    move-result v0

    mul-int/lit8 v0, v0, 0x1f

    invoke-virtual {p2}, Ljava/lang/String;->hashCode()I

    move-result v1

    add-int/2addr v0, v1

    invoke-static {v0}, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->hash(I)I

    move-result v0

    .line 211
    iget-object v1, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->table:[Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;

    array-length v2, v1

    add-int/lit8 v2, v2, -0x1

    and-int/2addr v2, v0

    aget-object v1, v1, v2

    :goto_0
    if-eqz v1, :cond_1

    .line 213
    iget v2, v1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->hash:I

    if-ne v2, v0, :cond_0

    iget-object v2, v1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->cls:Ljava/lang/Class;

    if-ne p1, v2, :cond_0

    iget-object v2, v1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->name:Ljava/lang/String;

    invoke-static {v2, p2}, Ljava/util/Objects;->equals(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_0

    return-object v1

    .line 212
    :cond_0
    iget-object v1, v1, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->nextHashEntry:Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;

    goto :goto_0

    :cond_1
    const/4 p1, 0x0

    return-object p1
.end method

.method public getOrPutMethods(Ljava/lang/String;Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;)Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;
    .locals 6

    .line 220
    iget-object v0, p2, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;->cls:Ljava/lang/Class;

    .line 221
    iget v1, p2, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;->clsHashCode31:I

    invoke-virtual {p1}, Ljava/lang/String;->hashCode()I

    move-result v2

    add-int/2addr v1, v2

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->hash(I)I

    move-result v1

    .line 222
    iget-object v2, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->table:[Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;

    .line 223
    array-length v3, v2

    add-int/lit8 v3, v3, -0x1

    and-int/2addr v3, v1

    .line 224
    aget-object v4, v2, v3

    :goto_0
    if-eqz v4, :cond_1

    .line 226
    iget v5, v4, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->hash:I

    if-ne v5, v1, :cond_0

    iget-object v5, v4, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->cls:Ljava/lang/Class;

    if-ne v0, v5, :cond_0

    iget-object v5, v4, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->name:Ljava/lang/String;

    invoke-static {v5, p1}, Ljava/util/Objects;->equals(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v5

    if-eqz v5, :cond_0

    return-object v4

    .line 225
    :cond_0
    iget-object v4, v4, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->nextHashEntry:Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;

    goto :goto_0

    .line 229
    :cond_1
    new-instance v4, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;

    invoke-direct {v4}, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;-><init>()V

    .line 230
    aget-object v5, v2, v3

    iput-object v5, v4, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->nextHashEntry:Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;

    .line 231
    iput v1, v4, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->hash:I

    .line 232
    invoke-virtual {p1}, Ljava/lang/String;->intern()Ljava/lang/String;

    move-result-object p1

    iput-object p1, v4, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->name:Ljava/lang/String;

    .line 233
    iput-object v0, v4, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->cls:Ljava/lang/Class;

    .line 234
    aput-object v4, v2, v3

    .line 236
    iget-object p1, p2, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;->head:Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;

    iput-object p1, v4, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->nextClassEntry:Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;

    .line 237
    iput-object v4, p2, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;->head:Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;

    .line 239
    iget p1, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->size:I

    add-int/lit8 p1, p1, 0x1

    iput p1, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->size:I

    iget p2, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->threshold:I

    if-ne p1, p2, :cond_2

    .line 240
    array-length p1, v2

    mul-int/lit8 p1, p1, 0x2

    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->resize(I)V

    :cond_2
    return-object v4
.end method

.method public getTable()[Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;
    .locals 1

    .line 162
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->table:[Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;

    return-object v0
.end method

.method public init(I)V
    .locals 1

    mul-int/lit8 v0, p1, 0x6

    .line 127
    div-int/lit8 v0, v0, 0x8

    iput v0, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->threshold:I

    .line 128
    new-array p1, p1, [Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;

    iput-object p1, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->table:[Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;

    return-void
.end method

.method public isEmpty()Z
    .locals 1

    .line 117
    iget v0, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->size:I

    if-nez v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public resize(I)V
    .locals 8

    .line 132
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->table:[Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;

    .line 133
    array-length v1, v0

    .line 135
    new-array v2, p1, [Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;

    const/4 v3, 0x0

    :goto_0
    if-ge v3, v1, :cond_1

    .line 139
    aget-object v4, v0, v3

    :goto_1
    if-eqz v4, :cond_0

    .line 140
    iget-object v5, v4, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->nextHashEntry:Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;

    .line 141
    iget v6, v4, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->hash:I

    add-int/lit8 v7, p1, -0x1

    and-int/2addr v6, v7

    .line 143
    aget-object v7, v2, v6

    iput-object v7, v4, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->nextHashEntry:Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;

    .line 144
    aput-object v4, v2, v6

    move-object v4, v5

    goto :goto_1

    :cond_0
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    .line 150
    :cond_1
    iput-object v2, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->table:[Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;

    mul-int/lit8 p1, p1, 0x6

    .line 151
    div-int/lit8 p1, p1, 0x8

    iput p1, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->threshold:I

    return-void
.end method

.method public size()I
    .locals 1

    .line 113
    iget v0, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->size:I

    return v0
.end method
