.class public Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;
.super Ljava/lang/Object;
.source "MetaClassRegistryImpl.java"

# interfaces
.implements Lgroovy/lang/MetaClassRegistry;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl$DefaultModuleListener;
    }
.end annotation


# static fields
.field public static final DONT_LOAD_DEFAULT:I = 0x1

.field private static final EMPTY_METACLASSREGISTRYCHANGEEVENTLISTENER_ARRAY:[Lgroovy/lang/MetaClassRegistryChangeEventListener;

.field private static final EMPTY_METACLASS_ARRAY:[Lgroovy/lang/MetaClass;

.field public static final LOAD_DEFAULT:I = 0x0

.field public static final MODULE_META_INF_FILE:Ljava/lang/String; = "META-INF/services/org.codehaus.groovy.runtime.ExtensionModule"
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end field

.field private static instanceExclude:Lgroovy/lang/MetaClassRegistry;

.field private static instanceInclude:Lgroovy/lang/MetaClassRegistry;


# instance fields
.field private final changeListenerList:Ljava/util/LinkedList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/LinkedList<",
            "Lgroovy/lang/MetaClassRegistryChangeEventListener;",
            ">;"
        }
    .end annotation
.end field

.field private final instanceMethods:Lorg/codehaus/groovy/util/FastArray;

.field private volatile metaClassCreationHandle:Lgroovy/lang/MetaClassRegistry$MetaClassCreationHandle;

.field private final metaClassInfo:Lorg/codehaus/groovy/util/ManagedConcurrentLinkedQueue;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lorg/codehaus/groovy/util/ManagedConcurrentLinkedQueue<",
            "Lgroovy/lang/MetaClass;",
            ">;"
        }
    .end annotation
.end field

.field private final moduleRegistry:Lorg/codehaus/groovy/runtime/m12n/ExtensionModuleRegistry;

.field private final nonRemoveableChangeListenerList:Ljava/util/LinkedList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/LinkedList<",
            "Lgroovy/lang/MetaClassRegistryChangeEventListener;",
            ">;"
        }
    .end annotation
.end field

.field private final staticMethods:Lorg/codehaus/groovy/util/FastArray;

.field private final useAccessible:Z


# direct methods
.method static constructor <clinit>()V
    .locals 2

    const/4 v0, 0x0

    new-array v1, v0, [Lgroovy/lang/MetaClass;

    .line 67
    sput-object v1, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->EMPTY_METACLASS_ARRAY:[Lgroovy/lang/MetaClass;

    new-array v0, v0, [Lgroovy/lang/MetaClassRegistryChangeEventListener;

    .line 68
    sput-object v0, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->EMPTY_METACLASSREGISTRYCHANGEEVENTLISTENER_ARRAY:[Lgroovy/lang/MetaClassRegistryChangeEventListener;

    return-void
.end method

.method public constructor <init>()V
    .locals 2

    const/4 v0, 0x0

    const/4 v1, 0x1

    .line 86
    invoke-direct {p0, v0, v1}, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;-><init>(IZ)V

    return-void
.end method

.method public constructor <init>(I)V
    .locals 1

    const/4 v0, 0x1

    .line 90
    invoke-direct {p0, p1, v0}, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;-><init>(IZ)V

    return-void
.end method

.method public constructor <init>(IZ)V
    .locals 5

    .line 101
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 72
    new-instance v0, Lorg/codehaus/groovy/util/FastArray;

    invoke-direct {v0}, Lorg/codehaus/groovy/util/FastArray;-><init>()V

    iput-object v0, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->instanceMethods:Lorg/codehaus/groovy/util/FastArray;

    .line 73
    new-instance v0, Lorg/codehaus/groovy/util/FastArray;

    invoke-direct {v0}, Lorg/codehaus/groovy/util/FastArray;-><init>()V

    iput-object v0, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->staticMethods:Lorg/codehaus/groovy/util/FastArray;

    .line 75
    new-instance v0, Ljava/util/LinkedList;

    invoke-direct {v0}, Ljava/util/LinkedList;-><init>()V

    iput-object v0, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->changeListenerList:Ljava/util/LinkedList;

    .line 76
    new-instance v0, Ljava/util/LinkedList;

    invoke-direct {v0}, Ljava/util/LinkedList;-><init>()V

    iput-object v0, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->nonRemoveableChangeListenerList:Ljava/util/LinkedList;

    .line 77
    new-instance v0, Lorg/codehaus/groovy/util/ManagedConcurrentLinkedQueue;

    invoke-static {}, Lorg/codehaus/groovy/util/ReferenceBundle;->getWeakBundle()Lorg/codehaus/groovy/util/ReferenceBundle;

    move-result-object v1

    invoke-direct {v0, v1}, Lorg/codehaus/groovy/util/ManagedConcurrentLinkedQueue;-><init>(Lorg/codehaus/groovy/util/ReferenceBundle;)V

    iput-object v0, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->metaClassInfo:Lorg/codehaus/groovy/util/ManagedConcurrentLinkedQueue;

    .line 78
    new-instance v0, Lorg/codehaus/groovy/runtime/m12n/ExtensionModuleRegistry;

    invoke-direct {v0}, Lorg/codehaus/groovy/runtime/m12n/ExtensionModuleRegistry;-><init>()V

    iput-object v0, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->moduleRegistry:Lorg/codehaus/groovy/runtime/m12n/ExtensionModuleRegistry;

    .line 322
    new-instance v0, Lgroovy/lang/MetaClassRegistry$MetaClassCreationHandle;

    invoke-direct {v0}, Lgroovy/lang/MetaClassRegistry$MetaClassCreationHandle;-><init>()V

    iput-object v0, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->metaClassCreationHandle:Lgroovy/lang/MetaClassRegistry$MetaClassCreationHandle;

    .line 102
    iput-boolean p2, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->useAccessible:Z

    if-nez p1, :cond_3

    .line 105
    new-instance p1, Ljava/util/HashMap;

    invoke-direct {p1}, Ljava/util/HashMap;-><init>()V

    const/4 p2, 0x0

    const/4 v0, 0x1

    .line 108
    invoke-direct {p0, p2, v0, v0, p1}, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->registerMethods(Ljava/lang/Class;ZZLjava/util/Map;)V

    .line 109
    sget-object p2, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->ADDITIONAL_CLASSES:[Ljava/lang/Class;

    const/4 v1, 0x0

    move v2, v1

    .line 110
    :goto_0
    array-length v3, p2

    if-eq v2, v3, :cond_0

    .line 111
    aget-object v3, p2, v2

    invoke-direct {p0, p1, v3}, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->createMetaMethodFromClass(Ljava/util/Map;Ljava/lang/Class;)V

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 114
    :cond_0
    invoke-static {}, Lorg/codehaus/groovy/vmplugin/VMPluginFactory;->getPlugin()Lorg/codehaus/groovy/vmplugin/VMPlugin;

    move-result-object p2

    invoke-interface {p2}, Lorg/codehaus/groovy/vmplugin/VMPlugin;->getPluginDefaultGroovyMethods()[Ljava/lang/Class;

    move-result-object p2

    .line 115
    array-length v2, p2

    move v3, v1

    :goto_1
    if-ge v3, v2, :cond_1

    aget-object v4, p2, v3

    .line 116
    invoke-direct {p0, v4, v1, v0, p1}, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->registerMethods(Ljava/lang/Class;ZZLjava/util/Map;)V

    add-int/lit8 v3, v3, 0x1

    goto :goto_1

    .line 118
    :cond_1
    const-class p2, Lorg/codehaus/groovy/runtime/DefaultGroovyStaticMethods;

    invoke-direct {p0, p2, v1, v1, p1}, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->registerMethods(Ljava/lang/Class;ZZLjava/util/Map;)V

    .line 119
    invoke-static {}, Lorg/codehaus/groovy/vmplugin/VMPluginFactory;->getPlugin()Lorg/codehaus/groovy/vmplugin/VMPlugin;

    move-result-object p2

    invoke-interface {p2}, Lorg/codehaus/groovy/vmplugin/VMPlugin;->getPluginStaticGroovyMethods()[Ljava/lang/Class;

    move-result-object p2

    .line 120
    array-length v0, p2

    move v2, v1

    :goto_2
    if-ge v2, v0, :cond_2

    aget-object v3, p2, v2

    .line 121
    invoke-direct {p0, v3, v1, v1, p1}, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->registerMethods(Ljava/lang/Class;ZZLjava/util/Map;)V

    add-int/lit8 v2, v2, 0x1

    goto :goto_2

    .line 124
    :cond_2
    new-instance p2, Lorg/codehaus/groovy/runtime/m12n/ExtensionModuleScanner;

    new-instance v0, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl$DefaultModuleListener;

    invoke-direct {v0, p0, p1}, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl$DefaultModuleListener;-><init>(Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;Ljava/util/Map;)V

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/Class;->getClassLoader()Ljava/lang/ClassLoader;

    move-result-object v1

    invoke-direct {p2, v0, v1}, Lorg/codehaus/groovy/runtime/m12n/ExtensionModuleScanner;-><init>(Lorg/codehaus/groovy/runtime/m12n/ExtensionModuleScanner$ExtensionModuleListener;Ljava/lang/ClassLoader;)V

    .line 125
    invoke-virtual {p2}, Lorg/codehaus/groovy/runtime/m12n/ExtensionModuleScanner;->scanClasspathModules()V

    .line 127
    invoke-static {p1}, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->refreshMopMethods(Ljava/util/Map;)V

    .line 131
    :cond_3
    invoke-direct {p0}, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->installMetaClassCreationHandle()V

    .line 133
    iget-object p1, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->metaClassCreationHandle:Lgroovy/lang/MetaClassRegistry$MetaClassCreationHandle;

    const-class p2, Lgroovy/lang/ExpandoMetaClass;

    invoke-virtual {p1, p2, p0}, Lgroovy/lang/MetaClassRegistry$MetaClassCreationHandle;->create(Ljava/lang/Class;Lgroovy/lang/MetaClassRegistry;)Lgroovy/lang/MetaClass;

    move-result-object p1

    .line 134
    invoke-interface {p1}, Lgroovy/lang/MetaClass;->initialize()V

    .line 135
    const-class p2, Lgroovy/lang/ExpandoMetaClass;

    invoke-static {p2}, Lorg/codehaus/groovy/reflection/ClassInfo;->getClassInfo(Ljava/lang/Class;)Lorg/codehaus/groovy/reflection/ClassInfo;

    move-result-object p2

    invoke-virtual {p2, p1}, Lorg/codehaus/groovy/reflection/ClassInfo;->setStrongMetaClass(Lgroovy/lang/MetaClass;)V

    .line 138
    new-instance p1, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl$$ExternalSyntheticLambda0;

    invoke-direct {p1, p0}, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl$$ExternalSyntheticLambda0;-><init>(Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;)V

    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->addNonRemovableMetaClassRegistryChangeEventListener(Lgroovy/lang/MetaClassRegistryChangeEventListener;)V

    return-void
.end method

.method public constructor <init>(Z)V
    .locals 1

    const/4 v0, 0x0

    .line 98
    invoke-direct {p0, v0, p1}, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;-><init>(IZ)V

    return-void
.end method

.method static synthetic access$000(Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;Ljava/lang/Class;Lgroovy/lang/MetaClass;Lgroovy/lang/MetaClass;)V
    .locals 0

    .line 61
    invoke-direct {p0, p1, p2, p3}, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->setMetaClass(Ljava/lang/Class;Lgroovy/lang/MetaClass;Lgroovy/lang/MetaClass;)V

    return-void
.end method

.method static synthetic access$100(Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;)Lorg/codehaus/groovy/runtime/m12n/ExtensionModuleRegistry;
    .locals 0

    .line 61
    iget-object p0, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->moduleRegistry:Lorg/codehaus/groovy/runtime/m12n/ExtensionModuleRegistry;

    return-object p0
.end method

.method static synthetic access$200(Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;)Lorg/codehaus/groovy/util/FastArray;
    .locals 0

    .line 61
    iget-object p0, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->staticMethods:Lorg/codehaus/groovy/util/FastArray;

    return-object p0
.end method

.method static synthetic access$300(Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;)Lorg/codehaus/groovy/util/FastArray;
    .locals 0

    .line 61
    iget-object p0, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->instanceMethods:Lorg/codehaus/groovy/util/FastArray;

    return-object p0
.end method

.method private createMetaMethodFromClass(Ljava/util/Map;Ljava/lang/Class;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Lorg/codehaus/groovy/reflection/CachedClass;",
            "Ljava/util/List<",
            "Lgroovy/lang/MetaMethod;",
            ">;>;",
            "Ljava/lang/Class;",
            ")V"
        }
    .end annotation

    const/4 v0, 0x0

    :try_start_0
    new-array v1, v0, [Ljava/lang/Class;

    .line 248
    invoke-virtual {p2, v1}, Ljava/lang/Class;->getDeclaredConstructor([Ljava/lang/Class;)Ljava/lang/reflect/Constructor;

    move-result-object p2

    new-array v0, v0, [Ljava/lang/Object;

    invoke-virtual {p2, v0}, Ljava/lang/reflect/Constructor;->newInstance([Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lgroovy/lang/MetaMethod;

    .line 249
    invoke-virtual {p2}, Lgroovy/lang/MetaMethod;->getDeclaringClass()Lorg/codehaus/groovy/reflection/CachedClass;

    move-result-object v0

    .line 250
    sget-object v1, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl$$ExternalSyntheticLambda1;->INSTANCE:Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl$$ExternalSyntheticLambda1;

    invoke-interface {p1, v0, v1}, Ljava/util/Map;->computeIfAbsent(Ljava/lang/Object;Ljava/util/function/Function;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/util/List;

    .line 251
    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 252
    iget-object p1, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->instanceMethods:Lorg/codehaus/groovy/util/FastArray;

    invoke-virtual {p1, p2}, Lorg/codehaus/groovy/util/FastArray;->add(Ljava/lang/Object;)V
    :try_end_0
    .catch Ljava/lang/InstantiationException; {:try_start_0 .. :try_end_0} :catch_0
    .catch Ljava/lang/IllegalAccessException; {:try_start_0 .. :try_end_0} :catch_0
    .catch Ljava/lang/NoSuchMethodException; {:try_start_0 .. :try_end_0} :catch_0
    .catch Ljava/lang/reflect/InvocationTargetException; {:try_start_0 .. :try_end_0} :catch_0

    :catch_0
    return-void
.end method

.method public static declared-synchronized getInstance(I)Lgroovy/lang/MetaClassRegistry;
    .locals 2

    const-class v0, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;

    monitor-enter v0

    const/4 v1, 0x1

    if-eq p0, v1, :cond_1

    .line 418
    :try_start_0
    sget-object p0, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->instanceInclude:Lgroovy/lang/MetaClassRegistry;

    if-nez p0, :cond_0

    .line 419
    new-instance p0, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;

    invoke-direct {p0}, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;-><init>()V

    sput-object p0, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->instanceInclude:Lgroovy/lang/MetaClassRegistry;

    .line 421
    :cond_0
    sget-object p0, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->instanceInclude:Lgroovy/lang/MetaClassRegistry;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit v0

    return-object p0

    .line 423
    :cond_1
    :try_start_1
    sget-object p0, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->instanceExclude:Lgroovy/lang/MetaClassRegistry;

    if-nez p0, :cond_2

    .line 424
    new-instance p0, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;

    invoke-direct {p0, v1}, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;-><init>(I)V

    sput-object p0, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->instanceExclude:Lgroovy/lang/MetaClassRegistry;

    .line 426
    :cond_2
    sget-object p0, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->instanceExclude:Lgroovy/lang/MetaClassRegistry;
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    monitor-exit v0

    return-object p0

    :catchall_0
    move-exception p0

    monitor-exit v0

    throw p0
.end method

.method private installMetaClassCreationHandle()V
    .locals 4

    :try_start_0
    const-string v0, "groovy.runtime.metaclass.CustomMetaClassCreationHandle"

    .line 183
    invoke-static {v0}, Ljava/lang/Class;->forName(Ljava/lang/String;)Ljava/lang/Class;

    move-result-object v0

    const/4 v1, 0x0

    new-array v2, v1, [Ljava/lang/Class;

    .line 184
    invoke-virtual {v0, v2}, Ljava/lang/Class;->getConstructor([Ljava/lang/Class;)Ljava/lang/reflect/Constructor;

    move-result-object v0

    new-array v1, v1, [Ljava/lang/Object;

    .line 185
    invoke-virtual {v0, v1}, Ljava/lang/reflect/Constructor;->newInstance([Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovy/lang/MetaClassRegistry$MetaClassCreationHandle;

    iput-object v0, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->metaClassCreationHandle:Lgroovy/lang/MetaClassRegistry$MetaClassCreationHandle;
    :try_end_0
    .catch Ljava/lang/ClassNotFoundException; {:try_start_0 .. :try_end_0} :catch_1
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception v0

    .line 189
    new-instance v1, Lgroovy/lang/GroovyRuntimeException;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "Could not instantiate custom Metaclass creation handle: "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-direct {v1, v2, v0}, Lgroovy/lang/GroovyRuntimeException;-><init>(Ljava/lang/String;Ljava/lang/Throwable;)V

    throw v1

    .line 187
    :catch_1
    new-instance v0, Lgroovy/lang/MetaClassRegistry$MetaClassCreationHandle;

    invoke-direct {v0}, Lgroovy/lang/MetaClassRegistry$MetaClassCreationHandle;-><init>()V

    iput-object v0, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->metaClassCreationHandle:Lgroovy/lang/MetaClassRegistry$MetaClassCreationHandle;

    :goto_0
    return-void
.end method

.method static synthetic lambda$createMetaMethodFromClass$3(Lorg/codehaus/groovy/reflection/CachedClass;)Ljava/util/List;
    .locals 1

    .line 250
    new-instance p0, Ljava/util/ArrayList;

    const/4 v0, 0x4

    invoke-direct {p0, v0}, Ljava/util/ArrayList;-><init>(I)V

    return-object p0
.end method

.method static synthetic lambda$registerMethods$1(Lorg/codehaus/groovy/reflection/CachedClass;)Ljava/util/List;
    .locals 1

    .line 213
    new-instance p0, Ljava/util/ArrayList;

    const/4 v0, 0x4

    invoke-direct {p0, v0}, Ljava/util/ArrayList;-><init>(I)V

    return-object p0
.end method

.method static synthetic lambda$registerMethods$2(Lorg/codehaus/groovy/reflection/CachedClass;)Ljava/util/List;
    .locals 1

    .line 230
    new-instance p0, Ljava/util/ArrayList;

    const/4 v0, 0x4

    invoke-direct {p0, v0}, Ljava/util/ArrayList;-><init>(I)V

    return-object p0
.end method

.method private static refreshMopMethods(Ljava/util/Map;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Lorg/codehaus/groovy/reflection/CachedClass;",
            "Ljava/util/List<",
            "Lgroovy/lang/MetaMethod;",
            ">;>;)V"
        }
    .end annotation

    .line 160
    invoke-interface {p0}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    move-result-object p0

    invoke-interface {p0}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object p0

    :goto_0
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/Map$Entry;

    .line 161
    invoke-interface {v0}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/reflection/CachedClass;

    .line 162
    invoke-interface {v0}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/List;

    invoke-virtual {v1, v0}, Lorg/codehaus/groovy/reflection/CachedClass;->setNewMopMethods(Ljava/util/List;)V

    goto :goto_0

    :cond_0
    return-void
.end method

.method private registerMethods(Ljava/lang/Class;ZZLjava/util/Map;)V
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Class;",
            "ZZ",
            "Ljava/util/Map<",
            "Lorg/codehaus/groovy/reflection/CachedClass;",
            "Ljava/util/List<",
            "Lgroovy/lang/MetaMethod;",
            ">;>;)V"
        }
    .end annotation

    const/4 v0, 0x0

    if-eqz p2, :cond_0

    .line 199
    :try_start_0
    invoke-static {}, Lorg/codehaus/groovy/reflection/GeneratedMetaMethod$DgmMethodRecord;->loadDgmInfo()Ljava/util/List;

    move-result-object p1

    .line 201
    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result p2

    if-eqz p2, :cond_3

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lorg/codehaus/groovy/reflection/GeneratedMetaMethod$DgmMethodRecord;

    .line 202
    iget-object p3, p2, Lorg/codehaus/groovy/reflection/GeneratedMetaMethod$DgmMethodRecord;->parameters:[Ljava/lang/Class;

    array-length p3, p3

    const/4 v1, 0x1

    sub-int/2addr p3, v1

    new-array v7, p3, [Ljava/lang/Class;

    .line 203
    iget-object v2, p2, Lorg/codehaus/groovy/reflection/GeneratedMetaMethod$DgmMethodRecord;->parameters:[Ljava/lang/Class;

    invoke-static {v2, v1, v7, v0, p3}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    .line 205
    new-instance p3, Lorg/codehaus/groovy/reflection/GeneratedMetaMethod$Proxy;

    iget-object v3, p2, Lorg/codehaus/groovy/reflection/GeneratedMetaMethod$DgmMethodRecord;->className:Ljava/lang/String;

    iget-object v4, p2, Lorg/codehaus/groovy/reflection/GeneratedMetaMethod$DgmMethodRecord;->methodName:Ljava/lang/String;

    iget-object v1, p2, Lorg/codehaus/groovy/reflection/GeneratedMetaMethod$DgmMethodRecord;->parameters:[Ljava/lang/Class;

    aget-object v1, v1, v0

    .line 208
    invoke-static {v1}, Lorg/codehaus/groovy/reflection/ReflectionCache;->getCachedClass(Ljava/lang/Class;)Lorg/codehaus/groovy/reflection/CachedClass;

    move-result-object v5

    iget-object v6, p2, Lorg/codehaus/groovy/reflection/GeneratedMetaMethod$DgmMethodRecord;->returnType:Ljava/lang/Class;

    move-object v2, p3

    invoke-direct/range {v2 .. v7}, Lorg/codehaus/groovy/reflection/GeneratedMetaMethod$Proxy;-><init>(Ljava/lang/String;Ljava/lang/String;Lorg/codehaus/groovy/reflection/CachedClass;Ljava/lang/Class;[Ljava/lang/Class;)V

    .line 212
    invoke-virtual {p3}, Lgroovy/lang/MetaMethod;->getDeclaringClass()Lorg/codehaus/groovy/reflection/CachedClass;

    move-result-object p2

    .line 213
    sget-object v1, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl$$ExternalSyntheticLambda2;->INSTANCE:Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl$$ExternalSyntheticLambda2;

    invoke-interface {p4, p2, v1}, Ljava/util/Map;->computeIfAbsent(Ljava/lang/Object;Ljava/util/function/Function;)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Ljava/util/List;

    .line 214
    invoke-interface {p2, p3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 215
    iget-object p2, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->instanceMethods:Lorg/codehaus/groovy/util/FastArray;

    invoke-virtual {p2, p3}, Lorg/codehaus/groovy/util/FastArray;->add(Ljava/lang/Object;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_0

    :catchall_0
    move-exception p1

    .line 218
    invoke-virtual {p1}, Ljava/lang/Throwable;->printStackTrace()V

    goto :goto_3

    .line 223
    :cond_0
    invoke-static {p1}, Lorg/codehaus/groovy/reflection/ReflectionCache;->getCachedClass(Ljava/lang/Class;)Lorg/codehaus/groovy/reflection/CachedClass;

    move-result-object p1

    invoke-virtual {p1}, Lorg/codehaus/groovy/reflection/CachedClass;->getMethods()[Lorg/codehaus/groovy/reflection/CachedMethod;

    move-result-object p1

    .line 225
    array-length p2, p1

    move v1, v0

    :goto_1
    if-ge v1, p2, :cond_3

    aget-object v2, p1, v1

    .line 226
    invoke-virtual {v2}, Lorg/codehaus/groovy/reflection/CachedMethod;->getModifiers()I

    move-result v3

    .line 227
    invoke-static {v3}, Ljava/lang/reflect/Modifier;->isStatic(I)Z

    move-result v4

    if-eqz v4, :cond_2

    invoke-static {v3}, Ljava/lang/reflect/Modifier;->isPublic(I)Z

    move-result v3

    if-eqz v3, :cond_2

    const-class v3, Ljava/lang/Deprecated;

    invoke-virtual {v2, v3}, Lorg/codehaus/groovy/reflection/CachedMethod;->getAnnotation(Ljava/lang/Class;)Ljava/lang/annotation/Annotation;

    move-result-object v3

    if-nez v3, :cond_2

    .line 228
    invoke-virtual {v2}, Lorg/codehaus/groovy/reflection/CachedMethod;->getParameterTypes()[Lorg/codehaus/groovy/reflection/CachedClass;

    move-result-object v3

    .line 229
    array-length v4, v3

    if-lez v4, :cond_2

    .line 230
    aget-object v3, v3, v0

    sget-object v4, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl$$ExternalSyntheticLambda3;->INSTANCE:Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl$$ExternalSyntheticLambda3;

    invoke-interface {p4, v3, v4}, Ljava/util/Map;->computeIfAbsent(Ljava/lang/Object;Ljava/util/function/Function;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/util/List;

    if-eqz p3, :cond_1

    .line 232
    new-instance v4, Lorg/codehaus/groovy/runtime/metaclass/NewInstanceMetaMethod;

    invoke-direct {v4, v2}, Lorg/codehaus/groovy/runtime/metaclass/NewInstanceMetaMethod;-><init>(Lorg/codehaus/groovy/reflection/CachedMethod;)V

    .line 233
    invoke-interface {v3, v4}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 234
    iget-object v2, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->instanceMethods:Lorg/codehaus/groovy/util/FastArray;

    invoke-virtual {v2, v4}, Lorg/codehaus/groovy/util/FastArray;->add(Ljava/lang/Object;)V

    goto :goto_2

    .line 236
    :cond_1
    new-instance v4, Lorg/codehaus/groovy/runtime/metaclass/NewStaticMetaMethod;

    invoke-direct {v4, v2}, Lorg/codehaus/groovy/runtime/metaclass/NewStaticMetaMethod;-><init>(Lorg/codehaus/groovy/reflection/CachedMethod;)V

    .line 237
    invoke-interface {v3, v4}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 238
    iget-object v2, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->staticMethods:Lorg/codehaus/groovy/util/FastArray;

    invoke-virtual {v2, v4}, Lorg/codehaus/groovy/util/FastArray;->add(Ljava/lang/Object;)V

    :cond_2
    :goto_2
    add-int/lit8 v1, v1, 0x1

    goto :goto_1

    :cond_3
    :goto_3
    return-void
.end method

.method private setMetaClass(Ljava/lang/Class;Lgroovy/lang/MetaClass;Lgroovy/lang/MetaClass;)V
    .locals 2

    .line 271
    invoke-static {p1}, Lorg/codehaus/groovy/reflection/ClassInfo;->getClassInfo(Ljava/lang/Class;)Lorg/codehaus/groovy/reflection/ClassInfo;

    move-result-object v0

    .line 274
    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->lock()V

    .line 276
    :try_start_0
    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getStrongMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v1

    .line 277
    invoke-virtual {v0, p3}, Lorg/codehaus/groovy/reflection/ClassInfo;->setStrongMetaClass(Lgroovy/lang/MetaClass;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 279
    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->unlock()V

    if-nez p2, :cond_0

    if-ne v1, p3, :cond_1

    :cond_0
    if-eqz p2, :cond_2

    if-eq v1, p3, :cond_2

    if-eq v1, p2, :cond_2

    :cond_1
    const/4 p2, 0x0

    .line 282
    invoke-virtual {p0, p2, p1, v1, p3}, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->fireConstantMetaClassUpdate(Ljava/lang/Object;Ljava/lang/Class;Lgroovy/lang/MetaClass;Lgroovy/lang/MetaClass;)V

    :cond_2
    return-void

    :catchall_0
    move-exception p1

    .line 279
    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->unlock()V

    .line 280
    throw p1
.end method


# virtual methods
.method public addMetaClassRegistryChangeEventListener(Lgroovy/lang/MetaClassRegistryChangeEventListener;)V
    .locals 2

    .line 353
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->changeListenerList:Ljava/util/LinkedList;

    monitor-enter v0

    .line 354
    :try_start_0
    iget-object v1, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->changeListenerList:Ljava/util/LinkedList;

    invoke-virtual {v1, p1}, Ljava/util/LinkedList;->add(Ljava/lang/Object;)Z

    .line 355
    monitor-exit v0

    return-void

    :catchall_0
    move-exception p1

    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw p1
.end method

.method public addNonRemovableMetaClassRegistryChangeEventListener(Lgroovy/lang/MetaClassRegistryChangeEventListener;)V
    .locals 2

    .line 364
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->changeListenerList:Ljava/util/LinkedList;

    monitor-enter v0

    .line 365
    :try_start_0
    iget-object v1, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->nonRemoveableChangeListenerList:Ljava/util/LinkedList;

    invoke-virtual {v1, p1}, Ljava/util/LinkedList;->add(Ljava/lang/Object;)Z

    .line 366
    monitor-exit v0

    return-void

    :catchall_0
    move-exception p1

    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw p1
.end method

.method protected fireConstantMetaClassUpdate(Ljava/lang/Object;Ljava/lang/Class;Lgroovy/lang/MetaClass;Lgroovy/lang/MetaClass;)V
    .locals 8

    .line 390
    invoke-virtual {p0}, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->getMetaClassRegistryChangeEventListeners()[Lgroovy/lang/MetaClassRegistryChangeEventListener;

    move-result-object v0

    .line 391
    new-instance v7, Lgroovy/lang/MetaClassRegistryChangeEvent;

    move-object v1, v7

    move-object v2, p0

    move-object v3, p1

    move-object v4, p2

    move-object v5, p3

    move-object v6, p4

    invoke-direct/range {v1 .. v6}, Lgroovy/lang/MetaClassRegistryChangeEvent;-><init>(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Class;Lgroovy/lang/MetaClass;Lgroovy/lang/MetaClass;)V

    .line 392
    array-length p1, v0

    const/4 p2, 0x0

    :goto_0
    if-ge p2, p1, :cond_0

    aget-object p3, v0, p2

    .line 393
    invoke-interface {p3, v7}, Lgroovy/lang/MetaClassRegistryChangeEventListener;->updateConstantMetaClass(Lgroovy/lang/MetaClassRegistryChangeEvent;)V

    add-int/lit8 p2, p2, 0x1

    goto :goto_0

    :cond_0
    return-void
.end method

.method public getInstanceMethods()Lorg/codehaus/groovy/util/FastArray;
    .locals 1

    .line 431
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->instanceMethods:Lorg/codehaus/groovy/util/FastArray;

    return-object v0
.end method

.method public final getMetaClass(Ljava/lang/Class;)Lgroovy/lang/MetaClass;
    .locals 0

    .line 258
    invoke-static {p1}, Lorg/codehaus/groovy/reflection/ClassInfo;->getClassInfo(Ljava/lang/Class;)Lorg/codehaus/groovy/reflection/ClassInfo;

    move-result-object p1

    invoke-virtual {p1}, Lorg/codehaus/groovy/reflection/ClassInfo;->getMetaClass()Lgroovy/lang/MetaClass;

    move-result-object p1

    return-object p1
.end method

.method public getMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;
    .locals 1

    .line 262
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getClassInfo(Ljava/lang/Class;)Lorg/codehaus/groovy/reflection/ClassInfo;

    move-result-object v0

    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/reflection/ClassInfo;->getMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object p1

    return-object p1
.end method

.method public getMetaClassCreationHandler()Lgroovy/lang/MetaClassRegistry$MetaClassCreationHandle;
    .locals 1

    .line 330
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->metaClassCreationHandle:Lgroovy/lang/MetaClassRegistry$MetaClassCreationHandle;

    return-object v0
.end method

.method public getMetaClassRegistryChangeEventListeners()[Lgroovy/lang/MetaClassRegistryChangeEventListener;
    .locals 4

    .line 401
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->changeListenerList:Ljava/util/LinkedList;

    monitor-enter v0

    .line 402
    :try_start_0
    new-instance v1, Ljava/util/ArrayList;

    iget-object v2, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->changeListenerList:Ljava/util/LinkedList;

    .line 403
    invoke-virtual {v2}, Ljava/util/LinkedList;->size()I

    move-result v2

    iget-object v3, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->nonRemoveableChangeListenerList:Ljava/util/LinkedList;

    invoke-virtual {v3}, Ljava/util/LinkedList;->size()I

    move-result v3

    add-int/2addr v2, v3

    invoke-direct {v1, v2}, Ljava/util/ArrayList;-><init>(I)V

    .line 404
    iget-object v2, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->nonRemoveableChangeListenerList:Ljava/util/LinkedList;

    invoke-virtual {v1, v2}, Ljava/util/ArrayList;->addAll(Ljava/util/Collection;)Z

    .line 405
    iget-object v2, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->changeListenerList:Ljava/util/LinkedList;

    invoke-virtual {v1, v2}, Ljava/util/ArrayList;->addAll(Ljava/util/Collection;)Z

    .line 406
    sget-object v2, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->EMPTY_METACLASSREGISTRYCHANGEEVENTLISTENER_ARRAY:[Lgroovy/lang/MetaClassRegistryChangeEventListener;

    invoke-virtual {v1, v2}, Ljava/util/ArrayList;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object v1

    check-cast v1, [Lgroovy/lang/MetaClassRegistryChangeEventListener;

    monitor-exit v0

    return-object v1

    :catchall_0
    move-exception v1

    .line 407
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw v1
.end method

.method public getModuleRegistry()Lorg/codehaus/groovy/runtime/m12n/ExtensionModuleRegistry;
    .locals 1

    .line 172
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->moduleRegistry:Lorg/codehaus/groovy/runtime/m12n/ExtensionModuleRegistry;

    return-object v0
.end method

.method public getStaticMethods()Lorg/codehaus/groovy/util/FastArray;
    .locals 1

    .line 435
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->staticMethods:Lorg/codehaus/groovy/util/FastArray;

    return-object v0
.end method

.method public iterator()Ljava/util/Iterator;
    .locals 2

    .line 450
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->metaClassInfo:Lorg/codehaus/groovy/util/ManagedConcurrentLinkedQueue;

    sget-object v1, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->EMPTY_METACLASS_ARRAY:[Lgroovy/lang/MetaClass;

    invoke-virtual {v0, v1}, Lorg/codehaus/groovy/util/ManagedConcurrentLinkedQueue;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lgroovy/lang/MetaClass;

    .line 452
    new-instance v1, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl$1;

    invoke-direct {v1, p0, v0}, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl$1;-><init>(Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;[Lgroovy/lang/MetaClass;)V

    return-object v1
.end method

.method public synthetic lambda$new$0$org-codehaus-groovy-runtime-metaclass-MetaClassRegistryImpl(Lgroovy/lang/MetaClassRegistryChangeEvent;)V
    .locals 5

    .line 142
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->metaClassInfo:Lorg/codehaus/groovy/util/ManagedConcurrentLinkedQueue;

    monitor-enter v0

    .line 143
    :try_start_0
    iget-object v1, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->metaClassInfo:Lorg/codehaus/groovy/util/ManagedConcurrentLinkedQueue;

    invoke-virtual {p1}, Lgroovy/lang/MetaClassRegistryChangeEvent;->getNewMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v2

    invoke-virtual {v1, v2}, Lorg/codehaus/groovy/util/ManagedConcurrentLinkedQueue;->add(Ljava/lang/Object;)V

    .line 144
    invoke-static {}, Lorg/codehaus/groovy/runtime/metaclass/DefaultMetaClassInfo;->getNewConstantMetaClassVersioning()Lorg/codehaus/groovy/runtime/metaclass/DefaultMetaClassInfo$ConstantMetaClassVersioning;

    .line 145
    invoke-virtual {p1}, Lgroovy/lang/MetaClassRegistryChangeEvent;->getClassToUpdate()Ljava/lang/Class;

    move-result-object v1

    .line 146
    invoke-virtual {p1}, Lgroovy/lang/MetaClassRegistryChangeEvent;->getNewMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v2

    const/4 v3, 0x1

    const/4 v4, 0x0

    if-nez v2, :cond_0

    move v2, v3

    goto :goto_0

    :cond_0
    move v2, v4

    :goto_0
    invoke-static {v1, v2}, Lorg/codehaus/groovy/runtime/metaclass/DefaultMetaClassInfo;->setPrimitiveMeta(Ljava/lang/Class;Z)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_1

    :try_start_1
    const-string v2, "__$stMC"

    .line 149
    invoke-virtual {v1, v2}, Ljava/lang/Class;->getDeclaredField(Ljava/lang/String;)Ljava/lang/reflect/Field;

    move-result-object v1

    const/4 v2, 0x0

    .line 150
    invoke-virtual {p1}, Lgroovy/lang/MetaClassRegistryChangeEvent;->getNewMetaClass()Lgroovy/lang/MetaClass;

    move-result-object p1

    if-eqz p1, :cond_1

    goto :goto_1

    :cond_1
    move v3, v4

    :goto_1
    invoke-virtual {v1, v2, v3}, Ljava/lang/reflect/Field;->setBoolean(Ljava/lang/Object;Z)V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 155
    :catchall_0
    :try_start_2
    monitor-exit v0

    return-void

    :catchall_1
    move-exception p1

    monitor-exit v0
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    throw p1
.end method

.method public registerExtensionModuleFromProperties(Ljava/util/Properties;Ljava/lang/ClassLoader;Ljava/util/Map;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Properties;",
            "Ljava/lang/ClassLoader;",
            "Ljava/util/Map<",
            "Lorg/codehaus/groovy/reflection/CachedClass;",
            "Ljava/util/List<",
            "Lgroovy/lang/MetaMethod;",
            ">;>;)V"
        }
    .end annotation

    .line 167
    new-instance v0, Lorg/codehaus/groovy/runtime/m12n/ExtensionModuleScanner;

    new-instance v1, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl$DefaultModuleListener;

    invoke-direct {v1, p0, p3}, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl$DefaultModuleListener;-><init>(Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;Ljava/util/Map;)V

    invoke-direct {v0, v1, p2}, Lorg/codehaus/groovy/runtime/m12n/ExtensionModuleScanner;-><init>(Lorg/codehaus/groovy/runtime/m12n/ExtensionModuleScanner$ExtensionModuleListener;Ljava/lang/ClassLoader;)V

    .line 168
    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/runtime/m12n/ExtensionModuleScanner;->scanExtensionModuleFromProperties(Ljava/util/Properties;)V

    return-void
.end method

.method public removeMetaClass(Ljava/lang/Class;)V
    .locals 1

    const/4 v0, 0x0

    .line 287
    invoke-direct {p0, p1, v0, v0}, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->setMetaClass(Ljava/lang/Class;Lgroovy/lang/MetaClass;Lgroovy/lang/MetaClass;)V

    return-void
.end method

.method public removeMetaClassRegistryChangeEventListener(Lgroovy/lang/MetaClassRegistryChangeEventListener;)V
    .locals 2

    .line 374
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->changeListenerList:Ljava/util/LinkedList;

    monitor-enter v0

    .line 375
    :try_start_0
    iget-object v1, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->changeListenerList:Ljava/util/LinkedList;

    invoke-virtual {v1, p1}, Ljava/util/LinkedList;->remove(Ljava/lang/Object;)Z

    .line 376
    monitor-exit v0

    return-void

    :catchall_0
    move-exception p1

    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw p1
.end method

.method public setMetaClass(Ljava/lang/Class;Lgroovy/lang/MetaClass;)V
    .locals 1

    const/4 v0, 0x0

    .line 297
    invoke-direct {p0, p1, v0, p2}, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->setMetaClass(Ljava/lang/Class;Lgroovy/lang/MetaClass;Lgroovy/lang/MetaClass;)V

    return-void
.end method

.method public setMetaClass(Ljava/lang/Object;Lgroovy/lang/MetaClass;)V
    .locals 3

    .line 302
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    .line 303
    invoke-static {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getClassInfo(Ljava/lang/Class;)Lorg/codehaus/groovy/reflection/ClassInfo;

    move-result-object v1

    .line 305
    invoke-virtual {v1}, Lorg/codehaus/groovy/reflection/ClassInfo;->lock()V

    .line 307
    :try_start_0
    invoke-virtual {v1, p1}, Lorg/codehaus/groovy/reflection/ClassInfo;->getPerInstanceMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v2

    .line 308
    invoke-virtual {v1, p1, p2}, Lorg/codehaus/groovy/reflection/ClassInfo;->setPerInstanceMetaClass(Ljava/lang/Object;Lgroovy/lang/MetaClass;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 311
    invoke-virtual {v1}, Lorg/codehaus/groovy/reflection/ClassInfo;->unlock()V

    .line 314
    invoke-virtual {p0, p1, v0, v2, p2}, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->fireConstantMetaClassUpdate(Ljava/lang/Object;Ljava/lang/Class;Lgroovy/lang/MetaClass;Lgroovy/lang/MetaClass;)V

    return-void

    :catchall_0
    move-exception p1

    .line 311
    invoke-virtual {v1}, Lorg/codehaus/groovy/reflection/ClassInfo;->unlock()V

    .line 312
    throw p1
.end method

.method public setMetaClassCreationHandle(Lgroovy/lang/MetaClassRegistry$MetaClassCreationHandle;)V
    .locals 1

    if-eqz p1, :cond_0

    .line 343
    invoke-static {}, Lorg/codehaus/groovy/reflection/ClassInfo;->clearModifiedExpandos()V

    .line 344
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->metaClassCreationHandle:Lgroovy/lang/MetaClassRegistry$MetaClassCreationHandle;

    invoke-virtual {v0}, Lgroovy/lang/MetaClassRegistry$MetaClassCreationHandle;->isDisableCustomMetaClassLookup()Z

    move-result v0

    invoke-virtual {p1, v0}, Lgroovy/lang/MetaClassRegistry$MetaClassCreationHandle;->setDisableCustomMetaClassLookup(Z)V

    .line 345
    iput-object p1, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->metaClassCreationHandle:Lgroovy/lang/MetaClassRegistry$MetaClassCreationHandle;

    return-void

    .line 342
    :cond_0
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string v0, "Cannot set MetaClassCreationHandle to null value!"

    invoke-direct {p1, v0}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public useAccessible()Z
    .locals 1

    .line 319
    iget-boolean v0, p0, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->useAccessible:Z

    return v0
.end method
