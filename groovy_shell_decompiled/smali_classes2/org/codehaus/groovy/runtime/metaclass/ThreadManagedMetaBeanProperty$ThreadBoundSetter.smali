.class Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty$ThreadBoundSetter;
.super Lgroovy/lang/MetaMethod;
.source "ThreadManagedMetaBeanProperty.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x2
    name = "ThreadBoundSetter"
.end annotation


# instance fields
.field private final name:Ljava/lang/String;

.field final synthetic this$0:Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty;


# direct methods
.method public constructor <init>(Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty;Ljava/lang/String;)V
    .locals 2

    .line 189
    iput-object p1, p0, Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty$ThreadBoundSetter;->this$0:Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty;

    invoke-direct {p0}, Lgroovy/lang/MetaMethod;-><init>()V

    const/4 v0, 0x1

    new-array v0, v0, [Lorg/codehaus/groovy/reflection/CachedClass;

    .line 190
    invoke-static {p1}, Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty;->access$400(Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty;)Ljava/lang/Class;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/reflection/ReflectionCache;->getCachedClass(Ljava/lang/Class;)Lorg/codehaus/groovy/reflection/CachedClass;

    move-result-object p1

    const/4 v1, 0x0

    aput-object p1, v0, v1

    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty$ThreadBoundSetter;->setParametersTypes([Lorg/codehaus/groovy/reflection/CachedClass;)V

    .line 191
    invoke-static {p2}, Lgroovy/lang/MetaProperty;->getSetterName(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    iput-object p1, p0, Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty$ThreadBoundSetter;->name:Ljava/lang/String;

    return-void
.end method


# virtual methods
.method public getDeclaringClass()Lorg/codehaus/groovy/reflection/CachedClass;
    .locals 1

    .line 212
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty$ThreadBoundSetter;->this$0:Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty;

    invoke-static {v0}, Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty;->access$200(Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty;)Ljava/lang/Class;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/reflection/ReflectionCache;->getCachedClass(Ljava/lang/Class;)Lorg/codehaus/groovy/reflection/CachedClass;

    move-result-object v0

    return-object v0
.end method

.method public getModifiers()I
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public getName()Ljava/lang/String;
    .locals 1

    .line 204
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty$ThreadBoundSetter;->name:Ljava/lang/String;

    return-object v0
.end method

.method public getReturnType()Ljava/lang/Class;
    .locals 1

    .line 208
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty$ThreadBoundSetter;->this$0:Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty;

    invoke-static {v0}, Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty;->access$500(Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty;)Ljava/lang/Class;

    move-result-object v0

    return-object v0
.end method

.method public invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 219
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty$ThreadBoundSetter;->this$0:Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty;

    invoke-static {v0}, Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty;->access$300(Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty;)Lorg/codehaus/groovy/util/ManagedConcurrentMap;

    move-result-object v0

    const/4 v1, 0x0

    aget-object p2, p2, v1

    invoke-virtual {v0, p1, p2}, Lorg/codehaus/groovy/util/ManagedConcurrentMap;->put(Ljava/lang/Object;Ljava/lang/Object;)V

    const/4 p1, 0x0

    return-object p1
.end method
