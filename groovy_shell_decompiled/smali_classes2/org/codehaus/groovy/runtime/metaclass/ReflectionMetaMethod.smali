.class public Lorg/codehaus/groovy/runtime/metaclass/ReflectionMetaMethod;
.super Lgroovy/lang/MetaMethod;
.source "ReflectionMetaMethod.java"


# instance fields
.field protected final method:Lorg/codehaus/groovy/reflection/CachedMethod;


# direct methods
.method public constructor <init>(Lorg/codehaus/groovy/reflection/CachedMethod;)V
    .locals 0

    .line 31
    invoke-direct {p0}, Lgroovy/lang/MetaMethod;-><init>()V

    .line 32
    iput-object p1, p0, Lorg/codehaus/groovy/runtime/metaclass/ReflectionMetaMethod;->method:Lorg/codehaus/groovy/reflection/CachedMethod;

    .line 33
    invoke-virtual {p1}, Lorg/codehaus/groovy/reflection/CachedMethod;->getParameterTypes()[Lorg/codehaus/groovy/reflection/CachedClass;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/runtime/metaclass/ReflectionMetaMethod;->setParametersTypes([Lorg/codehaus/groovy/reflection/CachedClass;)V

    return-void
.end method


# virtual methods
.method public getCachedMethod()Lgroovy/lang/MetaMethod;
    .locals 1

    .line 71
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/metaclass/ReflectionMetaMethod;->method:Lorg/codehaus/groovy/reflection/CachedMethod;

    return-object v0
.end method

.method public getDeclaringClass()Lorg/codehaus/groovy/reflection/CachedClass;
    .locals 1

    .line 49
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/metaclass/ReflectionMetaMethod;->method:Lorg/codehaus/groovy/reflection/CachedMethod;

    iget-object v0, v0, Lorg/codehaus/groovy/reflection/CachedMethod;->cachedClass:Lorg/codehaus/groovy/reflection/CachedClass;

    return-object v0
.end method

.method public getModifiers()I
    .locals 1

    .line 37
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/metaclass/ReflectionMetaMethod;->method:Lorg/codehaus/groovy/reflection/CachedMethod;

    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/CachedMethod;->getModifiers()I

    move-result v0

    return v0
.end method

.method public getName()Ljava/lang/String;
    .locals 1

    .line 41
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/metaclass/ReflectionMetaMethod;->method:Lorg/codehaus/groovy/reflection/CachedMethod;

    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/CachedMethod;->getName()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method protected getPT()[Ljava/lang/Class;
    .locals 1

    .line 67
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/metaclass/ReflectionMetaMethod;->method:Lorg/codehaus/groovy/reflection/CachedMethod;

    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/CachedMethod;->getNativeParameterTypes()[Ljava/lang/Class;

    move-result-object v0

    return-object v0
.end method

.method public getReturnType()Ljava/lang/Class;
    .locals 1

    .line 45
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/metaclass/ReflectionMetaMethod;->method:Lorg/codehaus/groovy/reflection/CachedMethod;

    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/CachedMethod;->getReturnType()Ljava/lang/Class;

    move-result-object v0

    return-object v0
.end method

.method public invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 54
    :try_start_0
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/metaclass/ReflectionMetaMethod;->method:Lorg/codehaus/groovy/reflection/CachedMethod;

    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/CachedMethod;->setAccessible()Ljava/lang/reflect/Method;

    move-result-object v0

    invoke-virtual {v0, p1, p2}, Ljava/lang/reflect/Method;->invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1
    :try_end_0
    .catch Ljava/lang/IllegalArgumentException; {:try_start_0 .. :try_end_0} :catch_2
    .catch Ljava/lang/IllegalAccessException; {:try_start_0 .. :try_end_0} :catch_1
    .catch Ljava/lang/reflect/InvocationTargetException; {:try_start_0 .. :try_end_0} :catch_0

    return-object p1

    :catch_0
    move-exception p1

    .line 58
    invoke-virtual {p1}, Ljava/lang/reflect/InvocationTargetException;->getCause()Ljava/lang/Throwable;

    move-result-object p2

    instance-of p2, p2, Ljava/lang/RuntimeException;

    if-eqz p2, :cond_0

    invoke-virtual {p1}, Ljava/lang/reflect/InvocationTargetException;->getCause()Ljava/lang/Throwable;

    move-result-object p1

    check-cast p1, Ljava/lang/RuntimeException;

    goto :goto_0

    :cond_0
    new-instance p2, Lorg/codehaus/groovy/runtime/InvokerInvocationException;

    invoke-direct {p2, p1}, Lorg/codehaus/groovy/runtime/InvokerInvocationException;-><init>(Ljava/lang/reflect/InvocationTargetException;)V

    move-object p1, p2

    :goto_0
    throw p1

    :catch_1
    move-exception p1

    goto :goto_1

    :catch_2
    move-exception p1

    .line 56
    :goto_1
    new-instance p2, Lorg/codehaus/groovy/runtime/InvokerInvocationException;

    invoke-direct {p2, p1}, Lorg/codehaus/groovy/runtime/InvokerInvocationException;-><init>(Ljava/lang/Throwable;)V

    throw p2
.end method

.method public toString()Ljava/lang/String;
    .locals 1

    .line 63
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/metaclass/ReflectionMetaMethod;->method:Lorg/codehaus/groovy/reflection/CachedMethod;

    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/CachedMethod;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
