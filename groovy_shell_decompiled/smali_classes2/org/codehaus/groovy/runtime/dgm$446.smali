.class public Lorg/codehaus/groovy/runtime/dgm$446;
.super Lorg/codehaus/groovy/reflection/GeneratedMetaMethod;


# direct methods
.method public constructor <init>(Ljava/lang/String;Lorg/codehaus/groovy/reflection/CachedClass;Ljava/lang/Class;[Ljava/lang/Class;)V
    .locals 0

    invoke-direct {p0, p1, p2, p3, p4}, Lorg/codehaus/groovy/reflection/GeneratedMetaMethod;-><init>(Ljava/lang/String;Lorg/codehaus/groovy/reflection/CachedClass;Ljava/lang/Class;[Ljava/lang/Class;)V

    return-void
.end method


# virtual methods
.method public final doMethodInvoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    check-cast p1, Ljava/math/BigDecimal;

    invoke-virtual {p0}, Lorg/codehaus/groovy/runtime/dgm$446;->getParameterTypes()[Lorg/codehaus/groovy/reflection/CachedClass;

    move-result-object v0

    const/4 v1, 0x0

    aget-object v0, v0, v1

    aget-object p2, p2, v1

    invoke-virtual {v0, p2}, Lorg/codehaus/groovy/reflection/CachedClass;->coerceArgument(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Ljava/math/BigDecimal;

    invoke-static {p1, p2}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->isAtLeast(Ljava/math/BigDecimal;Ljava/math/BigDecimal;)Ljava/lang/Boolean;

    move-result-object p1

    return-object p1
.end method

.method public invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    check-cast p1, Ljava/math/BigDecimal;

    const/4 v0, 0x0

    aget-object p2, p2, v0

    check-cast p2, Ljava/math/BigDecimal;

    invoke-static {p1, p2}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->isAtLeast(Ljava/math/BigDecimal;Ljava/math/BigDecimal;)Ljava/lang/Boolean;

    move-result-object p1

    return-object p1
.end method

.method public isValidMethod([Ljava/lang/Class;)Z
    .locals 2

    const/4 v0, 0x0

    if-eqz p1, :cond_0

    invoke-virtual {p0}, Lorg/codehaus/groovy/runtime/dgm$446;->getParameterTypes()[Lorg/codehaus/groovy/reflection/CachedClass;

    move-result-object v1

    aget-object v1, v1, v0

    aget-object p1, p1, v0

    invoke-virtual {v1, p1}, Lorg/codehaus/groovy/reflection/CachedClass;->isAssignableFrom(Ljava/lang/Class;)Z

    move-result p1

    if-eqz p1, :cond_1

    :cond_0
    const/4 v0, 0x1

    :cond_1
    return v0
.end method
