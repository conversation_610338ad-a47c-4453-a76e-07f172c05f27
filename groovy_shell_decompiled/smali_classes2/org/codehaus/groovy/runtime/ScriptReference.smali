.class public Lorg/codehaus/groovy/runtime/ScriptReference;
.super Lgroovy/lang/Reference;
.source "ScriptReference.java"


# static fields
.field private static final serialVersionUID:J = -0x28719b989839f2a0L


# instance fields
.field private script:Lgroovy/lang/Script;

.field private variable:Ljava/lang/String;


# direct methods
.method public constructor <init>(Lgroovy/lang/Script;Ljava/lang/String;)V
    .locals 0

    .line 33
    invoke-direct {p0}, Lgroovy/lang/Reference;-><init>()V

    .line 34
    iput-object p1, p0, Lorg/codehaus/groovy/runtime/ScriptReference;->script:Lgroovy/lang/Script;

    .line 35
    iput-object p2, p0, Lorg/codehaus/groovy/runtime/ScriptReference;->variable:Ljava/lang/String;

    return-void
.end method


# virtual methods
.method public get()Ljava/lang/Object;
    .locals 2

    .line 39
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/ScriptReference;->script:Lgroovy/lang/Script;

    invoke-virtual {v0}, Lgroovy/lang/Script;->getBinding()Lgroovy/lang/Binding;

    move-result-object v0

    iget-object v1, p0, Lorg/codehaus/groovy/runtime/ScriptReference;->variable:Ljava/lang/String;

    invoke-virtual {v0, v1}, Lgroovy/lang/Binding;->getVariable(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v0

    return-object v0
.end method

.method public set(Ljava/lang/Object;)V
    .locals 2

    .line 43
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/ScriptReference;->script:Lgroovy/lang/Script;

    invoke-virtual {v0}, Lgroovy/lang/Script;->getBinding()Lgroovy/lang/Binding;

    move-result-object v0

    iget-object v1, p0, Lorg/codehaus/groovy/runtime/ScriptReference;->variable:Ljava/lang/String;

    invoke-virtual {v0, v1, p1}, Lgroovy/lang/Binding;->setVariable(Ljava/lang/String;Ljava/lang/Object;)V

    return-void
.end method
