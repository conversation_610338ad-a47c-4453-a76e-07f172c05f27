.class Lorg/codehaus/groovy/runtime/ResourceGroovyMethods$1;
.super Lgroovy/lang/Closure;
.source "ResourceGroovyMethods.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/codehaus/groovy/runtime/ResourceGroovyMethods;->directorySize(Ljava/io/File;)J
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lgroovy/lang/Closure<",
        "Ljava/lang/Void;",
        ">;"
    }
.end annotation


# static fields
.field private static final serialVersionUID:J = 0x6ab3fa9a441fb2b5L


# instance fields
.field final synthetic val$size:[J


# direct methods
.method constructor <init>(Ljava/lang/Object;[J)V
    .locals 0

    .line 113
    iput-object p2, p0, Lorg/codehaus/groovy/runtime/ResourceGroovyMethods$1;->val$size:[J

    invoke-direct {p0, p1}, Lgroovy/lang/Closure;-><init>(Ljava/lang/Object;)V

    return-void
.end method


# virtual methods
.method public doCall([Ljava/lang/Object;)V
    .locals 6

    .line 117
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/ResourceGroovyMethods$1;->val$size:[J

    const/4 v1, 0x0

    aget-wide v2, v0, v1

    aget-object p1, p1, v1

    check-cast p1, Ljava/io/File;

    invoke-virtual {p1}, Ljava/io/File;->length()J

    move-result-wide v4

    add-long/2addr v2, v4

    aput-wide v2, v0, v1

    return-void
.end method
