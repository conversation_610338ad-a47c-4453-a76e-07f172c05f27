.class Lorg/codehaus/groovy/runtime/IOGroovyMethods$3;
.super Ljava/lang/Object;
.source "IOGroovyMethods.java"

# interfaces
.implements Ljava/util/Iterator;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/codehaus/groovy/runtime/IOGroovyMethods;->iterator(Ljava/io/DataInputStream;)Ljava/util/Iterator;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ljava/util/Iterator<",
        "Ljava/lang/Byte;",
        ">;"
    }
.end annotation


# instance fields
.field hasNext:Z

.field nextMustRead:Z

.field nextVal:Ljava/lang/Byte;

.field final synthetic val$self:Ljava/io/DataInputStream;


# direct methods
.method constructor <init>(Ljava/io/DataInputStream;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .line 1008
    iput-object p1, p0, Lorg/codehaus/groovy/runtime/IOGroovyMethods$3;->val$self:Ljava/io/DataInputStream;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 p1, 0x1

    .line 1010
    iput-boolean p1, p0, Lorg/codehaus/groovy/runtime/IOGroovyMethods$3;->nextMustRead:Z

    .line 1011
    iput-boolean p1, p0, Lorg/codehaus/groovy/runtime/IOGroovyMethods$3;->hasNext:Z

    return-void
.end method


# virtual methods
.method public hasNext()Z
    .locals 2

    .line 1014
    iget-boolean v0, p0, Lorg/codehaus/groovy/runtime/IOGroovyMethods$3;->nextMustRead:Z

    if-eqz v0, :cond_0

    iget-boolean v0, p0, Lorg/codehaus/groovy/runtime/IOGroovyMethods$3;->hasNext:Z

    if-eqz v0, :cond_0

    const/4 v0, 0x0

    .line 1016
    :try_start_0
    iget-object v1, p0, Lorg/codehaus/groovy/runtime/IOGroovyMethods$3;->val$self:Ljava/io/DataInputStream;

    invoke-virtual {v1}, Ljava/io/DataInputStream;->readByte()B

    move-result v1

    invoke-static {v1}, Ljava/lang/Byte;->valueOf(B)Ljava/lang/Byte;

    move-result-object v1

    iput-object v1, p0, Lorg/codehaus/groovy/runtime/IOGroovyMethods$3;->nextVal:Ljava/lang/Byte;

    .line 1017
    iput-boolean v0, p0, Lorg/codehaus/groovy/runtime/IOGroovyMethods$3;->nextMustRead:Z
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    .line 1019
    :catch_0
    iput-boolean v0, p0, Lorg/codehaus/groovy/runtime/IOGroovyMethods$3;->hasNext:Z

    .line 1022
    :cond_0
    :goto_0
    iget-boolean v0, p0, Lorg/codehaus/groovy/runtime/IOGroovyMethods$3;->hasNext:Z

    return v0
.end method

.method public next()Ljava/lang/Byte;
    .locals 2

    .line 1027
    iget-boolean v0, p0, Lorg/codehaus/groovy/runtime/IOGroovyMethods$3;->nextMustRead:Z

    if-eqz v0, :cond_0

    .line 1029
    :try_start_0
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/IOGroovyMethods$3;->val$self:Ljava/io/DataInputStream;

    invoke-virtual {v0}, Ljava/io/DataInputStream;->readByte()B

    move-result v0

    invoke-static {v0}, Ljava/lang/Byte;->valueOf(B)Ljava/lang/Byte;

    move-result-object v0
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    const/4 v0, 0x0

    .line 1031
    iput-boolean v0, p0, Lorg/codehaus/groovy/runtime/IOGroovyMethods$3;->hasNext:Z

    const/4 v0, 0x0

    goto :goto_0

    .line 1034
    :cond_0
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/IOGroovyMethods$3;->nextVal:Ljava/lang/Byte;

    :goto_0
    const/4 v1, 0x1

    .line 1035
    iput-boolean v1, p0, Lorg/codehaus/groovy/runtime/IOGroovyMethods$3;->nextMustRead:Z

    return-object v0
.end method

.method public bridge synthetic next()Ljava/lang/Object;
    .locals 1

    .line 1008
    invoke-virtual {p0}, Lorg/codehaus/groovy/runtime/IOGroovyMethods$3;->next()Ljava/lang/Byte;

    move-result-object v0

    return-object v0
.end method

.method public remove()V
    .locals 2

    .line 1040
    new-instance v0, Ljava/lang/UnsupportedOperationException;

    const-string v1, "Cannot remove() from a DataInputStream Iterator"

    invoke-direct {v0, v1}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw v0
.end method
