.class public Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;
.super Ljava/lang/Object;
.source "StampedCommonCache.java"

# interfaces
.implements Lorg/codehaus/groovy/runtime/memoize/EvictableCache;
.implements Lorg/codehaus/groovy/runtime/memoize/ValueConvertable;
.implements Ljava/io/Serializable;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<K:",
        "Ljava/lang/Object;",
        "V:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;",
        "Lorg/codehaus/groovy/runtime/memoize/EvictableCache<",
        "TK;TV;>;",
        "Lorg/codehaus/groovy/runtime/memoize/ValueConvertable<",
        "TV;",
        "Ljava/lang/Object;",
        ">;",
        "Ljava/io/Serializable;"
    }
.end annotation


# static fields
.field private static final serialVersionUID:J = 0x5dd2fb8809c4d80aL


# instance fields
.field private final commonCache:Lorg/codehaus/groovy/runtime/memoize/CommonCache;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lorg/codehaus/groovy/runtime/memoize/CommonCache<",
            "TK;TV;>;"
        }
    .end annotation
.end field

.field private final sl:Ljava/util/concurrent/locks/StampedLock;


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 49
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 43
    new-instance v0, Ljava/util/concurrent/locks/StampedLock;

    invoke-direct {v0}, Ljava/util/concurrent/locks/StampedLock;-><init>()V

    iput-object v0, p0, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->sl:Ljava/util/concurrent/locks/StampedLock;

    .line 50
    new-instance v0, Lorg/codehaus/groovy/runtime/memoize/CommonCache;

    invoke-direct {v0}, Lorg/codehaus/groovy/runtime/memoize/CommonCache;-><init>()V

    iput-object v0, p0, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->commonCache:Lorg/codehaus/groovy/runtime/memoize/CommonCache;

    return-void
.end method

.method public constructor <init>(I)V
    .locals 1

    .line 81
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 43
    new-instance v0, Ljava/util/concurrent/locks/StampedLock;

    invoke-direct {v0}, Ljava/util/concurrent/locks/StampedLock;-><init>()V

    iput-object v0, p0, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->sl:Ljava/util/concurrent/locks/StampedLock;

    .line 82
    new-instance v0, Lorg/codehaus/groovy/runtime/memoize/CommonCache;

    invoke-direct {v0, p1}, Lorg/codehaus/groovy/runtime/memoize/CommonCache;-><init>(I)V

    iput-object v0, p0, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->commonCache:Lorg/codehaus/groovy/runtime/memoize/CommonCache;

    return-void
.end method

.method public constructor <init>(II)V
    .locals 1

    .line 71
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 43
    new-instance v0, Ljava/util/concurrent/locks/StampedLock;

    invoke-direct {v0}, Ljava/util/concurrent/locks/StampedLock;-><init>()V

    iput-object v0, p0, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->sl:Ljava/util/concurrent/locks/StampedLock;

    .line 72
    new-instance v0, Lorg/codehaus/groovy/runtime/memoize/CommonCache;

    invoke-direct {v0, p1, p2}, Lorg/codehaus/groovy/runtime/memoize/CommonCache;-><init>(II)V

    iput-object v0, p0, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->commonCache:Lorg/codehaus/groovy/runtime/memoize/CommonCache;

    return-void
.end method

.method public constructor <init>(IILorg/codehaus/groovy/runtime/memoize/EvictableCache$EvictionStrategy;)V
    .locals 1

    .line 60
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 43
    new-instance v0, Ljava/util/concurrent/locks/StampedLock;

    invoke-direct {v0}, Ljava/util/concurrent/locks/StampedLock;-><init>()V

    iput-object v0, p0, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->sl:Ljava/util/concurrent/locks/StampedLock;

    .line 61
    new-instance v0, Lorg/codehaus/groovy/runtime/memoize/CommonCache;

    invoke-direct {v0, p1, p2, p3}, Lorg/codehaus/groovy/runtime/memoize/CommonCache;-><init>(IILorg/codehaus/groovy/runtime/memoize/EvictableCache$EvictionStrategy;)V

    iput-object v0, p0, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->commonCache:Lorg/codehaus/groovy/runtime/memoize/CommonCache;

    return-void
.end method

.method public constructor <init>(Ljava/util/Map;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "TK;TV;>;)V"
        }
    .end annotation

    .line 90
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 43
    new-instance v0, Ljava/util/concurrent/locks/StampedLock;

    invoke-direct {v0}, Ljava/util/concurrent/locks/StampedLock;-><init>()V

    iput-object v0, p0, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->sl:Ljava/util/concurrent/locks/StampedLock;

    .line 91
    new-instance v0, Lorg/codehaus/groovy/runtime/memoize/CommonCache;

    invoke-direct {v0, p1}, Lorg/codehaus/groovy/runtime/memoize/CommonCache;-><init>(Ljava/util/Map;)V

    iput-object v0, p0, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->commonCache:Lorg/codehaus/groovy/runtime/memoize/CommonCache;

    return-void
.end method

.method private compute(Ljava/lang/Object;Lorg/codehaus/groovy/runtime/memoize/MemoizeCache$ValueProvider;Z)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TK;",
            "Lorg/codehaus/groovy/runtime/memoize/MemoizeCache$ValueProvider<",
            "-TK;+TV;>;Z)TV;"
        }
    .end annotation

    if-nez p2, :cond_0

    const/4 p2, 0x0

    goto :goto_0

    .line 163
    :cond_0
    invoke-interface {p2, p1}, Lorg/codehaus/groovy/runtime/memoize/MemoizeCache$ValueProvider;->provide(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p2

    :goto_0
    if-eqz p3, :cond_1

    .line 164
    invoke-virtual {p0, p2}, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->convertValue(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p3

    if-eqz p3, :cond_1

    .line 165
    iget-object p3, p0, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->commonCache:Lorg/codehaus/groovy/runtime/memoize/CommonCache;

    invoke-virtual {p3, p1, p2}, Lorg/codehaus/groovy/runtime/memoize/CommonCache;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_1
    return-object p2
.end method

.method private doWithReadLock(Lorg/codehaus/groovy/runtime/memoize/EvictableCache$Action;)Ljava/lang/Object;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<R:",
            "Ljava/lang/Object;",
            ">(",
            "Lorg/codehaus/groovy/runtime/memoize/EvictableCache$Action<",
            "TK;TV;TR;>;)TR;"
        }
    .end annotation

    .line 283
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->sl:Ljava/util/concurrent/locks/StampedLock;

    invoke-virtual {v0}, Ljava/util/concurrent/locks/StampedLock;->tryOptimisticRead()J

    move-result-wide v0

    .line 284
    iget-object v2, p0, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->commonCache:Lorg/codehaus/groovy/runtime/memoize/CommonCache;

    invoke-interface {p1, v2}, Lorg/codehaus/groovy/runtime/memoize/EvictableCache$Action;->doWith(Lorg/codehaus/groovy/runtime/memoize/EvictableCache;)Ljava/lang/Object;

    move-result-object v2

    .line 286
    iget-object v3, p0, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->sl:Ljava/util/concurrent/locks/StampedLock;

    invoke-virtual {v3, v0, v1}, Ljava/util/concurrent/locks/StampedLock;->validate(J)Z

    move-result v0

    if-nez v0, :cond_0

    .line 287
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->sl:Ljava/util/concurrent/locks/StampedLock;

    invoke-virtual {v0}, Ljava/util/concurrent/locks/StampedLock;->readLock()J

    move-result-wide v0

    .line 289
    :try_start_0
    iget-object v2, p0, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->commonCache:Lorg/codehaus/groovy/runtime/memoize/CommonCache;

    invoke-interface {p1, v2}, Lorg/codehaus/groovy/runtime/memoize/EvictableCache$Action;->doWith(Lorg/codehaus/groovy/runtime/memoize/EvictableCache;)Ljava/lang/Object;

    move-result-object v2
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 291
    iget-object p1, p0, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->sl:Ljava/util/concurrent/locks/StampedLock;

    invoke-virtual {p1, v0, v1}, Ljava/util/concurrent/locks/StampedLock;->unlockRead(J)V

    goto :goto_0

    :catchall_0
    move-exception p1

    iget-object v2, p0, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->sl:Ljava/util/concurrent/locks/StampedLock;

    invoke-virtual {v2, v0, v1}, Ljava/util/concurrent/locks/StampedLock;->unlockRead(J)V

    .line 292
    throw p1

    :cond_0
    :goto_0
    return-object v2
.end method

.method private doWithWriteLock(Lorg/codehaus/groovy/runtime/memoize/EvictableCache$Action;)Ljava/lang/Object;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<R:",
            "Ljava/lang/Object;",
            ">(",
            "Lorg/codehaus/groovy/runtime/memoize/EvictableCache$Action<",
            "TK;TV;TR;>;)TR;"
        }
    .end annotation

    .line 270
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->sl:Ljava/util/concurrent/locks/StampedLock;

    invoke-virtual {v0}, Ljava/util/concurrent/locks/StampedLock;->writeLock()J

    move-result-wide v0

    .line 272
    :try_start_0
    iget-object v2, p0, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->commonCache:Lorg/codehaus/groovy/runtime/memoize/CommonCache;

    invoke-interface {p1, v2}, Lorg/codehaus/groovy/runtime/memoize/EvictableCache$Action;->doWith(Lorg/codehaus/groovy/runtime/memoize/EvictableCache;)Ljava/lang/Object;

    move-result-object p1
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 274
    iget-object v2, p0, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->sl:Ljava/util/concurrent/locks/StampedLock;

    invoke-virtual {v2, v0, v1}, Ljava/util/concurrent/locks/StampedLock;->unlockWrite(J)V

    return-object p1

    :catchall_0
    move-exception p1

    iget-object v2, p0, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->sl:Ljava/util/concurrent/locks/StampedLock;

    invoke-virtual {v2, v0, v1}, Ljava/util/concurrent/locks/StampedLock;->unlockWrite(J)V

    .line 275
    throw p1
.end method

.method static synthetic lambda$cleanUpNullReferences$11(Lorg/codehaus/groovy/runtime/memoize/EvictableCache;)Ljava/lang/Object;
    .locals 0

    .line 252
    invoke-interface {p0}, Lorg/codehaus/groovy/runtime/memoize/EvictableCache;->cleanUpNullReferences()V

    const/4 p0, 0x0

    return-object p0
.end method

.method static synthetic lambda$clearAll$10(Lorg/codehaus/groovy/runtime/memoize/EvictableCache;)Ljava/util/Map;
    .locals 0

    .line 243
    invoke-interface {p0}, Lorg/codehaus/groovy/runtime/memoize/EvictableCache;->clearAll()Ljava/util/Map;

    move-result-object p0

    return-object p0
.end method

.method static synthetic lambda$containsKey$5(Ljava/lang/Object;Lorg/codehaus/groovy/runtime/memoize/EvictableCache;)Ljava/lang/Boolean;
    .locals 0

    .line 196
    invoke-interface {p1, p0}, Lorg/codehaus/groovy/runtime/memoize/EvictableCache;->containsKey(Ljava/lang/Object;)Z

    move-result p0

    invoke-static {p0}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object p0

    return-object p0
.end method

.method static synthetic lambda$containsValue$6(Ljava/lang/Object;Lorg/codehaus/groovy/runtime/memoize/EvictableCache;)Ljava/lang/Boolean;
    .locals 0

    .line 201
    invoke-interface {p1, p0}, Lorg/codehaus/groovy/runtime/memoize/EvictableCache;->containsValue(Ljava/lang/Object;)Z

    move-result p0

    invoke-static {p0}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object p0

    return-object p0
.end method

.method static synthetic lambda$entrySet$3(Lorg/codehaus/groovy/runtime/memoize/EvictableCache;)Ljava/util/Set;
    .locals 0

    .line 180
    invoke-interface {p0}, Lorg/codehaus/groovy/runtime/memoize/EvictableCache;->entrySet()Ljava/util/Set;

    move-result-object p0

    return-object p0
.end method

.method static synthetic lambda$get$0(Ljava/lang/Object;Lorg/codehaus/groovy/runtime/memoize/EvictableCache;)Ljava/lang/Object;
    .locals 0

    .line 99
    invoke-interface {p1, p0}, Lorg/codehaus/groovy/runtime/memoize/EvictableCache;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    return-object p0
.end method

.method static synthetic lambda$keys$4(Lorg/codehaus/groovy/runtime/memoize/EvictableCache;)Ljava/util/Set;
    .locals 0

    .line 188
    invoke-interface {p0}, Lorg/codehaus/groovy/runtime/memoize/EvictableCache;->keys()Ljava/util/Set;

    move-result-object p0

    return-object p0
.end method

.method static synthetic lambda$put$1(Ljava/lang/Object;Ljava/lang/Object;Lorg/codehaus/groovy/runtime/memoize/EvictableCache;)Ljava/lang/Object;
    .locals 0

    .line 107
    invoke-interface {p2, p0, p1}, Lorg/codehaus/groovy/runtime/memoize/EvictableCache;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    return-object p0
.end method

.method static synthetic lambda$putAll$9(Ljava/util/Map;Lorg/codehaus/groovy/runtime/memoize/EvictableCache;)Ljava/lang/Object;
    .locals 0

    .line 228
    invoke-interface {p1, p0}, Lorg/codehaus/groovy/runtime/memoize/EvictableCache;->putAll(Ljava/util/Map;)V

    const/4 p0, 0x0

    return-object p0
.end method

.method static synthetic lambda$remove$8(Ljava/lang/Object;Lorg/codehaus/groovy/runtime/memoize/EvictableCache;)Ljava/lang/Object;
    .locals 0

    .line 222
    invoke-interface {p1, p0}, Lorg/codehaus/groovy/runtime/memoize/EvictableCache;->remove(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    return-object p0
.end method

.method static synthetic lambda$size$7(Lorg/codehaus/groovy/runtime/memoize/EvictableCache;)Ljava/lang/Integer;
    .locals 0

    .line 209
    invoke-interface {p0}, Lorg/codehaus/groovy/runtime/memoize/EvictableCache;->size()I

    move-result p0

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    return-object p0
.end method

.method static synthetic lambda$values$2(Lorg/codehaus/groovy/runtime/memoize/EvictableCache;)Ljava/util/Collection;
    .locals 0

    .line 175
    invoke-interface {p0}, Lorg/codehaus/groovy/runtime/memoize/EvictableCache;->values()Ljava/util/Collection;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public cleanUpNullReferences()V
    .locals 1

    .line 251
    sget-object v0, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache$$ExternalSyntheticLambda8;->INSTANCE:Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache$$ExternalSyntheticLambda8;

    invoke-direct {p0, v0}, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->doWithWriteLock(Lorg/codehaus/groovy/runtime/memoize/EvictableCache$Action;)Ljava/lang/Object;

    return-void
.end method

.method public clearAll()Ljava/util/Map;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "TK;TV;>;"
        }
    .end annotation

    .line 243
    sget-object v0, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache$$ExternalSyntheticLambda9;->INSTANCE:Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache$$ExternalSyntheticLambda9;

    invoke-direct {p0, v0}, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->doWithWriteLock(Lorg/codehaus/groovy/runtime/memoize/EvictableCache$Action;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/Map;

    return-object v0
.end method

.method public containsKey(Ljava/lang/Object;)Z
    .locals 1

    .line 196
    new-instance v0, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache$$ExternalSyntheticLambda0;

    invoke-direct {v0, p1}, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache$$ExternalSyntheticLambda0;-><init>(Ljava/lang/Object;)V

    invoke-direct {p0, v0}, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->doWithReadLock(Lorg/codehaus/groovy/runtime/memoize/EvictableCache$Action;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/Boolean;

    invoke-virtual {p1}, Ljava/lang/Boolean;->booleanValue()Z

    move-result p1

    return p1
.end method

.method public containsValue(Ljava/lang/Object;)Z
    .locals 1

    .line 201
    new-instance v0, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache$$ExternalSyntheticLambda3;

    invoke-direct {v0, p1}, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache$$ExternalSyntheticLambda3;-><init>(Ljava/lang/Object;)V

    invoke-direct {p0, v0}, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->doWithReadLock(Lorg/codehaus/groovy/runtime/memoize/EvictableCache$Action;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/Boolean;

    invoke-virtual {p1}, Ljava/lang/Boolean;->booleanValue()Z

    move-result p1

    return p1
.end method

.method public convertValue(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TV;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    return-object p1
.end method

.method public entrySet()Ljava/util/Set;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Set<",
            "Ljava/util/Map$Entry<",
            "TK;TV;>;>;"
        }
    .end annotation

    .line 180
    sget-object v0, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache$$ExternalSyntheticLambda10;->INSTANCE:Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache$$ExternalSyntheticLambda10;

    invoke-direct {p0, v0}, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->doWithReadLock(Lorg/codehaus/groovy/runtime/memoize/EvictableCache$Action;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/Set;

    return-object v0
.end method

.method public get(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            ")TV;"
        }
    .end annotation

    .line 99
    new-instance v0, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache$$ExternalSyntheticLambda4;

    invoke-direct {v0, p1}, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache$$ExternalSyntheticLambda4;-><init>(Ljava/lang/Object;)V

    invoke-direct {p0, v0}, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->doWithReadLock(Lorg/codehaus/groovy/runtime/memoize/EvictableCache$Action;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public getAndPut(Ljava/lang/Object;Lorg/codehaus/groovy/runtime/memoize/MemoizeCache$ValueProvider;)Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TK;",
            "Lorg/codehaus/groovy/runtime/memoize/MemoizeCache$ValueProvider<",
            "-TK;+TV;>;)TV;"
        }
    .end annotation

    const/4 v0, 0x1

    .line 115
    invoke-virtual {p0, p1, p2, v0}, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->getAndPut(Ljava/lang/Object;Lorg/codehaus/groovy/runtime/memoize/MemoizeCache$ValueProvider;Z)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public getAndPut(Ljava/lang/Object;Lorg/codehaus/groovy/runtime/memoize/MemoizeCache$ValueProvider;Z)Ljava/lang/Object;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TK;",
            "Lorg/codehaus/groovy/runtime/memoize/MemoizeCache$ValueProvider<",
            "-TK;+TV;>;Z)TV;"
        }
    .end annotation

    .line 122
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->sl:Ljava/util/concurrent/locks/StampedLock;

    invoke-virtual {v0}, Ljava/util/concurrent/locks/StampedLock;->tryOptimisticRead()J

    move-result-wide v0

    .line 123
    iget-object v2, p0, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->commonCache:Lorg/codehaus/groovy/runtime/memoize/CommonCache;

    invoke-virtual {v2, p1}, Lorg/codehaus/groovy/runtime/memoize/CommonCache;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    .line 124
    iget-object v3, p0, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->sl:Ljava/util/concurrent/locks/StampedLock;

    invoke-virtual {v3, v0, v1}, Ljava/util/concurrent/locks/StampedLock;->validate(J)Z

    move-result v3

    if-eqz v3, :cond_0

    .line 125
    invoke-virtual {p0, v2}, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->convertValue(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    if-eqz v3, :cond_0

    return-object v2

    .line 130
    :cond_0
    iget-object v2, p0, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->sl:Ljava/util/concurrent/locks/StampedLock;

    invoke-virtual {v2}, Ljava/util/concurrent/locks/StampedLock;->readLock()J

    move-result-wide v2

    .line 133
    :try_start_0
    iget-object v4, p0, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->sl:Ljava/util/concurrent/locks/StampedLock;

    invoke-virtual {v4, v0, v1}, Ljava/util/concurrent/locks/StampedLock;->validate(J)Z

    move-result v0

    if-nez v0, :cond_1

    .line 134
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->commonCache:Lorg/codehaus/groovy/runtime/memoize/CommonCache;

    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/runtime/memoize/CommonCache;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    .line 135
    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->convertValue(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-eqz v1, :cond_1

    .line 156
    :goto_0
    iget-object p1, p0, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->sl:Ljava/util/concurrent/locks/StampedLock;

    invoke-virtual {p1, v2, v3}, Ljava/util/concurrent/locks/StampedLock;->unlock(J)V

    return-object v0

    .line 140
    :cond_1
    :try_start_1
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->sl:Ljava/util/concurrent/locks/StampedLock;

    invoke-virtual {v0, v2, v3}, Ljava/util/concurrent/locks/StampedLock;->tryConvertToWriteLock(J)J

    move-result-wide v0

    const-wide/16 v4, 0x0

    cmp-long v4, v4, v0

    if-nez v4, :cond_2

    .line 142
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->sl:Ljava/util/concurrent/locks/StampedLock;

    invoke-virtual {v0, v2, v3}, Ljava/util/concurrent/locks/StampedLock;->unlockRead(J)V

    .line 143
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->sl:Ljava/util/concurrent/locks/StampedLock;

    invoke-virtual {v0}, Ljava/util/concurrent/locks/StampedLock;->writeLock()J

    move-result-wide v2

    .line 146
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->commonCache:Lorg/codehaus/groovy/runtime/memoize/CommonCache;

    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/runtime/memoize/CommonCache;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    .line 147
    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->convertValue(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    if-eqz v1, :cond_3

    goto :goto_0

    :cond_2
    move-wide v2, v0

    .line 154
    :cond_3
    invoke-direct {p0, p1, p2, p3}, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->compute(Ljava/lang/Object;Lorg/codehaus/groovy/runtime/memoize/MemoizeCache$ValueProvider;Z)Ljava/lang/Object;

    move-result-object p1
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 156
    iget-object p2, p0, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->sl:Ljava/util/concurrent/locks/StampedLock;

    invoke-virtual {p2, v2, v3}, Ljava/util/concurrent/locks/StampedLock;->unlock(J)V

    return-object p1

    :catchall_0
    move-exception p1

    iget-object p2, p0, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->sl:Ljava/util/concurrent/locks/StampedLock;

    invoke-virtual {p2, v2, v3}, Ljava/util/concurrent/locks/StampedLock;->unlock(J)V

    .line 157
    throw p1
.end method

.method public isEmpty()Z
    .locals 1

    .line 214
    invoke-virtual {p0}, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->size()I

    move-result v0

    if-nez v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public keySet()Ljava/util/Set;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Set<",
            "TK;>;"
        }
    .end annotation

    .line 235
    invoke-virtual {p0}, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->keys()Ljava/util/Set;

    move-result-object v0

    return-object v0
.end method

.method public keys()Ljava/util/Set;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Set<",
            "TK;>;"
        }
    .end annotation

    .line 188
    sget-object v0, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache$$ExternalSyntheticLambda11;->INSTANCE:Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache$$ExternalSyntheticLambda11;

    invoke-direct {p0, v0}, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->doWithReadLock(Lorg/codehaus/groovy/runtime/memoize/EvictableCache$Action;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/Set;

    return-object v0
.end method

.method public put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TK;TV;)TV;"
        }
    .end annotation

    .line 107
    new-instance v0, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache$$ExternalSyntheticLambda6;

    invoke-direct {v0, p1, p2}, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache$$ExternalSyntheticLambda6;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    invoke-direct {p0, v0}, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->doWithWriteLock(Lorg/codehaus/groovy/runtime/memoize/EvictableCache$Action;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public putAll(Ljava/util/Map;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "+TK;+TV;>;)V"
        }
    .end annotation

    .line 227
    new-instance v0, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache$$ExternalSyntheticLambda7;

    invoke-direct {v0, p1}, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache$$ExternalSyntheticLambda7;-><init>(Ljava/util/Map;)V

    invoke-direct {p0, v0}, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->doWithWriteLock(Lorg/codehaus/groovy/runtime/memoize/EvictableCache$Action;)Ljava/lang/Object;

    return-void
.end method

.method public remove(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            ")TV;"
        }
    .end annotation

    .line 222
    new-instance v0, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache$$ExternalSyntheticLambda5;

    invoke-direct {v0, p1}, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache$$ExternalSyntheticLambda5;-><init>(Ljava/lang/Object;)V

    invoke-direct {p0, v0}, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->doWithWriteLock(Lorg/codehaus/groovy/runtime/memoize/EvictableCache$Action;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public size()I
    .locals 1

    .line 209
    sget-object v0, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache$$ExternalSyntheticLambda1;->INSTANCE:Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache$$ExternalSyntheticLambda1;

    invoke-direct {p0, v0}, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->doWithReadLock(Lorg/codehaus/groovy/runtime/memoize/EvictableCache$Action;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/Integer;

    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    move-result v0

    return v0
.end method

.method public values()Ljava/util/Collection;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Collection<",
            "TV;>;"
        }
    .end annotation

    .line 175
    sget-object v0, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache$$ExternalSyntheticLambda2;->INSTANCE:Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache$$ExternalSyntheticLambda2;

    invoke-direct {p0, v0}, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->doWithReadLock(Lorg/codehaus/groovy/runtime/memoize/EvictableCache$Action;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/Collection;

    return-object v0
.end method
