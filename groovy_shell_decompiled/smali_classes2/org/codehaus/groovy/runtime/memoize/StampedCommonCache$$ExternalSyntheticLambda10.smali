.class public final synthetic Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache$$ExternalSyntheticLambda10;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Lorg/codehaus/groovy/runtime/memoize/EvictableCache$Action;


# static fields
.field public static final synthetic INSTANCE:Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache$$ExternalSyntheticLambda10;


# direct methods
.method static synthetic constructor <clinit>()V
    .locals 1

    new-instance v0, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache$$ExternalSyntheticLambda10;

    invoke-direct {v0}, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache$$ExternalSyntheticLambda10;-><init>()V

    sput-object v0, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache$$ExternalSyntheticLambda10;->INSTANCE:Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache$$ExternalSyntheticLambda10;

    return-void
.end method

.method private synthetic constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final doWith(Lorg/codehaus/groovy/runtime/memoize/EvictableCache;)Ljava/lang/Object;
    .locals 0

    invoke-static {p1}, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->lambda$entrySet$3(Lorg/codehaus/groovy/runtime/memoize/EvictableCache;)Ljava/util/Set;

    move-result-object p1

    return-object p1
.end method
