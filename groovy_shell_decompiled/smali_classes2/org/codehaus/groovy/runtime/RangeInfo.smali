.class public Lorg/codehaus/groovy/runtime/RangeInfo;
.super Ljava/lang/Object;
.source "RangeInfo.java"


# instance fields
.field public final from:I

.field public final reverse:Z

.field public final to:I


# direct methods
.method public constructor <init>(IIZ)V
    .locals 0

    .line 26
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 27
    iput p1, p0, Lorg/codehaus/groovy/runtime/RangeInfo;->from:I

    .line 28
    iput p2, p0, Lorg/codehaus/groovy/runtime/RangeInfo;->to:I

    .line 29
    iput-boolean p3, p0, Lorg/codehaus/groovy/runtime/RangeInfo;->reverse:Z

    return-void
.end method
