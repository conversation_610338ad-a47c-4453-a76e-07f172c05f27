.class final Lorg/codehaus/groovy/runtime/DefaultGroovyMethods$IteratorIterableAdapter;
.super Ljava/lang/Object;
.source "DefaultGroovyMethods.java"

# interfaces
.implements Ljava/lang/Iterable;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1a
    name = "IteratorIterableAdapter"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;",
        "Ljava/lang/Iterable<",
        "TT;>;"
    }
.end annotation


# instance fields
.field private final self:Ljava/util/Iterator;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Iterator<",
            "TT;>;"
        }
    .end annotation
.end field


# direct methods
.method private constructor <init>(Ljava/util/Iterator;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Iterator<",
            "TT;>;)V"
        }
    .end annotation

    .line 1529
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 1530
    iput-object p1, p0, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods$IteratorIterableAdapter;->self:Ljava/util/Iterator;

    return-void
.end method

.method synthetic constructor <init>(Ljava/util/Iterator;Lorg/codehaus/groovy/runtime/DefaultGroovyMethods$1;)V
    .locals 0

    .line 1526
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods$IteratorIterableAdapter;-><init>(Ljava/util/Iterator;)V

    return-void
.end method


# virtual methods
.method public iterator()Ljava/util/Iterator;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Iterator<",
            "TT;>;"
        }
    .end annotation

    .line 1535
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods$IteratorIterableAdapter;->self:Ljava/util/Iterator;

    return-object v0
.end method
