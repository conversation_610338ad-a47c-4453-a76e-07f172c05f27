.class final Lorg/codehaus/groovy/runtime/DefaultGroovyMethods$DropWhileIterator;
.super Ljava/lang/Object;
.source "DefaultGroovyMethods.java"

# interfaces
.implements Ljava/util/Iterator;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1a
    name = "DropWhileIterator"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "<E:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;",
        "Ljava/util/Iterator<",
        "TE;>;"
    }
.end annotation


# instance fields
.field private buffer:Ljava/lang/Object;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "TE;"
        }
    .end annotation
.end field

.field private buffering:Z

.field private final condition:Lgroovy/lang/Closure;

.field private final delegate:Ljava/util/Iterator;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Iterator<",
            "TE;>;"
        }
    .end annotation
.end field


# direct methods
.method private constructor <init>(Ljava/util/Iterator;Lgroovy/lang/Closure;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Iterator<",
            "TE;>;",
            "Lgroovy/lang/Closure;",
            ")V"
        }
    .end annotation

    .line 11796
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    .line 11793
    iput-boolean v0, p0, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods$DropWhileIterator;->buffering:Z

    const/4 v0, 0x0

    .line 11794
    iput-object v0, p0, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods$DropWhileIterator;->buffer:Ljava/lang/Object;

    .line 11797
    iput-object p1, p0, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods$DropWhileIterator;->delegate:Ljava/util/Iterator;

    .line 11798
    iput-object p2, p0, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods$DropWhileIterator;->condition:Lgroovy/lang/Closure;

    .line 11799
    invoke-direct {p0}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods$DropWhileIterator;->prepare()V

    return-void
.end method

.method synthetic constructor <init>(Ljava/util/Iterator;Lgroovy/lang/Closure;Lorg/codehaus/groovy/runtime/DefaultGroovyMethods$1;)V
    .locals 0

    .line 11790
    invoke-direct {p0, p1, p2}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods$DropWhileIterator;-><init>(Ljava/util/Iterator;Lgroovy/lang/Closure;)V

    return-void
.end method

.method private prepare()V
    .locals 5

    .line 11827
    new-instance v0, Lorg/codehaus/groovy/runtime/callsite/BooleanClosureWrapper;

    iget-object v1, p0, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods$DropWhileIterator;->condition:Lgroovy/lang/Closure;

    invoke-direct {v0, v1}, Lorg/codehaus/groovy/runtime/callsite/BooleanClosureWrapper;-><init>(Lgroovy/lang/Closure;)V

    .line 11828
    :cond_0
    iget-object v1, p0, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods$DropWhileIterator;->delegate:Ljava/util/Iterator;

    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    .line 11829
    iget-object v1, p0, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods$DropWhileIterator;->delegate:Ljava/util/Iterator;

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    const/4 v2, 0x1

    new-array v3, v2, [Ljava/lang/Object;

    const/4 v4, 0x0

    aput-object v1, v3, v4

    .line 11830
    invoke-virtual {v0, v3}, Lorg/codehaus/groovy/runtime/callsite/BooleanClosureWrapper;->call([Ljava/lang/Object;)Z

    move-result v3

    if-nez v3, :cond_0

    .line 11831
    iput-object v1, p0, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods$DropWhileIterator;->buffer:Ljava/lang/Object;

    .line 11832
    iput-boolean v2, p0, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods$DropWhileIterator;->buffering:Z

    :cond_1
    return-void
.end method


# virtual methods
.method public hasNext()Z
    .locals 1

    .line 11803
    iget-boolean v0, p0, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods$DropWhileIterator;->buffering:Z

    if-nez v0, :cond_1

    iget-object v0, p0, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods$DropWhileIterator;->delegate:Ljava/util/Iterator;

    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v0, 0x1

    :goto_1
    return v0
.end method

.method public next()Ljava/lang/Object;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()TE;"
        }
    .end annotation

    .line 11807
    iget-boolean v0, p0, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods$DropWhileIterator;->buffering:Z

    if-eqz v0, :cond_0

    .line 11808
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods$DropWhileIterator;->buffer:Ljava/lang/Object;

    const/4 v1, 0x0

    .line 11809
    iput-boolean v1, p0, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods$DropWhileIterator;->buffering:Z

    const/4 v1, 0x0

    .line 11810
    iput-object v1, p0, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods$DropWhileIterator;->buffer:Ljava/lang/Object;

    return-object v0

    .line 11813
    :cond_0
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods$DropWhileIterator;->delegate:Ljava/util/Iterator;

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    return-object v0
.end method

.method public remove()V
    .locals 1

    .line 11818
    iget-boolean v0, p0, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods$DropWhileIterator;->buffering:Z

    if-eqz v0, :cond_0

    const/4 v0, 0x0

    .line 11819
    iput-boolean v0, p0, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods$DropWhileIterator;->buffering:Z

    const/4 v0, 0x0

    .line 11820
    iput-object v0, p0, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods$DropWhileIterator;->buffer:Ljava/lang/Object;

    goto :goto_0

    .line 11822
    :cond_0
    iget-object v0, p0, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods$DropWhileIterator;->delegate:Ljava/util/Iterator;

    invoke-interface {v0}, Ljava/util/Iterator;->remove()V

    :goto_0
    return-void
.end method
