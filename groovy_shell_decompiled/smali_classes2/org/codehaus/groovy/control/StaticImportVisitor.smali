.class public Lorg/codehaus/groovy/control/StaticImportVisitor;
.super Lorg/codehaus/groovy/ast/ClassCodeExpressionTransformer;
.source "StaticImportVisitor.java"


# instance fields
.field private currentClass:Lorg/codehaus/groovy/ast/ClassNode;

.field private currentMethod:Lorg/codehaus/groovy/ast/MethodNode;

.field private foundArgs:Lorg/codehaus/groovy/ast/expr/Expression;

.field private foundConstant:Lorg/codehaus/groovy/ast/expr/Expression;

.field private inAnnotation:Z

.field private inClosure:Z

.field private inLeftExpression:Z

.field private inPropertyExpression:Z

.field private inSpecialConstructorCall:Z

.field private sourceUnit:Lorg/codehaus/groovy/control/SourceUnit;


# direct methods
.method public constructor <init>()V
    .locals 1
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    const/4 v0, 0x0

    .line 88
    invoke-direct {p0, v0, v0}, Lorg/codehaus/groovy/control/StaticImportVisitor;-><init>(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/control/SourceUnit;)V

    return-void
.end method

.method public constructor <init>(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/control/SourceUnit;)V
    .locals 0

    .line 91
    invoke-direct {p0}, Lorg/codehaus/groovy/ast/ClassCodeExpressionTransformer;-><init>()V

    .line 92
    iput-object p1, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    .line 93
    iput-object p2, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->sourceUnit:Lorg/codehaus/groovy/control/SourceUnit;

    return-void
.end method

.method private findStaticFieldOrPropertyAccessorImportFromModule(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 5

    .line 396
    iget-object v0, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->getModule()Lorg/codehaus/groovy/ast/ModuleNode;

    move-result-object v0

    const/4 v1, 0x0

    if-nez v0, :cond_0

    return-object v1

    .line 397
    :cond_0
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ModuleNode;->getStaticImports()Ljava/util/Map;

    move-result-object v2

    .line 405
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/control/StaticImportVisitor;->getAccessorName(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v3

    invoke-direct {p0, v2, v3}, Lorg/codehaus/groovy/control/StaticImportVisitor;->findStaticProperty(Ljava/util/Map;Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v3

    if-eqz v3, :cond_1

    return-object v3

    .line 407
    :cond_1
    iget-boolean v3, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->inLeftExpression:Z

    if-nez v3, :cond_2

    .line 408
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "is"

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-static {p1}, Lorg/apache/groovy/util/BeanUtils;->capitalize(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-direct {p0, v2, v3}, Lorg/codehaus/groovy/control/StaticImportVisitor;->findStaticProperty(Ljava/util/Map;Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v3

    if-eqz v3, :cond_2

    return-object v3

    .line 415
    :cond_2
    invoke-interface {v2, p1}, Ljava/util/Map;->containsKey(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_3

    invoke-interface {v2, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lorg/codehaus/groovy/ast/ImportNode;

    .line 416
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/ImportNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v3

    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/ImportNode;->getFieldName()Ljava/lang/String;

    move-result-object v2

    invoke-direct {p0, v3, v2}, Lorg/codehaus/groovy/control/StaticImportVisitor;->findStaticPropertyOrField(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v2

    if-eqz v2, :cond_3

    return-object v2

    .line 423
    :cond_3
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ModuleNode;->getStaticStarImports()Ljava/util/Map;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Map;->values()Ljava/util/Collection;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_4
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_5

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lorg/codehaus/groovy/ast/ImportNode;

    .line 424
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/ImportNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v2

    invoke-direct {p0, v2, p1}, Lorg/codehaus/groovy/control/StaticImportVisitor;->findStaticPropertyOrField(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v2

    if-eqz v2, :cond_4

    return-object v2

    :cond_5
    return-object v1
.end method

.method private static findStaticMethod(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 1

    .line 587
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/ClassNode;->isPrimaryClassNode()Z

    move-result v0

    if-nez v0, :cond_0

    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/ClassNode;->isResolved()Z

    move-result v0

    if-eqz v0, :cond_1

    .line 588
    :cond_0
    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/ast/ClassNode;->hasPossibleStaticMethod(Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Z

    move-result v0

    if-eqz v0, :cond_1

    .line 589
    invoke-static {p0, p1, p2}, Lorg/codehaus/groovy/control/StaticImportVisitor;->newStaticMethodCallX(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/StaticMethodCallExpression;

    move-result-object p0

    return-object p0

    :cond_1
    const/4 p0, 0x0

    return-object p0
.end method

.method private findStaticMethodImportFromModule(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 8

    .line 432
    iget-object v0, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->getModule()Lorg/codehaus/groovy/ast/ModuleNode;

    move-result-object v0

    const/4 v1, 0x0

    if-nez v0, :cond_0

    return-object v1

    .line 433
    :cond_0
    instance-of v0, p1, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    if-nez v0, :cond_1

    return-object v1

    .line 434
    :cond_1
    move-object v0, p1

    check-cast v0, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/ConstantExpression;->getValue()Ljava/lang/Object;

    move-result-object v0

    instance-of v0, v0, Ljava/lang/String;

    if-nez v0, :cond_2

    return-object v1

    .line 437
    :cond_2
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/Expression;->getText()Ljava/lang/String;

    move-result-object p1

    .line 438
    iget-object v0, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->getModule()Lorg/codehaus/groovy/ast/ModuleNode;

    move-result-object v0

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ModuleNode;->getStaticImports()Ljava/util/Map;

    move-result-object v0

    .line 444
    invoke-interface {v0, p1}, Ljava/util/Map;->containsKey(Ljava/lang/Object;)Z

    move-result v2

    const/4 v3, 0x0

    const-string v4, "call"

    if-eqz v2, :cond_4

    .line 445
    invoke-interface {v0, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lorg/codehaus/groovy/ast/ImportNode;

    .line 446
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/ImportNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v5

    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/ImportNode;->getFieldName()Ljava/lang/String;

    move-result-object v6

    invoke-static {v5, v6, p2}, Lorg/codehaus/groovy/control/StaticImportVisitor;->findStaticMethod(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v5

    if-eqz v5, :cond_3

    return-object v5

    .line 450
    :cond_3
    iget-boolean v5, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->inClosure:Z

    if-nez v5, :cond_4

    iget-boolean v5, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->inLeftExpression:Z

    if-nez v5, :cond_4

    .line 451
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/ImportNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v5

    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/ImportNode;->getFieldName()Ljava/lang/String;

    move-result-object v2

    invoke-direct {p0, v5, v2}, Lorg/codehaus/groovy/control/StaticImportVisitor;->findStaticPropertyOrField(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v2

    if-eqz v2, :cond_4

    .line 453
    new-instance p1, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    invoke-direct {p1, v2, v4, p2}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;-><init>(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)V

    .line 454
    invoke-virtual {p1, v3}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setImplicitThis(Z)V

    return-object p1

    .line 465
    :cond_4
    invoke-static {p1}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->isValidAccessorName(Ljava/lang/String;)Z

    move-result v2

    if-eqz v2, :cond_7

    .line 467
    invoke-interface {v0, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lorg/codehaus/groovy/ast/ImportNode;

    if-eqz v5, :cond_5

    .line 469
    invoke-virtual {v5}, Lorg/codehaus/groovy/ast/ImportNode;->getFieldName()Ljava/lang/String;

    move-result-object v6

    invoke-static {v6}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->getPropNameForAccessor(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v6

    .line 470
    invoke-virtual {v5}, Lorg/codehaus/groovy/ast/ImportNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v7

    invoke-direct {p0, v7, v6, p2}, Lorg/codehaus/groovy/control/StaticImportVisitor;->findStaticPropertyAccessorGivenArgs(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v6

    if-eqz v6, :cond_5

    .line 472
    invoke-virtual {v5}, Lorg/codehaus/groovy/ast/ImportNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    invoke-virtual {v5}, Lorg/codehaus/groovy/ast/ImportNode;->getFieldName()Ljava/lang/String;

    move-result-object v0

    invoke-static {p1, v0, p2}, Lorg/codehaus/groovy/control/StaticImportVisitor;->newStaticMethodCallX(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/StaticMethodCallExpression;

    move-result-object p1

    return-object p1

    .line 475
    :cond_5
    invoke-static {p1}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->getPropNameForAccessor(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v5

    invoke-interface {v0, v5}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/ast/ImportNode;

    if-eqz v0, :cond_7

    .line 477
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ImportNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v5

    .line 478
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ImportNode;->getFieldName()Ljava/lang/String;

    move-result-object v0

    .line 479
    new-instance v6, Ljava/lang/StringBuilder;

    invoke-direct {v6}, Ljava/lang/StringBuilder;-><init>()V

    invoke-static {p1}, Lorg/codehaus/groovy/control/StaticImportVisitor;->prefix(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v7

    invoke-virtual {v6, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v6

    invoke-static {v0}, Lorg/apache/groovy/util/BeanUtils;->capitalize(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v7

    invoke-virtual {v6, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v6

    invoke-virtual {v6}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v6

    invoke-static {v5, v6, p2}, Lorg/codehaus/groovy/control/StaticImportVisitor;->findStaticMethod(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v6

    if-eqz v6, :cond_6

    return-object v6

    .line 481
    :cond_6
    invoke-direct {p0, v5, v0, p2}, Lorg/codehaus/groovy/control/StaticImportVisitor;->findStaticPropertyAccessorGivenArgs(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v6

    if-eqz v6, :cond_7

    .line 483
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-static {p1}, Lorg/codehaus/groovy/control/StaticImportVisitor;->prefix(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-static {v0}, Lorg/apache/groovy/util/BeanUtils;->capitalize(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v5, p1, p2}, Lorg/codehaus/groovy/control/StaticImportVisitor;->newStaticMethodCallX(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/StaticMethodCallExpression;

    move-result-object p1

    return-object p1

    .line 488
    :cond_7
    iget-object v0, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->getModule()Lorg/codehaus/groovy/ast/ModuleNode;

    move-result-object v0

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ModuleNode;->getStaticStarImports()Ljava/util/Map;

    move-result-object v0

    .line 489
    iget-object v5, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v5}, Lorg/codehaus/groovy/ast/ClassNode;->isEnum()Z

    move-result v5

    if-eqz v5, :cond_8

    iget-object v5, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v5}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v5

    invoke-interface {v0, v5}, Ljava/util/Map;->containsKey(Ljava/lang/Object;)Z

    move-result v5

    if-eqz v5, :cond_8

    .line 490
    iget-object v1, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/ast/ImportNode;

    .line 491
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ImportNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    invoke-static {v0, p1, p2}, Lorg/codehaus/groovy/control/StaticImportVisitor;->findStaticMethod(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    return-object p1

    .line 497
    :cond_8
    invoke-interface {v0}, Ljava/util/Map;->values()Ljava/util/Collection;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_9
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v5

    if-eqz v5, :cond_c

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lorg/codehaus/groovy/ast/ImportNode;

    .line 498
    invoke-virtual {v5}, Lorg/codehaus/groovy/ast/ImportNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v5

    .line 499
    invoke-static {v5, p1, p2}, Lorg/codehaus/groovy/control/StaticImportVisitor;->findStaticMethod(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v6

    if-eqz v6, :cond_a

    return-object v6

    .line 501
    :cond_a
    iget-boolean v6, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->inClosure:Z

    if-nez v6, :cond_b

    iget-boolean v6, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->inLeftExpression:Z

    if-nez v6, :cond_b

    .line 502
    invoke-direct {p0, v5, p1}, Lorg/codehaus/groovy/control/StaticImportVisitor;->findStaticPropertyOrField(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v6

    if-eqz v6, :cond_b

    .line 504
    new-instance p1, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    invoke-direct {p1, v6, v4, p2}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;-><init>(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)V

    .line 505
    invoke-virtual {p1, v3}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setImplicitThis(Z)V

    return-object p1

    :cond_b
    if-eqz v2, :cond_9

    .line 510
    invoke-static {p1}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->getPropNameForAccessor(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v6

    .line 511
    invoke-direct {p0, v5, v6, p2}, Lorg/codehaus/groovy/control/StaticImportVisitor;->findStaticPropertyAccessorGivenArgs(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v6

    if-eqz v6, :cond_9

    .line 513
    invoke-static {v5, p1, p2}, Lorg/codehaus/groovy/control/StaticImportVisitor;->newStaticMethodCallX(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/StaticMethodCallExpression;

    move-result-object p1

    return-object p1

    :cond_c
    return-object v1
.end method

.method private findStaticProperty(Ljava/util/Map;Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Lorg/codehaus/groovy/ast/ImportNode;",
            ">;",
            "Ljava/lang/String;",
            ")",
            "Lorg/codehaus/groovy/ast/expr/Expression;"
        }
    .end annotation

    .line 568
    invoke-interface {p1, p2}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lorg/codehaus/groovy/ast/ImportNode;

    if-eqz p1, :cond_1

    .line 569
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ImportNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p2

    .line 570
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ImportNode;->getFieldName()Ljava/lang/String;

    move-result-object v0

    invoke-direct {p0, p2, v0}, Lorg/codehaus/groovy/control/StaticImportVisitor;->findStaticPropertyAccessorByFullName(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    if-nez v0, :cond_2

    .line 572
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ImportNode;->getFieldName()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->getPropNameForAccessor(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    .line 573
    invoke-static {p2, v1}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->hasStaticProperty(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Z

    move-result v2

    if-eqz v2, :cond_2

    .line 574
    iget-boolean v0, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->inLeftExpression:Z

    if-eqz v0, :cond_0

    .line 575
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ImportNode;->getFieldName()Ljava/lang/String;

    move-result-object p1

    sget-object v0, Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;->EMPTY_ARGUMENTS:Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    invoke-static {p2, p1, v0}, Lorg/codehaus/groovy/control/StaticImportVisitor;->newStaticMethodCallX(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/StaticMethodCallExpression;

    move-result-object v0

    goto :goto_0

    .line 578
    :cond_0
    invoke-static {p2, v1}, Lorg/codehaus/groovy/control/StaticImportVisitor;->newStaticPropertyX(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    move-result-object v0

    goto :goto_0

    :cond_1
    const/4 v0, 0x0

    :cond_2
    :goto_0
    return-object v0
.end method

.method private findStaticPropertyAccessor(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 3

    .line 540
    invoke-direct {p0, p2}, Lorg/codehaus/groovy/control/StaticImportVisitor;->getAccessorName(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    .line 541
    invoke-direct {p0, p1, v0}, Lorg/codehaus/groovy/control/StaticImportVisitor;->findStaticPropertyAccessorByFullName(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v1

    if-nez v1, :cond_0

    .line 542
    iget-boolean v2, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->inLeftExpression:Z

    if-nez v2, :cond_0

    .line 543
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "is"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    const/4 v2, 0x3

    invoke-virtual {v0, v2}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-direct {p0, p1, v1}, Lorg/codehaus/groovy/control/StaticImportVisitor;->findStaticPropertyAccessorByFullName(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v1

    :cond_0
    if-nez v1, :cond_2

    .line 545
    invoke-static {p1, p2}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->hasStaticProperty(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Z

    move-result v2

    if-eqz v2, :cond_2

    .line 546
    iget-boolean v1, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->inLeftExpression:Z

    if-eqz v1, :cond_1

    .line 547
    sget-object p2, Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;->EMPTY_ARGUMENTS:Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    invoke-static {p1, v0, p2}, Lorg/codehaus/groovy/control/StaticImportVisitor;->newStaticMethodCallX(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/StaticMethodCallExpression;

    move-result-object v1

    goto :goto_0

    .line 549
    :cond_1
    invoke-static {p1, p2}, Lorg/codehaus/groovy/control/StaticImportVisitor;->newStaticPropertyX(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    move-result-object v1

    :cond_2
    :goto_0
    return-object v1
.end method

.method private findStaticPropertyAccessorByFullName(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 2

    .line 529
    iget-boolean v0, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->inLeftExpression:Z

    if-eqz v0, :cond_0

    new-instance v0, Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    sget-object v1, Lorg/codehaus/groovy/ast/expr/EmptyExpression;->INSTANCE:Lorg/codehaus/groovy/ast/expr/EmptyExpression;

    invoke-direct {v0, v1}, Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;-><init>(Lorg/codehaus/groovy/ast/expr/Expression;)V

    goto :goto_0

    :cond_0
    sget-object v0, Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;->EMPTY_ARGUMENTS:Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    .line 530
    :goto_0
    invoke-static {p1, p2, v0}, Lorg/codehaus/groovy/control/StaticImportVisitor;->findStaticMethod(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    return-object p1
.end method

.method private findStaticPropertyAccessorGivenArgs(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 0

    .line 536
    invoke-direct {p0, p1, p2}, Lorg/codehaus/groovy/control/StaticImportVisitor;->findStaticPropertyAccessor(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    return-object p1
.end method

.method private findStaticPropertyOrField(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 2

    .line 555
    invoke-direct {p0, p1, p2}, Lorg/codehaus/groovy/control/StaticImportVisitor;->findStaticPropertyAccessor(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    if-nez v0, :cond_1

    .line 557
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->isPrimaryClassNode()Z

    move-result v1

    if-nez v1, :cond_0

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->isResolved()Z

    move-result v1

    if-eqz v1, :cond_1

    .line 558
    :cond_0
    invoke-static {p1, p2}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->getField(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object v1

    if-eqz v1, :cond_1

    .line 559
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/FieldNode;->isStatic()Z

    move-result v1

    if-eqz v1, :cond_1

    .line 560
    invoke-static {p1, p2}, Lorg/codehaus/groovy/control/StaticImportVisitor;->newStaticPropertyX(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    move-result-object v0

    :cond_1
    return-object v0
.end method

.method private getAccessorName(Ljava/lang/String;)Ljava/lang/String;
    .locals 2

    .line 525
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    iget-boolean v1, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->inLeftExpression:Z

    if-eqz v1, :cond_0

    const-string v1, "set"

    goto :goto_0

    :cond_0
    const-string v1, "get"

    :goto_0
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-static {p1}, Lorg/apache/groovy/util/BeanUtils;->capitalize(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method static synthetic lambda$transformMethodCallExpression$0(Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/ClassNode;)Z
    .locals 0

    .line 259
    invoke-virtual {p2, p0, p1}, Lorg/codehaus/groovy/ast/ClassNode;->tryFindPossibleMethod(Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/MethodNode;

    move-result-object p0

    if-eqz p0, :cond_0

    const/4 p0, 0x1

    goto :goto_0

    :cond_0
    const/4 p0, 0x0

    :goto_0
    return p0
.end method

.method static synthetic lambda$transformMethodCallExpression$1(Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/ClassNode;)Z
    .locals 2

    const/4 v0, 0x1

    .line 278
    invoke-static {p2, p0, p1, v0}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->hasPossibleStaticMethod(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;Z)Z

    move-result v1

    if-eqz v1, :cond_0

    return v0

    .line 282
    :cond_0
    instance-of v1, p1, Lorg/codehaus/groovy/ast/expr/TupleExpression;

    if-eqz v1, :cond_1

    check-cast p1, Lorg/codehaus/groovy/ast/expr/TupleExpression;

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/TupleExpression;->getExpressions()Ljava/util/List;

    move-result-object p1

    invoke-interface {p1}, Ljava/util/List;->isEmpty()Z

    move-result p1

    if-eqz p1, :cond_1

    .line 283
    invoke-static {p2, p0}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->hasPossibleStaticProperty(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Z

    move-result p0

    if-eqz p0, :cond_1

    return v0

    :cond_1
    const/4 p0, 0x0

    return p0
.end method

.method private static newStaticMethodCallX(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/StaticMethodCallExpression;
    .locals 1

    .line 596
    new-instance v0, Lorg/codehaus/groovy/ast/expr/StaticMethodCallExpression;

    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/ClassNode;->getPlainNodeReference()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p0

    invoke-direct {v0, p0, p1, p2}, Lorg/codehaus/groovy/ast/expr/StaticMethodCallExpression;-><init>(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)V

    return-object v0
.end method

.method private static newStaticPropertyX(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/PropertyExpression;
    .locals 2

    .line 600
    new-instance v0, Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    new-instance v1, Lorg/codehaus/groovy/ast/expr/ClassExpression;

    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/ClassNode;->getPlainNodeReference()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p0

    invoke-direct {v1, p0}, Lorg/codehaus/groovy/ast/expr/ClassExpression;-><init>(Lorg/codehaus/groovy/ast/ClassNode;)V

    invoke-direct {v0, v1, p1}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;-><init>(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;)V

    return-object v0
.end method

.method private static prefix(Ljava/lang/String;)Ljava/lang/String;
    .locals 2

    const-string v0, "is"

    .line 521
    invoke-virtual {p0, v0}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v1

    if-eqz v1, :cond_0

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    const/4 v1, 0x3

    invoke-virtual {p0, v0, v1}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object v0

    :goto_0
    return-object v0
.end method

.method private transformMapEntryExpression(Lorg/codehaus/groovy/ast/expr/MapEntryExpression;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 4

    .line 182
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MapEntryExpression;->getKeyExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    .line 183
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MapEntryExpression;->getValueExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v1

    .line 184
    iget-object v2, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/ClassNode;->getModule()Lorg/codehaus/groovy/ast/ModuleNode;

    move-result-object v2

    if-eqz v2, :cond_0

    .line 185
    instance-of v3, v0, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    if-eqz v3, :cond_0

    .line 186
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/ModuleNode;->getStaticImports()Ljava/util/Map;

    move-result-object v2

    .line 187
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/Expression;->getText()Ljava/lang/String;

    move-result-object v3

    invoke-interface {v2, v3}, Ljava/util/Map;->containsKey(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_0

    .line 188
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/Expression;->getText()Ljava/lang/String;

    move-result-object v0

    invoke-interface {v2, v0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/ast/ImportNode;

    .line 189
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ImportNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v2

    invoke-virtual {v2, p2}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result p2

    if-eqz p2, :cond_0

    .line 190
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ImportNode;->getFieldName()Ljava/lang/String;

    move-result-object p1

    .line 191
    new-instance p2, Lorg/codehaus/groovy/ast/expr/MapEntryExpression;

    new-instance v0, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    invoke-direct {v0, p1}, Lorg/codehaus/groovy/ast/expr/ConstantExpression;-><init>(Ljava/lang/Object;)V

    invoke-virtual {v1, p0}, Lorg/codehaus/groovy/ast/expr/Expression;->transformExpression(Lorg/codehaus/groovy/ast/expr/ExpressionTransformer;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    invoke-direct {p2, v0, p1}, Lorg/codehaus/groovy/ast/expr/MapEntryExpression;-><init>(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)V

    return-object p2

    :cond_0
    return-object p1
.end method


# virtual methods
.method protected getSourceUnit()Lorg/codehaus/groovy/control/SourceUnit;
    .locals 1

    .line 605
    iget-object v0, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->sourceUnit:Lorg/codehaus/groovy/control/SourceUnit;

    return-object v0
.end method

.method public transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 3

    if-nez p1, :cond_0

    const/4 p1, 0x0

    return-object p1

    .line 124
    :cond_0
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    .line 125
    const-class v1, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    if-ne v0, v1, :cond_1

    .line 126
    check-cast p1, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/control/StaticImportVisitor;->transformVariableExpression(Lorg/codehaus/groovy/ast/expr/VariableExpression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    return-object p1

    .line 128
    :cond_1
    const-class v1, Lorg/codehaus/groovy/ast/expr/BinaryExpression;

    if-ne v0, v1, :cond_2

    .line 129
    check-cast p1, Lorg/codehaus/groovy/ast/expr/BinaryExpression;

    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/control/StaticImportVisitor;->transformBinaryExpression(Lorg/codehaus/groovy/ast/expr/BinaryExpression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    return-object p1

    .line 131
    :cond_2
    const-class v1, Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    if-ne v0, v1, :cond_3

    .line 132
    check-cast p1, Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/control/StaticImportVisitor;->transformPropertyExpression(Lorg/codehaus/groovy/ast/expr/PropertyExpression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    return-object p1

    .line 134
    :cond_3
    const-class v1, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    if-ne v0, v1, :cond_4

    .line 135
    check-cast p1, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/control/StaticImportVisitor;->transformMethodCallExpression(Lorg/codehaus/groovy/ast/expr/MethodCallExpression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    return-object p1

    .line 137
    :cond_4
    instance-of v1, p1, Lorg/codehaus/groovy/ast/expr/ClosureExpression;

    if-eqz v1, :cond_5

    .line 138
    check-cast p1, Lorg/codehaus/groovy/ast/expr/ClosureExpression;

    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/control/StaticImportVisitor;->transformClosureExpression(Lorg/codehaus/groovy/ast/expr/ClosureExpression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    return-object p1

    .line 140
    :cond_5
    const-class v1, Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;

    if-ne v0, v1, :cond_6

    .line 141
    check-cast p1, Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;

    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/control/StaticImportVisitor;->transformConstructorCallExpression(Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    return-object p1

    .line 143
    :cond_6
    const-class v1, Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    if-ne v0, v1, :cond_8

    .line 144
    invoke-virtual {p1, p0}, Lorg/codehaus/groovy/ast/expr/Expression;->transformExpression(Lorg/codehaus/groovy/ast/expr/ExpressionTransformer;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    .line 145
    iget-object v0, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->foundArgs:Lorg/codehaus/groovy/ast/expr/Expression;

    if-nez v0, :cond_7

    iget-boolean v0, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->inPropertyExpression:Z

    if-eqz v0, :cond_7

    .line 146
    iput-object p1, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->foundArgs:Lorg/codehaus/groovy/ast/expr/Expression;

    :cond_7
    return-object p1

    .line 150
    :cond_8
    instance-of v0, p1, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    if-eqz v0, :cond_b

    .line 151
    invoke-virtual {p1, p0}, Lorg/codehaus/groovy/ast/expr/Expression;->transformExpression(Lorg/codehaus/groovy/ast/expr/ExpressionTransformer;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    .line 152
    iget-object v1, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->foundConstant:Lorg/codehaus/groovy/ast/expr/Expression;

    if-nez v1, :cond_9

    iget-boolean v1, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->inPropertyExpression:Z

    if-eqz v1, :cond_9

    .line 153
    iput-object v0, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->foundConstant:Lorg/codehaus/groovy/ast/expr/Expression;

    .line 155
    :cond_9
    iget-boolean v1, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->inAnnotation:Z

    if-eqz v1, :cond_a

    instance-of p1, p1, Lorg/codehaus/groovy/ast/expr/AnnotationConstantExpression;

    if-eqz p1, :cond_a

    .line 156
    move-object p1, v0

    check-cast p1, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    .line 157
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/ConstantExpression;->getValue()Ljava/lang/Object;

    move-result-object v1

    instance-of v1, v1, Lorg/codehaus/groovy/ast/AnnotationNode;

    if-eqz v1, :cond_a

    .line 160
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/ConstantExpression;->getValue()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lorg/codehaus/groovy/ast/AnnotationNode;

    .line 161
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/AnnotationNode;->getMembers()Ljava/util/Map;

    move-result-object p1

    .line 162
    invoke-interface {p1}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    move-result-object p1

    invoke-interface {p1}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_a

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/util/Map$Entry;

    .line 163
    invoke-interface {v1}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lorg/codehaus/groovy/ast/expr/Expression;

    invoke-virtual {p0, v2}, Lorg/codehaus/groovy/control/StaticImportVisitor;->transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v2

    .line 164
    invoke-interface {v1, v2}, Ljava/util/Map$Entry;->setValue(Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    :cond_a
    return-object v0

    .line 171
    :cond_b
    invoke-virtual {p1, p0}, Lorg/codehaus/groovy/ast/expr/Expression;->transformExpression(Lorg/codehaus/groovy/ast/expr/ExpressionTransformer;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    return-object p1
.end method

.method protected transformBinaryExpression(Lorg/codehaus/groovy/ast/expr/BinaryExpression;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 5

    .line 199
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/BinaryExpression;->getOperation()Lorg/codehaus/groovy/syntax/Token;

    move-result-object v0

    invoke-virtual {v0}, Lorg/codehaus/groovy/syntax/Token;->getType()I

    move-result v0

    .line 201
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/BinaryExpression;->getRightExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v1

    invoke-virtual {p0, v1}, Lorg/codehaus/groovy/control/StaticImportVisitor;->transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v1

    .line 202
    invoke-virtual {p1, v1}, Lorg/codehaus/groovy/ast/expr/BinaryExpression;->setRightExpression(Lorg/codehaus/groovy/ast/expr/Expression;)V

    const/16 v2, 0x64

    if-ne v0, v2, :cond_0

    .line 204
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/BinaryExpression;->getLeftExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    instance-of v0, v0, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    if-eqz v0, :cond_0

    .line 205
    iget-boolean v0, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->inLeftExpression:Z

    const/4 v2, 0x1

    .line 206
    iput-boolean v2, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->inLeftExpression:Z

    .line 207
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/BinaryExpression;->getLeftExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v2

    invoke-virtual {p0, v2}, Lorg/codehaus/groovy/control/StaticImportVisitor;->transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v2

    .line 208
    iput-boolean v0, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->inLeftExpression:Z

    .line 209
    instance-of v0, v2, Lorg/codehaus/groovy/ast/expr/StaticMethodCallExpression;

    if-eqz v0, :cond_1

    .line 210
    check-cast v2, Lorg/codehaus/groovy/ast/expr/StaticMethodCallExpression;

    .line 211
    new-instance v0, Lorg/codehaus/groovy/ast/expr/StaticMethodCallExpression;

    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/expr/StaticMethodCallExpression;->getOwnerType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v3

    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/expr/StaticMethodCallExpression;->getMethod()Ljava/lang/String;

    move-result-object v4

    invoke-direct {v0, v3, v4, v1}, Lorg/codehaus/groovy/ast/expr/StaticMethodCallExpression;-><init>(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)V

    .line 212
    invoke-virtual {v0, v2}, Lorg/codehaus/groovy/ast/expr/StaticMethodCallExpression;->copyNodeMetaData(Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 213
    invoke-static {v0, p1}, Lorg/codehaus/groovy/control/StaticImportVisitor;->setSourcePosition(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)V

    return-object v0

    .line 217
    :cond_0
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/BinaryExpression;->getLeftExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/control/StaticImportVisitor;->transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v2

    .line 219
    :cond_1
    invoke-virtual {p1, v2}, Lorg/codehaus/groovy/ast/expr/BinaryExpression;->setLeftExpression(Lorg/codehaus/groovy/ast/expr/Expression;)V

    return-object p1
.end method

.method protected transformClosureExpression(Lorg/codehaus/groovy/ast/expr/ClosureExpression;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 6

    .line 344
    iget-boolean v0, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->inClosure:Z

    const/4 v1, 0x1

    .line 345
    iput-boolean v1, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->inClosure:Z

    .line 346
    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/ClosureUtils;->getParametersSafe(Lorg/codehaus/groovy/ast/expr/ClosureExpression;)[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v1

    array-length v2, v1

    const/4 v3, 0x0

    :goto_0
    if-ge v3, v2, :cond_1

    aget-object v4, v1, v3

    .line 347
    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/Parameter;->hasInitialExpression()Z

    move-result v5

    if-eqz v5, :cond_0

    .line 348
    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/Parameter;->getInitialExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v5

    invoke-virtual {p0, v5}, Lorg/codehaus/groovy/control/StaticImportVisitor;->transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v5

    invoke-virtual {v4, v5}, Lorg/codehaus/groovy/ast/Parameter;->setInitialExpression(Lorg/codehaus/groovy/ast/expr/Expression;)V

    :cond_0
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    .line 351
    :cond_1
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/ClosureExpression;->getCode()Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v1

    if-eqz v1, :cond_2

    .line 352
    invoke-virtual {v1, p0}, Lorg/codehaus/groovy/ast/stmt/Statement;->visit(Lorg/codehaus/groovy/ast/GroovyCodeVisitor;)V

    .line 353
    :cond_2
    iput-boolean v0, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->inClosure:Z

    return-object p1
.end method

.method protected transformConstructorCallExpression(Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 5

    .line 323
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;->isSpecialCall()Z

    move-result v0

    iput-boolean v0, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->inSpecialConstructorCall:Z

    .line 324
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;->getArguments()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    .line 325
    instance-of v1, v0, Lorg/codehaus/groovy/ast/expr/TupleExpression;

    const/4 v2, 0x0

    if-eqz v1, :cond_0

    .line 326
    check-cast v0, Lorg/codehaus/groovy/ast/expr/TupleExpression;

    .line 327
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/TupleExpression;->getExpressions()Ljava/util/List;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    const/4 v3, 0x1

    if-ne v1, v3, :cond_0

    .line 328
    invoke-virtual {v0, v2}, Lorg/codehaus/groovy/ast/expr/TupleExpression;->getExpression(I)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    .line 329
    instance-of v1, v0, Lorg/codehaus/groovy/ast/expr/NamedArgumentListExpression;

    if-eqz v1, :cond_0

    .line 330
    check-cast v0, Lorg/codehaus/groovy/ast/expr/NamedArgumentListExpression;

    .line 331
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/NamedArgumentListExpression;->getMapEntryExpressions()Ljava/util/List;

    move-result-object v0

    move v1, v2

    .line 332
    :goto_0
    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v3

    if-ge v1, v3, :cond_0

    .line 333
    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lorg/codehaus/groovy/ast/expr/MapEntryExpression;

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v4

    invoke-direct {p0, v3, v4}, Lorg/codehaus/groovy/control/StaticImportVisitor;->transformMapEntryExpression(Lorg/codehaus/groovy/ast/expr/MapEntryExpression;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v3

    check-cast v3, Lorg/codehaus/groovy/ast/expr/MapEntryExpression;

    invoke-interface {v0, v1, v3}, Ljava/util/List;->set(ILjava/lang/Object;)Ljava/lang/Object;

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 338
    :cond_0
    invoke-virtual {p1, p0}, Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;->transformExpression(Lorg/codehaus/groovy/ast/expr/ExpressionTransformer;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    .line 339
    iput-boolean v2, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->inSpecialConstructorCall:Z

    return-object p1
.end method

.method protected transformMethodCallExpression(Lorg/codehaus/groovy/ast/expr/MethodCallExpression;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 9

    .line 249
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->getObjectExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/control/StaticImportVisitor;->transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    .line 250
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->getMethod()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v1

    invoke-virtual {p0, v1}, Lorg/codehaus/groovy/control/StaticImportVisitor;->transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v1

    .line 251
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->getArguments()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v2

    invoke-virtual {p0, v2}, Lorg/codehaus/groovy/control/StaticImportVisitor;->transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v2

    .line 254
    iget-boolean v3, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->inSpecialConstructorCall:Z

    const/4 v4, 0x0

    const/4 v5, 0x1

    if-nez v3, :cond_1

    iget-object v3, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->currentMethod:Lorg/codehaus/groovy/ast/MethodNode;

    if-eqz v3, :cond_0

    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/MethodNode;->isStatic()Z

    move-result v3

    if-eqz v3, :cond_0

    goto :goto_0

    :cond_0
    move v3, v4

    goto :goto_1

    :cond_1
    :goto_0
    move v3, v5

    .line 256
    :goto_1
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->isImplicitThis()Z

    move-result v6

    if-eqz v6, :cond_4

    .line 257
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->getMethodAsString()Ljava/lang/String;

    move-result-object v6

    .line 258
    iget-object v7, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    if-eqz v3, :cond_2

    invoke-static {v7, v6, v2, v5}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->hasPossibleStaticMethod(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;Z)Z

    move-result v7

    goto :goto_2

    :cond_2
    invoke-virtual {v7, v6, v2}, Lorg/codehaus/groovy/ast/ClassNode;->tryFindPossibleMethod(Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/MethodNode;

    move-result-object v7

    if-eqz v7, :cond_3

    move v7, v5

    goto :goto_2

    :cond_3
    move v7, v4

    :goto_2
    if-nez v7, :cond_5

    .line 259
    iget-object v7, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v7}, Lorg/codehaus/groovy/ast/ClassNode;->getOuterClasses()Ljava/util/List;

    move-result-object v7

    invoke-interface {v7}, Ljava/util/List;->stream()Ljava/util/stream/Stream;

    move-result-object v7

    new-instance v8, Lorg/codehaus/groovy/control/StaticImportVisitor$$ExternalSyntheticLambda0;

    invoke-direct {v8, v6, v2}, Lorg/codehaus/groovy/control/StaticImportVisitor$$ExternalSyntheticLambda0;-><init>(Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)V

    invoke-interface {v7, v8}, Ljava/util/stream/Stream;->noneMatch(Ljava/util/function/Predicate;)Z

    move-result v6

    if-eqz v6, :cond_5

    .line 260
    invoke-direct {p0, v1, v2}, Lorg/codehaus/groovy/control/StaticImportVisitor;->findStaticMethodImportFromModule(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v6

    if-eqz v6, :cond_5

    .line 262
    invoke-static {v6, p1}, Lorg/codehaus/groovy/control/StaticImportVisitor;->setSourcePosition(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)V

    return-object v6

    :cond_4
    if-eqz v3, :cond_5

    .line 266
    invoke-static {v0}, Lorg/apache/groovy/ast/tools/ExpressionUtils;->isSuperExpression(Lorg/codehaus/groovy/ast/expr/Expression;)Z

    move-result v6

    if-eqz v6, :cond_5

    .line 267
    new-instance v0, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    new-instance v3, Lorg/codehaus/groovy/ast/expr/ClassExpression;

    iget-object v4, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/ClassNode;->getSuperClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v4

    invoke-direct {v3, v4}, Lorg/codehaus/groovy/ast/expr/ClassExpression;-><init>(Lorg/codehaus/groovy/ast/ClassNode;)V

    invoke-direct {v0, v3, v1, v2}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;-><init>(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)V

    .line 268
    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/ast/expr/Expression;->setSourcePosition(Lorg/codehaus/groovy/ast/ASTNode;)V

    return-object v0

    .line 272
    :cond_5
    instance-of v6, v1, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    if-eqz v6, :cond_b

    move-object v6, v1

    check-cast v6, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    invoke-virtual {v6}, Lorg/codehaus/groovy/ast/expr/ConstantExpression;->getValue()Ljava/lang/Object;

    move-result-object v7

    instance-of v7, v7, Ljava/lang/String;

    if-eqz v7, :cond_b

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->isImplicitThis()Z

    move-result v7

    if-nez v7, :cond_6

    invoke-static {v0}, Lorg/apache/groovy/ast/tools/ExpressionUtils;->isThisOrSuper(Lorg/codehaus/groovy/ast/expr/Expression;)Z

    move-result v7

    if-eqz v7, :cond_b

    .line 273
    :cond_6
    invoke-virtual {v6}, Lorg/codehaus/groovy/ast/expr/ConstantExpression;->getValue()Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Ljava/lang/String;

    if-nez v3, :cond_7

    .line 275
    iget-object v3, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v3, v6, v2}, Lorg/codehaus/groovy/ast/ClassNode;->hasPossibleMethod(Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Z

    move-result v3

    if-eqz v3, :cond_7

    move v4, v5

    .line 277
    :cond_7
    new-instance v3, Lorg/codehaus/groovy/control/StaticImportVisitor$$ExternalSyntheticLambda1;

    invoke-direct {v3, v6, v2}, Lorg/codehaus/groovy/control/StaticImportVisitor$$ExternalSyntheticLambda1;-><init>(Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)V

    .line 289
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->isImplicitThis()Z

    move-result v5

    if-eqz v5, :cond_b

    .line 290
    iget-object v5, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {v5}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->isInnerClass(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v5

    if-eqz v5, :cond_9

    .line 291
    iget-boolean v5, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->inSpecialConstructorCall:Z

    if-eqz v5, :cond_b

    if-nez v4, :cond_b

    .line 293
    iget-object v4, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/ClassNode;->getOuterClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v4

    invoke-virtual {v4, v6, v2}, Lorg/codehaus/groovy/ast/ClassNode;->hasPossibleMethod(Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Z

    move-result v4

    if-eqz v4, :cond_8

    .line 294
    new-instance v0, Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    new-instance v3, Lorg/codehaus/groovy/ast/expr/ClassExpression;

    iget-object v4, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/ClassNode;->getOuterClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v4

    invoke-direct {v3, v4}, Lorg/codehaus/groovy/ast/expr/ClassExpression;-><init>(Lorg/codehaus/groovy/ast/ClassNode;)V

    new-instance v4, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    const-string v5, "this"

    invoke-direct {v4, v5}, Lorg/codehaus/groovy/ast/expr/ConstantExpression;-><init>(Ljava/lang/Object;)V

    invoke-direct {v0, v3, v4}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;-><init>(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)V

    goto :goto_3

    .line 295
    :cond_8
    iget-object v4, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/ClassNode;->getOuterClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v4

    invoke-interface {v3, v4}, Ljava/util/function/Predicate;->test(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_b

    .line 296
    new-instance v0, Lorg/codehaus/groovy/ast/expr/StaticMethodCallExpression;

    iget-object v1, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/ClassNode;->getOuterClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    invoke-direct {v0, v1, v6, v2}, Lorg/codehaus/groovy/ast/expr/StaticMethodCallExpression;-><init>(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)V

    .line 297
    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/ast/expr/Expression;->setSourcePosition(Lorg/codehaus/groovy/ast/ASTNode;)V

    return-object v0

    .line 301
    :cond_9
    iget-boolean v5, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->inSpecialConstructorCall:Z

    if-nez v5, :cond_a

    iget-boolean v5, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->inClosure:Z

    if-nez v5, :cond_b

    if-nez v4, :cond_b

    const-string v4, "call"

    invoke-virtual {v6, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v4

    if-nez v4, :cond_b

    .line 303
    :cond_a
    iget-object v4, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-interface {v3, v4}, Ljava/util/function/Predicate;->test(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_b

    .line 304
    new-instance v0, Lorg/codehaus/groovy/ast/expr/StaticMethodCallExpression;

    iget-object v1, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-direct {v0, v1, v6, v2}, Lorg/codehaus/groovy/ast/expr/StaticMethodCallExpression;-><init>(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)V

    .line 305
    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/ast/expr/Expression;->setSourcePosition(Lorg/codehaus/groovy/ast/ASTNode;)V

    return-object v0

    .line 312
    :cond_b
    :goto_3
    new-instance v3, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    invoke-direct {v3, v0, v1, v2}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;-><init>(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)V

    .line 313
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->getGenericsTypes()[Lorg/codehaus/groovy/ast/GenericsType;

    move-result-object v0

    invoke-virtual {v3, v0}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setGenericsTypes([Lorg/codehaus/groovy/ast/GenericsType;)V

    .line 314
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->getMethodTarget()Lorg/codehaus/groovy/ast/MethodNode;

    move-result-object v0

    invoke-virtual {v3, v0}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setMethodTarget(Lorg/codehaus/groovy/ast/MethodNode;)V

    .line 315
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->isImplicitThis()Z

    move-result v0

    invoke-virtual {v3, v0}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setImplicitThis(Z)V

    .line 316
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->isSpreadSafe()Z

    move-result v0

    invoke-virtual {v3, v0}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setSpreadSafe(Z)V

    .line 317
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->isSafe()Z

    move-result v0

    invoke-virtual {v3, v0}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setSafe(Z)V

    .line 318
    invoke-virtual {v3, p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setSourcePosition(Lorg/codehaus/groovy/ast/ASTNode;)V

    return-object v3
.end method

.method protected transformPropertyExpression(Lorg/codehaus/groovy/ast/expr/PropertyExpression;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 6

    .line 358
    iget-object v0, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->currentMethod:Lorg/codehaus/groovy/ast/MethodNode;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/MethodNode;->isStatic()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 359
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->getObjectExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    invoke-static {v0}, Lorg/apache/groovy/ast/tools/ExpressionUtils;->isSuperExpression(Lorg/codehaus/groovy/ast/expr/Expression;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 360
    new-instance v0, Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    new-instance v1, Lorg/codehaus/groovy/ast/expr/ClassExpression;

    iget-object v2, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    .line 361
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/ClassNode;->getUnresolvedSuperClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v2

    invoke-direct {v1, v2}, Lorg/codehaus/groovy/ast/expr/ClassExpression;-><init>(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 362
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->getProperty()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v2

    invoke-virtual {p0, v2}, Lorg/codehaus/groovy/control/StaticImportVisitor;->transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v2

    invoke-direct {v0, v1, v2}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;-><init>(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)V

    .line 364
    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->setSourcePosition(Lorg/codehaus/groovy/ast/ASTNode;)V

    return-object v0

    .line 368
    :cond_0
    iget-boolean v0, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->inPropertyExpression:Z

    .line 369
    iget-object v1, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->foundConstant:Lorg/codehaus/groovy/ast/expr/Expression;

    .line 370
    iget-object v2, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->foundArgs:Lorg/codehaus/groovy/ast/expr/Expression;

    const/4 v3, 0x1

    .line 371
    iput-boolean v3, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->inPropertyExpression:Z

    const/4 v3, 0x0

    .line 372
    iput-object v3, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->foundConstant:Lorg/codehaus/groovy/ast/expr/Expression;

    .line 373
    iput-object v3, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->foundArgs:Lorg/codehaus/groovy/ast/expr/Expression;

    .line 374
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->getObjectExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v3

    invoke-virtual {p0, v3}, Lorg/codehaus/groovy/control/StaticImportVisitor;->transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v3

    .line 375
    iget-object v4, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->foundArgs:Lorg/codehaus/groovy/ast/expr/Expression;

    if-eqz v4, :cond_1

    iget-object v4, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->foundConstant:Lorg/codehaus/groovy/ast/expr/Expression;

    if-eqz v4, :cond_1

    .line 376
    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/expr/Expression;->getText()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v4}, Ljava/lang/String;->trim()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v4}, Ljava/lang/String;->isEmpty()Z

    move-result v4

    if-nez v4, :cond_1

    instance-of v4, v3, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    if-eqz v4, :cond_1

    move-object v4, v3

    check-cast v4, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    .line 378
    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->isImplicitThis()Z

    move-result v4

    if-eqz v4, :cond_1

    .line 379
    iget-object v4, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->foundConstant:Lorg/codehaus/groovy/ast/expr/Expression;

    iget-object v5, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->foundArgs:Lorg/codehaus/groovy/ast/expr/Expression;

    invoke-direct {p0, v4, v5}, Lorg/codehaus/groovy/control/StaticImportVisitor;->findStaticMethodImportFromModule(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v4

    if-eqz v4, :cond_1

    .line 382
    invoke-virtual {v4, p1}, Lorg/codehaus/groovy/ast/expr/Expression;->setSourcePosition(Lorg/codehaus/groovy/ast/ASTNode;)V

    move-object v3, v4

    .line 385
    :cond_1
    iput-boolean v0, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->inPropertyExpression:Z

    .line 386
    iput-object v1, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->foundConstant:Lorg/codehaus/groovy/ast/expr/Expression;

    .line 387
    iput-object v2, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->foundArgs:Lorg/codehaus/groovy/ast/expr/Expression;

    .line 389
    invoke-virtual {p1, v3}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->setObjectExpression(Lorg/codehaus/groovy/ast/expr/Expression;)V

    return-object p1
.end method

.method protected transformVariableExpression(Lorg/codehaus/groovy/ast/expr/VariableExpression;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 4

    .line 224
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->getAccessedVariable()Lorg/codehaus/groovy/ast/Variable;

    move-result-object v0

    .line 225
    instance-of v1, v0, Lorg/codehaus/groovy/ast/DynamicVariable;

    if-eqz v1, :cond_1

    .line 226
    invoke-interface {v0}, Lorg/codehaus/groovy/ast/Variable;->getName()Ljava/lang/String;

    move-result-object v0

    invoke-direct {p0, v0}, Lorg/codehaus/groovy/control/StaticImportVisitor;->findStaticFieldOrPropertyAccessorImportFromModule(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    if-eqz v0, :cond_2

    .line 228
    invoke-static {v0, p1}, Lorg/codehaus/groovy/control/StaticImportVisitor;->setSourcePosition(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)V

    .line 229
    iget-boolean p1, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->inAnnotation:Z

    if-eqz p1, :cond_0

    .line 230
    invoke-static {v0}, Lorg/apache/groovy/ast/tools/ExpressionUtils;->transformInlineConstants(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    :cond_0
    return-object v0

    .line 234
    :cond_1
    instance-of v1, v0, Lorg/codehaus/groovy/ast/FieldNode;

    if-eqz v1, :cond_2

    .line 235
    iget-boolean v1, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->inSpecialConstructorCall:Z

    if-eqz v1, :cond_2

    .line 236
    move-object v1, v0

    check-cast v1, Lorg/codehaus/groovy/ast/FieldNode;

    .line 237
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/FieldNode;->getDeclaringClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v2

    .line 238
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/FieldNode;->isStatic()Z

    move-result v1

    if-eqz v1, :cond_2

    iget-object v1, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v1, v2}, Lorg/codehaus/groovy/ast/ClassNode;->isDerivedFrom(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v1

    if-eqz v1, :cond_2

    .line 239
    new-instance v1, Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    new-instance v3, Lorg/codehaus/groovy/ast/expr/ClassExpression;

    invoke-direct {v3, v2}, Lorg/codehaus/groovy/ast/expr/ClassExpression;-><init>(Lorg/codehaus/groovy/ast/ClassNode;)V

    invoke-interface {v0}, Lorg/codehaus/groovy/ast/Variable;->getName()Ljava/lang/String;

    move-result-object v0

    invoke-direct {v1, v3, v0}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;-><init>(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;)V

    .line 240
    invoke-static {v1, p1}, Lorg/codehaus/groovy/control/StaticImportVisitor;->setSourcePosition(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)V

    return-object v1

    :cond_2
    return-object p1
.end method

.method public visitAnnotations(Lorg/codehaus/groovy/ast/AnnotatedNode;)V
    .locals 2

    .line 115
    iget-boolean v0, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->inAnnotation:Z

    const/4 v1, 0x1

    .line 116
    iput-boolean v1, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->inAnnotation:Z

    .line 117
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeExpressionTransformer;->visitAnnotations(Lorg/codehaus/groovy/ast/AnnotatedNode;)V

    .line 118
    iput-boolean v0, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->inAnnotation:Z

    return-void
.end method

.method public visitClass(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/control/SourceUnit;)V
    .locals 0
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    .line 101
    iput-object p1, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    .line 102
    iput-object p2, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->sourceUnit:Lorg/codehaus/groovy/control/SourceUnit;

    .line 103
    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/control/StaticImportVisitor;->visitClass(Lorg/codehaus/groovy/ast/ClassNode;)V

    return-void
.end method

.method protected visitConstructorOrMethod(Lorg/codehaus/groovy/ast/MethodNode;Z)V
    .locals 0

    .line 108
    iput-object p1, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->currentMethod:Lorg/codehaus/groovy/ast/MethodNode;

    .line 109
    invoke-super {p0, p1, p2}, Lorg/codehaus/groovy/ast/ClassCodeExpressionTransformer;->visitConstructorOrMethod(Lorg/codehaus/groovy/ast/MethodNode;Z)V

    const/4 p1, 0x0

    .line 110
    iput-object p1, p0, Lorg/codehaus/groovy/control/StaticImportVisitor;->currentMethod:Lorg/codehaus/groovy/ast/MethodNode;

    return-void
.end method
