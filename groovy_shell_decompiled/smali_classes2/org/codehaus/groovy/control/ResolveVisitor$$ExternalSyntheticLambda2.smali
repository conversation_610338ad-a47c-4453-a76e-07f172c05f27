.class public final synthetic Lorg/codehaus/groovy/control/ResolveVisitor$$ExternalSyntheticLambda2;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Predicate;


# instance fields
.field public final synthetic f$0:Lorg/codehaus/groovy/control/ResolveVisitor;

.field public final synthetic f$1:Ljava/lang/String;

.field public final synthetic f$2:Lorg/codehaus/groovy/ast/ClassNode;


# direct methods
.method public synthetic constructor <init>(Lorg/codehaus/groovy/control/ResolveVisitor;Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/codehaus/groovy/control/ResolveVisitor$$ExternalSyntheticLambda2;->f$0:Lorg/codehaus/groovy/control/ResolveVisitor;

    iput-object p2, p0, Lorg/codehaus/groovy/control/ResolveVisitor$$ExternalSyntheticLambda2;->f$1:Ljava/lang/String;

    iput-object p3, p0, Lorg/codehaus/groovy/control/ResolveVisitor$$ExternalSyntheticLambda2;->f$2:Lorg/codehaus/groovy/ast/ClassNode;

    return-void
.end method


# virtual methods
.method public final test(Ljava/lang/Object;)Z
    .locals 3

    iget-object v0, p0, Lorg/codehaus/groovy/control/ResolveVisitor$$ExternalSyntheticLambda2;->f$0:Lorg/codehaus/groovy/control/ResolveVisitor;

    iget-object v1, p0, Lorg/codehaus/groovy/control/ResolveVisitor$$ExternalSyntheticLambda2;->f$1:Ljava/lang/String;

    iget-object v2, p0, Lorg/codehaus/groovy/control/ResolveVisitor$$ExternalSyntheticLambda2;->f$2:Lorg/codehaus/groovy/ast/ClassNode;

    check-cast p1, Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v0, v1, v2, p1}, Lorg/codehaus/groovy/control/ResolveVisitor;->lambda$setRedirect$1$org-codehaus-groovy-control-ResolveVisitor(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result p1

    return p1
.end method
