.class Lorg/codehaus/groovy/control/CompilationUnit$3;
.super Ljava/lang/Object;
.source "CompilationUnit.java"

# interfaces
.implements Lorg/codehaus/groovy/control/CompilationUnit$IPrimaryClassNodeOperation;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/codehaus/groovy/control/CompilationUnit;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lorg/codehaus/groovy/control/CompilationUnit;


# direct methods
.method constructor <init>(Lorg/codehaus/groovy/control/CompilationUnit;)V
    .locals 0

    .line 746
    iput-object p1, p0, Lorg/codehaus/groovy/control/CompilationUnit$3;->this$0:Lorg/codehaus/groovy/control/CompilationUnit;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method static synthetic lambda$call$0(Lgroovyjarjarasm/asm/ClassVisitor;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/control/CompilationUnit$ClassgenCallback;)V
    .locals 0

    .line 807
    invoke-interface {p2, p0, p1}, Lorg/codehaus/groovy/control/CompilationUnit$ClassgenCallback;->call(Lgroovyjarjarasm/asm/ClassVisitor;Lorg/codehaus/groovy/ast/ClassNode;)V

    return-void
.end method


# virtual methods
.method public call(Lorg/codehaus/groovy/control/SourceUnit;Lorg/codehaus/groovy/classgen/GeneratorContext;Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 6
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lorg/codehaus/groovy/control/CompilationFailedException;
        }
    .end annotation

    .line 749
    new-instance v0, Lorg/codehaus/groovy/control/OptimizerVisitor;

    iget-object v1, p0, Lorg/codehaus/groovy/control/CompilationUnit$3;->this$0:Lorg/codehaus/groovy/control/CompilationUnit;

    invoke-direct {v0, v1}, Lorg/codehaus/groovy/control/OptimizerVisitor;-><init>(Lorg/codehaus/groovy/control/CompilationUnit;)V

    invoke-virtual {v0, p3, p1}, Lorg/codehaus/groovy/control/OptimizerVisitor;->visitClass(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/control/SourceUnit;)V

    .line 754
    new-instance v0, Lorg/codehaus/groovy/classgen/Verifier;

    invoke-direct {v0}, Lorg/codehaus/groovy/classgen/Verifier;-><init>()V

    .line 756
    :try_start_0
    invoke-interface {v0, p3}, Lorg/codehaus/groovy/ast/GroovyClassVisitor;->visitClass(Lorg/codehaus/groovy/ast/ClassNode;)V
    :try_end_0
    .catch Lorg/codehaus/groovy/syntax/RuntimeParserException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception v0

    .line 758
    iget-object v1, p0, Lorg/codehaus/groovy/control/CompilationUnit$3;->this$0:Lorg/codehaus/groovy/control/CompilationUnit;

    invoke-virtual {v1}, Lorg/codehaus/groovy/control/CompilationUnit;->getErrorCollector()Lorg/codehaus/groovy/control/ErrorCollector;

    move-result-object v1

    new-instance v2, Lorg/codehaus/groovy/syntax/SyntaxException;

    invoke-virtual {v0}, Lorg/codehaus/groovy/syntax/RuntimeParserException;->getMessage()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v0}, Lorg/codehaus/groovy/syntax/RuntimeParserException;->getNode()Lorg/codehaus/groovy/ast/ASTNode;

    move-result-object v0

    invoke-direct {v2, v3, v0}, Lorg/codehaus/groovy/syntax/SyntaxException;-><init>(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    invoke-virtual {v1, v2, p1}, Lorg/codehaus/groovy/control/ErrorCollector;->addError(Lorg/codehaus/groovy/syntax/SyntaxException;Lorg/codehaus/groovy/control/SourceUnit;)V

    .line 761
    :goto_0
    new-instance v0, Lorg/codehaus/groovy/control/LabelVerifier;

    invoke-direct {v0, p1}, Lorg/codehaus/groovy/control/LabelVerifier;-><init>(Lorg/codehaus/groovy/control/SourceUnit;)V

    .line 762
    invoke-interface {v0, p3}, Lorg/codehaus/groovy/ast/GroovyClassVisitor;->visitClass(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 764
    new-instance v0, Lorg/codehaus/groovy/control/CompilationUnit$3$1;

    invoke-direct {v0, p0, p1}, Lorg/codehaus/groovy/control/CompilationUnit$3$1;-><init>(Lorg/codehaus/groovy/control/CompilationUnit$3;Lorg/codehaus/groovy/control/SourceUnit;)V

    .line 770
    invoke-interface {v0, p3}, Lorg/codehaus/groovy/ast/GroovyClassVisitor;->visitClass(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 772
    new-instance v0, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;

    invoke-direct {v0, p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;-><init>(Lorg/codehaus/groovy/control/SourceUnit;)V

    .line 773
    invoke-interface {v0, p3}, Lorg/codehaus/groovy/ast/GroovyClassVisitor;->visitClass(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 775
    new-instance v0, Lorg/codehaus/groovy/classgen/ExtendedVerifier;

    invoke-direct {v0, p1}, Lorg/codehaus/groovy/classgen/ExtendedVerifier;-><init>(Lorg/codehaus/groovy/control/SourceUnit;)V

    .line 776
    invoke-interface {v0, p3}, Lorg/codehaus/groovy/ast/GroovyClassVisitor;->visitClass(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 780
    iget-object v0, p0, Lorg/codehaus/groovy/control/CompilationUnit$3;->this$0:Lorg/codehaus/groovy/control/CompilationUnit;

    invoke-virtual {v0}, Lorg/codehaus/groovy/control/CompilationUnit;->getErrorCollector()Lorg/codehaus/groovy/control/ErrorCollector;

    move-result-object v0

    invoke-virtual {v0}, Lorg/codehaus/groovy/control/ErrorCollector;->failIfErrors()V

    .line 785
    iget-object v0, p0, Lorg/codehaus/groovy/control/CompilationUnit$3;->this$0:Lorg/codehaus/groovy/control/CompilationUnit;

    invoke-virtual {v0}, Lorg/codehaus/groovy/control/CompilationUnit;->createClassVisitor()Lgroovyjarjarasm/asm/ClassVisitor;

    move-result-object v0

    if-nez p1, :cond_0

    .line 787
    invoke-virtual {p3}, Lorg/codehaus/groovy/ast/ClassNode;->getModule()Lorg/codehaus/groovy/ast/ModuleNode;

    move-result-object v1

    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/ModuleNode;->getDescription()Ljava/lang/String;

    move-result-object v1

    goto :goto_1

    :cond_0
    invoke-virtual {p1}, Lorg/codehaus/groovy/control/SourceUnit;->getName()Ljava/lang/String;

    move-result-object v1

    :goto_1
    if-eqz v1, :cond_1

    const/16 v2, 0x5c

    .line 791
    invoke-virtual {v1, v2}, Ljava/lang/String;->lastIndexOf(I)I

    move-result v2

    const/16 v3, 0x2f

    invoke-virtual {v1, v3}, Ljava/lang/String;->lastIndexOf(I)I

    move-result v3

    invoke-static {v2, v3}, Ljava/lang/Math;->max(II)I

    move-result v2

    add-int/lit8 v2, v2, 0x1

    invoke-virtual {v1, v2}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object v1

    .line 797
    :cond_1
    new-instance v2, Lorg/codehaus/groovy/classgen/AsmClassGenerator;

    invoke-direct {v2, p1, p2, v0, v1}, Lorg/codehaus/groovy/classgen/AsmClassGenerator;-><init>(Lorg/codehaus/groovy/control/SourceUnit;Lorg/codehaus/groovy/classgen/GeneratorContext;Lgroovyjarjarasm/asm/ClassVisitor;Ljava/lang/String;)V

    .line 798
    invoke-interface {v2, p3}, Lorg/codehaus/groovy/ast/GroovyClassVisitor;->visitClass(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 800
    move-object v1, v0

    check-cast v1, Lgroovyjarjarasm/asm/ClassWriter;

    invoke-virtual {v1}, Lgroovyjarjarasm/asm/ClassWriter;->toByteArray()[B

    move-result-object v1

    .line 801
    iget-object v3, p0, Lorg/codehaus/groovy/control/CompilationUnit$3;->this$0:Lorg/codehaus/groovy/control/CompilationUnit;

    invoke-virtual {v3}, Lorg/codehaus/groovy/control/CompilationUnit;->getClasses()Ljava/util/List;

    move-result-object v3

    new-instance v4, Lorg/codehaus/groovy/tools/GroovyClass;

    invoke-virtual {p3}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v5

    invoke-direct {v4, v5, v1}, Lorg/codehaus/groovy/tools/GroovyClass;-><init>(Ljava/lang/String;[B)V

    invoke-interface {v3, v4}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 806
    iget-object v1, p0, Lorg/codehaus/groovy/control/CompilationUnit$3;->this$0:Lorg/codehaus/groovy/control/CompilationUnit;

    invoke-virtual {v1}, Lorg/codehaus/groovy/control/CompilationUnit;->getClassgenCallback()Lorg/codehaus/groovy/control/CompilationUnit$ClassgenCallback;

    move-result-object v1

    invoke-static {v1}, Ljava/util/Optional;->ofNullable(Ljava/lang/Object;)Ljava/util/Optional;

    move-result-object v1

    new-instance v3, Lorg/codehaus/groovy/control/CompilationUnit$3$$ExternalSyntheticLambda0;

    invoke-direct {v3, v0, p3}, Lorg/codehaus/groovy/control/CompilationUnit$3$$ExternalSyntheticLambda0;-><init>(Lgroovyjarjarasm/asm/ClassVisitor;Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 807
    invoke-virtual {v1, v3}, Ljava/util/Optional;->ifPresent(Ljava/util/function/Consumer;)V

    .line 812
    move-object p3, v2

    check-cast p3, Lorg/codehaus/groovy/classgen/AsmClassGenerator;

    invoke-virtual {v2}, Lorg/codehaus/groovy/classgen/AsmClassGenerator;->getInnerClasses()Ljava/util/LinkedList;

    move-result-object p3

    .line 813
    :goto_2
    invoke-virtual {p3}, Ljava/util/LinkedList;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_2

    .line 814
    iget-object v0, p0, Lorg/codehaus/groovy/control/CompilationUnit$3;->this$0:Lorg/codehaus/groovy/control/CompilationUnit;

    invoke-static {v0}, Lorg/codehaus/groovy/control/CompilationUnit;->access$000(Lorg/codehaus/groovy/control/CompilationUnit;)Lorg/codehaus/groovy/control/CompilationUnit$IPrimaryClassNodeOperation;

    move-result-object v0

    invoke-virtual {p3}, Ljava/util/LinkedList;->removeFirst()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/ast/ClassNode;

    invoke-interface {v0, p1, p2, v1}, Lorg/codehaus/groovy/control/CompilationUnit$IPrimaryClassNodeOperation;->call(Lorg/codehaus/groovy/control/SourceUnit;Lorg/codehaus/groovy/classgen/GeneratorContext;Lorg/codehaus/groovy/ast/ClassNode;)V

    goto :goto_2

    :cond_2
    return-void
.end method

.method public needSortedInput()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method
