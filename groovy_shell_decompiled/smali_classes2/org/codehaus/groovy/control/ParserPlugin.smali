.class public interface abstract Lorg/codehaus/groovy/control/ParserPlugin;
.super Ljava/lang/Object;
.source "ParserPlugin.java"


# direct methods
.method public static buildAST(Ljava/lang/CharSequence;Lorg/codehaus/groovy/control/CompilerConfiguration;Lgroovy/lang/GroovyClassLoader;Lorg/codehaus/groovy/control/ErrorCollector;)Lorg/codehaus/groovy/ast/ModuleNode;
    .locals 7
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lorg/codehaus/groovy/control/CompilationFailedException;
        }
    .end annotation

    .line 38
    new-instance v6, Lorg/codehaus/groovy/control/SourceUnit;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Script"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-static {}, Ljava/lang/System;->nanoTime()J

    move-result-wide v1

    invoke-virtual {v0, v1, v2}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ".groovy"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-interface {p0}, Ljava/lang/CharSequence;->toString()Ljava/lang/String;

    move-result-object v2

    move-object v0, v6

    move-object v3, p1

    move-object v4, p2

    move-object v5, p3

    invoke-direct/range {v0 .. v5}, Lorg/codehaus/groovy/control/SourceUnit;-><init>(Ljava/lang/String;Ljava/lang/String;Lorg/codehaus/groovy/control/CompilerConfiguration;Lgroovy/lang/GroovyClassLoader;Lorg/codehaus/groovy/control/ErrorCollector;)V

    .line 39
    invoke-virtual {v6}, Lorg/codehaus/groovy/control/SourceUnit;->parse()V

    .line 40
    invoke-virtual {v6}, Lorg/codehaus/groovy/control/SourceUnit;->completePhase()V

    .line 41
    invoke-virtual {v6}, Lorg/codehaus/groovy/control/SourceUnit;->nextPhase()V

    .line 42
    invoke-virtual {v6}, Lorg/codehaus/groovy/control/SourceUnit;->convert()V

    .line 43
    invoke-virtual {v6}, Lorg/codehaus/groovy/control/SourceUnit;->getAST()Lorg/codehaus/groovy/ast/ModuleNode;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public abstract buildAST(Lorg/codehaus/groovy/control/SourceUnit;Ljava/lang/ClassLoader;Lorg/codehaus/groovy/syntax/Reduction;)Lorg/codehaus/groovy/ast/ModuleNode;
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lorg/codehaus/groovy/syntax/ParserException;
        }
    .end annotation
.end method

.method public abstract parseCST(Lorg/codehaus/groovy/control/SourceUnit;Ljava/io/Reader;)Lorg/codehaus/groovy/syntax/Reduction;
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lorg/codehaus/groovy/control/CompilationFailedException;
        }
    .end annotation
.end method
