.class public Lorg/codehaus/groovy/control/ResolveVisitor;
.super Lorg/codehaus/groovy/ast/ClassCodeExpressionTransformer;
.source "ResolveVisitor.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/codehaus/groovy/control/ResolveVisitor$ConstructedClassWithPackage;,
        Lorg/codehaus/groovy/control/ResolveVisitor$ConstructedNestedClass;,
        Lorg/codehaus/groovy/control/ResolveVisitor$LowerCaseClass;
    }
.end annotation


# static fields
.field private static final BIGDECIMAL_STR:Ljava/lang/String; = "BigDecimal"

.field private static final BIGINTEGER_STR:Ljava/lang/String; = "BigInteger"

.field public static final DEFAULT_IMPORTS:[Ljava/lang/String;

.field private static final DEFAULT_IMPORT_CLASS_AND_PACKAGES_CACHE:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/util/Set<",
            "Ljava/lang/String;",
            ">;>;"
        }
    .end annotation
.end field

.field public static final EMPTY_STRING_ARRAY:[Ljava/lang/String;

.field public static final QUESTION_MARK:Ljava/lang/String; = "?"


# instance fields
.field private checkingVariableTypeInDeclaration:Z

.field private classNodeResolver:Lorg/codehaus/groovy/control/ClassNodeResolver;

.field private final compilationUnit:Lorg/codehaus/groovy/control/CompilationUnit;

.field private currImportNode:Lorg/codehaus/groovy/ast/ImportNode;

.field private currentClass:Lorg/codehaus/groovy/ast/ClassNode;

.field private currentMethod:Lorg/codehaus/groovy/ast/MethodNode;

.field private currentScope:Lorg/codehaus/groovy/ast/VariableScope;

.field private final fieldTypesChecked:Ljava/util/Set;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Set<",
            "Lorg/codehaus/groovy/ast/FieldNode;",
            ">;"
        }
    .end annotation
.end field

.field private genericParameterNames:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Lorg/codehaus/groovy/ast/GenericsType$GenericsTypeName;",
            "Lorg/codehaus/groovy/ast/GenericsType;",
            ">;"
        }
    .end annotation
.end field

.field private inClosure:Z

.field private inPropertyExpression:Z

.field private isTopLevelProperty:Z

.field private final possibleOuterClassNodeMap:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            ">;"
        }
    .end annotation
.end field

.field private source:Lorg/codehaus/groovy/control/SourceUnit;


# direct methods
.method static constructor <clinit>()V
    .locals 6

    const-string v0, "java.lang."

    const-string v1, "java.util."

    const-string v2, "java.io."

    const-string v3, "java.net."

    const-string v4, "groovy.lang."

    const-string v5, "groovy.util."

    .line 104
    filled-new-array/range {v0 .. v5}, [Ljava/lang/String;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/control/ResolveVisitor;->DEFAULT_IMPORTS:[Ljava/lang/String;

    const/4 v1, 0x0

    new-array v1, v1, [Ljava/lang/String;

    .line 108
    sput-object v1, Lorg/codehaus/groovy/control/ResolveVisitor;->EMPTY_STRING_ARRAY:[Ljava/lang/String;

    .line 645
    new-instance v1, Lorg/codehaus/groovy/runtime/memoize/UnlimitedConcurrentCache;

    invoke-direct {v1}, Lorg/codehaus/groovy/runtime/memoize/UnlimitedConcurrentCache;-><init>()V

    sput-object v1, Lorg/codehaus/groovy/control/ResolveVisitor;->DEFAULT_IMPORT_CLASS_AND_PACKAGES_CACHE:Ljava/util/Map;

    .line 647
    invoke-static {}, Lorg/codehaus/groovy/vmplugin/VMPluginFactory;->getPlugin()Lorg/codehaus/groovy/vmplugin/VMPlugin;

    move-result-object v2

    invoke-interface {v2, v0}, Lorg/codehaus/groovy/vmplugin/VMPlugin;->getDefaultImportClasses([Ljava/lang/String;)Ljava/util/Map;

    move-result-object v0

    invoke-interface {v1, v0}, Ljava/util/Map;->putAll(Ljava/util/Map;)V

    return-void
.end method

.method public constructor <init>(Lorg/codehaus/groovy/control/CompilationUnit;)V
    .locals 1

    .line 250
    invoke-direct {p0}, Lorg/codehaus/groovy/ast/ClassCodeExpressionTransformer;-><init>()V

    const/4 v0, 0x1

    .line 115
    iput-boolean v0, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->isTopLevelProperty:Z

    .line 119
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    iput-object v0, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->possibleOuterClassNodeMap:Ljava/util/Map;

    .line 120
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    iput-object v0, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->genericParameterNames:Ljava/util/Map;

    .line 121
    new-instance v0, Ljava/util/HashSet;

    invoke-direct {v0}, Ljava/util/HashSet;-><init>()V

    iput-object v0, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->fieldTypesChecked:Ljava/util/Set;

    .line 251
    iput-object p1, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->compilationUnit:Lorg/codehaus/groovy/control/CompilationUnit;

    .line 253
    new-instance p1, Lorg/codehaus/groovy/control/ClassNodeResolver;

    invoke-direct {p1}, Lorg/codehaus/groovy/control/ClassNodeResolver;-><init>()V

    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/control/ResolveVisitor;->setClassNodeResolver(Lorg/codehaus/groovy/control/ClassNodeResolver;)V

    return-void
.end method

.method private ambiguousClass(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)V
    .locals 2

    .line 689
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 690
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "reference to "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p3

    const-string v0, " is ambiguous, both class "

    invoke-virtual {p3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p3

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p3

    const-string v0, " and "

    invoke-virtual {p3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p3

    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p3, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string p3, " match"

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p2, p1}, Lorg/codehaus/groovy/control/ResolveVisitor;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    goto :goto_0

    .line 692
    :cond_0
    invoke-virtual {p1, p2}, Lorg/codehaus/groovy/ast/ClassNode;->setRedirect(Lorg/codehaus/groovy/ast/ClassNode;)V

    :goto_0
    return-void
.end method

.method private checkAnnotationMemberValue(Lorg/codehaus/groovy/ast/expr/Expression;)V
    .locals 2

    .line 1387
    instance-of v0, p1, Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    if-eqz v0, :cond_0

    .line 1388
    check-cast p1, Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    .line 1389
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->getObjectExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    instance-of v0, v0, Lorg/codehaus/groovy/ast/expr/ClassExpression;

    if-nez v0, :cond_1

    .line 1390
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "unable to find class \'"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->getText()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "\' for annotation attribute constant"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->getObjectExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    invoke-virtual {p0, v0, p1}, Lorg/codehaus/groovy/control/ResolveVisitor;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    goto :goto_1

    .line 1392
    :cond_0
    instance-of v0, p1, Lorg/codehaus/groovy/ast/expr/ListExpression;

    if-eqz v0, :cond_1

    .line 1393
    check-cast p1, Lorg/codehaus/groovy/ast/expr/ListExpression;

    .line 1394
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/ListExpression;->getExpressions()Ljava/util/List;

    move-result-object p1

    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_1

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/ast/expr/Expression;

    .line 1395
    invoke-direct {p0, v0}, Lorg/codehaus/groovy/control/ResolveVisitor;->checkAnnotationMemberValue(Lorg/codehaus/groovy/ast/expr/Expression;)V

    goto :goto_0

    :cond_1
    :goto_1
    return-void
.end method

.method private checkCyclicInheritance(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 4

    .line 1491
    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/ClassNode;->redirect()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    if-eq v0, p1, :cond_5

    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/ClassNode;->getOuterClasses()Ljava/util/List;

    move-result-object v0

    invoke-interface {v0, p1}, Ljava/util/List;->contains(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    goto/16 :goto_2

    .line 1493
    :cond_0
    sget-object v0, Lorg/codehaus/groovy/ast/ClassHelper;->OBJECT_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    if-eq p2, v0, :cond_6

    .line 1494
    new-instance v0, Ljava/util/HashSet;

    invoke-direct {v0}, Ljava/util/HashSet;-><init>()V

    .line 1495
    sget-object v1, Lorg/codehaus/groovy/ast/ClassHelper;->OBJECT_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-interface {v0, v1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    const/4 v1, 0x0

    .line 1496
    invoke-interface {v0, v1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 1498
    new-instance v1, Ljava/util/LinkedList;

    invoke-direct {v1}, Ljava/util/LinkedList;-><init>()V

    .line 1499
    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/ClassNode;->getInterfaces()[Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v2

    invoke-static {v1, v2}, Ljava/util/Collections;->addAll(Ljava/util/Collection;[Ljava/lang/Object;)Z

    .line 1500
    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/ClassNode;->getUnresolvedSuperClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/util/LinkedList;->add(Ljava/lang/Object;)Z

    .line 1501
    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/ClassNode;->getOuterClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/util/LinkedList;->add(Ljava/lang/Object;)Z

    .line 1503
    :cond_1
    invoke-virtual {v1}, Ljava/util/LinkedList;->poll()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lorg/codehaus/groovy/ast/ClassNode;

    .line 1504
    invoke-interface {v0, v2}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    move-result v3

    if-nez v3, :cond_2

    goto :goto_1

    .line 1505
    :cond_2
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/ClassNode;->redirect()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v3

    if-ne v3, p1, :cond_4

    move-object v0, p2

    .line 1506
    :goto_0
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->getOuterClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    if-eqz v1, :cond_3

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->getOuterClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    goto :goto_0

    .line 1507
    :cond_3
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "Cycle detected: a cycle exists in the type hierarchy between "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v1, " and "

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/control/ResolveVisitor;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    return-void

    .line 1510
    :cond_4
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/ClassNode;->getInterfaces()[Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v3

    invoke-static {v1, v3}, Ljava/util/Collections;->addAll(Ljava/util/Collection;[Ljava/lang/Object;)Z

    .line 1511
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/ClassNode;->getUnresolvedSuperClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v3

    invoke-virtual {v1, v3}, Ljava/util/LinkedList;->add(Ljava/lang/Object;)Z

    .line 1512
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/ClassNode;->getOuterClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/util/LinkedList;->add(Ljava/lang/Object;)Z

    .line 1513
    :goto_1
    invoke-virtual {v1}, Ljava/util/LinkedList;->isEmpty()Z

    move-result v2

    if-eqz v2, :cond_1

    goto :goto_3

    .line 1492
    :cond_5
    :goto_2
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Cycle detected: the type "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getUnresolvedName()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, " cannot extend/implement itself or one of its own member types"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/control/ResolveVisitor;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    :cond_6
    :goto_3
    return-void
.end method

.method private checkThisAndSuperAsPropertyAccess(Lorg/codehaus/groovy/ast/expr/PropertyExpression;)V
    .locals 4

    .line 1073
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->isImplicitThis()Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    .line 1074
    :cond_0
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->getPropertyAsString()Ljava/lang/String;

    move-result-object v0

    if-nez v0, :cond_1

    return-void

    :cond_1
    const-string v1, "this"

    .line 1076
    invoke-virtual {v0, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    const-string v2, "super"

    if-nez v1, :cond_2

    invoke-virtual {v0, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_2

    return-void

    .line 1078
    :cond_2
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->getObjectExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v1

    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/expr/Expression;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    .line 1079
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->getObjectExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v3

    instance-of v3, v3, Lorg/codehaus/groovy/ast/expr/ClassExpression;

    if-eqz v3, :cond_a

    .line 1080
    iget-object v3, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    instance-of v3, v3, Lorg/codehaus/groovy/ast/InnerClassNode;

    if-nez v3, :cond_3

    invoke-static {v1}, Lorg/codehaus/groovy/transform/trait/Traits;->isTrait(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v3

    if-nez v3, :cond_3

    const-string v0, "The usage of \'Class.this\' and \'Class.super\' is only allowed in nested/inner classes."

    .line 1081
    invoke-virtual {p0, v0, p1}, Lorg/codehaus/groovy/control/ResolveVisitor;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    return-void

    .line 1084
    :cond_3
    iget-object v3, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->currentScope:Lorg/codehaus/groovy/ast/VariableScope;

    if-eqz v3, :cond_4

    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/VariableScope;->isInStaticContext()Z

    move-result v3

    if-nez v3, :cond_4

    invoke-static {v1}, Lorg/codehaus/groovy/transform/trait/Traits;->isTrait(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v3

    if-eqz v3, :cond_4

    invoke-virtual {v2, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_4

    invoke-direct {p0, v1}, Lorg/codehaus/groovy/control/ResolveVisitor;->directlyImplementsTrait(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v0

    if-eqz v0, :cond_4

    return-void

    .line 1087
    :cond_4
    iget-object v0, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    :goto_0
    if-eqz v0, :cond_6

    .line 1089
    invoke-virtual {v0, v1}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_5

    goto :goto_1

    .line 1090
    :cond_5
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->getOuterClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    goto :goto_0

    :cond_6
    :goto_1
    if-nez v0, :cond_7

    .line 1093
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "The class \'"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "\' needs to be an outer class of \'"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v1, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    .line 1094
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "\' when using \'.this\' or \'.super\'."

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    .line 1093
    invoke-virtual {p0, v0, p1}, Lorg/codehaus/groovy/control/ResolveVisitor;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 1096
    :cond_7
    iget-object v0, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->getModifiers()I

    move-result v0

    and-int/lit8 v0, v0, 0x8

    if-nez v0, :cond_8

    return-void

    .line 1097
    :cond_8
    iget-object v0, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->currentScope:Lorg/codehaus/groovy/ast/VariableScope;

    if-eqz v0, :cond_9

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/VariableScope;->isInStaticContext()Z

    move-result v0

    if-nez v0, :cond_9

    return-void

    .line 1098
    :cond_9
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "The usage of \'Class.this\' and \'Class.super\' within static nested class \'"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v1, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    .line 1099
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "\' is not allowed in a static context."

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    .line 1098
    invoke-virtual {p0, v0, p1}, Lorg/codehaus/groovy/control/ResolveVisitor;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    :cond_a
    return-void
.end method

.method private static correctClassClassChain(Lorg/codehaus/groovy/ast/expr/PropertyExpression;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 5

    .line 976
    new-instance v0, Ljava/util/LinkedList;

    invoke-direct {v0}, Ljava/util/LinkedList;-><init>()V

    move-object v1, p0

    :goto_0
    if-eqz v1, :cond_2

    .line 979
    instance-of v2, v1, Lorg/codehaus/groovy/ast/expr/ClassExpression;

    if-eqz v2, :cond_0

    .line 980
    check-cast v1, Lorg/codehaus/groovy/ast/expr/ClassExpression;

    goto :goto_1

    .line 982
    :cond_0
    invoke-virtual {v1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v2

    const-class v3, Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    if-eq v2, v3, :cond_1

    return-object p0

    .line 985
    :cond_1
    invoke-virtual {v0, v1}, Ljava/util/LinkedList;->addFirst(Ljava/lang/Object;)V

    .line 978
    check-cast v1, Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->getObjectExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v1

    goto :goto_0

    :cond_2
    const/4 v1, 0x0

    :goto_1
    if-nez v1, :cond_3

    return-object p0

    .line 989
    :cond_3
    invoke-virtual {v0}, Ljava/util/LinkedList;->isEmpty()Z

    move-result v2

    if-eqz v2, :cond_4

    return-object p0

    .line 990
    :cond_4
    invoke-virtual {v0}, Ljava/util/LinkedList;->removeFirst()Ljava/lang/Object;

    move-result-object v2

    .line 991
    invoke-virtual {v2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v3

    const-class v4, Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    if-eq v3, v4, :cond_5

    return-object p0

    .line 992
    :cond_5
    check-cast v2, Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    .line 993
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->getPropertyAsString()Ljava/lang/String;

    move-result-object v3

    if-eqz v3, :cond_9

    const-string v4, "class"

    .line 994
    invoke-virtual {v3, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-nez v3, :cond_6

    goto :goto_2

    .line 996
    :cond_6
    invoke-virtual {v1, v2}, Lorg/codehaus/groovy/ast/expr/ClassExpression;->setSourcePosition(Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 997
    invoke-virtual {v0}, Ljava/util/LinkedList;->isEmpty()Z

    move-result v2

    if-eqz v2, :cond_7

    return-object v1

    .line 998
    :cond_7
    invoke-virtual {v0}, Ljava/util/LinkedList;->removeFirst()Ljava/lang/Object;

    move-result-object v0

    .line 999
    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v2

    const-class v3, Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    if-eq v2, v3, :cond_8

    return-object p0

    .line 1000
    :cond_8
    check-cast v0, Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    .line 1002
    invoke-virtual {v0, v1}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->setObjectExpression(Lorg/codehaus/groovy/ast/expr/Expression;)V

    :cond_9
    :goto_2
    return-object p0
.end method

.method private directlyImplementsTrait(Lorg/codehaus/groovy/ast/ClassNode;)Z
    .locals 4

    .line 1060
    iget-object v0, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->getInterfaces()[Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    if-nez v0, :cond_0

    .line 1062
    iget-object v0, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->getSuperClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result p1

    return p1

    .line 1064
    :cond_0
    array-length v1, v0

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v1, :cond_2

    aget-object v3, v0, v2

    .line 1065
    invoke-virtual {v3, p1}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_1

    const/4 p1, 0x1

    return p1

    :cond_1
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 1069
    :cond_2
    iget-object v0, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->getSuperClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result p1

    return p1
.end method

.method private static findHierClasses(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/util/Map;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            ")",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            ">;"
        }
    .end annotation

    .line 1689
    new-instance v0, Ljava/util/LinkedHashMap;

    invoke-direct {v0}, Ljava/util/LinkedHashMap;-><init>()V

    .line 1690
    :goto_0
    sget-object v1, Lorg/codehaus/groovy/ast/ClassHelper;->OBJECT_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    if-eq p0, v1, :cond_1

    if-eqz p0, :cond_1

    .line 1691
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/util/Map;->containsKey(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_0

    goto :goto_1

    .line 1692
    :cond_0
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-interface {v0, v1, p0}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1690
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/ClassNode;->getSuperClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p0

    goto :goto_0

    :cond_1
    :goto_1
    return-object v0
.end method

.method private findPossibleOuterClassNodeForNonStaticInnerClassInstantiation(Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;)V
    .locals 2

    .line 1251
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;->getArguments()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    .line 1252
    instance-of v1, v0, Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    if-eqz v1, :cond_0

    .line 1253
    check-cast v0, Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    .line 1254
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;->getExpressions()Ljava/util/List;

    move-result-object v0

    .line 1255
    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v1

    if-nez v1, :cond_0

    const/4 v1, 0x0

    .line 1256
    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/ast/expr/Expression;

    .line 1258
    instance-of v1, v0, Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;

    if-eqz v1, :cond_0

    .line 1259
    check-cast v0, Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;

    .line 1260
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    .line 1261
    iget-object v1, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->possibleOuterClassNodeMap:Ljava/util/Map;

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    invoke-interface {v1, p1, v0}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_0
    return-void
.end method

.method private fixDeclaringClass(Lorg/codehaus/groovy/ast/expr/DeclarationExpression;)V
    .locals 1

    .line 1313
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/DeclarationExpression;->getDeclaringClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    if-nez v0, :cond_0

    iget-object v0, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->currentMethod:Lorg/codehaus/groovy/ast/MethodNode;

    if-eqz v0, :cond_0

    .line 1314
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/MethodNode;->getDeclaringClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    invoke-virtual {p1, v0}, Lorg/codehaus/groovy/ast/expr/DeclarationExpression;->setDeclaringClass(Lorg/codehaus/groovy/ast/ClassNode;)V

    :cond_0
    return-void
.end method

.method private static getDescription(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/lang/String;
    .locals 2

    .line 1268
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/ClassNode;->isInterface()Z

    move-result v1

    if-eqz v1, :cond_0

    const-string v1, "interface"

    goto :goto_0

    :cond_0
    const-string v1, "class"

    :goto_0
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, " \'"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object p0

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    const-string v0, "\'"

    invoke-virtual {p0, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method private isRepeatable(Ljava/lang/Class;)Z
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Class<",
            "*>;)Z"
        }
    .end annotation

    .line 1359
    invoke-virtual {p1}, Ljava/lang/Class;->getAnnotations()[Ljava/lang/annotation/Annotation;

    move-result-object p1

    .line 1360
    array-length v0, p1

    const/4 v1, 0x0

    move v2, v1

    :goto_0
    if-ge v2, v0, :cond_1

    aget-object v3, p1, v2

    .line 1361
    invoke-interface {v3}, Ljava/lang/annotation/Annotation;->annotationType()Ljava/lang/Class;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v3

    const-string v4, "java.lang.annotation.Repeatable"

    invoke-virtual {v3, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_0

    const/4 p1, 0x1

    return p1

    :cond_0
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_1
    return v1
.end method

.method private static isVisibleNestedClass(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;)Z
    .locals 2

    .line 1054
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/ClassNode;->getModifiers()I

    move-result v0

    .line 1055
    invoke-static {v0}, Ljava/lang/reflect/Modifier;->isPublic(I)Z

    move-result v1

    if-nez v1, :cond_1

    invoke-static {v0}, Ljava/lang/reflect/Modifier;->isProtected(I)Z

    move-result v1

    if-nez v1, :cond_1

    .line 1056
    invoke-static {v0}, Ljava/lang/reflect/Modifier;->isPrivate(I)Z

    move-result v0

    if-nez v0, :cond_0

    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/ClassNode;->getPackageName()Ljava/lang/String;

    move-result-object p0

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getPackageName()Ljava/lang/String;

    move-result-object p1

    invoke-static {p0, p1}, Ljava/util/Objects;->equals(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p0

    if-eqz p0, :cond_0

    goto :goto_0

    :cond_0
    const/4 p0, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 p0, 0x1

    :goto_1
    return p0
.end method

.method static synthetic lambda$resolveFromDefaultImports$2(Ljava/lang/String;)Ljava/util/Set;
    .locals 1

    .line 665
    new-instance p0, Ljava/util/HashSet;

    const/4 v0, 0x2

    invoke-direct {p0, v0}, Ljava/util/HashSet;-><init>(I)V

    return-object p0
.end method

.method static synthetic lambda$resolveToOuterNested$0(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/CompileUnit$ConstructedOuterNestedClassNode;Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 0

    .line 379
    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/ast/ClassNode;->setRedirect(Lorg/codehaus/groovy/ast/ClassNode;)V

    return-void
.end method

.method private static lookupClassName(Lorg/codehaus/groovy/ast/expr/PropertyExpression;)Ljava/lang/String;
    .locals 6

    .line 915
    new-instance v0, Ljava/lang/StringBuilder;

    const/16 v1, 0x20

    invoke-direct {v0, v1}, Ljava/lang/StringBuilder;-><init>(I)V

    const/4 v1, 0x1

    :goto_0
    const/4 v2, 0x0

    if-eqz p0, :cond_6

    if-eqz v0, :cond_6

    .line 918
    instance-of v3, p0, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    if-eqz v3, :cond_2

    .line 919
    check-cast p0, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    .line 921
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->isSuperExpression()Z

    move-result v3

    if-nez v3, :cond_1

    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->isThisExpression()Z

    move-result v3

    if-eqz v3, :cond_0

    goto :goto_1

    .line 924
    :cond_0
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->getName()Ljava/lang/String;

    move-result-object p0

    .line 925
    invoke-static {v1, v0, p0}, Lorg/codehaus/groovy/control/ResolveVisitor;->makeClassName(ZLjava/lang/StringBuilder;Ljava/lang/String;)Lgroovy/lang/Tuple2;

    move-result-object p0

    .line 926
    invoke-virtual {p0}, Lgroovy/lang/Tuple2;->getV1()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/StringBuilder;

    .line 927
    invoke-virtual {p0}, Lgroovy/lang/Tuple2;->getV2()Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Ljava/lang/Boolean;

    invoke-virtual {p0}, Ljava/lang/Boolean;->booleanValue()Z

    goto :goto_3

    :cond_1
    :goto_1
    return-object v2

    .line 934
    :cond_2
    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v3

    const-class v4, Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    if-eq v3, v4, :cond_3

    return-object v2

    .line 938
    :cond_3
    check-cast p0, Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->getPropertyAsString()Ljava/lang/String;

    move-result-object v3

    if-eqz v3, :cond_5

    const-string v4, "class"

    .line 940
    invoke-virtual {v3, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v4

    if-eqz v4, :cond_4

    goto :goto_2

    .line 943
    :cond_4
    invoke-static {v1, v0, v3}, Lorg/codehaus/groovy/control/ResolveVisitor;->makeClassName(ZLjava/lang/StringBuilder;Ljava/lang/String;)Lgroovy/lang/Tuple2;

    move-result-object v0

    .line 944
    invoke-virtual {v0}, Lgroovy/lang/Tuple2;->getV1()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/StringBuilder;

    .line 945
    invoke-virtual {v0}, Lgroovy/lang/Tuple2;->getV2()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/Boolean;

    invoke-virtual {v0}, Ljava/lang/Boolean;->booleanValue()Z

    move-result v0

    .line 917
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->getObjectExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p0

    move-object v5, v1

    move v1, v0

    move-object v0, v5

    goto :goto_0

    :cond_5
    :goto_2
    return-object v2

    :cond_6
    :goto_3
    if-eqz v0, :cond_8

    .line 948
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->length()I

    move-result p0

    if-nez p0, :cond_7

    goto :goto_4

    .line 950
    :cond_7
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    return-object p0

    :cond_8
    :goto_4
    return-object v2
.end method

.method private static makeClassName(ZLjava/lang/StringBuilder;Ljava/lang/String;)Lgroovy/lang/Tuple2;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z",
            "Ljava/lang/StringBuilder;",
            "Ljava/lang/String;",
            ")",
            "Lgroovy/lang/Tuple2<",
            "Ljava/lang/StringBuilder;",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    if-eqz p0, :cond_1

    .line 961
    invoke-static {p2}, Lorg/codehaus/groovy/control/ResolveVisitor;->testVanillaNameForClass(Ljava/lang/String;)Z

    move-result p0

    if-nez p0, :cond_0

    const/4 p0, 0x0

    .line 962
    sget-object p1, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    invoke-static {p0, p1}, Lgroovy/lang/Tuple;->tuple(Ljava/lang/Object;Ljava/lang/Object;)Lgroovy/lang/Tuple2;

    move-result-object p0

    return-object p0

    .line 964
    :cond_0
    new-instance p0, Ljava/lang/StringBuilder;

    invoke-direct {p0, p2}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    sget-object p1, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    invoke-static {p0, p1}, Lgroovy/lang/Tuple;->tuple(Ljava/lang/Object;Ljava/lang/Object;)Lgroovy/lang/Tuple2;

    move-result-object p0

    return-object p0

    :cond_1
    const/4 p0, 0x0

    .line 967
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string v0, "."

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p1, p0, p2}, Ljava/lang/StringBuilder;->insert(ILjava/lang/String;)Ljava/lang/StringBuilder;

    .line 968
    sget-object p0, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    invoke-static {p1, p0}, Lgroovy/lang/Tuple;->tuple(Ljava/lang/Object;Ljava/lang/Object;)Lgroovy/lang/Tuple2;

    move-result-object p0

    return-object p0
.end method

.method private static replaceLastPointWithDollar(Ljava/lang/String;)Ljava/lang/String;
    .locals 3

    const/16 v0, 0x2e

    .line 582
    invoke-virtual {p0, v0}, Ljava/lang/String;->lastIndexOf(I)I

    move-result v0

    .line 584
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const/4 v2, 0x0

    invoke-virtual {p0, v2, v0}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v2, "$"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    add-int/lit8 v0, v0, 0x1

    invoke-virtual {p0, v0}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object p0

    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method private resolveAliasFromModule(Lorg/codehaus/groovy/ast/ClassNode;)Z
    .locals 10

    .line 700
    instance-of v0, p1, Lorg/codehaus/groovy/control/ResolveVisitor$ConstructedClassWithPackage;

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    return v1

    .line 702
    :cond_0
    iget-object v0, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->getModule()Lorg/codehaus/groovy/ast/ModuleNode;

    move-result-object v0

    if-nez v0, :cond_1

    return v1

    .line 704
    :cond_1
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v2

    .line 710
    invoke-virtual {v2}, Ljava/lang/String;->length()I

    move-result v3

    .line 717
    :cond_2
    invoke-virtual {v2, v1, v3}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object v3

    const/4 v4, 0x0

    .line 719
    invoke-virtual {v0, v3}, Lorg/codehaus/groovy/ast/ModuleNode;->getImport(Ljava/lang/String;)Lorg/codehaus/groovy/ast/ImportNode;

    move-result-object v5

    if-eqz v5, :cond_3

    .line 720
    iget-object v6, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->currImportNode:Lorg/codehaus/groovy/ast/ImportNode;

    if-eq v5, v6, :cond_3

    .line 721
    invoke-virtual {v5}, Lorg/codehaus/groovy/ast/ImportNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v4

    :cond_3
    const/4 v5, 0x1

    if-nez v4, :cond_4

    .line 724
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ModuleNode;->getStaticImports()Ljava/util/Map;

    move-result-object v6

    invoke-interface {v6, v3}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Lorg/codehaus/groovy/ast/ImportNode;

    if-eqz v6, :cond_4

    .line 725
    iget-object v7, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->currImportNode:Lorg/codehaus/groovy/ast/ImportNode;

    if-eq v6, v7, :cond_4

    .line 727
    new-instance v7, Lorg/codehaus/groovy/control/ResolveVisitor$ConstructedNestedClass;

    invoke-virtual {v6}, Lorg/codehaus/groovy/ast/ImportNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v8

    invoke-virtual {v6}, Lorg/codehaus/groovy/ast/ImportNode;->getFieldName()Ljava/lang/String;

    move-result-object v6

    invoke-direct {v7, v8, v6}, Lorg/codehaus/groovy/control/ResolveVisitor$ConstructedNestedClass;-><init>(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)V

    .line 728
    invoke-virtual {p0, v7, v1, v1, v5}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolve(Lorg/codehaus/groovy/ast/ClassNode;ZZZ)Z

    move-result v6

    if-eqz v6, :cond_4

    invoke-virtual {v7}, Lorg/codehaus/groovy/ast/ClassNode;->getModifiers()I

    move-result v6

    and-int/lit8 v6, v6, 0x8

    if-eqz v6, :cond_4

    .line 729
    invoke-virtual {v7}, Lorg/codehaus/groovy/ast/ClassNode;->redirect()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    invoke-virtual {p1, v0}, Lorg/codehaus/groovy/ast/ClassNode;->setRedirect(Lorg/codehaus/groovy/ast/ClassNode;)V

    return v5

    :cond_4
    const/16 v6, 0x2e

    if-eqz v4, :cond_6

    .line 736
    invoke-virtual {v3}, Ljava/lang/String;->length()I

    move-result v7

    invoke-virtual {v2}, Ljava/lang/String;->length()I

    move-result v8

    if-ne v7, v8, :cond_5

    .line 741
    invoke-virtual {p1, v4}, Lorg/codehaus/groovy/ast/ClassNode;->setRedirect(Lorg/codehaus/groovy/ast/ClassNode;)V

    return v5

    .line 752
    :cond_5
    new-instance v7, Ljava/lang/StringBuilder;

    invoke-direct {v7}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/ClassNode;->getNameWithoutPackage()Ljava/lang/String;

    move-result-object v8

    invoke-virtual {v7, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v7

    const-string v8, "$"

    invoke-virtual {v7, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v7

    invoke-virtual {v3}, Ljava/lang/String;->length()I

    move-result v8

    add-int/2addr v8, v5

    invoke-virtual {v2, v8}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object v8

    const/16 v9, 0x24

    invoke-virtual {v8, v6, v9}, Ljava/lang/String;->replace(CC)Ljava/lang/String;

    move-result-object v8

    invoke-virtual {v7, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v7

    invoke-virtual {v7}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v7

    .line 753
    new-instance v8, Lorg/codehaus/groovy/control/ResolveVisitor$ConstructedClassWithPackage;

    new-instance v9, Ljava/lang/StringBuilder;

    invoke-direct {v9}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/ClassNode;->getPackageName()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v9, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    const-string v9, "."

    invoke-virtual {v4, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    invoke-direct {v8, v4, v7}, Lorg/codehaus/groovy/control/ResolveVisitor$ConstructedClassWithPackage;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    .line 754
    invoke-virtual {p0, v8, v5, v5, v1}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolve(Lorg/codehaus/groovy/ast/ClassNode;ZZZ)Z

    move-result v4

    if-eqz v4, :cond_6

    .line 755
    invoke-virtual {v8}, Lorg/codehaus/groovy/control/ResolveVisitor$ConstructedClassWithPackage;->redirect()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    invoke-virtual {p1, v0}, Lorg/codehaus/groovy/ast/ClassNode;->setRedirect(Lorg/codehaus/groovy/ast/ClassNode;)V

    return v5

    .line 760
    :cond_6
    invoke-virtual {v3, v6}, Ljava/lang/String;->lastIndexOf(I)I

    move-result v3

    const/4 v4, -0x1

    if-ne v3, v4, :cond_2

    return v1
.end method

.method private resolveGenericsHeader([Lorg/codehaus/groovy/ast/GenericsType;)V
    .locals 2

    const/4 v0, 0x0

    const/4 v1, 0x0

    .line 1582
    invoke-direct {p0, p1, v0, v1}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolveGenericsHeader([Lorg/codehaus/groovy/ast/GenericsType;Lorg/codehaus/groovy/ast/GenericsType;I)V

    return-void
.end method

.method private resolveGenericsHeader([Lorg/codehaus/groovy/ast/GenericsType;Lorg/codehaus/groovy/ast/GenericsType;I)V
    .locals 18

    move-object/from16 v0, p0

    move-object/from16 v1, p1

    if-nez v1, :cond_0

    return-void

    .line 1587
    :cond_0
    iget-object v2, v0, Lorg/codehaus/groovy/control/ResolveVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    const/4 v3, 0x1

    invoke-virtual {v2, v3}, Lorg/codehaus/groovy/ast/ClassNode;->setUsingGenerics(Z)V

    .line 1588
    new-instance v2, Ljava/util/LinkedList;

    invoke-direct {v2}, Ljava/util/LinkedList;-><init>()V

    .line 1589
    new-instance v4, Ljava/util/LinkedList;

    invoke-direct {v4}, Ljava/util/LinkedList;-><init>()V

    .line 1590
    array-length v5, v1

    const/4 v7, 0x0

    :goto_0
    if-ge v7, v5, :cond_c

    aget-object v8, v1, v7

    if-lez p3, :cond_1

    .line 1591
    invoke-virtual {v8}, Lorg/codehaus/groovy/ast/GenericsType;->getName()Ljava/lang/String;

    move-result-object v9

    invoke-virtual/range {p2 .. p2}, Lorg/codehaus/groovy/ast/GenericsType;->getName()Ljava/lang/String;

    move-result-object v10

    invoke-virtual {v9, v10}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v9

    if-eqz v9, :cond_1

    goto/16 :goto_4

    .line 1595
    :cond_1
    invoke-virtual {v8}, Lorg/codehaus/groovy/ast/GenericsType;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v9

    .line 1596
    invoke-virtual {v8}, Lorg/codehaus/groovy/ast/GenericsType;->getName()Ljava/lang/String;

    move-result-object v10

    .line 1597
    new-instance v11, Lorg/codehaus/groovy/ast/GenericsType$GenericsTypeName;

    invoke-direct {v11, v10}, Lorg/codehaus/groovy/ast/GenericsType$GenericsTypeName;-><init>(Ljava/lang/String;)V

    .line 1598
    invoke-virtual {v8}, Lorg/codehaus/groovy/ast/GenericsType;->getUpperBounds()[Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v12

    const-string v13, "?"

    .line 1599
    invoke-virtual {v13, v10}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v10

    if-eqz p3, :cond_3

    if-lez p3, :cond_2

    .line 1600
    iget-object v13, v0, Lorg/codehaus/groovy/control/ResolveVisitor;->genericParameterNames:Ljava/util/Map;

    invoke-interface {v13, v11}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v13

    if-eqz v13, :cond_2

    goto :goto_1

    :cond_2
    const/4 v13, 0x0

    goto :goto_2

    :cond_3
    :goto_1
    move v13, v3

    :goto_2
    if-eqz v12, :cond_9

    .line 1604
    array-length v14, v12

    const/4 v15, 0x0

    const/16 v16, 0x0

    :goto_3
    if-ge v15, v14, :cond_b

    aget-object v6, v12, v15

    if-nez v10, :cond_7

    if-nez v16, :cond_4

    if-nez v6, :cond_5

    .line 1606
    :cond_4
    invoke-virtual {v0, v9}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolve(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v17

    if-nez v17, :cond_6

    :cond_5
    if-eqz v13, :cond_6

    .line 1608
    iget-object v1, v0, Lorg/codehaus/groovy/control/ResolveVisitor;->genericParameterNames:Ljava/util/Map;

    invoke-interface {v1, v11, v8}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1609
    invoke-virtual {v8, v3}, Lorg/codehaus/groovy/ast/GenericsType;->setPlaceholder(Z)V

    .line 1610
    invoke-virtual {v9, v6}, Lorg/codehaus/groovy/ast/ClassNode;->setRedirect(Lorg/codehaus/groovy/ast/ClassNode;)V

    move/from16 v16, v3

    .line 1615
    :cond_6
    invoke-static {v6, v9}, Lgroovy/lang/Tuple;->tuple(Ljava/lang/Object;Ljava/lang/Object;)Lgroovy/lang/Tuple2;

    move-result-object v1

    invoke-interface {v4, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :cond_7
    if-eqz v6, :cond_8

    .line 1618
    invoke-virtual {v6}, Lorg/codehaus/groovy/ast/ClassNode;->isUsingGenerics()Z

    move-result v1

    if-eqz v1, :cond_8

    .line 1619
    invoke-static {v6, v8}, Lgroovy/lang/Tuple;->tuple(Ljava/lang/Object;Ljava/lang/Object;)Lgroovy/lang/Tuple2;

    move-result-object v1

    invoke-interface {v2, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :cond_8
    add-int/lit8 v15, v15, 0x1

    move-object/from16 v1, p1

    goto :goto_3

    :cond_9
    if-nez v10, :cond_b

    if-eqz v13, :cond_b

    .line 1625
    iget-object v1, v0, Lorg/codehaus/groovy/control/ResolveVisitor;->genericParameterNames:Ljava/util/Map;

    invoke-interface {v1, v11}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/ast/GenericsType;

    .line 1626
    iget-object v6, v0, Lorg/codehaus/groovy/control/ResolveVisitor;->genericParameterNames:Ljava/util/Map;

    invoke-interface {v6, v11, v8}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1627
    invoke-virtual {v8, v3}, Lorg/codehaus/groovy/ast/GenericsType;->setPlaceholder(Z)V

    if-nez v1, :cond_a

    .line 1630
    sget-object v1, Lorg/codehaus/groovy/ast/ClassHelper;->OBJECT_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v9, v1}, Lorg/codehaus/groovy/ast/ClassNode;->setRedirect(Lorg/codehaus/groovy/ast/ClassNode;)V

    goto :goto_4

    .line 1632
    :cond_a
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/GenericsType;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    invoke-virtual {v9, v1}, Lorg/codehaus/groovy/ast/ClassNode;->setRedirect(Lorg/codehaus/groovy/ast/ClassNode;)V

    :cond_b
    :goto_4
    add-int/lit8 v7, v7, 0x1

    move-object/from16 v1, p1

    goto/16 :goto_0

    .line 1639
    :cond_c
    invoke-interface {v4}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_5
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_d

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lgroovy/lang/Tuple2;

    .line 1640
    invoke-virtual {v3}, Lgroovy/lang/Tuple2;->getV1()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lorg/codehaus/groovy/ast/ClassNode;

    .line 1641
    invoke-virtual {v3}, Lgroovy/lang/Tuple2;->getV2()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lorg/codehaus/groovy/ast/ClassNode;

    .line 1642
    invoke-direct {v0, v4, v3}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolveOrFail(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ASTNode;)V

    goto :goto_5

    .line 1645
    :cond_d
    invoke-interface {v2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_6
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_f

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovy/lang/Tuple2;

    .line 1646
    invoke-virtual {v2}, Lgroovy/lang/Tuple2;->getV1()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lorg/codehaus/groovy/ast/ClassNode;

    .line 1647
    invoke-virtual {v2}, Lgroovy/lang/Tuple2;->getV2()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lorg/codehaus/groovy/ast/GenericsType;

    .line 1648
    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/ClassNode;->getGenericsTypes()[Lorg/codehaus/groovy/ast/GenericsType;

    move-result-object v3

    if-nez p3, :cond_e

    goto :goto_7

    :cond_e
    move-object/from16 v2, p2

    :goto_7
    add-int/lit8 v4, p3, 0x1

    invoke-direct {v0, v3, v2, v4}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolveGenericsHeader([Lorg/codehaus/groovy/ast/GenericsType;Lorg/codehaus/groovy/ast/GenericsType;I)V

    goto :goto_6

    :cond_f
    return-void
.end method

.method private resolveGenericsType(Lorg/codehaus/groovy/ast/GenericsType;)Z
    .locals 5

    .line 1653
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/GenericsType;->isResolved()Z

    move-result v0

    const/4 v1, 0x1

    if-eqz v0, :cond_0

    return v1

    .line 1654
    :cond_0
    iget-object v0, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v0, v1}, Lorg/codehaus/groovy/ast/ClassNode;->setUsingGenerics(Z)V

    .line 1655
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/GenericsType;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    .line 1657
    new-instance v2, Lorg/codehaus/groovy/ast/GenericsType$GenericsTypeName;

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v3

    invoke-direct {v2, v3}, Lorg/codehaus/groovy/ast/GenericsType$GenericsTypeName;-><init>(Ljava/lang/String;)V

    .line 1658
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/GenericsType;->getUpperBounds()[Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v3

    .line 1659
    iget-object v4, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->genericParameterNames:Ljava/util/Map;

    invoke-interface {v4, v2}, Ljava/util/Map;->containsKey(Ljava/lang/Object;)Z

    move-result v4

    if-nez v4, :cond_3

    if-eqz v3, :cond_1

    .line 1661
    array-length v1, v3

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v1, :cond_4

    aget-object v4, v3, v2

    .line 1662
    invoke-direct {p0, v4, p1}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolveOrFail(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 1663
    invoke-virtual {v0, v4}, Lorg/codehaus/groovy/ast/ClassNode;->setRedirect(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 1664
    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/ClassNode;->getGenericsTypes()[Lorg/codehaus/groovy/ast/GenericsType;

    move-result-object v4

    invoke-direct {p0, v4}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolveGenericsTypes([Lorg/codehaus/groovy/ast/GenericsType;)Z

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 1666
    :cond_1
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/GenericsType;->isWildcard()Z

    move-result v1

    if-eqz v1, :cond_2

    .line 1667
    sget-object v1, Lorg/codehaus/groovy/ast/ClassHelper;->OBJECT_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v0, v1}, Lorg/codehaus/groovy/ast/ClassNode;->setRedirect(Lorg/codehaus/groovy/ast/ClassNode;)V

    goto :goto_1

    .line 1669
    :cond_2
    invoke-direct {p0, v0, p1}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolveOrFail(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ASTNode;)V

    goto :goto_1

    .line 1672
    :cond_3
    iget-object v3, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->genericParameterNames:Ljava/util/Map;

    invoke-interface {v3, v2}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lorg/codehaus/groovy/ast/GenericsType;

    .line 1673
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/GenericsType;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v2

    invoke-virtual {v0, v2}, Lorg/codehaus/groovy/ast/ClassNode;->setRedirect(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 1674
    invoke-virtual {p1, v1}, Lorg/codehaus/groovy/ast/GenericsType;->setPlaceholder(Z)V

    .line 1677
    :cond_4
    :goto_1
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/GenericsType;->getLowerBound()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    if-eqz v1, :cond_5

    .line 1678
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/GenericsType;->getLowerBound()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    invoke-direct {p0, v1, p1}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolveOrFail(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 1681
    :cond_5
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->getGenericsTypes()[Lorg/codehaus/groovy/ast/GenericsType;

    move-result-object v0

    invoke-direct {p0, v0}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolveGenericsTypes([Lorg/codehaus/groovy/ast/GenericsType;)Z

    move-result v0

    if-eqz v0, :cond_6

    .line 1682
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/GenericsType;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->isResolved()Z

    move-result v0

    invoke-virtual {p1, v0}, Lorg/codehaus/groovy/ast/GenericsType;->setResolved(Z)V

    .line 1684
    :cond_6
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/GenericsType;->isResolved()Z

    move-result p1

    return p1
.end method

.method private resolveGenericsTypes([Lorg/codehaus/groovy/ast/GenericsType;)Z
    .locals 6

    const/4 v0, 0x1

    if-nez p1, :cond_0

    return v0

    .line 1572
    :cond_0
    iget-object v1, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v1, v0}, Lorg/codehaus/groovy/ast/ClassNode;->setUsingGenerics(Z)V

    .line 1574
    array-length v1, p1

    const/4 v2, 0x0

    move v4, v0

    move v3, v2

    :goto_0
    if-ge v3, v1, :cond_2

    aget-object v5, p1, v3

    .line 1576
    invoke-direct {p0, v5}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolveGenericsType(Lorg/codehaus/groovy/ast/GenericsType;)Z

    move-result v5

    if-eqz v5, :cond_1

    if-eqz v4, :cond_1

    move v4, v0

    goto :goto_1

    :cond_1
    move v4, v2

    :goto_1
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    :cond_2
    return v4
.end method

.method private resolveOrFail(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V
    .locals 1

    const/4 v0, 0x0

    .line 330
    invoke-direct {p0, p1, p2, p3, v0}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolveOrFail(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;Z)V

    return-void
.end method

.method private resolveOrFail(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;Z)V
    .locals 1

    if-eqz p4, :cond_0

    .line 335
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getGenericsTypes()[Lorg/codehaus/groovy/ast/GenericsType;

    move-result-object p4

    invoke-direct {p0, p4}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolveGenericsTypes([Lorg/codehaus/groovy/ast/GenericsType;)Z

    .line 336
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolveAliasFromModule(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result p4

    if-eqz p4, :cond_0

    return-void

    .line 338
    :cond_0
    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolve(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result p4

    if-eqz p4, :cond_1

    return-void

    .line 339
    :cond_1
    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolveToInner(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result p4

    if-eqz p4, :cond_2

    return-void

    .line 340
    :cond_2
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolveToOuterNested(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result p4

    if-eqz p4, :cond_3

    return-void

    .line 342
    :cond_3
    new-instance p4, Ljava/lang/StringBuilder;

    invoke-direct {p4}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "unable to resolve class "

    invoke-virtual {p4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p4

    const/4 v0, 0x0

    invoke-virtual {p1, v0}, Lorg/codehaus/groovy/ast/ClassNode;->toString(Z)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p4, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1, p3}, Lorg/codehaus/groovy/control/ResolveVisitor;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    return-void
.end method

.method private resolveOrFail(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ASTNode;)V
    .locals 1

    const-string v0, ""

    .line 326
    invoke-direct {p0, p1, v0, p2}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolveOrFail(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    return-void
.end method

.method private resolveOuterNestedClassFurther(Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 8

    .line 1519
    iget-object v0, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->getCompileUnit()Lorg/codehaus/groovy/ast/CompileUnit;

    move-result-object v0

    if-nez v0, :cond_0

    return-void

    .line 1523
    :cond_0
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/CompileUnit;->getClassesToResolve()Ljava/util/Map;

    move-result-object v1

    .line 1524
    new-instance v2, Ljava/util/LinkedList;

    invoke-direct {v2}, Ljava/util/LinkedList;-><init>()V

    .line 1526
    invoke-interface {v1}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    move-result-object v3

    invoke-interface {v3}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v3

    :cond_1
    :goto_0
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    move-result v4

    if-eqz v4, :cond_3

    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Ljava/util/Map$Entry;

    .line 1527
    invoke-interface {v4}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Ljava/lang/String;

    .line 1528
    invoke-interface {v4}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lorg/codehaus/groovy/ast/CompileUnit$ConstructedOuterNestedClassNode;

    .line 1531
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/CompileUnit$ConstructedOuterNestedClassNode;->getEnclosingClassNode()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v7

    invoke-virtual {v7}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v7

    invoke-virtual {v6, v7}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v6

    if-eqz v6, :cond_1

    .line 1532
    invoke-virtual {v0, v5}, Lorg/codehaus/groovy/ast/CompileUnit;->getClass(Ljava/lang/String;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v6

    if-nez v6, :cond_2

    return-void

    .line 1538
    :cond_2
    invoke-virtual {v4, v6}, Lorg/codehaus/groovy/ast/CompileUnit$ConstructedOuterNestedClassNode;->setRedirect(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 1539
    invoke-interface {v2, v5}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    .line 1543
    :cond_3
    invoke-interface {v2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :goto_1
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_4

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/String;

    .line 1544
    invoke-interface {v1, v0}, Ljava/util/Map;->remove(Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_1

    :cond_4
    return-void
.end method

.method private resolveToOuterNested(Lorg/codehaus/groovy/ast/ClassNode;)Z
    .locals 10

    .line 375
    iget-object v0, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->getCompileUnit()Lorg/codehaus/groovy/ast/CompileUnit;

    move-result-object v0

    const/4 v1, 0x0

    if-nez v0, :cond_0

    return v1

    .line 377
    :cond_0
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v2

    .line 379
    new-instance v3, Lorg/codehaus/groovy/control/ResolveVisitor$$ExternalSyntheticLambda0;

    invoke-direct {v3, p1}, Lorg/codehaus/groovy/control/ResolveVisitor$$ExternalSyntheticLambda0;-><init>(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 381
    iget-object v4, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/ClassNode;->getModule()Lorg/codehaus/groovy/ast/ModuleNode;

    move-result-object v4

    .line 382
    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/ModuleNode;->getStaticImports()Ljava/util/Map;

    move-result-object v5

    invoke-interface {v5}, Ljava/util/Map;->values()Ljava/util/Collection;

    move-result-object v5

    invoke-interface {v5}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object v5

    :cond_1
    :goto_0
    invoke-interface {v5}, Ljava/util/Iterator;->hasNext()Z

    move-result v6

    const/4 v7, 0x1

    if-eqz v6, :cond_3

    invoke-interface {v5}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Lorg/codehaus/groovy/ast/ImportNode;

    .line 383
    invoke-virtual {v6}, Lorg/codehaus/groovy/ast/ImportNode;->getFieldName()Ljava/lang/String;

    move-result-object v8

    .line 384
    invoke-virtual {v6}, Lorg/codehaus/groovy/ast/ImportNode;->getAlias()Ljava/lang/String;

    move-result-object v9

    .line 386
    invoke-virtual {v2, v9}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v9

    if-nez v9, :cond_2

    goto :goto_0

    .line 388
    :cond_2
    invoke-direct {p0, v0, v6, v8, v3}, Lorg/codehaus/groovy/control/ResolveVisitor;->tryToConstructOuterNestedClassNodeViaStaticImport(Lorg/codehaus/groovy/ast/CompileUnit;Lorg/codehaus/groovy/ast/ImportNode;Ljava/lang/String;Ljava/util/function/BiConsumer;)Lorg/codehaus/groovy/ast/CompileUnit$ConstructedOuterNestedClassNode;

    move-result-object v6

    if-eqz v6, :cond_1

    .line 390
    invoke-virtual {v0, v6}, Lorg/codehaus/groovy/ast/CompileUnit;->addClassNodeToResolve(Lorg/codehaus/groovy/ast/CompileUnit$ConstructedOuterNestedClassNode;)V

    return v7

    .line 395
    :cond_3
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/CompileUnit;->getClassesToCompile()Ljava/util/Map;

    move-result-object v5

    invoke-interface {v5}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    move-result-object v5

    invoke-interface {v5}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v5

    :cond_4
    invoke-interface {v5}, Ljava/util/Iterator;->hasNext()Z

    move-result v6

    if-eqz v6, :cond_5

    invoke-interface {v5}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Ljava/util/Map$Entry;

    .line 396
    invoke-interface {v6}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Lorg/codehaus/groovy/ast/ClassNode;

    .line 397
    invoke-direct {p0, p1, v6, v3}, Lorg/codehaus/groovy/control/ResolveVisitor;->tryToConstructOuterNestedClassNode(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/function/BiConsumer;)Lorg/codehaus/groovy/ast/CompileUnit$ConstructedOuterNestedClassNode;

    move-result-object v6

    if-eqz v6, :cond_4

    .line 399
    invoke-virtual {v0, v6}, Lorg/codehaus/groovy/ast/CompileUnit;->addClassNodeToResolve(Lorg/codehaus/groovy/ast/CompileUnit$ConstructedOuterNestedClassNode;)V

    return v7

    .line 405
    :cond_5
    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/ModuleNode;->getStaticStarImports()Ljava/util/Map;

    move-result-object p1

    invoke-interface {p1}, Ljava/util/Map;->values()Ljava/util/Collection;

    move-result-object p1

    invoke-interface {p1}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object p1

    move v4, v1

    :cond_6
    :goto_1
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v5

    if-eqz v5, :cond_7

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lorg/codehaus/groovy/ast/ImportNode;

    .line 406
    invoke-direct {p0, v0, v5, v2, v3}, Lorg/codehaus/groovy/control/ResolveVisitor;->tryToConstructOuterNestedClassNodeViaStaticImport(Lorg/codehaus/groovy/ast/CompileUnit;Lorg/codehaus/groovy/ast/ImportNode;Ljava/lang/String;Ljava/util/function/BiConsumer;)Lorg/codehaus/groovy/ast/CompileUnit$ConstructedOuterNestedClassNode;

    move-result-object v5

    if-eqz v5, :cond_6

    .line 408
    invoke-virtual {v0, v5}, Lorg/codehaus/groovy/ast/CompileUnit;->addClassNodeToResolve(Lorg/codehaus/groovy/ast/CompileUnit$ConstructedOuterNestedClassNode;)V

    move v4, v7

    goto :goto_1

    :cond_7
    if-eqz v4, :cond_8

    return v7

    :cond_8
    const/16 p1, 0x2e

    .line 416
    invoke-virtual {v2, p1}, Ljava/lang/String;->indexOf(I)I

    move-result p1

    const/4 v4, -0x1

    if-ne p1, v4, :cond_a

    .line 417
    iget-object p1, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {p1}, Lorg/codehaus/groovy/control/ResolveVisitor;->findHierClasses(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/util/Map;

    move-result-object p1

    .line 418
    invoke-interface {p1}, Ljava/util/Map;->values()Ljava/util/Collection;

    move-result-object p1

    invoke-interface {p1}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_9
    :goto_2
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v4

    if-eqz v4, :cond_a

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lorg/codehaus/groovy/ast/ClassNode;

    .line 419
    invoke-direct {p0, v0, v2, v4, v3}, Lorg/codehaus/groovy/control/ResolveVisitor;->tryToConstructOuterNestedClassNodeForBaseType(Lorg/codehaus/groovy/ast/CompileUnit;Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/function/BiConsumer;)Lorg/codehaus/groovy/ast/CompileUnit$ConstructedOuterNestedClassNode;

    move-result-object v4

    if-eqz v4, :cond_9

    .line 421
    invoke-virtual {v0, v4}, Lorg/codehaus/groovy/ast/CompileUnit;->addClassNodeToResolve(Lorg/codehaus/groovy/ast/CompileUnit$ConstructedOuterNestedClassNode;)V

    move v1, v7

    goto :goto_2

    :cond_a
    return v1
.end method

.method private setRedirect(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;)Z
    .locals 4

    .line 553
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v0

    .line 555
    new-instance v1, Lorg/codehaus/groovy/control/ResolveVisitor$$ExternalSyntheticLambda2;

    invoke-direct {v1, p0, v0, p1}, Lorg/codehaus/groovy/control/ResolveVisitor$$ExternalSyntheticLambda2;-><init>(Lorg/codehaus/groovy/control/ResolveVisitor;Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 566
    invoke-interface {v1, p2}, Ljava/util/function/Predicate;->test(Ljava/lang/Object;)Z

    move-result v0

    const/4 v2, 0x1

    if-eqz v0, :cond_1

    .line 567
    iget-object v0, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    if-eq v0, p2, :cond_0

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->getOuterClasses()Ljava/util/List;

    move-result-object v0

    invoke-interface {v0, p2}, Ljava/util/List;->contains(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_0

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->redirect()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    iget-object v3, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {v0, v3}, Lorg/codehaus/groovy/control/ResolveVisitor;->isVisibleNestedClass(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v0

    if-nez v0, :cond_0

    const/4 v0, 0x0

    .line 568
    invoke-virtual {p1, v0}, Lorg/codehaus/groovy/ast/ClassNode;->setRedirect(Lorg/codehaus/groovy/ast/ClassNode;)V

    goto :goto_0

    :cond_0
    return v2

    .line 573
    :cond_1
    :goto_0
    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/ClassNode;->getAllInterfaces()Ljava/util/Set;

    move-result-object p1

    invoke-interface {p1}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_2
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result p2

    if-eqz p2, :cond_3

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lorg/codehaus/groovy/ast/ClassNode;

    .line 574
    invoke-interface {v1, p2}, Ljava/util/function/Predicate;->test(Ljava/lang/Object;)Z

    move-result p2

    if-eqz p2, :cond_2

    return v2

    :cond_3
    const/4 p1, 0x0

    return p1
.end method

.method private static testVanillaNameForClass(Ljava/lang/String;)Z
    .locals 2

    const/4 v0, 0x0

    if-eqz p0, :cond_1

    .line 1149
    invoke-virtual {p0}, Ljava/lang/String;->length()I

    move-result v1

    if-nez v1, :cond_0

    goto :goto_0

    .line 1150
    :cond_0
    invoke-virtual {p0, v0}, Ljava/lang/String;->charAt(I)C

    move-result p0

    invoke-static {p0}, Ljava/lang/Character;->isLowerCase(C)Z

    move-result p0

    xor-int/lit8 p0, p0, 0x1

    return p0

    :cond_1
    :goto_0
    return v0
.end method

.method private static transformInlineConstants(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 3

    .line 1370
    instance-of v0, p0, Lorg/codehaus/groovy/ast/expr/AnnotationConstantExpression;

    if-eqz v0, :cond_1

    .line 1371
    move-object v0, p0

    check-cast v0, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    .line 1372
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/ConstantExpression;->getValue()Ljava/lang/Object;

    move-result-object v1

    instance-of v1, v1, Lorg/codehaus/groovy/ast/AnnotationNode;

    if-eqz v1, :cond_0

    .line 1375
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/ConstantExpression;->getValue()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/ast/AnnotationNode;

    .line 1376
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/AnnotationNode;->getMembers()Ljava/util/Map;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/util/Map$Entry;

    .line 1377
    invoke-interface {v1}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lorg/codehaus/groovy/ast/expr/Expression;

    invoke-static {v2}, Lorg/codehaus/groovy/control/ResolveVisitor;->transformInlineConstants(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v2

    invoke-interface {v1, v2}, Ljava/util/Map$Entry;->setValue(Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    :cond_0
    return-object p0

    .line 1381
    :cond_1
    invoke-static {p0}, Lorg/apache/groovy/ast/tools/ExpressionUtils;->transformInlineConstants(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p0

    return-object p0
.end method

.method private tryToConstructOuterNestedClassNode(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/function/BiConsumer;)Lorg/codehaus/groovy/ast/CompileUnit$ConstructedOuterNestedClassNode;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            "Ljava/util/function/BiConsumer<",
            "Lorg/codehaus/groovy/ast/CompileUnit$ConstructedOuterNestedClassNode;",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            ">;)",
            "Lorg/codehaus/groovy/ast/CompileUnit$ConstructedOuterNestedClassNode;"
        }
    .end annotation

    .line 443
    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v0

    .line 445
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object p1

    move-object v1, p1

    :cond_0
    const/16 v2, 0x2e

    invoke-virtual {v1, v2}, Ljava/lang/String;->indexOf(I)I

    move-result v3

    const/4 v4, -0x1

    if-eq v3, v4, :cond_1

    const/4 v3, 0x0

    .line 446
    invoke-virtual {v1, v2}, Ljava/lang/String;->lastIndexOf(I)I

    move-result v4

    invoke-virtual {v1, v3, v4}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object v1

    .line 447
    invoke-virtual {v0, v1}, Ljava/lang/String;->endsWith(Ljava/lang/String;)Z

    move-result v3

    if-eqz v3, :cond_0

    .line 448
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-virtual {p1, v1}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object p1

    const/16 v1, 0x24

    invoke-virtual {p1, v2, v1}, Ljava/lang/String;->replace(CC)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    .line 449
    new-instance v0, Lorg/codehaus/groovy/ast/CompileUnit$ConstructedOuterNestedClassNode;

    invoke-direct {v0, p2, p1}, Lorg/codehaus/groovy/ast/CompileUnit$ConstructedOuterNestedClassNode;-><init>(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)V

    .line 450
    invoke-virtual {v0, p3}, Lorg/codehaus/groovy/ast/CompileUnit$ConstructedOuterNestedClassNode;->addSetRedirectListener(Ljava/util/function/BiConsumer;)V

    return-object v0

    :cond_1
    const/4 p1, 0x0

    return-object p1
.end method

.method private tryToConstructOuterNestedClassNodeForBaseType(Lorg/codehaus/groovy/ast/CompileUnit;Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/function/BiConsumer;)Lorg/codehaus/groovy/ast/CompileUnit$ConstructedOuterNestedClassNode;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/CompileUnit;",
            "Ljava/lang/String;",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            "Ljava/util/function/BiConsumer<",
            "Lorg/codehaus/groovy/ast/CompileUnit$ConstructedOuterNestedClassNode;",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            ">;)",
            "Lorg/codehaus/groovy/ast/CompileUnit$ConstructedOuterNestedClassNode;"
        }
    .end annotation

    .line 459
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/CompileUnit;->getClassesToCompile()Ljava/util/Map;

    move-result-object p1

    invoke-interface {p1, p3}, Ljava/util/Map;->containsValue(Ljava/lang/Object;)Z

    move-result p1

    if-nez p1, :cond_0

    const/4 p1, 0x0

    return-object p1

    .line 461
    :cond_0
    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p3}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, "$"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    .line 462
    new-instance p2, Lorg/codehaus/groovy/ast/CompileUnit$ConstructedOuterNestedClassNode;

    invoke-direct {p2, p3, p1}, Lorg/codehaus/groovy/ast/CompileUnit$ConstructedOuterNestedClassNode;-><init>(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)V

    .line 463
    invoke-virtual {p2, p4}, Lorg/codehaus/groovy/ast/CompileUnit$ConstructedOuterNestedClassNode;->addSetRedirectListener(Ljava/util/function/BiConsumer;)V

    return-object p2
.end method

.method private tryToConstructOuterNestedClassNodeViaStaticImport(Lorg/codehaus/groovy/ast/CompileUnit;Lorg/codehaus/groovy/ast/ImportNode;Ljava/lang/String;Ljava/util/function/BiConsumer;)Lorg/codehaus/groovy/ast/CompileUnit$ConstructedOuterNestedClassNode;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/CompileUnit;",
            "Lorg/codehaus/groovy/ast/ImportNode;",
            "Ljava/lang/String;",
            "Ljava/util/function/BiConsumer<",
            "Lorg/codehaus/groovy/ast/CompileUnit$ConstructedOuterNestedClassNode;",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            ">;)",
            "Lorg/codehaus/groovy/ast/CompileUnit$ConstructedOuterNestedClassNode;"
        }
    .end annotation

    .line 431
    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/ImportNode;->getClassName()Ljava/lang/String;

    move-result-object p2

    .line 432
    invoke-virtual {p1, p2}, Lorg/codehaus/groovy/ast/CompileUnit;->getClass(Ljava/lang/String;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    if-nez p1, :cond_0

    const/4 p1, 0x0

    return-object p1

    .line 436
    :cond_0
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string v0, "$"

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    const/16 v0, 0x2e

    const/16 v1, 0x24

    invoke-virtual {p3, v0, v1}, Ljava/lang/String;->replace(CC)Ljava/lang/String;

    move-result-object p3

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    .line 437
    new-instance p3, Lorg/codehaus/groovy/ast/CompileUnit$ConstructedOuterNestedClassNode;

    invoke-direct {p3, p1, p2}, Lorg/codehaus/groovy/ast/CompileUnit$ConstructedOuterNestedClassNode;-><init>(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)V

    .line 438
    invoke-virtual {p3, p4}, Lorg/codehaus/groovy/ast/CompileUnit$ConstructedOuterNestedClassNode;->addSetRedirectListener(Ljava/util/function/BiConsumer;)V

    return-object p3
.end method


# virtual methods
.method protected getSourceUnit()Lorg/codehaus/groovy/control/SourceUnit;
    .locals 1

    .line 267
    iget-object v0, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->source:Lorg/codehaus/groovy/control/SourceUnit;

    return-object v0
.end method

.method public synthetic lambda$setRedirect$1$org-codehaus-groovy-control-ResolveVisitor(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;)Z
    .locals 1

    .line 556
    invoke-virtual {p3}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_1

    .line 557
    new-instance v0, Lorg/codehaus/groovy/control/ResolveVisitor$ConstructedNestedClass;

    invoke-direct {v0, p3, p1}, Lorg/codehaus/groovy/control/ResolveVisitor$ConstructedNestedClass;-><init>(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)V

    .line 558
    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolveFromCompileUnit(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result p1

    if-nez p1, :cond_0

    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolveToOuter(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result p1

    if-eqz p1, :cond_1

    .line 559
    :cond_0
    invoke-virtual {p2, v0}, Lorg/codehaus/groovy/ast/ClassNode;->setRedirect(Lorg/codehaus/groovy/ast/ClassNode;)V

    const/4 p1, 0x1

    return p1

    :cond_1
    const/4 p1, 0x0

    return p1
.end method

.method protected resolve(Lorg/codehaus/groovy/ast/ClassNode;)Z
    .locals 1

    const/4 v0, 0x1

    .line 468
    invoke-virtual {p0, p1, v0, v0, v0}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolve(Lorg/codehaus/groovy/ast/ClassNode;ZZZ)Z

    move-result p1

    return p1
.end method

.method protected resolve(Lorg/codehaus/groovy/ast/ClassNode;ZZZ)Z
    .locals 4

    .line 472
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getGenericsTypes()[Lorg/codehaus/groovy/ast/GenericsType;

    move-result-object v0

    invoke-direct {p0, v0}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolveGenericsTypes([Lorg/codehaus/groovy/ast/GenericsType;)Z

    .line 473
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->isResolved()Z

    move-result v0

    const/4 v1, 0x1

    if-nez v0, :cond_9

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->isPrimaryClassNode()Z

    move-result v0

    if-eqz v0, :cond_0

    goto/16 :goto_0

    .line 474
    :cond_0
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->isArray()Z

    move-result v0

    if-eqz v0, :cond_2

    .line 475
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getComponentType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    .line 476
    invoke-virtual {p0, v0, p2, p3, p4}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolve(Lorg/codehaus/groovy/ast/ClassNode;ZZZ)Z

    move-result p2

    if-eqz p2, :cond_1

    .line 478
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->makeArray()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p3

    .line 479
    invoke-virtual {p1, p3}, Lorg/codehaus/groovy/ast/ClassNode;->setRedirect(Lorg/codehaus/groovy/ast/ClassNode;)V

    :cond_1
    return p2

    .line 485
    :cond_2
    iget-object v0, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    if-ne v0, p1, :cond_3

    return v1

    .line 487
    :cond_3
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v0

    .line 489
    iget-object v2, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->genericParameterNames:Ljava/util/Map;

    new-instance v3, Lorg/codehaus/groovy/ast/GenericsType$GenericsTypeName;

    invoke-direct {v3, v0}, Lorg/codehaus/groovy/ast/GenericsType$GenericsTypeName;-><init>(Ljava/lang/String;)V

    invoke-interface {v2, v3}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lorg/codehaus/groovy/ast/GenericsType;

    const/4 v3, 0x0

    if-eqz v2, :cond_4

    .line 491
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/GenericsType;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p2

    invoke-virtual {p1, p2}, Lorg/codehaus/groovy/ast/ClassNode;->setRedirect(Lorg/codehaus/groovy/ast/ClassNode;)V

    new-array p2, v1, [Lorg/codehaus/groovy/ast/GenericsType;

    aput-object v2, p2, v3

    .line 492
    invoke-virtual {p1, p2}, Lorg/codehaus/groovy/ast/ClassNode;->setGenericsTypes([Lorg/codehaus/groovy/ast/GenericsType;)V

    .line 493
    invoke-virtual {p1, v1}, Lorg/codehaus/groovy/ast/ClassNode;->setGenericsPlaceHolder(Z)V

    return v1

    .line 497
    :cond_4
    iget-object v2, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/ClassNode;->getNameWithoutPackage()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v2, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_5

    .line 498
    iget-object p2, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {p1, p2}, Lorg/codehaus/groovy/ast/ClassNode;->setRedirect(Lorg/codehaus/groovy/ast/ClassNode;)V

    return v1

    .line 502
    :cond_5
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->hasPackageName()Z

    move-result v0

    if-nez v0, :cond_6

    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolveNestedClass(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v0

    if-nez v0, :cond_9

    .line 503
    :cond_6
    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolveFromModule(Lorg/codehaus/groovy/ast/ClassNode;Z)Z

    move-result p2

    if-nez p2, :cond_9

    .line 504
    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolveFromCompileUnit(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result p2

    if-nez p2, :cond_9

    if-eqz p3, :cond_7

    .line 505
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->hasPackageName()Z

    move-result p2

    if-nez p2, :cond_7

    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolveFromDefaultImports(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result p2

    if-nez p2, :cond_9

    .line 506
    :cond_7
    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolveToOuter(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result p2

    if-nez p2, :cond_9

    if-eqz p4, :cond_8

    .line 507
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->hasPackageName()Z

    move-result p2

    if-eqz p2, :cond_8

    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolveFromStaticInnerClasses(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result p1

    if-eqz p1, :cond_8

    goto :goto_0

    :cond_8
    move v1, v3

    :cond_9
    :goto_0
    return v1
.end method

.method protected resolveFromCompileUnit(Lorg/codehaus/groovy/ast/ClassNode;)Z
    .locals 3

    .line 678
    iget-object v0, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->getCompileUnit()Lorg/codehaus/groovy/ast/CompileUnit;

    move-result-object v0

    const/4 v1, 0x0

    if-nez v0, :cond_0

    return v1

    .line 680
    :cond_0
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Lorg/codehaus/groovy/ast/CompileUnit;->getClass(Ljava/lang/String;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    if-eqz v0, :cond_2

    if-eq p1, v0, :cond_1

    .line 682
    invoke-virtual {p1, v0}, Lorg/codehaus/groovy/ast/ClassNode;->setRedirect(Lorg/codehaus/groovy/ast/ClassNode;)V

    :cond_1
    const/4 p1, 0x1

    return p1

    :cond_2
    return v1
.end method

.method protected resolveFromDefaultImports(Lorg/codehaus/groovy/ast/ClassNode;)Z
    .locals 4

    .line 618
    instance-of v0, p1, Lorg/codehaus/groovy/control/ResolveVisitor$LowerCaseClass;

    if-nez v0, :cond_3

    .line 619
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v0

    .line 621
    sget-object v1, Lorg/codehaus/groovy/control/ResolveVisitor;->DEFAULT_IMPORT_CLASS_AND_PACKAGES_CACHE:Ljava/util/Map;

    invoke-interface {v1, v0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/util/Set;

    const/4 v2, 0x1

    if-eqz v1, :cond_0

    .line 625
    sget-object v3, Lorg/codehaus/groovy/control/ResolveVisitor;->EMPTY_STRING_ARRAY:[Ljava/lang/String;

    invoke-interface {v1, v3}, Ljava/util/Set;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object v1

    check-cast v1, [Ljava/lang/String;

    invoke-virtual {p0, p1, v1}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolveFromDefaultImports(Lorg/codehaus/groovy/ast/ClassNode;[Ljava/lang/String;)Z

    move-result v1

    if-eqz v1, :cond_0

    return v2

    .line 630
    :cond_0
    sget-object v1, Lorg/codehaus/groovy/control/ResolveVisitor;->DEFAULT_IMPORTS:[Ljava/lang/String;

    invoke-virtual {p0, p1, v1}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolveFromDefaultImports(Lorg/codehaus/groovy/ast/ClassNode;[Ljava/lang/String;)Z

    move-result v1

    if-eqz v1, :cond_1

    return v2

    :cond_1
    const-string v1, "BigInteger"

    .line 633
    invoke-virtual {v1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_2

    .line 634
    sget-object v0, Lorg/codehaus/groovy/ast/ClassHelper;->BigInteger_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {p1, v0}, Lorg/codehaus/groovy/ast/ClassNode;->setRedirect(Lorg/codehaus/groovy/ast/ClassNode;)V

    return v2

    :cond_2
    const-string v1, "BigDecimal"

    .line 637
    invoke-virtual {v1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_3

    .line 638
    sget-object v0, Lorg/codehaus/groovy/ast/ClassHelper;->BigDecimal_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {p1, v0}, Lorg/codehaus/groovy/ast/ClassNode;->setRedirect(Lorg/codehaus/groovy/ast/ClassNode;)V

    return v2

    :cond_3
    const/4 p1, 0x0

    return p1
.end method

.method protected resolveFromDefaultImports(Lorg/codehaus/groovy/ast/ClassNode;[Ljava/lang/String;)Z
    .locals 7

    .line 651
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v0

    .line 653
    array-length v1, p2

    const/4 v2, 0x0

    move v3, v2

    :goto_0
    if-ge v3, v1, :cond_2

    aget-object v4, p2, v3

    .line 660
    new-instance v5, Lorg/codehaus/groovy/control/ResolveVisitor$ConstructedClassWithPackage;

    invoke-direct {v5, v4, v0}, Lorg/codehaus/groovy/control/ResolveVisitor$ConstructedClassWithPackage;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    .line 661
    invoke-virtual {p0, v5, v2, v2, v2}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolve(Lorg/codehaus/groovy/ast/ClassNode;ZZZ)Z

    move-result v6

    if-eqz v6, :cond_1

    .line 662
    invoke-virtual {v5}, Lorg/codehaus/groovy/control/ResolveVisitor$ConstructedClassWithPackage;->redirect()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    invoke-virtual {p1, v1}, Lorg/codehaus/groovy/ast/ClassNode;->setRedirect(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 664
    sget-object p1, Lorg/codehaus/groovy/control/ResolveVisitor;->DEFAULT_IMPORTS:[Ljava/lang/String;

    if-ne p1, p2, :cond_0

    .line 665
    sget-object p1, Lorg/codehaus/groovy/control/ResolveVisitor;->DEFAULT_IMPORT_CLASS_AND_PACKAGES_CACHE:Ljava/util/Map;

    sget-object p2, Lorg/codehaus/groovy/control/ResolveVisitor$$ExternalSyntheticLambda1;->INSTANCE:Lorg/codehaus/groovy/control/ResolveVisitor$$ExternalSyntheticLambda1;

    invoke-interface {p1, v0, p2}, Ljava/util/Map;->computeIfAbsent(Ljava/lang/Object;Ljava/util/function/Function;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/util/Set;

    .line 666
    invoke-interface {p1, v4}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    :cond_0
    const/4 p1, 0x1

    return p1

    :cond_1
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    :cond_2
    return v2
.end method

.method protected resolveFromModule(Lorg/codehaus/groovy/ast/ClassNode;Z)Z
    .locals 9

    .line 766
    instance-of v0, p1, Lorg/codehaus/groovy/control/ResolveVisitor$ConstructedNestedClass;

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    return v1

    .line 775
    :cond_0
    instance-of v0, p1, Lorg/codehaus/groovy/control/ResolveVisitor$LowerCaseClass;

    if-eqz v0, :cond_1

    .line 776
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolveAliasFromModule(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result p1

    return p1

    .line 779
    :cond_1
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v0

    .line 780
    iget-object v2, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/ClassNode;->getModule()Lorg/codehaus/groovy/ast/ModuleNode;

    move-result-object v2

    if-nez v2, :cond_2

    return v1

    .line 789
    :cond_2
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->hasPackageName()Z

    move-result v3

    const/4 v4, 0x1

    if-nez v3, :cond_3

    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/ModuleNode;->hasPackageName()Z

    move-result v3

    if-eqz v3, :cond_3

    instance-of v3, p1, Lorg/codehaus/groovy/control/ResolveVisitor$ConstructedClassWithPackage;

    if-nez v3, :cond_3

    .line 790
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/ModuleNode;->getPackageName()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v3, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {p1, v3}, Lorg/codehaus/groovy/ast/ClassNode;->setName(Ljava/lang/String;)Ljava/lang/String;

    move v3, v4

    goto :goto_0

    :cond_3
    move v3, v1

    .line 794
    :goto_0
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/ModuleNode;->getClasses()Ljava/util/List;

    move-result-object v5

    .line 795
    invoke-interface {v5}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v5

    :cond_4
    invoke-interface {v5}, Ljava/util/Iterator;->hasNext()Z

    move-result v6

    if-eqz v6, :cond_6

    invoke-interface {v5}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Lorg/codehaus/groovy/ast/ClassNode;

    .line 796
    invoke-virtual {v6}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v7

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v8

    invoke-virtual {v7, v8}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v7

    if-eqz v7, :cond_4

    if-eq v6, p1, :cond_5

    .line 797
    invoke-virtual {p1, v6}, Lorg/codehaus/groovy/ast/ClassNode;->setRedirect(Lorg/codehaus/groovy/ast/ClassNode;)V

    :cond_5
    return v4

    :cond_6
    if-eqz v3, :cond_7

    .line 801
    invoke-virtual {p1, v0}, Lorg/codehaus/groovy/ast/ClassNode;->setName(Ljava/lang/String;)Ljava/lang/String;

    :cond_7
    if-eqz p2, :cond_10

    .line 804
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolveAliasFromModule(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result p2

    if-eqz p2, :cond_8

    return v4

    .line 806
    :cond_8
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/ModuleNode;->hasPackageName()Z

    move-result p2

    if-eqz p2, :cond_9

    .line 810
    new-instance p2, Lorg/codehaus/groovy/control/ResolveVisitor$ConstructedClassWithPackage;

    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/ModuleNode;->getPackageName()Ljava/lang/String;

    move-result-object v3

    invoke-direct {p2, v3, v0}, Lorg/codehaus/groovy/control/ResolveVisitor$ConstructedClassWithPackage;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    .line 811
    invoke-virtual {p0, p2, v1, v1, v1}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolve(Lorg/codehaus/groovy/ast/ClassNode;ZZZ)Z

    move-result v3

    if-eqz v3, :cond_9

    .line 812
    invoke-direct {p0, p1, p2, v0}, Lorg/codehaus/groovy/control/ResolveVisitor;->ambiguousClass(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)V

    return v4

    .line 817
    :cond_9
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/ModuleNode;->getStaticImports()Ljava/util/Map;

    move-result-object p2

    invoke-interface {p2}, Ljava/util/Map;->values()Ljava/util/Collection;

    move-result-object p2

    invoke-interface {p2}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object p2

    :cond_a
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_b

    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lorg/codehaus/groovy/ast/ImportNode;

    .line 818
    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/ImportNode;->getFieldName()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v5, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v5

    if-eqz v5, :cond_a

    .line 819
    new-instance v5, Lorg/codehaus/groovy/control/ResolveVisitor$ConstructedNestedClass;

    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/ImportNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v3

    invoke-direct {v5, v3, v0}, Lorg/codehaus/groovy/control/ResolveVisitor$ConstructedNestedClass;-><init>(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)V

    .line 820
    invoke-virtual {p0, v5, v1, v1, v4}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolve(Lorg/codehaus/groovy/ast/ClassNode;ZZZ)Z

    move-result v3

    if-eqz v3, :cond_a

    invoke-virtual {v5}, Lorg/codehaus/groovy/ast/ClassNode;->getModifiers()I

    move-result v3

    and-int/lit8 v3, v3, 0x8

    if-eqz v3, :cond_a

    .line 821
    invoke-virtual {v5}, Lorg/codehaus/groovy/ast/ClassNode;->redirect()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p2

    invoke-virtual {p1, p2}, Lorg/codehaus/groovy/ast/ClassNode;->setRedirect(Lorg/codehaus/groovy/ast/ClassNode;)V

    return v4

    .line 826
    :cond_b
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/ModuleNode;->getStaticStarImports()Ljava/util/Map;

    move-result-object p2

    invoke-interface {p2}, Ljava/util/Map;->values()Ljava/util/Collection;

    move-result-object p2

    invoke-interface {p2}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object p2

    :cond_c
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_d

    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lorg/codehaus/groovy/ast/ImportNode;

    .line 827
    new-instance v5, Lorg/codehaus/groovy/control/ResolveVisitor$ConstructedNestedClass;

    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/ImportNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v3

    invoke-direct {v5, v3, v0}, Lorg/codehaus/groovy/control/ResolveVisitor$ConstructedNestedClass;-><init>(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)V

    .line 828
    invoke-virtual {p0, v5, v1, v1, v4}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolve(Lorg/codehaus/groovy/ast/ClassNode;ZZZ)Z

    move-result v3

    if-eqz v3, :cond_c

    invoke-virtual {v5}, Lorg/codehaus/groovy/ast/ClassNode;->getModifiers()I

    move-result v3

    and-int/lit8 v3, v3, 0x8

    if-eqz v3, :cond_c

    .line 829
    invoke-direct {p0, p1, v5, v0}, Lorg/codehaus/groovy/control/ResolveVisitor;->ambiguousClass(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)V

    return v4

    .line 834
    :cond_d
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/ModuleNode;->getStarImports()Ljava/util/List;

    move-result-object p2

    invoke-interface {p2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p2

    :cond_e
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_10

    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lorg/codehaus/groovy/ast/ImportNode;

    .line 835
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/ImportNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v3

    if-eqz v3, :cond_f

    .line 836
    new-instance v3, Lorg/codehaus/groovy/control/ResolveVisitor$ConstructedNestedClass;

    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/ImportNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v2

    invoke-direct {v3, v2, v0}, Lorg/codehaus/groovy/control/ResolveVisitor$ConstructedNestedClass;-><init>(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)V

    .line 837
    invoke-virtual {p0, v3, v1, v1, v4}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolve(Lorg/codehaus/groovy/ast/ClassNode;ZZZ)Z

    move-result v2

    if-eqz v2, :cond_e

    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/ClassNode;->getModifiers()I

    move-result v2

    and-int/lit8 v2, v2, 0x8

    if-eqz v2, :cond_e

    .line 838
    invoke-direct {p0, p1, v3, v0}, Lorg/codehaus/groovy/control/ResolveVisitor;->ambiguousClass(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)V

    return v4

    .line 842
    :cond_f
    new-instance v3, Lorg/codehaus/groovy/control/ResolveVisitor$ConstructedClassWithPackage;

    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/ImportNode;->getPackageName()Ljava/lang/String;

    move-result-object v2

    invoke-direct {v3, v2, v0}, Lorg/codehaus/groovy/control/ResolveVisitor$ConstructedClassWithPackage;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    .line 843
    invoke-virtual {p0, v3, v1, v1, v4}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolve(Lorg/codehaus/groovy/ast/ClassNode;ZZZ)Z

    move-result v2

    if-eqz v2, :cond_e

    .line 844
    invoke-direct {p0, p1, v3, v0}, Lorg/codehaus/groovy/control/ResolveVisitor;->ambiguousClass(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)V

    return v4

    :cond_10
    return v1
.end method

.method protected resolveFromStaticInnerClasses(Lorg/codehaus/groovy/ast/ClassNode;)Z
    .locals 5

    .line 591
    instance-of v0, p1, Lorg/codehaus/groovy/control/ResolveVisitor$LowerCaseClass;

    const/4 v1, 0x0

    if-nez v0, :cond_3

    instance-of v0, p1, Lorg/codehaus/groovy/control/ResolveVisitor$ConstructedNestedClass;

    if-nez v0, :cond_3

    .line 592
    instance-of v0, p1, Lorg/codehaus/groovy/control/ResolveVisitor$ConstructedClassWithPackage;

    const/4 v2, 0x1

    if-eqz v0, :cond_1

    .line 596
    move-object v0, p1

    check-cast v0, Lorg/codehaus/groovy/control/ResolveVisitor$ConstructedClassWithPackage;

    .line 597
    iget-object v3, v0, Lorg/codehaus/groovy/control/ResolveVisitor$ConstructedClassWithPackage;->className:Ljava/lang/String;

    .line 598
    invoke-static {v3}, Lorg/codehaus/groovy/control/ResolveVisitor;->replaceLastPointWithDollar(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v4

    iput-object v4, v0, Lorg/codehaus/groovy/control/ResolveVisitor$ConstructedClassWithPackage;->className:Ljava/lang/String;

    .line 599
    invoke-virtual {p0, v0, v1, v2, v2}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolve(Lorg/codehaus/groovy/ast/ClassNode;ZZZ)Z

    move-result v4

    if-eqz v4, :cond_0

    .line 600
    invoke-virtual {v0}, Lorg/codehaus/groovy/control/ResolveVisitor$ConstructedClassWithPackage;->redirect()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    invoke-virtual {p1, v0}, Lorg/codehaus/groovy/ast/ClassNode;->setRedirect(Lorg/codehaus/groovy/ast/ClassNode;)V

    return v2

    .line 603
    :cond_0
    iput-object v3, v0, Lorg/codehaus/groovy/control/ResolveVisitor$ConstructedClassWithPackage;->className:Ljava/lang/String;

    goto :goto_0

    .line 605
    :cond_1
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v0

    .line 606
    invoke-static {v0}, Lorg/codehaus/groovy/control/ResolveVisitor;->replaceLastPointWithDollar(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {p1, v3}, Lorg/codehaus/groovy/ast/ClassNode;->setName(Ljava/lang/String;)Ljava/lang/String;

    .line 607
    invoke-virtual {p0, p1, v1, v2, v2}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolve(Lorg/codehaus/groovy/ast/ClassNode;ZZZ)Z

    move-result v3

    if-eqz v3, :cond_2

    return v2

    .line 608
    :cond_2
    invoke-virtual {p1, v0}, Lorg/codehaus/groovy/ast/ClassNode;->setName(Ljava/lang/String;)Ljava/lang/String;

    :cond_3
    :goto_0
    return v1
.end method

.method protected resolveNestedClass(Lorg/codehaus/groovy/ast/ClassNode;)Z
    .locals 4

    .line 511
    instance-of v0, p1, Lorg/codehaus/groovy/control/ResolveVisitor$ConstructedNestedClass;

    const/4 v1, 0x0

    if-nez v0, :cond_5

    instance-of v0, p1, Lorg/codehaus/groovy/control/ResolveVisitor$ConstructedClassWithPackage;

    if-eqz v0, :cond_0

    goto :goto_0

    .line 518
    :cond_0
    iget-object v0, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {v0}, Lorg/codehaus/groovy/control/ResolveVisitor;->findHierClasses(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/util/Map;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Map;->values()Ljava/util/Collection;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_1
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    const/4 v3, 0x1

    if-eqz v2, :cond_2

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lorg/codehaus/groovy/ast/ClassNode;

    .line 519
    invoke-direct {p0, p1, v2}, Lorg/codehaus/groovy/control/ResolveVisitor;->setRedirect(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v2

    if-eqz v2, :cond_1

    return v3

    .line 523
    :cond_2
    iget-object v0, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->possibleOuterClassNodeMap:Ljava/util/Map;

    invoke-interface {v0, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/ast/ClassNode;

    if-eqz v0, :cond_3

    .line 525
    invoke-direct {p0, p1, v0}, Lorg/codehaus/groovy/control/ResolveVisitor;->setRedirect(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v0

    if-eqz v0, :cond_3

    return v3

    .line 534
    :cond_3
    iget-object v0, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->getOuterClasses()Ljava/util/List;

    move-result-object v0

    .line 535
    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v2

    if-nez v2, :cond_5

    .line 543
    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v2

    invoke-interface {v0, v2}, Ljava/util/List;->listIterator(I)Ljava/util/ListIterator;

    move-result-object v0

    :cond_4
    invoke-interface {v0}, Ljava/util/ListIterator;->hasPrevious()Z

    move-result v2

    if-eqz v2, :cond_5

    .line 544
    invoke-interface {v0}, Ljava/util/ListIterator;->previous()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lorg/codehaus/groovy/ast/ClassNode;

    .line 545
    invoke-direct {p0, p1, v2}, Lorg/codehaus/groovy/control/ResolveVisitor;->setRedirect(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v2

    if-eqz v2, :cond_4

    return v3

    :cond_5
    :goto_0
    return v1
.end method

.method protected resolveToInner(Lorg/codehaus/groovy/ast/ClassNode;)Z
    .locals 5

    .line 349
    instance-of v0, p1, Lorg/codehaus/groovy/control/ResolveVisitor$ConstructedClassWithPackage;

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    return v1

    .line 350
    :cond_0
    instance-of v0, p1, Lorg/codehaus/groovy/control/ResolveVisitor$ConstructedNestedClass;

    if-eqz v0, :cond_1

    return v1

    .line 354
    :cond_1
    :goto_0
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->isArray()Z

    move-result v0

    if-eqz v0, :cond_2

    .line 355
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getComponentType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    goto :goto_0

    .line 358
    :cond_2
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v0

    move-object v2, v0

    :cond_3
    const/16 v3, 0x2e

    .line 359
    invoke-virtual {v2, v3}, Ljava/lang/String;->lastIndexOf(I)I

    move-result v3

    const/4 v4, -0x1

    if-eq v3, v4, :cond_4

    .line 360
    invoke-static {v2}, Lorg/codehaus/groovy/control/ResolveVisitor;->replaceLastPointWithDollar(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    .line 361
    invoke-virtual {p1, v2}, Lorg/codehaus/groovy/ast/ClassNode;->setName(Ljava/lang/String;)Ljava/lang/String;

    const/4 v3, 0x1

    .line 362
    invoke-virtual {p0, p1, v3, v1, v1}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolve(Lorg/codehaus/groovy/ast/ClassNode;ZZZ)Z

    move-result v4

    if-eqz v4, :cond_3

    return v3

    .line 366
    :cond_4
    invoke-virtual {p1, v0}, Lorg/codehaus/groovy/ast/ClassNode;->setName(Ljava/lang/String;)Ljava/lang/String;

    return v1
.end method

.method protected resolveToOuter(Lorg/codehaus/groovy/ast/ClassNode;)Z
    .locals 4

    .line 855
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v0

    .line 862
    instance-of v1, p1, Lorg/codehaus/groovy/control/ResolveVisitor$LowerCaseClass;

    const/4 v2, 0x0

    if-eqz v1, :cond_0

    .line 863
    iget-object p1, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->classNodeResolver:Lorg/codehaus/groovy/control/ClassNodeResolver;

    sget-object v1, Lorg/codehaus/groovy/control/ClassNodeResolver;->NO_CLASS:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {p1, v0, v1}, Lorg/codehaus/groovy/control/ClassNodeResolver;->cacheClass(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)V

    return v2

    .line 867
    :cond_0
    iget-object v1, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/ClassNode;->getModule()Lorg/codehaus/groovy/ast/ModuleNode;

    move-result-object v1

    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/ModuleNode;->hasPackageName()Z

    move-result v1

    if-eqz v1, :cond_1

    const/16 v1, 0x2e

    invoke-virtual {v0, v1}, Ljava/lang/String;->indexOf(I)I

    move-result v1

    const/4 v3, -0x1

    if-ne v1, v3, :cond_1

    return v2

    .line 869
    :cond_1
    iget-object v1, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->classNodeResolver:Lorg/codehaus/groovy/control/ClassNodeResolver;

    iget-object v3, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->compilationUnit:Lorg/codehaus/groovy/control/CompilationUnit;

    invoke-virtual {v1, v0, v3}, Lorg/codehaus/groovy/control/ClassNodeResolver;->resolveName(Ljava/lang/String;Lorg/codehaus/groovy/control/CompilationUnit;)Lorg/codehaus/groovy/control/ClassNodeResolver$LookupResult;

    move-result-object v0

    if-eqz v0, :cond_3

    .line 871
    invoke-virtual {v0}, Lorg/codehaus/groovy/control/ClassNodeResolver$LookupResult;->isSourceUnit()Z

    move-result v1

    if-eqz v1, :cond_2

    .line 872
    invoke-virtual {v0}, Lorg/codehaus/groovy/control/ClassNodeResolver$LookupResult;->getSourceUnit()Lorg/codehaus/groovy/control/SourceUnit;

    move-result-object v0

    .line 873
    iget-object v1, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/ClassNode;->getCompileUnit()Lorg/codehaus/groovy/ast/CompileUnit;

    move-result-object v1

    invoke-virtual {v1, p1, v0}, Lorg/codehaus/groovy/ast/CompileUnit;->addClassNodeToCompile(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/control/SourceUnit;)V

    goto :goto_0

    .line 875
    :cond_2
    invoke-virtual {v0}, Lorg/codehaus/groovy/control/ClassNodeResolver$LookupResult;->getClassNode()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    invoke-virtual {p1, v0}, Lorg/codehaus/groovy/ast/ClassNode;->setRedirect(Lorg/codehaus/groovy/ast/ClassNode;)V

    :goto_0
    const/4 p1, 0x1

    return p1

    :cond_3
    return v2
.end method

.method public setClassNodeResolver(Lorg/codehaus/groovy/control/ClassNodeResolver;)V
    .locals 0

    .line 257
    iput-object p1, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->classNodeResolver:Lorg/codehaus/groovy/control/ClassNodeResolver;

    return-void
.end method

.method public startResolving(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/control/SourceUnit;)V
    .locals 0

    .line 261
    iput-object p2, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->source:Lorg/codehaus/groovy/control/SourceUnit;

    .line 262
    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/control/ResolveVisitor;->visitClass(Lorg/codehaus/groovy/ast/ClassNode;)V

    return-void
.end method

.method public transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 2

    if-nez p1, :cond_0

    const/4 p1, 0x0

    return-object p1

    .line 887
    :cond_0
    instance-of v0, p1, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    if-eqz v0, :cond_1

    .line 888
    move-object v0, p1

    check-cast v0, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/control/ResolveVisitor;->transformVariableExpression(Lorg/codehaus/groovy/ast/expr/VariableExpression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    goto :goto_0

    .line 889
    :cond_1
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    const-class v1, Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    if-ne v0, v1, :cond_2

    .line 890
    move-object v0, p1

    check-cast v0, Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/control/ResolveVisitor;->transformPropertyExpression(Lorg/codehaus/groovy/ast/expr/PropertyExpression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    goto :goto_0

    .line 891
    :cond_2
    instance-of v0, p1, Lorg/codehaus/groovy/ast/expr/DeclarationExpression;

    if-eqz v0, :cond_3

    .line 892
    move-object v0, p1

    check-cast v0, Lorg/codehaus/groovy/ast/expr/DeclarationExpression;

    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/control/ResolveVisitor;->transformDeclarationExpression(Lorg/codehaus/groovy/ast/expr/DeclarationExpression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    goto :goto_0

    .line 893
    :cond_3
    instance-of v0, p1, Lorg/codehaus/groovy/ast/expr/BinaryExpression;

    if-eqz v0, :cond_4

    .line 894
    move-object v0, p1

    check-cast v0, Lorg/codehaus/groovy/ast/expr/BinaryExpression;

    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/control/ResolveVisitor;->transformBinaryExpression(Lorg/codehaus/groovy/ast/expr/BinaryExpression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    goto :goto_0

    .line 895
    :cond_4
    instance-of v0, p1, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    if-eqz v0, :cond_5

    .line 896
    move-object v0, p1

    check-cast v0, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/control/ResolveVisitor;->transformMethodCallExpression(Lorg/codehaus/groovy/ast/expr/MethodCallExpression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    goto :goto_0

    .line 897
    :cond_5
    instance-of v0, p1, Lorg/codehaus/groovy/ast/expr/ClosureExpression;

    if-eqz v0, :cond_6

    .line 898
    move-object v0, p1

    check-cast v0, Lorg/codehaus/groovy/ast/expr/ClosureExpression;

    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/control/ResolveVisitor;->transformClosureExpression(Lorg/codehaus/groovy/ast/expr/ClosureExpression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    goto :goto_0

    .line 899
    :cond_6
    instance-of v0, p1, Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;

    if-eqz v0, :cond_7

    .line 900
    move-object v0, p1

    check-cast v0, Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;

    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/control/ResolveVisitor;->transformConstructorCallExpression(Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    goto :goto_0

    .line 901
    :cond_7
    instance-of v0, p1, Lorg/codehaus/groovy/ast/expr/AnnotationConstantExpression;

    if-eqz v0, :cond_8

    .line 902
    move-object v0, p1

    check-cast v0, Lorg/codehaus/groovy/ast/expr/AnnotationConstantExpression;

    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/control/ResolveVisitor;->transformAnnotationConstantExpression(Lorg/codehaus/groovy/ast/expr/AnnotationConstantExpression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    goto :goto_0

    .line 904
    :cond_8
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/Expression;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    invoke-direct {p0, v0, p1}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolveOrFail(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 905
    invoke-virtual {p1, p0}, Lorg/codehaus/groovy/ast/expr/Expression;->transformExpression(Lorg/codehaus/groovy/ast/expr/ExpressionTransformer;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    :goto_0
    if-eqz v0, :cond_9

    if-eq v0, p1, :cond_9

    .line 908
    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/ast/expr/Expression;->setSourcePosition(Lorg/codehaus/groovy/ast/ASTNode;)V

    :cond_9
    return-object v0
.end method

.method protected transformAnnotationConstantExpression(Lorg/codehaus/groovy/ast/expr/AnnotationConstantExpression;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 3

    .line 1319
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/AnnotationConstantExpression;->getValue()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/ast/AnnotationNode;

    .line 1320
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/AnnotationNode;->getClassNode()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    const-string v2, " for annotation"

    .line 1321
    invoke-direct {p0, v1, v2, v0}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolveOrFail(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 1322
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/AnnotationNode;->getMembers()Ljava/util/Map;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/util/Map$Entry;

    .line 1323
    invoke-interface {v1}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lorg/codehaus/groovy/ast/expr/Expression;

    invoke-virtual {p0, v2}, Lorg/codehaus/groovy/control/ResolveVisitor;->transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v2

    invoke-interface {v1, v2}, Ljava/util/Map$Entry;->setValue(Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    :cond_0
    return-object p1
.end method

.method protected transformBinaryExpression(Lorg/codehaus/groovy/ast/expr/BinaryExpression;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 5

    .line 1154
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/BinaryExpression;->getLeftExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/control/ResolveVisitor;->transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    .line 1155
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/BinaryExpression;->getOperation()Lorg/codehaus/groovy/syntax/Token;

    move-result-object v1

    const/16 v2, 0x44c

    invoke-virtual {v1, v2}, Lorg/codehaus/groovy/syntax/Token;->isA(I)Z

    move-result v1

    if-eqz v1, :cond_1

    instance-of v1, v0, Lorg/codehaus/groovy/ast/expr/ClassExpression;

    if-eqz v1, :cond_1

    .line 1156
    check-cast v0, Lorg/codehaus/groovy/ast/expr/ClassExpression;

    .line 1157
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "you tried to assign a value to the class \'"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/ClassExpression;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v2

    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v2, "\'"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    .line 1158
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/ClassExpression;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->isScript()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 1159
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ". Do you have a script with this name?"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    .line 1161
    :cond_0
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/BinaryExpression;->getLeftExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    invoke-virtual {p0, v1, v0}, Lorg/codehaus/groovy/control/ResolveVisitor;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    return-object p1

    .line 1164
    :cond_1
    instance-of v1, v0, Lorg/codehaus/groovy/ast/expr/ClassExpression;

    if-eqz v1, :cond_8

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/BinaryExpression;->getOperation()Lorg/codehaus/groovy/syntax/Token;

    move-result-object v1

    const/4 v2, 0x3

    new-array v2, v2, [I

    fill-array-data v2, :array_0

    invoke-virtual {v1, v2}, Lorg/codehaus/groovy/syntax/Token;->isOneOf([I)Z

    move-result v1

    if-eqz v1, :cond_8

    .line 1166
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/BinaryExpression;->getRightExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v1

    instance-of v1, v1, Lorg/codehaus/groovy/ast/expr/ListExpression;

    if-eqz v1, :cond_6

    .line 1167
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/BinaryExpression;->getRightExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/ast/expr/ListExpression;

    .line 1168
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/expr/ListExpression;->getExpressions()Ljava/util/List;

    move-result-object v2

    invoke-interface {v2}, Ljava/util/List;->isEmpty()Z

    move-result v2

    if-eqz v2, :cond_2

    .line 1169
    new-instance p1, Lorg/codehaus/groovy/ast/expr/ClassExpression;

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/Expression;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->makeArray()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    invoke-direct {p1, v0}, Lorg/codehaus/groovy/ast/expr/ClassExpression;-><init>(Lorg/codehaus/groovy/ast/ClassNode;)V

    return-object p1

    :cond_2
    const/4 v2, 0x1

    .line 1173
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/expr/ListExpression;->getExpressions()Ljava/util/List;

    move-result-object v3

    invoke-interface {v3}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v3

    :cond_3
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    move-result v4

    if-eqz v4, :cond_4

    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lorg/codehaus/groovy/ast/expr/Expression;

    .line 1174
    instance-of v4, v4, Lorg/codehaus/groovy/ast/expr/MapEntryExpression;

    if-nez v4, :cond_3

    const/4 v2, 0x0

    :cond_4
    if-eqz v2, :cond_7

    .line 1181
    new-instance p1, Lorg/codehaus/groovy/ast/expr/MapExpression;

    invoke-direct {p1}, Lorg/codehaus/groovy/ast/expr/MapExpression;-><init>()V

    .line 1182
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/expr/ListExpression;->getExpressions()Ljava/util/List;

    move-result-object v2

    invoke-interface {v2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :goto_0
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_5

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lorg/codehaus/groovy/ast/expr/Expression;

    .line 1183
    invoke-virtual {p0, v3}, Lorg/codehaus/groovy/control/ResolveVisitor;->transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v3

    check-cast v3, Lorg/codehaus/groovy/ast/expr/MapEntryExpression;

    invoke-virtual {p1, v3}, Lorg/codehaus/groovy/ast/expr/MapExpression;->addMapEntryExpression(Lorg/codehaus/groovy/ast/expr/MapEntryExpression;)V

    goto :goto_0

    .line 1185
    :cond_5
    invoke-virtual {p1, v1}, Lorg/codehaus/groovy/ast/expr/MapExpression;->setSourcePosition(Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 1186
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/Expression;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    invoke-static {v0, p1}, Lorg/codehaus/groovy/ast/expr/CastExpression;->asExpression(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/CastExpression;

    move-result-object p1

    return-object p1

    .line 1189
    :cond_6
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/BinaryExpression;->getRightExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v1

    instance-of v1, v1, Lorg/codehaus/groovy/ast/expr/SpreadMapExpression;

    if-eqz v1, :cond_7

    .line 1191
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/BinaryExpression;->getRightExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    check-cast p1, Lorg/codehaus/groovy/ast/expr/SpreadMapExpression;

    .line 1192
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/SpreadMapExpression;->getExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/control/ResolveVisitor;->transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    .line 1193
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/Expression;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    invoke-static {v0, p1}, Lorg/codehaus/groovy/ast/expr/CastExpression;->asExpression(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/CastExpression;

    move-result-object p1

    return-object p1

    .line 1196
    :cond_7
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/BinaryExpression;->getRightExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v1

    instance-of v1, v1, Lorg/codehaus/groovy/ast/expr/MapEntryExpression;

    if-eqz v1, :cond_8

    .line 1198
    new-instance v1, Lorg/codehaus/groovy/ast/expr/MapExpression;

    invoke-direct {v1}, Lorg/codehaus/groovy/ast/expr/MapExpression;-><init>()V

    .line 1199
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/BinaryExpression;->getRightExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v2

    invoke-virtual {p0, v2}, Lorg/codehaus/groovy/control/ResolveVisitor;->transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v2

    check-cast v2, Lorg/codehaus/groovy/ast/expr/MapEntryExpression;

    invoke-virtual {v1, v2}, Lorg/codehaus/groovy/ast/expr/MapExpression;->addMapEntryExpression(Lorg/codehaus/groovy/ast/expr/MapEntryExpression;)V

    .line 1200
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/BinaryExpression;->getRightExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    invoke-virtual {v1, p1}, Lorg/codehaus/groovy/ast/expr/MapExpression;->setSourcePosition(Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 1201
    new-instance p1, Lorg/codehaus/groovy/ast/expr/CastExpression;

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/Expression;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    invoke-direct {p1, v0, v1}, Lorg/codehaus/groovy/ast/expr/CastExpression;-><init>(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;)V

    return-object p1

    .line 1204
    :cond_8
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/BinaryExpression;->getRightExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v1

    invoke-virtual {p0, v1}, Lorg/codehaus/groovy/control/ResolveVisitor;->transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v1

    .line 1205
    invoke-virtual {p1, v0}, Lorg/codehaus/groovy/ast/expr/BinaryExpression;->setLeftExpression(Lorg/codehaus/groovy/ast/expr/Expression;)V

    .line 1206
    invoke-virtual {p1, v1}, Lorg/codehaus/groovy/ast/expr/BinaryExpression;->setRightExpression(Lorg/codehaus/groovy/ast/expr/Expression;)V

    return-object p1

    :array_0
    .array-data 4
        0x771
        0x32a
        0x32b
    .end array-data
.end method

.method protected transformClosureExpression(Lorg/codehaus/groovy/ast/expr/ClosureExpression;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 6

    .line 1211
    iget-boolean v0, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->inClosure:Z

    const/4 v1, 0x1

    .line 1212
    iput-boolean v1, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->inClosure:Z

    .line 1213
    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/ClosureUtils;->getParametersSafe(Lorg/codehaus/groovy/ast/expr/ClosureExpression;)[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v1

    array-length v2, v1

    const/4 v3, 0x0

    :goto_0
    if-ge v3, v2, :cond_1

    aget-object v4, v1, v3

    .line 1214
    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/Parameter;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v5

    .line 1215
    invoke-direct {p0, v5, p1}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolveOrFail(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 1216
    invoke-virtual {p0, v4}, Lorg/codehaus/groovy/control/ResolveVisitor;->visitAnnotations(Lorg/codehaus/groovy/ast/AnnotatedNode;)V

    .line 1217
    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/Parameter;->hasInitialExpression()Z

    move-result v5

    if-eqz v5, :cond_0

    .line 1218
    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/Parameter;->getInitialExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v5

    invoke-virtual {p0, v5}, Lorg/codehaus/groovy/control/ResolveVisitor;->transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v5

    invoke-virtual {v4, v5}, Lorg/codehaus/groovy/ast/Parameter;->setInitialExpression(Lorg/codehaus/groovy/ast/expr/Expression;)V

    .line 1220
    :cond_0
    invoke-virtual {p0, v4}, Lorg/codehaus/groovy/control/ResolveVisitor;->visitAnnotations(Lorg/codehaus/groovy/ast/AnnotatedNode;)V

    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    .line 1223
    :cond_1
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/ClosureExpression;->getCode()Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v1

    if-eqz v1, :cond_2

    .line 1224
    invoke-virtual {v1, p0}, Lorg/codehaus/groovy/ast/stmt/Statement;->visit(Lorg/codehaus/groovy/ast/GroovyCodeVisitor;)V

    .line 1225
    :cond_2
    iput-boolean v0, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->inClosure:Z

    return-object p1
.end method

.method protected transformConstructorCallExpression(Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 3

    .line 1230
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/control/ResolveVisitor;->findPossibleOuterClassNodeForNonStaticInnerClassInstantiation(Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;)V

    .line 1232
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    .line 1233
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;->isUsingAnonymousInnerClass()Z

    move-result v1

    if-eqz v1, :cond_0

    const/4 v1, 0x0

    .line 1234
    invoke-virtual {v0, v1}, Lorg/codehaus/groovy/ast/ClassNode;->getUnresolvedSuperClass(Z)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    invoke-direct {p0, v1, v0}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolveOrFail(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ASTNode;)V

    goto :goto_0

    .line 1236
    :cond_0
    invoke-direct {p0, v0, p1}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolveOrFail(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 1237
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->isAbstract()Z

    move-result v1

    if-eqz v1, :cond_1

    .line 1238
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "You cannot create an instance from the abstract "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-static {v0}, Lorg/codehaus/groovy/control/ResolveVisitor;->getDescription(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "."

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0, p1}, Lorg/codehaus/groovy/control/ResolveVisitor;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 1242
    :cond_1
    :goto_0
    invoke-virtual {p1, p0}, Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;->transformExpression(Lorg/codehaus/groovy/ast/expr/ExpressionTransformer;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    return-object p1
.end method

.method protected transformDeclarationExpression(Lorg/codehaus/groovy/ast/expr/DeclarationExpression;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 4

    .line 1288
    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/control/ResolveVisitor;->visitAnnotations(Lorg/codehaus/groovy/ast/AnnotatedNode;)V

    .line 1289
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/DeclarationExpression;->getLeftExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    const/4 v1, 0x1

    .line 1290
    iput-boolean v1, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->checkingVariableTypeInDeclaration:Z

    .line 1291
    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/control/ResolveVisitor;->transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v1

    const/4 v2, 0x0

    .line 1292
    iput-boolean v2, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->checkingVariableTypeInDeclaration:Z

    .line 1293
    instance-of v2, v1, Lorg/codehaus/groovy/ast/expr/ClassExpression;

    if-eqz v2, :cond_0

    .line 1294
    check-cast v1, Lorg/codehaus/groovy/ast/expr/ClassExpression;

    .line 1295
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "you tried to assign a value to the class "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/expr/ClassExpression;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0, v1, v0}, Lorg/codehaus/groovy/control/ResolveVisitor;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    return-object p1

    .line 1298
    :cond_0
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/DeclarationExpression;->getRightExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/control/ResolveVisitor;->transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    .line 1299
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/DeclarationExpression;->getRightExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v2

    if-ne v0, v2, :cond_1

    .line 1300
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/control/ResolveVisitor;->fixDeclaringClass(Lorg/codehaus/groovy/ast/expr/DeclarationExpression;)V

    return-object p1

    .line 1303
    :cond_1
    new-instance v2, Lorg/codehaus/groovy/ast/expr/DeclarationExpression;

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/DeclarationExpression;->getOperation()Lorg/codehaus/groovy/syntax/Token;

    move-result-object v3

    invoke-direct {v2, v1, v3, v0}, Lorg/codehaus/groovy/ast/expr/DeclarationExpression;-><init>(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/syntax/Token;Lorg/codehaus/groovy/ast/expr/Expression;)V

    .line 1304
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/DeclarationExpression;->getDeclaringClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    invoke-virtual {v2, v0}, Lorg/codehaus/groovy/ast/expr/DeclarationExpression;->setDeclaringClass(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 1305
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/DeclarationExpression;->getAnnotations()Ljava/util/List;

    move-result-object v0

    invoke-virtual {v2, v0}, Lorg/codehaus/groovy/ast/expr/DeclarationExpression;->addAnnotations(Ljava/util/List;)V

    .line 1306
    invoke-virtual {v2, p1}, Lorg/codehaus/groovy/ast/expr/DeclarationExpression;->copyNodeMetaData(Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 1307
    invoke-direct {p0, v2}, Lorg/codehaus/groovy/control/ResolveVisitor;->fixDeclaringClass(Lorg/codehaus/groovy/ast/expr/DeclarationExpression;)V

    return-object v2
.end method

.method protected transformMethodCallExpression(Lorg/codehaus/groovy/ast/expr/MethodCallExpression;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 4

    .line 1272
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->getArguments()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/control/ResolveVisitor;->transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    .line 1273
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->getMethod()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v1

    invoke-virtual {p0, v1}, Lorg/codehaus/groovy/control/ResolveVisitor;->transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v1

    .line 1274
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->getObjectExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v2

    invoke-virtual {p0, v2}, Lorg/codehaus/groovy/control/ResolveVisitor;->transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v2

    .line 1276
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->getGenericsTypes()[Lorg/codehaus/groovy/ast/GenericsType;

    move-result-object v3

    invoke-direct {p0, v3}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolveGenericsTypes([Lorg/codehaus/groovy/ast/GenericsType;)Z

    .line 1278
    new-instance v3, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    invoke-direct {v3, v2, v1, v0}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;-><init>(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)V

    .line 1279
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->getGenericsTypes()[Lorg/codehaus/groovy/ast/GenericsType;

    move-result-object v0

    invoke-virtual {v3, v0}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setGenericsTypes([Lorg/codehaus/groovy/ast/GenericsType;)V

    .line 1280
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->getMethodTarget()Lorg/codehaus/groovy/ast/MethodNode;

    move-result-object v0

    invoke-virtual {v3, v0}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setMethodTarget(Lorg/codehaus/groovy/ast/MethodNode;)V

    .line 1281
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->isImplicitThis()Z

    move-result v0

    invoke-virtual {v3, v0}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setImplicitThis(Z)V

    .line 1282
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->isSpreadSafe()Z

    move-result v0

    invoke-virtual {v3, v0}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setSpreadSafe(Z)V

    .line 1283
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->isSafe()Z

    move-result p1

    invoke-virtual {v3, p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setSafe(Z)V

    return-object v3
.end method

.method protected transformPropertyExpression(Lorg/codehaus/groovy/ast/expr/PropertyExpression;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 7

    .line 1007
    iget-boolean v0, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->isTopLevelProperty:Z

    .line 1008
    iget-boolean v1, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->inPropertyExpression:Z

    .line 1010
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->getObjectExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v2

    const/4 v3, 0x1

    .line 1011
    iput-boolean v3, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->inPropertyExpression:Z

    .line 1012
    invoke-virtual {v2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v4

    const-class v5, Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    const/4 v6, 0x0

    if-eq v4, v5, :cond_0

    goto :goto_0

    :cond_0
    move v3, v6

    :goto_0
    iput-boolean v3, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->isTopLevelProperty:Z

    .line 1013
    invoke-virtual {p0, v2}, Lorg/codehaus/groovy/control/ResolveVisitor;->transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v2

    .line 1015
    iput-boolean v6, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->inPropertyExpression:Z

    .line 1016
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->getProperty()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v3

    invoke-virtual {p0, v3}, Lorg/codehaus/groovy/control/ResolveVisitor;->transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v3

    .line 1017
    iput-boolean v0, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->isTopLevelProperty:Z

    .line 1018
    iput-boolean v1, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->inPropertyExpression:Z

    .line 1020
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->isSpreadSafe()Z

    move-result v0

    .line 1022
    new-instance v1, Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->isSafe()Z

    move-result v4

    invoke-direct {v1, v2, v3, v4}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;-><init>(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;Z)V

    .line 1023
    invoke-virtual {v1, v0}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->setSpreadSafe(Z)V

    .line 1024
    invoke-virtual {v1, p1}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->setSourcePosition(Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 1026
    invoke-static {v1}, Lorg/codehaus/groovy/control/ResolveVisitor;->lookupClassName(Lorg/codehaus/groovy/ast/expr/PropertyExpression;)Ljava/lang/String;

    move-result-object p1

    if-eqz p1, :cond_1

    .line 1028
    invoke-static {p1}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/String;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    .line 1029
    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolve(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v0

    if-eqz v0, :cond_1

    .line 1030
    new-instance v0, Lorg/codehaus/groovy/ast/expr/ClassExpression;

    invoke-direct {v0, p1}, Lorg/codehaus/groovy/ast/expr/ClassExpression;-><init>(Lorg/codehaus/groovy/ast/ClassNode;)V

    return-object v0

    .line 1033
    :cond_1
    instance-of p1, v2, Lorg/codehaus/groovy/ast/expr/ClassExpression;

    if-eqz p1, :cond_4

    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->getPropertyAsString()Ljava/lang/String;

    move-result-object p1

    if-eqz p1, :cond_4

    .line 1035
    check-cast v2, Lorg/codehaus/groovy/ast/expr/ClassExpression;

    .line 1036
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/expr/ClassExpression;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    :goto_1
    if-eqz p1, :cond_4

    .line 1038
    new-instance v0, Lorg/codehaus/groovy/control/ResolveVisitor$ConstructedNestedClass;

    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->getPropertyAsString()Ljava/lang/String;

    move-result-object v3

    invoke-direct {v0, p1, v3}, Lorg/codehaus/groovy/control/ResolveVisitor$ConstructedNestedClass;-><init>(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)V

    .line 1039
    invoke-virtual {p0, v0, v6, v6, v6}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolve(Lorg/codehaus/groovy/ast/ClassNode;ZZZ)Z

    move-result v3

    if-eqz v3, :cond_3

    .line 1040
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/expr/ClassExpression;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v3

    if-eq p1, v3, :cond_2

    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/expr/ClassExpression;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v3

    invoke-static {v0, v3}, Lorg/codehaus/groovy/control/ResolveVisitor;->isVisibleNestedClass(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v3

    if-eqz v3, :cond_3

    .line 1041
    :cond_2
    new-instance p1, Lorg/codehaus/groovy/ast/expr/ClassExpression;

    invoke-direct {p1, v0}, Lorg/codehaus/groovy/ast/expr/ClassExpression;-><init>(Lorg/codehaus/groovy/ast/ClassNode;)V

    return-object p1

    .line 1044
    :cond_3
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getSuperClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    goto :goto_1

    .line 1048
    :cond_4
    invoke-direct {p0, v1}, Lorg/codehaus/groovy/control/ResolveVisitor;->checkThisAndSuperAsPropertyAccess(Lorg/codehaus/groovy/ast/expr/PropertyExpression;)V

    .line 1049
    iget-boolean p1, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->isTopLevelProperty:Z

    if-eqz p1, :cond_5

    invoke-static {v1}, Lorg/codehaus/groovy/control/ResolveVisitor;->correctClassClassChain(Lorg/codehaus/groovy/ast/expr/PropertyExpression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v1

    :cond_5
    return-object v1
.end method

.method protected transformVariableExpression(Lorg/codehaus/groovy/ast/expr/VariableExpression;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 3

    .line 1104
    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/control/ResolveVisitor;->visitAnnotations(Lorg/codehaus/groovy/ast/AnnotatedNode;)V

    .line 1105
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->getAccessedVariable()Lorg/codehaus/groovy/ast/Variable;

    move-result-object v0

    .line 1107
    instance-of v0, v0, Lorg/codehaus/groovy/ast/DynamicVariable;

    if-nez v0, :cond_0

    iget-boolean v1, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->checkingVariableTypeInDeclaration:Z

    if-nez v1, :cond_0

    return-object p1

    :cond_0
    if-eqz v0, :cond_5

    .line 1115
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->getName()Ljava/lang/String;

    move-result-object v0

    .line 1116
    invoke-static {v0}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/String;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    .line 1120
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/ClassNode;->isResolved()Z

    move-result v2

    if-nez v2, :cond_2

    const/4 v2, 0x0

    .line 1126
    invoke-virtual {v0, v2}, Ljava/lang/String;->charAt(I)C

    move-result v2

    invoke-static {v2}, Ljava/lang/Character;->isLowerCase(C)Z

    move-result v2

    if-eqz v2, :cond_1

    .line 1127
    new-instance v1, Lorg/codehaus/groovy/control/ResolveVisitor$LowerCaseClass;

    invoke-direct {v1, v0}, Lorg/codehaus/groovy/control/ResolveVisitor$LowerCaseClass;-><init>(Ljava/lang/String;)V

    .line 1129
    :cond_1
    invoke-virtual {p0, v1}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolve(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v2

    :cond_2
    if-eqz v2, :cond_5

    .line 1136
    iget-object v0, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->currentScope:Lorg/codehaus/groovy/ast/VariableScope;

    :goto_0
    if-eqz v0, :cond_4

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/VariableScope;->isRoot()Z

    move-result v2

    if-nez v2, :cond_4

    .line 1137
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->getName()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Lorg/codehaus/groovy/ast/VariableScope;->removeReferencedClassVariable(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v2

    if-nez v2, :cond_3

    goto :goto_1

    .line 1136
    :cond_3
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/VariableScope;->getParent()Lorg/codehaus/groovy/ast/VariableScope;

    move-result-object v0

    goto :goto_0

    .line 1139
    :cond_4
    :goto_1
    new-instance p1, Lorg/codehaus/groovy/ast/expr/ClassExpression;

    invoke-direct {p1, v1}, Lorg/codehaus/groovy/ast/expr/ClassExpression;-><init>(Lorg/codehaus/groovy/ast/ClassNode;)V

    return-object p1

    .line 1142
    :cond_5
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    invoke-direct {p0, v0, p1}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolveOrFail(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 1143
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->getOriginType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    .line 1144
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    if-eq v0, v1, :cond_6

    invoke-direct {p0, v0, p1}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolveOrFail(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ASTNode;)V

    :cond_6
    return-object p1
.end method

.method public visitAnnotations(Lorg/codehaus/groovy/ast/AnnotatedNode;)V
    .locals 6

    .line 1330
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/AnnotatedNode;->getAnnotations()Ljava/util/List;

    move-result-object p1

    .line 1331
    invoke-interface {p1}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    .line 1332
    :cond_0
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    .line 1333
    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_1
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_4

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/ast/AnnotationNode;

    .line 1335
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/AnnotationNode;->isBuiltIn()Z

    move-result v2

    if-eqz v2, :cond_2

    goto :goto_0

    .line 1336
    :cond_2
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/AnnotationNode;->getClassNode()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v2

    const-string v3, " for annotation"

    .line 1337
    invoke-direct {p0, v2, v3, v1}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolveOrFail(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 1338
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/AnnotationNode;->getMembers()Ljava/util/Map;

    move-result-object v3

    invoke-interface {v3}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    move-result-object v3

    invoke-interface {v3}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v3

    :goto_1
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    move-result v4

    if-eqz v4, :cond_3

    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Ljava/util/Map$Entry;

    .line 1339
    invoke-interface {v4}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lorg/codehaus/groovy/ast/expr/Expression;

    invoke-virtual {p0, v5}, Lorg/codehaus/groovy/control/ResolveVisitor;->transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v5

    .line 1340
    invoke-static {v5}, Lorg/codehaus/groovy/control/ResolveVisitor;->transformInlineConstants(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v5

    .line 1341
    invoke-interface {v4, v5}, Ljava/util/Map$Entry;->setValue(Ljava/lang/Object;)Ljava/lang/Object;

    .line 1342
    invoke-direct {p0, v5}, Lorg/codehaus/groovy/control/ResolveVisitor;->checkAnnotationMemberValue(Lorg/codehaus/groovy/ast/expr/Expression;)V

    goto :goto_1

    .line 1344
    :cond_3
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/ClassNode;->isResolved()Z

    move-result v3

    if-eqz v3, :cond_1

    .line 1345
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/ClassNode;->getTypeClass()Ljava/lang/Class;

    move-result-object v3

    .line 1346
    const-class v4, Ljava/lang/annotation/Retention;

    invoke-virtual {v3, v4}, Ljava/lang/Class;->getAnnotation(Ljava/lang/Class;)Ljava/lang/annotation/Annotation;

    move-result-object v4

    check-cast v4, Ljava/lang/annotation/Retention;

    if-eqz v4, :cond_1

    .line 1347
    invoke-interface {v4}, Ljava/lang/annotation/Retention;->value()Ljava/lang/annotation/RetentionPolicy;

    move-result-object v4

    sget-object v5, Ljava/lang/annotation/RetentionPolicy;->SOURCE:Ljava/lang/annotation/RetentionPolicy;

    invoke-virtual {v4, v5}, Ljava/lang/annotation/RetentionPolicy;->equals(Ljava/lang/Object;)Z

    move-result v4

    if-nez v4, :cond_1

    invoke-direct {p0, v3}, Lorg/codehaus/groovy/control/ResolveVisitor;->isRepeatable(Ljava/lang/Class;)Z

    move-result v4

    if-nez v4, :cond_1

    .line 1349
    invoke-virtual {v3}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v3

    invoke-interface {v0, v3, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lorg/codehaus/groovy/ast/AnnotationNode;

    if-eqz v3, :cond_1

    .line 1351
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "Cannot specify duplicate annotation on the same member : "

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {p0, v2, v1}, Lorg/codehaus/groovy/control/ResolveVisitor;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    goto/16 :goto_0

    :cond_4
    return-void
.end method

.method public visitBlockStatement(Lorg/codehaus/groovy/ast/stmt/BlockStatement;)V
    .locals 2

    .line 1564
    iget-object v0, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->currentScope:Lorg/codehaus/groovy/ast/VariableScope;

    .line 1565
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->getVariableScope()Lorg/codehaus/groovy/ast/VariableScope;

    move-result-object v1

    iput-object v1, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->currentScope:Lorg/codehaus/groovy/ast/VariableScope;

    .line 1566
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeExpressionTransformer;->visitBlockStatement(Lorg/codehaus/groovy/ast/stmt/BlockStatement;)V

    .line 1567
    iput-object v0, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->currentScope:Lorg/codehaus/groovy/ast/VariableScope;

    return-void
.end method

.method public visitCatchStatement(Lorg/codehaus/groovy/ast/stmt/CatchStatement;)V
    .locals 2

    .line 1549
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/stmt/CatchStatement;->getExceptionType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    invoke-direct {p0, v0, p1}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolveOrFail(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 1550
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/stmt/CatchStatement;->getExceptionType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    sget-object v1, Lorg/codehaus/groovy/ast/ClassHelper;->DYNAMIC_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    if-ne v0, v1, :cond_0

    .line 1551
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/stmt/CatchStatement;->getVariable()Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v0

    const-class v1, Ljava/lang/Exception;

    invoke-static {v1}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    invoke-virtual {v0, v1}, Lorg/codehaus/groovy/ast/Parameter;->setType(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 1553
    :cond_0
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeExpressionTransformer;->visitCatchStatement(Lorg/codehaus/groovy/ast/stmt/CatchStatement;)V

    return-void
.end method

.method public visitClass(Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 11

    .line 1402
    iget-object v0, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    .line 1404
    iput-object p1, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    .line 1406
    instance-of v1, p1, Lorg/codehaus/groovy/ast/InnerClassNode;

    if-eqz v1, :cond_1

    .line 1407
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getModifiers()I

    move-result v1

    invoke-static {v1}, Ljava/lang/reflect/Modifier;->isStatic(I)Z

    move-result v1

    if-eqz v1, :cond_0

    .line 1408
    new-instance v1, Ljava/util/HashMap;

    invoke-direct {v1}, Ljava/util/HashMap;-><init>()V

    iput-object v1, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->genericParameterNames:Ljava/util/Map;

    .line 1411
    :cond_0
    move-object v1, p1

    check-cast v1, Lorg/codehaus/groovy/ast/InnerClassNode;

    .line 1412
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/InnerClassNode;->isAnonymous()Z

    move-result v2

    if-eqz v2, :cond_2

    .line 1413
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/InnerClassNode;->getEnclosingMethod()Lorg/codehaus/groovy/ast/MethodNode;

    move-result-object v1

    if-eqz v1, :cond_2

    .line 1415
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/MethodNode;->getGenericsTypes()[Lorg/codehaus/groovy/ast/GenericsType;

    move-result-object v1

    invoke-direct {p0, v1}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolveGenericsHeader([Lorg/codehaus/groovy/ast/GenericsType;)V

    goto :goto_0

    .line 1419
    :cond_1
    new-instance v1, Ljava/util/HashMap;

    invoke-direct {v1}, Ljava/util/HashMap;-><init>()V

    iput-object v1, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->genericParameterNames:Ljava/util/Map;

    .line 1422
    :cond_2
    :goto_0
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getGenericsTypes()[Lorg/codehaus/groovy/ast/GenericsType;

    move-result-object v1

    invoke-direct {p0, v1}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolveGenericsHeader([Lorg/codehaus/groovy/ast/GenericsType;)V

    .line 1424
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getModule()Lorg/codehaus/groovy/ast/ModuleNode;

    move-result-object v1

    .line 1425
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/ModuleNode;->hasImportsResolved()Z

    move-result v2

    const/4 v3, 0x0

    const/4 v4, 0x1

    if-nez v2, :cond_c

    .line 1426
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/ModuleNode;->getImports()Ljava/util/List;

    move-result-object v2

    invoke-interface {v2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :goto_1
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v5

    const-string v6, "unable to resolve class "

    const/4 v7, 0x0

    if-eqz v5, :cond_4

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lorg/codehaus/groovy/ast/ImportNode;

    .line 1427
    iput-object v5, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->currImportNode:Lorg/codehaus/groovy/ast/ImportNode;

    .line 1428
    invoke-virtual {v5}, Lorg/codehaus/groovy/ast/ImportNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v5

    .line 1429
    invoke-virtual {p0, v5, v3, v3, v4}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolve(Lorg/codehaus/groovy/ast/ClassNode;ZZZ)Z

    move-result v8

    if-eqz v8, :cond_3

    .line 1430
    iput-object v7, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->currImportNode:Lorg/codehaus/groovy/ast/ImportNode;

    goto :goto_1

    .line 1433
    :cond_3
    iput-object v7, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->currImportNode:Lorg/codehaus/groovy/ast/ImportNode;

    .line 1434
    new-instance v7, Ljava/lang/StringBuilder;

    invoke-direct {v7}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v7, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v6

    invoke-virtual {v5}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v7

    invoke-virtual {v6, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v6

    invoke-virtual {v6}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {p0, v6, v5}, Lorg/codehaus/groovy/control/ResolveVisitor;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    goto :goto_1

    .line 1436
    :cond_4
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/ModuleNode;->getStarImports()Ljava/util/List;

    move-result-object v2

    invoke-interface {v2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :cond_5
    :goto_2
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v5

    if-eqz v5, :cond_7

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lorg/codehaus/groovy/ast/ImportNode;

    .line 1437
    invoke-virtual {v5}, Lorg/codehaus/groovy/ast/ImportNode;->getLineNumber()I

    move-result v8

    if-lez v8, :cond_5

    .line 1438
    iput-object v5, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->currImportNode:Lorg/codehaus/groovy/ast/ImportNode;

    .line 1439
    invoke-virtual {v5}, Lorg/codehaus/groovy/ast/ImportNode;->getPackageName()Ljava/lang/String;

    move-result-object v8

    .line 1440
    invoke-virtual {v8}, Ljava/lang/String;->length()I

    move-result v9

    sub-int/2addr v9, v4

    invoke-virtual {v8, v3, v9}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object v8

    .line 1441
    invoke-static {v8}, Lorg/codehaus/groovy/ast/ClassHelper;->makeWithoutCaching(Ljava/lang/String;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v8

    .line 1442
    invoke-virtual {p0, v8, v3, v3, v4}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolve(Lorg/codehaus/groovy/ast/ClassNode;ZZZ)Z

    move-result v9

    if-eqz v9, :cond_6

    .line 1443
    invoke-virtual {v5, v8}, Lorg/codehaus/groovy/ast/ImportNode;->setType(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 1445
    :cond_6
    iput-object v7, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->currImportNode:Lorg/codehaus/groovy/ast/ImportNode;

    goto :goto_2

    .line 1448
    :cond_7
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/ModuleNode;->getStaticImports()Ljava/util/Map;

    move-result-object v2

    invoke-interface {v2}, Ljava/util/Map;->values()Ljava/util/Collection;

    move-result-object v2

    invoke-interface {v2}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :cond_8
    :goto_3
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v5

    if-eqz v5, :cond_9

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lorg/codehaus/groovy/ast/ImportNode;

    .line 1449
    invoke-virtual {v5}, Lorg/codehaus/groovy/ast/ImportNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v5

    .line 1450
    invoke-virtual {p0, v5, v4, v4, v4}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolve(Lorg/codehaus/groovy/ast/ClassNode;ZZZ)Z

    move-result v7

    if-nez v7, :cond_8

    .line 1451
    new-instance v7, Ljava/lang/StringBuilder;

    invoke-direct {v7}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v7, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v7

    invoke-virtual {v5}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v8

    invoke-virtual {v7, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v7

    invoke-virtual {v7}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v7

    invoke-virtual {p0, v7, v5}, Lorg/codehaus/groovy/control/ResolveVisitor;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    goto :goto_3

    .line 1453
    :cond_9
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/ModuleNode;->getStaticStarImports()Ljava/util/Map;

    move-result-object v2

    invoke-interface {v2}, Ljava/util/Map;->values()Ljava/util/Collection;

    move-result-object v2

    invoke-interface {v2}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :cond_a
    :goto_4
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v5

    if-eqz v5, :cond_b

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lorg/codehaus/groovy/ast/ImportNode;

    .line 1454
    invoke-virtual {v5}, Lorg/codehaus/groovy/ast/ImportNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v5

    .line 1455
    invoke-virtual {p0, v5, v4, v4, v4}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolve(Lorg/codehaus/groovy/ast/ClassNode;ZZZ)Z

    move-result v7

    if-nez v7, :cond_a

    .line 1456
    new-instance v7, Ljava/lang/StringBuilder;

    invoke-direct {v7}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v7, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v7

    invoke-virtual {v5}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v8

    invoke-virtual {v7, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v7

    invoke-virtual {v7}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v7

    invoke-virtual {p0, v7, v5}, Lorg/codehaus/groovy/control/ResolveVisitor;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    goto :goto_4

    .line 1458
    :cond_b
    invoke-virtual {v1, v4}, Lorg/codehaus/groovy/ast/ModuleNode;->setImportsResolved(Z)V

    .line 1461
    :cond_c
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getUnresolvedSuperClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    const-string v2, ""

    if-eqz v1, :cond_d

    .line 1463
    invoke-direct {p0, v1, v2, p1, v4}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolveOrFail(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;Z)V

    .line 1465
    :cond_d
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getInterfaces()[Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v5

    array-length v6, v5

    move v7, v3

    :goto_5
    if-ge v7, v6, :cond_e

    aget-object v8, v5, v7

    .line 1466
    invoke-direct {p0, v8, v2, p1, v4}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolveOrFail(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;Z)V

    add-int/lit8 v7, v7, 0x1

    goto :goto_5

    :cond_e
    if-eqz v1, :cond_f

    .line 1469
    invoke-direct {p0, p1, v1}, Lorg/codehaus/groovy/control/ResolveVisitor;->checkCyclicInheritance(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 1470
    :cond_f
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getInterfaces()[Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    array-length v2, v1

    move v4, v3

    :goto_6
    if-ge v4, v2, :cond_10

    aget-object v5, v1, v4

    .line 1471
    invoke-direct {p0, p1, v5}, Lorg/codehaus/groovy/control/ResolveVisitor;->checkCyclicInheritance(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;)V

    add-int/lit8 v4, v4, 0x1

    goto :goto_6

    .line 1473
    :cond_10
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getGenericsTypes()[Lorg/codehaus/groovy/ast/GenericsType;

    move-result-object v1

    if-eqz v1, :cond_13

    .line 1474
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getGenericsTypes()[Lorg/codehaus/groovy/ast/GenericsType;

    move-result-object v1

    array-length v2, v1

    move v4, v3

    :goto_7
    if-ge v4, v2, :cond_13

    aget-object v5, v1, v4

    if-eqz v5, :cond_12

    .line 1475
    invoke-virtual {v5}, Lorg/codehaus/groovy/ast/GenericsType;->getUpperBounds()[Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v6

    if-eqz v6, :cond_12

    .line 1476
    invoke-virtual {v5}, Lorg/codehaus/groovy/ast/GenericsType;->getUpperBounds()[Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v6

    array-length v7, v6

    move v8, v3

    :goto_8
    if-ge v8, v7, :cond_12

    aget-object v9, v6, v8

    .line 1477
    invoke-virtual {v9}, Lorg/codehaus/groovy/ast/ClassNode;->isGenericsPlaceHolder()Z

    move-result v10

    if-eqz v10, :cond_11

    invoke-virtual {v5}, Lorg/codehaus/groovy/ast/GenericsType;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v10

    invoke-direct {p0, v9, v10}, Lorg/codehaus/groovy/control/ResolveVisitor;->checkCyclicInheritance(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;)V

    :cond_11
    add-int/lit8 v8, v8, 0x1

    goto :goto_8

    :cond_12
    add-int/lit8 v4, v4, 0x1

    goto :goto_7

    .line 1483
    :cond_13
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeExpressionTransformer;->visitClass(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 1485
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolveOuterNestedClassFurther(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 1487
    iput-object v0, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    return-void
.end method

.method protected visitConstructorOrMethod(Lorg/codehaus/groovy/ast/MethodNode;Z)V
    .locals 9

    .line 272
    iget-object v0, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->currentScope:Lorg/codehaus/groovy/ast/VariableScope;

    .line 273
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getVariableScope()Lorg/codehaus/groovy/ast/VariableScope;

    move-result-object v1

    iput-object v1, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->currentScope:Lorg/codehaus/groovy/ast/VariableScope;

    .line 274
    iget-object v1, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->genericParameterNames:Ljava/util/Map;

    .line 275
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->isStatic()Z

    move-result v2

    if-eqz v2, :cond_0

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getDeclaringClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v2

    invoke-static {v2}, Lorg/codehaus/groovy/transform/trait/Traits;->isTrait(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v2

    if-nez v2, :cond_0

    .line 276
    new-instance v2, Ljava/util/HashMap;

    invoke-direct {v2}, Ljava/util/HashMap;-><init>()V

    goto :goto_0

    :cond_0
    new-instance v2, Ljava/util/HashMap;

    iget-object v3, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->genericParameterNames:Ljava/util/Map;

    invoke-direct {v2, v3}, Ljava/util/HashMap;-><init>(Ljava/util/Map;)V

    :goto_0
    iput-object v2, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->genericParameterNames:Ljava/util/Map;

    .line 278
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getGenericsTypes()[Lorg/codehaus/groovy/ast/GenericsType;

    move-result-object v2

    invoke-direct {p0, v2}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolveGenericsHeader([Lorg/codehaus/groovy/ast/GenericsType;)V

    .line 280
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v2

    .line 281
    array-length v3, v2

    const/4 v4, 0x0

    move v5, v4

    :goto_1
    if-ge v5, v3, :cond_1

    aget-object v6, v2, v5

    .line 282
    invoke-virtual {v6}, Lorg/codehaus/groovy/ast/Parameter;->getInitialExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v7

    invoke-virtual {p0, v7}, Lorg/codehaus/groovy/control/ResolveVisitor;->transform(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v7

    invoke-virtual {v6, v7}, Lorg/codehaus/groovy/ast/Parameter;->setInitialExpression(Lorg/codehaus/groovy/ast/expr/Expression;)V

    .line 283
    invoke-virtual {v6}, Lorg/codehaus/groovy/ast/Parameter;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v7

    invoke-virtual {v6}, Lorg/codehaus/groovy/ast/Parameter;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v8

    invoke-direct {p0, v7, v8}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolveOrFail(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 284
    invoke-virtual {p0, v6}, Lorg/codehaus/groovy/control/ResolveVisitor;->visitAnnotations(Lorg/codehaus/groovy/ast/AnnotatedNode;)V

    add-int/lit8 v5, v5, 0x1

    goto :goto_1

    .line 286
    :cond_1
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getExceptions()[Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v2

    .line 287
    array-length v3, v2

    :goto_2
    if-ge v4, v3, :cond_2

    aget-object v5, v2, v4

    .line 288
    invoke-direct {p0, v5, p1}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolveOrFail(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ASTNode;)V

    add-int/lit8 v4, v4, 0x1

    goto :goto_2

    .line 290
    :cond_2
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getReturnType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v2

    invoke-direct {p0, v2, p1}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolveOrFail(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 292
    iget-object v2, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->currentMethod:Lorg/codehaus/groovy/ast/MethodNode;

    .line 293
    iput-object p1, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->currentMethod:Lorg/codehaus/groovy/ast/MethodNode;

    .line 294
    invoke-super {p0, p1, p2}, Lorg/codehaus/groovy/ast/ClassCodeExpressionTransformer;->visitConstructorOrMethod(Lorg/codehaus/groovy/ast/MethodNode;Z)V

    .line 296
    iput-object v2, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->currentMethod:Lorg/codehaus/groovy/ast/MethodNode;

    .line 297
    iput-object v1, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->genericParameterNames:Ljava/util/Map;

    .line 298
    iput-object v0, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->currentScope:Lorg/codehaus/groovy/ast/VariableScope;

    return-void
.end method

.method public visitField(Lorg/codehaus/groovy/ast/FieldNode;)V
    .locals 2

    .line 303
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/FieldNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    .line 304
    iget-object v1, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->fieldTypesChecked:Ljava/util/Set;

    invoke-interface {v1, p1}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_0

    .line 305
    invoke-direct {p0, v0, p1}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolveOrFail(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 307
    :cond_0
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeExpressionTransformer;->visitField(Lorg/codehaus/groovy/ast/FieldNode;)V

    return-void
.end method

.method public visitForLoop(Lorg/codehaus/groovy/ast/stmt/ForStatement;)V
    .locals 1

    .line 1558
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/stmt/ForStatement;->getVariableType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    invoke-direct {p0, v0, p1}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolveOrFail(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 1559
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeExpressionTransformer;->visitForLoop(Lorg/codehaus/groovy/ast/stmt/ForStatement;)V

    return-void
.end method

.method public visitProperty(Lorg/codehaus/groovy/ast/PropertyNode;)V
    .locals 2

    .line 312
    iget-object v0, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->genericParameterNames:Ljava/util/Map;

    .line 313
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/PropertyNode;->isStatic()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/PropertyNode;->getDeclaringClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    invoke-static {v1}, Lorg/codehaus/groovy/transform/trait/Traits;->isTrait(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v1

    if-nez v1, :cond_0

    .line 314
    new-instance v1, Ljava/util/HashMap;

    invoke-direct {v1}, Ljava/util/HashMap;-><init>()V

    iput-object v1, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->genericParameterNames:Ljava/util/Map;

    .line 317
    :cond_0
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/PropertyNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    .line 318
    invoke-direct {p0, v1, p1}, Lorg/codehaus/groovy/control/ResolveVisitor;->resolveOrFail(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 319
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeExpressionTransformer;->visitProperty(Lorg/codehaus/groovy/ast/PropertyNode;)V

    .line 320
    iget-object v1, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->fieldTypesChecked:Ljava/util/Set;

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/PropertyNode;->getField()Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object p1

    invoke-interface {v1, p1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 322
    iput-object v0, p0, Lorg/codehaus/groovy/control/ResolveVisitor;->genericParameterNames:Ljava/util/Map;

    return-void
.end method
