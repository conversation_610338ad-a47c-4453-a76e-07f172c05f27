.class public interface abstract Lorg/codehaus/groovy/ast/NodeMetaDataHandler;
.super Ljava/lang/Object;
.source "NodeMetaDataHandler.java"


# virtual methods
.method public copyNodeMetaData(Lorg/codehaus/groovy/ast/NodeMetaDataHandler;)V
    .locals 1

    .line 71
    invoke-interface {p1}, Lorg/codehaus/groovy/ast/NodeMetaDataHandler;->getMetaDataMap()Ljava/util/Map;

    move-result-object p1

    if-nez p1, :cond_0

    return-void

    .line 75
    :cond_0
    invoke-interface {p0}, Lorg/codehaus/groovy/ast/NodeMetaDataHandler;->getMetaDataMap()Ljava/util/Map;

    move-result-object v0

    if-nez v0, :cond_1

    .line 77
    new-instance v0, Lorg/codehaus/groovy/util/ListHashMap;

    invoke-direct {v0}, Lorg/codehaus/groovy/util/ListHashMap;-><init>()V

    .line 78
    invoke-interface {p0, v0}, Lorg/codehaus/groovy/ast/NodeMetaDataHandler;->setMetaDataMap(Ljava/util/Map;)V

    .line 81
    :cond_1
    invoke-interface {v0, p1}, Ljava/util/Map;->putAll(Ljava/util/Map;)V

    return-void
.end method

.method public abstract getMetaDataMap()Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "**>;"
        }
    .end annotation
.end method

.method public getNodeMetaData(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Ljava/lang/Object;",
            ")TT;"
        }
    .end annotation

    .line 40
    invoke-interface {p0}, Lorg/codehaus/groovy/ast/NodeMetaDataHandler;->getMetaDataMap()Ljava/util/Map;

    move-result-object v0

    if-nez v0, :cond_0

    const/4 p1, 0x0

    return-object p1

    .line 44
    :cond_0
    invoke-interface {v0, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public getNodeMetaData(Ljava/lang/Object;Ljava/util/function/Function;)Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Ljava/lang/Object;",
            "Ljava/util/function/Function<",
            "*+TT;>;)TT;"
        }
    .end annotation

    if-eqz p1, :cond_1

    .line 57
    invoke-interface {p0}, Lorg/codehaus/groovy/ast/NodeMetaDataHandler;->getMetaDataMap()Ljava/util/Map;

    move-result-object v0

    if-nez v0, :cond_0

    .line 59
    new-instance v0, Lorg/codehaus/groovy/util/ListHashMap;

    invoke-direct {v0}, Lorg/codehaus/groovy/util/ListHashMap;-><init>()V

    .line 60
    invoke-interface {p0, v0}, Lorg/codehaus/groovy/ast/NodeMetaDataHandler;->setMetaDataMap(Ljava/util/Map;)V

    .line 62
    :cond_0
    invoke-interface {v0, p1, p2}, Ljava/util/Map;->computeIfAbsent(Ljava/lang/Object;Ljava/util/function/Function;)Ljava/lang/Object;

    move-result-object p1

    return-object p1

    .line 55
    :cond_1
    new-instance p1, Lorg/codehaus/groovy/GroovyBugError;

    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "Tried to get/set meta data with null key on "

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string v0, "."

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-direct {p1, p2}, Lorg/codehaus/groovy/GroovyBugError;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public getNodeMetaData()Ljava/util/Map;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "**>;"
        }
    .end annotation

    .line 138
    invoke-interface {p0}, Lorg/codehaus/groovy/ast/NodeMetaDataHandler;->getMetaDataMap()Ljava/util/Map;

    move-result-object v0

    if-nez v0, :cond_0

    .line 140
    invoke-static {}, Ljava/util/Collections;->emptyMap()Ljava/util/Map;

    move-result-object v0

    return-object v0

    .line 142
    :cond_0
    invoke-static {v0}, Ljava/util/Collections;->unmodifiableMap(Ljava/util/Map;)Ljava/util/Map;

    move-result-object v0

    return-object v0
.end method

.method public putNodeMetaData(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    if-eqz p1, :cond_1

    .line 108
    invoke-interface {p0}, Lorg/codehaus/groovy/ast/NodeMetaDataHandler;->getMetaDataMap()Ljava/util/Map;

    move-result-object v0

    if-nez v0, :cond_0

    .line 110
    new-instance v0, Lorg/codehaus/groovy/util/ListHashMap;

    invoke-direct {v0}, Lorg/codehaus/groovy/util/ListHashMap;-><init>()V

    .line 111
    invoke-interface {p0, v0}, Lorg/codehaus/groovy/ast/NodeMetaDataHandler;->setMetaDataMap(Ljava/util/Map;)V

    .line 113
    :cond_0
    invoke-interface {v0, p1, p2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1

    .line 106
    :cond_1
    new-instance p1, Lorg/codehaus/groovy/GroovyBugError;

    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "Tried to set meta data with null key on "

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string v0, "."

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-direct {p1, p2}, Lorg/codehaus/groovy/GroovyBugError;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public removeNodeMetaData(Ljava/lang/Object;)V
    .locals 2

    if-eqz p1, :cond_1

    .line 125
    invoke-interface {p0}, Lorg/codehaus/groovy/ast/NodeMetaDataHandler;->getMetaDataMap()Ljava/util/Map;

    move-result-object v0

    if-nez v0, :cond_0

    return-void

    .line 129
    :cond_0
    invoke-interface {v0, p1}, Ljava/util/Map;->remove(Ljava/lang/Object;)Ljava/lang/Object;

    return-void

    .line 123
    :cond_1
    new-instance p1, Lorg/codehaus/groovy/GroovyBugError;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Tried to remove meta data with null key "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "."

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-direct {p1, v0}, Lorg/codehaus/groovy/GroovyBugError;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public abstract setMetaDataMap(Ljava/util/Map;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "**>;)V"
        }
    .end annotation
.end method

.method public setNodeMetaData(Ljava/lang/Object;Ljava/lang/Object;)V
    .locals 1

    .line 93
    invoke-interface {p0, p1, p2}, Lorg/codehaus/groovy/ast/NodeMetaDataHandler;->putNodeMetaData(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    if-nez p1, :cond_0

    return-void

    .line 94
    :cond_0
    new-instance p1, Lorg/codehaus/groovy/GroovyBugError;

    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "Tried to overwrite existing meta data "

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string v0, "."

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-direct {p1, p2}, Lorg/codehaus/groovy/GroovyBugError;-><init>(Ljava/lang/String;)V

    throw p1
.end method
