.class public Lorg/codehaus/groovy/GroovyException;
.super Ljava/lang/Exception;
.source "GroovyException.java"

# interfaces
.implements Lorg/codehaus/groovy/GroovyExceptionInterface;


# static fields
.field private static final serialVersionUID:J = -0xd9c6c8e7453028L


# instance fields
.field private fatal:Z


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 25
    invoke-direct {p0}, Ljava/lang/Exception;-><init>()V

    const/4 v0, 0x1

    .line 23
    iput-boolean v0, p0, Lorg/codehaus/groovy/GroovyException;->fatal:Z

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;)V
    .locals 0

    .line 29
    invoke-direct {p0, p1}, Ljava/lang/Exception;-><init>(Ljava/lang/String;)V

    const/4 p1, 0x1

    .line 23
    iput-boolean p1, p0, Lorg/codehaus/groovy/GroovyException;->fatal:Z

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;Ljava/lang/Throwable;)V
    .locals 0

    .line 33
    invoke-direct {p0, p1, p2}, Ljava/lang/Exception;-><init>(Ljava/lang/String;Ljava/lang/Throwable;)V

    const/4 p1, 0x1

    .line 23
    iput-boolean p1, p0, Lorg/codehaus/groovy/GroovyException;->fatal:Z

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;Z)V
    .locals 0

    .line 42
    invoke-direct {p0, p1}, Ljava/lang/Exception;-><init>(Ljava/lang/String;)V

    const/4 p1, 0x1

    .line 23
    iput-boolean p1, p0, Lorg/codehaus/groovy/GroovyException;->fatal:Z

    .line 43
    iput-boolean p2, p0, Lorg/codehaus/groovy/GroovyException;->fatal:Z

    return-void
.end method

.method public constructor <init>(Z)V
    .locals 1

    .line 37
    invoke-direct {p0}, Ljava/lang/Exception;-><init>()V

    const/4 v0, 0x1

    .line 23
    iput-boolean v0, p0, Lorg/codehaus/groovy/GroovyException;->fatal:Z

    .line 38
    iput-boolean p1, p0, Lorg/codehaus/groovy/GroovyException;->fatal:Z

    return-void
.end method


# virtual methods
.method public isFatal()Z
    .locals 1

    .line 47
    iget-boolean v0, p0, Lorg/codehaus/groovy/GroovyException;->fatal:Z

    return v0
.end method

.method public setFatal(Z)V
    .locals 0

    .line 51
    iput-boolean p1, p0, Lorg/codehaus/groovy/GroovyException;->fatal:Z

    return-void
.end method
