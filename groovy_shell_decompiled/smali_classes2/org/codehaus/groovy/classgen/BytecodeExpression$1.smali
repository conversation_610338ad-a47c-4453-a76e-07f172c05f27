.class Lorg/codehaus/groovy/classgen/BytecodeExpression$1;
.super Lorg/codehaus/groovy/classgen/BytecodeExpression;
.source "BytecodeExpression.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/codehaus/groovy/classgen/BytecodeExpression;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# direct methods
.method constructor <init>()V
    .locals 0

    .line 32
    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/BytecodeExpression;-><init>()V

    return-void
.end method


# virtual methods
.method public visit(Lgroovyjarjarasm/asm/MethodVisitor;)V
    .locals 0

    return-void
.end method
