.class Lorg/codehaus/groovy/classgen/Verifier$14;
.super Lorg/codehaus/groovy/classgen/BytecodeInstruction;
.source "Verifier.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/codehaus/groovy/classgen/Verifier;->getCovariantImplementation(Lorg/codehaus/groovy/ast/MethodNode;Lorg/codehaus/groovy/ast/MethodNode;Ljava/util/Map;Z)Lorg/codehaus/groovy/ast/MethodNode;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lorg/codehaus/groovy/classgen/Verifier;

.field final synthetic val$nmr:Lorg/codehaus/groovy/ast/ClassNode;

.field final synthetic val$oldMethod:Lorg/codehaus/groovy/ast/MethodNode;

.field final synthetic val$overridingMethod:Lorg/codehaus/groovy/ast/MethodNode;

.field final synthetic val$owner:Ljava/lang/String;


# direct methods
.method constructor <init>(Lorg/codehaus/groovy/classgen/Verifier;Lorg/codehaus/groovy/ast/MethodNode;Lorg/codehaus/groovy/ast/MethodNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 0

    .line 1410
    iput-object p1, p0, Lorg/codehaus/groovy/classgen/Verifier$14;->this$0:Lorg/codehaus/groovy/classgen/Verifier;

    iput-object p2, p0, Lorg/codehaus/groovy/classgen/Verifier$14;->val$oldMethod:Lorg/codehaus/groovy/ast/MethodNode;

    iput-object p3, p0, Lorg/codehaus/groovy/classgen/Verifier$14;->val$overridingMethod:Lorg/codehaus/groovy/ast/MethodNode;

    iput-object p4, p0, Lorg/codehaus/groovy/classgen/Verifier$14;->val$owner:Ljava/lang/String;

    iput-object p5, p0, Lorg/codehaus/groovy/classgen/Verifier$14;->val$nmr:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/BytecodeInstruction;-><init>()V

    return-void
.end method


# virtual methods
.method public visit(Lgroovyjarjarasm/asm/MethodVisitor;)V
    .locals 13

    const/16 v0, 0x19

    const/4 v1, 0x0

    .line 1413
    invoke-virtual {p1, v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitVarInsn(II)V

    .line 1414
    iget-object v0, p0, Lorg/codehaus/groovy/classgen/Verifier$14;->val$oldMethod:Lorg/codehaus/groovy/ast/MethodNode;

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/MethodNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v0

    .line 1415
    iget-object v2, p0, Lorg/codehaus/groovy/classgen/Verifier$14;->val$overridingMethod:Lorg/codehaus/groovy/ast/MethodNode;

    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/MethodNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v2

    .line 1417
    array-length v3, v0

    move v4, v1

    :goto_0
    if-ge v1, v3, :cond_3

    .line 1418
    aget-object v5, v0, v1

    invoke-virtual {v5}, Lorg/codehaus/groovy/ast/Parameter;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v5

    add-int/lit8 v6, v1, 0x1

    add-int v7, v6, v4

    .line 1419
    invoke-static {p1, v5, v7}, Lorg/codehaus/groovy/classgen/asm/BytecodeHelper;->load(Lgroovyjarjarasm/asm/MethodVisitor;Lorg/codehaus/groovy/ast/ClassNode;I)V

    .line 1420
    invoke-virtual {v5}, Lorg/codehaus/groovy/ast/ClassNode;->redirect()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v7

    sget-object v8, Lorg/codehaus/groovy/ast/ClassHelper;->double_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    if-eq v7, v8, :cond_0

    .line 1421
    invoke-virtual {v5}, Lorg/codehaus/groovy/ast/ClassNode;->redirect()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v7

    sget-object v8, Lorg/codehaus/groovy/ast/ClassHelper;->long_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    if-ne v7, v8, :cond_1

    :cond_0
    add-int/lit8 v4, v4, 0x1

    .line 1424
    :cond_1
    aget-object v7, v2, v1

    invoke-virtual {v7}, Lorg/codehaus/groovy/ast/Parameter;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v7

    invoke-virtual {v5, v7}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result v5

    if-nez v5, :cond_2

    .line 1425
    aget-object v1, v2, v1

    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/Parameter;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    invoke-static {p1, v1}, Lorg/codehaus/groovy/classgen/asm/BytecodeHelper;->doCast(Lgroovyjarjarasm/asm/MethodVisitor;Lorg/codehaus/groovy/ast/ClassNode;)V

    :cond_2
    move v1, v6

    goto :goto_0

    :cond_3
    const/16 v8, 0xb6

    .line 1428
    iget-object v9, p0, Lorg/codehaus/groovy/classgen/Verifier$14;->val$owner:Ljava/lang/String;

    iget-object v0, p0, Lorg/codehaus/groovy/classgen/Verifier$14;->val$overridingMethod:Lorg/codehaus/groovy/ast/MethodNode;

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/MethodNode;->getName()Ljava/lang/String;

    move-result-object v10

    iget-object v0, p0, Lorg/codehaus/groovy/classgen/Verifier$14;->val$nmr:Lorg/codehaus/groovy/ast/ClassNode;

    iget-object v1, p0, Lorg/codehaus/groovy/classgen/Verifier$14;->val$overridingMethod:Lorg/codehaus/groovy/ast/MethodNode;

    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/MethodNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v1

    invoke-static {v0, v1}, Lorg/codehaus/groovy/classgen/asm/BytecodeHelper;->getMethodDescriptor(Lorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/Parameter;)Ljava/lang/String;

    move-result-object v11

    const/4 v12, 0x0

    move-object v7, p1

    invoke-virtual/range {v7 .. v12}, Lgroovyjarjarasm/asm/MethodVisitor;->visitMethodInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V

    .line 1430
    iget-object v0, p0, Lorg/codehaus/groovy/classgen/Verifier$14;->val$oldMethod:Lorg/codehaus/groovy/ast/MethodNode;

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/MethodNode;->getReturnType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    invoke-static {p1, v0}, Lorg/codehaus/groovy/classgen/asm/BytecodeHelper;->doReturn(Lgroovyjarjarasm/asm/MethodVisitor;Lorg/codehaus/groovy/ast/ClassNode;)V

    return-void
.end method
