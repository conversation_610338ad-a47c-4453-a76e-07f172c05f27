.class Lorg/codehaus/groovy/classgen/Verifier$1;
.super Ljava/lang/Object;
.source "Verifier.java"

# interfaces
.implements Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableNotFinalCallback;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/codehaus/groovy/classgen/Verifier;->getFinalVariablesCallback()Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableNotFinalCallback;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lorg/codehaus/groovy/classgen/Verifier;


# direct methods
.method constructor <init>(Lorg/codehaus/groovy/classgen/Verifier;)V
    .locals 0

    .line 252
    iput-object p1, p0, Lorg/codehaus/groovy/classgen/Verifier$1;->this$0:Lorg/codehaus/groovy/classgen/Verifier;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public variableNotAlwaysInitialized(Lorg/codehaus/groovy/ast/expr/VariableExpression;)V
    .locals 3

    .line 268
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->getAccessedVariable()Lorg/codehaus/groovy/ast/Variable;

    move-result-object v0

    invoke-interface {v0}, Lorg/codehaus/groovy/ast/Variable;->getModifiers()I

    move-result v0

    invoke-static {v0}, Ljava/lang/reflect/Modifier;->isFinal(I)Z

    move-result v0

    if-nez v0, :cond_0

    return-void

    .line 269
    :cond_0
    new-instance v0, Lorg/codehaus/groovy/syntax/RuntimeParserException;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "The variable ["

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->getName()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v2, "] may be uninitialized"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-direct {v0, v1, p1}, Lorg/codehaus/groovy/syntax/RuntimeParserException;-><init>(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    throw v0
.end method

.method public variableNotFinal(Lorg/codehaus/groovy/ast/Variable;Lorg/codehaus/groovy/ast/expr/Expression;)V
    .locals 4

    .line 255
    instance-of v0, p1, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    if-eqz v0, :cond_0

    .line 256
    check-cast p1, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->getAccessedVariable()Lorg/codehaus/groovy/ast/Variable;

    move-result-object p1

    .line 258
    :cond_0
    instance-of v0, p1, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    const-string v1, "] is declared final but is reassigned"

    if-eqz v0, :cond_2

    invoke-interface {p1}, Lorg/codehaus/groovy/ast/Variable;->getModifiers()I

    move-result v0

    invoke-static {v0}, Ljava/lang/reflect/Modifier;->isFinal(I)Z

    move-result v0

    if-nez v0, :cond_1

    goto :goto_0

    .line 259
    :cond_1
    new-instance v0, Lorg/codehaus/groovy/syntax/RuntimeParserException;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "The variable ["

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-interface {p1}, Lorg/codehaus/groovy/ast/Variable;->getName()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {v0, p1, p2}, Lorg/codehaus/groovy/syntax/RuntimeParserException;-><init>(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    throw v0

    .line 261
    :cond_2
    :goto_0
    instance-of v0, p1, Lorg/codehaus/groovy/ast/Parameter;

    if-eqz v0, :cond_4

    invoke-interface {p1}, Lorg/codehaus/groovy/ast/Variable;->getModifiers()I

    move-result v0

    invoke-static {v0}, Ljava/lang/reflect/Modifier;->isFinal(I)Z

    move-result v0

    if-nez v0, :cond_3

    goto :goto_1

    .line 262
    :cond_3
    new-instance v0, Lorg/codehaus/groovy/syntax/RuntimeParserException;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "The parameter ["

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-interface {p1}, Lorg/codehaus/groovy/ast/Variable;->getName()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {v0, p1, p2}, Lorg/codehaus/groovy/syntax/RuntimeParserException;-><init>(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    throw v0

    :cond_4
    :goto_1
    return-void
.end method
