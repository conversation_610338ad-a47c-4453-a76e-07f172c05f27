.class public Lorg/codehaus/groovy/classgen/DummyClassGenerator;
.super Lorg/codehaus/groovy/classgen/ClassGenerator;
.source "DummyClassGenerator.java"


# instance fields
.field private classNode:Lorg/codehaus/groovy/ast/ClassNode;

.field private final context:Lorg/codehaus/groovy/classgen/GeneratorContext;

.field private final cv:Lgroovyjarjarasm/asm/ClassVisitor;

.field private internalBaseClassName:Ljava/lang/String;

.field private internalClassName:Ljava/lang/String;

.field private mv:Lgroovyjarjarasm/asm/MethodVisitor;


# direct methods
.method public constructor <init>(Lorg/codehaus/groovy/classgen/GeneratorContext;Lgroovyjarjarasm/asm/ClassVisitor;Ljava/lang/ClassLoader;Ljava/lang/String;)V
    .locals 0

    .line 58
    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/ClassGenerator;-><init>()V

    .line 59
    iput-object p1, p0, Lorg/codehaus/groovy/classgen/DummyClassGenerator;->context:Lorg/codehaus/groovy/classgen/GeneratorContext;

    .line 60
    iput-object p2, p0, Lorg/codehaus/groovy/classgen/DummyClassGenerator;->cv:Lgroovyjarjarasm/asm/ClassVisitor;

    return-void
.end method


# virtual methods
.method protected getCompileUnit()Lorg/codehaus/groovy/ast/CompileUnit;
    .locals 1

    .line 155
    iget-object v0, p0, Lorg/codehaus/groovy/classgen/DummyClassGenerator;->classNode:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->getCompileUnit()Lorg/codehaus/groovy/ast/CompileUnit;

    move-result-object v0

    if-nez v0, :cond_0

    .line 157
    iget-object v0, p0, Lorg/codehaus/groovy/classgen/DummyClassGenerator;->context:Lorg/codehaus/groovy/classgen/GeneratorContext;

    invoke-virtual {v0}, Lorg/codehaus/groovy/classgen/GeneratorContext;->getCompileUnit()Lorg/codehaus/groovy/ast/CompileUnit;

    move-result-object v0

    :cond_0
    return-object v0
.end method

.method public visitAnnotations(Lorg/codehaus/groovy/ast/AnnotatedNode;)V
    .locals 0

    return-void
.end method

.method public visitClass(Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 8

    .line 67
    :try_start_0
    iput-object p1, p0, Lorg/codehaus/groovy/classgen/DummyClassGenerator;->classNode:Lorg/codehaus/groovy/ast/ClassNode;

    .line 68
    invoke-static {p1}, Lorg/codehaus/groovy/classgen/asm/BytecodeHelper;->getClassInternalName(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/classgen/DummyClassGenerator;->internalClassName:Ljava/lang/String;

    .line 72
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getSuperClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/classgen/asm/BytecodeHelper;->getClassInternalName(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/classgen/DummyClassGenerator;->internalBaseClassName:Ljava/lang/String;

    .line 74
    iget-object v1, p0, Lorg/codehaus/groovy/classgen/DummyClassGenerator;->cv:Lgroovyjarjarasm/asm/ClassVisitor;

    const/16 v2, 0x2f

    .line 76
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getModifiers()I

    move-result v3

    iget-object v4, p0, Lorg/codehaus/groovy/classgen/DummyClassGenerator;->internalClassName:Ljava/lang/String;

    const/4 v5, 0x0

    iget-object v6, p0, Lorg/codehaus/groovy/classgen/DummyClassGenerator;->internalBaseClassName:Ljava/lang/String;

    .line 80
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getInterfaces()[Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/classgen/asm/BytecodeHelper;->getClassInternalNames([Lorg/codehaus/groovy/ast/ClassNode;)[Ljava/lang/String;

    move-result-object v7

    .line 74
    invoke-virtual/range {v1 .. v7}, Lgroovyjarjarasm/asm/ClassVisitor;->visit(IILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)V

    .line 83
    invoke-virtual {p1, p0}, Lorg/codehaus/groovy/ast/ClassNode;->visitContents(Lorg/codehaus/groovy/ast/GroovyClassVisitor;)V

    .line 85
    iget-object v0, p0, Lorg/codehaus/groovy/classgen/DummyClassGenerator;->innerClasses:Ljava/util/LinkedList;

    invoke-virtual {v0}, Ljava/util/LinkedList;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/ast/ClassNode;

    .line 87
    invoke-static {v1}, Lorg/codehaus/groovy/classgen/asm/BytecodeHelper;->getClassInternalName(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/lang/String;

    move-result-object v2

    .line 88
    iget-object v3, p0, Lorg/codehaus/groovy/classgen/DummyClassGenerator;->internalClassName:Ljava/lang/String;

    .line 89
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/ClassNode;->getEnclosingMethod()Lorg/codehaus/groovy/ast/MethodNode;

    move-result-object v4

    if-eqz v4, :cond_0

    const/4 v3, 0x0

    .line 94
    :cond_0
    iget-object v4, p0, Lorg/codehaus/groovy/classgen/DummyClassGenerator;->cv:Lgroovyjarjarasm/asm/ClassVisitor;

    .line 97
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v5

    .line 98
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/ClassNode;->getModifiers()I

    move-result v1

    .line 94
    invoke-virtual {v4, v2, v3, v5, v1}, Lgroovyjarjarasm/asm/ClassVisitor;->visitInnerClass(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;I)V

    goto :goto_0

    .line 100
    :cond_1
    iget-object v0, p0, Lorg/codehaus/groovy/classgen/DummyClassGenerator;->cv:Lgroovyjarjarasm/asm/ClassVisitor;

    invoke-virtual {v0}, Lgroovyjarjarasm/asm/ClassVisitor;->visitEnd()V
    :try_end_0
    .catch Lgroovy/lang/GroovyRuntimeException; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    :catch_0
    move-exception v0

    .line 103
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getModule()Lorg/codehaus/groovy/ast/ModuleNode;

    move-result-object p1

    invoke-virtual {v0, p1}, Lgroovy/lang/GroovyRuntimeException;->setModule(Lorg/codehaus/groovy/ast/ModuleNode;)V

    .line 104
    throw v0
.end method

.method public visitConstructor(Lorg/codehaus/groovy/ast/ConstructorNode;)V
    .locals 8

    .line 110
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ConstructorNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v0

    invoke-virtual {p0, p1, v0}, Lorg/codehaus/groovy/classgen/DummyClassGenerator;->visitParameters(Lorg/codehaus/groovy/ast/ASTNode;[Lorg/codehaus/groovy/ast/Parameter;)V

    .line 112
    sget-object v0, Lorg/codehaus/groovy/ast/ClassHelper;->VOID_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ConstructorNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v1

    invoke-static {v0, v1}, Lorg/codehaus/groovy/classgen/asm/BytecodeHelper;->getMethodDescriptor(Lorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/Parameter;)Ljava/lang/String;

    move-result-object v5

    .line 113
    iget-object v2, p0, Lorg/codehaus/groovy/classgen/DummyClassGenerator;->cv:Lgroovyjarjarasm/asm/ClassVisitor;

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ConstructorNode;->getModifiers()I

    move-result v3

    const-string v4, "<init>"

    const/4 v6, 0x0

    const/4 v7, 0x0

    invoke-virtual/range {v2 .. v7}, Lgroovyjarjarasm/asm/ClassVisitor;->visitMethod(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lgroovyjarjarasm/asm/MethodVisitor;

    move-result-object p1

    iput-object p1, p0, Lorg/codehaus/groovy/classgen/DummyClassGenerator;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v0, 0xbb

    const-string v1, "java/lang/RuntimeException"

    .line 114
    invoke-virtual {p1, v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitTypeInsn(ILjava/lang/String;)V

    .line 115
    iget-object p1, p0, Lorg/codehaus/groovy/classgen/DummyClassGenerator;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v0, 0x59

    invoke-virtual {p1, v0}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    .line 116
    iget-object p1, p0, Lorg/codehaus/groovy/classgen/DummyClassGenerator;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const-string v0, "not intended for execution"

    invoke-virtual {p1, v0}, Lgroovyjarjarasm/asm/MethodVisitor;->visitLdcInsn(Ljava/lang/Object;)V

    .line 117
    iget-object v1, p0, Lorg/codehaus/groovy/classgen/DummyClassGenerator;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v2, 0xb7

    const-string v3, "java/lang/RuntimeException"

    const-string v4, "<init>"

    const-string v5, "(Ljava/lang/String;)V"

    const/4 v6, 0x0

    invoke-virtual/range {v1 .. v6}, Lgroovyjarjarasm/asm/MethodVisitor;->visitMethodInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V

    .line 118
    iget-object p1, p0, Lorg/codehaus/groovy/classgen/DummyClassGenerator;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v0, 0xbf

    invoke-virtual {p1, v0}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    .line 119
    iget-object p1, p0, Lorg/codehaus/groovy/classgen/DummyClassGenerator;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/4 v0, 0x0

    invoke-virtual {p1, v0, v0}, Lgroovyjarjarasm/asm/MethodVisitor;->visitMaxs(II)V

    return-void
.end method

.method public visitField(Lorg/codehaus/groovy/ast/FieldNode;)V
    .locals 6

    .line 140
    iget-object v0, p0, Lorg/codehaus/groovy/classgen/DummyClassGenerator;->cv:Lgroovyjarjarasm/asm/ClassVisitor;

    .line 141
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/FieldNode;->getModifiers()I

    move-result v1

    .line 142
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object v2

    .line 143
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/FieldNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/classgen/asm/BytecodeHelper;->getTypeDescription(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/lang/String;

    move-result-object v3

    const/4 v4, 0x0

    const/4 v5, 0x0

    .line 140
    invoke-virtual/range {v0 .. v5}, Lgroovyjarjarasm/asm/ClassVisitor;->visitField(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Object;)Lgroovyjarjarasm/asm/FieldVisitor;

    return-void
.end method

.method public visitMethod(Lorg/codehaus/groovy/ast/MethodNode;)V
    .locals 8

    .line 124
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v0

    invoke-virtual {p0, p1, v0}, Lorg/codehaus/groovy/classgen/DummyClassGenerator;->visitParameters(Lorg/codehaus/groovy/ast/ASTNode;[Lorg/codehaus/groovy/ast/Parameter;)V

    .line 126
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getReturnType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v1

    invoke-static {v0, v1}, Lorg/codehaus/groovy/classgen/asm/BytecodeHelper;->getMethodDescriptor(Lorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/Parameter;)Ljava/lang/String;

    move-result-object v5

    .line 127
    iget-object v2, p0, Lorg/codehaus/groovy/classgen/DummyClassGenerator;->cv:Lgroovyjarjarasm/asm/ClassVisitor;

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getModifiers()I

    move-result v3

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getName()Ljava/lang/String;

    move-result-object v4

    const/4 v6, 0x0

    const/4 v7, 0x0

    invoke-virtual/range {v2 .. v7}, Lgroovyjarjarasm/asm/ClassVisitor;->visitMethod(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lgroovyjarjarasm/asm/MethodVisitor;

    move-result-object p1

    iput-object p1, p0, Lorg/codehaus/groovy/classgen/DummyClassGenerator;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v0, 0xbb

    const-string v1, "java/lang/RuntimeException"

    .line 129
    invoke-virtual {p1, v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitTypeInsn(ILjava/lang/String;)V

    .line 130
    iget-object p1, p0, Lorg/codehaus/groovy/classgen/DummyClassGenerator;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v0, 0x59

    invoke-virtual {p1, v0}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    .line 131
    iget-object p1, p0, Lorg/codehaus/groovy/classgen/DummyClassGenerator;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const-string v0, "not intended for execution"

    invoke-virtual {p1, v0}, Lgroovyjarjarasm/asm/MethodVisitor;->visitLdcInsn(Ljava/lang/Object;)V

    .line 132
    iget-object v1, p0, Lorg/codehaus/groovy/classgen/DummyClassGenerator;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v2, 0xb7

    const-string v3, "java/lang/RuntimeException"

    const-string v4, "<init>"

    const-string v5, "(Ljava/lang/String;)V"

    const/4 v6, 0x0

    invoke-virtual/range {v1 .. v6}, Lgroovyjarjarasm/asm/MethodVisitor;->visitMethodInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V

    .line 133
    iget-object p1, p0, Lorg/codehaus/groovy/classgen/DummyClassGenerator;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v0, 0xbf

    invoke-virtual {p1, v0}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    .line 135
    iget-object p1, p0, Lorg/codehaus/groovy/classgen/DummyClassGenerator;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/4 v0, 0x0

    invoke-virtual {p1, v0, v0}, Lgroovyjarjarasm/asm/MethodVisitor;->visitMaxs(II)V

    return-void
.end method

.method protected visitParameter(Lorg/codehaus/groovy/ast/ASTNode;Lorg/codehaus/groovy/ast/Parameter;)V
    .locals 0

    return-void
.end method

.method protected visitParameters(Lorg/codehaus/groovy/ast/ASTNode;[Lorg/codehaus/groovy/ast/Parameter;)V
    .locals 3

    .line 163
    array-length v0, p2

    const/4 v1, 0x0

    :goto_0
    if-ge v1, v0, :cond_0

    aget-object v2, p2, v1

    .line 164
    invoke-virtual {p0, p1, v2}, Lorg/codehaus/groovy/classgen/DummyClassGenerator;->visitParameter(Lorg/codehaus/groovy/ast/ASTNode;Lorg/codehaus/groovy/ast/Parameter;)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    return-void
.end method

.method public visitProperty(Lorg/codehaus/groovy/ast/PropertyNode;)V
    .locals 0

    return-void
.end method
