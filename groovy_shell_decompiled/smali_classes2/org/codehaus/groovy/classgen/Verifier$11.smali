.class Lorg/codehaus/groovy/classgen/Verifier$11;
.super Lorg/codehaus/groovy/classgen/BytecodeInstruction;
.source "Verifier.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/codehaus/groovy/classgen/Verifier;->addInitialization(Lorg/codehaus/groovy/ast/ClassNode;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lorg/codehaus/groovy/classgen/Verifier;

.field final synthetic val$node:Lorg/codehaus/groovy/ast/ClassNode;


# direct methods
.method constructor <init>(Lorg/codehaus/groovy/classgen/Verifier;Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 0

    .line 984
    iput-object p1, p0, Lorg/codehaus/groovy/classgen/Verifier$11;->this$0:Lorg/codehaus/groovy/classgen/Verifier;

    iput-object p2, p0, Lorg/codehaus/groovy/classgen/Verifier$11;->val$node:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/BytecodeInstruction;-><init>()V

    return-void
.end method


# virtual methods
.method public visit(Lgroovyjarjarasm/asm/MethodVisitor;)V
    .locals 7

    .line 987
    iget-object v0, p0, Lorg/codehaus/groovy/classgen/Verifier$11;->val$node:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {v0}, Lorg/codehaus/groovy/classgen/asm/BytecodeHelper;->getClassInternalName(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/lang/String;

    move-result-object v3

    const/16 v2, 0xb8

    const-string v4, "__$swapInit"

    const-string v5, "()V"

    const/4 v6, 0x0

    move-object v1, p1

    invoke-virtual/range {v1 .. v6}, Lgroovyjarjarasm/asm/MethodVisitor;->visitMethodInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V

    return-void
.end method
