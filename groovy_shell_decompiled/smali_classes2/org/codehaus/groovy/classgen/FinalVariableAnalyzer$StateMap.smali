.class Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$StateMap;
.super Ljava/util/HashMap;
.source "FinalVariableAnalyzer.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0xa
    name = "StateMap"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/util/HashMap<",
        "Lorg/codehaus/groovy/ast/Variable;",
        "Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;",
        ">;"
    }
.end annotation


# static fields
.field private static final serialVersionUID:J = -0x519fc37b76d6d70cL


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 544
    invoke-direct {p0}, Ljava/util/HashMap;-><init>()V

    return-void
.end method

.method synthetic constructor <init>(Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$1;)V
    .locals 0

    .line 544
    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$StateMap;-><init>()V

    return-void
.end method


# virtual methods
.method public bridge synthetic get(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 544
    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$StateMap;->get(Ljava/lang/Object;)Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;

    move-result-object p1

    return-object p1
.end method

.method public get(Ljava/lang/Object;)Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;
    .locals 0

    .line 549
    check-cast p1, Lorg/codehaus/groovy/ast/Variable;

    invoke-static {p1}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->access$100(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/Variable;

    move-result-object p1

    invoke-super {p0, p1}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;

    return-object p1
.end method

.method public bridge synthetic put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 544
    check-cast p1, Lorg/codehaus/groovy/ast/Variable;

    check-cast p2, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;

    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$StateMap;->put(Lorg/codehaus/groovy/ast/Variable;Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;)Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;

    move-result-object p1

    return-object p1
.end method

.method public put(Lorg/codehaus/groovy/ast/Variable;Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;)Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;
    .locals 0

    .line 554
    invoke-static {p1}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->access$100(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/Variable;

    move-result-object p1

    invoke-super {p0, p1, p2}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;

    return-object p1
.end method
