.class public interface abstract Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableNotFinalCallback;
.super Ljava/lang/Object;
.source "FinalVariableAnalyzer.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "VariableNotFinalCallback"
.end annotation


# virtual methods
.method public abstract variableNotAlwaysInitialized(Lorg/codehaus/groovy/ast/expr/VariableExpression;)V
.end method

.method public abstract variableNotFinal(Lorg/codehaus/groovy/ast/Variable;Lorg/codehaus/groovy/ast/expr/Expression;)V
.end method
