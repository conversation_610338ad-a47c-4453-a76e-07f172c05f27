.class public Lorg/codehaus/groovy/classgen/GeneratorContext;
.super Ljava/lang/Object;
.source "GeneratorContext.java"


# static fields
.field private static final CHARACTERS_TO_ENCODE:[Z

.field private static final MAX_ENCODING:I = 0x5d

.field private static final MIN_ENCODING:I = 0x20


# instance fields
.field private closureClassIdx:I

.field private final compileUnit:Lorg/codehaus/groovy/ast/CompileUnit;

.field private innerClassIdx:I

.field private syntheticMethodIdx:I


# direct methods
.method static constructor <clinit>()V
    .locals 3

    const/16 v0, 0x3e

    new-array v0, v0, [Z

    .line 88
    sput-object v0, Lorg/codehaus/groovy/classgen/GeneratorContext;->CHARACTERS_TO_ENCODE:[Z

    const/4 v1, 0x0

    const/4 v2, 0x1

    aput-boolean v2, v0, v1

    aput-boolean v2, v0, v2

    const/16 v1, 0xf

    aput-boolean v2, v0, v1

    const/16 v1, 0xe

    aput-boolean v2, v0, v1

    const/16 v1, 0x1b

    aput-boolean v2, v0, v1

    const/4 v1, 0x4

    aput-boolean v2, v0, v1

    const/16 v1, 0x1c

    aput-boolean v2, v0, v1

    const/16 v1, 0x1e

    aput-boolean v2, v0, v1

    const/16 v1, 0x3b

    aput-boolean v2, v0, v1

    const/16 v1, 0x3d

    aput-boolean v2, v0, v1

    const/16 v1, 0x1a

    aput-boolean v2, v0, v1

    const/16 v1, 0x3c

    aput-boolean v2, v0, v1

    return-void
.end method

.method public constructor <init>(Lorg/codehaus/groovy/ast/CompileUnit;)V
    .locals 1

    .line 38
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x1

    .line 33
    iput v0, p0, Lorg/codehaus/groovy/classgen/GeneratorContext;->innerClassIdx:I

    .line 34
    iput v0, p0, Lorg/codehaus/groovy/classgen/GeneratorContext;->closureClassIdx:I

    const/4 v0, 0x0

    .line 35
    iput v0, p0, Lorg/codehaus/groovy/classgen/GeneratorContext;->syntheticMethodIdx:I

    .line 39
    iput-object p1, p0, Lorg/codehaus/groovy/classgen/GeneratorContext;->compileUnit:Lorg/codehaus/groovy/ast/CompileUnit;

    return-void
.end method

.method public constructor <init>(Lorg/codehaus/groovy/ast/CompileUnit;I)V
    .locals 1

    .line 42
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x1

    .line 33
    iput v0, p0, Lorg/codehaus/groovy/classgen/GeneratorContext;->innerClassIdx:I

    .line 34
    iput v0, p0, Lorg/codehaus/groovy/classgen/GeneratorContext;->closureClassIdx:I

    const/4 v0, 0x0

    .line 35
    iput v0, p0, Lorg/codehaus/groovy/classgen/GeneratorContext;->syntheticMethodIdx:I

    .line 43
    iput-object p1, p0, Lorg/codehaus/groovy/classgen/GeneratorContext;->compileUnit:Lorg/codehaus/groovy/ast/CompileUnit;

    .line 44
    iput p2, p0, Lorg/codehaus/groovy/classgen/GeneratorContext;->innerClassIdx:I

    return-void
.end method

.method public static encodeAsValidClassName(Ljava/lang/String;)Ljava/lang/String;
    .locals 9

    .line 105
    invoke-virtual {p0}, Ljava/lang/String;->length()I

    move-result v0

    const/4 v1, 0x0

    const/4 v2, -0x1

    const/4 v3, 0x0

    move v4, v1

    move v5, v2

    :goto_0
    if-ge v4, v0, :cond_2

    .line 109
    invoke-virtual {p0, v4}, Ljava/lang/String;->charAt(I)C

    move-result v6

    add-int/lit8 v6, v6, -0x20

    if-ltz v6, :cond_1

    .line 110
    sget-object v7, Lorg/codehaus/groovy/classgen/GeneratorContext;->CHARACTERS_TO_ENCODE:[Z

    array-length v8, v7

    if-ge v6, v8, :cond_1

    .line 111
    aget-boolean v6, v7, v6

    if-eqz v6, :cond_1

    if-nez v3, :cond_0

    .line 113
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-virtual {p0}, Ljava/lang/String;->length()I

    move-result v5

    add-int/lit8 v5, v5, 0x3

    invoke-direct {v3, v5}, Ljava/lang/StringBuilder;-><init>(I)V

    .line 114
    invoke-virtual {v3, p0, v1, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/CharSequence;II)Ljava/lang/StringBuilder;

    goto :goto_1

    :cond_0
    add-int/lit8 v5, v5, 0x1

    .line 116
    invoke-virtual {v3, p0, v5, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/CharSequence;II)Ljava/lang/StringBuilder;

    :goto_1
    const/16 v5, 0x5f

    .line 118
    invoke-virtual {v3, v5}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    move v5, v4

    :cond_1
    add-int/lit8 v4, v4, 0x1

    goto :goto_0

    :cond_2
    if-nez v3, :cond_3

    return-object p0

    :cond_3
    if-eq v5, v2, :cond_4

    add-int/lit8 v5, v5, 0x1

    .line 125
    invoke-virtual {v3, p0, v5, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/CharSequence;II)Ljava/lang/StringBuilder;

    .line 126
    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    return-object p0

    .line 124
    :cond_4
    new-instance v0, Lorg/codehaus/groovy/GroovyBugError;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "unexpected escape char control flow in "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    invoke-direct {v0, p0}, Lorg/codehaus/groovy/GroovyBugError;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method private getNextInnerName(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/MethodNode;Ljava/lang/String;)Ljava/lang/String;
    .locals 2

    const-string p1, "_"

    const-string v0, ""

    if-eqz p3, :cond_1

    .line 66
    invoke-virtual {p3}, Lorg/codehaus/groovy/ast/MethodNode;->getName()Ljava/lang/String;

    move-result-object p3

    .line 68
    sget-object v1, Lorg/codehaus/groovy/ast/ClassHelper;->CLOSURE_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {p2, v1}, Lorg/codehaus/groovy/ast/ClassNode;->isDerivedFrom(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result p2

    if-eqz p2, :cond_0

    goto :goto_0

    .line 71
    :cond_0
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-static {p3}, Lorg/codehaus/groovy/classgen/GeneratorContext;->encodeAsValidClassName(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p3

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    .line 75
    :cond_1
    :goto_0
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, p4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    iget p2, p0, Lorg/codehaus/groovy/classgen/GeneratorContext;->closureClassIdx:I

    add-int/lit8 p3, p2, 0x1

    iput p3, p0, Lorg/codehaus/groovy/classgen/GeneratorContext;->closureClassIdx:I

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method


# virtual methods
.method public getCompileUnit()Lorg/codehaus/groovy/ast/CompileUnit;
    .locals 1

    .line 52
    iget-object v0, p0, Lorg/codehaus/groovy/classgen/GeneratorContext;->compileUnit:Lorg/codehaus/groovy/ast/CompileUnit;

    return-object v0
.end method

.method public getNextClosureInnerName(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/MethodNode;)Ljava/lang/String;
    .locals 1

    const-string v0, "closure"

    .line 56
    invoke-direct {p0, p1, p2, p3, v0}, Lorg/codehaus/groovy/classgen/GeneratorContext;->getNextInnerName(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/MethodNode;Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public getNextConstructorReferenceSyntheticMethodName(Lorg/codehaus/groovy/ast/MethodNode;)Ljava/lang/String;
    .locals 4

    .line 79
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "ctorRef$"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ""

    if-nez p1, :cond_0

    goto :goto_0

    .line 82
    :cond_0
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getName()Ljava/lang/String;

    move-result-object p1

    const-string v3, "<"

    invoke-virtual {p1, v3, v1}, Ljava/lang/String;->replace(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/lang/String;

    move-result-object p1

    const-string v3, ">"

    invoke-virtual {p1, v3, v1}, Ljava/lang/String;->replace(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v1, "$"

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    :goto_0
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    iget v0, p0, Lorg/codehaus/groovy/classgen/GeneratorContext;->syntheticMethodIdx:I

    add-int/lit8 v1, v0, 0x1

    iput v1, p0, Lorg/codehaus/groovy/classgen/GeneratorContext;->syntheticMethodIdx:I

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public getNextInnerClassIdx()I
    .locals 2

    .line 48
    iget v0, p0, Lorg/codehaus/groovy/classgen/GeneratorContext;->innerClassIdx:I

    add-int/lit8 v1, v0, 0x1

    iput v1, p0, Lorg/codehaus/groovy/classgen/GeneratorContext;->innerClassIdx:I

    return v0
.end method

.method public getNextLambdaInnerName(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/MethodNode;)Ljava/lang/String;
    .locals 1

    const-string v0, "lambda"

    .line 60
    invoke-direct {p0, p1, p2, p3, v0}, Lorg/codehaus/groovy/classgen/GeneratorContext;->getNextInnerName(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/MethodNode;Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method
