.class public final synthetic Lorg/codehaus/groovy/classgen/AsmClassGenerator$$ExternalSyntheticLambda4;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Supplier;


# instance fields
.field public final synthetic f$0:Lorg/codehaus/groovy/classgen/GeneratorContext;


# direct methods
.method public synthetic constructor <init>(Lorg/codehaus/groovy/classgen/GeneratorContext;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/codehaus/groovy/classgen/AsmClassGenerator$$ExternalSyntheticLambda4;->f$0:Lorg/codehaus/groovy/classgen/GeneratorContext;

    return-void
.end method


# virtual methods
.method public final get()Ljava/lang/Object;
    .locals 1

    iget-object v0, p0, Lorg/codehaus/groovy/classgen/AsmClassGenerator$$ExternalSyntheticLambda4;->f$0:Lorg/codehaus/groovy/classgen/GeneratorContext;

    invoke-virtual {v0}, Lorg/codehaus/groovy/classgen/GeneratorContext;->getCompileUnit()Lorg/codehaus/groovy/ast/CompileUnit;

    move-result-object v0

    return-object v0
.end method
