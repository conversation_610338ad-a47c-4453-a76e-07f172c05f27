.class public final synthetic Lorg/codehaus/groovy/classgen/ReturnAdder$$ExternalSyntheticLambda0;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Lorg/codehaus/groovy/classgen/ReturnAdder$ReturnStatementListener;


# instance fields
.field public final synthetic f$0:[Z


# direct methods
.method public synthetic constructor <init>([Z)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/codehaus/groovy/classgen/ReturnAdder$$ExternalSyntheticLambda0;->f$0:[Z

    return-void
.end method


# virtual methods
.method public final returnStatementAdded(Lorg/codehaus/groovy/ast/stmt/ReturnStatement;)V
    .locals 1

    iget-object v0, p0, Lorg/codehaus/groovy/classgen/ReturnAdder$$ExternalSyntheticLambda0;->f$0:[Z

    invoke-static {v0, p1}, Lorg/codehaus/groovy/classgen/ReturnAdder;->lambda$addReturnsIfNeeded$1([ZLorg/codehaus/groovy/ast/stmt/ReturnStatement;)V

    return-void
.end method
