.class public final synthetic Lorg/codehaus/groovy/classgen/Verifier$$ExternalSyntheticLambda5;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Lorg/codehaus/groovy/classgen/Verifier$DefaultArgsAction;


# instance fields
.field public final synthetic f$0:Lorg/codehaus/groovy/classgen/Verifier;

.field public final synthetic f$1:Lorg/codehaus/groovy/ast/ClassNode;


# direct methods
.method public synthetic constructor <init>(Lorg/codehaus/groovy/classgen/Verifier;Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/codehaus/groovy/classgen/Verifier$$ExternalSyntheticLambda5;->f$0:Lorg/codehaus/groovy/classgen/Verifier;

    iput-object p2, p0, Lorg/codehaus/groovy/classgen/Verifier$$ExternalSyntheticLambda5;->f$1:Lorg/codehaus/groovy/ast/ClassNode;

    return-void
.end method


# virtual methods
.method public final call(Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;[Lorg/codehaus/groovy/ast/Parameter;Lorg/codehaus/groovy/ast/MethodNode;)V
    .locals 2

    iget-object v0, p0, Lorg/codehaus/groovy/classgen/Verifier$$ExternalSyntheticLambda5;->f$0:Lorg/codehaus/groovy/classgen/Verifier;

    iget-object v1, p0, Lorg/codehaus/groovy/classgen/Verifier$$ExternalSyntheticLambda5;->f$1:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v0, v1, p1, p2, p3}, Lorg/codehaus/groovy/classgen/Verifier;->lambda$addDefaultParameterMethods$1$org-codehaus-groovy-classgen-Verifier(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;[Lorg/codehaus/groovy/ast/Parameter;Lorg/codehaus/groovy/ast/MethodNode;)V

    return-void
.end method
