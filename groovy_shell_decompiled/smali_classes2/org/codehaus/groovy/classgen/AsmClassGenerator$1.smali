.class Lorg/codehaus/groovy/classgen/AsmClassGenerator$1;
.super Lorg/codehaus/groovy/classgen/BytecodeInstruction;
.source "AsmClassGenerator.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/codehaus/groovy/classgen/AsmClassGenerator;->visitClosureListExpression(Lorg/codehaus/groovy/ast/expr/ClosureListExpression;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lorg/codehaus/groovy/classgen/AsmClassGenerator;

.field final synthetic val$dflt:Lgroovyjarjarasm/asm/Label;

.field final synthetic val$labels:[Lgroovyjarjarasm/asm/Label;

.field final synthetic val$size:I


# direct methods
.method constructor <init>(Lorg/codehaus/groovy/classgen/AsmClassGenerator;ILgroovyjarjarasm/asm/Label;[Lgroovyjarjarasm/asm/Label;)V
    .locals 0

    .line 1710
    iput-object p1, p0, Lorg/codehaus/groovy/classgen/AsmClassGenerator$1;->this$0:Lorg/codehaus/groovy/classgen/AsmClassGenerator;

    iput p2, p0, Lorg/codehaus/groovy/classgen/AsmClassGenerator$1;->val$size:I

    iput-object p3, p0, Lorg/codehaus/groovy/classgen/AsmClassGenerator$1;->val$dflt:Lgroovyjarjarasm/asm/Label;

    iput-object p4, p0, Lorg/codehaus/groovy/classgen/AsmClassGenerator$1;->val$labels:[Lgroovyjarjarasm/asm/Label;

    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/BytecodeInstruction;-><init>()V

    return-void
.end method


# virtual methods
.method public visit(Lgroovyjarjarasm/asm/MethodVisitor;)V
    .locals 4

    const/16 v0, 0x15

    const/4 v1, 0x1

    .line 1712
    invoke-virtual {p1, v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitVarInsn(II)V

    .line 1713
    iget v0, p0, Lorg/codehaus/groovy/classgen/AsmClassGenerator$1;->val$size:I

    sub-int/2addr v0, v1

    iget-object v1, p0, Lorg/codehaus/groovy/classgen/AsmClassGenerator$1;->val$dflt:Lgroovyjarjarasm/asm/Label;

    iget-object v2, p0, Lorg/codehaus/groovy/classgen/AsmClassGenerator$1;->val$labels:[Lgroovyjarjarasm/asm/Label;

    const/4 v3, 0x0

    invoke-virtual {p1, v3, v0, v1, v2}, Lgroovyjarjarasm/asm/MethodVisitor;->visitTableSwitchInsn(IILgroovyjarjarasm/asm/Label;[Lgroovyjarjarasm/asm/Label;)V

    return-void
.end method
