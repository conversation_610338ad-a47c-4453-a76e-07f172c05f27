.class Lorg/codehaus/groovy/classgen/Verifier$8;
.super Lorg/codehaus/groovy/ast/CodeVisitorSupport;
.source "Verifier.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/codehaus/groovy/classgen/Verifier;->addDefaultParameterMethods(Lorg/codehaus/groovy/ast/ClassNode;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lorg/codehaus/groovy/classgen/Verifier;

.field final synthetic val$newMethod:Lorg/codehaus/groovy/ast/MethodNode;


# direct methods
.method constructor <init>(Lorg/codehaus/groovy/classgen/Verifier;Lorg/codehaus/groovy/ast/MethodNode;)V
    .locals 0

    .line 840
    iput-object p1, p0, Lorg/codehaus/groovy/classgen/Verifier$8;->this$0:Lorg/codehaus/groovy/classgen/Verifier;

    iput-object p2, p0, Lorg/codehaus/groovy/classgen/Verifier$8;->val$newMethod:Lorg/codehaus/groovy/ast/MethodNode;

    invoke-direct {p0}, Lorg/codehaus/groovy/ast/CodeVisitorSupport;-><init>()V

    return-void
.end method


# virtual methods
.method public visitConstructorCallExpression(Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;)V
    .locals 2

    .line 843
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;->isUsingAnonymousInnerClass()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 844
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    iget-object v1, p0, Lorg/codehaus/groovy/classgen/Verifier$8;->val$newMethod:Lorg/codehaus/groovy/ast/MethodNode;

    invoke-virtual {v0, v1}, Lorg/codehaus/groovy/ast/ClassNode;->setEnclosingMethod(Lorg/codehaus/groovy/ast/MethodNode;)V

    .line 846
    :cond_0
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/CodeVisitorSupport;->visitConstructorCallExpression(Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;)V

    return-void
.end method
