.class public Lorg/codehaus/groovy/classgen/VerifierCodeVisitor;
.super Lorg/codehaus/groovy/ast/CodeVisitorSupport;
.source "VerifierCodeVisitor.java"


# instance fields
.field private final classNode:Lorg/codehaus/groovy/ast/ClassNode;


# direct methods
.method public constructor <init>(Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 0

    .line 42
    invoke-direct {p0}, Lorg/codehaus/groovy/ast/CodeVisitorSupport;-><init>()V

    .line 43
    iput-object p1, p0, Lorg/codehaus/groovy/classgen/VerifierCodeVisitor;->classNode:Lorg/codehaus/groovy/ast/ClassNode;

    return-void
.end method

.method public static assertValidIdentifier(Ljava/lang/String;Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V
    .locals 6

    .line 80
    invoke-virtual {p0}, Ljava/lang/String;->length()I

    move-result v0

    const-string v1, "Invalid "

    if-lez v0, :cond_5

    const/4 v2, 0x0

    .line 84
    invoke-virtual {p0, v2}, Ljava/lang/String;->charAt(I)C

    move-result v2

    const/4 v3, 0x1

    if-ne v0, v3, :cond_1

    const/16 v4, 0x24

    if-eq v2, v4, :cond_0

    goto :goto_0

    .line 86
    :cond_0
    new-instance v0, Lorg/codehaus/groovy/syntax/RuntimeParserException;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v1, ". Must include a letter but only found: "

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    invoke-direct {v0, p0, p2}, Lorg/codehaus/groovy/syntax/RuntimeParserException;-><init>(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    throw v0

    .line 88
    :cond_1
    :goto_0
    invoke-static {v2}, Ljava/lang/Character;->isJavaIdentifierStart(C)Z

    move-result v2

    if-eqz v2, :cond_4

    move v2, v3

    :goto_1
    if-ge v2, v0, :cond_3

    .line 93
    invoke-virtual {p0, v2}, Ljava/lang/String;->charAt(I)C

    move-result v4

    .line 94
    invoke-static {v4}, Ljava/lang/Character;->isJavaIdentifierPart(C)Z

    move-result v5

    if-eqz v5, :cond_2

    add-int/lit8 v2, v2, 0x1

    goto :goto_1

    .line 95
    :cond_2
    new-instance v0, Lorg/codehaus/groovy/syntax/RuntimeParserException;

    new-instance v5, Ljava/lang/StringBuilder;

    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v5, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v1, ". Invalid character at position: "

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    add-int/2addr v2, v3

    invoke-virtual {p1, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v1, " of value:  "

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v4}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v1, " in name: "

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    invoke-direct {v0, p0, p2}, Lorg/codehaus/groovy/syntax/RuntimeParserException;-><init>(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    throw v0

    :cond_3
    return-void

    .line 89
    :cond_4
    new-instance v0, Lorg/codehaus/groovy/syntax/RuntimeParserException;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v1, ". Must start with a letter but was: "

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    invoke-direct {v0, p0, p2}, Lorg/codehaus/groovy/syntax/RuntimeParserException;-><init>(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    throw v0

    .line 82
    :cond_5
    new-instance p0, Lorg/codehaus/groovy/syntax/RuntimeParserException;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, ". Identifier must not be empty"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {p0, p1, p2}, Lorg/codehaus/groovy/syntax/RuntimeParserException;-><init>(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    throw p0
.end method


# virtual methods
.method public visitConstructorCallExpression(Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;)V
    .locals 2

    .line 73
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    .line 74
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->isEnum()Z

    move-result v1

    if-eqz v1, :cond_1

    iget-object v1, p0, Lorg/codehaus/groovy/classgen/VerifierCodeVisitor;->classNode:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v0, v1}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_0

    .line 75
    :cond_0
    new-instance v0, Lorg/codehaus/groovy/syntax/RuntimeParserException;

    const-string v1, "Enum constructor calls are only allowed inside the enum class"

    invoke-direct {v0, v1, p1}, Lorg/codehaus/groovy/syntax/RuntimeParserException;-><init>(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    throw v0

    :cond_1
    :goto_0
    return-void
.end method

.method public visitFieldExpression(Lorg/codehaus/groovy/ast/expr/FieldExpression;)V
    .locals 2

    .line 52
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/FieldExpression;->getField()Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object v0

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/FieldNode;->isSynthetic()Z

    move-result v0

    if-nez v0, :cond_0

    .line 53
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/FieldExpression;->getFieldName()Ljava/lang/String;

    move-result-object v0

    const-string v1, "field name"

    invoke-static {v0, v1, p1}, Lorg/codehaus/groovy/classgen/VerifierCodeVisitor;->assertValidIdentifier(Ljava/lang/String;Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 55
    :cond_0
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/CodeVisitorSupport;->visitFieldExpression(Lorg/codehaus/groovy/ast/expr/FieldExpression;)V

    return-void
.end method

.method public visitForLoop(Lorg/codehaus/groovy/ast/stmt/ForStatement;)V
    .locals 2

    .line 47
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/stmt/ForStatement;->getVariable()Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v0

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/Parameter;->getName()Ljava/lang/String;

    move-result-object v0

    const-string v1, "for loop variable name"

    invoke-static {v0, v1, p1}, Lorg/codehaus/groovy/classgen/VerifierCodeVisitor;->assertValidIdentifier(Ljava/lang/String;Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 48
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/CodeVisitorSupport;->visitForLoop(Lorg/codehaus/groovy/ast/stmt/ForStatement;)V

    return-void
.end method

.method public visitListExpression(Lorg/codehaus/groovy/ast/expr/ListExpression;)V
    .locals 3

    .line 64
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/ListExpression;->getExpressions()Ljava/util/List;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/ast/expr/Expression;

    .line 65
    instance-of v2, v1, Lorg/codehaus/groovy/ast/expr/MapEntryExpression;

    if-nez v2, :cond_0

    goto :goto_0

    .line 66
    :cond_0
    new-instance p1, Lorg/codehaus/groovy/syntax/RuntimeParserException;

    const-string v0, "No map entry allowed at this place"

    invoke-direct {p1, v0, v1}, Lorg/codehaus/groovy/syntax/RuntimeParserException;-><init>(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    throw p1

    .line 69
    :cond_1
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/CodeVisitorSupport;->visitListExpression(Lorg/codehaus/groovy/ast/expr/ListExpression;)V

    return-void
.end method

.method public visitVariableExpression(Lorg/codehaus/groovy/ast/expr/VariableExpression;)V
    .locals 2

    .line 59
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->getName()Ljava/lang/String;

    move-result-object v0

    const-string v1, "variable name"

    invoke-static {v0, v1, p1}, Lorg/codehaus/groovy/classgen/VerifierCodeVisitor;->assertValidIdentifier(Ljava/lang/String;Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 60
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/CodeVisitorSupport;->visitVariableExpression(Lorg/codehaus/groovy/ast/expr/VariableExpression;)V

    return-void
.end method
