.class public Lorg/codehaus/groovy/classgen/genArrayAccess;
.super Lgroovy/lang/Script;
.source "genArrayAccess.groovy"


# static fields
.field private static synthetic $callSiteArray:Ljava/lang/ref/SoftReference;

.field private static synthetic $staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

.field private static synthetic $staticClassInfo$:Lorg/codehaus/groovy/reflection/ClassInfo;

.field public static transient synthetic __$stMC:Z


# direct methods
.method private static synthetic $createCallSiteArray()Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;
    .locals 3

    const/4 v0, 0x5

    new-array v0, v0, [Ljava/lang/String;

    invoke-static {v0}, Lorg/codehaus/groovy/classgen/genArrayAccess;->$createCallSiteArray_1([Ljava/lang/String;)V

    new-instance v1, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;

    const-class v2, Lorg/codehaus/groovy/classgen/genArrayAccess;

    invoke-direct {v1, v2, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;-><init>(Ljava/lang/Class;[Ljava/lang/String;)V

    return-object v1
.end method

.method private static synthetic $createCallSiteArray_1([Ljava/lang/String;)V
    .locals 3

    const/4 v0, 0x0

    const-string v1, "runScript"

    aput-object v1, p0, v0

    const/4 v0, 0x1

    const-string v1, "println"

    aput-object v1, p0, v0

    const/4 v0, 0x2

    const-string v2, "genInners"

    aput-object v2, p0, v0

    const/4 v0, 0x3

    aput-object v1, p0, v0

    const/4 v0, 0x4

    const-string v1, "each"

    aput-object v1, p0, v0

    return-void
.end method

.method private static synthetic $getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;
    .locals 2

    sget-object v0, Lorg/codehaus/groovy/classgen/genArrayAccess;->$callSiteArray:Ljava/lang/ref/SoftReference;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Ljava/lang/ref/SoftReference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;

    if-nez v0, :cond_1

    :cond_0
    invoke-static {}, Lorg/codehaus/groovy/classgen/genArrayAccess;->$createCallSiteArray()Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;

    move-result-object v0

    new-instance v1, Ljava/lang/ref/SoftReference;

    invoke-direct {v1, v0}, Ljava/lang/ref/SoftReference;-><init>(Ljava/lang/Object;)V

    sput-object v1, Lorg/codehaus/groovy/classgen/genArrayAccess;->$callSiteArray:Ljava/lang/ref/SoftReference;

    :cond_1
    iget-object v0, v0, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;->array:[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    return-object v0
.end method

.method public constructor <init>()V
    .locals 0

    invoke-static {}, Lorg/codehaus/groovy/classgen/genArrayAccess;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    invoke-direct {p0}, Lgroovy/lang/Script;-><init>()V

    return-void
.end method

.method public constructor <init>(Lgroovy/lang/Binding;)V
    .locals 0

    invoke-static {}, Lorg/codehaus/groovy/classgen/genArrayAccess;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    invoke-direct {p0, p1}, Lgroovy/lang/Script;-><init>(Lgroovy/lang/Binding;)V

    return-void
.end method

.method public static varargs main([Ljava/lang/String;)V
    .locals 3

    invoke-static {}, Lorg/codehaus/groovy/classgen/genArrayAccess;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v0

    const/4 v1, 0x0

    aget-object v0, v0, v1

    const-class v1, Lorg/codehaus/groovy/runtime/InvokerHelper;

    const-class v2, Lorg/codehaus/groovy/classgen/genArrayAccess;

    invoke-interface {v0, v1, v2, p0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method


# virtual methods
.method protected synthetic $getStaticMetaClass()Lgroovy/lang/MetaClass;
    .locals 2

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    const-class v1, Lorg/codehaus/groovy/classgen/genArrayAccess;

    if-eq v0, v1, :cond_0

    invoke-static {p0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->initMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0

    :cond_0
    sget-object v0, Lorg/codehaus/groovy/classgen/genArrayAccess;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    if-nez v0, :cond_1

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getClassInfo(Ljava/lang/Class;)Lorg/codehaus/groovy/reflection/ClassInfo;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/classgen/genArrayAccess;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    :cond_1
    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0
.end method

.method public genInners()Ljava/lang/Object;
    .locals 6

    invoke-static {}, Lorg/codehaus/groovy/classgen/genArrayAccess;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v0

    .line 40
    new-instance v1, Lgroovy/lang/Reference;

    const-string v2, ""

    invoke-direct {v1, v2}, Lgroovy/lang/Reference;-><init>(Ljava/lang/Object;)V

    const/16 v2, 0x10

    new-array v2, v2, [Ljava/lang/Object;

    const/4 v3, 0x0

    const-string v4, "boolean"

    aput-object v4, v2, v3

    const/4 v3, 0x1

    const-string v4, "Boolean"

    aput-object v4, v2, v3

    const/4 v3, 0x2

    const-string v4, "byte"

    aput-object v4, v2, v3

    const/4 v3, 0x3

    const-string v4, "Byte"

    aput-object v4, v2, v3

    const-string v3, "char"

    const/4 v4, 0x4

    aput-object v3, v2, v4

    const/4 v3, 0x5

    const-string v5, "Character"

    aput-object v5, v2, v3

    const/4 v3, 0x6

    const-string v5, "short"

    aput-object v5, v2, v3

    const/4 v3, 0x7

    const-string v5, "Short"

    aput-object v5, v2, v3

    const/16 v3, 0x8

    const-string v5, "int"

    aput-object v5, v2, v3

    const/16 v3, 0x9

    const-string v5, "Integer"

    aput-object v5, v2, v3

    const/16 v3, 0xa

    const-string v5, "long"

    aput-object v5, v2, v3

    const/16 v3, 0xb

    const-string v5, "Long"

    aput-object v5, v2, v3

    const/16 v3, 0xc

    const-string v5, "float"

    aput-object v5, v2, v3

    const/16 v3, 0xd

    const-string v5, "Float"

    aput-object v5, v2, v3

    const/16 v3, 0xe

    const-string v5, "double"

    aput-object v5, v2, v3

    const/16 v3, 0xf

    const-string v5, "Double"

    aput-object v5, v2, v3

    .line 42
    invoke-static {v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createMap([Ljava/lang/Object;)Ljava/util/Map;

    move-result-object v2

    .line 53
    aget-object v0, v0, v4

    new-instance v3, Lorg/codehaus/groovy/classgen/genArrayAccess$_genInners_closure1;

    invoke-direct {v3, p0, p0, v1}, Lorg/codehaus/groovy/classgen/genArrayAccess$_genInners_closure1;-><init>(Ljava/lang/Object;Ljava/lang/Object;Lgroovy/lang/Reference;)V

    invoke-interface {v0, v2, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 148
    invoke-virtual {v1}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    return-object v0
.end method

.method public run()Ljava/lang/Object;
    .locals 8

    invoke-static {}, Lorg/codehaus/groovy/classgen/genArrayAccess;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v0

    sget-boolean v1, Lorg/codehaus/groovy/classgen/genArrayAccess;->__$stMC:Z

    const-string v2, "\n}\n"

    const-string v3, "\npackage org.codehaus.groovy.runtime.dgmimpl;\n\nimport groovy.lang.MetaClassImpl;\nimport groovy.lang.MetaMethod;\nimport org.codehaus.groovy.reflection.CachedClass;\nimport org.codehaus.groovy.reflection.ReflectionCache;\nimport org.codehaus.groovy.runtime.callsite.CallSite;\nimport org.codehaus.groovy.runtime.callsite.PojoMetaMethodSite;\n\npublic class ArrayOperations {\n  "

    const/4 v4, 0x0

    const/4 v5, 0x1

    if-nez v1, :cond_0

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->disabledStandardMetaClass()Z

    move-result v1

    if-nez v1, :cond_0

    const/4 v1, 0x3

    .line 24
    aget-object v0, v0, v1

    new-instance v1, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v5, v5, [Ljava/lang/Object;

    .line 35
    invoke-virtual {p0}, Lorg/codehaus/groovy/classgen/genArrayAccess;->genInners()Ljava/lang/Object;

    move-result-object v6

    aput-object v6, v5, v4

    filled-new-array {v3, v2}, [Ljava/lang/String;

    move-result-object v2

    invoke-direct {v1, v5, v2}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-interface {v0, p0, v1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callCurrent(Lgroovy/lang/GroovyObject;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    return-object v0

    .line 24
    :cond_0
    aget-object v1, v0, v5

    new-instance v6, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v5, v5, [Ljava/lang/Object;

    const/4 v7, 0x2

    .line 35
    aget-object v0, v0, v7

    invoke-interface {v0, p0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callCurrent(Lgroovy/lang/GroovyObject;)Ljava/lang/Object;

    move-result-object v0

    aput-object v0, v5, v4

    filled-new-array {v3, v2}, [Ljava/lang/String;

    move-result-object v0

    invoke-direct {v6, v5, v0}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-interface {v1, p0, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callCurrent(Lgroovy/lang/GroovyObject;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    return-object v0
.end method
