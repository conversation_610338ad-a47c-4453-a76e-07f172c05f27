.class public Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;
.super Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;
.source "ClassCompletionVerifier.java"


# static fields
.field private static final INVALID_NAME_CHARS:[Ljava/lang/String;


# instance fields
.field private currentClass:Lorg/codehaus/groovy/ast/ClassNode;

.field private inConstructor:Z

.field private inStaticConstructor:Z

.field private final source:Lorg/codehaus/groovy/control/SourceUnit;

.field private final strictNames:Z


# direct methods
.method static constructor <clinit>()V
    .locals 7

    const-string v0, "."

    const-string v1, ":"

    const-string v2, "/"

    const-string v3, ";"

    const-string v4, "["

    const-string v5, "<"

    const-string v6, ">"

    .line 88
    filled-new-array/range {v0 .. v6}, [Ljava/lang/String;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->INVALID_NAME_CHARS:[Ljava/lang/String;

    return-void
.end method

.method public constructor <init>(Lorg/codehaus/groovy/control/SourceUnit;)V
    .locals 2

    .line 96
    invoke-direct {p0}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;-><init>()V

    const-string v0, "groovy.compiler.strictNames"

    const-string v1, "false"

    .line 90
    invoke-static {v0, v1}, Ljava/lang/System;->getProperty(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Ljava/lang/Boolean;->parseBoolean(Ljava/lang/String;)Z

    move-result v0

    iput-boolean v0, p0, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->strictNames:Z

    const/4 v0, 0x0

    .line 93
    iput-boolean v0, p0, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->inConstructor:Z

    .line 94
    iput-boolean v0, p0, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->inStaticConstructor:Z

    .line 97
    iput-object p1, p0, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->source:Lorg/codehaus/groovy/control/SourceUnit;

    return-void
.end method

.method private addErrorIfParamsAndReturnTypeEqual([Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/Parameter;Lorg/codehaus/groovy/ast/MethodNode;Lorg/codehaus/groovy/ast/MethodNode;)V
    .locals 4

    const/4 v0, 0x1

    const/4 v1, 0x0

    .line 511
    :goto_0
    array-length v2, p1

    if-ge v1, v2, :cond_1

    .line 512
    aget-object v2, p2, v1

    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/Parameter;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v2

    aget-object v3, p1, v1

    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/Parameter;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v3

    invoke-virtual {v2, v3}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result v2

    and-int/2addr v0, v2

    if-nez v0, :cond_0

    goto :goto_1

    :cond_0
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 515
    :cond_1
    :goto_1
    invoke-virtual {p3}, Lorg/codehaus/groovy/ast/MethodNode;->getReturnType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    invoke-virtual {p4}, Lorg/codehaus/groovy/ast/MethodNode;->getReturnType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p2

    invoke-virtual {p1, p2}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result p1

    and-int/2addr p1, v0

    if-eqz p1, :cond_2

    .line 517
    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    const-string p2, "Repetitive method name/signature for "

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-static {p3}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->getDescription(Lorg/codehaus/groovy/ast/MethodNode;)Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string p2, " in "

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    iget-object p2, p0, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    .line 518
    invoke-static {p2}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->getDescription(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string p2, "."

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    .line 517
    invoke-virtual {p0, p1, p3}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    :cond_2
    return-void
.end method

.method private addInvalidUseOfFinalError(Lorg/codehaus/groovy/ast/MethodNode;[Lorg/codehaus/groovy/ast/Parameter;Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 3

    .line 383
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "You are not allowed to override the final method "

    .line 384
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getName()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 385
    invoke-direct {p0, p2, v0}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->appendParamsDescription([Lorg/codehaus/groovy/ast/Parameter;Ljava/lang/StringBuilder;)V

    const-string p2, " from "

    .line 386
    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-static {p3}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->getDescription(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/lang/String;

    move-result-object p3

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string p2, "."

    .line 387
    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 388
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p2, p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    return-void
.end method

.method private addWeakerAccessError(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/MethodNode;[Lorg/codehaus/groovy/ast/Parameter;Lorg/codehaus/groovy/ast/MethodNode;)V
    .locals 2

    .line 406
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    .line 407
    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/MethodNode;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 408
    invoke-direct {p0, p3, v0}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->appendParamsDescription([Lorg/codehaus/groovy/ast/Parameter;Ljava/lang/StringBuilder;)V

    const-string p3, " in "

    .line 409
    invoke-virtual {v0, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 410
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string p1, " cannot override "

    .line 411
    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 412
    invoke-virtual {p4}, Lorg/codehaus/groovy/ast/MethodNode;->getName()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 413
    invoke-virtual {v0, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 414
    invoke-virtual {p4}, Lorg/codehaus/groovy/ast/MethodNode;->getDeclaringClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string p1, "; attempting to assign weaker access privileges; was "

    .line 415
    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 416
    invoke-virtual {p4}, Lorg/codehaus/groovy/ast/MethodNode;->isPublic()Z

    move-result p1

    if-eqz p1, :cond_0

    const-string p1, "public"

    goto :goto_0

    :cond_0
    invoke-virtual {p4}, Lorg/codehaus/groovy/ast/MethodNode;->isProtected()Z

    move-result p1

    if-eqz p1, :cond_1

    const-string p1, "protected"

    goto :goto_0

    :cond_1
    const-string p1, "package-private"

    :goto_0
    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 417
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    return-void
.end method

.method private appendParamsDescription([Lorg/codehaus/groovy/ast/Parameter;Ljava/lang/StringBuilder;)V
    .locals 5

    const-string v0, "("

    .line 392
    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 394
    array-length v0, p1

    const/4 v1, 0x0

    move v2, v1

    :goto_0
    if-ge v1, v0, :cond_1

    aget-object v3, p1, v1

    if-eqz v2, :cond_0

    const-string v4, ","

    .line 396
    invoke-virtual {p2, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_1

    :cond_0
    const/4 v2, 0x1

    .line 400
    :goto_1
    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/Parameter;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v3

    invoke-virtual {p2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_1
    const-string p1, ")"

    .line 402
    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    return-void
.end method

.method private checkAbstractDeclaration(Lorg/codehaus/groovy/ast/MethodNode;)V
    .locals 2

    .line 301
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->isAbstract()Z

    move-result v0

    if-nez v0, :cond_0

    return-void

    .line 302
    :cond_0
    iget-object v0, p0, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->getModifiers()I

    move-result v0

    invoke-static {v0}, Ljava/lang/reflect/Modifier;->isAbstract(I)Z

    move-result v0

    if-eqz v0, :cond_1

    return-void

    .line 303
    :cond_1
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Can\'t have an abstract method in a non-abstract class. The "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v1, p0, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    .line 304
    invoke-static {v1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->getDescription(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, " must be declared abstract or the method \'"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    .line 305
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getTypeDescriptor()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "\' must not be abstract."

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    .line 303
    invoke-virtual {p0, v0, p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    return-void
.end method

.method private checkAbstractMethodVisibility(Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 4

    .line 189
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getModifiers()I

    move-result v0

    invoke-static {v0}, Ljava/lang/reflect/Modifier;->isAbstract(I)Z

    move-result v0

    if-eqz v0, :cond_2

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getModifiers()I

    move-result v0

    invoke-static {v0}, Ljava/lang/reflect/Modifier;->isInterface(I)Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_1

    .line 191
    :cond_0
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getAbstractMethods()Ljava/util/List;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_1
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_2

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/ast/MethodNode;

    .line 192
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/MethodNode;->isPrivate()Z

    move-result v2

    if-eqz v2, :cond_1

    .line 193
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "Method \'"

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/MethodNode;->getName()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    const-string v3, "\' from "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-static {p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->getDescription(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    const-string v3, " must not be private as it is declared as an abstract method."

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {p0, v2, v1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    goto :goto_0

    :cond_2
    :goto_1
    return-void
.end method

.method private checkClassExtendsAllSelfTypes(Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 7

    .line 225
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getModifiers()I

    move-result v0

    .line 226
    invoke-static {v0}, Ljava/lang/reflect/Modifier;->isInterface(I)Z

    move-result v0

    if-nez v0, :cond_3

    .line 227
    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->getInterfacesAndSuperInterfaces(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/util/Set;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_3

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/ast/ClassNode;

    .line 228
    invoke-static {v1}, Lorg/codehaus/groovy/transform/trait/Traits;->isTrait(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v2

    if-eqz v2, :cond_0

    .line 229
    new-instance v2, Ljava/util/LinkedHashSet;

    invoke-direct {v2}, Ljava/util/LinkedHashSet;-><init>()V

    const/4 v3, 0x1

    const/4 v4, 0x0

    .line 230
    invoke-static {v1, v2, v3, v4}, Lorg/codehaus/groovy/transform/trait/Traits;->collectSelfTypes(Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/LinkedHashSet;ZZ)Ljava/util/LinkedHashSet;

    move-result-object v2

    invoke-virtual {v2}, Ljava/util/LinkedHashSet;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :cond_1
    :goto_0
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_0

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lorg/codehaus/groovy/ast/ClassNode;

    .line 231
    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/ClassNode;->isInterface()Z

    move-result v4

    const-string v5, " implements "

    if-eqz v4, :cond_2

    invoke-virtual {p1, v3}, Lorg/codehaus/groovy/ast/ClassNode;->implementsInterface(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v4

    if-nez v4, :cond_2

    .line 232
    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    invoke-static {p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->getDescription(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v4, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    .line 233
    invoke-static {v1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->getDescription(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    const-string v5, " but does not implement self type "

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    .line 234
    invoke-static {v3}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->getDescription(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v4, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    .line 232
    invoke-virtual {p0, v3, v1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    goto :goto_0

    .line 236
    :cond_2
    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/ClassNode;->isInterface()Z

    move-result v4

    if-nez v4, :cond_1

    invoke-virtual {p1, v3}, Lorg/codehaus/groovy/ast/ClassNode;->isDerivedFrom(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v4

    if-nez v4, :cond_1

    .line 237
    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    invoke-static {p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->getDescription(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v4, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    .line 238
    invoke-static {v1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->getDescription(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    const-string v5, " but does not extend self type "

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    .line 239
    invoke-static {v3}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->getDescription(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v4, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    .line 237
    invoke-virtual {p0, v3, v1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    goto/16 :goto_0

    :cond_3
    return-void
.end method

.method private checkClassForAbstractAndFinal(Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 2

    .line 254
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getModifiers()I

    move-result v0

    invoke-static {v0}, Ljava/lang/reflect/Modifier;->isAbstract(I)Z

    move-result v0

    if-nez v0, :cond_0

    return-void

    .line 255
    :cond_0
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getModifiers()I

    move-result v0

    invoke-static {v0}, Ljava/lang/reflect/Modifier;->isFinal(I)Z

    move-result v0

    if-nez v0, :cond_1

    return-void

    .line 256
    :cond_1
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->isInterface()Z

    move-result v0

    const-string v1, "The "

    if-eqz v0, :cond_2

    .line 257
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-static {p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->getDescription(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, " must not be final. It is by definition abstract."

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0, p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    goto :goto_0

    .line 259
    :cond_2
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-static {p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->getDescription(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, " must not be both final and abstract."

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0, p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    :goto_0
    return-void
.end method

.method private checkClassForIncorrectModifiers(Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 0

    .line 249
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->checkClassForAbstractAndFinal(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 250
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->checkClassForOtherModifiers(Lorg/codehaus/groovy/ast/ClassNode;)V

    return-void
.end method

.method private checkClassForModifier(Lorg/codehaus/groovy/ast/ClassNode;ZLjava/lang/String;)V
    .locals 1

    if-nez p2, :cond_0

    return-void

    .line 281
    :cond_0
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "The "

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-static {p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->getDescription(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string v0, " has an incorrect modifier "

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string p3, "."

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p2, p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    return-void
.end method

.method private checkClassForOtherModifiers(Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 2

    .line 264
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getModifiers()I

    move-result v0

    invoke-static {v0}, Ljava/lang/reflect/Modifier;->isTransient(I)Z

    move-result v0

    const-string v1, "transient"

    invoke-direct {p0, p1, v0, v1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->checkClassForModifier(Lorg/codehaus/groovy/ast/ClassNode;ZLjava/lang/String;)V

    .line 265
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getModifiers()I

    move-result v0

    invoke-static {v0}, Ljava/lang/reflect/Modifier;->isVolatile(I)Z

    move-result v0

    const-string v1, "volatile"

    invoke-direct {p0, p1, v0, v1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->checkClassForModifier(Lorg/codehaus/groovy/ast/ClassNode;ZLjava/lang/String;)V

    .line 266
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getModifiers()I

    move-result v0

    invoke-static {v0}, Ljava/lang/reflect/Modifier;->isNative(I)Z

    move-result v0

    const-string v1, "native"

    invoke-direct {p0, p1, v0, v1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->checkClassForModifier(Lorg/codehaus/groovy/ast/ClassNode;ZLjava/lang/String;)V

    .line 267
    instance-of v0, p1, Lorg/codehaus/groovy/ast/InnerClassNode;

    if-nez v0, :cond_0

    .line 268
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getModifiers()I

    move-result v0

    invoke-static {v0}, Ljava/lang/reflect/Modifier;->isStatic(I)Z

    move-result v0

    const-string v1, "static"

    invoke-direct {p0, p1, v0, v1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->checkClassForModifier(Lorg/codehaus/groovy/ast/ClassNode;ZLjava/lang/String;)V

    .line 269
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getModifiers()I

    move-result v0

    invoke-static {v0}, Ljava/lang/reflect/Modifier;->isPrivate(I)Z

    move-result v0

    const-string v1, "private"

    invoke-direct {p0, p1, v0, v1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->checkClassForModifier(Lorg/codehaus/groovy/ast/ClassNode;ZLjava/lang/String;)V

    :cond_0
    return-void
.end method

.method private checkClassForOverwritingFinal(Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 3

    .line 309
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getSuperClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    if-nez v0, :cond_0

    return-void

    .line 311
    :cond_0
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->getModifiers()I

    move-result v1

    invoke-static {v1}, Ljava/lang/reflect/Modifier;->isFinal(I)Z

    move-result v1

    if-nez v1, :cond_1

    return-void

    .line 312
    :cond_1
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "You are not allowed to overwrite the final "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-static {v0}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->getDescription(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "."

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    .line 313
    invoke-virtual {p0, v0, p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    return-void
.end method

.method private checkDuplicateProperties(Lorg/codehaus/groovy/ast/PropertyNode;)V
    .locals 9

    .line 542
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/PropertyNode;->getDeclaringClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    .line 543
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/PropertyNode;->getName()Ljava/lang/String;

    move-result-object v1

    .line 544
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "get"

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-static {v1}, Lorg/apache/groovy/util/BeanUtils;->capitalize(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v2, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    const/4 v4, 0x0

    .line 545
    invoke-virtual {v1, v4}, Ljava/lang/String;->charAt(I)C

    move-result v4

    invoke-static {v4}, Ljava/lang/Character;->isUpperCase(C)Z

    move-result v4

    if-eqz v4, :cond_1

    .line 546
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->getProperties()Ljava/util/List;

    move-result-object v4

    invoke-interface {v4}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v4

    :cond_0
    :goto_0
    invoke-interface {v4}, Ljava/util/Iterator;->hasNext()Z

    move-result v5

    if-eqz v5, :cond_1

    invoke-interface {v4}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lorg/codehaus/groovy/ast/PropertyNode;

    .line 547
    invoke-virtual {v5}, Lorg/codehaus/groovy/ast/PropertyNode;->getField()Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object v6

    invoke-virtual {v6}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object v6

    .line 548
    new-instance v7, Ljava/lang/StringBuilder;

    invoke-direct {v7}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v7, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v7

    invoke-static {v6}, Lorg/apache/groovy/util/BeanUtils;->capitalize(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    invoke-virtual {v7, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v7

    invoke-virtual {v7}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v7

    if-eq p1, v5, :cond_0

    .line 549
    invoke-virtual {v2, v7}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v5

    if-eqz v5, :cond_0

    .line 550
    new-instance v5, Ljava/lang/StringBuilder;

    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    const-string v7, "The field "

    invoke-virtual {v5, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    invoke-virtual {v5, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    const-string v7, " and "

    invoke-virtual {v5, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    const-string v6, " on the class "

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    .line 551
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    const-string v6, " will result in duplicate JavaBean properties, which is not allowed"

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v5

    .line 552
    invoke-virtual {p0, v5, p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    goto :goto_0

    :cond_1
    return-void
.end method

.method private checkFinalFieldAccess(Lorg/codehaus/groovy/ast/expr/Expression;)V
    .locals 4

    .line 599
    instance-of v0, p1, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    if-nez v0, :cond_0

    instance-of v1, p1, Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    if-nez v1, :cond_0

    return-void

    :cond_0
    const/4 v1, 0x0

    if-eqz v0, :cond_1

    .line 602
    move-object v0, p1

    check-cast v0, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    .line 603
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->getAccessedVariable()Lorg/codehaus/groovy/ast/Variable;

    move-result-object v1

    goto :goto_0

    .line 605
    :cond_1
    move-object v0, p1

    check-cast v0, Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    .line 606
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->getObjectExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v2

    .line 607
    instance-of v3, v2, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    if-eqz v3, :cond_2

    .line 608
    check-cast v2, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    .line 609
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->isThisExpression()Z

    move-result v2

    if-eqz v2, :cond_2

    .line 610
    iget-object v1, p0, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->getPropertyAsString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v1, v0}, Lorg/codehaus/groovy/ast/ClassNode;->getDeclaredField(Ljava/lang/String;)Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object v1

    .line 614
    :cond_2
    :goto_0
    instance-of v0, v1, Lorg/codehaus/groovy/ast/FieldNode;

    if-eqz v0, :cond_8

    .line 615
    check-cast v1, Lorg/codehaus/groovy/ast/FieldNode;

    .line 621
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/FieldNode;->isFinal()Z

    move-result v0

    .line 622
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/FieldNode;->isStatic()Z

    move-result v2

    if-eqz v0, :cond_5

    if-eqz v2, :cond_3

    .line 623
    iget-boolean v0, p0, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->inStaticConstructor:Z

    if-eqz v0, :cond_4

    :cond_3
    if-nez v2, :cond_5

    iget-boolean v0, p0, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->inConstructor:Z

    if-nez v0, :cond_5

    :cond_4
    const/4 v0, 0x1

    goto :goto_1

    :cond_5
    const/4 v0, 0x0

    :goto_1
    if-eqz v0, :cond_8

    .line 625
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "cannot modify"

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    if-eqz v2, :cond_6

    const-string v3, " static"

    goto :goto_2

    :cond_6
    const-string v3, ""

    :goto_2
    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v3, " final field \'"

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "\' outside of "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    if-eqz v2, :cond_7

    const-string v1, "static initialization block."

    goto :goto_3

    :cond_7
    const-string v1, "constructor."

    .line 626
    :goto_3
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    .line 625
    invoke-virtual {p0, v0, p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    :cond_8
    return-void
.end method

.method private checkForInvalidDeclaration(Lorg/codehaus/groovy/ast/expr/Expression;)V
    .locals 1

    .line 683
    instance-of v0, p1, Lorg/codehaus/groovy/ast/expr/DeclarationExpression;

    if-nez v0, :cond_0

    return-void

    :cond_0
    const-string v0, "Invalid use of declaration inside method call."

    .line 684
    invoke-virtual {p0, v0, p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    return-void
.end method

.method private checkGenericsUsage(Lorg/codehaus/groovy/ast/ASTNode;Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 2

    .line 722
    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/ClassNode;->isArray()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 723
    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/ClassNode;->getComponentType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p2

    invoke-direct {p0, p1, p2}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->checkGenericsUsage(Lorg/codehaus/groovy/ast/ASTNode;Lorg/codehaus/groovy/ast/ClassNode;)V

    goto :goto_0

    .line 724
    :cond_0
    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/ClassNode;->isRedirectNode()Z

    move-result v0

    if-nez v0, :cond_1

    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/ClassNode;->isUsingGenerics()Z

    move-result v0

    if-eqz v0, :cond_1

    .line 725
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "A transform used a generics containing ClassNode "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string v0, " for "

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    .line 727
    invoke-static {p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->getRefDescriptor(Lorg/codehaus/groovy/ast/ASTNode;)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string v0, "directly. You are not supposed to do this. Please create a new ClassNode referring to the old ClassNode and use the new ClassNode instead of the old one. Otherwise the compiler will create wrong descriptors and a potential NullPointerException in TypeResolver in the OpenJDK. If this is not your own doing, please report this bug to the writer of the transform."

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    .line 725
    invoke-virtual {p0, p2, p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    :cond_1
    :goto_0
    return-void
.end method

.method private checkGenericsUsage(Lorg/codehaus/groovy/ast/ASTNode;[Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 3

    .line 710
    array-length v0, p2

    const/4 v1, 0x0

    :goto_0
    if-ge v1, v0, :cond_0

    aget-object v2, p2, v1

    .line 711
    invoke-direct {p0, p1, v2}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->checkGenericsUsage(Lorg/codehaus/groovy/ast/ASTNode;Lorg/codehaus/groovy/ast/ClassNode;)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    return-void
.end method

.method private checkGenericsUsage(Lorg/codehaus/groovy/ast/ASTNode;[Lorg/codehaus/groovy/ast/Parameter;)V
    .locals 3

    .line 716
    array-length v0, p2

    const/4 v1, 0x0

    :goto_0
    if-ge v1, v0, :cond_0

    aget-object v2, p2, v1

    .line 717
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/Parameter;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v2

    invoke-direct {p0, p1, v2}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->checkGenericsUsage(Lorg/codehaus/groovy/ast/ASTNode;Lorg/codehaus/groovy/ast/ClassNode;)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    return-void
.end method

.method private checkImplementsAndExtends(Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 6

    .line 317
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getSuperClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    .line 318
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->isInterface()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->isInterface()Z

    move-result v1

    if-nez v1, :cond_0

    .line 319
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "You are not allowed to extend the "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-static {v0}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->getDescription(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ", use implements instead."

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0, p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 321
    :cond_0
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getInterfaces()[Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    array-length v1, v0

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v1, :cond_2

    aget-object v3, v0, v2

    .line 323
    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/ClassNode;->isInterface()Z

    move-result v4

    if-nez v4, :cond_1

    .line 324
    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    const-string v5, "You are not allowed to implement the "

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-static {v3}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->getDescription(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v4, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    const-string v4, ", use extends instead."

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {p0, v3, p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    :cond_1
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_2
    return-void
.end method

.method private checkInterfaceFieldModifiers(Lorg/codehaus/groovy/ast/FieldNode;)V
    .locals 2

    .line 559
    iget-object v0, p0, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->isInterface()Z

    move-result v0

    if-nez v0, :cond_0

    return-void

    .line 560
    :cond_0
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/FieldNode;->getModifiers()I

    move-result v0

    and-int/lit8 v0, v0, 0x19

    if-eqz v0, :cond_1

    .line 561
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/FieldNode;->getModifiers()I

    move-result v0

    and-int/lit8 v0, v0, 0x6

    if-eqz v0, :cond_2

    .line 562
    :cond_1
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "The "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-static {p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->getDescription(Lorg/codehaus/groovy/ast/FieldNode;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, " is not \'public static final\' but is defined in "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v1, p0, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    .line 563
    invoke-static {v1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->getDescription(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "."

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    .line 562
    invoke-virtual {p0, v0, p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    :cond_2
    return-void
.end method

.method private checkInterfaceMethodVisibility(Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 4

    .line 177
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->isInterface()Z

    move-result v0

    if-nez v0, :cond_0

    return-void

    .line 178
    :cond_0
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getMethods()Ljava/util/List;

    move-result-object p1

    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_1
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_3

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/ast/MethodNode;

    .line 179
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/MethodNode;->isPrivate()Z

    move-result v1

    const-string v2, "."

    const-string v3, "Method \'"

    if-eqz v1, :cond_2

    .line 180
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/MethodNode;->getName()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v3, "\' is private but should be public in "

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    iget-object v3, p0, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {v3}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->getDescription(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0, v1, v0}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    goto :goto_0

    .line 181
    :cond_2
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/MethodNode;->isProtected()Z

    move-result v1

    if-eqz v1, :cond_1

    .line 182
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/MethodNode;->getName()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v3, "\' is protected but should be public in "

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    iget-object v3, p0, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {v3}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->getDescription(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0, v1, v0}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    goto :goto_0

    :cond_3
    return-void
.end method

.method private checkInvalidDeclarationModifier(Lorg/codehaus/groovy/ast/expr/DeclarationExpression;ILjava/lang/String;)V
    .locals 1

    .line 677
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/DeclarationExpression;->getVariableExpression()Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v0

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->getModifiers()I

    move-result v0

    and-int/2addr p2, v0

    if-eqz p2, :cond_0

    .line 678
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "Modifier \'"

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string p3, "\' not allowed here."

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p2, p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    :cond_0
    return-void
.end method

.method private checkInvalidFieldModifiers(Lorg/codehaus/groovy/ast/FieldNode;)V
    .locals 2

    .line 568
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/FieldNode;->getModifiers()I

    move-result v0

    const/16 v1, 0x50

    and-int/2addr v0, v1

    if-ne v0, v1, :cond_0

    .line 569
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Illegal combination of modifiers, final and volatile, for field \'"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "\'"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0, p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    :cond_0
    return-void
.end method

.method private checkMethodForModifier(Lorg/codehaus/groovy/ast/MethodNode;ZLjava/lang/String;)V
    .locals 1

    if-nez p2, :cond_0

    return-void

    .line 276
    :cond_0
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "The "

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-static {p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->getDescription(Lorg/codehaus/groovy/ast/MethodNode;)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string v0, " has an incorrect modifier "

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string p3, "."

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p2, p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    return-void
.end method

.method private checkMethodForWeakerAccessPrivileges(Lorg/codehaus/groovy/ast/MethodNode;Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 4

    .line 463
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->isPublic()Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    .line 464
    :cond_0
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v0

    .line 465
    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/ClassNode;->getSuperClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getName()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Lorg/codehaus/groovy/ast/ClassNode;->getMethods(Ljava/lang/String;)Ljava/util/List;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :cond_1
    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_6

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lorg/codehaus/groovy/ast/MethodNode;

    .line 466
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/MethodNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v3

    .line 467
    invoke-static {v0, v3}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->hasEqualParameterTypes([Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/Parameter;)Z

    move-result v3

    if-nez v3, :cond_2

    goto :goto_0

    .line 468
    :cond_2
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->isPrivate()Z

    move-result v3

    if-eqz v3, :cond_3

    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/MethodNode;->isPrivate()Z

    move-result v3

    if-eqz v3, :cond_5

    .line 469
    :cond_3
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->isProtected()Z

    move-result v3

    if-eqz v3, :cond_4

    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/MethodNode;->isProtected()Z

    move-result v3

    if-nez v3, :cond_4

    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/MethodNode;->isPackageScope()Z

    move-result v3

    if-nez v3, :cond_4

    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/MethodNode;->isPrivate()Z

    move-result v3

    if-eqz v3, :cond_5

    .line 470
    :cond_4
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->isPrivate()Z

    move-result v3

    if-nez v3, :cond_1

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->isProtected()Z

    move-result v3

    if-nez v3, :cond_1

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->isPublic()Z

    move-result v3

    if-nez v3, :cond_1

    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/MethodNode;->isPublic()Z

    move-result v3

    if-nez v3, :cond_5

    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/MethodNode;->isProtected()Z

    move-result v3

    if-eqz v3, :cond_1

    .line 471
    :cond_5
    invoke-direct {p0, p2, p1, v0, v2}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->addWeakerAccessError(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/MethodNode;[Lorg/codehaus/groovy/ast/Parameter;Lorg/codehaus/groovy/ast/MethodNode;)V

    :cond_6
    return-void
.end method

.method private checkMethodModifiers(Lorg/codehaus/groovy/ast/MethodNode;)V
    .locals 2

    .line 455
    iget-object v0, p0, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->getModifiers()I

    move-result v0

    and-int/lit16 v0, v0, 0x200

    if-eqz v0, :cond_0

    .line 456
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getModifiers()I

    move-result v0

    invoke-static {v0}, Ljava/lang/reflect/Modifier;->isStrict(I)Z

    move-result v0

    const-string v1, "strictfp"

    invoke-direct {p0, p1, v0, v1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->checkMethodForModifier(Lorg/codehaus/groovy/ast/MethodNode;ZLjava/lang/String;)V

    .line 457
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getModifiers()I

    move-result v0

    invoke-static {v0}, Ljava/lang/reflect/Modifier;->isSynchronized(I)Z

    move-result v0

    const-string v1, "synchronized"

    invoke-direct {p0, p1, v0, v1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->checkMethodForModifier(Lorg/codehaus/groovy/ast/MethodNode;ZLjava/lang/String;)V

    .line 458
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getModifiers()I

    move-result v0

    invoke-static {v0}, Ljava/lang/reflect/Modifier;->isNative(I)Z

    move-result v0

    const-string v1, "native"

    invoke-direct {p0, p1, v0, v1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->checkMethodForModifier(Lorg/codehaus/groovy/ast/MethodNode;ZLjava/lang/String;)V

    :cond_0
    return-void
.end method

.method private checkMethodsForIncorrectModifiers(Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 6

    .line 346
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->isInterface()Z

    move-result v0

    if-nez v0, :cond_0

    return-void

    .line 347
    :cond_0
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getMethods()Ljava/util/List;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_1
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_3

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/ast/MethodNode;

    .line 348
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/MethodNode;->isFinal()Z

    move-result v2

    const-string v3, " from "

    const-string v4, "The "

    if-eqz v2, :cond_2

    .line 349
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-static {v1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->getDescription(Lorg/codehaus/groovy/ast/MethodNode;)Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v2, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-static {p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->getDescription(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v2, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    const-string v5, " must not be final. It is by definition abstract."

    invoke-virtual {v2, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {p0, v2, v1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 352
    :cond_2
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/MethodNode;->isStatic()Z

    move-result v2

    if-eqz v2, :cond_1

    invoke-static {v1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->isConstructor(Lorg/codehaus/groovy/ast/MethodNode;)Z

    move-result v2

    if-nez v2, :cond_1

    .line 353
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-static {v1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->getDescription(Lorg/codehaus/groovy/ast/MethodNode;)Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v2, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-static {p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->getDescription(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    const-string v3, " must not be static. Only fields may be static in an interface."

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {p0, v2, v1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    goto :goto_0

    :cond_3
    return-void
.end method

.method private checkMethodsForIncorrectName(Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 8

    .line 330
    iget-boolean v0, p0, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->strictNames:Z

    if-nez v0, :cond_0

    return-void

    .line 331
    :cond_0
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getAllDeclaredMethods()Ljava/util/List;

    move-result-object p1

    .line 332
    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_1
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_4

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/ast/MethodNode;

    .line 333
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/MethodNode;->getName()Ljava/lang/String;

    move-result-object v1

    const-string v2, "<init>"

    .line 334
    invoke-virtual {v1, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-nez v2, :cond_1

    const-string v2, "<clinit>"

    invoke-virtual {v1, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_2

    goto :goto_0

    .line 337
    :cond_2
    sget-object v2, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->INVALID_NAME_CHARS:[Ljava/lang/String;

    array-length v3, v2

    const/4 v4, 0x0

    :goto_1
    if-ge v4, v3, :cond_1

    aget-object v5, v2, v4

    .line 338
    invoke-virtual {v1, v5}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result v6

    if-eqz v6, :cond_3

    .line 339
    new-instance v6, Ljava/lang/StringBuilder;

    invoke-direct {v6}, Ljava/lang/StringBuilder;-><init>()V

    const-string v7, "You are not allowed to have \'"

    invoke-virtual {v6, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v6

    invoke-virtual {v6, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    const-string v6, "\' in a method name"

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {p0, v5, v0}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    :cond_3
    add-int/lit8 v4, v4, 0x1

    goto :goto_1

    :cond_4
    return-void
.end method

.method private checkMethodsForOverridingFinal(Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 6

    .line 370
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getMethods()Ljava/util/List;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_0
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_3

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/ast/MethodNode;

    .line 371
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/MethodNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v2

    .line 372
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getSuperClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v3

    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/MethodNode;->getName()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3, v4}, Lorg/codehaus/groovy/ast/ClassNode;->getMethods(Ljava/lang/String;)Ljava/util/List;

    move-result-object v3

    invoke-interface {v3}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v3

    :goto_1
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    move-result v4

    if-eqz v4, :cond_0

    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lorg/codehaus/groovy/ast/MethodNode;

    .line 373
    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/MethodNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v5

    .line 374
    invoke-static {v2, v5}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->hasEqualParameterTypes([Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/Parameter;)Z

    move-result v5

    if-nez v5, :cond_1

    goto :goto_1

    .line 375
    :cond_1
    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/MethodNode;->isFinal()Z

    move-result v3

    if-nez v3, :cond_2

    goto :goto_0

    .line 376
    :cond_2
    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/MethodNode;->getDeclaringClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    invoke-direct {p0, v1, v2, p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->addInvalidUseOfFinalError(Lorg/codehaus/groovy/ast/MethodNode;[Lorg/codehaus/groovy/ast/Parameter;Lorg/codehaus/groovy/ast/ClassNode;)V

    :cond_3
    return-void
.end method

.method private checkMethodsForWeakerAccess(Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 2

    .line 360
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getMethods()Ljava/util/List;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/ast/MethodNode;

    .line 361
    invoke-direct {p0, v1, p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->checkMethodForWeakerAccessPrivileges(Lorg/codehaus/groovy/ast/MethodNode;Lorg/codehaus/groovy/ast/ClassNode;)V

    goto :goto_0

    :cond_0
    return-void
.end method

.method private checkNoAbstractMethodsNonabstractClass(Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 5

    .line 200
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getModifiers()I

    move-result v0

    invoke-static {v0}, Ljava/lang/reflect/Modifier;->isAbstract(I)Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    .line 201
    :cond_0
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getAbstractMethods()Ljava/util/List;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_5

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/ast/MethodNode;

    .line 202
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/MethodNode;->getName()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/MethodNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v3

    invoke-virtual {p1, v2, v3}, Lorg/codehaus/groovy/ast/ClassNode;->getMethod(Ljava/lang/String;[Lorg/codehaus/groovy/ast/Parameter;)Lorg/codehaus/groovy/ast/MethodNode;

    move-result-object v2

    if-nez v2, :cond_1

    .line 204
    sget-object v2, Lorg/codehaus/groovy/ast/ClassHelper;->GROOVY_OBJECT_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/MethodNode;->getName()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/MethodNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v4

    invoke-virtual {v2, v3, v4}, Lorg/codehaus/groovy/ast/ClassNode;->getMethod(Ljava/lang/String;[Lorg/codehaus/groovy/ast/Parameter;)Lorg/codehaus/groovy/ast/MethodNode;

    move-result-object v2

    if-eqz v2, :cond_1

    .line 205
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/MethodNode;->isAbstract()Z

    move-result v3

    if-nez v3, :cond_1

    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/MethodNode;->getReturnType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v3

    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/MethodNode;->getReturnType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v4

    invoke-virtual {v3, v4}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_1

    return-void

    :cond_1
    if-eqz v2, :cond_4

    .line 210
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/MethodNode;->getReturnType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v3

    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/MethodNode;->getReturnType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v4

    invoke-virtual {v3, v4}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_2

    goto :goto_2

    .line 215
    :cond_2
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "Abstract "

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-static {v1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->getDescription(Lorg/codehaus/groovy/ast/MethodNode;)Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    const-string v4, " is not implemented but a method of the same name but different return type is defined: "

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    .line 217
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/MethodNode;->isStatic()Z

    move-result v4

    if-eqz v4, :cond_3

    const-string v4, "static "

    goto :goto_1

    :cond_3
    const-string v4, ""

    :goto_1
    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    .line 218
    invoke-static {v2}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->getDescription(Lorg/codehaus/groovy/ast/MethodNode;)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    .line 215
    invoke-virtual {p0, v2, v1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    goto/16 :goto_0

    .line 211
    :cond_4
    :goto_2
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "Can\'t have an abstract method in a non-abstract class. The "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    .line 212
    invoke-static {p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->getDescription(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    const-string v3, " must be declared abstract or the "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    .line 213
    invoke-static {v1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->getDescription(Lorg/codehaus/groovy/ast/MethodNode;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v2, " must be implemented."

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    .line 211
    invoke-virtual {p0, v1, p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    goto/16 :goto_0

    :cond_5
    return-void
.end method

.method private checkNoStaticMethodWithSameSignatureAsNonStatic(Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 8

    .line 128
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getSuperClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 132
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->getDeclaredMethodsMap()Ljava/util/Map;

    move-result-object v0

    goto :goto_0

    .line 134
    :cond_0
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    .line 137
    :goto_0
    invoke-static {p1, v0}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->addDeclaredMethodsFromInterfaces(Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/Map;)V

    .line 138
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getMethods()Ljava/util/List;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_1
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_8

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lorg/codehaus/groovy/ast/MethodNode;

    .line 139
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/MethodNode;->getTypeDescriptor()Ljava/lang/String;

    move-result-object v3

    invoke-interface {v0, v3}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lorg/codehaus/groovy/ast/MethodNode;

    if-eqz v3, :cond_7

    .line 140
    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/MethodNode;->isStatic()Z

    move-result v4

    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/MethodNode;->isStatic()Z

    move-result v5

    xor-int/2addr v4, v5

    if-eqz v4, :cond_7

    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/MethodNode;->isStaticConstructor()Z

    move-result v4

    if-nez v4, :cond_7

    .line 141
    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/MethodNode;->isAbstract()Z

    move-result v4

    if-nez v4, :cond_1

    goto :goto_1

    .line 142
    :cond_1
    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/MethodNode;->getDeclaringClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v4

    .line 143
    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/ClassNode;->getOuterClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v5

    if-nez v5, :cond_2

    .line 144
    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/ClassNode;->isResolved()Z

    move-result v6

    if-eqz v6, :cond_2

    .line 146
    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/ClassNode;->getTypeClass()Ljava/lang/Class;

    move-result-object v4

    .line 147
    invoke-virtual {v4}, Ljava/lang/Class;->getEnclosingClass()Ljava/lang/Class;

    move-result-object v4

    if-eqz v4, :cond_2

    .line 149
    invoke-static {v4}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v5

    .line 152
    :cond_2
    invoke-static {v5}, Lorg/codehaus/groovy/transform/trait/Traits;->isTrait(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v4

    if-nez v4, :cond_7

    .line 154
    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/MethodNode;->getName()Ljava/lang/String;

    move-result-object v3

    .line 155
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/ASTNode;->getLineNumber()I

    move-result v4

    const/4 v5, -0x1

    if-ne v4, v5, :cond_6

    .line 157
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getProperties()Ljava/util/List;

    move-result-object v4

    invoke-interface {v4}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v4

    :cond_3
    invoke-interface {v4}, Ljava/util/Iterator;->hasNext()Z

    move-result v5

    if-eqz v5, :cond_6

    invoke-interface {v4}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lorg/codehaus/groovy/ast/PropertyNode;

    const-string v6, "set"

    .line 158
    invoke-virtual {v3, v6}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v6

    const-string v7, "is"

    if-nez v6, :cond_4

    const-string v6, "get"

    invoke-virtual {v3, v6}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v6

    if-nez v6, :cond_4

    invoke-virtual {v3, v7}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v6

    if-eqz v6, :cond_3

    .line 159
    :cond_4
    invoke-virtual {v5}, Lorg/codehaus/groovy/ast/PropertyNode;->getField()Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object v6

    invoke-virtual {v6}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object v6

    invoke-static {v6}, Lorg/codehaus/groovy/classgen/Verifier;->capitalize(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v6

    .line 160
    invoke-virtual {v3, v7}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v7

    if-eqz v7, :cond_5

    const/4 v7, 0x2

    goto :goto_2

    :cond_5
    const/4 v7, 0x3

    :goto_2
    invoke-virtual {v3, v7}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object v7

    .line 161
    invoke-virtual {v6, v7}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v6

    if-eqz v6, :cond_3

    goto :goto_3

    :cond_6
    move-object v5, v2

    .line 168
    :goto_3
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "The "

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-static {v2}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->getDescription(Lorg/codehaus/groovy/ast/MethodNode;)Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    const-string v4, " is already defined in "

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-static {p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->getDescription(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    const-string v4, ". You cannot have both a static and an instance method with the same signature"

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {p0, v3, v5}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 172
    :cond_7
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/MethodNode;->getTypeDescriptor()Ljava/lang/String;

    move-result-object v3

    invoke-interface {v0, v3, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto/16 :goto_1

    :cond_8
    return-void
.end method

.method private checkOverloadingPrivateAndPublic(Lorg/codehaus/groovy/ast/MethodNode;)V
    .locals 6

    .line 478
    invoke-static {p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->isConstructor(Lorg/codehaus/groovy/ast/MethodNode;)Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    .line 479
    :cond_0
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->isPrivate()Z

    move-result v0

    .line 480
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->isPublic()Z

    move-result v1

    .line 481
    iget-object v2, p0, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getName()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Lorg/codehaus/groovy/ast/ClassNode;->getMethods(Ljava/lang/String;)Ljava/util/List;

    move-result-object v2

    invoke-interface {v2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :cond_1
    :goto_0
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_6

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lorg/codehaus/groovy/ast/MethodNode;

    if-ne v3, p1, :cond_2

    goto :goto_0

    .line 483
    :cond_2
    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/MethodNode;->getDeclaringClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v4

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getDeclaringClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v5

    invoke-virtual {v4, v5}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result v4

    if-nez v4, :cond_3

    goto :goto_0

    .line 484
    :cond_3
    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/MethodNode;->isPublic()Z

    move-result v4

    const/4 v5, 0x1

    if-nez v4, :cond_5

    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/MethodNode;->isProtected()Z

    move-result v3

    if-eqz v3, :cond_4

    goto :goto_1

    :cond_4
    move v0, v5

    goto :goto_2

    :cond_5
    :goto_1
    move v1, v5

    :goto_2
    if-eqz v0, :cond_1

    if-eqz v1, :cond_1

    :cond_6
    if-eqz v0, :cond_7

    if-eqz v1, :cond_7

    const-string v0, "Mixing private and public/protected methods of the same name causes multimethods to be disabled and is forbidden to avoid surprising behaviour. Renaming the private methods will solve the problem."

    .line 492
    invoke-virtual {p0, v0, p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    :cond_7
    return-void
.end method

.method private checkRepetitiveMethod(Lorg/codehaus/groovy/ast/MethodNode;)V
    .locals 6

    .line 497
    invoke-static {p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->isConstructor(Lorg/codehaus/groovy/ast/MethodNode;)Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    .line 498
    :cond_0
    iget-object v0, p0, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Lorg/codehaus/groovy/ast/ClassNode;->getMethods(Ljava/lang/String;)Ljava/util/List;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_4

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/ast/MethodNode;

    if-ne v1, p1, :cond_1

    goto :goto_0

    .line 500
    :cond_1
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/MethodNode;->getDeclaringClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v2

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getDeclaringClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v3

    invoke-virtual {v2, v3}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-nez v2, :cond_2

    goto :goto_0

    .line 501
    :cond_2
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v2

    .line 502
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/MethodNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v3

    .line 503
    array-length v4, v2

    array-length v5, v3

    if-eq v4, v5, :cond_3

    goto :goto_0

    .line 504
    :cond_3
    invoke-direct {p0, v3, v2, p1, v1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->addErrorIfParamsAndReturnTypeEqual([Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/Parameter;Lorg/codehaus/groovy/ast/MethodNode;Lorg/codehaus/groovy/ast/MethodNode;)V

    goto :goto_0

    :cond_4
    return-void
.end method

.method private checkStringExceedingMaximumLength(Lorg/codehaus/groovy/ast/expr/ConstantExpression;)V
    .locals 3

    .line 700
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/ConstantExpression;->getValue()Ljava/lang/Object;

    move-result-object v0

    .line 701
    instance-of v1, v0, Ljava/lang/String;

    if-eqz v1, :cond_0

    .line 702
    check-cast v0, Ljava/lang/String;

    .line 703
    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    const v2, 0xffff

    if-le v1, v2, :cond_0

    .line 704
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "String too long. The given string is "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, " Unicode code units long, but only a maximum of 65535 is allowed."

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0, p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    :cond_0
    return-void
.end method

.method private checkSuperOrThisOnLHS(Lorg/codehaus/groovy/ast/expr/Expression;)V
    .locals 2

    .line 589
    instance-of v0, p1, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    if-nez v0, :cond_0

    return-void

    .line 590
    :cond_0
    move-object v0, p1

    check-cast v0, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    .line 591
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->isThisExpression()Z

    move-result v1

    if-eqz v1, :cond_1

    const-string v0, "cannot have \'this\' as LHS of an assignment"

    .line 592
    invoke-virtual {p0, v0, p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    goto :goto_0

    .line 593
    :cond_1
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->isSuperExpression()Z

    move-result v0

    if-eqz v0, :cond_2

    const-string v0, "cannot have \'super\' as LHS of an assignment"

    .line 594
    invoke-virtual {p0, v0, p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    :cond_2
    :goto_0
    return-void
.end method

.method private static getDescription(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/lang/String;
    .locals 2

    .line 285
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/ClassNode;->isInterface()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-static {p0}, Lorg/codehaus/groovy/transform/trait/Traits;->isTrait(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v1

    if-eqz v1, :cond_0

    const-string v1, "trait"

    goto :goto_0

    :cond_0
    const-string v1, "interface"

    goto :goto_0

    :cond_1
    const-string v1, "class"

    :goto_0
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, " \'"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object p0

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    const-string v0, "\'"

    invoke-virtual {p0, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method private static getDescription(Lorg/codehaus/groovy/ast/FieldNode;)Ljava/lang/String;
    .locals 2

    .line 293
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "field \'"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object p0

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    const-string v0, "\'"

    invoke-virtual {p0, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method private static getDescription(Lorg/codehaus/groovy/ast/MethodNode;)Ljava/lang/String;
    .locals 2

    .line 289
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "method \'"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/MethodNode;->getTypeDescriptor()Ljava/lang/String;

    move-result-object p0

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    const-string v0, "\'"

    invoke-virtual {p0, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method private static getDescription(Lorg/codehaus/groovy/ast/Parameter;)Ljava/lang/String;
    .locals 2

    .line 297
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "parameter \'"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/Parameter;->getName()Ljava/lang/String;

    move-result-object p0

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    const-string v0, "\'"

    invoke-virtual {p0, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method private static getRefDescriptor(Lorg/codehaus/groovy/ast/ASTNode;)Ljava/lang/String;
    .locals 3

    .line 740
    instance-of v0, p0, Lorg/codehaus/groovy/ast/FieldNode;

    const-string v1, " "

    if-eqz v0, :cond_0

    .line 741
    check-cast p0, Lorg/codehaus/groovy/ast/FieldNode;

    .line 742
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "the field "

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object p0

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    return-object p0

    .line 743
    :cond_0
    instance-of v0, p0, Lorg/codehaus/groovy/ast/PropertyNode;

    if-eqz v0, :cond_1

    .line 744
    check-cast p0, Lorg/codehaus/groovy/ast/PropertyNode;

    .line 745
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "the property "

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/PropertyNode;->getName()Ljava/lang/String;

    move-result-object p0

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    return-object p0

    .line 746
    :cond_1
    instance-of v0, p0, Lorg/codehaus/groovy/ast/ConstructorNode;

    if-eqz v0, :cond_2

    .line 747
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "the constructor "

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/ASTNode;->getText()Ljava/lang/String;

    move-result-object p0

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    return-object p0

    .line 748
    :cond_2
    instance-of v0, p0, Lorg/codehaus/groovy/ast/MethodNode;

    if-eqz v0, :cond_3

    .line 749
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "the method "

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/ASTNode;->getText()Ljava/lang/String;

    move-result-object p0

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    return-object p0

    .line 750
    :cond_3
    instance-of v0, p0, Lorg/codehaus/groovy/ast/ClassNode;

    if-eqz v0, :cond_4

    .line 751
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "the super class "

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    return-object p0

    .line 753
    :cond_4
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "<unknown with class "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object p0

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p0

    const-string v0, "> "

    invoke-virtual {p0, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method private static hasEqualParameterTypes([Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/Parameter;)Z
    .locals 4

    .line 421
    array-length v0, p0

    array-length v1, p1

    const/4 v2, 0x0

    if-eq v0, v1, :cond_0

    return v2

    :cond_0
    move v0, v2

    .line 422
    :goto_0
    array-length v1, p0

    if-ge v0, v1, :cond_2

    .line 423
    aget-object v1, p0, v0

    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/Parameter;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v1

    .line 424
    aget-object v3, p1, v0

    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/Parameter;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v3

    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v3

    .line 425
    invoke-virtual {v1, v3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_1

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_1
    return v2

    :cond_2
    const/4 p0, 0x1

    return p0
.end method

.method private static isConstructor(Lorg/codehaus/groovy/ast/MethodNode;)Z
    .locals 1

    .line 366
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/MethodNode;->getName()Ljava/lang/String;

    move-result-object p0

    const-string v0, "<clinit>"

    invoke-virtual {p0, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p0

    return p0
.end method


# virtual methods
.method public getClassNode()Lorg/codehaus/groovy/ast/ClassNode;
    .locals 1

    .line 101
    iget-object v0, p0, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    return-object v0
.end method

.method protected getSourceUnit()Lorg/codehaus/groovy/control/SourceUnit;
    .locals 1

    .line 432
    iget-object v0, p0, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->source:Lorg/codehaus/groovy/control/SourceUnit;

    return-object v0
.end method

.method public visitBinaryExpression(Lorg/codehaus/groovy/ast/expr/BinaryExpression;)V
    .locals 2

    .line 574
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/BinaryExpression;->getOperation()Lorg/codehaus/groovy/syntax/Token;

    move-result-object v0

    invoke-virtual {v0}, Lorg/codehaus/groovy/syntax/Token;->getType()I

    move-result v0

    const/16 v1, 0x1e

    if-ne v0, v1, :cond_0

    .line 575
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/BinaryExpression;->getRightExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    instance-of v0, v0, Lorg/codehaus/groovy/ast/expr/MapEntryExpression;

    if-eqz v0, :cond_0

    .line 578
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/BinaryExpression;->getRightExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    const-string v1, "You tried to use a map entry for an index operation, this is not allowed. Maybe something should be set in parentheses or a comma is missing?"

    .line 576
    invoke-virtual {p0, v1, v0}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 580
    :cond_0
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;->visitBinaryExpression(Lorg/codehaus/groovy/ast/expr/BinaryExpression;)V

    .line 582
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/BinaryExpression;->getOperation()Lorg/codehaus/groovy/syntax/Token;

    move-result-object v0

    invoke-virtual {v0}, Lorg/codehaus/groovy/syntax/Token;->getType()I

    move-result v0

    invoke-static {v0}, Lorg/codehaus/groovy/syntax/Types;->isAssignment(I)Z

    move-result v0

    if-eqz v0, :cond_1

    .line 583
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/BinaryExpression;->getLeftExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    invoke-direct {p0, v0}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->checkFinalFieldAccess(Lorg/codehaus/groovy/ast/expr/Expression;)V

    .line 584
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/BinaryExpression;->getLeftExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    invoke-direct {p0, p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->checkSuperOrThisOnLHS(Lorg/codehaus/groovy/ast/expr/Expression;)V

    :cond_1
    return-void
.end method

.method public visitCatchStatement(Lorg/codehaus/groovy/ast/stmt/CatchStatement;)V
    .locals 2

    .line 638
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/stmt/CatchStatement;->getExceptionType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    const-class v1, Ljava/lang/Throwable;

    invoke-static {v1}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    invoke-virtual {v0, v1}, Lorg/codehaus/groovy/ast/ClassNode;->isDerivedFrom(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v0

    if-nez v0, :cond_0

    const-string v0, "Catch statement parameter type is not a subclass of Throwable."

    .line 639
    invoke-virtual {p0, v0, p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 641
    :cond_0
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;->visitCatchStatement(Lorg/codehaus/groovy/ast/stmt/CatchStatement;)V

    return-void
.end method

.method public visitClass(Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 2

    .line 105
    iget-object v0, p0, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    .line 106
    iput-object p1, p0, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    .line 107
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->checkImplementsAndExtends(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 108
    iget-object v1, p0, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->source:Lorg/codehaus/groovy/control/SourceUnit;

    if-eqz v1, :cond_0

    invoke-virtual {v1}, Lorg/codehaus/groovy/control/SourceUnit;->getErrorCollector()Lorg/codehaus/groovy/control/ErrorCollector;

    move-result-object v1

    invoke-virtual {v1}, Lorg/codehaus/groovy/control/ErrorCollector;->hasErrors()Z

    move-result v1

    if-nez v1, :cond_0

    .line 109
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->checkClassForIncorrectModifiers(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 110
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->checkInterfaceMethodVisibility(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 111
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->checkAbstractMethodVisibility(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 112
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->checkClassForOverwritingFinal(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 113
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->checkMethodsForIncorrectModifiers(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 114
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->checkMethodsForIncorrectName(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 115
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->checkMethodsForWeakerAccess(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 116
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->checkMethodsForOverridingFinal(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 117
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->checkNoAbstractMethodsNonabstractClass(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 118
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->checkClassExtendsAllSelfTypes(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 119
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->checkNoStaticMethodWithSameSignatureAsNonStatic(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 120
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getUnresolvedInterfaces()[Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    invoke-direct {p0, p1, v1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->checkGenericsUsage(Lorg/codehaus/groovy/ast/ASTNode;[Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 121
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getUnresolvedSuperClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    invoke-direct {p0, p1, v1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->checkGenericsUsage(Lorg/codehaus/groovy/ast/ASTNode;Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 123
    :cond_0
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;->visitClass(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 124
    iput-object v0, p0, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    return-void
.end method

.method public visitConstantExpression(Lorg/codehaus/groovy/ast/expr/ConstantExpression;)V
    .locals 0

    .line 688
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;->visitConstantExpression(Lorg/codehaus/groovy/ast/expr/ConstantExpression;)V

    .line 689
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->checkStringExceedingMaximumLength(Lorg/codehaus/groovy/ast/expr/ConstantExpression;)V

    return-void
.end method

.method public visitConstructor(Lorg/codehaus/groovy/ast/ConstructorNode;)V
    .locals 1

    const/4 v0, 0x1

    .line 631
    iput-boolean v0, p0, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->inConstructor:Z

    .line 632
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ConstructorNode;->isStaticConstructor()Z

    move-result v0

    iput-boolean v0, p0, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->inStaticConstructor:Z

    .line 633
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ConstructorNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v0

    invoke-direct {p0, p1, v0}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->checkGenericsUsage(Lorg/codehaus/groovy/ast/ASTNode;[Lorg/codehaus/groovy/ast/Parameter;)V

    .line 634
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;->visitConstructor(Lorg/codehaus/groovy/ast/ConstructorNode;)V

    return-void
.end method

.method public visitDeclarationExpression(Lorg/codehaus/groovy/ast/expr/DeclarationExpression;)V
    .locals 2

    .line 659
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;->visitDeclarationExpression(Lorg/codehaus/groovy/ast/expr/DeclarationExpression;)V

    .line 660
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/DeclarationExpression;->isMultipleAssignmentDeclaration()Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    const/16 v0, 0x400

    const-string v1, "abstract"

    .line 661
    invoke-direct {p0, p1, v0, v1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->checkInvalidDeclarationModifier(Lorg/codehaus/groovy/ast/expr/DeclarationExpression;ILjava/lang/String;)V

    const/16 v0, 0x100

    const-string v1, "native"

    .line 662
    invoke-direct {p0, p1, v0, v1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->checkInvalidDeclarationModifier(Lorg/codehaus/groovy/ast/expr/DeclarationExpression;ILjava/lang/String;)V

    const/4 v0, 0x2

    const-string v1, "private"

    .line 663
    invoke-direct {p0, p1, v0, v1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->checkInvalidDeclarationModifier(Lorg/codehaus/groovy/ast/expr/DeclarationExpression;ILjava/lang/String;)V

    const/4 v0, 0x4

    const-string v1, "protected"

    .line 664
    invoke-direct {p0, p1, v0, v1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->checkInvalidDeclarationModifier(Lorg/codehaus/groovy/ast/expr/DeclarationExpression;ILjava/lang/String;)V

    const/4 v0, 0x1

    const-string v1, "public"

    .line 665
    invoke-direct {p0, p1, v0, v1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->checkInvalidDeclarationModifier(Lorg/codehaus/groovy/ast/expr/DeclarationExpression;ILjava/lang/String;)V

    const/16 v0, 0x8

    const-string v1, "static"

    .line 666
    invoke-direct {p0, p1, v0, v1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->checkInvalidDeclarationModifier(Lorg/codehaus/groovy/ast/expr/DeclarationExpression;ILjava/lang/String;)V

    const/16 v0, 0x800

    const-string v1, "strictfp"

    .line 667
    invoke-direct {p0, p1, v0, v1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->checkInvalidDeclarationModifier(Lorg/codehaus/groovy/ast/expr/DeclarationExpression;ILjava/lang/String;)V

    const/16 v0, 0x20

    const-string v1, "synchronized"

    .line 668
    invoke-direct {p0, p1, v0, v1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->checkInvalidDeclarationModifier(Lorg/codehaus/groovy/ast/expr/DeclarationExpression;ILjava/lang/String;)V

    const/16 v0, 0x80

    const-string v1, "transient"

    .line 669
    invoke-direct {p0, p1, v0, v1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->checkInvalidDeclarationModifier(Lorg/codehaus/groovy/ast/expr/DeclarationExpression;ILjava/lang/String;)V

    const/16 v0, 0x40

    const-string v1, "volatile"

    .line 670
    invoke-direct {p0, p1, v0, v1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->checkInvalidDeclarationModifier(Lorg/codehaus/groovy/ast/expr/DeclarationExpression;ILjava/lang/String;)V

    .line 671
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/DeclarationExpression;->getVariableExpression()Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v0

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->getOriginType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    sget-object v1, Lorg/codehaus/groovy/ast/ClassHelper;->VOID_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v0, v1}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_1

    .line 672
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "The variable \'"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/DeclarationExpression;->getVariableExpression()Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v1

    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "\' has invalid type void"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0, p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    :cond_1
    return-void
.end method

.method public visitField(Lorg/codehaus/groovy/ast/FieldNode;)V
    .locals 3

    .line 523
    iget-object v0, p0, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Lorg/codehaus/groovy/ast/ClassNode;->getDeclaredField(Ljava/lang/String;)Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object v0

    const-string v1, "The "

    if-eq v0, p1, :cond_0

    .line 524
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-static {p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->getDescription(Lorg/codehaus/groovy/ast/FieldNode;)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v2, " is declared multiple times."

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0, p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 526
    :cond_0
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->checkInterfaceFieldModifiers(Lorg/codehaus/groovy/ast/FieldNode;)V

    .line 527
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->checkInvalidFieldModifiers(Lorg/codehaus/groovy/ast/FieldNode;)V

    .line 528
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/FieldNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    invoke-direct {p0, p1, v0}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->checkGenericsUsage(Lorg/codehaus/groovy/ast/ASTNode;Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 529
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/FieldNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    sget-object v2, Lorg/codehaus/groovy/ast/ClassHelper;->VOID_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v0, v2}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_1

    .line 530
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-static {p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->getDescription(Lorg/codehaus/groovy/ast/FieldNode;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, " has invalid type void"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0, p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 532
    :cond_1
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;->visitField(Lorg/codehaus/groovy/ast/FieldNode;)V

    return-void
.end method

.method public visitGStringExpression(Lorg/codehaus/groovy/ast/expr/GStringExpression;)V
    .locals 1

    .line 693
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;->visitGStringExpression(Lorg/codehaus/groovy/ast/expr/GStringExpression;)V

    .line 694
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/GStringExpression;->getStrings()Ljava/util/List;

    move-result-object p1

    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    .line 695
    invoke-direct {p0, v0}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->checkStringExceedingMaximumLength(Lorg/codehaus/groovy/ast/expr/ConstantExpression;)V

    goto :goto_0

    :cond_0
    return-void
.end method

.method public visitMethod(Lorg/codehaus/groovy/ast/MethodNode;)V
    .locals 6

    const/4 v0, 0x0

    .line 436
    iput-boolean v0, p0, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->inConstructor:Z

    .line 437
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->isStaticConstructor()Z

    move-result v1

    iput-boolean v1, p0, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->inStaticConstructor:Z

    .line 438
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->checkAbstractDeclaration(Lorg/codehaus/groovy/ast/MethodNode;)V

    .line 439
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->checkRepetitiveMethod(Lorg/codehaus/groovy/ast/MethodNode;)V

    .line 440
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->checkOverloadingPrivateAndPublic(Lorg/codehaus/groovy/ast/MethodNode;)V

    .line 441
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->checkMethodModifiers(Lorg/codehaus/groovy/ast/MethodNode;)V

    .line 442
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v1

    invoke-direct {p0, p1, v1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->checkGenericsUsage(Lorg/codehaus/groovy/ast/ASTNode;[Lorg/codehaus/groovy/ast/Parameter;)V

    .line 443
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getReturnType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    invoke-direct {p0, p1, v1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->checkGenericsUsage(Lorg/codehaus/groovy/ast/ASTNode;Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 444
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v1

    array-length v2, v1

    :goto_0
    if-ge v0, v2, :cond_1

    aget-object v3, v1, v0

    .line 445
    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/Parameter;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v4

    sget-object v5, Lorg/codehaus/groovy/ast/ClassHelper;->VOID_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v4, v5}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result v4

    if-eqz v4, :cond_0

    .line 446
    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    const-string v5, "The "

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-static {v3}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->getDescription(Lorg/codehaus/groovy/ast/Parameter;)Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    const-string v5, " in "

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-static {p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->getDescription(Lorg/codehaus/groovy/ast/MethodNode;)Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    const-string v5, " has invalid type void"

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {p0, v4, v3}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    :cond_0
    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    .line 449
    :cond_1
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;->visitMethod(Lorg/codehaus/groovy/ast/MethodNode;)V

    return-void
.end method

.method public visitMethodCallExpression(Lorg/codehaus/groovy/ast/expr/MethodCallExpression;)V
    .locals 1

    .line 645
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;->visitMethodCallExpression(Lorg/codehaus/groovy/ast/expr/MethodCallExpression;)V

    .line 646
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->getArguments()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    .line 647
    instance-of v0, p1, Lorg/codehaus/groovy/ast/expr/TupleExpression;

    if-eqz v0, :cond_0

    .line 648
    check-cast p1, Lorg/codehaus/groovy/ast/expr/TupleExpression;

    .line 649
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/TupleExpression;->getExpressions()Ljava/util/List;

    move-result-object p1

    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_1

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/ast/expr/Expression;

    .line 650
    invoke-direct {p0, v0}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->checkForInvalidDeclaration(Lorg/codehaus/groovy/ast/expr/Expression;)V

    goto :goto_0

    .line 653
    :cond_0
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->checkForInvalidDeclaration(Lorg/codehaus/groovy/ast/expr/Expression;)V

    :cond_1
    return-void
.end method

.method public visitProperty(Lorg/codehaus/groovy/ast/PropertyNode;)V
    .locals 1

    .line 536
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->checkDuplicateProperties(Lorg/codehaus/groovy/ast/PropertyNode;)V

    .line 537
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/PropertyNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    invoke-direct {p0, p1, v0}, Lorg/codehaus/groovy/classgen/ClassCompletionVerifier;->checkGenericsUsage(Lorg/codehaus/groovy/ast/ASTNode;Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 538
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;->visitProperty(Lorg/codehaus/groovy/ast/PropertyNode;)V

    return-void
.end method
