.class public final synthetic Lorg/codehaus/groovy/classgen/InnerClassCompletionVisitor$$ExternalSyntheticLambda0;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/BiConsumer;


# instance fields
.field public final synthetic f$0:Lorg/codehaus/groovy/ast/ClassNode;


# direct methods
.method public synthetic constructor <init>(Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/codehaus/groovy/classgen/InnerClassCompletionVisitor$$ExternalSyntheticLambda0;->f$0:Lorg/codehaus/groovy/ast/ClassNode;

    return-void
.end method


# virtual methods
.method public final accept(Ljava/lang/Object;Ljava/lang/Object;)V
    .locals 1

    iget-object v0, p0, Lorg/codehaus/groovy/classgen/InnerClassCompletionVisitor$$ExternalSyntheticLambda0;->f$0:Lorg/codehaus/groovy/ast/ClassNode;

    check-cast p1, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    check-cast p2, [Lorg/codehaus/groovy/ast/Parameter;

    invoke-static {v0, p1, p2}, Lorg/codehaus/groovy/classgen/InnerClassCompletionVisitor;->lambda$addMopMethods$1(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/BlockStatement;[Lorg/codehaus/groovy/ast/Parameter;)V

    return-void
.end method
