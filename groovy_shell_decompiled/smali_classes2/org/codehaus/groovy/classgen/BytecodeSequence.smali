.class public Lorg/codehaus/groovy/classgen/BytecodeSequence;
.super Lorg/codehaus/groovy/ast/stmt/Statement;
.source "BytecodeSequence.java"


# instance fields
.field private final instructions:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "*>;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Ljava/util/List;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "*>;)V"
        }
    .end annotation

    .line 40
    invoke-direct {p0}, Lorg/codehaus/groovy/ast/stmt/Statement;-><init>()V

    .line 41
    invoke-static {p1}, Ljava/util/Objects;->requireNonNull(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/util/List;

    iput-object p1, p0, Lorg/codehaus/groovy/classgen/BytecodeSequence;->instructions:Ljava/util/List;

    return-void
.end method

.method public constructor <init>(Lorg/codehaus/groovy/classgen/BytecodeInstruction;)V
    .locals 0

    .line 36
    invoke-direct {p0}, Lorg/codehaus/groovy/ast/stmt/Statement;-><init>()V

    .line 37
    invoke-static {p1}, Ljava/util/Collections;->singletonList(Ljava/lang/Object;)Ljava/util/List;

    move-result-object p1

    iput-object p1, p0, Lorg/codehaus/groovy/classgen/BytecodeSequence;->instructions:Ljava/util/List;

    return-void
.end method


# virtual methods
.method public getBytecodeInstruction()Lorg/codehaus/groovy/classgen/BytecodeInstruction;
    .locals 2

    .line 56
    iget-object v0, p0, Lorg/codehaus/groovy/classgen/BytecodeSequence;->instructions:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    const/4 v1, 0x1

    if-ne v0, v1, :cond_0

    .line 57
    iget-object v0, p0, Lorg/codehaus/groovy/classgen/BytecodeSequence;->instructions:Ljava/util/List;

    const/4 v1, 0x0

    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    .line 58
    instance-of v1, v0, Lorg/codehaus/groovy/classgen/BytecodeInstruction;

    if-eqz v1, :cond_0

    .line 59
    check-cast v0, Lorg/codehaus/groovy/classgen/BytecodeInstruction;

    return-object v0

    :cond_0
    const/4 v0, 0x0

    return-object v0
.end method

.method public getInstructions()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "*>;"
        }
    .end annotation

    .line 45
    iget-object v0, p0, Lorg/codehaus/groovy/classgen/BytecodeSequence;->instructions:Ljava/util/List;

    invoke-static {v0}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v0

    return-object v0
.end method

.method public visit(Lorg/codehaus/groovy/ast/GroovyCodeVisitor;)V
    .locals 3

    .line 78
    instance-of v0, p1, Lorg/codehaus/groovy/classgen/ClassGenerator;

    if-eqz v0, :cond_0

    .line 79
    check-cast p1, Lorg/codehaus/groovy/classgen/ClassGenerator;

    invoke-virtual {p1, p0}, Lorg/codehaus/groovy/classgen/ClassGenerator;->visitBytecodeSequence(Lorg/codehaus/groovy/classgen/BytecodeSequence;)V

    goto :goto_1

    .line 81
    :cond_0
    iget-object v0, p0, Lorg/codehaus/groovy/classgen/BytecodeSequence;->instructions:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_1
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_2

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    .line 82
    instance-of v2, v1, Lorg/codehaus/groovy/ast/ASTNode;

    if-eqz v2, :cond_1

    .line 83
    check-cast v1, Lorg/codehaus/groovy/ast/ASTNode;

    invoke-virtual {v1, p1}, Lorg/codehaus/groovy/ast/ASTNode;->visit(Lorg/codehaus/groovy/ast/GroovyCodeVisitor;)V

    goto :goto_0

    :cond_2
    :goto_1
    return-void
.end method
