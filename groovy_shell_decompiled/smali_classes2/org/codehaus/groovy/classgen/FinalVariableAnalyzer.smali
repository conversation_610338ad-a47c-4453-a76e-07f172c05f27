.class public Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;
.super Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;
.source "FinalVariableAnalyzer.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableNotFinalCallback;,
        Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$StateMap;,
        Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;
    }
.end annotation


# instance fields
.field private final assignmentTracker:Ljava/util/Deque;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Deque<",
            "Ljava/util/Map<",
            "Lorg/codehaus/groovy/ast/Variable;",
            "Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;",
            ">;>;"
        }
    .end annotation
.end field

.field private final callback:Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableNotFinalCallback;

.field private declaredFinalVariables:Ljava/util/Set;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Set<",
            "Lorg/codehaus/groovy/ast/Variable;",
            ">;"
        }
    .end annotation
.end field

.field private inArgumentList:Z

.field private inAssignmentRHS:Z

.field private final sourceUnit:Lorg/codehaus/groovy/control/SourceUnit;


# direct methods
.method public constructor <init>(Lorg/codehaus/groovy/control/SourceUnit;)V
    .locals 1

    const/4 v0, 0x0

    .line 99
    invoke-direct {p0, p1, v0}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;-><init>(Lorg/codehaus/groovy/control/SourceUnit;Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableNotFinalCallback;)V

    return-void
.end method

.method public constructor <init>(Lorg/codehaus/groovy/control/SourceUnit;Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableNotFinalCallback;)V
    .locals 2

    .line 102
    invoke-direct {p0}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;-><init>()V

    const/4 v0, 0x0

    .line 66
    iput-object v0, p0, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->declaredFinalVariables:Ljava/util/Set;

    const/4 v1, 0x0

    .line 67
    iput-boolean v1, p0, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->inAssignmentRHS:Z

    .line 68
    iput-boolean v1, p0, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->inArgumentList:Z

    .line 96
    new-instance v1, Ljava/util/LinkedList;

    invoke-direct {v1}, Ljava/util/LinkedList;-><init>()V

    iput-object v1, p0, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->assignmentTracker:Ljava/util/Deque;

    .line 103
    iput-object p2, p0, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->callback:Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableNotFinalCallback;

    .line 104
    iput-object p1, p0, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->sourceUnit:Lorg/codehaus/groovy/control/SourceUnit;

    .line 105
    new-instance p1, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$StateMap;

    invoke-direct {p1, v0}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$StateMap;-><init>(Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$1;)V

    invoke-interface {v1, p1}, Ljava/util/Deque;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method static synthetic access$100(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/Variable;
    .locals 0

    .line 61
    invoke-static {p0}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->getTarget(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/Variable;

    move-result-object p0

    return-object p0
.end method

.method private checkPrePostfixOperation(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)V
    .locals 7

    .line 248
    instance-of v0, p1, Lorg/codehaus/groovy/ast/Variable;

    if-eqz v0, :cond_0

    .line 249
    move-object v2, p1

    check-cast v2, Lorg/codehaus/groovy/ast/Variable;

    const/4 v3, 0x0

    const/4 v4, 0x0

    const/4 v5, 0x1

    move-object v1, p0

    move-object v6, p2

    invoke-direct/range {v1 .. v6}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->recordAssignment(Lorg/codehaus/groovy/ast/Variable;ZZZLorg/codehaus/groovy/ast/expr/Expression;)V

    .line 250
    instance-of v0, p1, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    if-eqz v0, :cond_0

    .line 251
    move-object v0, p1

    check-cast v0, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->getAccessedVariable()Lorg/codehaus/groovy/ast/Variable;

    move-result-object v2

    if-eq v2, p1, :cond_0

    const/4 v3, 0x0

    const/4 v4, 0x0

    const/4 v5, 0x1

    move-object v1, p0

    move-object v6, p2

    .line 253
    invoke-direct/range {v1 .. v6}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->recordAssignment(Lorg/codehaus/groovy/ast/Variable;ZZZLorg/codehaus/groovy/ast/expr/Expression;)V

    :cond_0
    return-void
.end method

.method private cleanLocalVars(Ljava/util/Map;Ljava/util/Map;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Lorg/codehaus/groovy/ast/Variable;",
            "Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;",
            ">;",
            "Ljava/util/Map<",
            "Lorg/codehaus/groovy/ast/Variable;",
            "Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;",
            ">;)V"
        }
    .end annotation

    .line 221
    invoke-interface {p2}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    move-result-object p2

    invoke-interface {p2}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object p2

    :cond_0
    :goto_0
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_1

    .line 222
    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/Map$Entry;

    .line 223
    invoke-interface {v0}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/ast/Variable;

    .line 224
    instance-of v1, v0, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    if-eqz v1, :cond_0

    move-object v1, v0

    check-cast v1, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->getAccessedVariable()Lorg/codehaus/groovy/ast/Variable;

    move-result-object v1

    if-ne v1, v0, :cond_0

    invoke-interface {p1, v0}, Ljava/util/Map;->containsKey(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_0

    .line 226
    invoke-interface {p2}, Ljava/util/Iterator;->remove()V

    goto :goto_0

    :cond_1
    return-void
.end method

.method private fallsThrough(Lorg/codehaus/groovy/ast/stmt/Statement;)Z
    .locals 3

    .line 459
    instance-of v0, p1, Lorg/codehaus/groovy/ast/stmt/EmptyStatement;

    const/4 v1, 0x1

    if-eqz v0, :cond_0

    return v1

    .line 462
    :cond_0
    instance-of v0, p1, Lorg/codehaus/groovy/ast/stmt/ReturnStatement;

    const/4 v2, 0x0

    if-eqz v0, :cond_1

    return v2

    .line 465
    :cond_1
    check-cast p1, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    .line 466
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->getStatements()Ljava/util/List;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    if-nez v0, :cond_2

    return v1

    .line 469
    :cond_2
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->getStatements()Ljava/util/List;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->last(Ljava/util/List;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lorg/codehaus/groovy/ast/stmt/Statement;

    .line 470
    instance-of v0, p1, Lorg/codehaus/groovy/ast/stmt/ReturnStatement;

    if-nez v0, :cond_3

    instance-of v0, p1, Lorg/codehaus/groovy/ast/stmt/BreakStatement;

    if-nez v0, :cond_3

    instance-of v0, p1, Lorg/codehaus/groovy/ast/stmt/ThrowStatement;

    if-nez v0, :cond_3

    instance-of p1, p1, Lorg/codehaus/groovy/ast/stmt/ContinueStatement;

    if-eqz p1, :cond_4

    :cond_3
    move v2, v1

    :cond_4
    xor-int/lit8 p1, v2, 0x1

    return p1
.end method

.method private fixVar(Lorg/codehaus/groovy/ast/Variable;)V
    .locals 4

    .line 516
    invoke-static {p1}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->getTarget(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/Variable;

    move-result-object v0

    if-nez v0, :cond_1

    instance-of v0, p1, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    if-eqz v0, :cond_1

    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->getState()Ljava/util/Map;

    move-result-object v0

    if-eqz v0, :cond_1

    invoke-interface {p1}, Lorg/codehaus/groovy/ast/Variable;->getName()Ljava/lang/String;

    move-result-object v0

    if-eqz v0, :cond_1

    .line 517
    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->getState()Ljava/util/Map;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Map;->keySet()Ljava/util/Set;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/ast/Variable;

    .line 518
    invoke-interface {p1}, Lorg/codehaus/groovy/ast/Variable;->getName()Ljava/lang/String;

    move-result-object v2

    invoke-interface {v1}, Lorg/codehaus/groovy/ast/Variable;->getName()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_0

    .line 519
    check-cast p1, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    invoke-virtual {p1, v1}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->setAccessedVariable(Lorg/codehaus/groovy/ast/Variable;)V

    :cond_1
    return-void
.end method

.method private getState()Ljava/util/Map;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Lorg/codehaus/groovy/ast/Variable;",
            "Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;",
            ">;"
        }
    .end annotation

    .line 129
    iget-object v0, p0, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->assignmentTracker:Ljava/util/Deque;

    invoke-interface {v0}, Ljava/util/Deque;->getLast()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/Map;

    return-object v0
.end method

.method private static getTarget(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/Variable;
    .locals 1

    .line 116
    instance-of v0, p0, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    if-eqz v0, :cond_1

    .line 117
    move-object v0, p0

    check-cast v0, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->getAccessedVariable()Lorg/codehaus/groovy/ast/Variable;

    move-result-object v0

    if-ne v0, p0, :cond_0

    return-object v0

    .line 119
    :cond_0
    invoke-static {v0}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->getTarget(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/Variable;

    move-result-object p0

    :cond_1
    return-object p0
.end method

.method static synthetic lambda$visitSwitch$0(Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;Lorg/codehaus/groovy/ast/Variable;Ljava/util/Map;)Z
    .locals 0

    .line 357
    invoke-interface {p2, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;->equals(Ljava/lang/Object;)Z

    move-result p0

    return p0
.end method

.method private popState()Ljava/util/Map;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Lorg/codehaus/groovy/ast/Variable;",
            "Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;",
            ">;"
        }
    .end annotation

    .line 125
    iget-object v0, p0, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->assignmentTracker:Ljava/util/Deque;

    invoke-interface {v0}, Ljava/util/Deque;->removeLast()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/Map;

    return-object v0
.end method

.method private pushState()Ljava/util/Map;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Lorg/codehaus/groovy/ast/Variable;",
            "Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;",
            ">;"
        }
    .end annotation

    .line 109
    new-instance v0, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$StateMap;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$StateMap;-><init>(Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$1;)V

    .line 110
    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->getState()Ljava/util/Map;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/util/Map;->putAll(Ljava/util/Map;)V

    .line 111
    iget-object v1, p0, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->assignmentTracker:Ljava/util/Deque;

    invoke-interface {v1, v0}, Ljava/util/Deque;->add(Ljava/lang/Object;)Z

    return-object v0
.end method

.method private recordAssignment(Lorg/codehaus/groovy/ast/Variable;ZZZLorg/codehaus/groovy/ast/expr/Expression;)V
    .locals 1

    if-nez p1, :cond_0

    return-void

    .line 485
    :cond_0
    invoke-static {p1}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->getTarget(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/Variable;

    move-result-object v0

    if-nez v0, :cond_1

    .line 486
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->fixVar(Lorg/codehaus/groovy/ast/Variable;)V

    .line 488
    invoke-static {p1}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->getTarget(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/Variable;

    move-result-object v0

    if-nez v0, :cond_1

    return-void

    :cond_1
    if-nez p2, :cond_2

    .line 491
    invoke-interface {p1}, Lorg/codehaus/groovy/ast/Variable;->isClosureSharedVariable()Z

    move-result p2

    if-eqz p2, :cond_2

    .line 492
    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->getState()Ljava/util/Map;

    move-result-object p2

    sget-object v0, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;->is_var:Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;

    invoke-interface {p2, p1, v0}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 494
    :cond_2
    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->getState()Ljava/util/Map;

    move-result-object p2

    invoke-interface {p2, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;

    if-nez p2, :cond_4

    if-eqz p3, :cond_3

    .line 496
    sget-object p2, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;->is_uninitialized:Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;

    goto :goto_0

    :cond_3
    sget-object p2, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;->is_final:Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;

    .line 497
    :goto_0
    invoke-static {p1}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->getTarget(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/Variable;

    move-result-object p3

    instance-of p3, p3, Lorg/codehaus/groovy/ast/Parameter;

    if-eqz p3, :cond_5

    .line 498
    sget-object p2, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;->is_var:Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;

    goto :goto_1

    .line 501
    :cond_4
    invoke-virtual {p2}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;->getNext()Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;

    move-result-object p2

    :cond_5
    :goto_1
    if-eqz p4, :cond_6

    .line 504
    sget-object p2, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;->is_var:Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;

    .line 506
    :cond_6
    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->getState()Ljava/util/Map;

    move-result-object p3

    invoke-interface {p3, p1, p2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 507
    sget-object p3, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;->is_var:Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;

    if-eq p2, p3, :cond_7

    sget-object p3, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;->is_ambiguous:Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;

    if-ne p2, p3, :cond_8

    :cond_7
    iget-object p2, p0, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->callback:Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableNotFinalCallback;

    if-eqz p2, :cond_8

    .line 508
    invoke-interface {p2, p1, p5}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableNotFinalCallback;->variableNotFinal(Lorg/codehaus/groovy/ast/Variable;Lorg/codehaus/groovy/ast/expr/Expression;)V

    :cond_8
    return-void
.end method

.method private recordAssignments(Lorg/codehaus/groovy/ast/expr/BinaryExpression;ZLorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)V
    .locals 6

    .line 179
    instance-of v0, p3, Lorg/codehaus/groovy/ast/Variable;

    if-eqz v0, :cond_1

    if-eqz p2, :cond_0

    .line 180
    instance-of p4, p4, Lorg/codehaus/groovy/ast/expr/EmptyExpression;

    if-eqz p4, :cond_0

    const/4 p4, 0x1

    goto :goto_0

    :cond_0
    const/4 p4, 0x0

    :goto_0
    move v3, p4

    .line 181
    move-object v1, p3

    check-cast v1, Lorg/codehaus/groovy/ast/Variable;

    const/4 v4, 0x0

    move-object v0, p0

    move v2, p2

    move-object v5, p1

    invoke-direct/range {v0 .. v5}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->recordAssignment(Lorg/codehaus/groovy/ast/Variable;ZZZLorg/codehaus/groovy/ast/expr/Expression;)V

    goto :goto_2

    .line 182
    :cond_1
    instance-of p1, p3, Lorg/codehaus/groovy/ast/expr/TupleExpression;

    if-eqz p1, :cond_3

    .line 183
    check-cast p3, Lorg/codehaus/groovy/ast/expr/TupleExpression;

    .line 184
    invoke-virtual {p3}, Lorg/codehaus/groovy/ast/expr/TupleExpression;->getExpressions()Ljava/util/List;

    move-result-object p1

    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_2
    :goto_1
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result p3

    if-eqz p3, :cond_3

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object p3

    move-object v5, p3

    check-cast v5, Lorg/codehaus/groovy/ast/expr/Expression;

    .line 185
    instance-of p3, v5, Lorg/codehaus/groovy/ast/Variable;

    if-eqz p3, :cond_2

    .line 186
    move-object v1, v5

    check-cast v1, Lorg/codehaus/groovy/ast/Variable;

    const/4 v3, 0x0

    const/4 v4, 0x0

    move-object v0, p0

    move v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->recordAssignment(Lorg/codehaus/groovy/ast/Variable;ZZZLorg/codehaus/groovy/ast/expr/Expression;)V

    goto :goto_1

    :cond_3
    :goto_2
    return-void
.end method

.method private recordFinalVars(Lorg/codehaus/groovy/ast/expr/Expression;)V
    .locals 2

    .line 193
    instance-of v0, p1, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    if-eqz v0, :cond_0

    .line 194
    check-cast p1, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    .line 195
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->getModifiers()I

    move-result v0

    invoke-static {v0}, Ljava/lang/reflect/Modifier;->isFinal(I)Z

    move-result v0

    if-eqz v0, :cond_2

    .line 196
    iget-object v0, p0, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->declaredFinalVariables:Ljava/util/Set;

    invoke-interface {v0, p1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    goto :goto_1

    .line 198
    :cond_0
    instance-of v0, p1, Lorg/codehaus/groovy/ast/expr/TupleExpression;

    if-eqz v0, :cond_2

    .line 199
    check-cast p1, Lorg/codehaus/groovy/ast/expr/TupleExpression;

    .line 200
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/TupleExpression;->getExpressions()Ljava/util/List;

    move-result-object p1

    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_1
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_2

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/ast/expr/Expression;

    .line 201
    instance-of v1, v0, Lorg/codehaus/groovy/ast/Variable;

    if-eqz v1, :cond_1

    .line 202
    iget-object v1, p0, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->declaredFinalVariables:Ljava/util/Set;

    check-cast v0, Lorg/codehaus/groovy/ast/Variable;

    invoke-interface {v1, v0}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    goto :goto_0

    :cond_2
    :goto_1
    return-void
.end method

.method private returningBlock(Lorg/codehaus/groovy/ast/stmt/Statement;)Z
    .locals 3

    .line 438
    instance-of v0, p1, Lorg/codehaus/groovy/ast/stmt/ReturnStatement;

    const/4 v1, 0x1

    if-nez v0, :cond_4

    instance-of v0, p1, Lorg/codehaus/groovy/ast/stmt/ThrowStatement;

    if-eqz v0, :cond_0

    goto :goto_0

    .line 441
    :cond_0
    instance-of v0, p1, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    const/4 v2, 0x0

    if-nez v0, :cond_1

    return v2

    .line 444
    :cond_1
    check-cast p1, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    .line 445
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->getStatements()Ljava/util/List;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    if-nez v0, :cond_2

    return v2

    .line 448
    :cond_2
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->getStatements()Ljava/util/List;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->last(Ljava/util/List;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lorg/codehaus/groovy/ast/stmt/Statement;

    .line 449
    instance-of v0, p1, Lorg/codehaus/groovy/ast/stmt/ReturnStatement;

    if-nez v0, :cond_4

    instance-of p1, p1, Lorg/codehaus/groovy/ast/stmt/ThrowStatement;

    if-eqz p1, :cond_3

    goto :goto_0

    :cond_3
    return v2

    :cond_4
    :goto_0
    return v1
.end method

.method private visitCatchFinally(Ljava/util/Map;Ljava/util/List;Lorg/codehaus/groovy/ast/stmt/CatchStatement;Lorg/codehaus/groovy/ast/stmt/Statement;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Lorg/codehaus/groovy/ast/Variable;",
            "Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;",
            ">;",
            "Ljava/util/List<",
            "Ljava/util/Map<",
            "Lorg/codehaus/groovy/ast/Variable;",
            "Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;",
            ">;>;",
            "Lorg/codehaus/groovy/ast/stmt/CatchStatement;",
            "Lorg/codehaus/groovy/ast/stmt/Statement;",
            ")V"
        }
    .end annotation

    .line 423
    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->pushState()Ljava/util/Map;

    .line 424
    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->getState()Ljava/util/Map;

    move-result-object v0

    invoke-interface {v0, p1}, Ljava/util/Map;->putAll(Ljava/util/Map;)V

    .line 425
    invoke-virtual {p3}, Lorg/codehaus/groovy/ast/stmt/CatchStatement;->getCode()Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    .line 426
    invoke-virtual {p3, p0}, Lorg/codehaus/groovy/ast/stmt/CatchStatement;->visit(Lorg/codehaus/groovy/ast/GroovyCodeVisitor;)V

    .line 427
    invoke-virtual {p4, p0}, Lorg/codehaus/groovy/ast/stmt/Statement;->visit(Lorg/codehaus/groovy/ast/GroovyCodeVisitor;)V

    if-eqz p1, :cond_0

    .line 428
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->returningBlock(Lorg/codehaus/groovy/ast/stmt/Statement;)Z

    move-result p1

    if-nez p1, :cond_1

    .line 429
    :cond_0
    new-instance p1, Ljava/util/HashMap;

    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->getState()Ljava/util/Map;

    move-result-object p3

    invoke-direct {p1, p3}, Ljava/util/HashMap;-><init>(Ljava/util/Map;)V

    invoke-interface {p2, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 431
    :cond_1
    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->popState()Ljava/util/Map;

    return-void
.end method


# virtual methods
.method protected getSourceUnit()Lorg/codehaus/groovy/control/SourceUnit;
    .locals 1

    .line 134
    iget-object v0, p0, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->sourceUnit:Lorg/codehaus/groovy/control/SourceUnit;

    return-object v0
.end method

.method public isEffectivelyFinal(Lorg/codehaus/groovy/ast/Variable;)Z
    .locals 1

    .line 138
    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->getState()Ljava/util/Map;

    move-result-object v0

    invoke-interface {v0, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;

    .line 139
    instance-of p1, p1, Lorg/codehaus/groovy/ast/Parameter;

    if-eqz p1, :cond_0

    if-eqz v0, :cond_1

    :cond_0
    if-eqz v0, :cond_2

    .line 140
    invoke-virtual {v0}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;->isFinal()Z

    move-result p1

    if-eqz p1, :cond_2

    :cond_1
    const/4 p1, 0x1

    goto :goto_0

    :cond_2
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public visitArgumentlistExpression(Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;)V
    .locals 2

    .line 153
    iget-boolean v0, p0, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->inArgumentList:Z

    const/4 v1, 0x1

    .line 154
    iput-boolean v1, p0, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->inArgumentList:Z

    .line 155
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;->visitArgumentlistExpression(Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;)V

    .line 156
    iput-boolean v0, p0, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->inArgumentList:Z

    return-void
.end method

.method public visitBinaryExpression(Lorg/codehaus/groovy/ast/expr/BinaryExpression;)V
    .locals 5

    .line 161
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/BinaryExpression;->getOperation()Lorg/codehaus/groovy/syntax/Token;

    move-result-object v0

    invoke-virtual {v0}, Lorg/codehaus/groovy/syntax/Token;->getType()I

    move-result v0

    invoke-static {v0}, Lorg/codehaus/groovy/transform/stc/StaticTypeCheckingSupport;->isAssignment(I)Z

    move-result v0

    .line 162
    instance-of v1, p1, Lorg/codehaus/groovy/ast/expr/DeclarationExpression;

    .line 163
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/BinaryExpression;->getLeftExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v2

    .line 164
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/BinaryExpression;->getRightExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v3

    if-eqz v1, :cond_0

    .line 166
    invoke-direct {p0, v2}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->recordFinalVars(Lorg/codehaus/groovy/ast/expr/Expression;)V

    .line 169
    :cond_0
    iput-boolean v0, p0, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->inAssignmentRHS:Z

    .line 170
    invoke-virtual {v3, p0}, Lorg/codehaus/groovy/ast/expr/Expression;->visit(Lorg/codehaus/groovy/ast/GroovyCodeVisitor;)V

    const/4 v4, 0x0

    .line 171
    iput-boolean v4, p0, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->inAssignmentRHS:Z

    .line 172
    invoke-virtual {v2, p0}, Lorg/codehaus/groovy/ast/expr/Expression;->visit(Lorg/codehaus/groovy/ast/GroovyCodeVisitor;)V

    if-eqz v0, :cond_1

    .line 174
    invoke-direct {p0, p1, v1, v2, v3}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->recordAssignments(Lorg/codehaus/groovy/ast/expr/BinaryExpression;ZLorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)V

    :cond_1
    return-void
.end method

.method public visitBlockStatement(Lorg/codehaus/groovy/ast/stmt/BlockStatement;)V
    .locals 2

    .line 145
    iget-object v0, p0, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->declaredFinalVariables:Ljava/util/Set;

    .line 146
    new-instance v1, Ljava/util/HashSet;

    invoke-direct {v1}, Ljava/util/HashSet;-><init>()V

    iput-object v1, p0, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->declaredFinalVariables:Ljava/util/Set;

    .line 147
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;->visitBlockStatement(Lorg/codehaus/groovy/ast/stmt/BlockStatement;)V

    .line 148
    iput-object v0, p0, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->declaredFinalVariables:Ljava/util/Set;

    return-void
.end method

.method public visitClosureExpression(Lorg/codehaus/groovy/ast/expr/ClosureExpression;)V
    .locals 3

    .line 210
    iget-boolean v0, p0, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->inAssignmentRHS:Z

    const/4 v1, 0x0

    .line 211
    iput-boolean v1, p0, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->inAssignmentRHS:Z

    .line 212
    new-instance v1, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$StateMap;

    const/4 v2, 0x0

    invoke-direct {v1, v2}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$StateMap;-><init>(Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$1;)V

    .line 213
    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->getState()Ljava/util/Map;

    move-result-object v2

    invoke-interface {v1, v2}, Ljava/util/Map;->putAll(Ljava/util/Map;)V

    .line 214
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;->visitClosureExpression(Lorg/codehaus/groovy/ast/expr/ClosureExpression;)V

    .line 215
    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->getState()Ljava/util/Map;

    move-result-object p1

    invoke-direct {p0, v1, p1}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->cleanLocalVars(Ljava/util/Map;Ljava/util/Map;)V

    .line 216
    iput-boolean v0, p0, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->inAssignmentRHS:Z

    return-void
.end method

.method public visitIfElse(Lorg/codehaus/groovy/ast/stmt/IfStatement;)V
    .locals 7

    .line 278
    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->visitStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 279
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/stmt/IfStatement;->getBooleanExpression()Lorg/codehaus/groovy/ast/expr/BooleanExpression;

    move-result-object v0

    invoke-virtual {v0, p0}, Lorg/codehaus/groovy/ast/expr/BooleanExpression;->visit(Lorg/codehaus/groovy/ast/GroovyCodeVisitor;)V

    .line 280
    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->pushState()Ljava/util/Map;

    move-result-object v0

    .line 281
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/stmt/IfStatement;->getIfBlock()Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v1

    invoke-virtual {v1, p0}, Lorg/codehaus/groovy/ast/stmt/Statement;->visit(Lorg/codehaus/groovy/ast/GroovyCodeVisitor;)V

    .line 282
    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->popState()Ljava/util/Map;

    .line 283
    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->pushState()Ljava/util/Map;

    move-result-object v1

    .line 284
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/stmt/IfStatement;->getElseBlock()Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    invoke-virtual {p1, p0}, Lorg/codehaus/groovy/ast/stmt/Statement;->visit(Lorg/codehaus/groovy/ast/GroovyCodeVisitor;)V

    .line 285
    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->popState()Ljava/util/Map;

    .line 288
    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->getState()Ljava/util/Map;

    move-result-object p1

    .line 289
    new-instance v2, Ljava/util/HashSet;

    invoke-direct {v2}, Ljava/util/HashSet;-><init>()V

    .line 290
    invoke-interface {p1}, Ljava/util/Map;->keySet()Ljava/util/Set;

    move-result-object v3

    invoke-interface {v2, v3}, Ljava/util/Set;->addAll(Ljava/util/Collection;)Z

    .line 291
    invoke-interface {v0}, Ljava/util/Map;->keySet()Ljava/util/Set;

    move-result-object v3

    invoke-interface {v2, v3}, Ljava/util/Set;->addAll(Ljava/util/Collection;)Z

    .line 292
    invoke-interface {v1}, Ljava/util/Map;->keySet()Ljava/util/Set;

    move-result-object v3

    invoke-interface {v2, v3}, Ljava/util/Set;->addAll(Ljava/util/Collection;)Z

    .line 293
    invoke-interface {v2}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :cond_0
    :goto_0
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_3

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lorg/codehaus/groovy/ast/Variable;

    .line 294
    invoke-interface {p1, v3}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;

    if-eqz v4, :cond_0

    .line 296
    invoke-interface {v0, v3}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;

    .line 297
    invoke-interface {v1, v3}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;

    if-ne v5, v6, :cond_1

    .line 299
    invoke-interface {p1, v3, v5}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    .line 301
    :cond_1
    sget-object v5, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;->is_uninitialized:Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;

    if-ne v4, v5, :cond_2

    sget-object v4, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;->is_ambiguous:Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;

    goto :goto_1

    :cond_2
    sget-object v4, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;->is_var:Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;

    :goto_1
    invoke-interface {p1, v3, v4}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    :cond_3
    return-void
.end method

.method public visitPostfixExpression(Lorg/codehaus/groovy/ast/expr/PostfixExpression;)V
    .locals 1

    .line 241
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/PostfixExpression;->getExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    instance-of v0, v0, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    iput-boolean v0, p0, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->inAssignmentRHS:Z

    .line 242
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;->visitPostfixExpression(Lorg/codehaus/groovy/ast/expr/PostfixExpression;)V

    const/4 v0, 0x0

    .line 243
    iput-boolean v0, p0, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->inAssignmentRHS:Z

    .line 244
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/PostfixExpression;->getExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    invoke-direct {p0, v0, p1}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->checkPrePostfixOperation(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)V

    return-void
.end method

.method public visitPrefixExpression(Lorg/codehaus/groovy/ast/expr/PrefixExpression;)V
    .locals 1

    .line 233
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/PrefixExpression;->getExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    instance-of v0, v0, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    iput-boolean v0, p0, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->inAssignmentRHS:Z

    .line 234
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;->visitPrefixExpression(Lorg/codehaus/groovy/ast/expr/PrefixExpression;)V

    const/4 v0, 0x0

    .line 235
    iput-boolean v0, p0, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->inAssignmentRHS:Z

    .line 236
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/PrefixExpression;->getExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    invoke-direct {p0, v0, p1}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->checkPrePostfixOperation(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)V

    return-void
.end method

.method public visitSwitch(Lorg/codehaus/groovy/ast/stmt/SwitchStatement;)V
    .locals 9

    .line 309
    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->visitStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 310
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/stmt/SwitchStatement;->getExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    invoke-virtual {v0, p0}, Lorg/codehaus/groovy/ast/expr/Expression;->visit(Lorg/codehaus/groovy/ast/GroovyCodeVisitor;)V

    .line 311
    new-instance v0, Ljava/util/ArrayList;

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/stmt/SwitchStatement;->getCaseStatements()Ljava/util/List;

    move-result-object v1

    invoke-direct {v0, v1}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    .line 312
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/stmt/SwitchStatement;->getDefaultStatement()Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v1

    instance-of v1, v1, Lorg/codehaus/groovy/ast/stmt/EmptyStatement;

    if-nez v1, :cond_0

    .line 313
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/stmt/SwitchStatement;->getDefaultStatement()Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 315
    :cond_0
    new-instance p1, Ljava/util/ArrayList;

    invoke-direct {p1}, Ljava/util/ArrayList;-><init>()V

    .line 318
    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v1

    const/4 v2, 0x1

    sub-int/2addr v1, v2

    const/4 v3, 0x0

    move v4, v3

    :goto_0
    if-gt v4, v1, :cond_7

    .line 320
    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->pushState()Ljava/util/Map;

    move v5, v3

    move v6, v5

    move v7, v4

    :goto_1
    if-nez v5, :cond_5

    .line 324
    invoke-interface {v0, v7}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lorg/codehaus/groovy/ast/stmt/Statement;

    .line 326
    instance-of v8, v5, Lorg/codehaus/groovy/ast/stmt/CaseStatement;

    if-eqz v8, :cond_1

    .line 327
    check-cast v5, Lorg/codehaus/groovy/ast/stmt/CaseStatement;

    .line 328
    invoke-virtual {v5}, Lorg/codehaus/groovy/ast/stmt/CaseStatement;->getCode()Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v8

    .line 329
    invoke-virtual {v5}, Lorg/codehaus/groovy/ast/stmt/CaseStatement;->getExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v5

    invoke-virtual {v5, p0}, Lorg/codehaus/groovy/ast/expr/Expression;->visit(Lorg/codehaus/groovy/ast/GroovyCodeVisitor;)V

    move-object v5, v8

    .line 331
    :cond_1
    invoke-virtual {v5, p0}, Lorg/codehaus/groovy/ast/stmt/Statement;->visit(Lorg/codehaus/groovy/ast/GroovyCodeVisitor;)V

    if-eq v7, v1, :cond_3

    .line 332
    invoke-direct {p0, v5}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->fallsThrough(Lorg/codehaus/groovy/ast/stmt/Statement;)Z

    move-result v8

    if-nez v8, :cond_2

    goto :goto_2

    :cond_2
    move v8, v3

    goto :goto_3

    :cond_3
    :goto_2
    move v8, v2

    :goto_3
    if-eqz v8, :cond_4

    .line 334
    invoke-direct {p0, v5}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->returningBlock(Lorg/codehaus/groovy/ast/stmt/Statement;)Z

    move-result v6

    :cond_4
    add-int/lit8 v7, v7, 0x1

    move v5, v8

    goto :goto_1

    :cond_5
    if-nez v6, :cond_6

    .line 338
    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->getState()Ljava/util/Map;

    move-result-object v5

    invoke-interface {p1, v5}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 340
    :cond_6
    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->popState()Ljava/util/Map;

    add-int/lit8 v4, v4, 0x1

    goto :goto_0

    .line 342
    :cond_7
    invoke-interface {p1}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_8

    return-void

    .line 347
    :cond_8
    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->getState()Ljava/util/Map;

    move-result-object v0

    .line 348
    new-instance v1, Ljava/util/HashSet;

    invoke-interface {v0}, Ljava/util/Map;->keySet()Ljava/util/Set;

    move-result-object v2

    invoke-direct {v1, v2}, Ljava/util/HashSet;-><init>(Ljava/util/Collection;)V

    .line 349
    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :goto_4
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v4

    if-eqz v4, :cond_9

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Ljava/util/Map;

    .line 350
    invoke-interface {v4}, Ljava/util/Map;->keySet()Ljava/util/Set;

    move-result-object v4

    invoke-interface {v1, v4}, Ljava/util/Set;->addAll(Ljava/util/Collection;)Z

    goto :goto_4

    .line 352
    :cond_9
    invoke-interface {v1}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :cond_a
    :goto_5
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_d

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lorg/codehaus/groovy/ast/Variable;

    .line 353
    invoke-interface {v0, v2}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;

    if-eqz v4, :cond_a

    .line 355
    invoke-interface {p1, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Ljava/util/Map;

    invoke-interface {v5, v2}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;

    if-eqz v5, :cond_a

    .line 357
    invoke-interface {p1}, Ljava/util/List;->stream()Ljava/util/stream/Stream;

    move-result-object v6

    new-instance v7, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$$ExternalSyntheticLambda0;

    invoke-direct {v7, v5, v2}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$$ExternalSyntheticLambda0;-><init>(Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;Lorg/codehaus/groovy/ast/Variable;)V

    invoke-interface {v6, v7}, Ljava/util/stream/Stream;->allMatch(Ljava/util/function/Predicate;)Z

    move-result v6

    if-eqz v6, :cond_b

    .line 358
    invoke-interface {v0, v2, v5}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_5

    .line 360
    :cond_b
    sget-object v5, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;->is_uninitialized:Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;

    if-ne v4, v5, :cond_c

    sget-object v4, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;->is_ambiguous:Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;

    goto :goto_6

    :cond_c
    sget-object v4, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;->is_var:Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;

    .line 361
    :goto_6
    invoke-interface {v0, v2, v4}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_5

    :cond_d
    return-void
.end method

.method public visitTryCatchFinally(Lorg/codehaus/groovy/ast/stmt/TryCatchStatement;)V
    .locals 7

    .line 370
    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->visitStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 371
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->getState()Ljava/util/Map;

    move-result-object v1

    invoke-direct {v0, v1}, Ljava/util/HashMap;-><init>(Ljava/util/Map;)V

    .line 372
    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->pushState()Ljava/util/Map;

    .line 373
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/stmt/TryCatchStatement;->getTryStatement()Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v1

    .line 374
    invoke-virtual {v1, p0}, Lorg/codehaus/groovy/ast/stmt/Statement;->visit(Lorg/codehaus/groovy/ast/GroovyCodeVisitor;)V

    .line 375
    new-instance v2, Ljava/util/HashMap;

    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->getState()Ljava/util/Map;

    move-result-object v3

    invoke-direct {v2, v3}, Ljava/util/HashMap;-><init>(Ljava/util/Map;)V

    .line 376
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/stmt/TryCatchStatement;->getFinallyStatement()Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v3

    .line 377
    new-instance v4, Ljava/util/ArrayList;

    invoke-direct {v4}, Ljava/util/ArrayList;-><init>()V

    .line 379
    invoke-virtual {v3, p0}, Lorg/codehaus/groovy/ast/stmt/Statement;->visit(Lorg/codehaus/groovy/ast/GroovyCodeVisitor;)V

    .line 380
    invoke-direct {p0, v1}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->returningBlock(Lorg/codehaus/groovy/ast/stmt/Statement;)Z

    move-result v5

    if-nez v5, :cond_0

    .line 381
    new-instance v5, Ljava/util/HashMap;

    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->getState()Ljava/util/Map;

    move-result-object v6

    invoke-direct {v5, v6}, Ljava/util/HashMap;-><init>(Ljava/util/Map;)V

    invoke-interface {v4, v5}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 383
    :cond_0
    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->popState()Ljava/util/Map;

    .line 385
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/stmt/TryCatchStatement;->getCatchStatements()Ljava/util/List;

    move-result-object v5

    invoke-interface {v5}, Ljava/util/List;->isEmpty()Z

    move-result v5

    if-eqz v5, :cond_1

    .line 386
    invoke-virtual {v3, p0}, Lorg/codehaus/groovy/ast/stmt/Statement;->visit(Lorg/codehaus/groovy/ast/GroovyCodeVisitor;)V

    .line 387
    invoke-direct {p0, v1}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->returningBlock(Lorg/codehaus/groovy/ast/stmt/Statement;)Z

    move-result v1

    if-nez v1, :cond_1

    .line 388
    new-instance v1, Ljava/util/HashMap;

    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->getState()Ljava/util/Map;

    move-result-object v5

    invoke-direct {v1, v5}, Ljava/util/HashMap;-><init>(Ljava/util/Map;)V

    invoke-interface {v4, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 391
    :cond_1
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/stmt/TryCatchStatement;->getCatchStatements()Ljava/util/List;

    move-result-object p1

    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_2

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/ast/stmt/CatchStatement;

    .line 395
    invoke-direct {p0, v0, v4, v1, v3}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->visitCatchFinally(Ljava/util/Map;Ljava/util/List;Lorg/codehaus/groovy/ast/stmt/CatchStatement;Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 396
    invoke-direct {p0, v2, v4, v1, v3}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->visitCatchFinally(Ljava/util/Map;Ljava/util/List;Lorg/codehaus/groovy/ast/stmt/CatchStatement;Lorg/codehaus/groovy/ast/stmt/Statement;)V

    goto :goto_0

    .line 399
    :cond_2
    invoke-interface {v4}, Ljava/util/List;->isEmpty()Z

    move-result p1

    if-eqz p1, :cond_3

    return-void

    :cond_3
    const/4 p1, 0x0

    .line 403
    invoke-interface {v4, p1}, Ljava/util/List;->remove(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/util/Map;

    .line 404
    invoke-interface {v4}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_4
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_9

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/util/Map;

    .line 405
    invoke-interface {p1}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    move-result-object v2

    invoke-interface {v2}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :cond_5
    :goto_1
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_4

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/util/Map$Entry;

    .line 406
    invoke-interface {v3}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lorg/codehaus/groovy/ast/Variable;

    .line 407
    invoke-interface {v3}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;

    .line 408
    invoke-interface {v1, v4}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;

    .line 409
    sget-object v6, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;->is_ambiguous:Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;

    if-ne v3, v6, :cond_6

    goto :goto_1

    :cond_6
    if-eq v3, v5, :cond_5

    .line 411
    sget-object v6, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;->is_uninitialized:Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;

    if-eq v3, v6, :cond_8

    sget-object v3, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;->is_uninitialized:Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;

    if-ne v5, v3, :cond_7

    goto :goto_2

    .line 414
    :cond_7
    sget-object v3, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;->is_var:Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;

    invoke-interface {p1, v4, v3}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_1

    .line 412
    :cond_8
    :goto_2
    sget-object v3, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;->is_ambiguous:Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;

    invoke-interface {p1, v4, v3}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_1

    .line 419
    :cond_9
    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->getState()Ljava/util/Map;

    move-result-object v0

    invoke-interface {v0, p1}, Ljava/util/Map;->putAll(Ljava/util/Map;)V

    return-void
.end method

.method public visitVariableExpression(Lorg/codehaus/groovy/ast/expr/VariableExpression;)V
    .locals 3

    .line 261
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;->visitVariableExpression(Lorg/codehaus/groovy/ast/expr/VariableExpression;)V

    .line 262
    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->getState()Ljava/util/Map;

    move-result-object v0

    .line 263
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->getAccessedVariable()Lorg/codehaus/groovy/ast/Variable;

    move-result-object v1

    if-nez v1, :cond_0

    .line 265
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->fixVar(Lorg/codehaus/groovy/ast/Variable;)V

    .line 266
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->getAccessedVariable()Lorg/codehaus/groovy/ast/Variable;

    move-result-object v1

    :cond_0
    if-eqz v1, :cond_3

    .line 268
    invoke-interface {v1}, Lorg/codehaus/groovy/ast/Variable;->isClosureSharedVariable()Z

    move-result v2

    if-nez v2, :cond_3

    iget-object v2, p0, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->callback:Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableNotFinalCallback;

    if-eqz v2, :cond_3

    .line 269
    invoke-interface {v0, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;

    .line 270
    iget-boolean v1, p0, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->inAssignmentRHS:Z

    if-nez v1, :cond_1

    iget-boolean v1, p0, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->inArgumentList:Z

    if-eqz v1, :cond_3

    :cond_1
    sget-object v1, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;->is_uninitialized:Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;

    if-eq v0, v1, :cond_2

    sget-object v1, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;->is_ambiguous:Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;

    if-ne v0, v1, :cond_3

    .line 271
    :cond_2
    iget-object v0, p0, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->callback:Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableNotFinalCallback;

    invoke-interface {v0, p1}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableNotFinalCallback;->variableNotAlwaysInitialized(Lorg/codehaus/groovy/ast/expr/VariableExpression;)V

    :cond_3
    return-void
.end method
