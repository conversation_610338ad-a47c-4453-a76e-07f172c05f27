.class Lorg/codehaus/groovy/classgen/Verifier$SwapInitStatement;
.super Lorg/codehaus/groovy/classgen/BytecodeSequence;
.source "Verifier.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/codehaus/groovy/classgen/Verifier;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0xa
    name = "SwapInitStatement"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/codehaus/groovy/classgen/Verifier$SwapInitStatement$SwapInitInstruction;
    }
.end annotation


# instance fields
.field private controller:Lorg/codehaus/groovy/classgen/asm/WriterController;


# direct methods
.method constructor <init>()V
    .locals 2

    .line 1584
    new-instance v0, Lorg/codehaus/groovy/classgen/Verifier$SwapInitStatement$SwapInitInstruction;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/codehaus/groovy/classgen/Verifier$SwapInitStatement$SwapInitInstruction;-><init>(Lorg/codehaus/groovy/classgen/Verifier$1;)V

    invoke-direct {p0, v0}, Lorg/codehaus/groovy/classgen/BytecodeSequence;-><init>(Lorg/codehaus/groovy/classgen/BytecodeInstruction;)V

    .line 1585
    invoke-virtual {p0}, Lorg/codehaus/groovy/classgen/Verifier$SwapInitStatement;->getInstructions()Ljava/util/List;

    move-result-object v0

    const/4 v1, 0x0

    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/classgen/Verifier$SwapInitStatement$SwapInitInstruction;

    invoke-static {v0, p0}, Lorg/codehaus/groovy/classgen/Verifier$SwapInitStatement$SwapInitInstruction;->access$202(Lorg/codehaus/groovy/classgen/Verifier$SwapInitStatement$SwapInitInstruction;Lorg/codehaus/groovy/classgen/Verifier$SwapInitStatement;)Lorg/codehaus/groovy/classgen/Verifier$SwapInitStatement;

    return-void
.end method

.method static synthetic access$300(Lorg/codehaus/groovy/classgen/Verifier$SwapInitStatement;)Lorg/codehaus/groovy/classgen/asm/WriterController;
    .locals 0

    .line 1580
    iget-object p0, p0, Lorg/codehaus/groovy/classgen/Verifier$SwapInitStatement;->controller:Lorg/codehaus/groovy/classgen/asm/WriterController;

    return-object p0
.end method


# virtual methods
.method public visit(Lorg/codehaus/groovy/ast/GroovyCodeVisitor;)V
    .locals 1

    .line 1590
    instance-of v0, p1, Lorg/codehaus/groovy/classgen/AsmClassGenerator;

    if-eqz v0, :cond_0

    .line 1591
    move-object v0, p1

    check-cast v0, Lorg/codehaus/groovy/classgen/AsmClassGenerator;

    invoke-virtual {v0}, Lorg/codehaus/groovy/classgen/AsmClassGenerator;->getController()Lorg/codehaus/groovy/classgen/asm/WriterController;

    move-result-object v0

    iput-object v0, p0, Lorg/codehaus/groovy/classgen/Verifier$SwapInitStatement;->controller:Lorg/codehaus/groovy/classgen/asm/WriterController;

    .line 1593
    :cond_0
    invoke-super {p0, p1}, Lorg/codehaus/groovy/classgen/BytecodeSequence;->visit(Lorg/codehaus/groovy/ast/GroovyCodeVisitor;)V

    return-void
.end method
