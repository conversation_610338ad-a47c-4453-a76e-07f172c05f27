.class public abstract Lorg/codehaus/groovy/classgen/InnerClassVisitorHelper;
.super Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;
.source "InnerClassVisitorHelper.java"


# static fields
.field private static final OBJECT_ARRAY:Lorg/codehaus/groovy/ast/ClassNode;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 55
    sget-object v0, Lorg/codehaus/groovy/ast/ClassHelper;->OBJECT_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->makeArray()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/classgen/InnerClassVisitorHelper;->OBJECT_ARRAY:Lorg/codehaus/groovy/ast/ClassNode;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 53
    invoke-direct {p0}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;-><init>()V

    return-void
.end method

.method protected static addFieldInit(Lorg/codehaus/groovy/ast/Parameter;Lorg/codehaus/groovy/ast/FieldNode;Lorg/codehaus/groovy/ast/stmt/BlockStatement;)V
    .locals 0

    .line 58
    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->fieldX(Lorg/codehaus/groovy/ast/FieldNode;)Lorg/codehaus/groovy/ast/expr/FieldExpression;

    move-result-object p1

    invoke-static {p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object p0

    invoke-static {p1, p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p0

    invoke-virtual {p2, p0}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    return-void
.end method

.method private static dynName(Lorg/codehaus/groovy/ast/Parameter;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 3

    .line 85
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 86
    new-instance v1, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    const-string v2, ""

    invoke-direct {v1, v2}, Lorg/codehaus/groovy/ast/expr/ConstantExpression;-><init>(Ljava/lang/Object;)V

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 87
    new-instance v1, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    invoke-direct {v1, v2}, Lorg/codehaus/groovy/ast/expr/ConstantExpression;-><init>(Ljava/lang/Object;)V

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 89
    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    .line 90
    invoke-static {p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object p0

    invoke-interface {v1, p0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 92
    new-instance p0, Lorg/codehaus/groovy/ast/expr/GStringExpression;

    const-string v2, "$name"

    invoke-direct {p0, v2, v0, v1}, Lorg/codehaus/groovy/ast/expr/GStringExpression;-><init>(Ljava/lang/String;Ljava/util/List;Ljava/util/List;)V

    return-object p0
.end method

.method protected static getClassNode(Lorg/codehaus/groovy/ast/ClassNode;Z)Lorg/codehaus/groovy/ast/ClassNode;
    .locals 0

    if-eqz p1, :cond_0

    .line 100
    sget-object p0, Lorg/codehaus/groovy/ast/ClassHelper;->CLASS_Type:Lorg/codehaus/groovy/ast/ClassNode;

    :cond_0
    return-object p0
.end method

.method protected static getObjectDistance(Lorg/codehaus/groovy/ast/ClassNode;)I
    .locals 2

    const/4 v0, 0x0

    :goto_0
    if-eqz p0, :cond_0

    .line 105
    sget-object v1, Lorg/codehaus/groovy/ast/ClassHelper;->OBJECT_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    if-eq p0, v1, :cond_0

    .line 106
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/ClassNode;->getSuperClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p0

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_0
    return v0
.end method

.method protected static isStatic(Lorg/codehaus/groovy/ast/InnerClassNode;)Z
    .locals 1

    const-string v0, "this$0"

    .line 96
    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/ast/InnerClassNode;->getDeclaredField(Ljava/lang/String;)Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object p0

    if-nez p0, :cond_0

    const/4 p0, 0x1

    goto :goto_0

    :cond_0
    const/4 p0, 0x0

    :goto_0
    return p0
.end method

.method protected static setMethodDispatcherCode(Lorg/codehaus/groovy/ast/stmt/BlockStatement;Lorg/codehaus/groovy/ast/expr/Expression;[Lorg/codehaus/groovy/ast/Parameter;)V
    .locals 6

    const/4 v0, 0x1

    .line 71
    aget-object v1, p2, v0

    .line 72
    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v1

    sget-object v2, Lorg/codehaus/groovy/classgen/InnerClassVisitorHelper;->OBJECT_ARRAY:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {v1, v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->isInstanceOfX(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/BooleanExpression;

    move-result-object v1

    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->notX(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/NotExpression;

    move-result-object v1

    const/4 v3, 0x0

    aget-object v4, p2, v3

    .line 73
    invoke-static {v4}, Lorg/codehaus/groovy/classgen/InnerClassVisitorHelper;->dynName(Lorg/codehaus/groovy/ast/Parameter;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v4

    aget-object v5, p2, v0

    invoke-static {v5}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v5

    invoke-static {p1, v4, v5}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object v4

    invoke-static {v4}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->returnS(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v4

    .line 71
    invoke-static {v1, v4}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ifS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/IfStatement;

    move-result-object v1

    invoke-virtual {p0, v1}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 76
    aget-object v1, p2, v0

    .line 77
    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v1

    invoke-static {v2, v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->castX(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/CastExpression;

    move-result-object v1

    const-string v4, "length"

    invoke-static {v1, v4}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->propX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    move-result-object v1

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v4

    invoke-static {v4, v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->constX(Ljava/lang/Object;Z)Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    move-result-object v4

    invoke-static {v1, v4}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->eqX(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/BinaryExpression;

    move-result-object v1

    aget-object v4, p2, v3

    .line 78
    invoke-static {v4}, Lorg/codehaus/groovy/classgen/InnerClassVisitorHelper;->dynName(Lorg/codehaus/groovy/ast/Parameter;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v4

    aget-object v5, p2, v0

    invoke-static {v5}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v5

    invoke-static {v2, v5}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->castX(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/CastExpression;

    move-result-object v2

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v5

    invoke-static {v5, v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->constX(Ljava/lang/Object;Z)Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    move-result-object v5

    invoke-static {v2, v5}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->indexX(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v2

    invoke-static {p1, v4, v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object v2

    invoke-static {v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->returnS(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v2

    .line 76
    invoke-static {v1, v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ifS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/IfStatement;

    move-result-object v1

    invoke-virtual {p0, v1}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 81
    aget-object v1, p2, v3

    invoke-static {v1}, Lorg/codehaus/groovy/classgen/InnerClassVisitorHelper;->dynName(Lorg/codehaus/groovy/ast/Parameter;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v1

    new-instance v2, Lorg/codehaus/groovy/ast/expr/SpreadExpression;

    aget-object p2, p2, v0

    invoke-static {p2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object p2

    invoke-direct {v2, p2}, Lorg/codehaus/groovy/ast/expr/SpreadExpression;-><init>(Lorg/codehaus/groovy/ast/expr/Expression;)V

    invoke-static {p1, v1, v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->returnS(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    return-void
.end method

.method protected static setPropertyGetterDispatcher(Lorg/codehaus/groovy/ast/stmt/BlockStatement;Lorg/codehaus/groovy/ast/expr/Expression;[Lorg/codehaus/groovy/ast/Parameter;)V
    .locals 1

    const/4 v0, 0x0

    .line 62
    aget-object p2, p2, v0

    invoke-static {p2}, Lorg/codehaus/groovy/classgen/InnerClassVisitorHelper;->dynName(Lorg/codehaus/groovy/ast/Parameter;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p2

    invoke-static {p1, p2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->propX(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->returnS(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    return-void
.end method

.method protected static setPropertySetterDispatcher(Lorg/codehaus/groovy/ast/stmt/BlockStatement;Lorg/codehaus/groovy/ast/expr/Expression;[Lorg/codehaus/groovy/ast/Parameter;)V
    .locals 1

    const/4 v0, 0x0

    .line 66
    aget-object v0, p2, v0

    invoke-static {v0}, Lorg/codehaus/groovy/classgen/InnerClassVisitorHelper;->dynName(Lorg/codehaus/groovy/ast/Parameter;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    invoke-static {p1, v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->propX(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    move-result-object p1

    const/4 v0, 0x1

    aget-object p2, p2, v0

    invoke-static {p2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object p2

    invoke-static {p1, p2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignX(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->stmt(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    return-void
.end method

.method protected static shouldHandleImplicitThisForInnerClass(Lorg/codehaus/groovy/ast/ClassNode;)Z
    .locals 1

    .line 114
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/ClassNode;->getModifiers()I

    move-result v0

    and-int/lit16 v0, v0, 0x4208

    if-nez v0, :cond_0

    instance-of v0, p0, Lorg/codehaus/groovy/ast/InnerClassNode;

    if-eqz v0, :cond_0

    check-cast p0, Lorg/codehaus/groovy/ast/InnerClassNode;

    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/InnerClassNode;->isAnonymous()Z

    move-result p0

    if-nez p0, :cond_0

    const/4 p0, 0x1

    goto :goto_0

    :cond_0
    const/4 p0, 0x0

    :goto_0
    return p0
.end method
