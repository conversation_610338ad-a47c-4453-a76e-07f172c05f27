.class Lorg/codehaus/groovy/classgen/Verifier$SwapInitStatement$SwapInitInstruction;
.super Lorg/codehaus/groovy/classgen/BytecodeInstruction;
.source "Verifier.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/codehaus/groovy/classgen/Verifier$SwapInitStatement;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0xa
    name = "SwapInitInstruction"
.end annotation


# instance fields
.field private statement:Lorg/codehaus/groovy/classgen/Verifier$SwapInitStatement;


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 1596
    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/BytecodeInstruction;-><init>()V

    return-void
.end method

.method synthetic constructor <init>(Lorg/codehaus/groovy/classgen/Verifier$1;)V
    .locals 0

    .line 1596
    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/Verifier$SwapInitStatement$SwapInitInstruction;-><init>()V

    return-void
.end method

.method static synthetic access$202(Lorg/codehaus/groovy/classgen/Verifier$SwapInitStatement$SwapInitInstruction;Lorg/codehaus/groovy/classgen/Verifier$SwapInitStatement;)Lorg/codehaus/groovy/classgen/Verifier$SwapInitStatement;
    .locals 0

    .line 1596
    iput-object p1, p0, Lorg/codehaus/groovy/classgen/Verifier$SwapInitStatement$SwapInitInstruction;->statement:Lorg/codehaus/groovy/classgen/Verifier$SwapInitStatement;

    return-object p1
.end method


# virtual methods
.method public visit(Lgroovyjarjarasm/asm/MethodVisitor;)V
    .locals 0

    .line 1601
    iget-object p1, p0, Lorg/codehaus/groovy/classgen/Verifier$SwapInitStatement$SwapInitInstruction;->statement:Lorg/codehaus/groovy/classgen/Verifier$SwapInitStatement;

    invoke-static {p1}, Lorg/codehaus/groovy/classgen/Verifier$SwapInitStatement;->access$300(Lorg/codehaus/groovy/classgen/Verifier$SwapInitStatement;)Lorg/codehaus/groovy/classgen/asm/WriterController;

    move-result-object p1

    invoke-virtual {p1}, Lorg/codehaus/groovy/classgen/asm/WriterController;->getCallSiteWriter()Lorg/codehaus/groovy/classgen/asm/CallSiteWriter;

    move-result-object p1

    invoke-virtual {p1}, Lorg/codehaus/groovy/classgen/asm/CallSiteWriter;->makeCallSiteArrayInitializer()V

    return-void
.end method
