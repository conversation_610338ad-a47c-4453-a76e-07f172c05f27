.class Lorg/codehaus/groovy/classgen/AsmClassGenerator$2;
.super Lorg/codehaus/groovy/classgen/BytecodeInstruction;
.source "AsmClassGenerator.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/codehaus/groovy/classgen/AsmClassGenerator;->visitClosureListExpression(Lorg/codehaus/groovy/ast/expr/ClosureListExpression;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lorg/codehaus/groovy/classgen/AsmClassGenerator;

.field final synthetic val$label:Lgroovyjarjarasm/asm/Label;


# direct methods
.method constructor <init>(Lorg/codehaus/groovy/classgen/AsmClassGenerator;Lgroovyjarjarasm/asm/Label;)V
    .locals 0

    .line 1722
    iput-object p1, p0, Lorg/codehaus/groovy/classgen/AsmClassGenerator$2;->this$0:Lorg/codehaus/groovy/classgen/AsmClassGenerator;

    iput-object p2, p0, Lorg/codehaus/groovy/classgen/AsmClassGenerator$2;->val$label:Lgroovyjarjarasm/asm/Label;

    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/BytecodeInstruction;-><init>()V

    return-void
.end method


# virtual methods
.method public visit(Lgroovyjarjarasm/asm/MethodVisitor;)V
    .locals 1

    .line 1724
    iget-object v0, p0, Lorg/codehaus/groovy/classgen/AsmClassGenerator$2;->val$label:Lgroovyjarjarasm/asm/Label;

    invoke-virtual {p1, v0}, Lgroovyjarjarasm/asm/MethodVisitor;->visitLabel(Lgroovyjarjarasm/asm/Label;)V

    const/16 v0, 0x57

    .line 1726
    invoke-virtual {p1, v0}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    return-void
.end method
