.class public final synthetic Lorg/codehaus/groovy/classgen/InnerClassCompletionVisitor$$ExternalSyntheticLambda4;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/BiConsumer;


# instance fields
.field public final synthetic f$0:Lorg/codehaus/groovy/classgen/InnerClassCompletionVisitor;

.field public final synthetic f$1:Z

.field public final synthetic f$2:Lorg/codehaus/groovy/ast/ClassNode;

.field public final synthetic f$3:Ljava/lang/String;

.field public final synthetic f$4:Ljava/lang/String;

.field public final synthetic f$5:Ljava/lang/String;

.field public final synthetic f$6:I


# direct methods
.method public synthetic constructor <init>(Lorg/codehaus/groovy/classgen/InnerClassCompletionVisitor;ZLorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;I)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/codehaus/groovy/classgen/InnerClassCompletionVisitor$$ExternalSyntheticLambda4;->f$0:Lorg/codehaus/groovy/classgen/InnerClassCompletionVisitor;

    iput-boolean p2, p0, Lorg/codehaus/groovy/classgen/InnerClassCompletionVisitor$$ExternalSyntheticLambda4;->f$1:Z

    iput-object p3, p0, Lorg/codehaus/groovy/classgen/InnerClassCompletionVisitor$$ExternalSyntheticLambda4;->f$2:Lorg/codehaus/groovy/ast/ClassNode;

    iput-object p4, p0, Lorg/codehaus/groovy/classgen/InnerClassCompletionVisitor$$ExternalSyntheticLambda4;->f$3:Ljava/lang/String;

    iput-object p5, p0, Lorg/codehaus/groovy/classgen/InnerClassCompletionVisitor$$ExternalSyntheticLambda4;->f$4:Ljava/lang/String;

    iput-object p6, p0, Lorg/codehaus/groovy/classgen/InnerClassCompletionVisitor$$ExternalSyntheticLambda4;->f$5:Ljava/lang/String;

    iput p7, p0, Lorg/codehaus/groovy/classgen/InnerClassCompletionVisitor$$ExternalSyntheticLambda4;->f$6:I

    return-void
.end method


# virtual methods
.method public final accept(Ljava/lang/Object;Ljava/lang/Object;)V
    .locals 9

    iget-object v0, p0, Lorg/codehaus/groovy/classgen/InnerClassCompletionVisitor$$ExternalSyntheticLambda4;->f$0:Lorg/codehaus/groovy/classgen/InnerClassCompletionVisitor;

    iget-boolean v1, p0, Lorg/codehaus/groovy/classgen/InnerClassCompletionVisitor$$ExternalSyntheticLambda4;->f$1:Z

    iget-object v2, p0, Lorg/codehaus/groovy/classgen/InnerClassCompletionVisitor$$ExternalSyntheticLambda4;->f$2:Lorg/codehaus/groovy/ast/ClassNode;

    iget-object v3, p0, Lorg/codehaus/groovy/classgen/InnerClassCompletionVisitor$$ExternalSyntheticLambda4;->f$3:Ljava/lang/String;

    iget-object v4, p0, Lorg/codehaus/groovy/classgen/InnerClassCompletionVisitor$$ExternalSyntheticLambda4;->f$4:Ljava/lang/String;

    iget-object v5, p0, Lorg/codehaus/groovy/classgen/InnerClassCompletionVisitor$$ExternalSyntheticLambda4;->f$5:Ljava/lang/String;

    iget v6, p0, Lorg/codehaus/groovy/classgen/InnerClassCompletionVisitor$$ExternalSyntheticLambda4;->f$6:I

    move-object v7, p1

    check-cast v7, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    move-object v8, p2

    check-cast v8, [Lorg/codehaus/groovy/ast/Parameter;

    invoke-virtual/range {v0 .. v8}, Lorg/codehaus/groovy/classgen/InnerClassCompletionVisitor;->lambda$addMopMethods$2$org-codehaus-groovy-classgen-InnerClassCompletionVisitor(ZLorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ILorg/codehaus/groovy/ast/stmt/BlockStatement;[Lorg/codehaus/groovy/ast/Parameter;)V

    return-void
.end method
