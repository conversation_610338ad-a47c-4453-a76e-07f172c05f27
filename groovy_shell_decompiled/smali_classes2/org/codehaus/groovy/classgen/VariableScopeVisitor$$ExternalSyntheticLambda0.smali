.class public final synthetic Lorg/codehaus/groovy/classgen/VariableScopeVisitor$$ExternalSyntheticLambda0;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/BiConsumer;


# instance fields
.field public final synthetic f$0:Lorg/codehaus/groovy/classgen/VariableScopeVisitor;


# direct methods
.method public synthetic constructor <init>(Lorg/codehaus/groovy/classgen/VariableScopeVisitor;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor$$ExternalSyntheticLambda0;->f$0:Lorg/codehaus/groovy/classgen/VariableScopeVisitor;

    return-void
.end method


# virtual methods
.method public final accept(Ljava/lang/Object;Ljava/lang/Object;)V
    .locals 1

    iget-object v0, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor$$ExternalSyntheticLambda0;->f$0:Lorg/codehaus/groovy/classgen/VariableScopeVisitor;

    check-cast p1, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    check-cast p2, Lorg/codehaus/groovy/ast/ASTNode;

    invoke-virtual {v0, p1, p2}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->lambda$checkFinalFieldAccess$0$org-codehaus-groovy-classgen-VariableScopeVisitor(Lorg/codehaus/groovy/ast/expr/VariableExpression;Lorg/codehaus/groovy/ast/ASTNode;)V

    return-void
.end method
