.class public final synthetic Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$$ExternalSyntheticLambda0;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Predicate;


# instance fields
.field public final synthetic f$0:Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;

.field public final synthetic f$1:Lorg/codehaus/groovy/ast/Variable;


# direct methods
.method public synthetic constructor <init>(Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;Lorg/codehaus/groovy/ast/Variable;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$$ExternalSyntheticLambda0;->f$0:Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;

    iput-object p2, p0, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$$ExternalSyntheticLambda0;->f$1:Lorg/codehaus/groovy/ast/Variable;

    return-void
.end method


# virtual methods
.method public final test(Ljava/lang/Object;)Z
    .locals 2

    iget-object v0, p0, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$$ExternalSyntheticLambda0;->f$0:Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;

    iget-object v1, p0, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$$ExternalSyntheticLambda0;->f$1:Lorg/codehaus/groovy/ast/Variable;

    check-cast p1, Ljava/util/Map;

    invoke-static {v0, v1, p1}, Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer;->lambda$visitSwitch$0(Lorg/codehaus/groovy/classgen/FinalVariableAnalyzer$VariableState;Lorg/codehaus/groovy/ast/Variable;Ljava/util/Map;)Z

    move-result p1

    return p1
.end method
