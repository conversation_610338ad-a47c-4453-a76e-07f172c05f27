.class public final synthetic Lorg/codehaus/groovy/classgen/Verifier$$ExternalSyntheticLambda1;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Consumer;


# instance fields
.field public final synthetic f$0:Ljava/util/List;


# direct methods
.method public synthetic constructor <init>(Ljava/util/List;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/codehaus/groovy/classgen/Verifier$$ExternalSyntheticLambda1;->f$0:Ljava/util/List;

    return-void
.end method


# virtual methods
.method public final accept(Ljava/lang/Object;)V
    .locals 1

    iget-object v0, p0, Lorg/codehaus/groovy/classgen/Verifier$$ExternalSyntheticLambda1;->f$0:Ljava/util/List;

    check-cast p1, Lorg/codehaus/groovy/ast/stmt/Statement;

    invoke-static {v0, p1}, Lorg/codehaus/groovy/classgen/Verifier;->lambda$addInitialization$3(Ljava/util/List;Lorg/codehaus/groovy/ast/stmt/Statement;)V

    return-void
.end method
