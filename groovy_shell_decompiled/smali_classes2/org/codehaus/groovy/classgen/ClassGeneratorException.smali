.class public Lorg/codehaus/groovy/classgen/ClassGeneratorException;
.super Ljava/lang/RuntimeException;
.source "ClassGeneratorException.java"


# static fields
.field private static final serialVersionUID:J = 0x61e63a1d0e7ef41L


# direct methods
.method public constructor <init>(Ljava/lang/String;)V
    .locals 0

    .line 29
    invoke-direct {p0, p1}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/String;)V

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;Ljava/lang/Throwable;)V
    .locals 0

    .line 33
    invoke-direct {p0, p1, p2}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/String;Ljava/lang/Throwable;)V

    return-void
.end method
