.class public final synthetic Lorg/codehaus/groovy/classgen/Verifier$$ExternalSyntheticLambda0;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Consumer;


# instance fields
.field public final synthetic f$0:Ljava/lang/String;


# direct methods
.method public synthetic constructor <init>(Ljava/lang/String;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/codehaus/groovy/classgen/Verifier$$ExternalSyntheticLambda0;->f$0:Ljava/lang/String;

    return-void
.end method


# virtual methods
.method public final accept(Ljava/lang/Object;)V
    .locals 1

    iget-object v0, p0, Lorg/codehaus/groovy/classgen/Verifier$$ExternalSyntheticLambda0;->f$0:Ljava/lang/String;

    check-cast p1, Lgroovyjarjarasm/asm/MethodVisitor;

    invoke-static {v0, p1}, Lorg/codehaus/groovy/classgen/Verifier;->lambda$setMetaClassFieldIfNotExists$0(Ljava/lang/String;Lgroovyjarjarasm/asm/MethodVisitor;)V

    return-void
.end method
