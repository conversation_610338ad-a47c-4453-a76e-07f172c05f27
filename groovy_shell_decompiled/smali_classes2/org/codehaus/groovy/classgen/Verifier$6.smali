.class Lorg/codehaus/groovy/classgen/Verifier$6;
.super Lorg/codehaus/groovy/ast/CodeVisitorSupport;
.source "Verifier.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/codehaus/groovy/classgen/Verifier;->visitConstructor(Lorg/codehaus/groovy/ast/ConstructorNode;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field private inClosure:Z

.field private inSpecialConstructorCall:Z

.field final synthetic this$0:Lorg/codehaus/groovy/classgen/Verifier;

.field final synthetic val$node:Lorg/codehaus/groovy/ast/ConstructorNode;


# direct methods
.method constructor <init>(Lorg/codehaus/groovy/classgen/Verifier;Lorg/codehaus/groovy/ast/ConstructorNode;)V
    .locals 0

    .line 529
    iput-object p1, p0, Lorg/codehaus/groovy/classgen/Verifier$6;->this$0:Lorg/codehaus/groovy/classgen/Verifier;

    iput-object p2, p0, Lorg/codehaus/groovy/classgen/Verifier$6;->val$node:Lorg/codehaus/groovy/ast/ConstructorNode;

    invoke-direct {p0}, Lorg/codehaus/groovy/ast/CodeVisitorSupport;-><init>()V

    return-void
.end method

.method private isNonStaticMemberAccess(Lorg/codehaus/groovy/ast/expr/VariableExpression;)Z
    .locals 1

    .line 577
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->getAccessedVariable()Lorg/codehaus/groovy/ast/Variable;

    move-result-object p1

    .line 578
    iget-boolean v0, p0, Lorg/codehaus/groovy/classgen/Verifier$6;->inClosure:Z

    if-nez v0, :cond_0

    if-eqz p1, :cond_0

    invoke-interface {p1}, Lorg/codehaus/groovy/ast/Variable;->getModifiers()I

    move-result v0

    invoke-static {v0}, Ljava/lang/reflect/Modifier;->isStatic(I)Z

    move-result v0

    if-nez v0, :cond_0

    instance-of v0, p1, Lorg/codehaus/groovy/ast/DynamicVariable;

    if-nez v0, :cond_0

    instance-of p1, p1, Lorg/codehaus/groovy/ast/Parameter;

    if-nez p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method private isThisObjectExpression(Lorg/codehaus/groovy/ast/expr/MethodCallExpression;)Z
    .locals 3

    .line 583
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->isImplicitThis()Z

    move-result v0

    const/4 v1, 0x1

    if-eqz v0, :cond_0

    return v1

    .line 585
    :cond_0
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->getObjectExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    instance-of v0, v0, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    const/4 v2, 0x0

    if-eqz v0, :cond_3

    .line 586
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->getObjectExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    check-cast p1, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    .line 587
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->isThisExpression()Z

    move-result v0

    if-nez v0, :cond_2

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->isSuperExpression()Z

    move-result p1

    if-eqz p1, :cond_1

    goto :goto_0

    :cond_1
    move v1, v2

    :cond_2
    :goto_0
    return v1

    :cond_3
    return v2
.end method

.method private newVariableError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)Lgroovy/lang/GroovyRuntimeException;
    .locals 3

    .line 594
    new-instance v0, Lorg/codehaus/groovy/syntax/RuntimeParserException;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "Cannot reference \'"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v1, "\' before supertype constructor has been called. Possible causes:\nYou attempted to access an instance field, method, or property.\nYou attempted to construct a non-static inner class."

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {v0, p1, p2}, Lorg/codehaus/groovy/syntax/RuntimeParserException;-><init>(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 598
    iget-object p1, p0, Lorg/codehaus/groovy/classgen/Verifier$6;->this$0:Lorg/codehaus/groovy/classgen/Verifier;

    invoke-virtual {p1}, Lorg/codehaus/groovy/classgen/Verifier;->getClassNode()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getModule()Lorg/codehaus/groovy/ast/ModuleNode;

    move-result-object p1

    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/syntax/RuntimeParserException;->setModule(Lorg/codehaus/groovy/ast/ModuleNode;)V

    return-object v0
.end method


# virtual methods
.method public visitClosureExpression(Lorg/codehaus/groovy/ast/expr/ClosureExpression;)V
    .locals 2

    .line 532
    iget-boolean v0, p0, Lorg/codehaus/groovy/classgen/Verifier$6;->inClosure:Z

    const/4 v1, 0x1

    .line 533
    iput-boolean v1, p0, Lorg/codehaus/groovy/classgen/Verifier$6;->inClosure:Z

    .line 534
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/CodeVisitorSupport;->visitClosureExpression(Lorg/codehaus/groovy/ast/expr/ClosureExpression;)V

    .line 535
    iput-boolean v0, p0, Lorg/codehaus/groovy/classgen/Verifier$6;->inClosure:Z

    return-void
.end method

.method public visitConstructorCallExpression(Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;)V
    .locals 2

    .line 540
    iget-boolean v0, p0, Lorg/codehaus/groovy/classgen/Verifier$6;->inSpecialConstructorCall:Z

    .line 541
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;->isSpecialCall()Z

    move-result v1

    or-int/2addr v1, v0

    iput-boolean v1, p0, Lorg/codehaus/groovy/classgen/Verifier$6;->inSpecialConstructorCall:Z

    .line 542
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/CodeVisitorSupport;->visitConstructorCallExpression(Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;)V

    .line 543
    iput-boolean v0, p0, Lorg/codehaus/groovy/classgen/Verifier$6;->inSpecialConstructorCall:Z

    return-void
.end method

.method public visitMethodCallExpression(Lorg/codehaus/groovy/ast/expr/MethodCallExpression;)V
    .locals 2

    .line 548
    iget-boolean v0, p0, Lorg/codehaus/groovy/classgen/Verifier$6;->inSpecialConstructorCall:Z

    if-eqz v0, :cond_3

    invoke-direct {p0, p1}, Lorg/codehaus/groovy/classgen/Verifier$6;->isThisObjectExpression(Lorg/codehaus/groovy/ast/expr/MethodCallExpression;)Z

    move-result v0

    if-eqz v0, :cond_3

    .line 549
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->getMethodTarget()Lorg/codehaus/groovy/ast/MethodNode;

    move-result-object v0

    if-eqz v0, :cond_1

    .line 550
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/MethodNode;->isStatic()Z

    move-result v1

    if-nez v1, :cond_0

    iget-object v1, p0, Lorg/codehaus/groovy/classgen/Verifier$6;->this$0:Lorg/codehaus/groovy/classgen/Verifier;

    invoke-static {v1}, Lorg/codehaus/groovy/classgen/Verifier;->access$000(Lorg/codehaus/groovy/classgen/Verifier;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/ClassNode;->getOuterClasses()Ljava/util/List;

    move-result-object v1

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/MethodNode;->getDeclaringClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    invoke-interface {v1, v0}, Ljava/util/List;->contains(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_0

    goto :goto_0

    .line 557
    :cond_0
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->getMethod()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    invoke-virtual {v0, p0}, Lorg/codehaus/groovy/ast/expr/Expression;->visit(Lorg/codehaus/groovy/ast/GroovyCodeVisitor;)V

    .line 558
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->getArguments()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    invoke-virtual {p1, p0}, Lorg/codehaus/groovy/ast/expr/Expression;->visit(Lorg/codehaus/groovy/ast/GroovyCodeVisitor;)V

    goto :goto_1

    .line 551
    :cond_1
    :goto_0
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->isImplicitThis()Z

    move-result v0

    if-nez v0, :cond_2

    .line 552
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->getObjectExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/Expression;->getText()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->getObjectExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    invoke-direct {p0, v0, p1}, Lorg/codehaus/groovy/classgen/Verifier$6;->newVariableError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)Lgroovy/lang/GroovyRuntimeException;

    move-result-object p1

    throw p1

    .line 554
    :cond_2
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->getMethodAsString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->getMethod()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    invoke-direct {p0, v0, p1}, Lorg/codehaus/groovy/classgen/Verifier$6;->newVariableError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)Lgroovy/lang/GroovyRuntimeException;

    move-result-object p1

    throw p1

    .line 560
    :cond_3
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/CodeVisitorSupport;->visitMethodCallExpression(Lorg/codehaus/groovy/ast/expr/MethodCallExpression;)V

    :goto_1
    return-void
.end method

.method public visitVariableExpression(Lorg/codehaus/groovy/ast/expr/VariableExpression;)V
    .locals 2

    .line 567
    iget-boolean v0, p0, Lorg/codehaus/groovy/classgen/Verifier$6;->inSpecialConstructorCall:Z

    if-eqz v0, :cond_2

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->isThisExpression()Z

    move-result v0

    if-nez v0, :cond_0

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->isSuperExpression()Z

    move-result v0

    if-nez v0, :cond_0

    invoke-direct {p0, p1}, Lorg/codehaus/groovy/classgen/Verifier$6;->isNonStaticMemberAccess(Lorg/codehaus/groovy/ast/expr/VariableExpression;)Z

    move-result v0

    if-eqz v0, :cond_2

    .line 568
    :cond_0
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->getName()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->getLineNumber()I

    move-result v1

    if-lez v1, :cond_1

    goto :goto_0

    :cond_1
    iget-object p1, p0, Lorg/codehaus/groovy/classgen/Verifier$6;->val$node:Lorg/codehaus/groovy/ast/ConstructorNode;

    :goto_0
    invoke-direct {p0, v0, p1}, Lorg/codehaus/groovy/classgen/Verifier$6;->newVariableError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)Lgroovy/lang/GroovyRuntimeException;

    move-result-object p1

    throw p1

    :cond_2
    return-void
.end method
