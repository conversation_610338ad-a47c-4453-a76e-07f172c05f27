.class public Lorg/codehaus/groovy/classgen/VariableScopeVisitor;
.super Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;
.source "VariableScopeVisitor.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/codehaus/groovy/classgen/VariableScopeVisitor$StateStackElement;
    }
.end annotation


# instance fields
.field private currentClass:Lorg/codehaus/groovy/ast/ClassNode;

.field private currentScope:Lorg/codehaus/groovy/ast/VariableScope;

.field private inClosure:Z

.field private inConstructor:Z

.field private inSpecialConstructorCall:Z

.field private final recurseInnerClasses:Z

.field private final source:Lorg/codehaus/groovy/control/SourceUnit;

.field private final stateStack:Ljava/util/Deque;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Deque<",
            "Lorg/codehaus/groovy/classgen/VariableScopeVisitor$StateStackElement;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lorg/codehaus/groovy/control/SourceUnit;)V
    .locals 1

    const/4 v0, 0x0

    .line 96
    invoke-direct {p0, p1, v0}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;-><init>(Lorg/codehaus/groovy/control/SourceUnit;Z)V

    return-void
.end method

.method public constructor <init>(Lorg/codehaus/groovy/control/SourceUnit;Z)V
    .locals 1

    .line 89
    invoke-direct {p0}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;-><init>()V

    .line 74
    new-instance v0, Ljava/util/LinkedList;

    invoke-direct {v0}, Ljava/util/LinkedList;-><init>()V

    iput-object v0, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->stateStack:Ljava/util/Deque;

    .line 90
    iput-object p1, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->source:Lorg/codehaus/groovy/control/SourceUnit;

    .line 91
    new-instance p1, Lorg/codehaus/groovy/ast/VariableScope;

    invoke-direct {p1}, Lorg/codehaus/groovy/ast/VariableScope;-><init>()V

    iput-object p1, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->currentScope:Lorg/codehaus/groovy/ast/VariableScope;

    .line 92
    iput-boolean p2, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->recurseInnerClasses:Z

    return-void
.end method

.method private checkFinalFieldAccess(Lorg/codehaus/groovy/ast/expr/Expression;)V
    .locals 3

    .line 293
    new-instance v0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor$$ExternalSyntheticLambda0;

    invoke-direct {v0, p0}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor$$ExternalSyntheticLambda0;-><init>(Lorg/codehaus/groovy/classgen/VariableScopeVisitor;)V

    .line 303
    instance-of v1, p1, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    if-eqz v1, :cond_0

    .line 304
    move-object v1, p1

    check-cast v1, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    invoke-interface {v0, v1, p1}, Ljava/util/function/BiConsumer;->accept(Ljava/lang/Object;Ljava/lang/Object;)V

    goto :goto_1

    .line 305
    :cond_0
    instance-of v1, p1, Lorg/codehaus/groovy/ast/expr/TupleExpression;

    if-eqz v1, :cond_1

    .line 306
    move-object v1, p1

    check-cast v1, Lorg/codehaus/groovy/ast/expr/TupleExpression;

    .line 307
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/expr/TupleExpression;->getExpressions()Ljava/util/List;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_1

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lorg/codehaus/groovy/ast/expr/Expression;

    .line 308
    check-cast v2, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    invoke-interface {v0, v2, p1}, Ljava/util/function/BiConsumer;->accept(Ljava/lang/Object;Ljava/lang/Object;)V

    goto :goto_0

    :cond_1
    :goto_1
    return-void
.end method

.method private checkPropertyOnExplicitThis(Lorg/codehaus/groovy/ast/expr/PropertyExpression;)V
    .locals 2

    .line 319
    iget-object v0, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->currentScope:Lorg/codehaus/groovy/ast/VariableScope;

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/VariableScope;->isInStaticContext()Z

    move-result v0

    if-nez v0, :cond_0

    return-void

    .line 320
    :cond_0
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->getObjectExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    .line 321
    instance-of v1, v0, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    if-nez v1, :cond_1

    return-void

    .line 322
    :cond_1
    check-cast v0, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    .line 323
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->getName()Ljava/lang/String;

    move-result-object v0

    const-string v1, "this"

    invoke-virtual {v0, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_2

    return-void

    .line 324
    :cond_2
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->getPropertyAsString()Ljava/lang/String;

    move-result-object v0

    if-eqz v0, :cond_5

    const-string v1, "class"

    .line 325
    invoke-virtual {v0, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_3

    goto :goto_0

    .line 326
    :cond_3
    iget-object v1, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-direct {p0, v1, v0}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->findClassMember(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/Variable;

    move-result-object v0

    if-nez v0, :cond_4

    return-void

    .line 328
    :cond_4
    invoke-direct {p0, v0, p1}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->checkVariableContextAccess(Lorg/codehaus/groovy/ast/Variable;Lorg/codehaus/groovy/ast/expr/Expression;)V

    :cond_5
    :goto_0
    return-void
.end method

.method private checkVariableContextAccess(Lorg/codehaus/groovy/ast/Variable;Lorg/codehaus/groovy/ast/expr/Expression;)V
    .locals 2

    .line 332
    invoke-interface {p1}, Lorg/codehaus/groovy/ast/Variable;->isInStaticContext()Z

    move-result v0

    if-eqz v0, :cond_2

    .line 333
    iget-boolean v0, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->inConstructor:Z

    if-eqz v0, :cond_1

    iget-object v0, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->isEnum()Z

    move-result v0

    if-eqz v0, :cond_1

    instance-of v0, p1, Lorg/codehaus/groovy/ast/FieldNode;

    if-eqz v0, :cond_1

    iget-object v0, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    move-object v1, p1

    check-cast v1, Lorg/codehaus/groovy/ast/FieldNode;

    .line 334
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/FieldNode;->getDeclaringClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    invoke-virtual {v0, v1}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_1

    .line 335
    invoke-interface {p1}, Lorg/codehaus/groovy/ast/Variable;->getModifiers()I

    move-result v0

    invoke-static {v0}, Ljava/lang/reflect/Modifier;->isFinal(I)Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-interface {p1}, Lorg/codehaus/groovy/ast/Variable;->getOriginType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/ast/ClassHelper;->isStaticConstantInitializerType(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v0

    if-nez v0, :cond_1

    .line 336
    invoke-interface {p1}, Lorg/codehaus/groovy/ast/Variable;->getOriginType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v0

    const-string v1, "String"

    invoke-virtual {v1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_1

    .line 337
    :cond_0
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Cannot refer to the static enum field \'"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-interface {p1}, Lorg/codehaus/groovy/ast/Variable;->getName()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, "\' within an initializer"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    :cond_1
    return-void

    .line 343
    :cond_2
    iget-object v0, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->currentScope:Lorg/codehaus/groovy/ast/VariableScope;

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/VariableScope;->isInStaticContext()Z

    move-result v0

    if-nez v0, :cond_3

    return-void

    .line 345
    :cond_3
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-interface {p1}, Lorg/codehaus/groovy/ast/Variable;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, " is declared in a dynamic context, but you tried to access it from a static context."

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0, p2}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 348
    iget-object p2, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->currentScope:Lorg/codehaus/groovy/ast/VariableScope;

    new-instance v0, Lorg/codehaus/groovy/ast/DynamicVariable;

    invoke-interface {p1}, Lorg/codehaus/groovy/ast/Variable;->getName()Ljava/lang/String;

    move-result-object p1

    iget-object v1, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->currentScope:Lorg/codehaus/groovy/ast/VariableScope;

    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/VariableScope;->isInStaticContext()Z

    move-result v1

    invoke-direct {v0, p1, v1}, Lorg/codehaus/groovy/ast/DynamicVariable;-><init>(Ljava/lang/String;Z)V

    invoke-virtual {p2, v0}, Lorg/codehaus/groovy/ast/VariableScope;->putDeclaredVariable(Lorg/codehaus/groovy/ast/Variable;)V

    return-void
.end method

.method private declare(Lorg/codehaus/groovy/ast/Variable;Lorg/codehaus/groovy/ast/ASTNode;)V
    .locals 4

    .line 136
    invoke-virtual {p2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    const-class v1, Lorg/codehaus/groovy/ast/FieldNode;

    const-string v2, "class"

    if-ne v0, v1, :cond_0

    const-string v0, "field"

    goto :goto_0

    .line 139
    :cond_0
    invoke-virtual {p2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    const-class v1, Lorg/codehaus/groovy/ast/PropertyNode;

    if-ne v0, v1, :cond_1

    const-string v0, "property"

    goto :goto_0

    :cond_1
    const-string v2, "scope"

    const-string v0, "variable"

    .line 144
    :goto_0
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "The current "

    .line 145
    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v2, " already contains a "

    .line 146
    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v0, " of the name "

    .line 147
    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-interface {p1}, Lorg/codehaus/groovy/ast/Variable;->getName()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 149
    iget-object v0, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->currentScope:Lorg/codehaus/groovy/ast/VariableScope;

    invoke-interface {p1}, Lorg/codehaus/groovy/ast/Variable;->getName()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Lorg/codehaus/groovy/ast/VariableScope;->getDeclaredVariable(Ljava/lang/String;)Lorg/codehaus/groovy/ast/Variable;

    move-result-object v0

    if-eqz v0, :cond_2

    .line 150
    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    return-void

    .line 154
    :cond_2
    iget-object v0, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->currentScope:Lorg/codehaus/groovy/ast/VariableScope;

    :cond_3
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/VariableScope;->getParent()Lorg/codehaus/groovy/ast/VariableScope;

    move-result-object v0

    if-eqz v0, :cond_5

    .line 158
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/VariableScope;->getClassScope()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v2

    if-eqz v2, :cond_4

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/VariableScope;->getClassScope()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v2

    invoke-static {v2}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->isAnonymous(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v2

    if-nez v2, :cond_4

    goto :goto_1

    .line 160
    :cond_4
    invoke-interface {p1}, Lorg/codehaus/groovy/ast/Variable;->getName()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Lorg/codehaus/groovy/ast/VariableScope;->getDeclaredVariable(Ljava/lang/String;)Lorg/codehaus/groovy/ast/Variable;

    move-result-object v2

    if-eqz v2, :cond_3

    .line 162
    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0, p2}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 167
    :cond_5
    :goto_1
    iget-object p2, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->currentScope:Lorg/codehaus/groovy/ast/VariableScope;

    invoke-virtual {p2, p1}, Lorg/codehaus/groovy/ast/VariableScope;->putDeclaredVariable(Lorg/codehaus/groovy/ast/Variable;)V

    return-void
.end method

.method private declare(Lorg/codehaus/groovy/ast/expr/VariableExpression;)V
    .locals 1

    .line 127
    iget-object v0, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->currentScope:Lorg/codehaus/groovy/ast/VariableScope;

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/VariableScope;->isInStaticContext()Z

    move-result v0

    invoke-virtual {p1, v0}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->setInStaticContext(Z)V

    .line 128
    invoke-direct {p0, p1, p1}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->declare(Lorg/codehaus/groovy/ast/Variable;Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 129
    invoke-virtual {p1, p1}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->setAccessedVariable(Lorg/codehaus/groovy/ast/Variable;)V

    return-void
.end method

.method private findClassMember(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/Variable;
    .locals 8

    .line 171
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->isAbstract()Z

    move-result v0

    :goto_0
    const/4 v7, 0x0

    if-eqz p1, :cond_c

    .line 173
    sget-object v1, Lorg/codehaus/groovy/ast/ClassHelper;->OBJECT_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {p1, v1}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_c

    .line 174
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getFields()Ljava/util/List;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :cond_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_1

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lorg/codehaus/groovy/ast/FieldNode;

    .line 175
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {p2, v3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_0

    return-object v2

    .line 178
    :cond_1
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getProperties()Ljava/util/List;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :cond_2
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_3

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lorg/codehaus/groovy/ast/PropertyNode;

    .line 179
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/PropertyNode;->getName()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {p2, v3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_2

    return-object v2

    .line 182
    :cond_3
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getMethods()Ljava/util/List;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :cond_4
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_8

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lorg/codehaus/groovy/ast/MethodNode;

    if-nez v0, :cond_5

    .line 183
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/MethodNode;->isAbstract()Z

    move-result v3

    if-nez v3, :cond_4

    :cond_5
    invoke-static {v2}, Lorg/apache/groovy/ast/tools/MethodNodeUtils;->getPropertyName(Lorg/codehaus/groovy/ast/MethodNode;)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {p2, v3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_4

    .line 185
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getSuperClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->getAllProperties(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/util/List;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_6
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_7

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/ast/PropertyNode;

    .line 186
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/PropertyNode;->getName()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {p2, v3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_6

    return-object v1

    .line 188
    :cond_7
    new-instance v0, Lorg/codehaus/groovy/ast/FieldNode;

    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/MethodNode;->getModifiers()I

    move-result v1

    and-int/lit8 v3, v1, 0xf

    sget-object v4, Lorg/codehaus/groovy/ast/ClassHelper;->DYNAMIC_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    const/4 v6, 0x0

    move-object v1, v0

    move-object v2, p2

    move-object v5, p1

    invoke-direct/range {v1 .. v6}, Lorg/codehaus/groovy/ast/FieldNode;-><init>(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;)V

    const/4 p2, 0x1

    .line 189
    invoke-virtual {v0, p2}, Lorg/codehaus/groovy/ast/FieldNode;->setHasNoRealSourcePosition(Z)V

    .line 190
    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/ast/FieldNode;->setDeclaringClass(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 191
    invoke-virtual {v0, p2}, Lorg/codehaus/groovy/ast/FieldNode;->setSynthetic(Z)V

    .line 193
    new-instance p2, Lorg/codehaus/groovy/ast/PropertyNode;

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/FieldNode;->getModifiers()I

    move-result v1

    invoke-direct {p2, v0, v1, v7, v7}, Lorg/codehaus/groovy/ast/PropertyNode;-><init>(Lorg/codehaus/groovy/ast/FieldNode;ILorg/codehaus/groovy/ast/stmt/Statement;Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 194
    invoke-virtual {p2, p1}, Lorg/codehaus/groovy/ast/PropertyNode;->setDeclaringClass(Lorg/codehaus/groovy/ast/ClassNode;)V

    return-object p2

    .line 199
    :cond_8
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getAllInterfaces()Ljava/util/Set;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :cond_9
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_b

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lorg/codehaus/groovy/ast/ClassNode;

    .line 200
    invoke-virtual {v2, p2}, Lorg/codehaus/groovy/ast/ClassNode;->getDeclaredField(Ljava/lang/String;)Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object v3

    if-eqz v3, :cond_a

    return-object v3

    .line 202
    :cond_a
    invoke-virtual {v2, p2}, Lorg/codehaus/groovy/ast/ClassNode;->getProperty(Ljava/lang/String;)Lorg/codehaus/groovy/ast/PropertyNode;

    move-result-object v2

    if-eqz v2, :cond_9

    return-object v2

    .line 173
    :cond_b
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getSuperClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    goto/16 :goto_0

    :cond_c
    return-object v7
.end method

.method private findVariableDeclaration(Ljava/lang/String;)Lorg/codehaus/groovy/ast/Variable;
    .locals 9

    const-string v0, "super"

    .line 211
    invoke-virtual {v0, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    const/4 v1, 0x0

    if-nez v0, :cond_14

    const-string v0, "this"

    invoke-virtual {v0, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    goto/16 :goto_b

    .line 214
    :cond_0
    iget-object v0, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->currentScope:Lorg/codehaus/groovy/ast/VariableScope;

    const/4 v2, 0x0

    move v3, v2

    :goto_0
    const/4 v4, 0x1

    if-nez v3, :cond_2

    .line 218
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/VariableScope;->isInStaticContext()Z

    move-result v3

    if-eqz v3, :cond_1

    goto :goto_1

    :cond_1
    move v3, v2

    goto :goto_2

    :cond_2
    :goto_1
    move v3, v4

    .line 220
    :goto_2
    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/ast/VariableScope;->getDeclaredVariable(Ljava/lang/String;)Lorg/codehaus/groovy/ast/Variable;

    move-result-object v5

    if-eqz v5, :cond_3

    goto/16 :goto_8

    .line 226
    :cond_3
    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/ast/VariableScope;->getReferencedLocalVariable(Ljava/lang/String;)Lorg/codehaus/groovy/ast/Variable;

    move-result-object v5

    if-eqz v5, :cond_4

    goto/16 :goto_8

    .line 232
    :cond_4
    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/ast/VariableScope;->getReferencedClassVariable(Ljava/lang/String;)Lorg/codehaus/groovy/ast/Variable;

    move-result-object v5

    if-eqz v5, :cond_5

    goto/16 :goto_8

    .line 238
    :cond_5
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/VariableScope;->getClassScope()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v5

    if-eqz v5, :cond_13

    .line 240
    invoke-direct {p0, v5, p1}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->findClassMember(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/Variable;

    move-result-object v6

    if-nez v3, :cond_7

    .line 241
    iget-boolean v7, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->inSpecialConstructorCall:Z

    if-eqz v7, :cond_6

    goto :goto_3

    :cond_6
    move v7, v2

    goto :goto_4

    :cond_7
    :goto_3
    move v7, v4

    :goto_4
    if-nez v6, :cond_a

    .line 242
    invoke-virtual {v5}, Lorg/codehaus/groovy/ast/ClassNode;->getOuterClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v8

    if-eqz v8, :cond_a

    invoke-static {v5}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->isAnonymous(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v8

    if-nez v8, :cond_a

    if-nez v7, :cond_9

    .line 243
    invoke-virtual {v5}, Lorg/codehaus/groovy/ast/ClassNode;->getModifiers()I

    move-result v6

    invoke-static {v6}, Ljava/lang/reflect/Modifier;->isStatic(I)Z

    move-result v6

    if-eqz v6, :cond_8

    goto :goto_5

    :cond_8
    move v7, v2

    goto :goto_6

    :cond_9
    :goto_5
    move v7, v4

    .line 244
    :goto_6
    invoke-virtual {v5}, Lorg/codehaus/groovy/ast/ClassNode;->getOuterClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v5

    invoke-direct {p0, v5, p1}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->findClassMember(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/Variable;

    move-result-object v6

    goto :goto_4

    :cond_a
    if-eqz v6, :cond_c

    if-eqz v7, :cond_b

    .line 248
    invoke-interface {v6}, Lorg/codehaus/groovy/ast/Variable;->isInStaticContext()Z

    move-result v5

    if-eqz v5, :cond_c

    goto :goto_7

    :cond_b
    invoke-virtual {v5}, Lorg/codehaus/groovy/ast/ClassNode;->isScript()Z

    move-result v5

    if-nez v5, :cond_c

    :goto_7
    move-object v1, v6

    .line 253
    :cond_c
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/VariableScope;->getClassScope()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v5

    invoke-static {v5}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->isAnonymous(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v5

    if-nez v5, :cond_13

    move-object v5, v1

    :goto_8
    if-nez v5, :cond_d

    .line 258
    new-instance v5, Lorg/codehaus/groovy/ast/DynamicVariable;

    invoke-direct {v5, p1, v3}, Lorg/codehaus/groovy/ast/DynamicVariable;-><init>(Ljava/lang/String;Z)V

    .line 261
    :cond_d
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/VariableScope;->isClassScope()Z

    move-result v1

    if-eqz v1, :cond_e

    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/ast/VariableScope;->isReferencedLocalVariable(Ljava/lang/String;)Z

    move-result v1

    if-eqz v1, :cond_f

    .line 262
    :cond_e
    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/ast/VariableScope;->isReferencedClassVariable(Ljava/lang/String;)Z

    move-result v1

    if-eqz v1, :cond_10

    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/ast/VariableScope;->getDeclaredVariable(Ljava/lang/String;)Lorg/codehaus/groovy/ast/Variable;

    move-result-object p1

    if-nez p1, :cond_10

    :cond_f
    move v2, v4

    .line 264
    :cond_10
    iget-object p1, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->currentScope:Lorg/codehaus/groovy/ast/VariableScope;

    :goto_9
    if-eq p1, v0, :cond_12

    if-eqz v2, :cond_11

    .line 267
    invoke-virtual {p1, v5}, Lorg/codehaus/groovy/ast/VariableScope;->putReferencedClassVariable(Lorg/codehaus/groovy/ast/Variable;)V

    goto :goto_a

    .line 269
    :cond_11
    invoke-virtual {p1, v5}, Lorg/codehaus/groovy/ast/VariableScope;->putReferencedLocalVariable(Lorg/codehaus/groovy/ast/Variable;)V

    .line 271
    :goto_a
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/VariableScope;->getParent()Lorg/codehaus/groovy/ast/VariableScope;

    move-result-object p1

    goto :goto_9

    :cond_12
    return-object v5

    .line 255
    :cond_13
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/VariableScope;->getParent()Lorg/codehaus/groovy/ast/VariableScope;

    move-result-object v0

    goto/16 :goto_0

    :cond_14
    :goto_b
    return-object v1
.end method

.method private static isAnonymous(Lorg/codehaus/groovy/ast/ClassNode;)Z
    .locals 1

    .line 278
    instance-of v0, p0, Lorg/codehaus/groovy/ast/InnerClassNode;

    if-eqz v0, :cond_0

    move-object v0, p0

    check-cast v0, Lorg/codehaus/groovy/ast/InnerClassNode;

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/InnerClassNode;->isAnonymous()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/ClassNode;->isEnum()Z

    move-result p0

    if-nez p0, :cond_0

    const/4 p0, 0x1

    goto :goto_0

    :cond_0
    const/4 p0, 0x0

    :goto_0
    return p0
.end method

.method private markClosureSharedVariables()V
    .locals 3

    .line 282
    iget-object v0, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->currentScope:Lorg/codehaus/groovy/ast/VariableScope;

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/VariableScope;->getReferencedLocalVariablesIterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    .line 283
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/ast/Variable;

    const/4 v2, 0x1

    .line 284
    invoke-interface {v1, v2}, Lorg/codehaus/groovy/ast/Variable;->setClosureSharedVariable(Z)V

    goto :goto_0

    :cond_0
    return-void
.end method

.method private popState()V
    .locals 2

    .line 119
    iget-object v0, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->stateStack:Ljava/util/Deque;

    invoke-interface {v0}, Ljava/util/Deque;->pop()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor$StateStackElement;

    .line 120
    iget-object v1, v0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor$StateStackElement;->clazz:Lorg/codehaus/groovy/ast/ClassNode;

    iput-object v1, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    .line 121
    iget-object v1, v0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor$StateStackElement;->scope:Lorg/codehaus/groovy/ast/VariableScope;

    iput-object v1, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->currentScope:Lorg/codehaus/groovy/ast/VariableScope;

    .line 122
    iget-boolean v1, v0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor$StateStackElement;->inClosure:Z

    iput-boolean v1, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->inClosure:Z

    .line 123
    iget-boolean v0, v0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor$StateStackElement;->inConstructor:Z

    iput-boolean v0, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->inConstructor:Z

    return-void
.end method

.method private pushState()V
    .locals 1

    .line 115
    iget-object v0, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->currentScope:Lorg/codehaus/groovy/ast/VariableScope;

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/VariableScope;->isInStaticContext()Z

    move-result v0

    invoke-direct {p0, v0}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->pushState(Z)V

    return-void
.end method

.method private pushState(Z)V
    .locals 6

    .line 109
    iget-object v0, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->stateStack:Ljava/util/Deque;

    new-instance v1, Lorg/codehaus/groovy/classgen/VariableScopeVisitor$StateStackElement;

    iget-object v2, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    iget-object v3, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->currentScope:Lorg/codehaus/groovy/ast/VariableScope;

    iget-boolean v4, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->inClosure:Z

    iget-boolean v5, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->inConstructor:Z

    invoke-direct {v1, v2, v3, v4, v5}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor$StateStackElement;-><init>(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/VariableScope;ZZ)V

    invoke-interface {v0, v1}, Ljava/util/Deque;->push(Ljava/lang/Object;)V

    .line 110
    new-instance v0, Lorg/codehaus/groovy/ast/VariableScope;

    iget-object v1, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->currentScope:Lorg/codehaus/groovy/ast/VariableScope;

    invoke-direct {v0, v1}, Lorg/codehaus/groovy/ast/VariableScope;-><init>(Lorg/codehaus/groovy/ast/VariableScope;)V

    iput-object v0, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->currentScope:Lorg/codehaus/groovy/ast/VariableScope;

    .line 111
    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/ast/VariableScope;->setInStaticContext(Z)V

    return-void
.end method


# virtual methods
.method protected getSourceUnit()Lorg/codehaus/groovy/control/SourceUnit;
    .locals 1

    .line 101
    iget-object v0, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->source:Lorg/codehaus/groovy/control/SourceUnit;

    return-object v0
.end method

.method public synthetic lambda$checkFinalFieldAccess$0$org-codehaus-groovy-classgen-VariableScopeVisitor(Lorg/codehaus/groovy/ast/expr/VariableExpression;Lorg/codehaus/groovy/ast/ASTNode;)V
    .locals 2

    .line 294
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->getAccessedVariable()Lorg/codehaus/groovy/ast/Variable;

    move-result-object p1

    if-eqz p1, :cond_0

    .line 296
    invoke-interface {p1}, Lorg/codehaus/groovy/ast/Variable;->getModifiers()I

    move-result v0

    invoke-static {v0}, Ljava/lang/reflect/Modifier;->isFinal(I)Z

    move-result v0

    if-eqz v0, :cond_0

    instance-of v0, p1, Lorg/codehaus/groovy/ast/Parameter;

    if-eqz v0, :cond_0

    .line 297
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Cannot assign a value to final variable \'"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-interface {p1}, Lorg/codehaus/groovy/ast/Variable;->getName()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, "\'"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    :cond_0
    return-void
.end method

.method public prepareVisit(Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 1

    .line 357
    iput-object p1, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    .line 358
    iget-object v0, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->currentScope:Lorg/codehaus/groovy/ast/VariableScope;

    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/ast/VariableScope;->setClassScope(Lorg/codehaus/groovy/ast/ClassNode;)V

    return-void
.end method

.method public visitBinaryExpression(Lorg/codehaus/groovy/ast/expr/BinaryExpression;)V
    .locals 1

    .line 463
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;->visitBinaryExpression(Lorg/codehaus/groovy/ast/expr/BinaryExpression;)V

    .line 465
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/BinaryExpression;->getOperation()Lorg/codehaus/groovy/syntax/Token;

    move-result-object v0

    invoke-virtual {v0}, Lorg/codehaus/groovy/syntax/Token;->getType()I

    move-result v0

    invoke-static {v0}, Lorg/codehaus/groovy/syntax/Types;->isAssignment(I)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 466
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/BinaryExpression;->getLeftExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    invoke-direct {p0, p1}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->checkFinalFieldAccess(Lorg/codehaus/groovy/ast/expr/Expression;)V

    :cond_0
    return-void
.end method

.method public visitBlockStatement(Lorg/codehaus/groovy/ast/stmt/BlockStatement;)V
    .locals 1

    .line 421
    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->pushState()V

    .line 422
    iget-object v0, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->currentScope:Lorg/codehaus/groovy/ast/VariableScope;

    invoke-virtual {p1, v0}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->setVariableScope(Lorg/codehaus/groovy/ast/VariableScope;)V

    .line 423
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;->visitBlockStatement(Lorg/codehaus/groovy/ast/stmt/BlockStatement;)V

    .line 424
    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->popState()V

    return-void
.end method

.method public visitCatchStatement(Lorg/codehaus/groovy/ast/stmt/CatchStatement;)V
    .locals 2

    .line 429
    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->pushState()V

    .line 430
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/stmt/CatchStatement;->getVariable()Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v0

    .line 431
    iget-object v1, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->currentScope:Lorg/codehaus/groovy/ast/VariableScope;

    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/VariableScope;->isInStaticContext()Z

    move-result v1

    invoke-virtual {v0, v1}, Lorg/codehaus/groovy/ast/Parameter;->setInStaticContext(Z)V

    .line 432
    invoke-direct {p0, v0, p1}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->declare(Lorg/codehaus/groovy/ast/Variable;Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 433
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;->visitCatchStatement(Lorg/codehaus/groovy/ast/stmt/CatchStatement;)V

    .line 434
    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->popState()V

    return-void
.end method

.method public visitClass(Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 1

    .line 364
    invoke-static {p1}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->isAnonymous(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    .line 366
    :cond_0
    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->pushState()V

    const/4 v0, 0x0

    .line 367
    iput-boolean v0, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->inClosure:Z

    .line 368
    iput-object p1, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->currentClass:Lorg/codehaus/groovy/ast/ClassNode;

    .line 369
    iget-object v0, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->currentScope:Lorg/codehaus/groovy/ast/VariableScope;

    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/ast/VariableScope;->setClassScope(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 371
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;->visitClass(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 372
    iget-boolean v0, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->recurseInnerClasses:Z

    if-eqz v0, :cond_1

    .line 373
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getInnerClasses()Ljava/util/Iterator;

    move-result-object p1

    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_1

    .line 374
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->visitClass(Lorg/codehaus/groovy/ast/ClassNode;)V

    goto :goto_0

    .line 377
    :cond_1
    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->popState()V

    return-void
.end method

.method public visitClosureExpression(Lorg/codehaus/groovy/ast/expr/ClosureExpression;)V
    .locals 5

    .line 472
    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->pushState()V

    .line 473
    iget-object v0, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->currentScope:Lorg/codehaus/groovy/ast/VariableScope;

    invoke-virtual {p1, v0}, Lorg/codehaus/groovy/ast/expr/ClosureExpression;->setVariableScope(Lorg/codehaus/groovy/ast/VariableScope;)V

    .line 474
    iget-object v0, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->currentScope:Lorg/codehaus/groovy/ast/VariableScope;

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/VariableScope;->getParent()Lorg/codehaus/groovy/ast/VariableScope;

    move-result-object v0

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/VariableScope;->getClassScope()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->isAnonymous(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v0

    xor-int/lit8 v0, v0, 0x1

    iput-boolean v0, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->inClosure:Z

    .line 476
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/ClosureExpression;->isParameterSpecified()Z

    move-result v0

    if-eqz v0, :cond_1

    .line 477
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/ClosureExpression;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v0

    array-length v1, v0

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v1, :cond_2

    aget-object v3, v0, v2

    .line 478
    iget-object v4, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->currentScope:Lorg/codehaus/groovy/ast/VariableScope;

    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/VariableScope;->isInStaticContext()Z

    move-result v4

    invoke-virtual {v3, v4}, Lorg/codehaus/groovy/ast/Parameter;->setInStaticContext(Z)V

    .line 479
    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/Parameter;->hasInitialExpression()Z

    move-result v4

    if-eqz v4, :cond_0

    .line 480
    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/Parameter;->getInitialExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v4

    invoke-virtual {v4, p0}, Lorg/codehaus/groovy/ast/expr/Expression;->visit(Lorg/codehaus/groovy/ast/GroovyCodeVisitor;)V

    .line 482
    :cond_0
    invoke-direct {p0, v3, p1}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->declare(Lorg/codehaus/groovy/ast/Variable;Lorg/codehaus/groovy/ast/ASTNode;)V

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 484
    :cond_1
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/ClosureExpression;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v0

    if-eqz v0, :cond_2

    .line 485
    new-instance v0, Lorg/codehaus/groovy/ast/Parameter;

    sget-object v1, Lorg/codehaus/groovy/ast/ClassHelper;->OBJECT_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    const-string v2, "it"

    invoke-direct {v0, v1, v2}, Lorg/codehaus/groovy/ast/Parameter;-><init>(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)V

    .line 486
    iget-object v1, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->currentScope:Lorg/codehaus/groovy/ast/VariableScope;

    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/VariableScope;->isInStaticContext()Z

    move-result v1

    invoke-virtual {v0, v1}, Lorg/codehaus/groovy/ast/Parameter;->setInStaticContext(Z)V

    .line 487
    iget-object v1, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->currentScope:Lorg/codehaus/groovy/ast/VariableScope;

    invoke-virtual {v1, v0}, Lorg/codehaus/groovy/ast/VariableScope;->putDeclaredVariable(Lorg/codehaus/groovy/ast/Variable;)V

    .line 490
    :cond_2
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;->visitClosureExpression(Lorg/codehaus/groovy/ast/expr/ClosureExpression;)V

    .line 491
    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->markClosureSharedVariables()V

    .line 493
    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->popState()V

    return-void
.end method

.method public visitConstructorCallExpression(Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;)V
    .locals 7

    .line 498
    iget-boolean v0, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->inSpecialConstructorCall:Z

    .line 499
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;->isSpecialCall()Z

    move-result v1

    or-int/2addr v1, v0

    iput-boolean v1, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->inSpecialConstructorCall:Z

    .line 500
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;->visitConstructorCallExpression(Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;)V

    .line 501
    iput-boolean v0, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->inSpecialConstructorCall:Z

    .line 503
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;->isUsingAnonymousInnerClass()Z

    move-result v0

    if-nez v0, :cond_0

    return-void

    .line 505
    :cond_0
    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->pushState()V

    .line 506
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    check-cast p1, Lorg/codehaus/groovy/ast/InnerClassNode;

    .line 507
    iget-object v0, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->currentScope:Lorg/codehaus/groovy/ast/VariableScope;

    invoke-virtual {p1, v0}, Lorg/codehaus/groovy/ast/InnerClassNode;->setVariableScope(Lorg/codehaus/groovy/ast/VariableScope;)V

    .line 508
    iget-object v0, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->currentScope:Lorg/codehaus/groovy/ast/VariableScope;

    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/ast/VariableScope;->setClassScope(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 509
    iget-object v0, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->currentScope:Lorg/codehaus/groovy/ast/VariableScope;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Lorg/codehaus/groovy/ast/VariableScope;->setInStaticContext(Z)V

    .line 510
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/InnerClassNode;->getMethods()Ljava/util/List;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_3

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lorg/codehaus/groovy/ast/MethodNode;

    .line 511
    invoke-virtual {p0, v2}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->visitAnnotations(Lorg/codehaus/groovy/ast/AnnotatedNode;)V

    .line 512
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/MethodNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v3

    .line 513
    array-length v4, v3

    move v5, v1

    :goto_1
    if-ge v5, v4, :cond_1

    aget-object v6, v3, v5

    invoke-virtual {p0, v6}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->visitAnnotations(Lorg/codehaus/groovy/ast/AnnotatedNode;)V

    add-int/lit8 v5, v5, 0x1

    goto :goto_1

    .line 514
    :cond_1
    array-length v4, v3

    if-nez v4, :cond_2

    const/4 v3, 0x0

    .line 515
    :cond_2
    new-instance v4, Lorg/codehaus/groovy/ast/expr/ClosureExpression;

    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/MethodNode;->getCode()Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v2

    invoke-direct {v4, v3, v2}, Lorg/codehaus/groovy/ast/expr/ClosureExpression;-><init>([Lorg/codehaus/groovy/ast/Parameter;Lorg/codehaus/groovy/ast/stmt/Statement;)V

    invoke-virtual {p0, v4}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->visitClosureExpression(Lorg/codehaus/groovy/ast/expr/ClosureExpression;)V

    goto :goto_0

    .line 518
    :cond_3
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/InnerClassNode;->getFields()Ljava/util/List;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_4
    :goto_2
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_6

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/ast/FieldNode;

    .line 519
    invoke-virtual {p0, v1}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->visitAnnotations(Lorg/codehaus/groovy/ast/AnnotatedNode;)V

    .line 520
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/FieldNode;->getInitialExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v2

    if-eqz v2, :cond_4

    .line 522
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/expr/Expression;->isSynthetic()Z

    move-result v3

    if-eqz v3, :cond_5

    instance-of v3, v2, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    if-eqz v3, :cond_5

    move-object v3, v2

    check-cast v3, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    .line 523
    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->getAccessedVariable()Lorg/codehaus/groovy/ast/Variable;

    move-result-object v3

    instance-of v3, v3, Lorg/codehaus/groovy/ast/Parameter;

    if-eqz v3, :cond_5

    goto :goto_2

    .line 527
    :cond_5
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/FieldNode;->isStatic()Z

    move-result v1

    invoke-direct {p0, v1}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->pushState(Z)V

    .line 528
    invoke-virtual {v2, p0}, Lorg/codehaus/groovy/ast/expr/Expression;->visit(Lorg/codehaus/groovy/ast/GroovyCodeVisitor;)V

    .line 529
    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->popState()V

    goto :goto_2

    .line 533
    :cond_6
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/InnerClassNode;->getObjectInitializerStatements()Ljava/util/List;

    move-result-object p1

    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :goto_3
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_7

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/ast/stmt/Statement;

    .line 534
    invoke-virtual {v0, p0}, Lorg/codehaus/groovy/ast/stmt/Statement;->visit(Lorg/codehaus/groovy/ast/GroovyCodeVisitor;)V

    goto :goto_3

    .line 536
    :cond_7
    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->markClosureSharedVariables()V

    .line 537
    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->popState()V

    return-void
.end method

.method protected visitConstructorOrMethod(Lorg/codehaus/groovy/ast/MethodNode;Z)V
    .locals 4

    .line 396
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->isStatic()Z

    move-result v0

    invoke-direct {p0, v0}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->pushState(Z)V

    .line 397
    iput-boolean p2, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->inConstructor:Z

    .line 398
    iget-object p2, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->currentScope:Lorg/codehaus/groovy/ast/VariableScope;

    invoke-virtual {p1, p2}, Lorg/codehaus/groovy/ast/MethodNode;->setVariableScope(Lorg/codehaus/groovy/ast/VariableScope;)V

    .line 400
    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->visitAnnotations(Lorg/codehaus/groovy/ast/AnnotatedNode;)V

    .line 401
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object p2

    array-length v0, p2

    const/4 v1, 0x0

    move v2, v1

    :goto_0
    if-ge v2, v0, :cond_0

    aget-object v3, p2, v2

    .line 402
    invoke-virtual {p0, v3}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->visitAnnotations(Lorg/codehaus/groovy/ast/AnnotatedNode;)V

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 406
    :cond_0
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object p2

    array-length v0, p2

    :goto_1
    if-ge v1, v0, :cond_2

    aget-object v2, p2, v1

    .line 407
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/Parameter;->hasInitialExpression()Z

    move-result v3

    if-eqz v3, :cond_1

    .line 408
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/Parameter;->getInitialExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v3

    invoke-virtual {v3, p0}, Lorg/codehaus/groovy/ast/expr/Expression;->visit(Lorg/codehaus/groovy/ast/GroovyCodeVisitor;)V

    .line 410
    :cond_1
    invoke-direct {p0, v2, p1}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->declare(Lorg/codehaus/groovy/ast/Variable;Lorg/codehaus/groovy/ast/ASTNode;)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_1

    .line 412
    :cond_2
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getCode()Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->visitClassCodeContainer(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 414
    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->popState()V

    return-void
.end method

.method public visitDeclarationExpression(Lorg/codehaus/groovy/ast/expr/DeclarationExpression;)V
    .locals 1

    .line 542
    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->visitAnnotations(Lorg/codehaus/groovy/ast/AnnotatedNode;)V

    .line 544
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/DeclarationExpression;->getRightExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    invoke-virtual {v0, p0}, Lorg/codehaus/groovy/ast/expr/Expression;->visit(Lorg/codehaus/groovy/ast/GroovyCodeVisitor;)V

    .line 546
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/DeclarationExpression;->isMultipleAssignmentDeclaration()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 547
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/DeclarationExpression;->getTupleExpression()Lorg/codehaus/groovy/ast/expr/TupleExpression;

    move-result-object p1

    .line 548
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/TupleExpression;->getExpressions()Ljava/util/List;

    move-result-object p1

    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_1

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/ast/expr/Expression;

    .line 549
    check-cast v0, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    invoke-direct {p0, v0}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->declare(Lorg/codehaus/groovy/ast/expr/VariableExpression;)V

    goto :goto_0

    .line 552
    :cond_0
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/DeclarationExpression;->getVariableExpression()Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object p1

    invoke-direct {p0, p1}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->declare(Lorg/codehaus/groovy/ast/expr/VariableExpression;)V

    :cond_1
    return-void
.end method

.method public visitField(Lorg/codehaus/groovy/ast/FieldNode;)V
    .locals 1

    .line 382
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/FieldNode;->isStatic()Z

    move-result v0

    invoke-direct {p0, v0}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->pushState(Z)V

    .line 383
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;->visitField(Lorg/codehaus/groovy/ast/FieldNode;)V

    .line 384
    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->popState()V

    return-void
.end method

.method public visitFieldExpression(Lorg/codehaus/groovy/ast/expr/FieldExpression;)V
    .locals 1

    .line 558
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/FieldExpression;->getFieldName()Ljava/lang/String;

    move-result-object v0

    .line 560
    invoke-direct {p0, v0}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->findVariableDeclaration(Ljava/lang/String;)Lorg/codehaus/groovy/ast/Variable;

    move-result-object v0

    .line 561
    invoke-direct {p0, v0, p1}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->checkVariableContextAccess(Lorg/codehaus/groovy/ast/Variable;Lorg/codehaus/groovy/ast/expr/Expression;)V

    return-void
.end method

.method public visitForLoop(Lorg/codehaus/groovy/ast/stmt/ForStatement;)V
    .locals 2

    .line 439
    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->pushState()V

    .line 440
    iget-object v0, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->currentScope:Lorg/codehaus/groovy/ast/VariableScope;

    invoke-virtual {p1, v0}, Lorg/codehaus/groovy/ast/stmt/ForStatement;->setVariableScope(Lorg/codehaus/groovy/ast/VariableScope;)V

    .line 441
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/stmt/ForStatement;->getVariable()Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v0

    .line 442
    iget-object v1, p0, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->currentScope:Lorg/codehaus/groovy/ast/VariableScope;

    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/VariableScope;->isInStaticContext()Z

    move-result v1

    invoke-virtual {v0, v1}, Lorg/codehaus/groovy/ast/Parameter;->setInStaticContext(Z)V

    .line 443
    sget-object v1, Lorg/codehaus/groovy/ast/stmt/ForStatement;->FOR_LOOP_DUMMY:Lorg/codehaus/groovy/ast/Parameter;

    if-eq v0, v1, :cond_0

    invoke-direct {p0, v0, p1}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->declare(Lorg/codehaus/groovy/ast/Variable;Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 444
    :cond_0
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;->visitForLoop(Lorg/codehaus/groovy/ast/stmt/ForStatement;)V

    .line 445
    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->popState()V

    return-void
.end method

.method public visitIfElse(Lorg/codehaus/groovy/ast/stmt/IfStatement;)V
    .locals 1

    .line 450
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/stmt/IfStatement;->getBooleanExpression()Lorg/codehaus/groovy/ast/expr/BooleanExpression;

    move-result-object v0

    invoke-virtual {v0, p0}, Lorg/codehaus/groovy/ast/expr/BooleanExpression;->visit(Lorg/codehaus/groovy/ast/GroovyCodeVisitor;)V

    .line 451
    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->pushState()V

    .line 452
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/stmt/IfStatement;->getIfBlock()Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v0

    invoke-virtual {v0, p0}, Lorg/codehaus/groovy/ast/stmt/Statement;->visit(Lorg/codehaus/groovy/ast/GroovyCodeVisitor;)V

    .line 453
    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->popState()V

    .line 454
    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->pushState()V

    .line 455
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/stmt/IfStatement;->getElseBlock()Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    invoke-virtual {p1, p0}, Lorg/codehaus/groovy/ast/stmt/Statement;->visit(Lorg/codehaus/groovy/ast/GroovyCodeVisitor;)V

    .line 456
    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->popState()V

    return-void
.end method

.method public visitMethodCallExpression(Lorg/codehaus/groovy/ast/expr/MethodCallExpression;)V
    .locals 3

    .line 566
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->isImplicitThis()Z

    move-result v0

    if-eqz v0, :cond_3

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->getMethod()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    instance-of v0, v0, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    if-eqz v0, :cond_3

    .line 567
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->getMethod()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    .line 568
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/ConstantExpression;->getText()Ljava/lang/String;

    move-result-object v1

    if-eqz v1, :cond_2

    .line 574
    invoke-direct {p0, v1}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->findVariableDeclaration(Ljava/lang/String;)Lorg/codehaus/groovy/ast/Variable;

    move-result-object v1

    if-eqz v1, :cond_0

    .line 575
    instance-of v2, v1, Lorg/codehaus/groovy/ast/DynamicVariable;

    if-nez v2, :cond_0

    .line 576
    invoke-direct {p0, v1, p1}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->checkVariableContextAccess(Lorg/codehaus/groovy/ast/Variable;Lorg/codehaus/groovy/ast/expr/Expression;)V

    .line 579
    :cond_0
    instance-of v2, v1, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    if-nez v2, :cond_1

    instance-of v2, v1, Lorg/codehaus/groovy/ast/Parameter;

    if-eqz v2, :cond_3

    .line 580
    :cond_1
    new-instance v2, Lorg/codehaus/groovy/ast/expr/VariableExpression;

    invoke-direct {v2, v1}, Lorg/codehaus/groovy/ast/expr/VariableExpression;-><init>(Lorg/codehaus/groovy/ast/Variable;)V

    .line 581
    invoke-virtual {v2, v0}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->setSourcePosition(Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 582
    invoke-virtual {p1, v2}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setObjectExpression(Lorg/codehaus/groovy/ast/expr/Expression;)V

    .line 583
    new-instance v1, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    const-string v2, "call"

    invoke-direct {v1, v2}, Lorg/codehaus/groovy/ast/expr/ConstantExpression;-><init>(Ljava/lang/Object;)V

    .line 584
    invoke-virtual {v1, v0}, Lorg/codehaus/groovy/ast/expr/ConstantExpression;->setSourcePosition(Lorg/codehaus/groovy/ast/ASTNode;)V

    const/4 v0, 0x0

    .line 585
    invoke-virtual {p1, v0}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setImplicitThis(Z)V

    .line 586
    invoke-virtual {p1, v1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setMethod(Lorg/codehaus/groovy/ast/expr/Expression;)V

    goto :goto_0

    .line 571
    :cond_2
    new-instance p1, Lorg/codehaus/groovy/GroovyBugError;

    const-string v0, "method name is null"

    invoke-direct {p1, v0}, Lorg/codehaus/groovy/GroovyBugError;-><init>(Ljava/lang/String;)V

    throw p1

    .line 589
    :cond_3
    :goto_0
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;->visitMethodCallExpression(Lorg/codehaus/groovy/ast/expr/MethodCallExpression;)V

    return-void
.end method

.method public visitProperty(Lorg/codehaus/groovy/ast/PropertyNode;)V
    .locals 1

    .line 389
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/PropertyNode;->isStatic()Z

    move-result v0

    invoke-direct {p0, v0}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->pushState(Z)V

    .line 390
    invoke-super {p0, p1}, Lorg/codehaus/groovy/ast/ClassCodeVisitorSupport;->visitProperty(Lorg/codehaus/groovy/ast/PropertyNode;)V

    .line 391
    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->popState()V

    return-void
.end method

.method public visitPropertyExpression(Lorg/codehaus/groovy/ast/expr/PropertyExpression;)V
    .locals 1

    .line 594
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->getObjectExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    invoke-virtual {v0, p0}, Lorg/codehaus/groovy/ast/expr/Expression;->visit(Lorg/codehaus/groovy/ast/GroovyCodeVisitor;)V

    .line 595
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/PropertyExpression;->getProperty()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    invoke-virtual {v0, p0}, Lorg/codehaus/groovy/ast/expr/Expression;->visit(Lorg/codehaus/groovy/ast/GroovyCodeVisitor;)V

    .line 596
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->checkPropertyOnExplicitThis(Lorg/codehaus/groovy/ast/expr/PropertyExpression;)V

    return-void
.end method

.method public visitVariableExpression(Lorg/codehaus/groovy/ast/expr/VariableExpression;)V
    .locals 1

    .line 601
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->getName()Ljava/lang/String;

    move-result-object v0

    invoke-direct {p0, v0}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->findVariableDeclaration(Ljava/lang/String;)Lorg/codehaus/groovy/ast/Variable;

    move-result-object v0

    if-nez v0, :cond_0

    return-void

    .line 603
    :cond_0
    invoke-virtual {p1, v0}, Lorg/codehaus/groovy/ast/expr/VariableExpression;->setAccessedVariable(Lorg/codehaus/groovy/ast/Variable;)V

    .line 604
    invoke-direct {p0, v0, p1}, Lorg/codehaus/groovy/classgen/VariableScopeVisitor;->checkVariableContextAccess(Lorg/codehaus/groovy/ast/Variable;Lorg/codehaus/groovy/ast/expr/Expression;)V

    return-void
.end method
