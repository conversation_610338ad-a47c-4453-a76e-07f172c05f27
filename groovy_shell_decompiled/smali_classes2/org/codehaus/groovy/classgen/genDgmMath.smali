.class public Lorg/codehaus/groovy/classgen/genDgmMath;
.super Lgroovy/lang/Script;
.source "genDgmMath.groovy"


# static fields
.field private static synthetic $callSiteArray:Ljava/lang/ref/SoftReference;

.field private static synthetic $staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

.field private static synthetic $staticClassInfo$:Lorg/codehaus/groovy/reflection/ClassInfo;

.field public static transient synthetic __$stMC:Z


# direct methods
.method private static synthetic $createCallSiteArray()Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;
    .locals 3

    const/16 v0, 0x16

    new-array v0, v0, [Ljava/lang/String;

    invoke-static {v0}, Lorg/codehaus/groovy/classgen/genDgmMath;->$createCallSiteArray_1([Ljava/lang/String;)V

    new-instance v1, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;

    const-class v2, Lorg/codehaus/groovy/classgen/genDgmMath;

    invoke-direct {v1, v2, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;-><init>(Ljava/lang/Class;[Ljava/lang/String;)V

    return-object v1
.end method

.method private static synthetic $createCallSiteArray_1([Ljava/lang/String;)V
    .locals 5

    const/4 v0, 0x0

    const-string v1, "runScript"

    aput-object v1, p0, v0

    const/4 v0, 0x1

    const-string v1, "println"

    aput-object v1, p0, v0

    const/4 v0, 0x2

    const-string v2, "each"

    aput-object v2, p0, v0

    const/4 v0, 0x3

    aput-object v1, p0, v0

    const/4 v0, 0x4

    const-string v2, "iterator"

    aput-object v2, p0, v0

    const/4 v0, 0x5

    const-string v3, "print"

    aput-object v3, p0, v0

    const/4 v0, 0x6

    const-string v4, "printParams"

    aput-object v4, p0, v0

    const/4 v0, 0x7

    aput-object v1, p0, v0

    const/16 v0, 0x8

    aput-object v3, p0, v0

    const/16 v0, 0x9

    const-string v4, "printArgs"

    aput-object v4, p0, v0

    const/16 v0, 0xa

    aput-object v1, p0, v0

    const/16 v0, 0xb

    aput-object v1, p0, v0

    const/16 v0, 0xc

    aput-object v2, p0, v0

    const/16 v0, 0xd

    const-string v1, "minus"

    aput-object v1, p0, v0

    const/16 v0, 0xe

    aput-object v3, p0, v0

    const/16 v0, 0xf

    aput-object v2, p0, v0

    const/16 v0, 0x10

    aput-object v3, p0, v0

    const/16 v0, 0x11

    aput-object v2, p0, v0

    const/16 v0, 0x12

    aput-object v1, p0, v0

    const/16 v0, 0x13

    aput-object v3, p0, v0

    const/16 v0, 0x14

    aput-object v2, p0, v0

    const/16 v0, 0x15

    aput-object v3, p0, v0

    return-void
.end method

.method private static synthetic $getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;
    .locals 2

    sget-object v0, Lorg/codehaus/groovy/classgen/genDgmMath;->$callSiteArray:Ljava/lang/ref/SoftReference;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Ljava/lang/ref/SoftReference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;

    if-nez v0, :cond_1

    :cond_0
    invoke-static {}, Lorg/codehaus/groovy/classgen/genDgmMath;->$createCallSiteArray()Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;

    move-result-object v0

    new-instance v1, Ljava/lang/ref/SoftReference;

    invoke-direct {v1, v0}, Ljava/lang/ref/SoftReference;-><init>(Ljava/lang/Object;)V

    sput-object v1, Lorg/codehaus/groovy/classgen/genDgmMath;->$callSiteArray:Ljava/lang/ref/SoftReference;

    :cond_1
    iget-object v0, v0, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;->array:[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    return-object v0
.end method

.method public constructor <init>()V
    .locals 0

    invoke-static {}, Lorg/codehaus/groovy/classgen/genDgmMath;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    invoke-direct {p0}, Lgroovy/lang/Script;-><init>()V

    return-void
.end method

.method public constructor <init>(Lgroovy/lang/Binding;)V
    .locals 0

    invoke-static {}, Lorg/codehaus/groovy/classgen/genDgmMath;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    invoke-direct {p0, p1}, Lgroovy/lang/Script;-><init>(Lgroovy/lang/Binding;)V

    return-void
.end method

.method public static varargs main([Ljava/lang/String;)V
    .locals 3

    invoke-static {}, Lorg/codehaus/groovy/classgen/genDgmMath;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v0

    const/4 v1, 0x0

    aget-object v0, v0, v1

    const-class v1, Lorg/codehaus/groovy/runtime/InvokerHelper;

    const-class v2, Lorg/codehaus/groovy/classgen/genDgmMath;

    invoke-interface {v0, v1, v2, p0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method private printArgs(I)V
    .locals 9

    invoke-static {}, Lorg/codehaus/groovy/classgen/genDgmMath;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v0

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->isOrigInt()Z

    move-result v1

    const-string v2, ", "

    const-string v3, "a"

    const/4 v4, 0x0

    const/4 v5, 0x1

    .line 89
    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v6

    if-eqz v1, :cond_0

    .line 0
    sget-boolean v1, Lorg/codehaus/groovy/classgen/genDgmMath;->__$stMC:Z

    if-nez v1, :cond_0

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->disabledStandardMetaClass()Z

    move-result v1

    if-nez v1, :cond_0

    const/16 v1, 0x14

    .line 89
    aget-object v1, v0, v1

    sub-int/2addr p1, v5

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    invoke-static {v6, p1, v5}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createRange(Ljava/lang/Object;Ljava/lang/Object;Z)Ljava/util/List;

    move-result-object p1

    invoke-interface {v1, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    const-class v1, Ljava/util/Iterator;

    invoke-static {p1, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/util/Iterator;

    if-eqz p1, :cond_1

    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    const/16 v6, 0x15

    .line 90
    aget-object v6, v0, v6

    new-instance v7, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v8, v5, [Ljava/lang/Object;

    aput-object v1, v8, v4

    filled-new-array {v3, v2}, [Ljava/lang/String;

    move-result-object v1

    invoke-direct {v7, v8, v1}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-interface {v6, p0, v7}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callCurrent(Lgroovy/lang/GroovyObject;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    :cond_0
    const/16 v1, 0x11

    .line 89
    aget-object v1, v0, v1

    const/16 v7, 0x12

    aget-object v7, v0, v7

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    invoke-interface {v7, p1, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    invoke-static {v6, p1, v5}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createRange(Ljava/lang/Object;Ljava/lang/Object;Z)Ljava/util/List;

    move-result-object p1

    invoke-interface {v1, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    const-class v1, Ljava/util/Iterator;

    invoke-static {p1, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/util/Iterator;

    if-eqz p1, :cond_1

    :goto_1
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    const/16 v6, 0x13

    .line 90
    aget-object v6, v0, v6

    new-instance v7, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v8, v5, [Ljava/lang/Object;

    aput-object v1, v8, v4

    filled-new-array {v3, v2}, [Ljava/lang/String;

    move-result-object v1

    invoke-direct {v7, v8, v1}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-interface {v6, p0, v7}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callCurrent(Lgroovy/lang/GroovyObject;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_1

    :cond_1
    return-void
.end method

.method private printParams(I)V
    .locals 9

    invoke-static {}, Lorg/codehaus/groovy/classgen/genDgmMath;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v0

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->isOrigInt()Z

    move-result v1

    const-string v2, ", "

    const-string v3, "Object a"

    const/4 v4, 0x0

    const/4 v5, 0x1

    .line 83
    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v6

    if-eqz v1, :cond_0

    .line 0
    sget-boolean v1, Lorg/codehaus/groovy/classgen/genDgmMath;->__$stMC:Z

    if-nez v1, :cond_0

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->disabledStandardMetaClass()Z

    move-result v1

    if-nez v1, :cond_0

    const/16 v1, 0xf

    .line 83
    aget-object v1, v0, v1

    sub-int/2addr p1, v5

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    invoke-static {v6, p1, v5}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createRange(Ljava/lang/Object;Ljava/lang/Object;Z)Ljava/util/List;

    move-result-object p1

    invoke-interface {v1, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    const-class v1, Ljava/util/Iterator;

    invoke-static {p1, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/util/Iterator;

    if-eqz p1, :cond_1

    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    const/16 v6, 0x10

    .line 84
    aget-object v6, v0, v6

    new-instance v7, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v8, v5, [Ljava/lang/Object;

    aput-object v1, v8, v4

    filled-new-array {v3, v2}, [Ljava/lang/String;

    move-result-object v1

    invoke-direct {v7, v8, v1}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-interface {v6, p0, v7}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callCurrent(Lgroovy/lang/GroovyObject;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    :cond_0
    const/16 v1, 0xc

    .line 83
    aget-object v1, v0, v1

    const/16 v7, 0xd

    aget-object v7, v0, v7

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    invoke-interface {v7, p1, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    invoke-static {v6, p1, v5}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createRange(Ljava/lang/Object;Ljava/lang/Object;Z)Ljava/util/List;

    move-result-object p1

    invoke-interface {v1, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    const-class v1, Ljava/util/Iterator;

    invoke-static {p1, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/util/Iterator;

    if-eqz p1, :cond_1

    :goto_1
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    const/16 v6, 0xe

    .line 84
    aget-object v6, v0, v6

    new-instance v7, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v8, v5, [Ljava/lang/Object;

    aput-object v1, v8, v4

    filled-new-array {v3, v2}, [Ljava/lang/String;

    move-result-object v1

    invoke-direct {v7, v8, v1}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-interface {v6, p0, v7}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callCurrent(Lgroovy/lang/GroovyObject;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_1

    :cond_1
    return-void
.end method


# virtual methods
.method protected synthetic $getStaticMetaClass()Lgroovy/lang/MetaClass;
    .locals 2

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    const-class v1, Lorg/codehaus/groovy/classgen/genDgmMath;

    if-eq v0, v1, :cond_0

    invoke-static {p0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->initMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0

    :cond_0
    sget-object v0, Lorg/codehaus/groovy/classgen/genDgmMath;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    if-nez v0, :cond_1

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getClassInfo(Ljava/lang/Class;)Lorg/codehaus/groovy/reflection/ClassInfo;

    move-result-object v0

    sput-object v0, Lorg/codehaus/groovy/classgen/genDgmMath;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    :cond_1
    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0
.end method

.method public getMath(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 6

    invoke-static {}, Lorg/codehaus/groovy/classgen/genDgmMath;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    .line 24
    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->isOrigZ()Z

    move-result v0

    const-string v1, "FloatingPointMath"

    const-string v2, "Float"

    const-string v3, "Double"

    const/4 v4, 0x0

    const/4 v5, 0x1

    if-eqz v0, :cond_6

    sget-boolean v0, Lorg/codehaus/groovy/classgen/genDgmMath;->__$stMC:Z

    if-nez v0, :cond_6

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->disabledStandardMetaClass()Z

    move-result v0

    if-nez v0, :cond_6

    invoke-static {p1, v3}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_1

    invoke-static {p2, v3}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    move v0, v4

    goto :goto_1

    :cond_1
    :goto_0
    move v0, v5

    :goto_1
    if-nez v0, :cond_3

    invoke-static {p1, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_2

    goto :goto_2

    :cond_2
    move v0, v4

    goto :goto_3

    :cond_3
    :goto_2
    move v0, v5

    :goto_3
    if-nez v0, :cond_5

    invoke-static {p2, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_4

    goto :goto_4

    :cond_4
    move v0, v4

    goto :goto_5

    :cond_5
    :goto_4
    move v0, v5

    :goto_5
    if-eqz v0, :cond_d

    return-object v1

    :cond_6
    invoke-static {p1, v3}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_8

    invoke-static {p2, v3}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_7

    goto :goto_6

    :cond_7
    move v0, v4

    goto :goto_7

    :cond_8
    :goto_6
    move v0, v5

    :goto_7
    if-nez v0, :cond_a

    invoke-static {p1, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_9

    goto :goto_8

    :cond_9
    move v0, v4

    goto :goto_9

    :cond_a
    :goto_8
    move v0, v5

    :goto_9
    if-nez v0, :cond_c

    invoke-static {p2, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_b

    goto :goto_a

    :cond_b
    move v0, v4

    goto :goto_b

    :cond_c
    :goto_a
    move v0, v5

    :goto_b
    if-eqz v0, :cond_d

    return-object v1

    .line 27
    :cond_d
    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->isOrigZ()Z

    move-result v0

    const-string v1, "LongMath"

    const-string v2, "Long"

    if-eqz v0, :cond_10

    sget-boolean v0, Lorg/codehaus/groovy/classgen/genDgmMath;->__$stMC:Z

    if-nez v0, :cond_10

    invoke-static {}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->disabledStandardMetaClass()Z

    move-result v0

    if-nez v0, :cond_10

    invoke-static {p1, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p1

    if-nez p1, :cond_e

    invoke-static {p2, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_f

    :cond_e
    move v4, v5

    :cond_f
    if-eqz v4, :cond_13

    return-object v1

    :cond_10
    invoke-static {p1, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p1

    if-nez p1, :cond_11

    invoke-static {p2, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_12

    :cond_11
    move v4, v5

    :cond_12
    if-eqz v4, :cond_13

    return-object v1

    :cond_13
    const-string p1, "IntegerMath"

    return-object p1
.end method

.method public run()Ljava/lang/Object;
    .locals 10

    invoke-static {}, Lorg/codehaus/groovy/classgen/genDgmMath;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v0

    const/4 v1, 0x4

    new-array v2, v1, [Ljava/lang/Object;

    const-string v3, "Integer"

    const/4 v4, 0x0

    aput-object v3, v2, v4

    const-string v3, "Long"

    const/4 v5, 0x1

    aput-object v3, v2, v5

    const-string v3, "Float"

    const/4 v6, 0x2

    aput-object v3, v2, v6

    const-string v3, "Double"

    const/4 v7, 0x3

    aput-object v3, v2, v7

    .line 21
    invoke-static {v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v2

    new-instance v3, Lgroovy/lang/Reference;

    invoke-direct {v3, v2}, Lgroovy/lang/Reference;-><init>(Ljava/lang/Object;)V

    .line 33
    aget-object v2, v0, v5

    const-string v8, "\npublic CallSite createPojoCallSite(CallSite site, MetaClassImpl metaClass, MetaMethod metaMethod, Class[] params, Object receiver, Object[] args) {\n    NumberMath m = NumberMath.getMath((Number)receiver, (Number)args[0]);\n"

    invoke-interface {v2, p0, v8}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callCurrent(Lgroovy/lang/GroovyObject;Ljava/lang/Object;)Ljava/lang/Object;

    .line 38
    aget-object v2, v0, v6

    invoke-virtual {v3}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v8

    new-instance v9, Lorg/codehaus/groovy/classgen/genDgmMath$_run_closure1;

    invoke-direct {v9, p0, p0, v3}, Lorg/codehaus/groovy/classgen/genDgmMath$_run_closure1;-><init>(Ljava/lang/Object;Ljava/lang/Object;Lgroovy/lang/Reference;)V

    invoke-interface {v2, v8, v9}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 60
    aget-object v2, v0, v7

    const-string v3, "\n    return new NumberNumberCallSite (site, metaClass, metaMethod, params, (Number)receiver, (Number)args[0]){\n        public final Object invoke(Object receiver, Object[] args) {\n            return math.addImpl((Number)receiver,(Number)args[0]);\n        }\n\n        public final Object invokeBinop(Object receiver, Object arg) {\n            return math.addImpl((Number)receiver,(Number)arg);\n        }\n}\n"

    invoke-interface {v2, p0, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callCurrent(Lgroovy/lang/GroovyObject;Ljava/lang/Object;)Ljava/lang/Object;

    .line 72
    aget-object v1, v0, v1

    invoke-static {v6}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    const/16 v3, 0x100

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v3

    invoke-static {v2, v3, v5}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createRange(Ljava/lang/Object;Ljava/lang/Object;Z)Ljava/util/List;

    move-result-object v2

    invoke-interface {v1, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    const-class v2, Ljava/util/Iterator;

    invoke-static {v1, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/util/Iterator;

    if-eqz v1, :cond_0

    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_0

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    const/4 v3, 0x5

    .line 73
    aget-object v3, v0, v3

    new-instance v6, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v7, v5, [Ljava/lang/Object;

    aput-object v2, v7, v4

    const-string v8, "public Object invoke"

    const-string v9, " (Object receiver, "

    filled-new-array {v8, v9}, [Ljava/lang/String;

    move-result-object v8

    invoke-direct {v6, v7, v8}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-interface {v3, p0, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callCurrent(Lgroovy/lang/GroovyObject;Ljava/lang/Object;)Ljava/lang/Object;

    const/4 v3, 0x6

    .line 74
    aget-object v3, v0, v3

    invoke-interface {v3, p0, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callCurrent(Lgroovy/lang/GroovyObject;Ljava/lang/Object;)Ljava/lang/Object;

    const/4 v3, 0x7

    .line 75
    aget-object v3, v0, v3

    new-instance v6, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v7, v5, [Ljava/lang/Object;

    aput-object v2, v7, v4

    const-string v8, "Object a"

    const-string v9, ") {"

    filled-new-array {v8, v9}, [Ljava/lang/String;

    move-result-object v8

    invoke-direct {v6, v7, v8}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-interface {v3, p0, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callCurrent(Lgroovy/lang/GroovyObject;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v3, 0x8

    .line 76
    aget-object v3, v0, v3

    const-string v6, "  return invoke (receiver, new Object[] {"

    invoke-interface {v3, p0, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callCurrent(Lgroovy/lang/GroovyObject;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v3, 0x9

    .line 77
    aget-object v3, v0, v3

    invoke-interface {v3, p0, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callCurrent(Lgroovy/lang/GroovyObject;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v3, 0xa

    .line 78
    aget-object v3, v0, v3

    new-instance v6, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v7, v5, [Ljava/lang/Object;

    aput-object v2, v7, v4

    const-string v2, "a"

    const-string v8, "} );"

    filled-new-array {v2, v8}, [Ljava/lang/String;

    move-result-object v2

    invoke-direct {v6, v7, v2}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-interface {v3, p0, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callCurrent(Lgroovy/lang/GroovyObject;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v2, 0xb

    .line 79
    aget-object v2, v0, v2

    const-string v3, "}"

    invoke-interface {v2, p0, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callCurrent(Lgroovy/lang/GroovyObject;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    return-object v0
.end method
