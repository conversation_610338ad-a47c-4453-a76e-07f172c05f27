.class Lorg/codehaus/groovy/classgen/Verifier$13;
.super Lorg/codehaus/groovy/classgen/BytecodeInstruction;
.source "Verifier.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/codehaus/groovy/classgen/Verifier;->createSetterBlock(Lorg/codehaus/groovy/ast/PropertyNode;Lorg/codehaus/groovy/ast/FieldNode;)Lorg/codehaus/groovy/ast/stmt/Statement;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lorg/codehaus/groovy/classgen/Verifier;

.field final synthetic val$field:Lorg/codehaus/groovy/ast/FieldNode;

.field final synthetic val$owner:Ljava/lang/String;


# direct methods
.method constructor <init>(Lorg/codehaus/groovy/classgen/Verifier;Lorg/codehaus/groovy/ast/FieldNode;Ljava/lang/String;)V
    .locals 0

    .line 1208
    iput-object p1, p0, Lorg/codehaus/groovy/classgen/Verifier$13;->this$0:Lorg/codehaus/groovy/classgen/Verifier;

    iput-object p2, p0, Lorg/codehaus/groovy/classgen/Verifier$13;->val$field:Lorg/codehaus/groovy/ast/FieldNode;

    iput-object p3, p0, Lorg/codehaus/groovy/classgen/Verifier$13;->val$owner:Ljava/lang/String;

    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/BytecodeInstruction;-><init>()V

    return-void
.end method


# virtual methods
.method public visit(Lgroovyjarjarasm/asm/MethodVisitor;)V
    .locals 4

    .line 1211
    iget-object v0, p0, Lorg/codehaus/groovy/classgen/Verifier$13;->val$field:Lorg/codehaus/groovy/ast/FieldNode;

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/FieldNode;->isStatic()Z

    move-result v0

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    .line 1212
    iget-object v0, p0, Lorg/codehaus/groovy/classgen/Verifier$13;->val$field:Lorg/codehaus/groovy/ast/FieldNode;

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/FieldNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    invoke-static {p1, v0, v1}, Lorg/codehaus/groovy/classgen/asm/BytecodeHelper;->load(Lgroovyjarjarasm/asm/MethodVisitor;Lorg/codehaus/groovy/ast/ClassNode;I)V

    const/16 v0, 0xb3

    .line 1213
    iget-object v1, p0, Lorg/codehaus/groovy/classgen/Verifier$13;->val$owner:Ljava/lang/String;

    iget-object v2, p0, Lorg/codehaus/groovy/classgen/Verifier$13;->val$field:Lorg/codehaus/groovy/ast/FieldNode;

    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object v2

    iget-object v3, p0, Lorg/codehaus/groovy/classgen/Verifier$13;->val$field:Lorg/codehaus/groovy/ast/FieldNode;

    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/FieldNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v3

    invoke-static {v3}, Lorg/codehaus/groovy/classgen/asm/BytecodeHelper;->getTypeDescription(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {p1, v0, v1, v2, v3}, Lgroovyjarjarasm/asm/MethodVisitor;->visitFieldInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_0

    :cond_0
    const/16 v0, 0x19

    .line 1215
    invoke-virtual {p1, v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitVarInsn(II)V

    .line 1216
    iget-object v0, p0, Lorg/codehaus/groovy/classgen/Verifier$13;->val$field:Lorg/codehaus/groovy/ast/FieldNode;

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/FieldNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    const/4 v1, 0x1

    invoke-static {p1, v0, v1}, Lorg/codehaus/groovy/classgen/asm/BytecodeHelper;->load(Lgroovyjarjarasm/asm/MethodVisitor;Lorg/codehaus/groovy/ast/ClassNode;I)V

    const/16 v0, 0xb5

    .line 1217
    iget-object v1, p0, Lorg/codehaus/groovy/classgen/Verifier$13;->val$owner:Ljava/lang/String;

    iget-object v2, p0, Lorg/codehaus/groovy/classgen/Verifier$13;->val$field:Lorg/codehaus/groovy/ast/FieldNode;

    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object v2

    iget-object v3, p0, Lorg/codehaus/groovy/classgen/Verifier$13;->val$field:Lorg/codehaus/groovy/ast/FieldNode;

    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/FieldNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v3

    invoke-static {v3}, Lorg/codehaus/groovy/classgen/asm/BytecodeHelper;->getTypeDescription(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {p1, v0, v1, v2, v3}, Lgroovyjarjarasm/asm/MethodVisitor;->visitFieldInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    :goto_0
    const/16 v0, 0xb1

    .line 1219
    invoke-virtual {p1, v0}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    return-void
.end method
