.class Lorg/codehaus/groovy/classgen/InnerClassCompletionVisitor$1;
.super Lorg/codehaus/groovy/classgen/BytecodeInstruction;
.source "InnerClassCompletionVisitor.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/codehaus/groovy/classgen/InnerClassCompletionVisitor;->addMopMethods(Lorg/codehaus/groovy/ast/InnerClassNode;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lorg/codehaus/groovy/classgen/InnerClassCompletionVisitor;

.field final synthetic val$classInternalName:Ljava/lang/String;

.field final synthetic val$outerClassDescriptor:Ljava/lang/String;

.field final synthetic val$outerClassDistance:I

.field final synthetic val$outerClassInternalName:Ljava/lang/String;


# direct methods
.method constructor <init>(Lorg/codehaus/groovy/classgen/InnerClassCompletionVisitor;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;I)V
    .locals 0

    .line 209
    iput-object p1, p0, Lorg/codehaus/groovy/classgen/InnerClassCompletionVisitor$1;->this$0:Lorg/codehaus/groovy/classgen/InnerClassCompletionVisitor;

    iput-object p2, p0, Lorg/codehaus/groovy/classgen/InnerClassCompletionVisitor$1;->val$classInternalName:Ljava/lang/String;

    iput-object p3, p0, Lorg/codehaus/groovy/classgen/InnerClassCompletionVisitor$1;->val$outerClassDescriptor:Ljava/lang/String;

    iput-object p4, p0, Lorg/codehaus/groovy/classgen/InnerClassCompletionVisitor$1;->val$outerClassInternalName:Ljava/lang/String;

    iput p5, p0, Lorg/codehaus/groovy/classgen/InnerClassCompletionVisitor$1;->val$outerClassDistance:I

    invoke-direct {p0}, Lorg/codehaus/groovy/classgen/BytecodeInstruction;-><init>()V

    return-void
.end method


# virtual methods
.method public visit(Lgroovyjarjarasm/asm/MethodVisitor;)V
    .locals 8

    .line 212
    iget-object v0, p0, Lorg/codehaus/groovy/classgen/InnerClassCompletionVisitor$1;->this$0:Lorg/codehaus/groovy/classgen/InnerClassCompletionVisitor;

    iget-object v1, p0, Lorg/codehaus/groovy/classgen/InnerClassCompletionVisitor$1;->val$classInternalName:Ljava/lang/String;

    iget-object v2, p0, Lorg/codehaus/groovy/classgen/InnerClassCompletionVisitor$1;->val$outerClassDescriptor:Ljava/lang/String;

    iget-object v3, p0, Lorg/codehaus/groovy/classgen/InnerClassCompletionVisitor$1;->val$outerClassInternalName:Ljava/lang/String;

    invoke-static {v0, p1, v1, v2, v3}, Lorg/codehaus/groovy/classgen/InnerClassCompletionVisitor;->access$000(Lorg/codehaus/groovy/classgen/InnerClassCompletionVisitor;Lgroovyjarjarasm/asm/MethodVisitor;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    const/16 v0, 0x19

    const/4 v1, 0x1

    .line 213
    invoke-virtual {p1, v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitVarInsn(II)V

    const/4 v1, 0x2

    .line 214
    invoke-virtual {p1, v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitVarInsn(II)V

    .line 215
    iget-object v4, p0, Lorg/codehaus/groovy/classgen/InnerClassCompletionVisitor$1;->val$outerClassInternalName:Ljava/lang/String;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "this$dist$invoke$"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget v1, p0, Lorg/codehaus/groovy/classgen/InnerClassCompletionVisitor$1;->val$outerClassDistance:I

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v5

    const/16 v3, 0xb6

    const-string v6, "(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object;"

    const/4 v7, 0x0

    move-object v2, p1

    invoke-virtual/range {v2 .. v7}, Lgroovyjarjarasm/asm/MethodVisitor;->visitMethodInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V

    const/16 v0, 0xb0

    .line 216
    invoke-virtual {p1, v0}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    return-void
.end method
