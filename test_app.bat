@echo off
echo ================================
echo 测试修改后的EG应用
echo ================================
echo.

echo [1] 启动应用...
.\platform-tools\adb.exe shell monkey -p com.alibaba.android.rimet.beesforce -c android.intent.category.LAUNCHER 1

echo.
echo [2] 等待应用启动...
timeout /t 5 /nobreak

echo.
echo [3] 检查应用进程...
.\platform-tools\adb.exe shell "ps | grep com.alibaba.android.rimet.beesforce"

echo.
echo [4] 截取屏幕截图...
.\platform-tools\adb.exe shell screencap -p /sdcard/screenshot.png
.\platform-tools\adb.exe pull /sdcard/screenshot.png screenshot.png

echo.
echo [5] 检查应用日志...
.\platform-tools\adb.exe logcat -d | findstr -i "alibaba\|tianyu\|EG"

echo.
echo ================================
echo 测试完成！
echo ================================
echo.
echo 请检查：
echo 1. 应用是否正常启动
echo 2. 是否跳过了授权验证界面
echo 3. screenshot.png 中的应用界面
echo.
pause
