<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <TextView android:textSize="20.0sp" android:textStyle="bold" android:textColor="#ff282828" android:layout_gravity="center" android:padding="10.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/app_agreement" />
    <ScrollView android:layout_width="fill_parent" android:layout_height="400.0dip" android:layout_marginBottom="10.0dip">
        <TextView android:textSize="15.0sp" android:textColor="#ff282828" android:id="@id/tv_content" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginLeft="15.0dip" android:layout_marginRight="15.0dip" android:lineSpacingMultiplier="1.2" />
    </ScrollView>
    <LinearLayout android:gravity="center_horizontal" android:orientation="horizontal" android:layout_width="fill_parent" android:layout_height="wrap_content">
        <Button android:id="@id/tv_cancel" android:layout_width="100.0dip" android:layout_height="wrap_content" android:text="@string/app_btn_disagree" />
        <Space android:layout_width="60.0dip" android:layout_height="fill_parent" />
        <Button android:id="@id/tv_agree" android:layout_width="100.0dip" android:layout_height="wrap_content" android:text="@string/app_btn_agree" />
    </LinearLayout>
</LinearLayout>