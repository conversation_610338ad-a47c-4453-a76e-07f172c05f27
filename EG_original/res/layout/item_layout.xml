<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:gravity="center" android:orientation="horizontal" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_margin="10.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <LinearLayout android:gravity="center_vertical" android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_margin="10.0dip" android:layout_weight="1.0">
        <TextView android:textSize="14.0sp" android:textColor="@color/black" android:id="@id/sug_key" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="111" />
        <LinearLayout android:orientation="horizontal" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="5.0dip">
            <TextView android:textColor="#ff696969" android:id="@id/sug_city" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginLeft="8.0dip" android:text="111" />
            <TextView android:textColor="#ff696969" android:id="@id/sug_dis" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="111" />
        </LinearLayout>
    </LinearLayout>
</LinearLayout>