console.log("[*] 开始Hook定位服务...");

// JWK京崎大厦的坐标
var TARGET_LATITUDE = 30.1865;   // 纬度
var TARGET_LONGITUDE = 120.2103; // 经度
var TARGET_ALTITUDE = 10.0;      // 海拔
var TARGET_ACCURACY = 5.0;       // 精度

console.log("[*] 目标位置: JWK京崎大厦");
console.log("[*] 纬度: " + TARGET_LATITUDE);
console.log("[*] 经度: " + TARGET_LONGITUDE);

Java.perform(function() {
    console.log("[*] Java环境已准备就绪");
    
    // Hook 1: Android原生LocationManager
    try {
        var LocationManager = Java.use("android.location.LocationManager");
        
        // Hook getLastKnownLocation
        LocationManager.getLastKnownLocation.implementation = function(provider) {
            console.log("[+] Hook getLastKnownLocation, provider: " + provider);
            
            var Location = Java.use("android.location.Location");
            var fakeLocation = Location.$new(provider);
            
            fakeLocation.setLatitude(TARGET_LATITUDE);
            fakeLocation.setLongitude(TARGET_LONGITUDE);
            fakeLocation.setAltitude(TARGET_ALTITUDE);
            fakeLocation.setAccuracy(TARGET_ACCURACY);
            fakeLocation.setTime(Java.use("java.lang.System").currentTimeMillis());
            
            console.log("[+] ✅ 返回虚假位置: " + TARGET_LATITUDE + ", " + TARGET_LONGITUDE);
            return fakeLocation;
        };
        
        // Hook requestLocationUpdates
        LocationManager.requestLocationUpdates.overload('java.lang.String', 'long', 'float', 'android.location.LocationListener').implementation = function(provider, minTime, minDistance, listener) {
            console.log("[+] Hook requestLocationUpdates, provider: " + provider);
            
            // 调用原方法
            this.requestLocationUpdates(provider, minTime, minDistance, listener);
            
            // 立即发送虚假位置
            setTimeout(function() {
                Java.perform(function() {
                    try {
                        var Location = Java.use("android.location.Location");
                        var fakeLocation = Location.$new(provider);
                        
                        fakeLocation.setLatitude(TARGET_LATITUDE);
                        fakeLocation.setLongitude(TARGET_LONGITUDE);
                        fakeLocation.setAltitude(TARGET_ALTITUDE);
                        fakeLocation.setAccuracy(TARGET_ACCURACY);
                        fakeLocation.setTime(Java.use("java.lang.System").currentTimeMillis());
                        
                        listener.onLocationChanged(fakeLocation);
                        console.log("[+] ✅ 发送虚假位置更新: " + TARGET_LATITUDE + ", " + TARGET_LONGITUDE);
                    } catch (e) {
                        console.log("[-] 发送位置更新失败: " + e);
                    }
                });
            }, 1000);
        };
        
        console.log("[+] ✅ 成功Hook LocationManager");
    } catch (e) {
        console.log("[-] ❌ Hook LocationManager失败: " + e);
    }
    
    // Hook 2: Location对象
    try {
        var Location = Java.use("android.location.Location");
        
        Location.getLatitude.implementation = function() {
            var originalLat = this.getLatitude();
            console.log("[+] Hook getLatitude, 原始: " + originalLat + " -> 修改: " + TARGET_LATITUDE);
            return TARGET_LATITUDE;
        };
        
        Location.getLongitude.implementation = function() {
            var originalLng = this.getLongitude();
            console.log("[+] Hook getLongitude, 原始: " + originalLng + " -> 修改: " + TARGET_LONGITUDE);
            return TARGET_LONGITUDE;
        };
        
        Location.getAltitude.implementation = function() {
            return TARGET_ALTITUDE;
        };
        
        Location.getAccuracy.implementation = function() {
            return TARGET_ACCURACY;
        };
        
        console.log("[+] ✅ 成功Hook Location对象");
    } catch (e) {
        console.log("[-] ❌ Hook Location对象失败: " + e);
    }
    
    // Hook 3: 百度定位服务
    try {
        // Hook百度定位相关类
        setTimeout(function() {
            Java.perform(function() {
                try {
                    // 尝试Hook百度定位类
                    var BaiduLocationClass = Java.use("com.baidu.location.BDLocation");
                    
                    BaiduLocationClass.getLatitude.implementation = function() {
                        console.log("[+] Hook 百度定位 getLatitude -> " + TARGET_LATITUDE);
                        return TARGET_LATITUDE;
                    };
                    
                    BaiduLocationClass.getLongitude.implementation = function() {
                        console.log("[+] Hook 百度定位 getLongitude -> " + TARGET_LONGITUDE);
                        return TARGET_LONGITUDE;
                    };
                    
                    console.log("[+] ✅ 成功Hook百度定位");
                } catch (e) {
                    console.log("[-] 百度定位类未找到或Hook失败: " + e);
                }
            });
        }, 3000);
    } catch (e) {
        console.log("[-] ❌ Hook百度定位失败: " + e);
    }
    
    // Hook 4: GPS Provider
    try {
        var GpsStatus = Java.use("android.location.GpsStatus");
        console.log("[+] ✅ GPS相关类已准备");
    } catch (e) {
        console.log("[-] GPS相关类Hook失败: " + e);
    }
    
    // Hook 5: 网络定位
    try {
        var WifiManager = Java.use("android.net.wifi.WifiManager");
        var TelephonyManager = Java.use("android.telephony.TelephonyManager");
        console.log("[+] ✅ 网络定位相关类已准备");
    } catch (e) {
        console.log("[-] 网络定位Hook失败: " + e);
    }
    
    console.log("[*] 🎉 定位Hook设置完成！");
    console.log("[*] 📍 应用现在将始终显示位置为: JWK京崎大厦");
    console.log("[*] 📍 坐标: " + TARGET_LATITUDE + ", " + TARGET_LONGITUDE);
});
