package com.locationspoof.service;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

public class BootReceiver extends BroadcastReceiver {
    private static final String TAG = "LocationSpoofer";

    @Override
    public void onReceive(Context context, Intent intent) {
        String action = intent.getAction();
        Log.d(TAG, "收到广播: " + action);
        
        if (Intent.ACTION_BOOT_COMPLETED.equals(action) ||
            Intent.ACTION_MY_PACKAGE_REPLACED.equals(action) ||
            Intent.ACTION_PACKAGE_REPLACED.equals(action)) {
            
            Log.d(TAG, "系统启动完成，启动位置模拟服务");
            
            // 启动位置模拟服务
            Intent serviceIntent = new Intent(context, LocationSpooferService.class);
            context.startForegroundService(serviceIntent);
            
            // 同时启动Xposed Hook
            XposedLocationHook.startHook();
        }
    }
}
