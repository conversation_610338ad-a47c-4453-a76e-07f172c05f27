package com.locationspoof.service;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.location.Location;
import android.location.LocationManager;
import android.os.Build;
import android.os.IBinder;
import android.os.PowerManager;
import android.util.Log;

import java.lang.reflect.Method;
import java.util.Timer;
import java.util.TimerTask;

public class LocationSpooferService extends Service {
    private static final String TAG = "LocationSpoofer";
    private static final String CHANNEL_ID = "LocationSpooferChannel";
    
    // JWK京崎大厦坐标
    private static final double TARGET_LATITUDE = 30.1865;
    private static final double TARGET_LONGITUDE = 120.2103;
    private static final double TARGET_ALTITUDE = 10.0;
    private static final float TARGET_ACCURACY = 5.0f;
    
    private LocationManager locationManager;
    private PowerManager.WakeLock wakeLock;
    private Timer locationTimer;
    private boolean isServiceRunning = false;

    @Override
    public void onCreate() {
        super.onCreate();
        Log.d(TAG, "LocationSpooferService 创建");
        
        locationManager = (LocationManager) getSystemService(Context.LOCATION_SERVICE);
        
        // 获取唤醒锁，保持服务运行
        PowerManager powerManager = (PowerManager) getSystemService(Context.POWER_SERVICE);
        wakeLock = powerManager.newWakeLock(PowerManager.PARTIAL_WAKE_LOCK, TAG + ":WakeLock");
        
        createNotificationChannel();
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.d(TAG, "LocationSpooferService 启动");
        
        if (!isServiceRunning) {
            startForegroundService();
            startLocationSpoofing();
            isServiceRunning = true;
        }
        
        // 返回START_STICKY确保服务被杀死后自动重启
        return START_STICKY;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.d(TAG, "LocationSpooferService 销毁");
        
        stopLocationSpoofing();
        
        if (wakeLock != null && wakeLock.isHeld()) {
            wakeLock.release();
        }
        
        // 重启服务
        Intent restartIntent = new Intent(this, LocationSpooferService.class);
        startService(restartIntent);
    }

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                CHANNEL_ID,
                "位置模拟服务",
                NotificationManager.IMPORTANCE_LOW
            );
            channel.setDescription("JWK京崎大厦位置模拟服务正在运行");
            
            NotificationManager manager = getSystemService(NotificationManager.class);
            manager.createNotificationChannel(channel);
        }
    }

    private void startForegroundService() {
        Notification notification = new Notification.Builder(this, CHANNEL_ID)
            .setContentTitle("位置模拟器")
            .setContentText("正在模拟位置: JWK京崎大厦")
            .setSmallIcon(android.R.drawable.ic_menu_mylocation)
            .setOngoing(true)
            .build();
            
        startForeground(1, notification);
        Log.d(TAG, "前台服务已启动");
    }

    private void startLocationSpoofing() {
        if (!wakeLock.isHeld()) {
            wakeLock.acquire();
        }
        
        // 设置模拟位置提供者
        setupMockLocationProvider();
        
        // 启动定时器，每秒更新一次位置
        locationTimer = new Timer();
        locationTimer.scheduleAtFixedRate(new TimerTask() {
            @Override
            public void run() {
                updateMockLocation();
            }
        }, 0, 1000); // 每1秒更新一次
        
        Log.d(TAG, "位置模拟已启动 - JWK京崎大厦");
    }

    private void stopLocationSpoofing() {
        if (locationTimer != null) {
            locationTimer.cancel();
            locationTimer = null;
        }
        
        // 移除模拟位置提供者
        try {
            locationManager.removeTestProvider(LocationManager.GPS_PROVIDER);
            locationManager.removeTestProvider(LocationManager.NETWORK_PROVIDER);
        } catch (Exception e) {
            Log.w(TAG, "移除模拟位置提供者失败: " + e.getMessage());
        }
        
        isServiceRunning = false;
        Log.d(TAG, "位置模拟已停止");
    }

    private void setupMockLocationProvider() {
        try {
            // 设置GPS提供者
            if (locationManager.getProvider(LocationManager.GPS_PROVIDER) != null) {
                locationManager.addTestProvider(
                    LocationManager.GPS_PROVIDER,
                    false, false, false, false, true, true, true,
                    android.location.Criteria.POWER_MEDIUM,
                    android.location.Criteria.ACCURACY_FINE
                );
                locationManager.setTestProviderEnabled(LocationManager.GPS_PROVIDER, true);
            }
            
            // 设置网络提供者
            if (locationManager.getProvider(LocationManager.NETWORK_PROVIDER) != null) {
                locationManager.addTestProvider(
                    LocationManager.NETWORK_PROVIDER,
                    false, false, false, false, false, false, false,
                    android.location.Criteria.POWER_LOW,
                    android.location.Criteria.ACCURACY_COARSE
                );
                locationManager.setTestProviderEnabled(LocationManager.NETWORK_PROVIDER, true);
            }
            
            Log.d(TAG, "模拟位置提供者设置完成");
        } catch (Exception e) {
            Log.e(TAG, "设置模拟位置提供者失败: " + e.getMessage());
        }
    }

    private void updateMockLocation() {
        try {
            // 创建GPS位置
            Location gpsLocation = createMockLocation(LocationManager.GPS_PROVIDER);
            locationManager.setTestProviderLocation(LocationManager.GPS_PROVIDER, gpsLocation);
            
            // 创建网络位置
            Location networkLocation = createMockLocation(LocationManager.NETWORK_PROVIDER);
            locationManager.setTestProviderLocation(LocationManager.NETWORK_PROVIDER, networkLocation);
            
            // 使用反射Hook系统LocationManager
            hookSystemLocationManager();
            
        } catch (Exception e) {
            Log.e(TAG, "更新模拟位置失败: " + e.getMessage());
        }
    }

    private Location createMockLocation(String provider) {
        Location location = new Location(provider);
        location.setLatitude(TARGET_LATITUDE);
        location.setLongitude(TARGET_LONGITUDE);
        location.setAltitude(TARGET_ALTITUDE);
        location.setAccuracy(TARGET_ACCURACY);
        location.setTime(System.currentTimeMillis());
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
            location.setElapsedRealtimeNanos(System.nanoTime());
        }
        
        return location;
    }

    private void hookSystemLocationManager() {
        // 这里可以添加更深层的Hook逻辑
        // 由于需要系统权限，这部分在普通应用中有限制
        Log.d(TAG, "位置已更新: " + TARGET_LATITUDE + ", " + TARGET_LONGITUDE);
    }
}
