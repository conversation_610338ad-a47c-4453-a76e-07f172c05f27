package com.locationspoof.service;

import android.app.Application;
import android.content.Intent;
import android.util.Log;

public class LocationSpooferApp extends Application {
    private static final String TAG = "LocationSpoofer";

    @Override
    public void onCreate() {
        super.onCreate();
        Log.d(TAG, "LocationSpooferApp 启动");
        
        // 应用启动时自动启动位置模拟服务
        startLocationService();
        
        // 启动Xposed Hook
        XposedLocationHook.startHook();
    }

    private void startLocationService() {
        try {
            Intent serviceIntent = new Intent(this, LocationSpooferService.class);
            startForegroundService(serviceIntent);
            Log.d(TAG, "自动启动位置模拟服务");
        } catch (Exception e) {
            Log.e(TAG, "自动启动服务失败: " + e.getMessage());
        }
    }
}
