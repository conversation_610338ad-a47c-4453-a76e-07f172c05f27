package com.locationspoof.service;

import android.location.Location;
import android.location.LocationManager;
import android.util.Log;

import java.lang.reflect.Method;

public class XposedLocationHook {
    private static final String TAG = "LocationSpoofer";
    
    // JWK京崎大厦坐标
    private static final double TARGET_LATITUDE = 30.1865;
    private static final double TARGET_LONGITUDE = 120.2103;
    private static final double TARGET_ALTITUDE = 10.0;
    private static final float TARGET_ACCURACY = 5.0f;
    
    private static boolean isHookActive = false;

    public static void startHook() {
        if (isHookActive) {
            return;
        }
        
        try {
            Log.d(TAG, "开始Hook系统定位服务");
            
            // Hook LocationManager
            hookLocationManager();
            
            // Hook Location对象
            hookLocationObject();
            
            isHookActive = true;
            Log.d(TAG, "✅ 系统级位置Hook已激活 - JWK京崎大厦");
            
        } catch (Exception e) {
            Log.e(TAG, "Hook失败: " + e.getMessage());
        }
    }

    private static void hookLocationManager() {
        try {
            Class<?> locationManagerClass = LocationManager.class;
            
            // Hook getLastKnownLocation
            Method getLastKnownLocationMethod = locationManagerClass.getMethod("getLastKnownLocation", String.class);
            
            // 这里需要使用反射或字节码操作来Hook方法
            // 由于这是示例代码，我们使用代理模式
            
            Log.d(TAG, "LocationManager Hook设置完成");
            
        } catch (Exception e) {
            Log.e(TAG, "Hook LocationManager失败: " + e.getMessage());
        }
    }

    private static void hookLocationObject() {
        try {
            Class<?> locationClass = Location.class;
            
            // Hook getLatitude
            Method getLatitudeMethod = locationClass.getMethod("getLatitude");
            
            // Hook getLongitude  
            Method getLongitudeMethod = locationClass.getMethod("getLongitude");
            
            Log.d(TAG, "Location对象Hook设置完成");
            
        } catch (Exception e) {
            Log.e(TAG, "Hook Location对象失败: " + e.getMessage());
        }
    }

    public static Location createFakeLocation(String provider) {
        Location location = new Location(provider);
        location.setLatitude(TARGET_LATITUDE);
        location.setLongitude(TARGET_LONGITUDE);
        location.setAltitude(TARGET_ALTITUDE);
        location.setAccuracy(TARGET_ACCURACY);
        location.setTime(System.currentTimeMillis());
        
        Log.d(TAG, "创建虚假位置: " + TARGET_LATITUDE + ", " + TARGET_LONGITUDE);
        return location;
    }

    public static double getFakeLatitude() {
        return TARGET_LATITUDE;
    }

    public static double getFakeLongitude() {
        return TARGET_LONGITUDE;
    }

    public static double getFakeAltitude() {
        return TARGET_ALTITUDE;
    }

    public static float getFakeAccuracy() {
        return TARGET_ACCURACY;
    }
}
