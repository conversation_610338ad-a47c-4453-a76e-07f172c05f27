package com.locationspoof.service;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.provider.Settings;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

public class MainActivity extends Activity {
    private static final String TAG = "LocationSpoofer";
    private TextView statusText;
    private Button startButton;
    private Button stopButton;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // 创建简单的UI
        createUI();
        
        // 检查权限
        checkPermissions();
        
        // 自动启动服务
        autoStartService();
    }

    private void createUI() {
        // 创建垂直布局
        android.widget.LinearLayout layout = new android.widget.LinearLayout(this);
        layout.setOrientation(android.widget.LinearLayout.VERTICAL);
        layout.setPadding(50, 50, 50, 50);
        
        // 标题
        TextView titleText = new TextView(this);
        titleText.setText("JWK京崎大厦位置模拟器");
        titleText.setTextSize(20);
        titleText.setGravity(android.view.Gravity.CENTER);
        layout.addView(titleText);
        
        // 状态显示
        statusText = new TextView(this);
        statusText.setText("状态: 未启动");
        statusText.setTextSize(16);
        statusText.setPadding(0, 30, 0, 30);
        layout.addView(statusText);
        
        // 位置信息
        TextView locationText = new TextView(this);
        locationText.setText("目标位置: JWK京崎大厦\n坐标: 30.1865°N, 120.2103°E\n地址: 浙江省杭州市滨江区长河街道");
        locationText.setTextSize(14);
        locationText.setPadding(0, 20, 0, 30);
        layout.addView(locationText);
        
        // 启动按钮
        startButton = new Button(this);
        startButton.setText("启动位置模拟");
        startButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                startLocationService();
            }
        });
        layout.addView(startButton);
        
        // 停止按钮
        stopButton = new Button(this);
        stopButton.setText("停止位置模拟");
        stopButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                stopLocationService();
            }
        });
        layout.addView(stopButton);
        
        // 设置按钮
        Button settingsButton = new Button(this);
        settingsButton.setText("开启模拟位置权限");
        settingsButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                openDeveloperOptions();
            }
        });
        layout.addView(settingsButton);
        
        // 说明文字
        TextView infoText = new TextView(this);
        infoText.setText("\n使用说明:\n1. 点击'开启模拟位置权限'进入开发者选项\n2. 启用'模拟位置信息应用'并选择本应用\n3. 点击'启动位置模拟'\n4. 现在所有应用都会显示JWK京崎大厦位置");
        infoText.setTextSize(12);
        infoText.setPadding(0, 20, 0, 0);
        layout.addView(infoText);
        
        setContentView(layout);
    }

    private void checkPermissions() {
        // 检查是否有模拟位置权限
        try {
            String mockLocationApp = Settings.Secure.getString(
                getContentResolver(), 
                Settings.Secure.ALLOW_MOCK_LOCATION
            );
            
            if (mockLocationApp != null && mockLocationApp.contains(getPackageName())) {
                statusText.setText("状态: 权限已获取");
            } else {
                statusText.setText("状态: 需要模拟位置权限");
            }
        } catch (Exception e) {
            Log.e(TAG, "检查权限失败: " + e.getMessage());
        }
    }

    private void autoStartService() {
        // 自动启动服务
        startLocationService();
        
        // 隐藏应用（最小化到后台）
        moveTaskToBack(true);
    }

    private void startLocationService() {
        try {
            Intent serviceIntent = new Intent(this, LocationSpooferService.class);
            startForegroundService(serviceIntent);
            
            statusText.setText("状态: 位置模拟已启动");
            Toast.makeText(this, "JWK京崎大厦位置模拟已启动", Toast.LENGTH_SHORT).show();
            
            Log.d(TAG, "位置模拟服务已启动");
            
        } catch (Exception e) {
            Log.e(TAG, "启动服务失败: " + e.getMessage());
            Toast.makeText(this, "启动失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }

    private void stopLocationService() {
        try {
            Intent serviceIntent = new Intent(this, LocationSpooferService.class);
            stopService(serviceIntent);
            
            statusText.setText("状态: 位置模拟已停止");
            Toast.makeText(this, "位置模拟已停止", Toast.LENGTH_SHORT).show();
            
            Log.d(TAG, "位置模拟服务已停止");
            
        } catch (Exception e) {
            Log.e(TAG, "停止服务失败: " + e.getMessage());
        }
    }

    private void openDeveloperOptions() {
        try {
            Intent intent = new Intent(Settings.ACTION_APPLICATION_DEVELOPMENT_SETTINGS);
            startActivity(intent);
        } catch (Exception e) {
            // 如果无法直接打开开发者选项，打开设置
            Intent intent = new Intent(Settings.ACTION_SETTINGS);
            startActivity(intent);
            Toast.makeText(this, "请在设置中找到'开发者选项'", Toast.LENGTH_LONG).show();
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        checkPermissions();
    }
}
