apply plugin: 'com.android.application'

android {
    compileSdkVersion 33
    buildToolsVersion "33.0.2"

    defaultConfig {
        applicationId "com.locationspoof.service"
        minSdkVersion 21
        targetSdkVersion 33
        versionCode 1
        versionName "1.0"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {
    implementation 'androidx.appcompat:appcompat:1.6.1'
    
    // Xposed API
    compileOnly 'de.robv.android.xposed:api:82'
}
