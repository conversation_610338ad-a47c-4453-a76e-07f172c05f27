# NOTE: Do not modify this file.
#
# This file is generated via the -XX:DumpLoadedClassList=<class_list_file> option
# and is used at CDS archive dump time (see -Xshare:dump).
#
java/io/BufferedInputStream
java/io/BufferedOutputStream
java/io/BufferedWriter
java/io/ByteArrayInputStream
java/io/ByteArrayOutputStream
java/io/Closeable
java/io/DataInput
java/io/DataInputStream
java/io/DataOutput
java/io/DefaultFileSystem
java/io/File
java/io/File$PathStatus
java/io/FileCleanable
java/io/FileDescriptor
java/io/FileDescriptor$1
java/io/FileInputStream
java/io/FileInputStream$1
java/io/FileOutputStream
java/io/FilePermission
java/io/FileSystem
java/io/FilterInputStream
java/io/FilterOutputStream
java/io/Flushable
java/io/InputStream
java/io/ObjectStreamField
java/io/OutputStream
java/io/OutputStreamWriter
java/io/PrintStream
java/io/PrintStream$1
java/io/RandomAccessFile
java/io/RandomAccessFile$1
java/io/RandomAccessFile$2
java/io/Serializable
java/io/WinNTFileSystem
java/io/Writer
java/lang/AbstractStringBuilder
java/lang/Appendable
java/lang/ApplicationShutdownHooks
java/lang/ApplicationShutdownHooks$1
java/lang/ArithmeticException
java/lang/ArrayStoreException
java/lang/AssertionStatusDirectives
java/lang/AutoCloseable
java/lang/BaseVirtualThread
java/lang/Boolean
java/lang/BootstrapMethodError
java/lang/Byte
java/lang/CharSequence
java/lang/Character
java/lang/Character$CharacterCache
java/lang/CharacterData
java/lang/CharacterData00
java/lang/CharacterDataLatin1
java/lang/Class
java/lang/Class$1
java/lang/Class$3
java/lang/Class$Atomic
java/lang/Class$ReflectionData
java/lang/ClassCastException
java/lang/ClassLoader
java/lang/ClassLoader$ParallelLoaders
java/lang/ClassNotFoundException
java/lang/ClassValue
java/lang/ClassValue$Entry
java/lang/ClassValue$Identity
java/lang/ClassValue$Version
java/lang/Cloneable
java/lang/Comparable
java/lang/CompoundEnumeration
java/lang/Double
java/lang/Enum
java/lang/Error
java/lang/Exception
java/lang/Float
java/lang/IllegalArgumentException
java/lang/IllegalMonitorStateException
java/lang/IncompatibleClassChangeError
java/lang/Integer
java/lang/Integer$IntegerCache
java/lang/InternalError
java/lang/Iterable
java/lang/LinkageError
java/lang/LiveStackFrame
java/lang/LiveStackFrameInfo
java/lang/Long
java/lang/Long$LongCache
java/lang/Math
java/lang/Module
java/lang/Module$ArchivedData
java/lang/Module$EnableNativeAccess
java/lang/Module$ReflectionData
java/lang/ModuleLayer
java/lang/ModuleLayer$Controller
java/lang/NamedPackage
java/lang/NoClassDefFoundError
java/lang/NoSuchFieldException
java/lang/NoSuchMethodError
java/lang/NoSuchMethodException
java/lang/NullPointerException
java/lang/Number
java/lang/Object
java/lang/OutOfMemoryError
java/lang/Package
java/lang/Package$VersionInfo
java/lang/PublicMethods$Key
java/lang/PublicMethods$MethodList
java/lang/Readable
java/lang/Record
java/lang/ReflectiveOperationException
java/lang/Runnable
java/lang/Runtime
java/lang/Runtime$Version
java/lang/RuntimeException
java/lang/RuntimePermission
java/lang/SecurityManager
java/lang/Short
java/lang/Shutdown
java/lang/Shutdown$Lock
java/lang/StackFrameInfo
java/lang/StackOverflowError
java/lang/StackStreamFactory$AbstractStackWalker
java/lang/StackTraceElement
java/lang/StackWalker
java/lang/StackWalker$StackFrame
java/lang/StrictMath
java/lang/String
java/lang/String$CaseInsensitiveComparator
java/lang/StringBuffer
java/lang/StringBuilder
java/lang/StringCoding
java/lang/StringConcatHelper
java/lang/StringLatin1
java/lang/StringLatin1$CharsSpliterator
java/lang/StringUTF16
java/lang/StringUTF16$CharsSpliterator
java/lang/System
java/lang/System$2
java/lang/System$Logger
java/lang/System$LoggerFinder
java/lang/Terminator
java/lang/Terminator$1
java/lang/Thread
java/lang/Thread$Constants
java/lang/Thread$FieldHolder
java/lang/Thread$State
java/lang/Thread$ThreadIdentifiers
java/lang/Thread$UncaughtExceptionHandler
java/lang/ThreadBuilders$BoundVirtualThread
java/lang/ThreadGroup
java/lang/ThreadLocal
java/lang/ThreadLocal$ThreadLocalMap
java/lang/ThreadLocal$ThreadLocalMap$Entry
java/lang/Throwable
java/lang/VersionProps
java/lang/VirtualMachineError
java/lang/VirtualThread
java/lang/Void
java/lang/WeakPairMap
java/lang/WeakPairMap$Pair
java/lang/WeakPairMap$Pair$Lookup
java/lang/annotation/Annotation
java/lang/constant/Constable
java/lang/constant/ConstantDesc
java/lang/invoke/AbstractValidatingLambdaMetafactory
java/lang/invoke/BootstrapMethodInvoker
java/lang/invoke/BoundMethodHandle
java/lang/invoke/BoundMethodHandle$Specializer
java/lang/invoke/BoundMethodHandle$Specializer$Factory
java/lang/invoke/BoundMethodHandle$SpeciesData
java/lang/invoke/BoundMethodHandle$Species_D
java/lang/invoke/BoundMethodHandle$Species_DL
java/lang/invoke/BoundMethodHandle$Species_I
java/lang/invoke/BoundMethodHandle$Species_IL
java/lang/invoke/BoundMethodHandle$Species_L
java/lang/invoke/BoundMethodHandle$Species_LJ
java/lang/invoke/BoundMethodHandle$Species_LL
java/lang/invoke/BoundMethodHandle$Species_LLJ
java/lang/invoke/BoundMethodHandle$Species_LLL
java/lang/invoke/BoundMethodHandle$Species_LLLJ
java/lang/invoke/BoundMethodHandle$Species_LLLL
java/lang/invoke/BoundMethodHandle$Species_LLLLL
java/lang/invoke/BoundMethodHandle$Species_LLLLLL
java/lang/invoke/BoundMethodHandle$Species_LLLLLLL
java/lang/invoke/BoundMethodHandle$Species_LLLLLLLL
java/lang/invoke/BoundMethodHandle$Species_LLLLLLLLL
java/lang/invoke/CallSite
java/lang/invoke/ClassSpecializer
java/lang/invoke/ClassSpecializer$1
java/lang/invoke/ClassSpecializer$Factory
java/lang/invoke/ClassSpecializer$SpeciesData
java/lang/invoke/ConstantCallSite
java/lang/invoke/DelegatingMethodHandle
java/lang/invoke/DelegatingMethodHandle$Holder
java/lang/invoke/DirectMethodHandle
java/lang/invoke/DirectMethodHandle$2
java/lang/invoke/DirectMethodHandle$Accessor
java/lang/invoke/DirectMethodHandle$Constructor
java/lang/invoke/DirectMethodHandle$Holder
java/lang/invoke/DirectMethodHandle$Interface
java/lang/invoke/InfoFromMemberName
java/lang/invoke/InnerClassLambdaMetafactory
java/lang/invoke/InnerClassLambdaMetafactory$ForwardingMethodGenerator
java/lang/invoke/InvokerBytecodeGenerator
java/lang/invoke/InvokerBytecodeGenerator$1
java/lang/invoke/InvokerBytecodeGenerator$ClassData
java/lang/invoke/Invokers
java/lang/invoke/Invokers$Holder
java/lang/invoke/LambdaForm
java/lang/invoke/LambdaForm$BasicType
java/lang/invoke/LambdaForm$Holder
java/lang/invoke/LambdaForm$Kind
java/lang/invoke/LambdaForm$Name
java/lang/invoke/LambdaForm$NamedFunction
java/lang/invoke/LambdaFormBuffer
java/lang/invoke/LambdaFormEditor
java/lang/invoke/LambdaFormEditor$1
java/lang/invoke/LambdaFormEditor$Transform
java/lang/invoke/LambdaFormEditor$TransformKey
java/lang/invoke/LambdaMetafactory
java/lang/invoke/LambdaProxyClassArchive
java/lang/invoke/MemberName
java/lang/invoke/MemberName$Factory
java/lang/invoke/MethodHandle
java/lang/invoke/MethodHandleImpl
java/lang/invoke/MethodHandleImpl$1
java/lang/invoke/MethodHandleImpl$AsVarargsCollector
java/lang/invoke/MethodHandleImpl$Intrinsic
java/lang/invoke/MethodHandleImpl$IntrinsicMethodHandle
java/lang/invoke/MethodHandleInfo
java/lang/invoke/MethodHandleNatives
java/lang/invoke/MethodHandleNatives$CallSiteContext
java/lang/invoke/MethodHandleStatics
java/lang/invoke/MethodHandles
java/lang/invoke/MethodHandles$1
java/lang/invoke/MethodHandles$Lookup
java/lang/invoke/MethodHandles$Lookup$ClassDefiner
java/lang/invoke/MethodHandles$Lookup$ClassFile
java/lang/invoke/MethodHandles$Lookup$ClassOption
java/lang/invoke/MethodType
java/lang/invoke/MethodType$ConcurrentWeakInternSet
java/lang/invoke/MethodType$ConcurrentWeakInternSet$WeakEntry
java/lang/invoke/MethodTypeForm
java/lang/invoke/MutableCallSite
java/lang/invoke/ResolvedMethodName
java/lang/invoke/SimpleMethodHandle
java/lang/invoke/StringConcatFactory
java/lang/invoke/TypeConvertingMethodAdapter
java/lang/invoke/TypeDescriptor
java/lang/invoke/TypeDescriptor$OfField
java/lang/invoke/TypeDescriptor$OfMethod
java/lang/invoke/VarForm
java/lang/invoke/VarHandle
java/lang/invoke/VarHandle$AccessDescriptor
java/lang/invoke/VarHandle$AccessMode
java/lang/invoke/VarHandle$AccessType
java/lang/invoke/VarHandleBooleans$FieldInstanceReadOnly
java/lang/invoke/VarHandleBooleans$FieldInstanceReadWrite
java/lang/invoke/VarHandleByteArrayAsChars$ArrayHandle
java/lang/invoke/VarHandleByteArrayAsChars$ByteArrayViewVarHandle
java/lang/invoke/VarHandleByteArrayAsDoubles$ArrayHandle
java/lang/invoke/VarHandleByteArrayAsDoubles$ByteArrayViewVarHandle
java/lang/invoke/VarHandleByteArrayAsFloats$ArrayHandle
java/lang/invoke/VarHandleByteArrayAsFloats$ByteArrayViewVarHandle
java/lang/invoke/VarHandleByteArrayAsInts$ArrayHandle
java/lang/invoke/VarHandleByteArrayAsInts$ByteArrayViewVarHandle
java/lang/invoke/VarHandleByteArrayAsLongs$ArrayHandle
java/lang/invoke/VarHandleByteArrayAsLongs$ByteArrayViewVarHandle
java/lang/invoke/VarHandleByteArrayAsShorts$ArrayHandle
java/lang/invoke/VarHandleByteArrayAsShorts$ByteArrayViewVarHandle
java/lang/invoke/VarHandleGuards
java/lang/invoke/VarHandleLongs$FieldInstanceReadOnly
java/lang/invoke/VarHandleLongs$FieldInstanceReadWrite
java/lang/invoke/VarHandleReferences$FieldInstanceReadOnly
java/lang/invoke/VarHandleReferences$FieldInstanceReadWrite
java/lang/invoke/VarHandles
java/lang/invoke/VarHandles$1
java/lang/invoke/VolatileCallSite
java/lang/module/Configuration
java/lang/module/ModuleDescriptor
java/lang/module/ModuleDescriptor$1
java/lang/module/ModuleDescriptor$Builder
java/lang/module/ModuleDescriptor$Exports
java/lang/module/ModuleDescriptor$Modifier
java/lang/module/ModuleDescriptor$Opens
java/lang/module/ModuleDescriptor$Provides
java/lang/module/ModuleDescriptor$Requires
java/lang/module/ModuleDescriptor$Requires$Modifier
java/lang/module/ModuleDescriptor$Version
java/lang/module/ModuleFinder
java/lang/module/ModuleFinder$1
java/lang/module/ModuleFinder$2
java/lang/module/ModuleReader
java/lang/module/ModuleReference
java/lang/module/ResolvedModule
java/lang/module/Resolver
java/lang/ref/Cleaner
java/lang/ref/Cleaner$1
java/lang/ref/Cleaner$Cleanable
java/lang/ref/FinalReference
java/lang/ref/Finalizer
java/lang/ref/Finalizer$FinalizerThread
java/lang/ref/NativeReferenceQueue
java/lang/ref/NativeReferenceQueue$Lock
java/lang/ref/PhantomReference
java/lang/ref/Reference
java/lang/ref/Reference$1
java/lang/ref/Reference$ReferenceHandler
java/lang/ref/ReferenceQueue
java/lang/ref/ReferenceQueue$Null
java/lang/ref/SoftReference
java/lang/ref/WeakReference
java/lang/reflect/AccessFlag
java/lang/reflect/AccessFlag$1
java/lang/reflect/AccessFlag$10
java/lang/reflect/AccessFlag$11
java/lang/reflect/AccessFlag$12
java/lang/reflect/AccessFlag$13
java/lang/reflect/AccessFlag$14
java/lang/reflect/AccessFlag$15
java/lang/reflect/AccessFlag$16
java/lang/reflect/AccessFlag$17
java/lang/reflect/AccessFlag$18
java/lang/reflect/AccessFlag$2
java/lang/reflect/AccessFlag$3
java/lang/reflect/AccessFlag$4
java/lang/reflect/AccessFlag$5
java/lang/reflect/AccessFlag$6
java/lang/reflect/AccessFlag$7
java/lang/reflect/AccessFlag$8
java/lang/reflect/AccessFlag$9
java/lang/reflect/AccessFlag$Location
java/lang/reflect/AccessibleObject
java/lang/reflect/AnnotatedElement
java/lang/reflect/Array
java/lang/reflect/ClassFileFormatVersion
java/lang/reflect/Constructor
java/lang/reflect/Executable
java/lang/reflect/Field
java/lang/reflect/GenericDeclaration
java/lang/reflect/Member
java/lang/reflect/Method
java/lang/reflect/Modifier
java/lang/reflect/Parameter
java/lang/reflect/RecordComponent
java/lang/reflect/ReflectAccess
java/lang/reflect/Type
java/math/BigInteger
java/math/RoundingMode
java/net/DefaultInterface
java/net/Inet4Address
java/net/Inet4AddressImpl
java/net/Inet6Address
java/net/Inet6Address$Inet6AddressHolder
java/net/Inet6AddressImpl
java/net/InetAddress
java/net/InetAddress$1
java/net/InetAddress$InetAddressHolder
java/net/InetAddress$PlatformResolver
java/net/InetAddressImpl
java/net/InterfaceAddress
java/net/NetworkInterface
java/net/URI
java/net/URI$1
java/net/URI$Parser
java/net/URL
java/net/URL$3
java/net/URL$DefaultFactory
java/net/URLClassLoader
java/net/URLStreamHandler
java/net/URLStreamHandlerFactory
java/net/spi/InetAddressResolver
java/net/spi/InetAddressResolver$LookupPolicy
java/nio/Bits
java/nio/Bits$1
java/nio/Buffer
java/nio/Buffer$1
java/nio/Buffer$2
java/nio/ByteBuffer
java/nio/ByteOrder
java/nio/CharBuffer
java/nio/DirectByteBuffer
java/nio/DirectByteBufferR
java/nio/DirectIntBufferRU
java/nio/DirectIntBufferU
java/nio/DirectLongBufferU
java/nio/HeapByteBuffer
java/nio/HeapCharBuffer
java/nio/IntBuffer
java/nio/LongBuffer
java/nio/MappedByteBuffer
java/nio/charset/Charset
java/nio/charset/CharsetDecoder
java/nio/charset/CharsetEncoder
java/nio/charset/CoderResult
java/nio/charset/CodingErrorAction
java/nio/charset/StandardCharsets
java/nio/charset/spi/CharsetProvider
java/nio/file/CopyOption
java/nio/file/FileSystem
java/nio/file/FileSystems
java/nio/file/FileSystems$DefaultFileSystemHolder
java/nio/file/FileSystems$DefaultFileSystemHolder$1
java/nio/file/Files
java/nio/file/LinkOption
java/nio/file/OpenOption
java/nio/file/Path
java/nio/file/Paths
java/nio/file/StandardOpenOption
java/nio/file/Watchable
java/nio/file/attribute/AttributeView
java/nio/file/attribute/BasicFileAttributeView
java/nio/file/attribute/BasicFileAttributes
java/nio/file/attribute/DosFileAttributes
java/nio/file/attribute/FileAttributeView
java/nio/file/attribute/FileTime
java/nio/file/spi/FileSystemProvider
java/security/AccessControlContext
java/security/AccessController
java/security/AllPermission
java/security/BasicPermission
java/security/BasicPermissionCollection
java/security/CodeSource
java/security/Guard
java/security/Permission
java/security/PermissionCollection
java/security/Permissions
java/security/Principal
java/security/PrivilegedAction
java/security/PrivilegedExceptionAction
java/security/ProtectionDomain
java/security/ProtectionDomain$JavaSecurityAccessImpl
java/security/ProtectionDomain$Key
java/security/SecureClassLoader
java/security/SecureClassLoader$1
java/security/SecureClassLoader$CodeSourceKey
java/security/SecureClassLoader$DebugHolder
java/security/Security
java/security/Security$1
java/security/UnresolvedPermission
java/security/cert/Certificate
java/text/AttributedCharacterIterator$Attribute
java/text/DateFormat
java/text/DateFormat$Field
java/text/DateFormatSymbols
java/text/DecimalFormat
java/text/DecimalFormatSymbols
java/text/DigitList
java/text/DontCareFieldPosition
java/text/DontCareFieldPosition$1
java/text/FieldPosition
java/text/Format
java/text/Format$Field
java/text/Format$FieldDelegate
java/text/NumberFormat
java/text/NumberFormat$Field
java/text/SimpleDateFormat
java/text/spi/BreakIteratorProvider
java/text/spi/CollatorProvider
java/text/spi/DateFormatProvider
java/text/spi/DateFormatSymbolsProvider
java/text/spi/DecimalFormatSymbolsProvider
java/text/spi/NumberFormatProvider
java/time/Clock
java/time/Clock$SystemClock
java/time/Duration
java/time/Instant
java/time/InstantSource
java/time/LocalDate
java/time/LocalDate$1
java/time/LocalDateTime
java/time/LocalTime
java/time/LocalTime$1
java/time/Period
java/time/ZoneId
java/time/ZoneOffset
java/time/ZoneRegion
java/time/chrono/AbstractChronology
java/time/chrono/ChronoLocalDate
java/time/chrono/ChronoLocalDateTime
java/time/chrono/ChronoPeriod
java/time/chrono/Chronology
java/time/chrono/IsoChronology
java/time/format/DateTimeFormatter
java/time/format/DateTimeFormatterBuilder
java/time/format/DateTimeFormatterBuilder$1
java/time/format/DateTimeFormatterBuilder$2
java/time/format/DateTimeFormatterBuilder$CharLiteralPrinterParser
java/time/format/DateTimeFormatterBuilder$CompositePrinterParser
java/time/format/DateTimeFormatterBuilder$DateTimePrinterParser
java/time/format/DateTimeFormatterBuilder$InstantPrinterParser
java/time/format/DateTimeFormatterBuilder$NanosPrinterParser
java/time/format/DateTimeFormatterBuilder$NumberPrinterParser
java/time/format/DateTimeFormatterBuilder$OffsetIdPrinterParser
java/time/format/DateTimeFormatterBuilder$SettingsParser
java/time/format/DateTimeFormatterBuilder$StringLiteralPrinterParser
java/time/format/DateTimeFormatterBuilder$TextPrinterParser
java/time/format/DateTimeFormatterBuilder$ZoneIdPrinterParser
java/time/format/DateTimePrintContext
java/time/format/DateTimeTextProvider
java/time/format/DateTimeTextProvider$1
java/time/format/DateTimeTextProvider$LocaleStore
java/time/format/DecimalStyle
java/time/format/ResolverStyle
java/time/format/SignStyle
java/time/format/TextStyle
java/time/temporal/ChronoField
java/time/temporal/ChronoUnit
java/time/temporal/IsoFields
java/time/temporal/IsoFields$Field
java/time/temporal/IsoFields$Field$1
java/time/temporal/IsoFields$Field$2
java/time/temporal/IsoFields$Field$3
java/time/temporal/IsoFields$Field$4
java/time/temporal/IsoFields$Unit
java/time/temporal/JulianFields
java/time/temporal/JulianFields$Field
java/time/temporal/Temporal
java/time/temporal/TemporalAccessor
java/time/temporal/TemporalAdjuster
java/time/temporal/TemporalAmount
java/time/temporal/TemporalField
java/time/temporal/TemporalQueries
java/time/temporal/TemporalQueries$1
java/time/temporal/TemporalQueries$2
java/time/temporal/TemporalQueries$3
java/time/temporal/TemporalQueries$4
java/time/temporal/TemporalQueries$5
java/time/temporal/TemporalQueries$6
java/time/temporal/TemporalQueries$7
java/time/temporal/TemporalQuery
java/time/temporal/TemporalUnit
java/time/temporal/ValueRange
java/time/zone/ZoneOffsetTransitionRule
java/time/zone/ZoneRules
java/util/AbstractCollection
java/util/AbstractList
java/util/AbstractList$RandomAccessSpliterator
java/util/AbstractMap
java/util/AbstractMap$SimpleImmutableEntry
java/util/AbstractSet
java/util/ArrayDeque
java/util/ArrayDeque$DeqIterator
java/util/ArrayList
java/util/ArrayList$ArrayListSpliterator
java/util/ArrayList$Itr
java/util/ArrayList$SubList
java/util/Arrays
java/util/Arrays$ArrayList
java/util/Arrays$LegacyMergeSort
java/util/Calendar
java/util/Calendar$Builder
java/util/Collection
java/util/Collections
java/util/Collections$1
java/util/Collections$3
java/util/Collections$EmptyEnumeration
java/util/Collections$EmptyIterator
java/util/Collections$EmptyList
java/util/Collections$EmptyMap
java/util/Collections$EmptySet
java/util/Collections$SetFromMap
java/util/Collections$SingletonMap
java/util/Collections$SingletonSet
java/util/Collections$SynchronizedCollection
java/util/Collections$SynchronizedMap
java/util/Collections$SynchronizedSet
java/util/Collections$UnmodifiableCollection
java/util/Collections$UnmodifiableCollection$1
java/util/Collections$UnmodifiableList
java/util/Collections$UnmodifiableRandomAccessList
java/util/Collections$UnmodifiableSet
java/util/Comparator
java/util/Date
java/util/Deque
java/util/Dictionary
java/util/EnumMap
java/util/EnumMap$1
java/util/EnumSet
java/util/Enumeration
java/util/Formattable
java/util/Formatter
java/util/Formatter$Conversion
java/util/Formatter$Flags
java/util/Formatter$FormatSpecifier
java/util/Formatter$FormatString
java/util/GregorianCalendar
java/util/HashMap
java/util/HashMap$EntryIterator
java/util/HashMap$EntrySet
java/util/HashMap$HashIterator
java/util/HashMap$HashMapSpliterator
java/util/HashMap$KeyIterator
java/util/HashMap$KeySet
java/util/HashMap$KeySpliterator
java/util/HashMap$Node
java/util/HashMap$TreeNode
java/util/HashMap$ValueIterator
java/util/HashMap$ValueSpliterator
java/util/HashMap$Values
java/util/HashSet
java/util/Hashtable
java/util/Hashtable$Entry
java/util/Hashtable$Enumerator
java/util/HexFormat
java/util/IdentityHashMap
java/util/IdentityHashMap$IdentityHashMapIterator
java/util/IdentityHashMap$KeyIterator
java/util/IdentityHashMap$KeySet
java/util/IdentityHashMap$Values
java/util/ImmutableCollections
java/util/ImmutableCollections$AbstractImmutableCollection
java/util/ImmutableCollections$AbstractImmutableList
java/util/ImmutableCollections$AbstractImmutableMap
java/util/ImmutableCollections$AbstractImmutableSet
java/util/ImmutableCollections$List12
java/util/ImmutableCollections$ListItr
java/util/ImmutableCollections$ListN
java/util/ImmutableCollections$Map1
java/util/ImmutableCollections$MapN
java/util/ImmutableCollections$MapN$1
java/util/ImmutableCollections$MapN$MapNIterator
java/util/ImmutableCollections$Set12
java/util/ImmutableCollections$Set12$1
java/util/ImmutableCollections$SetN
java/util/ImmutableCollections$SetN$SetNIterator
java/util/Iterator
java/util/KeyValueHolder
java/util/LinkedHashMap
java/util/LinkedHashMap$Entry
java/util/LinkedHashMap$LinkedEntryIterator
java/util/LinkedHashMap$LinkedEntrySet
java/util/LinkedHashMap$LinkedHashIterator
java/util/LinkedHashSet
java/util/List
java/util/ListIterator
java/util/ListResourceBundle
java/util/Locale
java/util/Locale$Builder
java/util/Locale$Cache
java/util/Locale$Category
java/util/Map
java/util/Map$Entry
java/util/NavigableMap
java/util/NavigableSet
java/util/Objects
java/util/Optional
java/util/OptionalInt
java/util/Properties
java/util/Properties$EntrySet
java/util/Properties$LineReader
java/util/Queue
java/util/Random
java/util/RandomAccess
java/util/RegularEnumSet
java/util/ResourceBundle
java/util/ResourceBundle$1
java/util/ResourceBundle$2
java/util/ResourceBundle$Control
java/util/ResourceBundle$Control$CandidateListCache
java/util/ResourceBundle$NoFallbackControl
java/util/ResourceBundle$ResourceBundleProviderHelper
java/util/ResourceBundle$SingleFormatControl
java/util/SequencedCollection
java/util/SequencedMap
java/util/SequencedSet
java/util/ServiceLoader
java/util/ServiceLoader$1
java/util/ServiceLoader$2
java/util/ServiceLoader$3
java/util/ServiceLoader$LazyClassPathLookupIterator
java/util/ServiceLoader$ModuleServicesLookupIterator
java/util/ServiceLoader$Provider
java/util/ServiceLoader$ProviderImpl
java/util/Set
java/util/SortedMap
java/util/SortedSet
java/util/Spliterator
java/util/Spliterator$OfDouble
java/util/Spliterator$OfInt
java/util/Spliterator$OfLong
java/util/Spliterator$OfPrimitive
java/util/Spliterators
java/util/Spliterators$1Adapter
java/util/Spliterators$AbstractSpliterator
java/util/Spliterators$ArraySpliterator
java/util/Spliterators$EmptySpliterator
java/util/Spliterators$EmptySpliterator$OfDouble
java/util/Spliterators$EmptySpliterator$OfInt
java/util/Spliterators$EmptySpliterator$OfLong
java/util/Spliterators$EmptySpliterator$OfRef
java/util/Spliterators$IteratorSpliterator
java/util/StringJoiner
java/util/TimSort
java/util/TimeZone
java/util/TreeMap
java/util/TreeMap$Entry
java/util/TreeMap$EntryIterator
java/util/TreeMap$EntrySet
java/util/TreeMap$PrivateEntryIterator
java/util/WeakHashMap
java/util/WeakHashMap$Entry
java/util/WeakHashMap$KeySet
java/util/concurrent/AbstractExecutorService
java/util/concurrent/ConcurrentHashMap
java/util/concurrent/ConcurrentHashMap$BaseIterator
java/util/concurrent/ConcurrentHashMap$CollectionView
java/util/concurrent/ConcurrentHashMap$CounterCell
java/util/concurrent/ConcurrentHashMap$EntryIterator
java/util/concurrent/ConcurrentHashMap$EntrySetView
java/util/concurrent/ConcurrentHashMap$ForwardingNode
java/util/concurrent/ConcurrentHashMap$KeyIterator
java/util/concurrent/ConcurrentHashMap$KeySetView
java/util/concurrent/ConcurrentHashMap$MapEntry
java/util/concurrent/ConcurrentHashMap$Node
java/util/concurrent/ConcurrentHashMap$ReservationNode
java/util/concurrent/ConcurrentHashMap$Segment
java/util/concurrent/ConcurrentHashMap$Traverser
java/util/concurrent/ConcurrentHashMap$ValueIterator
java/util/concurrent/ConcurrentHashMap$ValuesView
java/util/concurrent/ConcurrentMap
java/util/concurrent/ConcurrentNavigableMap
java/util/concurrent/ConcurrentSkipListMap
java/util/concurrent/ConcurrentSkipListMap$Index
java/util/concurrent/ConcurrentSkipListMap$Node
java/util/concurrent/ConcurrentSkipListSet
java/util/concurrent/CopyOnWriteArrayList
java/util/concurrent/CopyOnWriteArrayList$COWIterator
java/util/concurrent/CountedCompleter
java/util/concurrent/Executor
java/util/concurrent/ExecutorService
java/util/concurrent/ForkJoinPool
java/util/concurrent/ForkJoinPool$2
java/util/concurrent/ForkJoinPool$DefaultForkJoinWorkerThreadFactory
java/util/concurrent/ForkJoinPool$ForkJoinWorkerThreadFactory
java/util/concurrent/ForkJoinPool$ManagedBlocker
java/util/concurrent/ForkJoinPool$WorkQueue
java/util/concurrent/ForkJoinTask
java/util/concurrent/ForkJoinTask$Aux
java/util/concurrent/ForkJoinWorkerThread
java/util/concurrent/Future
java/util/concurrent/ThreadFactory
java/util/concurrent/ThreadLocalRandom
java/util/concurrent/TimeUnit
java/util/concurrent/atomic/AtomicInteger
java/util/concurrent/atomic/AtomicLong
java/util/concurrent/atomic/LongAdder
java/util/concurrent/atomic/Striped64
java/util/concurrent/atomic/Striped64$Cell
java/util/concurrent/locks/AbstractOwnableSynchronizer
java/util/concurrent/locks/AbstractQueuedSynchronizer
java/util/concurrent/locks/AbstractQueuedSynchronizer$ConditionNode
java/util/concurrent/locks/AbstractQueuedSynchronizer$ConditionObject
java/util/concurrent/locks/AbstractQueuedSynchronizer$ExclusiveNode
java/util/concurrent/locks/AbstractQueuedSynchronizer$Node
java/util/concurrent/locks/Condition
java/util/concurrent/locks/Lock
java/util/concurrent/locks/LockSupport
java/util/concurrent/locks/ReentrantLock
java/util/concurrent/locks/ReentrantLock$NonfairSync
java/util/concurrent/locks/ReentrantLock$Sync
java/util/function/BiConsumer
java/util/function/BiFunction
java/util/function/BinaryOperator
java/util/function/Consumer
java/util/function/Function
java/util/function/IntConsumer
java/util/function/IntFunction
java/util/function/IntPredicate
java/util/function/Predicate
java/util/function/Supplier
java/util/jar/Attributes
java/util/jar/Attributes$Name
java/util/jar/JarEntry
java/util/jar/JarFile
java/util/jar/JarFile$JarFileEntry
java/util/jar/JarVerifier
java/util/jar/JavaUtilJarAccessImpl
java/util/jar/Manifest
java/util/jar/Manifest$FastInputStream
java/util/logging/Handler
java/util/logging/Level
java/util/logging/Level$KnownLevel
java/util/logging/LogManager
java/util/logging/LogManager$1
java/util/logging/LogManager$2
java/util/logging/LogManager$4
java/util/logging/LogManager$Cleaner
java/util/logging/LogManager$LogNode
java/util/logging/LogManager$LoggerContext
java/util/logging/LogManager$LoggerContext$1
java/util/logging/LogManager$LoggerWeakRef
java/util/logging/LogManager$LoggingProviderAccess
java/util/logging/LogManager$RootLogger
java/util/logging/LogManager$SystemLoggerContext
java/util/logging/LogManager$VisitedLoggers
java/util/logging/Logger
java/util/logging/Logger$ConfigurationData
java/util/logging/Logger$LoggerBundle
java/util/logging/Logger$SystemLoggerHelper
java/util/logging/Logger$SystemLoggerHelper$1
java/util/logging/LoggingPermission
java/util/random/RandomGenerator
java/util/regex/ASCII
java/util/regex/CharPredicates
java/util/regex/IntHashSet
java/util/regex/MatchResult
java/util/regex/Matcher
java/util/regex/Pattern
java/util/regex/Pattern$BackRef
java/util/regex/Pattern$Begin
java/util/regex/Pattern$BitClass
java/util/regex/Pattern$BmpCharPredicate
java/util/regex/Pattern$BmpCharProperty
java/util/regex/Pattern$BmpCharPropertyGreedy
java/util/regex/Pattern$Branch
java/util/regex/Pattern$BranchConn
java/util/regex/Pattern$CharPredicate
java/util/regex/Pattern$CharProperty
java/util/regex/Pattern$CharPropertyGreedy
java/util/regex/Pattern$Curly
java/util/regex/Pattern$Dollar
java/util/regex/Pattern$First
java/util/regex/Pattern$GroupCurly
java/util/regex/Pattern$GroupHead
java/util/regex/Pattern$GroupTail
java/util/regex/Pattern$LastNode
java/util/regex/Pattern$Node
java/util/regex/Pattern$Qtype
java/util/regex/Pattern$Ques
java/util/regex/Pattern$Slice
java/util/regex/Pattern$SliceNode
java/util/regex/Pattern$Start
java/util/regex/Pattern$StartS
java/util/regex/Pattern$TreeInfo
java/util/spi/CalendarDataProvider
java/util/spi/CurrencyNameProvider
java/util/spi/LocaleNameProvider
java/util/spi/LocaleServiceProvider
java/util/spi/TimeZoneNameProvider
java/util/stream/AbstractPipeline
java/util/stream/AbstractTask
java/util/stream/BaseStream
java/util/stream/Collector
java/util/stream/Collector$Characteristics
java/util/stream/Collectors
java/util/stream/Collectors$CollectorImpl
java/util/stream/Collectors$Partition
java/util/stream/FindOps
java/util/stream/FindOps$FindOp
java/util/stream/FindOps$FindSink
java/util/stream/FindOps$FindSink$OfInt
java/util/stream/FindOps$FindSink$OfRef
java/util/stream/ForEachOps
java/util/stream/ForEachOps$ForEachOp
java/util/stream/ForEachOps$ForEachOp$OfRef
java/util/stream/IntPipeline
java/util/stream/IntPipeline$10
java/util/stream/IntPipeline$10$1
java/util/stream/IntPipeline$Head
java/util/stream/IntPipeline$StatelessOp
java/util/stream/IntStream
java/util/stream/Node
java/util/stream/Node$Builder
java/util/stream/Node$OfDouble
java/util/stream/Node$OfInt
java/util/stream/Node$OfLong
java/util/stream/Node$OfPrimitive
java/util/stream/Nodes
java/util/stream/Nodes$ArrayNode
java/util/stream/Nodes$EmptyNode
java/util/stream/Nodes$EmptyNode$OfDouble
java/util/stream/Nodes$EmptyNode$OfInt
java/util/stream/Nodes$EmptyNode$OfLong
java/util/stream/Nodes$EmptyNode$OfRef
java/util/stream/Nodes$FixedNodeBuilder
java/util/stream/PipelineHelper
java/util/stream/ReduceOps
java/util/stream/ReduceOps$3
java/util/stream/ReduceOps$3ReducingSink
java/util/stream/ReduceOps$AccumulatingSink
java/util/stream/ReduceOps$Box
java/util/stream/ReduceOps$ReduceOp
java/util/stream/ReduceOps$ReduceTask
java/util/stream/ReferencePipeline
java/util/stream/ReferencePipeline$2
java/util/stream/ReferencePipeline$2$1
java/util/stream/ReferencePipeline$3
java/util/stream/ReferencePipeline$3$1
java/util/stream/ReferencePipeline$7
java/util/stream/ReferencePipeline$7$1
java/util/stream/ReferencePipeline$Head
java/util/stream/ReferencePipeline$StatelessOp
java/util/stream/Sink
java/util/stream/Sink$ChainedInt
java/util/stream/Sink$ChainedReference
java/util/stream/Sink$OfInt
java/util/stream/Stream
java/util/stream/Stream$Builder
java/util/stream/StreamOpFlag
java/util/stream/StreamOpFlag$MaskBuilder
java/util/stream/StreamOpFlag$Type
java/util/stream/StreamShape
java/util/stream/StreamSupport
java/util/stream/Streams
java/util/stream/Streams$AbstractStreamBuilderImpl
java/util/stream/Streams$StreamBuilderImpl
java/util/stream/TerminalOp
java/util/stream/TerminalSink
java/util/zip/CRC32
java/util/zip/Checksum
java/util/zip/Checksum$1
java/util/zip/Inflater
java/util/zip/Inflater$InflaterZStreamRef
java/util/zip/InflaterInputStream
java/util/zip/ZipCoder
java/util/zip/ZipCoder$Comparison
java/util/zip/ZipCoder$UTF8ZipCoder
java/util/zip/ZipConstants
java/util/zip/ZipEntry
java/util/zip/ZipFile
java/util/zip/ZipFile$1
java/util/zip/ZipFile$2
java/util/zip/ZipFile$CleanableResource
java/util/zip/ZipFile$EntrySpliterator
java/util/zip/ZipFile$InflaterCleanupAction
java/util/zip/ZipFile$Source
java/util/zip/ZipFile$Source$End
java/util/zip/ZipFile$Source$Key
java/util/zip/ZipFile$ZipFileInflaterInputStream
java/util/zip/ZipFile$ZipFileInputStream
java/util/zip/ZipUtils
jdk/internal/access/JavaIOFileDescriptorAccess
jdk/internal/access/JavaIOPrintStreamAccess
jdk/internal/access/JavaIORandomAccessFileAccess
jdk/internal/access/JavaLangAccess
jdk/internal/access/JavaLangInvokeAccess
jdk/internal/access/JavaLangModuleAccess
jdk/internal/access/JavaLangRefAccess
jdk/internal/access/JavaLangReflectAccess
jdk/internal/access/JavaNetInetAddressAccess
jdk/internal/access/JavaNetURLAccess
jdk/internal/access/JavaNetUriAccess
jdk/internal/access/JavaNioAccess
jdk/internal/access/JavaSecurityAccess
jdk/internal/access/JavaSecurityPropertiesAccess
jdk/internal/access/JavaUtilConcurrentFJPAccess
jdk/internal/access/JavaUtilJarAccess
jdk/internal/access/JavaUtilResourceBundleAccess
jdk/internal/access/JavaUtilZipFileAccess
jdk/internal/access/SharedSecrets
jdk/internal/foreign/abi/ABIDescriptor
jdk/internal/foreign/abi/NativeEntryPoint
jdk/internal/foreign/abi/UpcallLinker$CallRegs
jdk/internal/foreign/abi/VMStorage
jdk/internal/jimage/BasicImageReader
jdk/internal/jimage/BasicImageReader$1
jdk/internal/jimage/ImageHeader
jdk/internal/jimage/ImageLocation
jdk/internal/jimage/ImageReader
jdk/internal/jimage/ImageReader$SharedImageReader
jdk/internal/jimage/ImageReaderFactory
jdk/internal/jimage/ImageReaderFactory$1
jdk/internal/jimage/ImageStrings
jdk/internal/jimage/ImageStringsReader
jdk/internal/jimage/NativeImageBuffer
jdk/internal/jimage/NativeImageBuffer$1
jdk/internal/jimage/decompressor/Decompressor
jdk/internal/loader/AbstractClassLoaderValue
jdk/internal/loader/AbstractClassLoaderValue$Memoizer
jdk/internal/loader/ArchivedClassLoaders
jdk/internal/loader/BootLoader
jdk/internal/loader/BuiltinClassLoader
jdk/internal/loader/BuiltinClassLoader$1
jdk/internal/loader/BuiltinClassLoader$2
jdk/internal/loader/BuiltinClassLoader$5
jdk/internal/loader/BuiltinClassLoader$LoadedModule
jdk/internal/loader/ClassLoaderHelper
jdk/internal/loader/ClassLoaderValue
jdk/internal/loader/ClassLoaders
jdk/internal/loader/ClassLoaders$AppClassLoader
jdk/internal/loader/ClassLoaders$BootClassLoader
jdk/internal/loader/ClassLoaders$PlatformClassLoader
jdk/internal/loader/FileURLMapper
jdk/internal/loader/NativeLibraries
jdk/internal/loader/NativeLibraries$1
jdk/internal/loader/NativeLibraries$2
jdk/internal/loader/NativeLibraries$3
jdk/internal/loader/NativeLibraries$CountedLock
jdk/internal/loader/NativeLibraries$LibraryPaths
jdk/internal/loader/NativeLibraries$NativeLibraryContext
jdk/internal/loader/NativeLibraries$NativeLibraryContext$1
jdk/internal/loader/NativeLibraries$NativeLibraryImpl
jdk/internal/loader/NativeLibrary
jdk/internal/loader/Resource
jdk/internal/loader/URLClassPath
jdk/internal/loader/URLClassPath$1
jdk/internal/loader/URLClassPath$3
jdk/internal/loader/URLClassPath$JarLoader
jdk/internal/loader/URLClassPath$JarLoader$1
jdk/internal/loader/URLClassPath$JarLoader$2
jdk/internal/loader/URLClassPath$Loader
jdk/internal/logger/BootstrapLogger
jdk/internal/logger/BootstrapLogger$BootstrapExecutors
jdk/internal/logger/BootstrapLogger$DetectBackend
jdk/internal/logger/BootstrapLogger$DetectBackend$1
jdk/internal/logger/BootstrapLogger$LoggingBackend
jdk/internal/logger/BootstrapLogger$RedirectedLoggers
jdk/internal/logger/DefaultLoggerFinder
jdk/internal/logger/DefaultLoggerFinder$1
jdk/internal/math/DoubleToDecimal
jdk/internal/math/FloatToDecimal
jdk/internal/math/MathUtils
jdk/internal/misc/Blocker
jdk/internal/misc/CDS
jdk/internal/misc/CarrierThread
jdk/internal/misc/CarrierThreadLocal
jdk/internal/misc/InnocuousThread
jdk/internal/misc/InternalLock
jdk/internal/misc/MainMethodFinder
jdk/internal/misc/OSEnvironment
jdk/internal/misc/PreviewFeatures
jdk/internal/misc/ScopedMemoryAccess
jdk/internal/misc/Signal
jdk/internal/misc/Signal$Handler
jdk/internal/misc/Signal$NativeHandler
jdk/internal/misc/TerminatingThreadLocal
jdk/internal/misc/TerminatingThreadLocal$1
jdk/internal/misc/Unsafe
jdk/internal/misc/UnsafeConstants
jdk/internal/misc/VM
jdk/internal/misc/VM$BufferPool
jdk/internal/module/ArchivedBootLayer
jdk/internal/module/ArchivedModuleGraph
jdk/internal/module/Builder
jdk/internal/module/Checks
jdk/internal/module/DefaultRoots
jdk/internal/module/ModuleBootstrap
jdk/internal/module/ModuleBootstrap$Counters
jdk/internal/module/ModuleBootstrap$SafeModuleFinder
jdk/internal/module/ModuleHashes
jdk/internal/module/ModuleHashes$HashSupplier
jdk/internal/module/ModuleInfo$Attributes
jdk/internal/module/ModuleLoaderMap
jdk/internal/module/ModuleLoaderMap$Mapper
jdk/internal/module/ModuleLoaderMap$Modules
jdk/internal/module/ModulePatcher
jdk/internal/module/ModulePath
jdk/internal/module/ModulePath$Patterns
jdk/internal/module/ModuleReferenceImpl
jdk/internal/module/ModuleReferences
jdk/internal/module/ModuleResolution
jdk/internal/module/ModuleTarget
jdk/internal/module/Modules
jdk/internal/module/Resources
jdk/internal/module/ServicesCatalog
jdk/internal/module/ServicesCatalog$ServiceProvider
jdk/internal/module/SystemModuleFinders
jdk/internal/module/SystemModuleFinders$2
jdk/internal/module/SystemModuleFinders$SystemImage
jdk/internal/module/SystemModuleFinders$SystemModuleFinder
jdk/internal/module/SystemModuleFinders$SystemModuleReader
jdk/internal/module/SystemModules
jdk/internal/module/SystemModules$all
jdk/internal/module/SystemModulesMap
jdk/internal/org/objectweb/asm/AnnotationVisitor
jdk/internal/org/objectweb/asm/AnnotationWriter
jdk/internal/org/objectweb/asm/Attribute
jdk/internal/org/objectweb/asm/ByteVector
jdk/internal/org/objectweb/asm/ClassVisitor
jdk/internal/org/objectweb/asm/ClassWriter
jdk/internal/org/objectweb/asm/ConstantDynamic
jdk/internal/org/objectweb/asm/FieldVisitor
jdk/internal/org/objectweb/asm/FieldWriter
jdk/internal/org/objectweb/asm/Frame
jdk/internal/org/objectweb/asm/Handle
jdk/internal/org/objectweb/asm/Handler
jdk/internal/org/objectweb/asm/Label
jdk/internal/org/objectweb/asm/MethodVisitor
jdk/internal/org/objectweb/asm/MethodWriter
jdk/internal/org/objectweb/asm/Symbol
jdk/internal/org/objectweb/asm/SymbolTable
jdk/internal/org/objectweb/asm/SymbolTable$Entry
jdk/internal/org/objectweb/asm/Type
jdk/internal/perf/Perf
jdk/internal/perf/Perf$GetPerfAction
jdk/internal/perf/PerfCounter
jdk/internal/perf/PerfCounter$CoreCounters
jdk/internal/ref/Cleaner
jdk/internal/ref/CleanerFactory
jdk/internal/ref/CleanerFactory$1
jdk/internal/ref/CleanerImpl
jdk/internal/ref/CleanerImpl$CleanerCleanable
jdk/internal/ref/CleanerImpl$PhantomCleanableRef
jdk/internal/ref/PhantomCleanable
jdk/internal/reflect/CallerSensitive
jdk/internal/reflect/ConstantPool
jdk/internal/reflect/ConstructorAccessor
jdk/internal/reflect/ConstructorAccessorImpl
jdk/internal/reflect/DelegatingClassLoader
jdk/internal/reflect/DirectConstructorHandleAccessor
jdk/internal/reflect/DirectConstructorHandleAccessor$NativeAccessor
jdk/internal/reflect/DirectMethodHandleAccessor
jdk/internal/reflect/FieldAccessor
jdk/internal/reflect/FieldAccessorImpl
jdk/internal/reflect/MagicAccessorImpl
jdk/internal/reflect/MethodAccessor
jdk/internal/reflect/MethodAccessorImpl
jdk/internal/reflect/MethodHandleAccessorFactory
jdk/internal/reflect/MethodHandleAccessorFactory$LazyStaticHolder
jdk/internal/reflect/MethodHandleFieldAccessorImpl
jdk/internal/reflect/MethodHandleObjectFieldAccessorImpl
jdk/internal/reflect/NativeConstructorAccessorImpl
jdk/internal/reflect/Reflection
jdk/internal/reflect/ReflectionFactory
jdk/internal/reflect/ReflectionFactory$Config
jdk/internal/reflect/ReflectionFactory$GetReflectionFactoryAction
jdk/internal/reflect/UnsafeFieldAccessorImpl
jdk/internal/reflect/UnsafeStaticFieldAccessorImpl
jdk/internal/util/ArraysSupport
jdk/internal/util/ByteArray
jdk/internal/util/ClassFileDumper
jdk/internal/util/Preconditions
jdk/internal/util/Preconditions$1
jdk/internal/util/Preconditions$2
jdk/internal/util/Preconditions$3
jdk/internal/util/Preconditions$4
jdk/internal/util/StaticProperty
jdk/internal/util/SystemProps
jdk/internal/util/SystemProps$Raw
jdk/internal/util/random/RandomSupport
jdk/internal/vm/Continuation
jdk/internal/vm/ContinuationScope
jdk/internal/vm/FillerObject
jdk/internal/vm/SharedThreadContainer
jdk/internal/vm/StackChunk
jdk/internal/vm/StackableScope
jdk/internal/vm/ThreadContainer
jdk/internal/vm/ThreadContainers
jdk/internal/vm/ThreadContainers$RootContainer
jdk/internal/vm/ThreadContainers$RootContainer$TrackingRootContainer
jdk/internal/vm/vector/VectorSupport
jdk/internal/vm/vector/VectorSupport$Vector
jdk/internal/vm/vector/VectorSupport$VectorMask
jdk/internal/vm/vector/VectorSupport$VectorPayload
jdk/internal/vm/vector/VectorSupport$VectorShuffle
sun/invoke/empty/Empty
sun/invoke/util/BytecodeDescriptor
sun/invoke/util/ValueConversions
sun/invoke/util/ValueConversions$WrapperCache
sun/invoke/util/VerifyAccess
sun/invoke/util/VerifyType
sun/invoke/util/Wrapper
sun/invoke/util/Wrapper$Format
sun/io/Win32ErrorMode
sun/launcher/LauncherHelper
sun/net/util/IPAddressUtil
sun/net/util/IPAddressUtil$MASKS
sun/net/util/URLUtil
sun/net/www/ParseUtil
sun/net/www/protocol/file/Handler
sun/net/www/protocol/jar/Handler
sun/nio/ByteBuffered
sun/nio/ch/DirectBuffer
sun/nio/cs/ArrayDecoder
sun/nio/cs/ArrayEncoder
sun/nio/cs/HistoricallyNamedCharset
sun/nio/cs/ISO_8859_1
sun/nio/cs/MS1252
sun/nio/cs/MS1252$Holder
sun/nio/cs/SingleByte
sun/nio/cs/SingleByte$Decoder
sun/nio/cs/SingleByte$Encoder
sun/nio/cs/StandardCharsets
sun/nio/cs/StandardCharsets$Aliases
sun/nio/cs/StandardCharsets$Cache
sun/nio/cs/StandardCharsets$Classes
sun/nio/cs/StreamEncoder
sun/nio/cs/US_ASCII
sun/nio/cs/UTF_16
sun/nio/cs/UTF_16BE
sun/nio/cs/UTF_16LE
sun/nio/cs/UTF_8
sun/nio/cs/Unicode
sun/nio/fs/AbstractBasicFileAttributeView
sun/nio/fs/AbstractFileSystemProvider
sun/nio/fs/DefaultFileSystemProvider
sun/nio/fs/DynamicFileAttributeView
sun/nio/fs/NativeBuffer
sun/nio/fs/NativeBuffer$Deallocator
sun/nio/fs/NativeBuffers
sun/nio/fs/NativeBuffers$1
sun/nio/fs/Util
sun/nio/fs/WindowsFileAttributeViews
sun/nio/fs/WindowsFileAttributeViews$Basic
sun/nio/fs/WindowsFileAttributes
sun/nio/fs/WindowsFileSystem
sun/nio/fs/WindowsFileSystemProvider
sun/nio/fs/WindowsNativeDispatcher
sun/nio/fs/WindowsNativeDispatcher$Account
sun/nio/fs/WindowsNativeDispatcher$AclInformation
sun/nio/fs/WindowsNativeDispatcher$CompletionStatus
sun/nio/fs/WindowsNativeDispatcher$DiskFreeSpace
sun/nio/fs/WindowsNativeDispatcher$FirstFile
sun/nio/fs/WindowsNativeDispatcher$FirstStream
sun/nio/fs/WindowsNativeDispatcher$VolumeInformation
sun/nio/fs/WindowsPath
sun/nio/fs/WindowsPathParser
sun/nio/fs/WindowsPathParser$Result
sun/nio/fs/WindowsPathType
sun/nio/fs/WindowsUriSupport
sun/reflect/annotation/AnnotationParser
sun/security/action/GetBooleanAction
sun/security/action/GetIntegerAction
sun/security/action/GetPropertyAction
sun/security/util/Debug
sun/security/util/FilePermCompat
sun/security/util/LazyCodeSourcePermissionCollection
sun/security/util/SecurityProperties
sun/security/util/SignatureFileVerifier
sun/text/resources/cldr/FormatData
sun/util/PreHashedMap
sun/util/calendar/AbstractCalendar
sun/util/calendar/BaseCalendar
sun/util/calendar/BaseCalendar$Date
sun/util/calendar/CalendarDate
sun/util/calendar/CalendarSystem
sun/util/calendar/CalendarSystem$GregorianHolder
sun/util/calendar/CalendarUtils
sun/util/calendar/Gregorian
sun/util/calendar/Gregorian$Date
sun/util/calendar/ZoneInfo
sun/util/calendar/ZoneInfoFile
sun/util/calendar/ZoneInfoFile$1
sun/util/calendar/ZoneInfoFile$ZoneOffsetTransitionRule
sun/util/cldr/CLDRBaseLocaleDataMetaInfo
sun/util/cldr/CLDRCalendarDataProviderImpl
sun/util/cldr/CLDRLocaleProviderAdapter
sun/util/locale/BaseLocale
sun/util/locale/BaseLocale$Cache
sun/util/locale/BaseLocale$Key
sun/util/locale/InternalLocaleBuilder
sun/util/locale/InternalLocaleBuilder$CaseInsensitiveChar
sun/util/locale/LanguageTag
sun/util/locale/LocaleObjectCache
sun/util/locale/LocaleObjectCache$CacheEntry
sun/util/locale/LocaleUtils
sun/util/locale/ParseStatus
sun/util/locale/StringTokenIterator
sun/util/locale/provider/AvailableLanguageTags
sun/util/locale/provider/CalendarDataProviderImpl
sun/util/locale/provider/CalendarDataUtility
sun/util/locale/provider/CalendarDataUtility$CalendarWeekParameterGetter
sun/util/locale/provider/CalendarProviderImpl
sun/util/locale/provider/DateFormatProviderImpl
sun/util/locale/provider/DateFormatSymbolsProviderImpl
sun/util/locale/provider/DecimalFormatSymbolsProviderImpl
sun/util/locale/provider/JRELocaleProviderAdapter
sun/util/locale/provider/LocaleDataMetaInfo
sun/util/locale/provider/LocaleProviderAdapter
sun/util/locale/provider/LocaleProviderAdapter$Type
sun/util/locale/provider/LocaleResources
sun/util/locale/provider/LocaleResources$ResourceReference
sun/util/locale/provider/LocaleServiceProviderPool
sun/util/locale/provider/LocaleServiceProviderPool$LocalizedObjectGetter
sun/util/locale/provider/NumberFormatProviderImpl
sun/util/locale/provider/ResourceBundleBasedAdapter
sun/util/logging/PlatformLogger$Bridge
sun/util/logging/PlatformLogger$ConfigurableBridge
sun/util/logging/internal/LoggingProviderImpl
sun/util/logging/internal/LoggingProviderImpl$LogManagerAccess
sun/util/resources/Bundles
sun/util/resources/Bundles$1
sun/util/resources/Bundles$BundleReference
sun/util/resources/Bundles$CacheKey
sun/util/resources/Bundles$CacheKeyReference
sun/util/resources/Bundles$Strategy
sun/util/resources/LocaleData
sun/util/resources/LocaleData$1
sun/util/resources/LocaleData$LocaleDataStrategy
sun/util/resources/cldr/CalendarData
sun/util/spi/CalendarProvider
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DelegatingMethodHandle$Holder delegate L6_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DelegatingMethodHandle$Holder reinvoke_L L3_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DelegatingMethodHandle$Holder reinvoke_L L3_V
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DelegatingMethodHandle$Holder reinvoke_L LJI_J
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DelegatingMethodHandle$Holder reinvoke_L LJJ_J
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DelegatingMethodHandle$Holder reinvoke_L LJL3_J
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DelegatingMethodHandle$Holder reinvoke_L LJLIL_J
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DelegatingMethodHandle$Holder reinvoke_L LJLJL_J
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DelegatingMethodHandle$Holder reinvoke_L LJL_J
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DelegatingMethodHandle$Holder reinvoke_L LLJ_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DelegatingMethodHandle$Holder reinvoke_L LL_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DelegatingMethodHandle$Holder reinvoke_L L_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DelegatingMethodHandle$Holder reinvoke_L L_V
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder getDouble LL_D
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder getInt LL_I
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder getLong LL_J
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder getReference LL_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeInterface L3_I
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeSpecial L10_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeSpecial L11_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeSpecial L12_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeSpecial L3D_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeSpecial L3F_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeSpecial L3I_J
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeSpecial L3I_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeSpecial L3J_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeSpecial L3_J
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeSpecial L3_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeSpecial L4_J
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeSpecial L4_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeSpecial L4_V
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeSpecial L5_J
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeSpecial L5_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeSpecial L6_J
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeSpecial L6_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeSpecial L7_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeSpecial L8_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeSpecial L9_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeSpecial LLDL_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeSpecial LLD_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeSpecial LLFL_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeSpecial LLF_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeSpecial LLII_J
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeSpecial LLII_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeSpecial LLIL_J
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeSpecial LLIL_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeSpecial LLI_J
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeSpecial LLI_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeSpecial LLJI_J
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeSpecial LLJJ_J
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeSpecial LLJJ_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeSpecial LLJL3_J
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeSpecial LLJL4_J
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeSpecial LLJL5_J
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeSpecial LLJLII_J
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeSpecial LLJLIL_J
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeSpecial LLJLI_J
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeSpecial LLJLJJ_J
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeSpecial LLJLJL_J
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeSpecial LLJLJ_J
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeSpecial LLJLLI_J
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeSpecial LLJLL_J
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeSpecial LLJL_J
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeSpecial LLJ_J
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeSpecial LLJ_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeSpecial LL_I
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeSpecial LL_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeSpecial LL_V
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeStatic L10_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeStatic L11_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeStatic L12_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeStatic L3DL_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeStatic L3D_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeStatic L3IL_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeStatic L3I_I
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeStatic L3I_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeStatic L3_I
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeStatic L3_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeStatic L4J_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeStatic L4_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeStatic L5J_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeStatic L5_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeStatic L5_V
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeStatic L6J_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeStatic L6_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeStatic L7_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeStatic L8_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeStatic L9_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeStatic LD_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeStatic LF_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeStatic LI3_I
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeStatic LII_I
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeStatic LI_I
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeStatic LI_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeStatic LJI_J
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeStatic LJJ_J
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeStatic LJL3_J
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeStatic LJLIL_J
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeStatic LJLJL_J
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeStatic LJL_J
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeStatic LJ_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeStatic LLI_I
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeStatic LLJ_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeStatic LL_I
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeStatic LL_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeStatic L_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeStatic L_V
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeStaticInit LL_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeStaticInit L_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeVirtual L3_V
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder invokeVirtual LL_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder newInvokeSpecial L3_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder newInvokeSpecial L4_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder newInvokeSpecial LII_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder newInvokeSpecial LI_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder newInvokeSpecial LL_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.DirectMethodHandle$Holder newInvokeSpecial L_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.Invokers$Holder invokeExact_MT L3_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.Invokers$Holder invokeExact_MT L4_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.Invokers$Holder invokeExact_MT L7_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.Invokers$Holder invokeExact_MT L8_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.Invokers$Holder invokeExact_MT LL_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.Invokers$Holder invoke_MT LL_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.Invokers$Holder linkToTargetMethod DLL_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.Invokers$Holder linkToTargetMethod DL_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.Invokers$Holder linkToTargetMethod FLL_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.Invokers$Holder linkToTargetMethod FL_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.Invokers$Holder linkToTargetMethod IIL_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.Invokers$Holder linkToTargetMethod ILL_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.Invokers$Holder linkToTargetMethod IL_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.Invokers$Holder linkToTargetMethod JJL_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.Invokers$Holder linkToTargetMethod JL_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.Invokers$Holder linkToTargetMethod L10_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.Invokers$Holder linkToTargetMethod L11_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.Invokers$Holder linkToTargetMethod L3_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.Invokers$Holder linkToTargetMethod L4_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.Invokers$Holder linkToTargetMethod L5_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.Invokers$Holder linkToTargetMethod L6_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.Invokers$Holder linkToTargetMethod L7_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.Invokers$Holder linkToTargetMethod L8_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.Invokers$Holder linkToTargetMethod L9_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.Invokers$Holder linkToTargetMethod LDL_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.Invokers$Holder linkToTargetMethod LFL_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.Invokers$Holder linkToTargetMethod LIL_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.Invokers$Holder linkToTargetMethod LL_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.Invokers$Holder linkToTargetMethod L_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.LambdaForm$Holder identity_D LD_D
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.LambdaForm$Holder identity_I LI_I
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.LambdaForm$Holder identity_J LJ_J
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.LambdaForm$Holder identity_L LL_L
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.LambdaForm$Holder zero_D L_D
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.LambdaForm$Holder zero_I L_I
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.LambdaForm$Holder zero_J L_J
@lambda-form-invoker [LF_RESOLVE] java.lang.invoke.LambdaForm$Holder zero_L L_L
@lambda-form-invoker [SPECIES_RESOLVE] java.lang.invoke.BoundMethodHandle$Species_D
@lambda-form-invoker [SPECIES_RESOLVE] java.lang.invoke.BoundMethodHandle$Species_DL
@lambda-form-invoker [SPECIES_RESOLVE] java.lang.invoke.BoundMethodHandle$Species_I
@lambda-form-invoker [SPECIES_RESOLVE] java.lang.invoke.BoundMethodHandle$Species_IL
@lambda-form-invoker [SPECIES_RESOLVE] java.lang.invoke.BoundMethodHandle$Species_L
@lambda-form-invoker [SPECIES_RESOLVE] java.lang.invoke.BoundMethodHandle$Species_LJ
@lambda-form-invoker [SPECIES_RESOLVE] java.lang.invoke.BoundMethodHandle$Species_LL
@lambda-form-invoker [SPECIES_RESOLVE] java.lang.invoke.BoundMethodHandle$Species_LLJ
@lambda-form-invoker [SPECIES_RESOLVE] java.lang.invoke.BoundMethodHandle$Species_LLL
@lambda-form-invoker [SPECIES_RESOLVE] java.lang.invoke.BoundMethodHandle$Species_LLLJ
@lambda-form-invoker [SPECIES_RESOLVE] java.lang.invoke.BoundMethodHandle$Species_LLLL
@lambda-form-invoker [SPECIES_RESOLVE] java.lang.invoke.BoundMethodHandle$Species_LLLLL
@lambda-form-invoker [SPECIES_RESOLVE] java.lang.invoke.BoundMethodHandle$Species_LLLLLL
@lambda-form-invoker [SPECIES_RESOLVE] java.lang.invoke.BoundMethodHandle$Species_LLLLLLL
@lambda-form-invoker [SPECIES_RESOLVE] java.lang.invoke.BoundMethodHandle$Species_LLLLLLLL
@lambda-form-invoker [SPECIES_RESOLVE] java.lang.invoke.BoundMethodHandle$Species_LLLLLLLLL
@lambda-form-invoker [SPECIES_RESOLVE] java.lang.invoke.SimpleMethodHandle
@lambda-proxy java/lang/module/ModuleDescriptor$Builder accept ()Ljava/util/function/Consumer; (Ljava/lang/Object;)V REF_invokeStatic jdk/internal/module/Checks requirePackageName (Ljava/lang/String;)Ljava/lang/String; (Ljava/lang/String;)V
@lambda-proxy java/lang/module/ModuleFinder$2 accept (Ljava/lang/module/ModuleFinder$2;Ljava/lang/String;)Ljava/util/function/Consumer; (Ljava/lang/Object;)V REF_invokeVirtual java/lang/module/ModuleFinder$2 lambda$find$1 (Ljava/lang/String;Ljava/lang/module/ModuleReference;)V (Ljava/lang/module/ModuleReference;)V
@lambda-proxy java/lang/module/ModuleFinder$2 accept (Ljava/lang/module/ModuleFinder$2;Ljava/util/Set;)Ljava/util/function/Consumer; (Ljava/lang/Object;)V REF_invokeVirtual java/lang/module/ModuleFinder$2 lambda$findAll$3 (Ljava/util/Set;Ljava/lang/module/ModuleReference;)V (Ljava/lang/module/ModuleReference;)V
@lambda-proxy java/lang/module/ModuleFinder$2 apply ()Ljava/util/function/Function; (Ljava/lang/Object;)Ljava/lang/Object; REF_invokeStatic java/lang/module/ModuleFinder$2 lambda$findAll$2 (Ljava/lang/module/ModuleFinder;)Ljava/util/stream/Stream; (Ljava/lang/module/ModuleFinder;)Ljava/util/stream/Stream;
@lambda-proxy java/lang/module/ModuleFinder$2 apply ()Ljava/util/function/Function; (Ljava/lang/Object;)Ljava/lang/Object; REF_invokeVirtual java/util/Optional stream ()Ljava/util/stream/Stream; (Ljava/util/Optional;)Ljava/util/stream/Stream;
@lambda-proxy java/lang/module/ModuleFinder$2 apply (Ljava/lang/String;)Ljava/util/function/Function; (Ljava/lang/Object;)Ljava/lang/Object; REF_invokeStatic java/lang/module/ModuleFinder$2 lambda$find$0 (Ljava/lang/String;Ljava/lang/module/ModuleFinder;)Ljava/util/Optional; (Ljava/lang/module/ModuleFinder;)Ljava/util/Optional;
@lambda-proxy java/security/Security run ()Ljava/security/PrivilegedAction; ()Ljava/lang/Object; REF_invokeStatic java/security/Security lambda$static$0 ()Ljava/lang/Object; ()Ljava/lang/Object;
@lambda-proxy java/text/DecimalFormatSymbols test ()Ljava/util/function/IntPredicate; (I)Z REF_invokeStatic java/text/DecimalFormatSymbols lambda$findNonFormatChar$0 (I)Z (I)Z
@lambda-proxy java/time/ZoneOffset apply ()Ljava/util/function/Function; (Ljava/lang/Object;)Ljava/lang/Object; REF_invokeStatic java/time/ZoneOffset lambda$ofTotalSeconds$0 (Ljava/lang/Integer;)Ljava/time/ZoneOffset; (Ljava/lang/Integer;)Ljava/time/ZoneOffset;
@lambda-proxy java/time/format/DateTimeFormatter queryFrom ()Ljava/time/temporal/TemporalQuery; (Ljava/time/temporal/TemporalAccessor;)Ljava/lang/Object; REF_invokeStatic java/time/format/DateTimeFormatter lambda$static$0 (Ljava/time/temporal/TemporalAccessor;)Ljava/time/Period; (Ljava/time/temporal/TemporalAccessor;)Ljava/time/Period;
@lambda-proxy java/time/format/DateTimeFormatter queryFrom ()Ljava/time/temporal/TemporalQuery; (Ljava/time/temporal/TemporalAccessor;)Ljava/lang/Object; REF_invokeStatic java/time/format/DateTimeFormatter lambda$static$1 (Ljava/time/temporal/TemporalAccessor;)Ljava/lang/Boolean; (Ljava/time/temporal/TemporalAccessor;)Ljava/lang/Boolean;
@lambda-proxy java/time/format/DateTimeFormatterBuilder queryFrom ()Ljava/time/temporal/TemporalQuery; (Ljava/time/temporal/TemporalAccessor;)Ljava/lang/Object; REF_invokeStatic java/time/format/DateTimeFormatterBuilder lambda$static$0 (Ljava/time/temporal/TemporalAccessor;)Ljava/time/ZoneId; (Ljava/time/temporal/TemporalAccessor;)Ljava/time/ZoneId;
@lambda-proxy java/util/ResourceBundle$ResourceBundleProviderHelper run (Ljava/lang/reflect/Constructor;)Ljava/security/PrivilegedAction; ()Ljava/lang/Object; REF_invokeStatic java/util/ResourceBundle$ResourceBundleProviderHelper lambda$newResourceBundle$0 (Ljava/lang/reflect/Constructor;)Ljava/lang/Void; ()Ljava/lang/Void;
@lambda-proxy java/util/logging/Level apply ()Ljava/util/function/Function; (Ljava/lang/Object;)Ljava/lang/Object; REF_invokeVirtual java/util/logging/Level$KnownLevel mirrored ()Ljava/util/Optional; (Ljava/util/logging/Level$KnownLevel;)Ljava/util/Optional;
@lambda-proxy java/util/logging/Level$KnownLevel apply ()Ljava/util/function/Function; (Ljava/lang/Object;)Ljava/lang/Object; REF_invokeStatic java/util/logging/Level$KnownLevel lambda$add$3 (Ljava/lang/String;)Ljava/util/List; (Ljava/lang/String;)Ljava/util/List;
@lambda-proxy java/util/logging/Level$KnownLevel apply ()Ljava/util/function/Function; (Ljava/lang/Object;)Ljava/lang/Object; REF_invokeStatic java/util/logging/Level$KnownLevel lambda$add$4 (Ljava/lang/Integer;)Ljava/util/List; (Ljava/lang/Integer;)Ljava/util/List;
@lambda-proxy java/util/logging/Level$KnownLevel apply ()Ljava/util/function/Function; (Ljava/lang/Object;)Ljava/lang/Object; REF_invokeVirtual java/util/Optional stream ()Ljava/util/stream/Stream; (Ljava/util/Optional;)Ljava/util/stream/Stream;
@lambda-proxy java/util/regex/CharPredicates is ()Ljava/util/regex/Pattern$BmpCharPredicate; (I)Z REF_invokeStatic java/util/regex/CharPredicates lambda$ASCII_DIGIT$18 (I)Z (I)Z
@lambda-proxy java/util/regex/CharPredicates is ()Ljava/util/regex/Pattern$BmpCharPredicate; (I)Z REF_invokeStatic java/util/regex/CharPredicates lambda$ASCII_SPACE$20 (I)Z (I)Z
@lambda-proxy java/util/regex/Pattern is (I)Ljava/util/regex/Pattern$BmpCharPredicate; (I)Z REF_invokeStatic java/util/regex/Pattern lambda$Single$14 (II)Z (I)Z
@lambda-proxy java/util/regex/Pattern is (II)Ljava/util/regex/Pattern$BmpCharPredicate; (I)Z REF_invokeStatic java/util/regex/Pattern lambda$Range$17 (III)Z (I)Z
@lambda-proxy java/util/regex/Pattern is (Ljava/util/regex/Pattern$CharPredicate;)Ljava/util/regex/Pattern$CharPredicate; (I)Z REF_invokeStatic java/util/regex/Pattern lambda$negate$7 (Ljava/util/regex/Pattern$CharPredicate;I)Z (I)Z
@lambda-proxy java/util/regex/Pattern is (Ljava/util/regex/Pattern$CharPredicate;Ljava/util/regex/Pattern$CharPredicate;)Ljava/util/regex/Pattern$BmpCharPredicate; (I)Z REF_invokeStatic java/util/regex/Pattern lambda$union$3 (Ljava/util/regex/Pattern$CharPredicate;Ljava/util/regex/Pattern$CharPredicate;I)Z (I)Z
@lambda-proxy java/util/stream/Collectors accept ()Ljava/util/function/BiConsumer; (Ljava/lang/Object;Ljava/lang/Object;)V REF_invokeInterface java/util/Set add (Ljava/lang/Object;)Z (Ljava/util/HashSet;Ljava/lang/Object;)V
@lambda-proxy java/util/stream/Collectors accept ()Ljava/util/function/BiConsumer; (Ljava/lang/Object;Ljava/lang/Object;)V REF_invokeVirtual java/util/StringJoiner add (Ljava/lang/CharSequence;)Ljava/util/StringJoiner; (Ljava/util/StringJoiner;Ljava/lang/CharSequence;)V
@lambda-proxy java/util/stream/Collectors accept (Ljava/util/function/BiConsumer;Ljava/util/function/Predicate;)Ljava/util/function/BiConsumer; (Ljava/lang/Object;Ljava/lang/Object;)V REF_invokeStatic java/util/stream/Collectors lambda$partitioningBy$62 (Ljava/util/function/BiConsumer;Ljava/util/function/Predicate;Ljava/util/stream/Collectors$Partition;Ljava/lang/Object;)V (Ljava/util/stream/Collectors$Partition;Ljava/lang/Object;)V
@lambda-proxy java/util/stream/Collectors apply ()Ljava/util/function/BinaryOperator; (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object; REF_invokeStatic java/util/stream/Collectors lambda$toSet$7 (Ljava/util/HashSet;Ljava/util/HashSet;)Ljava/util/HashSet; (Ljava/util/HashSet;Ljava/util/HashSet;)Ljava/util/HashSet;
@lambda-proxy java/util/stream/Collectors apply ()Ljava/util/function/BinaryOperator; (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object; REF_invokeVirtual java/util/StringJoiner merge (Ljava/util/StringJoiner;)Ljava/util/StringJoiner; (Ljava/util/StringJoiner;Ljava/util/StringJoiner;)Ljava/util/StringJoiner;
@lambda-proxy java/util/stream/Collectors apply ()Ljava/util/function/Function; (Ljava/lang/Object;)Ljava/lang/Object; REF_invokeStatic java/util/stream/Collectors lambda$castingIdentity$2 (Ljava/lang/Object;)Ljava/lang/Object; (Ljava/lang/Object;)Ljava/lang/Object;
@lambda-proxy java/util/stream/Collectors apply ()Ljava/util/function/Function; (Ljava/lang/Object;)Ljava/lang/Object; REF_invokeVirtual java/util/StringJoiner toString ()Ljava/lang/String; (Ljava/util/StringJoiner;)Ljava/lang/String;
@lambda-proxy java/util/stream/Collectors apply (Ljava/util/function/BinaryOperator;)Ljava/util/function/BinaryOperator; (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object; REF_invokeStatic java/util/stream/Collectors lambda$partitioningBy$63 (Ljava/util/function/BinaryOperator;Ljava/util/stream/Collectors$Partition;Ljava/util/stream/Collectors$Partition;)Ljava/util/stream/Collectors$Partition; (Ljava/util/stream/Collectors$Partition;Ljava/util/stream/Collectors$Partition;)Ljava/util/stream/Collectors$Partition;
@lambda-proxy java/util/stream/Collectors get ()Ljava/util/function/Supplier; ()Ljava/lang/Object; REF_newInvokeSpecial java/util/HashSet <init> ()V ()Ljava/util/HashSet;
@lambda-proxy java/util/stream/Collectors get (Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/util/function/Supplier; ()Ljava/lang/Object; REF_invokeStatic java/util/stream/Collectors lambda$joining$11 (Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/util/StringJoiner; ()Ljava/util/StringJoiner;
@lambda-proxy java/util/stream/Collectors get (Ljava/util/stream/Collector;)Ljava/util/function/Supplier; ()Ljava/lang/Object; REF_invokeStatic java/util/stream/Collectors lambda$partitioningBy$64 (Ljava/util/stream/Collector;)Ljava/util/stream/Collectors$Partition; ()Ljava/util/stream/Collectors$Partition;
@lambda-proxy java/util/stream/FindOps$FindSink$OfInt get ()Ljava/util/function/Supplier; ()Ljava/lang/Object; REF_newInvokeSpecial java/util/stream/FindOps$FindSink$OfInt <init> ()V ()Ljava/util/stream/TerminalSink;
@lambda-proxy java/util/stream/FindOps$FindSink$OfInt get ()Ljava/util/function/Supplier; ()Ljava/lang/Object; REF_newInvokeSpecial java/util/stream/FindOps$FindSink$OfInt <init> ()V ()Ljava/util/stream/TerminalSink;
@lambda-proxy java/util/stream/FindOps$FindSink$OfInt test ()Ljava/util/function/Predicate; (Ljava/lang/Object;)Z REF_invokeVirtual java/util/OptionalInt isPresent ()Z (Ljava/util/OptionalInt;)Z
@lambda-proxy java/util/stream/FindOps$FindSink$OfInt test ()Ljava/util/function/Predicate; (Ljava/lang/Object;)Z REF_invokeVirtual java/util/OptionalInt isPresent ()Z (Ljava/util/OptionalInt;)Z
@lambda-proxy java/util/stream/FindOps$FindSink$OfRef get ()Ljava/util/function/Supplier; ()Ljava/lang/Object; REF_newInvokeSpecial java/util/stream/FindOps$FindSink$OfRef <init> ()V ()Ljava/util/stream/TerminalSink;
@lambda-proxy java/util/stream/FindOps$FindSink$OfRef get ()Ljava/util/function/Supplier; ()Ljava/lang/Object; REF_newInvokeSpecial java/util/stream/FindOps$FindSink$OfRef <init> ()V ()Ljava/util/stream/TerminalSink;
@lambda-proxy java/util/stream/FindOps$FindSink$OfRef test ()Ljava/util/function/Predicate; (Ljava/lang/Object;)Z REF_invokeVirtual java/util/Optional isPresent ()Z (Ljava/util/Optional;)Z
@lambda-proxy java/util/stream/FindOps$FindSink$OfRef test ()Ljava/util/function/Predicate; (Ljava/lang/Object;)Z REF_invokeVirtual java/util/Optional isPresent ()Z (Ljava/util/Optional;)Z
@lambda-proxy java/util/zip/ZipFile apply (Ljava/util/zip/ZipFile;)Ljava/util/function/IntFunction; (I)Ljava/lang/Object; REF_invokeVirtual java/util/zip/ZipFile lambda$jarStream$1 (I)Ljava/util/jar/JarEntry; (I)Ljava/util/jar/JarEntry;
@lambda-proxy jdk/internal/module/DefaultRoots apply ()Ljava/util/function/Function; (Ljava/lang/Object;)Ljava/lang/Object; REF_invokeVirtual java/lang/module/ModuleDescriptor name ()Ljava/lang/String; (Ljava/lang/module/ModuleDescriptor;)Ljava/lang/String;
@lambda-proxy jdk/internal/module/DefaultRoots apply ()Ljava/util/function/Function; (Ljava/lang/Object;)Ljava/lang/Object; REF_invokeVirtual java/lang/module/ModuleReference descriptor ()Ljava/lang/module/ModuleDescriptor; (Ljava/lang/module/ModuleReference;)Ljava/lang/module/ModuleDescriptor;
@lambda-proxy jdk/internal/module/DefaultRoots test ()Ljava/util/function/Predicate; (Ljava/lang/Object;)Z REF_invokeStatic jdk/internal/module/DefaultRoots lambda$compute$0 (Ljava/lang/module/ModuleReference;)Z (Ljava/lang/module/ModuleReference;)Z
@lambda-proxy jdk/internal/module/DefaultRoots test ()Ljava/util/function/Predicate; (Ljava/lang/Object;)Z REF_invokeStatic jdk/internal/module/DefaultRoots lambda$exportsAPI$2 (Ljava/lang/module/ModuleDescriptor$Exports;)Z (Ljava/lang/module/ModuleDescriptor$Exports;)Z
@lambda-proxy jdk/internal/module/DefaultRoots test (Ljava/lang/module/ModuleFinder;)Ljava/util/function/Predicate; (Ljava/lang/Object;)Z REF_invokeStatic jdk/internal/module/DefaultRoots lambda$compute$1 (Ljava/lang/module/ModuleFinder;Ljava/lang/module/ModuleDescriptor;)Z (Ljava/lang/module/ModuleDescriptor;)Z
@lambda-proxy jdk/internal/module/ModulePath apply ()Ljava/util/function/Function; (Ljava/lang/Object;)Ljava/lang/Object; REF_invokeVirtual java/util/Optional stream ()Ljava/util/stream/Stream; (Ljava/util/Optional;)Ljava/util/stream/Stream;
@lambda-proxy jdk/internal/module/ModulePath apply ()Ljava/util/function/Function; (Ljava/lang/Object;)Ljava/lang/Object; REF_invokeVirtual java/util/Optional stream ()Ljava/util/stream/Stream; (Ljava/util/Optional;)Ljava/util/stream/Stream;
@lambda-proxy jdk/internal/module/ModulePath apply ()Ljava/util/function/Function; (Ljava/lang/Object;)Ljava/lang/Object; REF_invokeVirtual java/util/zip/ZipEntry getName ()Ljava/lang/String; (Ljava/util/jar/JarEntry;)Ljava/lang/String;
@lambda-proxy jdk/internal/module/ModulePath apply (Ljdk/internal/module/ModulePath;)Ljava/util/function/Function; (Ljava/lang/Object;)Ljava/lang/Object; REF_invokeVirtual jdk/internal/module/ModulePath toPackageName (Ljava/lang/String;)Ljava/util/Optional; (Ljava/lang/String;)Ljava/util/Optional;
@lambda-proxy jdk/internal/module/ModulePath apply (Ljdk/internal/module/ModulePath;)Ljava/util/function/Function; (Ljava/lang/Object;)Ljava/lang/Object; REF_invokeVirtual jdk/internal/module/ModulePath toServiceName (Ljava/lang/String;)Ljava/util/Optional; (Ljava/lang/String;)Ljava/util/Optional;
@lambda-proxy jdk/internal/module/ModulePath test ()Ljava/util/function/Predicate; (Ljava/lang/Object;)Z REF_invokeStatic jdk/internal/module/ModulePath lambda$deriveModuleDescriptor$2 (Ljava/util/jar/JarEntry;)Z (Ljava/util/jar/JarEntry;)Z
@lambda-proxy jdk/internal/module/ModulePath test ()Ljava/util/function/Predicate; (Ljava/lang/Object;)Z REF_invokeStatic jdk/internal/module/ModulePath lambda$deriveModuleDescriptor$3 (Ljava/lang/String;)Z (Ljava/lang/String;)Z
@lambda-proxy jdk/internal/module/ModulePath test ()Ljava/util/function/Predicate; (Ljava/lang/Object;)Z REF_invokeStatic jdk/internal/module/ModulePath lambda$deriveModuleDescriptor$4 (Ljava/lang/String;)Z (Ljava/lang/String;)Z
@lambda-proxy jdk/internal/module/ModuleReferences generate (Ljava/util/function/Supplier;)Ljdk/internal/module/ModuleHashes$HashSupplier; (Ljava/lang/String;)[B REF_invokeStatic jdk/internal/module/ModuleReferences lambda$newJarModule$1 (Ljava/util/function/Supplier;Ljava/lang/String;)[B (Ljava/lang/String;)[B
@lambda-proxy jdk/internal/module/ModuleReferences get (Ljava/nio/file/Path;Ljava/net/URI;)Ljava/util/function/Supplier; ()Ljava/lang/Object; REF_invokeStatic jdk/internal/module/ModuleReferences lambda$newJarModule$0 (Ljava/nio/file/Path;Ljava/net/URI;)Ljava/lang/module/ModuleReader; ()Ljava/lang/module/ModuleReader;
@lambda-proxy sun/util/cldr/CLDRLocaleProviderAdapter apply ()Ljava/util/function/Function; (Ljava/lang/Object;)Ljava/lang/Object; REF_invokeStatic sun/util/cldr/CLDRLocaleProviderAdapter lambda$applyAliases$5 (Ljava/util/Locale;)Ljava/util/Locale; (Ljava/util/Locale;)Ljava/util/Locale;
@lambda-proxy sun/util/cldr/CLDRLocaleProviderAdapter run ()Ljava/security/PrivilegedExceptionAction; ()Ljava/lang/Object; REF_invokeStatic sun/util/cldr/CLDRLocaleProviderAdapter lambda$new$0 ()Lsun/util/locale/provider/LocaleDataMetaInfo; ()Lsun/util/locale/provider/LocaleDataMetaInfo;
@lambda-proxy sun/util/cldr/CLDRLocaleProviderAdapter run (Lsun/util/cldr/CLDRLocaleProviderAdapter;)Ljava/security/PrivilegedAction; ()Ljava/lang/Object; REF_invokeVirtual sun/util/cldr/CLDRLocaleProviderAdapter lambda$getCalendarDataProvider$1 ()Ljava/util/spi/CalendarDataProvider; ()Ljava/util/spi/CalendarDataProvider;
@lambda-proxy sun/util/locale/provider/JRELocaleProviderAdapter run (Lsun/util/locale/provider/JRELocaleProviderAdapter;)Ljava/security/PrivilegedAction; ()Ljava/lang/Object; REF_invokeVirtual sun/util/locale/provider/JRELocaleProviderAdapter lambda$getCalendarProvider$11 ()Lsun/util/spi/CalendarProvider; ()Lsun/util/spi/CalendarProvider;
@lambda-proxy sun/util/locale/provider/JRELocaleProviderAdapter run (Lsun/util/locale/provider/JRELocaleProviderAdapter;)Ljava/security/PrivilegedAction; ()Ljava/lang/Object; REF_invokeVirtual sun/util/locale/provider/JRELocaleProviderAdapter lambda$getDateFormatProvider$2 ()Ljava/text/spi/DateFormatProvider; ()Ljava/text/spi/DateFormatProvider;
@lambda-proxy sun/util/locale/provider/JRELocaleProviderAdapter run (Lsun/util/locale/provider/JRELocaleProviderAdapter;)Ljava/security/PrivilegedAction; ()Ljava/lang/Object; REF_invokeVirtual sun/util/locale/provider/JRELocaleProviderAdapter lambda$getDateFormatSymbolsProvider$3 ()Ljava/text/spi/DateFormatSymbolsProvider; ()Ljava/text/spi/DateFormatSymbolsProvider;
@lambda-proxy sun/util/locale/provider/JRELocaleProviderAdapter run (Lsun/util/locale/provider/JRELocaleProviderAdapter;)Ljava/security/PrivilegedAction; ()Ljava/lang/Object; REF_invokeVirtual sun/util/locale/provider/JRELocaleProviderAdapter lambda$getDecimalFormatSymbolsProvider$4 ()Ljava/text/spi/DecimalFormatSymbolsProvider; ()Ljava/text/spi/DecimalFormatSymbolsProvider;
@lambda-proxy sun/util/locale/provider/JRELocaleProviderAdapter run (Lsun/util/locale/provider/JRELocaleProviderAdapter;)Ljava/security/PrivilegedAction; ()Ljava/lang/Object; REF_invokeVirtual sun/util/locale/provider/JRELocaleProviderAdapter lambda$getNumberFormatProvider$5 ()Ljava/text/spi/NumberFormatProvider; ()Ljava/text/spi/NumberFormatProvider;
@lambda-proxy sun/util/locale/provider/LocaleProviderAdapter apply ()Ljava/util/function/Function; (Ljava/lang/Object;)Ljava/lang/Object; REF_invokeStatic sun/util/locale/provider/LocaleProviderAdapter lambda$toLocaleArray$0 (Ljava/lang/String;)Ljava/util/Locale; (Ljava/lang/String;)Ljava/util/Locale;
@lambda-proxy sun/util/locale/provider/LocaleProviderAdapter apply ()Ljava/util/function/IntFunction; (I)Ljava/lang/Object; REF_invokeStatic sun/util/locale/provider/LocaleProviderAdapter lambda$toLocaleArray$1 (I)[Ljava/util/Locale; (I)[Ljava/util/Locale;
