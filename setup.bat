@echo off
echo ================================
echo EG应用绕过工具 - 环境设置
echo ================================
echo.

echo [1] 检查ADB...
where adb >nul 2>&1
if %errorlevel% neq 0 (
    echo ADB未找到，正在下载...
    echo 请稍等...
    
    :: 创建临时目录
    if not exist "tools" mkdir tools
    cd tools
    
    :: 下载ADB工具
    echo 正在下载Android Platform Tools...
    powershell -Command "Invoke-WebRequest -Uri 'https://dl.google.com/android/repository/platform-tools-latest-windows.zip' -OutFile 'platform-tools.zip'"
    
    :: 解压
    powershell -Command "Expand-Archive -Path 'platform-tools.zip' -DestinationPath '.'"
    
    :: 添加到PATH
    set PATH=%PATH%;%cd%\platform-tools
    
    cd ..
    echo ADB安装完成
) else (
    echo ADB已安装
)

echo.
echo [2] 检查Python...
where python >nul 2>&1
if %errorlevel% neq 0 (
    echo Python未找到，请手动安装Python 3.7+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
) else (
    echo Python已安装
)

echo.
echo [3] 安装Frida...
pip install frida-tools
if %errorlevel% neq 0 (
    echo Frida安装失败，请检查网络连接
    pause
    exit /b 1
) else (
    echo Frida安装成功
)

echo.
echo [4] 检查设备连接...
adb devices
echo.
echo 请确保您的小米手机：
echo 1. 已开启开发者选项
echo 2. 已开启USB调试
echo 3. 已连接到电脑并授权
echo.
pause

echo.
echo [5] 下载Frida Server...
echo 请手动完成以下步骤：
echo 1. 访问: https://github.com/frida/frida/releases
echo 2. 下载适合您手机的frida-server (通常是arm64版本)
echo 3. 将文件重命名为 frida-server
echo 4. 放在当前目录下
echo.
echo 下载完成后按任意键继续...
pause

if exist "frida-server" (
    echo 找到frida-server，正在部署...
    adb push frida-server /data/local/tmp/frida-server
    adb shell chmod 755 /data/local/tmp/frida-server
    echo frida-server部署完成
) else (
    echo 未找到frida-server文件
    echo 请按照说明下载后重新运行此脚本
    pause
    exit /b 1
)

echo.
echo ================================
echo 环境设置完成！
echo ================================
echo.
echo 现在可以运行绕过脚本了
echo 运行命令: python frida_runner.py
echo.
pause
