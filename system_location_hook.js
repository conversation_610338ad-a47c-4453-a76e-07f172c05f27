console.log("[*] 🌍 系统级位置模拟器启动中...");
console.log("[*] 📍 目标位置: JWK京崎大厦 (30.1865, 120.2103)");

// JWK京崎大厦的坐标
var TARGET_LATITUDE = 30.1865;   // 纬度
var TARGET_LONGITUDE = 120.2103; // 经度
var TARGET_ALTITUDE = 10.0;      // 海拔
var TARGET_ACCURACY = 5.0;       // 精度

// 持续运行标志
var isHookActive = true;

Java.perform(function() {
    console.log("[*] ✅ Java环境已准备就绪");
    
    // Hook 1: Android原生LocationManager - 系统级Hook
    try {
        var LocationManager = Java.use("android.location.LocationManager");
        
        // Hook getLastKnownLocation - 返回JWK京崎大厦位置
        LocationManager.getLastKnownLocation.implementation = function(provider) {
            console.log("[+] 🎯 Hook getLastKnownLocation, provider: " + provider);
            
            var Location = Java.use("android.location.Location");
            var fakeLocation = Location.$new(provider);
            
            fakeLocation.setLatitude(TARGET_LATITUDE);
            fakeLocation.setLongitude(TARGET_LONGITUDE);
            fakeLocation.setAltitude(TARGET_ALTITUDE);
            fakeLocation.setAccuracy(TARGET_ACCURACY);
            fakeLocation.setTime(Java.use("java.lang.System").currentTimeMillis());
            
            console.log("[+] ✅ 返回JWK京崎大厦位置: " + TARGET_LATITUDE + ", " + TARGET_LONGITUDE);
            return fakeLocation;
        };
        
        // Hook requestLocationUpdates - 持续发送JWK京崎大厦位置
        LocationManager.requestLocationUpdates.overload('java.lang.String', 'long', 'float', 'android.location.LocationListener').implementation = function(provider, minTime, minDistance, listener) {
            console.log("[+] 🎯 Hook requestLocationUpdates, provider: " + provider);
            
            // 调用原方法
            this.requestLocationUpdates(provider, minTime, minDistance, listener);
            
            // 启动定时器，持续发送虚假位置
            var intervalId = setInterval(function() {
                if (!isHookActive) {
                    clearInterval(intervalId);
                    return;
                }
                
                Java.perform(function() {
                    try {
                        var Location = Java.use("android.location.Location");
                        var fakeLocation = Location.$new(provider);
                        
                        fakeLocation.setLatitude(TARGET_LATITUDE);
                        fakeLocation.setLongitude(TARGET_LONGITUDE);
                        fakeLocation.setAltitude(TARGET_ALTITUDE);
                        fakeLocation.setAccuracy(TARGET_ACCURACY);
                        fakeLocation.setTime(Java.use("java.lang.System").currentTimeMillis());
                        
                        listener.onLocationChanged(fakeLocation);
                        console.log("[+] ✅ 持续发送JWK京崎大厦位置更新");
                    } catch (e) {
                        console.log("[-] 发送位置更新失败: " + e);
                    }
                });
            }, 2000); // 每2秒发送一次
        };
        
        console.log("[+] ✅ 成功Hook LocationManager");
    } catch (e) {
        console.log("[-] ❌ Hook LocationManager失败: " + e);
    }
    
    // Hook 2: Location对象 - 拦截所有位置获取
    try {
        var Location = Java.use("android.location.Location");
        
        Location.getLatitude.implementation = function() {
            var originalLat = this.getLatitude();
            console.log("[+] 🎯 Hook getLatitude, 原始: " + originalLat + " -> JWK: " + TARGET_LATITUDE);
            return TARGET_LATITUDE;
        };
        
        Location.getLongitude.implementation = function() {
            var originalLng = this.getLongitude();
            console.log("[+] 🎯 Hook getLongitude, 原始: " + originalLng + " -> JWK: " + TARGET_LONGITUDE);
            return TARGET_LONGITUDE;
        };
        
        Location.getAltitude.implementation = function() {
            return TARGET_ALTITUDE;
        };
        
        Location.getAccuracy.implementation = function() {
            return TARGET_ACCURACY;
        };
        
        console.log("[+] ✅ 成功Hook Location对象");
    } catch (e) {
        console.log("[-] ❌ Hook Location对象失败: " + e);
    }
    
    // Hook 3: 百度定位SDK
    setTimeout(function() {
        Java.perform(function() {
            try {
                var BaiduLocation = Java.use("com.baidu.location.BDLocation");
                
                BaiduLocation.getLatitude.implementation = function() {
                    console.log("[+] 🎯 Hook 百度定位 getLatitude -> " + TARGET_LATITUDE);
                    return TARGET_LATITUDE;
                };
                
                BaiduLocation.getLongitude.implementation = function() {
                    console.log("[+] 🎯 Hook 百度定位 getLongitude -> " + TARGET_LONGITUDE);
                    return TARGET_LONGITUDE;
                };
                
                console.log("[+] ✅ 成功Hook百度定位SDK");
            } catch (e) {
                console.log("[-] 百度定位SDK未找到: " + e);
            }
        });
    }, 3000);
    
    // Hook 4: 高德定位SDK
    setTimeout(function() {
        Java.perform(function() {
            try {
                var AMapLocation = Java.use("com.amap.api.location.AMapLocation");
                
                AMapLocation.getLatitude.implementation = function() {
                    console.log("[+] 🎯 Hook 高德定位 getLatitude -> " + TARGET_LATITUDE);
                    return TARGET_LATITUDE;
                };
                
                AMapLocation.getLongitude.implementation = function() {
                    console.log("[+] 🎯 Hook 高德定位 getLongitude -> " + TARGET_LONGITUDE);
                    return TARGET_LONGITUDE;
                };
                
                console.log("[+] ✅ 成功Hook高德定位SDK");
            } catch (e) {
                console.log("[-] 高德定位SDK未找到: " + e);
            }
        });
    }, 5000);
    
    // Hook 5: 腾讯定位SDK
    setTimeout(function() {
        Java.perform(function() {
            try {
                var TencentLocation = Java.use("com.tencent.map.geolocation.TencentLocation");
                
                TencentLocation.getLatitude.implementation = function() {
                    console.log("[+] 🎯 Hook 腾讯定位 getLatitude -> " + TARGET_LATITUDE);
                    return TARGET_LATITUDE;
                };
                
                TencentLocation.getLongitude.implementation = function() {
                    console.log("[+] 🎯 Hook 腾讯定位 getLongitude -> " + TARGET_LONGITUDE);
                    return TARGET_LONGITUDE;
                };
                
                console.log("[+] ✅ 成功Hook腾讯定位SDK");
            } catch (e) {
                console.log("[-] 腾讯定位SDK未找到: " + e);
            }
        });
    }, 7000);
    
    // 持续监控和保持Hook活跃
    setInterval(function() {
        if (isHookActive) {
            console.log("[*] 🔄 位置模拟器运行中... JWK京崎大厦 (" + TARGET_LATITUDE + ", " + TARGET_LONGITUDE + ")");
        }
    }, 30000); // 每30秒输出一次状态
    
    console.log("[*] 🎉 系统级位置Hook设置完成！");
    console.log("[*] 📍 所有应用现在都将显示位置为: JWK京崎大厦");
    console.log("[*] 📍 地址: 浙江省杭州市滨江区长河街道JWK京崎大厦");
    console.log("[*] 📍 坐标: " + TARGET_LATITUDE + "°N, " + TARGET_LONGITUDE + "°E");
    console.log("[*] 🔄 Hook将持续运行，即使目标应用闪退也会保持位置模拟");
});

// 全局错误处理
Java.perform(function() {
    var System = Java.use("java.lang.System");
    console.log("[*] 📱 设备时间: " + new Date(System.currentTimeMillis()));
    console.log("[*] 🚀 位置模拟器已激活，开始拦截所有定位请求...");
});
