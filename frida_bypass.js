// Frida脚本 - 绕过EG应用付费验证
// 使用方法: frida -U -f com.alibaba.android.rimet.beesforce -l frida_bypass.js

console.log("[*] 开始Hook EG应用...");

Java.perform(function() {
    console.log("[*] Java环境已准备就绪");
    
    // Hook 1: 绕过授权验证方法
    try {
        var UtilClass = Java.use("com.tianyu.util.a");
        
        // Hook方法a() - 可能是验证方法
        UtilClass.a.overload().implementation = function() {
            console.log("[+] Hook到验证方法 com.tianyu.util.a.a()");
            console.log("[+] 原始返回值被修改为true");
            return true; // 强制返回true，绕过验证
        };
        
        // Hook方法a(Context) - 可能是包名验证
        UtilClass.a.overload('android.content.Context').implementation = function(context) {
            console.log("[+] Hook到包名验证方法 com.tianyu.util.a.a(Context)");
            console.log("[+] 原始返回值被修改为true");
            return true; // 强制返回true，绕过包名验证
        };
        
        console.log("[+] 成功Hook com.tianyu.util.a 类");
    } catch (e) {
        console.log("[-] Hook com.tianyu.util.a 失败: " + e);
    }
    
    // Hook 2: 绕过StubApp中的验证
    try {
        var StubApp = Java.use("com.stub.StubApp");
        
        // Hook native方法，可能用于验证
        if (StubApp.n0111) {
            StubApp.n0111.implementation = function() {
                console.log("[+] Hook到native验证方法 n0111");
                return true;
            };
        }
        
        console.log("[+] 成功Hook StubApp类");
    } catch (e) {
        console.log("[-] Hook StubApp 失败: " + e);
    }
    
    // Hook 3: 通用字符串解密Hook
    try {
        var UtilClass = Java.use("com.tianyu.util.a");
        
        // Hook字符串解密方法
        UtilClass.a.overload('java.lang.String').implementation = function(str) {
            var result = this.a(str);
            console.log("[+] 解密字符串: " + str + " -> " + result);
            
            // 如果解密结果包含验证相关的类名，可以进一步分析
            if (result.includes("Activity") || result.includes("Dialog") || result.includes("Check")) {
                console.log("[!] 发现可能的验证相关类: " + result);
            }
            
            return result;
        };
        
        console.log("[+] 成功Hook字符串解密方法");
    } catch (e) {
        console.log("[-] Hook字符串解密失败: " + e);
    }
    
    // Hook 4: 查找并Hook所有可能的验证Activity
    try {
        // 枚举所有已加载的类
        Java.enumerateLoadedClasses({
            onMatch: function(className) {
                // 查找可能的登录/验证Activity
                if (className.includes("Login") || 
                    className.includes("Auth") || 
                    className.includes("Verify") || 
                    className.includes("Check") ||
                    className.includes("License")) {
                    
                    console.log("[!] 发现可能的验证类: " + className);
                    
                    try {
                        var clazz = Java.use(className);
                        
                        // 尝试Hook onCreate方法
                        if (clazz.onCreate) {
                            clazz.onCreate.implementation = function(savedInstanceState) {
                                console.log("[+] Hook到验证Activity onCreate: " + className);
                                
                                // 调用原方法
                                this.onCreate(savedInstanceState);
                                
                                // 尝试自动跳过验证
                                var Intent = Java.use("android.content.Intent");
                                var intent = Intent.$new();
                                
                                // 可以在这里添加跳转到主界面的逻辑
                                console.log("[+] 尝试跳过验证界面");
                            };
                        }
                        
                    } catch (e) {
                        console.log("[-] Hook验证类失败: " + e);
                    }
                }
            },
            onComplete: function() {
                console.log("[*] 类枚举完成");
            }
        });
        
    } catch (e) {
        console.log("[-] 枚举类失败: " + e);
    }
    
    // Hook 5: Hook SharedPreferences，可能存储验证状态
    try {
        var SharedPreferences = Java.use("android.content.SharedPreferences");
        var Editor = Java.use("android.content.SharedPreferences$Editor");
        
        // Hook getBoolean方法，强制返回已验证状态
        SharedPreferences.getBoolean.overload('java.lang.String', 'boolean').implementation = function(key, defValue) {
            var result = this.getBoolean(key, defValue);
            
            // 如果key包含验证相关关键词，强制返回true
            if (key.includes("auth") || key.includes("verify") || key.includes("license") || 
                key.includes("valid") || key.includes("check") || key.includes("login")) {
                console.log("[+] Hook SharedPreferences.getBoolean: " + key + " -> true (原值: " + result + ")");
                return true;
            }
            
            return result;
        };
        
        console.log("[+] 成功Hook SharedPreferences");
    } catch (e) {
        console.log("[-] Hook SharedPreferences失败: " + e);
    }
    
    // Hook 6: Hook网络请求，可能用于在线验证
    try {
        var URL = Java.use("java.net.URL");
        var HttpURLConnection = Java.use("java.net.HttpURLConnection");
        
        // Hook URL构造函数
        URL.$init.overload('java.lang.String').implementation = function(url) {
            console.log("[+] 网络请求URL: " + url);
            
            // 如果是验证相关的URL，可以考虑阻止或修改
            if (url.includes("auth") || url.includes("verify") || url.includes("license")) {
                console.log("[!] 发现验证相关网络请求: " + url);
            }
            
            return this.$init(url);
        };
        
        console.log("[+] 成功Hook网络请求");
    } catch (e) {
        console.log("[-] Hook网络请求失败: " + e);
    }
    
    console.log("[*] 所有Hook设置完成，开始监控应用行为...");
});

// 监控应用启动
Java.perform(function() {
    var ActivityThread = Java.use("android.app.ActivityThread");
    
    ActivityThread.currentApplication.implementation = function() {
        var app = this.currentApplication();
        if (app != null) {
            console.log("[+] 应用已启动: " + app.getPackageName());
        }
        return app;
    };
});
