@echo off
echo ================================
echo Python 自动安装脚本
echo ================================
echo.

echo [1/4] 下载Python 3.11...
echo 正在下载Python安装包，请稍等...

powershell -Command "Invoke-WebRequest -Uri 'https://www.python.org/ftp/python/3.11.7/python-3.11.7-amd64.exe' -OutFile 'python-installer.exe'"

if %errorlevel% neq 0 (
    echo 下载失败，请检查网络连接
    echo 您也可以手动下载：https://www.python.org/downloads/
    pause
    exit /b 1
)

echo Python下载完成！

echo.
echo [2/4] 安装Python...
echo 正在静默安装Python（包含pip和添加到PATH）...

python-installer.exe /quiet InstallAllUsers=1 PrependPath=1 Include_test=0

echo 等待安装完成...
timeout /t 30 /nobreak

echo.
echo [3/4] 验证安装...
python --version
if %errorlevel% neq 0 (
    echo Python安装可能失败，尝试刷新环境变量...
    refreshenv
    python --version
)

echo.
echo [4/4] 安装Frida...
python -m pip install --upgrade pip
python -m pip install frida-tools

echo.
echo ================================
echo Python和Frida安装完成！
echo ================================
echo.
echo 清理安装文件...
del python-installer.exe

echo 现在可以运行绕过脚本了！
pause
