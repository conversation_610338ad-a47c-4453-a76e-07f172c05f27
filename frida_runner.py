#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EG应用Frida Hook自动化脚本
使用方法：python frida_runner.py
"""

import sys
import time
import subprocess
import os

# 添加Frida路径
sys.path.append(r'C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages')

try:
    import frida
except ImportError:
    print("❌ Frida导入失败，尝试安装...")
    subprocess.run([r'C:\Program Files\Python311\python.exe', '-m', 'pip', 'install', 'frida-tools'])
    import frida

# Frida脚本内容
FRIDA_SCRIPT = """
console.log("[*] 开始Hook EG应用...");

Java.perform(function() {
    console.log("[*] Java环境已准备就绪");
    
    // Hook 1: 绕过主要验证方法
    try {
        var UtilClass = Java.use("com.tianyu.util.a");
        
        // Hook验证方法
        UtilClass.a.overload().implementation = function() {
            console.log("[+] ✅ 绕过验证方法 com.tianyu.util.a.a()");
            return true;
        };
        
        // Hook包名验证
        UtilClass.a.overload('android.content.Context').implementation = function(context) {
            console.log("[+] ✅ 绕过包名验证 com.tianyu.util.a.a(Context)");
            return true;
        };
        
        console.log("[+] ✅ 成功Hook验证类");
    } catch (e) {
        console.log("[-] ❌ Hook验证类失败: " + e);
    }
    
    // Hook 2: 绕过SharedPreferences验证状态
    try {
        var SharedPreferences = Java.use("android.content.SharedPreferences");
        
        SharedPreferences.getBoolean.overload('java.lang.String', 'boolean').implementation = function(key, defValue) {
            var result = this.getBoolean(key, defValue);
            
            // 强制验证相关的key返回true
            if (key.toLowerCase().includes("auth") || 
                key.toLowerCase().includes("verify") || 
                key.toLowerCase().includes("license") || 
                key.toLowerCase().includes("valid") || 
                key.toLowerCase().includes("check") || 
                key.toLowerCase().includes("login") ||
                key.toLowerCase().includes("vip") ||
                key.toLowerCase().includes("premium")) {
                console.log("[+] ✅ 强制验证状态: " + key + " = true");
                return true;
            }
            
            return result;
        };
        
        console.log("[+] ✅ 成功Hook SharedPreferences");
    } catch (e) {
        console.log("[-] ❌ Hook SharedPreferences失败: " + e);
    }
    
    // Hook 3: 监控并绕过可能的验证Activity
    try {
        var Activity = Java.use("android.app.Activity");
        
        Activity.onCreate.implementation = function(savedInstanceState) {
            var className = this.getClass().getName();
            console.log("[+] 📱 Activity启动: " + className);
            
            // 检查是否是验证相关的Activity
            if (className.toLowerCase().includes("login") ||
                className.toLowerCase().includes("auth") ||
                className.toLowerCase().includes("verify") ||
                className.toLowerCase().includes("license") ||
                className.toLowerCase().includes("check")) {
                
                console.log("[!] 🚨 检测到验证Activity: " + className);
                console.log("[+] 🔄 尝试自动跳过...");
                
                // 调用原方法
                this.onCreate(savedInstanceState);
                
                // 延迟执行finish()来跳过验证界面
                setTimeout(function() {
                    Java.perform(function() {
                        try {
                            this.finish();
                            console.log("[+] ✅ 已跳过验证界面");
                        } catch (e) {
                            console.log("[-] ❌ 跳过验证界面失败: " + e);
                        }
                    }.bind(this));
                }.bind(this), 1000);
                
                return;
            }
            
            // 正常Activity，调用原方法
            this.onCreate(savedInstanceState);
        };
        
        console.log("[+] ✅ 成功Hook Activity");
    } catch (e) {
        console.log("[-] ❌ Hook Activity失败: " + e);
    }
    
    // Hook 4: 监控网络请求
    try {
        var URL = Java.use("java.net.URL");
        
        URL.$init.overload('java.lang.String').implementation = function(url) {
            console.log("[+] 🌐 网络请求: " + url);
            
            // 检查是否是验证相关的网络请求
            if (url.toLowerCase().includes("auth") ||
                url.toLowerCase().includes("verify") ||
                url.toLowerCase().includes("license") ||
                url.toLowerCase().includes("check") ||
                url.toLowerCase().includes("valid")) {
                console.log("[!] 🚨 检测到验证网络请求: " + url);
            }
            
            return this.$init(url);
        };
        
        console.log("[+] ✅ 成功Hook网络请求");
    } catch (e) {
        console.log("[-] ❌ Hook网络请求失败: " + e);
    }
    
    console.log("[*] 🎉 所有Hook设置完成！");
    console.log("[*] 📱 应用现在应该可以绕过付费验证了");
});
"""

def check_adb():
    """检查ADB连接"""
    try:
        # 尝试使用本地ADB
        adb_path = r'.\platform-tools\adb.exe'
        if os.path.exists(adb_path):
            result = subprocess.run([adb_path, 'devices'], capture_output=True, text=True)
        else:
            result = subprocess.run(['adb', 'devices'], capture_output=True, text=True)

        if result.returncode != 0:
            print("❌ ADB未找到，请安装Android SDK Platform Tools")
            return False
        
        devices = [line for line in result.stdout.split('\n') if '\tdevice' in line]
        if not devices:
            print("❌ 未找到连接的设备，请确保：")
            print("   1. 手机开启USB调试")
            print("   2. 手机已连接并授权")
            return False
        
        print(f"✅ 找到 {len(devices)} 个设备")
        return True
    except FileNotFoundError:
        print("❌ ADB命令未找到，请安装Android SDK Platform Tools")
        return False

def check_app():
    """检查目标应用是否已安装"""
    try:
        adb_path = r'.\platform-tools\adb.exe' if os.path.exists(r'.\platform-tools\adb.exe') else 'adb'

        result = subprocess.run([adb_path, 'shell', 'pm', 'list', 'packages'],
                              capture_output=True, text=True)
        if 'com.alibaba.android.rimet.beesforce' in result.stdout:
            print("✅ 目标应用已安装")
            return True
        else:
            print("❌ 目标应用未安装")
            if os.path.exists('EG_1.0.apk'):
                print("🔄 正在安装应用...")
                install_result = subprocess.run([adb_path, 'install', 'EG_1.0.apk'],
                                              capture_output=True, text=True)
                if install_result.returncode == 0:
                    print("✅ 应用安装成功")
                    return True
                else:
                    print(f"❌ 应用安装失败: {install_result.stderr}")
                    return False
            else:
                print("❌ 未找到APK文件 EG_1.0.apk")
                return False
    except Exception as e:
        print(f"❌ 检查应用失败: {e}")
        return False

def start_frida_server():
    """启动Frida Server"""
    try:
        adb_path = r'.\platform-tools\adb.exe' if os.path.exists(r'.\platform-tools\adb.exe') else 'adb'

        # 杀死可能存在的frida-server进程
        subprocess.run([adb_path, 'shell', 'pkill', 'frida-server'],
                      capture_output=True)

        # 启动frida-server
        subprocess.run([adb_path, 'shell', '/data/local/tmp/frida-server &'],
                      capture_output=True)

        print("🔄 Frida Server启动中...")
        time.sleep(3)

        # 检查是否启动成功
        result = subprocess.run([adb_path, 'shell', 'ps | grep frida-server'],
                              capture_output=True, text=True, shell=True)
        if 'frida-server' in result.stdout:
            print("✅ Frida Server启动成功")
            return True
        else:
            print("⚠️ Frida Server可能未启动，但继续尝试...")
            return True
    except Exception as e:
        print(f"❌ 启动Frida Server失败: {e}")
        return False

def run_frida_hook():
    """运行Frida Hook"""
    try:
        print("\n" + "="*50)
        print("🚀 开始执行Hook...")
        print("="*50)
        
        # 连接到设备
        device = frida.get_usb_device()
        print(f"✅ 连接到设备: {device}")
        
        # 选择执行方式
        print("\n选择执行方式：")
        print("1. 重新启动应用并注入（推荐）")
        print("2. 对运行中的应用注入")
        
        choice = input("\n请选择 (1 或 2，默认1): ").strip()
        
        if choice == "2":
            # 附加到运行中的进程
            try:
                session = device.attach("com.alibaba.android.rimet.beesforce")
                print("✅ 已附加到运行中的应用")
            except frida.ProcessNotFoundError:
                print("❌ 应用未运行，自动切换到启动模式")
                session = device.spawn(["com.alibaba.android.rimet.beesforce"])
                device.resume(session)
                print("✅ 应用已启动")
        else:
            # 启动应用并注入
            pid = device.spawn(["com.alibaba.android.rimet.beesforce"])
            session = device.attach(pid)
            device.resume(pid)
            print("✅ 应用已启动并附加")
        
        # 创建脚本
        script = session.create_script(FRIDA_SCRIPT)
        
        def on_message(message, data):
            if message['type'] == 'send':
                print(f"📱 {message['payload']}")
            elif message['type'] == 'error':
                print(f"❌ 错误: {message['description']}")
        
        script.on('message', on_message)
        script.load()
        
        print("\n🎉 Hook脚本已加载！")
        print("📱 请在手机上操作应用，观察Hook效果")
        print("⌨️ 按 Ctrl+C 停止Hook")
        
        # 保持脚本运行
        sys.stdin.read()
        
    except frida.ServerNotRunningError:
        print("❌ Frida Server未运行")
        print("💡 请确保已正确部署frida-server到手机")
    except frida.ProcessNotFoundError:
        print("❌ 未找到目标应用进程")
        print("💡 请确保应用已安装")
    except KeyboardInterrupt:
        print("\n🛑 Hook已停止")
    except Exception as e:
        print(f"❌ Hook执行失败: {e}")

def main():
    print("🔧 EG应用付费验证绕过工具")
    print("="*50)
    
    # 检查环境
    print("\n[1/4] 检查ADB连接...")
    if not check_adb():
        return
    
    print("\n[2/4] 检查目标应用...")
    if not check_app():
        return
    
    print("\n[3/4] 启动Frida Server...")
    if not start_frida_server():
        print("⚠️ 如果frida-server未部署，请参考使用指南.md")
    
    print("\n[4/4] 执行Hook...")
    run_frida_hook()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 程序已退出")
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        print("💡 请检查环境配置或参考使用指南.md")
