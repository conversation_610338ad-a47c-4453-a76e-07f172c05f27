.class public Lcom/tianyu/util/LocationSpoof;
.super Ljava/lang/Object;
.source "LocationSpoof.java"


# static fields
.field private static final TARGET_LATITUDE:D = 30.1865

.field private static final TARGET_LONGITUDE:D = 120.2103

.field private static final TARGET_ALTITUDE:D = 10.0

.field private static final TARGET_ACCURACY:F = 5.0f


# direct methods
.method public constructor <init>()V
    .locals 0

    .prologue
    .line 8
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static createFakeLocation(Ljava/lang/String;)Landroid/location/Location;
    .locals 4
    .param p0, "provider"    # Ljava/lang/String;

    .prologue
    .line 18
    new-instance v0, Landroid/location/Location;

    invoke-direct {v0, p0}, Landroid/location/Location;-><init>(Ljava/lang/String;)V

    .line 20
    .local v0, "location":Landroid/location/Location;
    const-wide v2, 0x403e2f9db22d0e56L    # 30.1865

    invoke-virtual {v0, v2, v3}, Landroid/location/Location;->setLatitude(D)V

    .line 21
    const-wide v2, 0x405e0d70a3d70a3dL    # 120.2103

    invoke-virtual {v0, v2, v3}, Landroid/location/Location;->setLongitude(D)V

    .line 22
    const-wide/high16 v2, 0x4024000000000000L    # 10.0

    invoke-virtual {v0, v2, v3}, Landroid/location/Location;->setAltitude(D)V

    .line 23
    const/high16 v1, 0x40a00000    # 5.0f

    invoke-virtual {v0, v1}, Landroid/location/Location;->setAccuracy(F)V

    .line 24
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v2

    invoke-virtual {v0, v2, v3}, Landroid/location/Location;->setTime(J)V

    .line 26
    return-object v0
.end method

.method public static getTargetLatitude()D
    .locals 2

    .prologue
    .line 30
    const-wide v0, 0x403e2f9db22d0e56L    # 30.1865

    return-wide v0
.end method

.method public static getTargetLongitude()D
    .locals 2

    .prologue
    .line 34
    const-wide v0, 0x405e0d70a3d70a3dL    # 120.2103

    return-wide v0
.end method

.method public static getTargetAltitude()D
    .locals 2

    .prologue
    .line 38
    const-wide/high16 v0, 0x4024000000000000L    # 10.0

    return-wide v0
.end method

.method public static getTargetAccuracy()F
    .locals 1

    .prologue
    .line 42
    const/high16 v0, 0x40a00000    # 5.0f

    return v0
.end method
