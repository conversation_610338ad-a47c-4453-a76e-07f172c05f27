<?xml version="1.0" encoding="utf-8" standalone="no"?><manifest xmlns:android="http://schemas.android.com/apk/res/android" android:compileSdkVersion="32" android:compileSdkVersionCodename="12" package="com.alibaba.android.rimet.beesforce" platformBuildVersionCode="32" platformBuildVersionName="12">
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION"/>
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE"/>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE"/>
    <uses-permission android:name="android.permission.READ_PHONE_STATE"/>
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.DOWNLOAD_WITHOUT_NOTIFICATION"/>
    <uses-permission android:name="android.permission.REPLACE_EXISTING_PACKAGE"/>
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES"/>
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS"/>
    <uses-permission android:name="android.permission.READ_LOGS"/>
    <uses-permission android:name="android.permission.VIBRATE"/>
    <uses-permission android:name="android.permission.WAKE_LOCK"/>
    <uses-permission android:name="android.permission.WRITE_SETTINGS"/>
    <uses-permission android:name="android.permission.ACCESS_MOCK_LOCATION"/>
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW"/>
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES"/>
    <uses-permission android:name="android.permission.REQUEST_DELETE_PACKAGES"/>
    <application android:appComponentFactory="qchyyds.OoOoOOOoOO" android:icon="@mipmap/ic_launcher" android:label="@string/app_name" android:launchMode="singleInstance" android:name="com.stub.StubApp" android:requestLegacyExternalStorage="true" android:supportsRtl="true" android:theme="@style/AppTheme" android:usesCleartextTraffic="true">
        <uses-library android:name="org.apache.http.legacy" android:required="false"/>
        <meta-data android:name="com.baidu.lbsapi.API_KEY" android:value="KpbYCL3vtdPqIqFXX1ffHBZwt51FcSID"/>
        <service android:enabled="true" android:exported="false" android:name="com.baidu.location.f" android:process=":remote"/>
        <service android:exported="false" android:name="com.zcshou.service.ServiceGo"/>
        <provider android:authorities="com.alibaba.android.rimet.beesforce.fileProvider" android:exported="false" android:grantUriPermissions="true" android:name="qchyyds.oooOoO">
            <meta-data android:name="android.support.FILE_PROVIDER_PATHS" android:resource="@xml/provider_paths"/>
        </provider>
        <activity android:exported="true" android:name="com.zcshou.gogogo.WelcomeActivity" android:theme="@style/AppTheme">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>
        <activity android:exported="false" android:name="square.boxfk.MainActivity" android:theme="@style/AppTheme.NoActionBar"/>
        <activity android:exported="false" android:label="@string/app_settings" android:name="square.boxfk.SettingsActivity"/>
        <activity android:exported="false" android:label="@string/app_history" android:name="square.boxfk.HistoryActivity"/>
        <activity android:exported="true" android:launchMode="singleTask" android:name="com.zcshou.gogogo.HomeActivity" android:screenOrientation="portrait">
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.BROWSABLE"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <data android:scheme="um.6125a15210c4020b03ec017a"/>
                <data android:host="debugmode" android:scheme="sa5d580a1c"/>
            </intent-filter>
        </activity>
        <activity android:configChanges="keyboardHidden|orientation|screenSize" android:exported="false" android:name="com.zcshou.gogogo.MapActivity" android:screenOrientation="portrait"/>
        <activity android:exported="false" android:name="com.zcshou.gogogo.SelectaddressActivity" android:screenOrientation="portrait"/>
        <activity android:exported="false" android:name="com.zcshou.gogogo.LoginActivity" android:screenOrientation="portrait"/>
        <uses-library android:name="androidx.window.extensions" android:required="false"/>
        <uses-library android:name="androidx.window.sidecar" android:required="false"/>
        <provider android:authorities="com.alibaba.android.rimet.beesforce.androidx-startup" android:exported="false" android:name="androidx.startup.InitializationProvider">
            <meta-data android:name="androidx.emoji2.text.EmojiCompatInitializer" android:value="androidx.startup"/>
            <meta-data android:name="androidx.lifecycle.ProcessLifecycleInitializer" android:value="androidx.startup"/>
        </provider>
        <provider android:authorities="com.alibaba.android.rimet.beesforce.kdkg.dfmfgxep" android:exported="false" android:grantUriPermissions="true" android:name="qchyyds.oooOoO">
            <meta-data android:name="android.support.FILE_PROVIDER_PATHS" android:resource="@xml/epic_paths"/>
        </provider>
        <service android:exported="false" android:name="qchyyds.oooooOooO"/>
        <activity android:exported="false" android:launchMode="standard" android:name="Epic.plugin.core.A$1"/>
        <activity android:exported="false" android:launchMode="standard" android:name="Epic.plugin.core.A$2" android:theme="@android:style/Theme.Translucent"/>
        <activity android:exported="false" android:launchMode="singleTop" android:name="Epic.plugin.core.B$1"/>
        <activity android:exported="false" android:launchMode="singleTop" android:name="Epic.plugin.core.B$2"/>
        <activity android:exported="false" android:launchMode="singleTop" android:name="Epic.plugin.core.B$3"/>
        <activity android:exported="false" android:launchMode="singleTop" android:name="Epic.plugin.core.B$4"/>
        <activity android:exported="false" android:launchMode="singleTop" android:name="Epic.plugin.core.B$5"/>
        <activity android:exported="false" android:launchMode="singleTop" android:name="Epic.plugin.core.B$6"/>
        <activity android:exported="false" android:launchMode="singleTop" android:name="Epic.plugin.core.B$7"/>
        <activity android:exported="false" android:launchMode="singleTop" android:name="Epic.plugin.core.B$8"/>
        <activity android:exported="false" android:launchMode="singleTask" android:name="Epic.plugin.core.C$1"/>
        <activity android:exported="false" android:launchMode="singleTask" android:name="Epic.plugin.core.C$2"/>
        <activity android:exported="false" android:launchMode="singleTask" android:name="Epic.plugin.core.C$3"/>
        <activity android:exported="false" android:launchMode="singleTask" android:name="Epic.plugin.core.C$4"/>
        <activity android:exported="false" android:launchMode="singleTask" android:name="Epic.plugin.core.C$5"/>
        <activity android:exported="false" android:launchMode="singleTask" android:name="Epic.plugin.core.C$6"/>
        <activity android:exported="false" android:launchMode="singleTask" android:name="Epic.plugin.core.C$7"/>
        <activity android:exported="false" android:launchMode="singleTask" android:name="Epic.plugin.core.C$8"/>
        <activity android:exported="false" android:launchMode="singleInstance" android:name="Epic.plugin.core.D$1"/>
        <activity android:exported="false" android:launchMode="singleInstance" android:name="Epic.plugin.core.D$2"/>
        <activity android:exported="false" android:launchMode="singleInstance" android:name="Epic.plugin.core.D$3"/>
        <activity android:exported="false" android:launchMode="singleInstance" android:name="Epic.plugin.core.D$4"/>
        <activity android:exported="false" android:launchMode="singleInstance" android:name="Epic.plugin.core.D$5"/>
        <activity android:exported="false" android:launchMode="singleInstance" android:name="Epic.plugin.core.D$6"/>
        <activity android:exported="false" android:launchMode="singleInstance" android:name="Epic.plugin.core.D$7"/>
        <activity android:exported="false" android:launchMode="singleInstance" android:name="Epic.plugin.core.D$8"/>
        <service android:exported="false" android:name="qchyyds.oOoOOooOo"/>
        <service android:exported="false" android:name="qchyyds.OoOoO" android:process=":daemon">
            <intent-filter>
                <action android:name="com.alibaba.android.rimet.beesforce.intent.ACTION_DAEMON_SERVICE"/>
            </intent-filter>
        </service>
        <provider android:authorities="com.alibaba.android.rimet.beesforce.VirtualAPK.Provider" android:exported="false" android:grantUriPermissions="true" android:name="qchyyds.oOoooooO" android:process=":daemon"/>
        <provider android:authorities="com.alibaba.android.rimet.beesforce.VirtualAPK.FileProvider" android:exported="false" android:grantUriPermissions="true" android:name="qchyyds.ooOOOooo" android:process=":daemon">
            <meta-data android:name="android.support.FILE_PROVIDER_PATHS" android:resource="@xml/epic_paths"/>
        </provider>
    </application>
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.READ_PHONE_STATE"/>
    <uses-permission android:name="android.permission.READ_PRIVILEGED_PHONE_STATE"/>
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES"/>
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES"/>
    <permission android:name="com.alibaba.android.rimet.beesforce.openadsdk.permission.TT_PANGOLIN" android:protectionLevel="signature"/>
    <uses-feature android:name="android.hardware.telephony" android:required="false"/>
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="com.alibaba.android.rimet.beesforce.openadsdk.permission.TT_PANGOLIN"/>
    <uses-permission android:name="android.permission.READ_PHONE_STATE"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.READ_PRIVILEGED_PHONE_STATE"/>
    <uses-permission android:name="android.permission.READ_SMS"/>
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE"/>
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES"/>
    <uses-permission android:name="android.permission.GET_TASKS"/>
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>
    <uses-permission android:name="android.permission.WAKE_LOCK"/>
    <uses-permission android:name="android.permission.RECEIVE_USER_PRESENT"/>
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW"/>
    <uses-permission android:name="android.permission.SYSTEM_OVERLAY_WINDOW"/>
    <uses-permission android:name="android.permission.EXPAND_STATUS_BAR"/>
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES"/>
    <uses-permission android:name="com.asus.msa.SupplementaryDID.ACCESS"/>
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.READ_LOGS"/>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE"/>
    <uses-permission android:name="com.google.android.gms.permission.AD_ID"/>
    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID"/>
    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION"/>
    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS"/>
    <uses-permission android:name="android.permission.INSTALL_PACKAGES"/>
    <queries>
        <intent>
            <action android:name="android.intent.action.VIEW"/>
            <category android:name="android.intent.category.BROWSABLE"/>
            <data android:scheme="https"/>
        </intent>
        <intent>
            <action android:name="android.support.customtabs.action.CustomTabsService"/>
        </intent>
    </queries>
</manifest>