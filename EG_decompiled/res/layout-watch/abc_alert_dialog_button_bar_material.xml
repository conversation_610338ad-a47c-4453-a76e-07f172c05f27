<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.ButtonBarLayout android:gravity="bottom" android:orientation="horizontal" android:id="@id/buttonPanel" android:paddingLeft="12.0dip" android:paddingTop="4.0dip" android:paddingRight="12.0dip" android:paddingBottom="4.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layoutDirection="locale"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <Button android:id="@android:id/button3" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_weight="1.0" style="?buttonBarNeutralButtonStyle" />
    <Button android:id="@android:id/button2" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_weight="1.0" style="?buttonBarNegativeButtonStyle" />
    <Button android:id="@android:id/button1" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_weight="1.0" style="?buttonBarPositiveButtonStyle" />
</androidx.appcompat.widget.ButtonBarLayout>