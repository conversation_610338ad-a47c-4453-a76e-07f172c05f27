<?xml version="1.0" encoding="utf-8"?>
<inset android:insetLeft="@dimen/abc_button_inset_horizontal_material" android:insetRight="@dimen/abc_button_inset_horizontal_material" android:insetTop="@dimen/abc_button_inset_vertical_material" android:insetBottom="@dimen/abc_button_inset_vertical_material"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <shape android:shape="rectangle">
        <corners android:radius="@dimen/abc_control_corner_material" />
        <solid android:color="@android:color/white" />
        <padding android:left="@dimen/abc_button_padding_horizontal_material" android:top="@dimen/abc_button_padding_vertical_material" android:right="@dimen/abc_button_padding_horizontal_material" android:bottom="@dimen/abc_button_padding_vertical_material" />
    </shape>
</inset>